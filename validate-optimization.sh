#!/bin/bash
# validate-optimization.sh - Comprehensive validation of WordPress performance optimization

set -e

echo "🔍 WordPress Performance Optimization Validation"
echo "================================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
PASSED=0
FAILED=0
WARNINGS=0

# Function to print test results
print_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    if [ "$result" = "PASS" ]; then
        echo -e "✅ ${GREEN}PASS${NC} - $test_name"
        [ -n "$details" ] && echo "   $details"
        ((PASSED++))
    elif [ "$result" = "FAIL" ]; then
        echo -e "❌ ${RED}FAIL${NC} - $test_name"
        [ -n "$details" ] && echo "   $details"
        ((FAILED++))
    elif [ "$result" = "WARN" ]; then
        echo -e "⚠️  ${YELLOW}WARN${NC} - $test_name"
        [ -n "$details" ] && echo "   $details"
        ((WARNINGS++))
    fi
    echo ""
}

# Check if development environment is running
echo "🔍 Checking environment status..."
if ! docker ps --format "table {{.Names}}" | grep -q "wordpress-dev"; then
    print_result "Development Environment" "FAIL" "WordPress development container not running. Run 'make dev' first."
    exit 1
fi

if ! docker ps --format "table {{.Names}}" | grep -q "mysql-dev"; then
    print_result "Database Environment" "FAIL" "MySQL development container not running."
    exit 1
fi

if ! docker ps --format "table {{.Names}}" | grep -q "redis-dev"; then
    print_result "Cache Environment" "FAIL" "Redis development container not running."
    exit 1
fi

print_result "Container Status" "PASS" "All required containers are running"

# Test 1: URL Performance Test
echo "🚀 Testing URL performance..."
PROBLEMATIC_URL="http://localhost:8080/product-category/%d0%b4%d0%b0%d0%bc%d1%81%d0%ba%d0%b0-%d0%ba%d0%be%d0%bb%d0%b5%d0%ba%d1%86%d0%b8%d1%8f/%d0%b4%d0%be%d0%bc%d0%b0%d1%88%d0%b5%d0%bd-%d1%85%d0%b0%d0%bb%d0%b0%d1%82/?orderby=date&wpf_count=24&wpf_fbv=1&all_products_filtering=1"

# Test URL performance 3 times
TOTAL_TIME=0
SUCCESS_COUNT=0

for i in {1..3}; do
    RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" -m 30 "$PROBLEMATIC_URL" 2>/dev/null || echo "30.0")
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -m 30 "$PROBLEMATIC_URL" 2>/dev/null || echo "000")
    
    if [ "$HTTP_CODE" = "200" ]; then
        TOTAL_TIME=$(echo "$TOTAL_TIME + $RESPONSE_TIME" | bc -l 2>/dev/null || echo "$TOTAL_TIME")
        ((SUCCESS_COUNT++))
    fi
done

if [ "$SUCCESS_COUNT" -eq 3 ]; then
    AVG_TIME=$(echo "scale=2; $TOTAL_TIME / 3" | bc -l 2>/dev/null || echo "0")
    if (( $(echo "$AVG_TIME < 1.5" | bc -l 2>/dev/null || echo "0") )); then
        print_result "URL Performance Test" "PASS" "Average response time: ${AVG_TIME}s (target: <1.5s)"
    elif (( $(echo "$AVG_TIME < 3.0" | bc -l 2>/dev/null || echo "0") )); then
        print_result "URL Performance Test" "WARN" "Average response time: ${AVG_TIME}s (target: <1.5s, acceptable: <3.0s)"
    else
        print_result "URL Performance Test" "FAIL" "Average response time: ${AVG_TIME}s (target: <1.5s)"
    fi
else
    print_result "URL Performance Test" "FAIL" "Only $SUCCESS_COUNT/3 requests succeeded"
fi

# Test 2: Plugin Status Check
echo "🔌 Checking required plugins..."
REQUIRED_PLUGINS=("woocommerce" "woo-product-filter" "advanced-custom-fields" "redis-cache")
PLUGIN_ISSUES=0

for plugin in "${REQUIRED_PLUGINS[@]}"; do
    STATUS=$(docker exec wordpress-dev wp plugin status "$plugin" --allow-root 2>/dev/null | grep "Status:" | awk '{print $2}' || echo "missing")
    
    if [ "$STATUS" = "active" ]; then
        print_result "Plugin: $plugin" "PASS" "Status: Active"
    elif [ "$STATUS" = "inactive" ]; then
        print_result "Plugin: $plugin" "FAIL" "Status: Inactive (run: wp plugin activate $plugin)"
        ((PLUGIN_ISSUES++))
    else
        print_result "Plugin: $plugin" "FAIL" "Status: Missing (run: wp plugin install $plugin --activate)"
        ((PLUGIN_ISSUES++))
    fi
done

# Test 3: Redis Cache Status
echo "🔴 Checking Redis cache..."
REDIS_STATUS=$(docker exec wordpress-dev wp redis status --allow-root 2>/dev/null | grep "Status:" | awk '{print $2}' || echo "unknown")

if [ "$REDIS_STATUS" = "Connected" ]; then
    print_result "Redis Cache" "PASS" "Status: Connected"
else
    print_result "Redis Cache" "FAIL" "Status: $REDIS_STATUS (run: wp redis enable)"
fi

# Test 4: Database Health Check
echo "🗄️ Checking database health..."

# Check Action Scheduler
ZERO_IDS=$(docker exec mysql-dev mysql -u root -proot_password pinkangel -e "SELECT COUNT(*) FROM pink_actionscheduler_actions WHERE action_id = 0;" 2>/dev/null | tail -1 || echo "error")

if [ "$ZERO_IDS" = "0" ]; then
    print_result "Action Scheduler Health" "PASS" "No records with action_id = 0"
elif [ "$ZERO_IDS" = "error" ]; then
    print_result "Action Scheduler Health" "FAIL" "Cannot check database (connection issue)"
else
    print_result "Action Scheduler Health" "FAIL" "$ZERO_IDS records with action_id = 0 (run: ./fix-action-scheduler.sh)"
fi

# Check database indexes
POSTMETA_INDEXES=$(docker exec mysql-dev mysql -u root -proot_password pinkangel -e "SHOW INDEX FROM pink_postmeta WHERE Key_name IN ('idx_post_meta', 'idx_meta_key_value');" 2>/dev/null | wc -l || echo "0")

if [ "$POSTMETA_INDEXES" -gt 2 ]; then
    print_result "Database Indexes" "PASS" "Critical postmeta indexes exist"
else
    print_result "Database Indexes" "FAIL" "Missing critical indexes (run: ./fix-url-performance.sh)"
fi

# Test 5: Error Log Check
echo "📋 Checking error logs..."

# Check for Action Scheduler errors
AS_ERRORS=$(docker logs wordpress-dev --tail 100 2>/dev/null | grep -c "duplicate entry.*actionscheduler" 2>/dev/null || echo "0")
AS_ERRORS=$(echo "$AS_ERRORS" | head -1)  # Take only first line
if [ "$AS_ERRORS" -eq 0 ] 2>/dev/null; then
    print_result "Action Scheduler Errors" "PASS" "No duplicate entry errors found"
else
    print_result "Action Scheduler Errors" "FAIL" "$AS_ERRORS duplicate entry errors (run: ./fix-action-scheduler.sh)"
fi

# Check for image 404 errors
IMG_ERRORS=$(docker logs wordpress-dev --tail 100 2>/dev/null | grep -c "404.*wp-content/uploads" 2>/dev/null || echo "0")
IMG_ERRORS=$(echo "$IMG_ERRORS" | head -1)  # Take only first line
if [ "$IMG_ERRORS" -eq 0 ] 2>/dev/null; then
    print_result "Image 404 Errors" "PASS" "No image 404 floods found"
elif [ "$IMG_ERRORS" -lt 5 ] 2>/dev/null; then
    print_result "Image 404 Errors" "WARN" "$IMG_ERRORS image 404 errors (acceptable if not flooding)"
else
    print_result "Image 404 Errors" "FAIL" "$IMG_ERRORS image 404 errors (run: ./fix-missing-images-and-errors.sh)"
fi

# Check for ACF errors
ACF_ERRORS=$(docker logs wordpress-dev --tail 100 2>/dev/null | grep -c "get_field" 2>/dev/null || echo "0")
ACF_ERRORS=$(echo "$ACF_ERRORS" | head -1)  # Take only first line
if [ "$ACF_ERRORS" -eq 0 ] 2>/dev/null; then
    print_result "ACF Function Errors" "PASS" "No get_field() errors found"
else
    print_result "ACF Function Errors" "FAIL" "$ACF_ERRORS ACF function errors (activate advanced-custom-fields plugin)"
fi

# Test 6: Image Redirect Test
echo "🖼️ Testing image redirect functionality..."
IMG_REDIRECT_CODE=$(curl -s -o /dev/null -w "%{http_code}" -m 10 "http://localhost:8080/wp-content/uploads/2024/06/nonexistent-image.jpg" 2>/dev/null || echo "000")

if [ "$IMG_REDIRECT_CODE" = "302" ]; then
    print_result "Image Redirect" "PASS" "Missing images redirect properly (HTTP 302)"
elif [ "$IMG_REDIRECT_CODE" = "200" ]; then
    print_result "Image Redirect" "WARN" "Images return HTTP 200 (may be serving placeholder)"
else
    print_result "Image Redirect" "FAIL" "Missing images return HTTP $IMG_REDIRECT_CODE (run: ./fix-missing-images-and-errors.sh)"
fi

# Test 7: System Resource Check
echo "💻 Checking system resources..."
CPU_USAGE=$(docker stats mysql-dev --no-stream --format "{{.CPUPerc}}" 2>/dev/null | sed 's/%//' || echo "0")
CPU_USAGE_NUM=$(echo "$CPU_USAGE" | sed 's/[^0-9.]//g')

if (( $(echo "$CPU_USAGE_NUM < 20" | bc -l 2>/dev/null || echo "1") )); then
    print_result "CPU Usage" "PASS" "MySQL CPU usage: ${CPU_USAGE}% (normal)"
elif (( $(echo "$CPU_USAGE_NUM < 50" | bc -l 2>/dev/null || echo "0") )); then
    print_result "CPU Usage" "WARN" "MySQL CPU usage: ${CPU_USAGE}% (elevated but acceptable)"
else
    print_result "CPU Usage" "FAIL" "MySQL CPU usage: ${CPU_USAGE}% (too high, check for heavy queries)"
fi

# Summary
echo "================================================"
echo "🏁 Validation Summary"
echo "================================================"
echo ""

TOTAL_TESTS=$((PASSED + FAILED + WARNINGS))

echo -e "📊 Results:"
echo -e "   ${GREEN}✅ Passed: $PASSED${NC}"
echo -e "   ${RED}❌ Failed: $FAILED${NC}"
echo -e "   ${YELLOW}⚠️  Warnings: $WARNINGS${NC}"
echo -e "   📋 Total Tests: $TOTAL_TESTS"
echo ""

# Overall status
if [ "$FAILED" -eq 0 ] && [ "$WARNINGS" -eq 0 ]; then
    echo -e "🎉 ${GREEN}OPTIMIZATION SUCCESSFUL!${NC}"
    echo "   All tests passed. Your WordPress optimization is complete."
    echo "   Performance should be optimal with sub-second loading times."
elif [ "$FAILED" -eq 0 ]; then
    echo -e "✅ ${YELLOW}OPTIMIZATION MOSTLY SUCCESSFUL${NC}"
    echo "   All critical tests passed with some warnings."
    echo "   Performance should be good but monitor the warnings."
elif [ "$FAILED" -le 2 ]; then
    echo -e "⚠️  ${YELLOW}OPTIMIZATION PARTIALLY SUCCESSFUL${NC}"
    echo "   Some issues remain but core performance should be improved."
    echo "   Address the failed tests to complete optimization."
else
    echo -e "❌ ${RED}OPTIMIZATION INCOMPLETE${NC}"
    echo "   Multiple critical issues remain."
    echo "   Review and re-run the optimization scripts."
fi

echo ""
echo "💡 Next steps:"
if [ "$FAILED" -gt 0 ]; then
    echo "   1. Address failed tests above"
    echo "   2. Re-run optimization scripts if needed"
    echo "   3. Run this validation again"
fi
echo "   4. Monitor performance with: make performance-test"
echo "   5. Check logs regularly: docker logs wordpress-dev --tail 20"
echo "   6. Run weekly maintenance: make clean-action-scheduler"

echo ""
echo "📚 For detailed troubleshooting, see:"
echo "   - WORDPRESS-PERFORMANCE-OPTIMIZATION-GUIDE.md"
echo "   - QUICK-OPTIMIZATION-REFERENCE.md"

# Exit with appropriate code
if [ "$FAILED" -eq 0 ]; then
    exit 0
else
    exit 1
fi
