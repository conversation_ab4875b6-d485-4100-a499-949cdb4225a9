# Docker Swarm Stack Configuration for Production
# Deploy with: docker stack deploy -c docker-stack.prod.yml pinkangel-prod
# 
# Note: The reverse-proxy container should be started separately with:
# docker-compose -f docker-compose.proxy.yml up -d
#
# This stack includes all production services except the reverse-proxy

version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./wordpress:/var/www/html:ro
      - ./nginx-config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx-config/wordpress.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx-cache:/var/cache/nginx-cache
      - nginx_logs:/var/log/nginx
    networks:
      - wordpress-prod-network
    depends_on:
      - wordpress-prod
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  wordpress-prod:
    image: pinkangel-wordpress-prod:latest
    environment:
      WORDPRESS_DB_HOST: mysql-prod
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD_FILE: /run/secrets/wordpress_db_password
      WORDPRESS_DB_NAME: pinkangel
      WORDPRESS_TABLE_PREFIX: pink_
      # WordPress security keys (from secrets)
      WORDPRESS_AUTH_KEY_FILE: /run/secrets/wordpress_auth_key
      WORDPRESS_SECURE_AUTH_KEY_FILE: /run/secrets/wordpress_secure_auth_key
      WORDPRESS_LOGGED_IN_KEY_FILE: /run/secrets/wordpress_logged_in_key
      WORDPRESS_NONCE_KEY_FILE: /run/secrets/wordpress_nonce_key
      WORDPRESS_AUTH_SALT_FILE: /run/secrets/wordpress_auth_salt
      WORDPRESS_SECURE_AUTH_SALT_FILE: /run/secrets/wordpress_secure_auth_salt
      WORDPRESS_LOGGED_IN_SALT_FILE: /run/secrets/wordpress_logged_in_salt
      WORDPRESS_NONCE_SALT_FILE: /run/secrets/wordpress_nonce_salt
      # Redis configuration
      WORDPRESS_REDIS_HOST: redis-prod
      WORDPRESS_REDIS_PORT: 6379
      WORDPRESS_REDIS_DATABASE: 0
      # Environment identification
      WORDPRESS_ENV: production
      # SMTP configuration (from secrets)
      WORDPRESS_SMTP_HOST_FILE: /run/secrets/wordpress_smtp_host
      WORDPRESS_SMTP_PORT_FILE: /run/secrets/wordpress_smtp_port
      WORDPRESS_SMTP_USER_FILE: /run/secrets/wordpress_smtp_user
      WORDPRESS_SMTP_PASSWORD_FILE: /run/secrets/wordpress_smtp_password
    user: "www-data:www-data"
    volumes:
      - ./wordpress:/var/www/html
      - ./php-config/php-prod.ini:/usr/local/etc/php/conf.d/php-prod.ini:ro
      - ./php-config/opcache.ini:/usr/local/etc/php/conf.d/opcache.ini:ro
      - ./php-config/php-fpm-prod.conf:/usr/local/etc/php-fpm.d/www.conf:ro
    networks:
      - wordpress-prod-network
    depends_on:
      - mysql-prod
      - redis-prod
    secrets:
      - wordpress_db_password
      - wordpress_auth_key
      - wordpress_secure_auth_key
      - wordpress_logged_in_key
      - wordpress_nonce_key
      - wordpress_auth_salt
      - wordpress_secure_auth_salt
      - wordpress_logged_in_salt
      - wordpress_nonce_salt
      - wordpress_smtp_host
      - wordpress_smtp_port
      - wordpress_smtp_user
      - wordpress_smtp_password
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  mysql-prod:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: pinkangel
      MYSQL_USER: wordpress
      MYSQL_PASSWORD_FILE: /run/secrets/wordpress_db_password
      MYSQL_ROOT_PASSWORD_FILE: /run/secrets/mysql_root_password
    volumes:
      - mysql_prod_data:/var/lib/mysql
      - ./mysql-config/my-prod.cnf:/etc/mysql/conf.d/my.cnf:ro
      - ./backups:/backups
    networks:
      - wordpress-prod-network
    secrets:
      - wordpress_db_password
      - mysql_root_password
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 3G
        reservations:
          memory: 2G

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    environment:
      PMA_HOST: mysql-prod
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD_FILE: /run/secrets/mysql_root_password
      MYSQL_ROOT_PASSWORD_FILE: /run/secrets/mysql_root_password
      PMA_ARBITRARY: 1
      UPLOAD_LIMIT: 100M
    ports:
      - "8081:80"
    networks:
      - wordpress-prod-network
    depends_on:
      - mysql-prod
    secrets:
      - mysql_root_password
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  redis-prod:
    image: redis:7-alpine
    volumes:
      - redis_prod_data:/data
      - ./redis-config/redis-prod.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - wordpress-prod-network
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  memcached:
    image: memcached:alpine
    command: memcached -m 256
    networks:
      - wordpress-prod-network
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  wordpress-cron-prod:
    image: pinkangel-wordpress-cron:latest
    environment:
      WORDPRESS_DB_HOST: mysql-prod
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD_FILE: /run/secrets/wordpress_db_password
      WORDPRESS_DB_NAME: pinkangel
      WORDPRESS_TABLE_PREFIX: pink_
      # WordPress security keys (from secrets)
      WORDPRESS_AUTH_KEY_FILE: /run/secrets/wordpress_auth_key
      WORDPRESS_SECURE_AUTH_KEY_FILE: /run/secrets/wordpress_secure_auth_key
      WORDPRESS_LOGGED_IN_KEY_FILE: /run/secrets/wordpress_logged_in_key
      WORDPRESS_NONCE_KEY_FILE: /run/secrets/wordpress_nonce_key
      WORDPRESS_AUTH_SALT_FILE: /run/secrets/wordpress_auth_salt
      WORDPRESS_SECURE_AUTH_SALT_FILE: /run/secrets/wordpress_secure_auth_salt
      WORDPRESS_LOGGED_IN_SALT_FILE: /run/secrets/wordpress_logged_in_salt
      WORDPRESS_NONCE_SALT_FILE: /run/secrets/wordpress_nonce_salt
      # Redis configuration
      WORDPRESS_REDIS_HOST: redis-prod
      WORDPRESS_REDIS_PORT: 6379
      WORDPRESS_REDIS_DATABASE: 0
      # Cron-specific environment
      CRON_ENVIRONMENT: production
      CRON_LOG_LEVEL: info
    volumes:
      - ./wordpress:/var/www/html:ro
      - ./php-config/php-prod.ini:/usr/local/etc/php/conf.d/php-prod.ini:ro
      - ./php-config/opcache.ini:/usr/local/etc/php/conf.d/opcache.ini:ro
      - cron_prod_logs:/var/log/wordpress-cron
    networks:
      - wordpress-prod-network
    depends_on:
      - mysql-prod
      - redis-prod
      - wordpress-prod
    # Add capabilities needed for cron daemon to manage process groups
    cap_add:
      - SETPCAP
      - SYS_ADMIN
    # Ensure container runs with proper init system
    init: true
    secrets:
      - wordpress_db_password
      - wordpress_auth_key
      - wordpress_secure_auth_key
      - wordpress_logged_in_key
      - wordpress_nonce_key
      - wordpress_auth_salt
      - wordpress_secure_auth_salt
      - wordpress_logged_in_salt
      - wordpress_nonce_salt
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

# Docker Swarm secrets (will be created from .env file)
secrets:
  wordpress_db_password:
    external: true
  mysql_root_password:
    external: true
  wordpress_auth_key:
    external: true
  wordpress_secure_auth_key:
    external: true
  wordpress_logged_in_key:
    external: true
  wordpress_nonce_key:
    external: true
  wordpress_auth_salt:
    external: true
  wordpress_secure_auth_salt:
    external: true
  wordpress_logged_in_salt:
    external: true
  wordpress_nonce_salt:
    external: true
  wordpress_smtp_host:
    external: true
  wordpress_smtp_port:
    external: true
  wordpress_smtp_user:
    external: true
  wordpress_smtp_password:
    external: true

volumes:
  mysql_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  nginx_logs:
    driver: local
  cron_prod_logs:
    driver: local

networks:
  wordpress-prod-network:
    driver: overlay
    attachable: true
