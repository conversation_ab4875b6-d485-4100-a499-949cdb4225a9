services:
  nginx-proxy:
    image: nginx:alpine
    container_name: reverse-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # Main nginx configuration
      - ./nginx-proxy-config/nginx.conf:/etc/nginx/nginx.conf:ro
      # Site-specific configurations
      - ./nginx-proxy-config/sites:/etc/nginx/sites-available:ro
      # SSL certificates
      - ./ssl-proxy:/etc/nginx/ssl:ro
      # Cache directories
      - ./nginx-proxy-cache:/var/cache/nginx-cache
      # Logs
      - nginx_proxy_logs:/var/log/nginx
    networks:
      - proxy-network
    deploy:
      resources:
        limits:
          memory: 512M  # Much more reasonable for nginx proxy
        reservations:
          memory: 128M

volumes:
  nginx_proxy_logs:

networks:
  proxy-network:
    external: true
    name: proxy-network
