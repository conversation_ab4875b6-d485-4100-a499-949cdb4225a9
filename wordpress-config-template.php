<?php
/**
 * WordPress Configuration File
 * This file should be placed in the wordpress directory as wp-config.php
 * 
 * Copy this template and customize for your environment
 */

// ** MySQL settings ** //
define('DB_NAME', 'wordpress');
define('DB_USER', 'wordpress');
define('DB_PASSWORD', getenv('WORDPRESS_DB_PASSWORD'));
define('DB_HOST', 'mysql-prod'); // Use 'mysql-dev' for development
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// ** Authentication Unique Keys and Salts ** //
// Generate these from: https://api.wordpress.org/secret-key/1.1/salt/
define('AUTH_KEY',         getenv('WORDPRESS_AUTH_KEY'));
define('SECURE_AUTH_KEY',  getenv('WORDPRESS_SECURE_AUTH_KEY'));
define('LOGGED_IN_KEY',    getenv('WORDPRESS_LOGGED_IN_KEY'));
define('NONCE_KEY',        getenv('WORDPRESS_NONCE_KEY'));
define('AUTH_SALT',        getenv('WORDPRESS_AUTH_SALT'));
define('SECURE_AUTH_SALT', getenv('WORDPRESS_SECURE_AUTH_SALT'));
define('LOGGED_IN_SALT',   getenv('WORDPRESS_LOGGED_IN_SALT'));
define('NONCE_SALT',       getenv('WORDPRESS_NONCE_SALT'));

// ** WordPress Database Table prefix ** //
$table_prefix = 'wp_';

// ** WordPress debugging ** //
// Enable for development, disable for production
if (getenv('WORDPRESS_DEBUG')) {
    define('WP_DEBUG', true);
    define('WP_DEBUG_LOG', true);
    define('WP_DEBUG_DISPLAY', false);
    define('SCRIPT_DEBUG', true);
    define('SAVEQUERIES', true);
} else {
    define('WP_DEBUG', false);
    define('WP_DEBUG_LOG', false);
    define('WP_DEBUG_DISPLAY', false);
}

// ** Security hardening ** //
define('DISALLOW_FILE_EDIT', true);
define('DISALLOW_FILE_MODS', false); // Set to true in production if you don't need plugin/theme updates
define('FORCE_SSL_ADMIN', true);
define('WP_POST_REVISIONS', 5);
define('AUTOSAVE_INTERVAL', 300);
define('WP_AUTO_UPDATE_CORE', 'minor');

// ** Performance optimizations ** //
define('WP_CACHE', true);
define('COMPRESS_CSS', true);
define('COMPRESS_SCRIPTS', true);
define('CONCATENATE_SCRIPTS', true);
define('ENFORCE_GZIP', true);

// ** Disable WordPress cron (use system cron instead) ** //
define('DISABLE_WP_CRON', true);

// ** Memory limits ** //
define('WP_MEMORY_LIMIT', '256M');
define('WP_MAX_MEMORY_LIMIT', '512M');

// ** Redis Cache Configuration ** //
define('WP_REDIS_HOST', 'redis-prod'); // Use 'redis-dev' for development
define('WP_REDIS_PORT', 6379);
define('WP_REDIS_DATABASE', 0);
define('WP_REDIS_PREFIX', 'wp:');
define('WP_REDIS_TIMEOUT', 1);
define('WP_REDIS_READ_TIMEOUT', 1);

// ** Memcached Configuration (Production only) ** //
if (!getenv('WORDPRESS_DEBUG')) {
    $memcached_servers = array(
        'default' => array(
            'memcached-prod:11211',
        )
    );
}

// ** File Permissions ** //
define('FS_CHMOD_DIR', (0755 & ~ umask()));
define('FS_CHMOD_FILE', (0644 & ~ umask()));

// ** SSL and URL Configuration ** //
if (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
    $_SERVER['HTTPS'] = 'on';
}

// ** Upload Configuration ** //
define('UPLOADS', 'wp-content/uploads');

// ** Cookie Configuration ** //
define('COOKIE_DOMAIN', getenv('WORDPRESS_DOMAIN'));
define('COOKIEPATH', '/');
define('SITECOOKIEPATH', '/');

// ** Multisite Configuration (if needed) ** //
// define('WP_ALLOW_MULTISITE', true);
// define('MULTISITE', true);
// define('SUBDOMAIN_INSTALL', false);
// define('DOMAIN_CURRENT_SITE', getenv('WORDPRESS_DOMAIN'));
// define('PATH_CURRENT_SITE', '/');
// define('SITE_ID_CURRENT_SITE', 1);
// define('BLOG_ID_CURRENT_SITE', 1);

// ** Custom Content Directory (optional) ** //
// define('WP_CONTENT_DIR', dirname(__FILE__) . '/wp-content');
// define('WP_CONTENT_URL', 'https://' . getenv('WORDPRESS_DOMAIN') . '/wp-content');

// ** Automatic Updates ** //
define('AUTOMATIC_UPDATER_DISABLED', false);
add_filter('auto_update_plugin', '__return_true');
add_filter('auto_update_theme', '__return_true');

// ** That's all, stop editing! Happy publishing. ** //

if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

require_once(ABSPATH . 'wp-settings.php');
