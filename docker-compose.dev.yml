services:
  wordpress-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: wordpress-dev
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      WORDPRESS_DB_HOST: mysql-dev
      WORDPRESS_DB_USER: root
      WORDPRESS_DB_PASSWORD: root_password
      WORDPRESS_DB_NAME: pinkangel
      # Redis configuration
      WORDPRESS_REDIS_HOST: redis-dev
      WORDPRESS_REDIS_PORT: 6379
      WORDPRESS_REDIS_DATABASE: 0
      WORDPRESS_DEBUG: 1
      WORDPRESS_CONFIG_EXTRA: |
        define('WP_DEBUG', true);
        define('WP_DEBUG_LOG', true);
        define('WP_DEBUG_DISPLAY', false);
        define('SCRIPT_DEBUG', true);
        define('SAVEQUERIES', true);
    volumes:
      - ./wordpress:/var/www/html
      - ./php-config/xdebug.ini:/usr/local/etc/php/conf.d/xdebug.ini
      - ./php-config/php-dev.ini:/usr/local/etc/php/conf.d/php-dev.ini
    depends_on:
      - mysql-dev
      - redis-dev
    networks:
      - wordpress-dev-network

  mysql-dev:
    image: mysql:8.0
    container_name: mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: pinkangel
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./mysql-config/my-dev.cnf:/etc/mysql/conf.d/my.cnf
    ports:
      - "3306:3306"
    networks:
      - wordpress-dev-network

  redis-dev:
    image: redis:7-alpine
    container_name: redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
      - ./redis-config/redis-dev.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - wordpress-dev-network

  wordpress-cron-dev:
    build:
      context: .
      dockerfile: Dockerfile.cron
    container_name: wordpress-cron-dev
    restart: unless-stopped
    init: true
    environment:
      WORDPRESS_DB_HOST: mysql-dev
      WORDPRESS_DB_USER: root
      WORDPRESS_DB_PASSWORD: root_password
      WORDPRESS_DB_NAME: pinkangel
      # Redis configuration
      WORDPRESS_REDIS_HOST: redis-dev
      WORDPRESS_REDIS_PORT: 6379
      WORDPRESS_REDIS_DATABASE: 0
      WORDPRESS_DEBUG: 1
      # Cron-specific environment
      CRON_ENVIRONMENT: development
      CRON_LOG_LEVEL: debug
    volumes:
      - ./wordpress:/var/www/html:ro
      - ./php-config/php-dev.ini:/usr/local/etc/php/conf.d/php-dev.ini:ro
      - cron_dev_logs:/var/log/wordpress-cron
    depends_on:
      - mysql-dev
      - redis-dev
      - wordpress-dev
    networks:
      - wordpress-dev-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    platform: linux/amd64
    container_name: phpmyadmin-dev
    restart: unless-stopped
    ports:
      - "8081:80"
    environment:
      PMA_HOST: mysql-dev
      PMA_PORT: 3306
      MYSQL_ROOT_PASSWORD: root_password
    depends_on:
      - mysql-dev
    networks:
      - wordpress-dev-network

volumes:
  mysql_dev_data:
  redis_dev_data:
  cron_dev_logs:

networks:
  wordpress-dev-network:
    driver: bridge
