window.addEventListener('load', function () {
    window.objectcache.groups.init();
    window.objectcache.latency.init();
    window.objectcache.flushlog.init();
    window.objectcache.commands.init();
    window.objectcache.diagnostics.init();
    window.objectcache.adaptive.init();
});

jQuery.extend(window.objectcache, {
    latency: {
        init: function () {
            this.fetchData();
            setInterval(this.fetchData, 10000);
        },

        fetchData: function () {
            jQuery
                .ajax({
                    url: objectcache.rest.url + 'objectcache/v1/latency',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('X-WP-Nonce', objectcache.rest.nonce);
                    },
                })
                .done(function (data, status, xhr) {
                    var header = xhr.getResponseHeader('X-WP-Nonce');

                    if (header) {
                        objectcache.rest.nonce = header;
                    }

                    var widget = document.querySelector('.objectcache\\:latency-widget');

                    var table = widget.querySelector('table');
                    table && widget.removeChild(table);

                    var error = widget.querySelector('.error');
                    error && widget.removeChild(error);

                    table = document.createElement('table');
                    widget.prepend(table);

                    var content = '';

                    var formatLatency = function (us) {
                        var ms = Math.round((us / 1000 + Number.EPSILON) * 100) / 100;
                        if (us < 500) return '<strong>' + ms + '</strong> ms';
                        if (us < 1000) return '<strong class="warning">' + ms + '</strong> ms';
                        return '<strong class="error">' + ms + '</strong> ms';
                    };

                    data.forEach(function (item) {
                        var note = item.note ? ' (' + item.note + ')' : '';

                        content += '<tr>';
                        content += '  <td>' + item.url + note + '</td>';
                        content += '  <td>';
                        content += item.error ? '<span class="error">' + item.error + '</span>' : formatLatency(item.latency);
                        content += '  </td>';
                        content += '</tr>';
                    });

                    document.querySelector('.objectcache\\:latency-widget table').innerHTML = content;
                })
                .fail(function (error) {
                    var widget = document.querySelector('.objectcache\\:latency-widget');

                    var table = widget.querySelector('table');
                    table && widget.removeChild(table);

                    var container = widget.querySelector('.error');

                    if (! container) {
                        container = document.createElement('p');
                        container.classList.add('error');

                        widget.append(container);
                    }

                    if (error.responseJSON && error.responseJSON.message) {
                        container.textContent = error.responseJSON.message;
                    } else {
                        container.textContent = 'Request failed (' + error.status + ').';
                    }
                });
        },
    },

    groups: {
        init: function () {
            document.querySelector('.objectcache\\:groups-widget button')
                .addEventListener('click', window.objectcache.groups.fetchData);

            if (! ClipboardJS.isSupported()) {
                return;
            }

            var widget = document.querySelector('.objectcache\\:groups-widget');
            var downloadButton = widget.querySelector('.button[data-download-target]');

            downloadButton.addEventListener('click', function (event) {
                var groups = widget.querySelector(event.target.dataset.downloadTarget);
                var hostname = window.location.hostname.replace('.', '-');

                if (groups) {
                    var data = groups.innerText.replace(/ *\t/g, ',');
                    var anchor = window.document.createElement('a');
                    anchor.href = window.URL.createObjectURL(new Blob([data], { type: 'text/plain' }));
                    anchor.download = 'cache-groups-' + hostname + '.csv';
                    anchor.click();
                }
            });
        },

        fetchData: function () {
            var widget = document.querySelector('.objectcache\\:groups-widget');
            var checkbox = widget.querySelector('input[type=checkbox]');
            var button = widget.querySelector('.button');
            button.blur();
            button.classList.add('disabled');
            button.textContent = button.dataset.loading;

            var download = widget.querySelector('.button[data-download-target]');
            download.classList.add('hidden');

            var container = widget.querySelector('.table-container');
            container && widget.removeChild(container);

            var error = widget.querySelector('.error');
            error && widget.removeChild(error);

            var title = document.querySelector('#objectcache_groups .hndle');

            if (title) {
                if ('label' in title.dataset) {
                    title.textContent = title.dataset.label;
                } else {
                    title.dataset.label = title.textContent;
                }
            }

            jQuery
                .ajax({
                    url: objectcache.rest.url + 'objectcache/v1/groups' + (
                        objectcache.rest.url.indexOf('?') < 0 ? '?' : '&'
                    ) + 'memory=' + (+(checkbox && checkbox.checked)),
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('X-WP-Nonce', objectcache.rest.nonce);
                    },
                })
                .done(function (data, status, xhr) {
                    var header = xhr.getResponseHeader('X-WP-Nonce');

                    if (header) {
                        objectcache.rest.nonce = header;
                    }

                    var info = widget.querySelector('p:first-child');
                    info && widget.removeChild(info);

                    var container = document.createElement('div');
                    container.classList.add('table-container');
                    widget.prepend(container);

                    var table = document.createElement('table');
                    container.prepend(table);

                    var escapeHtml = function (text) {
                        var div = document.createElement('div');
                        div.innerText = text;

                        return div.innerHTML.replace(/"/g, '&quot;').replace(/'/g, '&apos;');
                    };

                    var formatNumber = function (number) {
                        return new Intl.NumberFormat('en-US').format(number);
                    };

                    var formatBytes = function (bytes) {
                        return new Intl.NumberFormat('en-US', {
                            style: 'unit',
                            unit: 'byte',
                            unitDisplay: 'narrow',
                            notation: 'compact',
                        }).format(bytes).replace(/([\d.]+)(\D+)/, '$1 $2');
                    };

                    var content = '';

                    if (data.length) {
                        title.textContent = title.dataset.label + ' (' + data.length + ')';
                        title.dataset.keys = data.length;

                        data.forEach(function (item) {
                            var title = formatNumber(item.keys) + ' objects found in `' + escapeHtml(item.group) + '` cache group';

                            content += '<tr title="' + title + '">';
                            content += '  <td data-group="' + item.group + '">';
                            content += '    <span class="group-name">' + escapeHtml(item.group) + '</span>';
                            content += '    <button class="objectcache:flush-group button-link">Flush</button>';
                            content += '  </td>';
                            if (checkbox && checkbox.checked) {
                                content += '<td>';
                                content += formatBytes(item.bytes);
                                content += '</td>';
                            }
                            content += '  <td>';
                            content += formatNumber(item.keys);
                            content += '  </td>';
                            content += '</tr>';
                        });

                        download.classList.remove('hidden');
                    } else {
                        content += '<tr>';
                        content += '  <td colspan="2">No cache groups found.</td>';
                        content += '</tr>';
                    }

                    table.innerHTML = content;

                    document.querySelectorAll(
                        '.objectcache\\:groups-widget .objectcache\\:flush-group'
                    ).forEach(function (button) {
                        button.addEventListener('click', window.objectcache.groups.flushGroup);
                    });
                })
                .fail(function (error) {
                    var container = widget.querySelector('.error');

                    if (! container) {
                        container = document.createElement('p');
                        container.classList.add('error');

                        widget.append(container);
                    }

                    if (error.responseJSON && error.responseJSON.message) {
                        container.textContent = error.responseJSON.message;
                    } else {
                        container.textContent = 'Request failed (' + error.status + ').';
                    }
                })
                .always(function () {
                    var button = widget.querySelector('.objectcache\\:groups-widget .button');
                    button.textContent = button.dataset.text;
                    button.classList.remove('disabled');
                });
        },

        flushGroup: function (event) {
            event.preventDefault();

            var table = event.target.closest('table');

            if (table.classList.contains('busy')) {
                return;
            }

            table.classList.add('busy');

            event.target.disabled = true;

            var groupLabel = event.target.previousElementSibling;

            groupLabel.classList.remove('error');
            groupLabel.textContent = 'Flushing...';

            jQuery
                .ajax({
                    type: 'DELETE',
                    url: objectcache.rest.url + 'objectcache/v1/groups',
                    data: {
                        group: event.target.parentElement.dataset.group,
                    },
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('X-WP-Nonce', objectcache.rest.nonce);
                    },
                })
                .done(function(data, status, xhr) {
                    var header = xhr.getResponseHeader('X-WP-Nonce');

                    if (header) {
                        objectcache.rest.nonce = header;
                    }

                    var title = document.querySelector('#objectcache_groups .hndle');
                    title.dataset.keys = title.dataset.keys - 1;

                    title.textContent = title.dataset.label + ' (' + title.dataset.keys + ')';

                    event.target.closest('tr').remove();
                })
                .fail(function (error) {
                    groupLabel.classList.add('error');

                    if (error.responseJSON && error.responseJSON.message) {
                        groupLabel.textContent = error.responseJSON.message;
                    } else {
                        groupLabel.textContent = 'Request failed (' + error.status + ').';
                    }

                    setTimeout(function() {
                        groupLabel.classList.remove('error');
                        groupLabel.textContent = groupLabel.parentElement.dataset.group;
                    }, 3000);
                })
                .always(function () {
                    table.classList.remove('busy');
                    event.target.disabled = false;
                });
        }
    },

    flushlog: {
        init: function () {
            var inputs = document.querySelectorAll('.objectcache\\:flushlog-widget input');

            if (inputs) {
                for (var i = 0; i < inputs.length; i++) {
                    inputs[i].addEventListener('click', window.objectcache.flushlog.save);
                }
            }
        },

        save: function (event) {
            event.target.disabled = true;

            jQuery
                .ajax({
                    type: 'POST',
                    url: objectcache.rest.url + 'objectcache/v1/options',
                    data: {
                        [event.target.name]: event.target.checked ? 1 : 0,
                    },
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('X-WP-Nonce', objectcache.rest.nonce);
                    },
                })
                .fail(function (error) {
                    if (error.responseJSON && error.responseJSON.message) {
                        window.alert(error.responseJSON.message);
                    } else {
                        window.alert('Request failed (' + error.status + ').');
                    }
                })
                .always(function () {
                    event.target.disabled = false;
                });
        },
    },

    diagnostics: {
        init: function () {
            var widget = document.querySelector('.objectcache\\:health-widget');
            var downloadButton = widget.querySelector('.button[data-download-target]');

            downloadButton.addEventListener('click', function (event) {
                var diagnostics = widget.querySelector(event.target.dataset.downloadTarget);
                var hostname = window.location.hostname.replace('.', '-');

                if (diagnostics) {
                    var anchor = window.document.createElement('a');
                    anchor.href = window.URL.createObjectURL(new Blob([diagnostics.value], { type: 'text/plain' }));
                    anchor.download = 'diagnostics-' + hostname + '.txt';
                    anchor.click();
                }
            });
        },
    },

    commands: {
        init: function () {
            document.querySelector('.objectcache\\:commands-widget')
                .addEventListener('click', window.objectcache.commands.sort);
        },

        sort: function (event) {
            var sortBy = (event.target.closest('[data-sort]') || { dataset: {} }).dataset.sort;
            var sortable = ['calls', 'rejected', 'failed', 'usec'];

            if (sortBy && sortable.includes(sortBy)) {
                event.preventDefault();

                var container = document.querySelector('.objectcache\\:commands-widget');
                var items = Array.from(container.querySelectorAll('details'));

                var sorted = items.slice().sort((a, b) => {
                    return parseInt(b.dataset[sortBy]) - parseInt(a.dataset[sortBy]);
                });

                sorted.forEach(item => container.appendChild(item));
            }
        },
    },

    adaptive: {
        init: function () {
            var button = document.querySelector('.objectcache\\:adaptive-widget button');
            if (button) {
                button.addEventListener('click', window.objectcache.adaptive.fetchData);
            }
        },
        fetchData: function (event) {
            event.preventDefault();

            var widget = document.querySelector('.objectcache\\:adaptive-widget');
            var button = widget.querySelector('.button');

            button.blur();
            button.classList.add('disabled');
            button.textContent = button.dataset.loading;

            jQuery
                .ajax({
                    url: objectcache.rest.url + 'objectcache/v1/relay/adaptive',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('X-WP-Nonce', objectcache.rest.nonce);
                    },
                })
                .done(function (data, status, xhr) {
                    var header = xhr.getResponseHeader('X-WP-Nonce');

                    if (header) {
                        objectcache.rest.nonce = header;
                    }

                    var content = '';

                    if (data.length > 0) {
                        data.forEach(function (item) {
                            content += '<details>';
                            content += '  <summary>';
                            content += '    <span class="dashicons dashicons-arrow-right-alt2"></span>';
                            content += '    <code title="' + item.key + '">' + item.key + '</code>';
                            content += '    <time title="Reads/Writes ratio">1:' + Math.min(Math.round(item.ratio), 1000) + '</time>';
                            content += '  </summary>';
                            content += '  <ul>';
                            content += '    <li title="Number of reads">';
                            content += '      <span class="dashicons dashicons-visibility"></span>';
                            content += '      <time>' + item.reads + '</time>';
                            content += '    </li>';
                            content += '    <li title="Number of writes">';
                            content += '      <span class="dashicons dashicons-edit"></span>';
                            content += '      <time>' + item.writes + '</time>';
                            content += '    </li>';
                            content += '  </ul>';
                            content += '</details>';
                        });
                    } else {
                        content = 'No statistics available, yet.';
                    }

                    var table = widget.querySelector('p,div.table-container');
                    table && widget.removeChild(table);

                    var container = document.createElement('div');
                    container.classList.add('table-container');
                    container.innerHTML = content;

                    widget.prepend(container);
                })
                .fail(function (error) {
                    var container = widget.querySelector('.error');

                    if (! container) {
                        container = document.createElement('p');
                        container.classList.add('error');

                        widget.prepend(container);
                    }

                    if (error.responseJSON && error.responseJSON.message) {
                        container.textContent = error.responseJSON.message;
                    } else {
                        container.textContent = 'Request failed (' + error.status + ').';
                    }
                })
                .always(function () {
                    button.textContent = button.dataset.text;
                    button.classList.remove('disabled');
                });
        }
    }
});
