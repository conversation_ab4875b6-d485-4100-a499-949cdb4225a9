<?php
/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the website, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/
 *
 * @package WordPress
 */

// Function to read Docker secrets or environment variables
function get_secret_or_env($secret_file_env, $direct_env, $default = '') {
    // Try to read from Docker secret file first
    if (!empty($_ENV[$secret_file_env]) && file_exists($_ENV[$secret_file_env])) {
        $value = trim(file_get_contents($_ENV[$secret_file_env]));
        return !empty($value) ? $value : $default;
    }
    // Fall back to direct environment variable
    return $_ENV[$direct_env] ?? $default;
}

// ** Database settings - Environment-based configuration with Docker secrets support ** //
/** The name of the database for WordPress */
define( 'DB_NAME', $_ENV['WORDPRESS_DB_NAME'] ?? 'pinkangel' );

/** Database username */
define( 'DB_USER', $_ENV['WORDPRESS_DB_USER'] ?? 'root' );

/** Database password */
define( 'DB_PASSWORD', get_secret_or_env('WORDPRESS_DB_PASSWORD_FILE', 'WORDPRESS_DB_PASSWORD', 'root_password') );

/** Database hostname */
define( 'DB_HOST', $_ENV['WORDPRESS_DB_HOST'] ?? 'mysql-dev' );

/** Database charset to use in creating database tables. */
define( 'DB_CHARSET', 'utf8mb4' );

/** The database collate type. Don't change this if in doubt. */
define( 'DB_COLLATE', '' );

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',         get_secret_or_env('WORDPRESS_AUTH_KEY_FILE', 'WORDPRESS_AUTH_KEY', '*h)Vj^_ZYL7~]`%YW681#8)ok|8q9<r/Uul0S|G)REp 2wt[b{f0mC(m;={S0M5y') );
define( 'SECURE_AUTH_KEY',  get_secret_or_env('WORDPRESS_SECURE_AUTH_KEY_FILE', 'WORDPRESS_SECURE_AUTH_KEY', 'nV[rdR{g:VhWr}sz 2>yQXoLNQyf]+%4!P_O8hw~@3g-vw3,pSJF$u)Bab)onX2y') );
define( 'LOGGED_IN_KEY',    get_secret_or_env('WORDPRESS_LOGGED_IN_KEY_FILE', 'WORDPRESS_LOGGED_IN_KEY', 'MQR>gXn9#,[ Qqq^UQgz18V95vxe<iaJY[m_|C4{4yx:GV#@[,0(a{Y;<(I^85t(') );
define( 'NONCE_KEY',        get_secret_or_env('WORDPRESS_NONCE_KEY_FILE', 'WORDPRESS_NONCE_KEY', 'Aa3XIlqN6iZe-sWUZb=peW>Gq+ber%L#VC#>9Vk}>eo8cte^5&dt;r2_~z8tU5-r') );
define( 'AUTH_SALT',        get_secret_or_env('WORDPRESS_AUTH_SALT_FILE', 'WORDPRESS_AUTH_SALT', '^mNO -zJK-h,!,^U2^T(iWq.h[dZ{>5O`${AyJ>9i_Nuk}4OUQKZM=d4@pCZ&f*~') );
define( 'SECURE_AUTH_SALT', get_secret_or_env('WORDPRESS_SECURE_AUTH_SALT_FILE', 'WORDPRESS_SECURE_AUTH_SALT', 've@FPy$hIrY8+orss64#Ep;He.eZ$0lGSOBdwwbMf7>yA|Vf,#9K|qei DPkY_P<') );
define( 'LOGGED_IN_SALT',   get_secret_or_env('WORDPRESS_LOGGED_IN_SALT_FILE', 'WORDPRESS_LOGGED_IN_SALT', 'f=}^xEx_[G&k&4jrk]XDp+1?Lvg<Ra]SPSB8*F>p{+cji.CwqY^.7o:*+-,cDzb=') );
define( 'NONCE_SALT',       get_secret_or_env('WORDPRESS_NONCE_SALT_FILE', 'WORDPRESS_NONCE_SALT', '@1G,5`#FpzS Mj={gUwZ~OMYvdi17trXMBhNk;TVcxf4at*&c-@`f&|iPrPv+63_') );

/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 *
 * At the installation time, database tables are created with the specified prefix.
 * Changing this value after WordPress is installed will make your site think
 * it has not been installed.
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/#table-prefix
 */
$table_prefix = 'pink_';

/**
 * For developers: WordPress debugging mode.
 *
 * Debug settings are now handled automatically based on environment.
 * See environment-aware configuration below.
 *
 * @link https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/
 */

/* Add any custom values between this line and the "stop editing" line. */

// Redis Object Cache Configuration
define( 'WP_REDIS_HOST', $_ENV['WORDPRESS_REDIS_HOST'] ?? 'redis-dev' );
define( 'WP_REDIS_PORT', $_ENV['WORDPRESS_REDIS_PORT'] ?? 6379 );
define( 'WP_REDIS_DATABASE', $_ENV['WORDPRESS_REDIS_DATABASE'] ?? 0 );
define( 'WP_REDIS_TIMEOUT', 1 );
define( 'WP_REDIS_READ_TIMEOUT', 1 );

// Object Cache Pro Configuration
define( 'WP_REDIS_CONFIG', [
    'token' => 'offline-mode', // Offline mode token
    'host' => $_ENV['WORDPRESS_REDIS_HOST'] ?? 'redis-dev',
    'port' => (int) ($_ENV['WORDPRESS_REDIS_PORT'] ?? 6379),
    'database' => (int) ($_ENV['WORDPRESS_REDIS_DATABASE'] ?? 0),
    'timeout' => 1.0,
    'read_timeout' => 1.0,
    'retry_interval' => 100,
    'retries' => 3,
    'backoff' => 'smart',
    'compression' => 'none',
    'serializer' => 'php',
    'async_flush' => true,
    'split_alloptions' => true,
    'prefetch' => true,
    'debug' => false,
    'save_commands' => false,
    'analytics' => [
        'enabled' => false, // Disabled for privacy
        'persist' => false,
        'retention' => 0,
        'footnote' => false,
    ],
    'updates' => false, // Disabled for security
] );

// SMTP Configuration (compatible with both Docker secrets and direct env vars)
define( 'SMTP_HOST', get_secret_or_env('WORDPRESS_SMTP_HOST_FILE', 'WORDPRESS_SMTP_HOST', '') );
define( 'SMTP_PORT', (int)get_secret_or_env('WORDPRESS_SMTP_PORT_FILE', 'WORDPRESS_SMTP_PORT', '587') );
define( 'SMTP_USER', get_secret_or_env('WORDPRESS_SMTP_USER_FILE', 'WORDPRESS_SMTP_USER', '') );
define( 'SMTP_PASS', get_secret_or_env('WORDPRESS_SMTP_PASSWORD_FILE', 'WORDPRESS_SMTP_PASSWORD', '') );
define( 'SMTP_AUTH', !empty(get_secret_or_env('WORDPRESS_SMTP_USER_FILE', 'WORDPRESS_SMTP_USER', '')) );
$smtp_port = (int)get_secret_or_env('WORDPRESS_SMTP_PORT_FILE', 'WORDPRESS_SMTP_PORT', '587');
define( 'SMTP_SECURE', $smtp_port == 465 ? 'ssl' : 'tls' );



define( 'DISABLE_WP_CRON', 'true' );

// Filesystem method - prevents FTP credential prompts in containerized environment
define( 'FS_METHOD', 'direct' );

// Environment detection (production-ready)
$is_production = (getenv('WORDPRESS_ENV') === 'production');
$is_development = !$is_production;

// Reverse proxy configuration (production-ready)
if (!empty($_SERVER['HTTP_X_FORWARDED_PROTO'])) {
    if ($_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
        $_SERVER['HTTPS'] = 'on';
        $_SERVER['SERVER_PORT'] = 443;
        $_SERVER['REQUEST_SCHEME'] = 'https';
    }
}

// Additional reverse proxy headers
if (!empty($_SERVER['HTTP_X_FORWARDED_HOST'])) {
    $_SERVER['HTTP_HOST'] = $_SERVER['HTTP_X_FORWARDED_HOST'];
}

if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
    $_SERVER['REMOTE_ADDR'] = $_SERVER['HTTP_X_FORWARDED_FOR'];
}

// Determine if this is direct access (before using the variable)
// Check for direct access via localhost:8080 or localhost:8443
$is_direct_access = (isset($_SERVER['HTTP_HOST']) &&
    ($_SERVER['HTTP_HOST'] == 'localhost:8080' || $_SERVER['HTTP_HOST'] == 'localhost:8443'));

// Force WordPress to use correct URLs based on access method
if ($is_direct_access) {
    // When accessing directly through ports 8080/8443, use localhost URLs
    if (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == '8443') {
        define('WP_HOME', 'https://localhost:8443');
        define('WP_SITEURL', 'https://localhost:8443');
    } else {
        define('WP_HOME', 'http://localhost:8080');
        define('WP_SITEURL', 'http://localhost:8080');
    }
}

// Additional login fixes for reverse proxy (simplified)
// Only force SSL when accessed through reverse proxy (not direct port access)
if (!$is_direct_access) {
    define('FORCE_SSL_ADMIN', true);
}

// Session and cookie fixes
ini_set('session.cookie_httponly', 1);
// Only set secure cookies when not accessing directly through HTTP ports
if (!$is_direct_access || (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on')) {
    ini_set('session.cookie_secure', 1);
}
ini_set('session.use_only_cookies', 1);

// Production security settings
if ($is_production) {
    // Disable file editing in production
    define('DISALLOW_FILE_EDIT', true);
    define('DISALLOW_FILE_MODS', true);

    // Performance optimizations
    define('WP_CACHE', 'true');
    define('COMPRESS_CSS', true);
    define('COMPRESS_SCRIPTS', true);
    define('CONCATENATE_SCRIPTS', true);
    define('ENFORCE_GZIP', true);

    // Limit post revisions
    define('WP_POST_REVISIONS', 3);
    define('AUTOSAVE_INTERVAL', 300); // 5 minutes

    // Increase memory limit for production
    define('WP_MEMORY_LIMIT', '512M');

} else {
    // Development settings
    define('WP_DEBUG', true);
    define('WP_DEBUG_LOG', true);
    define('WP_DEBUG_DISPLAY', false);
    define('SCRIPT_DEBUG', true);

    // Allow file editing in development
    define('DISALLOW_FILE_EDIT', false);

    // Lower memory limit for development
    define('WP_MEMORY_LIMIT', '256M');
}

/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';
