(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[24],{104:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return l}));var r,a=n(52),i=n.n(a),o=n(53),c=n.n(o),s=function(t){var n=e[t];if(!n)return!1;try{var r="__storage_test__";return n.setItem(r,r),n.removeItem(r),!0}catch(e){return e instanceof DOMException&&(22===e.code||1014===e.code||"QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name)&&0!==n.length}},u=function(){function NullStorage(){i()(this,NullStorage)}return c()(NullStorage,[{key:"key",value:function(){return null}},{key:"getItem",value:function(){return null}},{key:"setItem",value:function(){}},{key:"removeItem",value:function(){}},{key:"clear",value:function(){}},{key:"length",get:function(){return 0}}]),NullStorage}(),l=function(){return r||(r=s("sessionStorage")?e.sessionStorage:s("localStorage")?e.localStorage:new u),r}}).call(this,n(28))},105:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTAButton}));var r=n(0),a=n.n(r),i=n(11),o=n(73);function CTAButton(t){var n,r=t.label,a=t.disabled,c=t.inProgress,s=t.onClick,u=t.href,l=t.external,f=t.hideExternalIndicator;return l&&!f&&(n=e.createElement(o.a,{width:14,height:14})),e.createElement(i.SpinnerButton,{className:"googlesitekit-notice__cta",disabled:a,isSaving:c,onClick:s,href:u,target:l?"_blank":"_self",trailingIcon:n},r)}CTAButton.propTypes={label:a.a.string.isRequired,disabled:a.a.bool,inProgress:a.a.bool,onClick:a.a.func,href:a.a.string,external:a.a.bool,hideExternalIndicator:a.a.bool}}).call(this,n(3))},106:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DismissButton}));var r=n(0),a=n.n(r),i=n(2),o=n(11);function DismissButton(t){var n=t.label,r=void 0===n?Object(i.__)("Got it","google-site-kit"):n,a=t.onClick,c=t.disabled;return e.createElement(o.Button,{onClick:a,disabled:c,tertiary:!0},r)}DismissButton.propTypes={label:a.a.string,onClick:a.a.func.isRequired,disabled:a.a.bool}}).call(this,n(3))},107:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o}));var r=n(245),a=n(89),i=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var i=n.invertColor,o=void 0!==i&&i;return Object(r.a)(e.createElement(a.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(3))},108:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return f}));var r=n(5),a=n.n(r),i=n(15),o=n(109),c=n(110);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function f(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=u(u({},l),t);a.referenceSiteURL&&(a.referenceSiteURL=a.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(a,n),f=Object(c.a)(a,n,s,r),g={},d=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);g[r]||(g[r]=Object(i.once)(f)),g[r].apply(g,t)};return{enableTracking:function(){a.trackingEnabled=!0},disableTracking:function(){a.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!a.trackingEnabled},trackEvent:f,trackEventOnce:d}}}).call(this,n(28))},109:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(62),a=n(43),i=n(61);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,u=t.referenceSiteURL,l=t.userIDHash,f=t.userRoles,g=void 0===f?[]:f,d=t.isAuthenticated,p=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(a.b,"]"))),!o){o=!0;var r=(null==g?void 0:g.length)?g.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:u,plugin_version:p||"",enabled_features:Array.from(i.a).join(","),active_modules:s.join(","),authenticated:d?"1":"0",user_properties:{user_roles:r,user_identifier:l}});var f=n.createElement("script");return f.setAttribute(a.b,""),f.async=!0,f.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a),n.head.appendChild(f),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a)}}}}}).call(this,n(28))},11:function(e,t){e.exports=googlesitekit.components},110:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(6),a=n.n(r),i=n(5),o=n.n(i),c=n(16),s=n.n(c),u=n(62);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n,r){var i=Object(u.a)(t);return function(){var t=s()(a.a.mark((function t(o,c,s,u){var l;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),l={send_to:"site_kit",event_category:o,event_label:s,value:u},t.abrupt("return",new Promise((function(e){var t,n,a=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(a),e()};i("event",c,f(f({},l),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,a){return t.apply(this,arguments)}}()}},111:function(e,t,n){"use strict";var r=n(1);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"none"},e),i)}},116:function(e,t,n){"use strict";(function(e){var r=n(20),a=n.n(r),i=n(22),o=n.n(i),c=n(0),s=n.n(c),u=n(10),l=n.n(u);function VisuallyHidden(t){var n=t.className,r=t.children,i=o()(t,["className","children"]);return r?e.createElement("span",a()({},i,{className:l()("screen-reader-text",n)}),r):null}VisuallyHidden.propTypes={className:s.a.string,children:s.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(3))},123:function(e,t,n){"use strict";n.d(t,"a",(function(){return b})),n.d(t,"c",(function(){return v})),n.d(t,"b",(function(){return h}));var r=n(22),a=n.n(r),i=n(5),o=n.n(i),c=n(6),s=n.n(c),u=n(12),l=n.n(u),f=n(4),g=n.n(f),d=n(37),p=n(9),b=function(e){var t;l()(e,"storeName is required to create a snapshot store.");var n={},r={deleteSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"DELETE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),restoreSnapshot:s.a.mark((function e(){var t,n,r,a,i,o,c=arguments;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.clearAfterRestore,r=void 0===n||n,e.next=4,{payload:{},type:"RESTORE_SNAPSHOT"};case 4:if(a=e.sent,i=a.cacheHit,o=a.value,!i){e.next=13;break}return e.next=10,{payload:{snapshot:o},type:"SET_STATE_FROM_SNAPSHOT"};case 10:if(!r){e.next=13;break}return e.next=13,{payload:{},type:"DELETE_SNAPSHOT"};case 13:return e.abrupt("return",i);case 14:case"end":return e.stop()}}),e)})),createSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CREATE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},i=(t={},o()(t,"DELETE_SNAPSHOT",(function(){return Object(d.c)("datastore::cache::".concat(e))})),o()(t,"CREATE_SNAPSHOT",Object(f.createRegistryControl)((function(t){return function(){return Object(d.f)("datastore::cache::".concat(e),t.stores[e].store.getState())}}))),o()(t,"RESTORE_SNAPSHOT",(function(){return Object(d.d)("datastore::cache::".concat(e),p.b)})),t);return{initialState:n,actions:r,controls:i,reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1?arguments[1]:void 0,r=t.type,i=t.payload;switch(r){case"SET_STATE_FROM_SNAPSHOT":var o=i.snapshot,c=(o.error,a()(o,["error"]));return c;default:return e}}}},m=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Object.values(e.stores).filter((function(e){return Object.keys(e.getActions()).includes("restoreSnapshot")}))},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(m(e).map((function(e){return e.getActions().createSnapshot()})))},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(m(e).map((function(e){return e.getActions().restoreSnapshot()})))}},1299:function(e,t,n){"use strict";n.r(t);var r=n(4),a=n.n(r),i=n(198),o=n.n(i),c=n(2),s=(n(762),n(763),n(941)),u=(n(767),n(942)),l=(n(768),n(769),n(943)),f=n(788),g=n(47),d=n(396),p=n(123),b=n(5),m=n.n(b),v=n(22),h=n.n(v),O=n(6),y=n.n(O),j=n(16),S=n.n(j),E=n(12),_=n.n(E),k=n(46),w=n(30),A=n(375),C=n(207),D=n(19),N=n(13),I=n(63),x=n(8),P=n(29),T={selectors:{areSettingsEditDependenciesLoaded:Object(r.createRegistrySelector)((function(e){return function(){return e(g.g).hasFinishedResolution("getAccounts")}}))}};function R(){return(R=S()(y.a.mark((function e(t){var n,r,a,i,o,c,s,u,l,f,p,b,m;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.select,r=t.dispatch,a=n(g.g).getAccountID(),n(g.g).getContainerID()!==g.b){e.next=16;break}return i=n(w.a).getValue(g.f,"containerName"),e.next=7,r(g.g).createContainer(a,g.d,{containerName:i});case 7:if(o=e.sent,c=o.response,!(s=o.error)){e.next=12;break}return e.abrupt("return",{error:s});case 12:return e.next=14,r(g.g).setContainerID(c.publicId);case 14:return e.next=16,r(g.g).setInternalContainerID(c.containerId);case 16:if(n(g.g).getAMPContainerID()!==g.b){e.next=30;break}return u=n(w.a).getValue(g.f,"ampContainerName"),e.next=21,r(g.g).createContainer(a,g.c,{containerName:u});case 21:if(l=e.sent,f=l.response,!(p=l.error)){e.next=26;break}return e.abrupt("return",{error:p});case 26:return e.next=28,r(g.g).setAMPContainerID(f.publicId);case 28:return e.next=30,r(g.g).setInternalAMPContainerID(f.containerId);case 30:if(!n(g.g).haveSettingsChanged()){e.next=41;break}return e.next=33,r(g.g).saveSettings();case 33:if(b=e.sent,!(m=b.error)){e.next=37;break}return e.abrupt("return",{error:m});case 37:if(!n(D.a).isModuleConnected(P.g)){e.next=41;break}return e.next=41,r(x.r).fetchGetSettings();case 41:return e.next=43,Object(k.invalidateCache)("modules",d.a);case 43:return e.abrupt("return",{});case 44:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function L(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?M(Object(n),!0).forEach((function(t){m()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):M(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var F,G,V,U,B,q,H,W,K=o.a.createModuleStore(d.a,{ownedSettingsSlugs:["accountID","ampContainerID","containerID","internalAMPContainerID","internalContainerID"],storeName:g.g,settingSlugs:["accountID","ampContainerID","containerID","internalContainerID","internalAMPContainerID","useSnippet","ownerID"],submitChanges:function(e){return R.apply(this,arguments)},validateCanSubmitChanges:function(e){var t=Object(I.e)(e),n=t(g.g),r=n.getAccountID,a=n.getContainerID,i=n.getContainers,o=n.getAMPContainerID,c=n.getInternalContainerID,s=n.getInternalAMPContainerID,u=n.haveSettingsChanged,l=n.isDoingSubmitChanges,f=t(N.c),d=f.isAMP,p=f.isSecondaryAMP,b=r();if(_()(!l(),C.a),_()(u(),C.b),_()(Object(A.c)(b),"a valid accountID is required to submit changes"),a()===g.b){var m=e(w.a).getValue(g.f,"containerName");_()(Object(A.e)(m),"a valid container name is required to submit changes");var v=i(b),h=Object(A.a)(m);_()(Object(A.b)(m,v),'a container with "'.concat(h,'" name already exists'))}if(d()){var O=o();if(_()(Object(A.f)(O),"a valid ampContainerID selection is required to submit changes"),Object(A.d)(O)&&_()(Object(A.g)(s()),"a valid internalAMPContainerID is required to submit changes"),O===g.b){var y=e(w.a).getValue(g.f,"ampContainerName");_()(Object(A.e)(y),"a valid container name is required to submit changes");var j=i(b),S=Object(A.a)(y);_()(Object(A.b)(y,j),'an AMP container with "'.concat(S,'" name already exists'))}}d()&&!p()||(_()(Object(A.f)(a()),"a valid containerID selection is required to submit changes"),Object(A.d)(a())&&_()(Object(A.g)(c()),"a valid internalContainerID is required to submit changes"))}});G=(F=K).actions,V=F.selectors,U=h()(F,["actions","selectors"]),B=G.setAmpContainerID,q=h()(G,["setAmpContainerID"]),H=V.getAmpContainerID,W=h()(V,["getAmpContainerID"]);var z=K=L(L({},U),{},{actions:L(L({},q),{},{setAMPContainerID:B}),selectors:L(L({},W),{},{getAMPContainerID:H})}),$=n(276),Y=n(50);function X(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Z(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?X(Object(n),!0).forEach((function(t){m()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):X(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var J=Object(Y.a)({baseName:"getAccounts",controlCallback:function(){return Object(k.get)("modules",d.a,"accounts",null,{useCache:!1})},reducerCallback:function(e,t){return Z(Z({},e),{},{accounts:t})}}),Q={accounts:void 0},ee={resetAccounts:y.a.mark((function e(){var t,n;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:return t=e.sent,n=t.dispatch,e.next=6,{payload:{},type:"RESET_ACCOUNTS"};case 6:n(g.g).invalidateResolutionForStoreSelector("getAccounts");case 7:case"end":return e.stop()}}),e)})),selectAccount:Object(I.f)((function(e){_()(Object($.c)(e),"A valid accountID selection is required to select.")}),y.a.mark((function e(t){var n,a,i,o,c,s,u,l,f;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(n=e.sent,a=n.dispatch,i=n.select,o=n.resolveSelect,t!==i(g.g).getAccountID()){e.next=8;break}return e.abrupt("return");case 8:if(a(g.g).setSettings({accountID:t,containerID:"",internalContainerID:"",ampContainerID:"",internalAMPContainerID:""}),x.a!==t&&!i(g.g).hasExistingTag()){e.next=11;break}return e.abrupt("return");case 11:if(c=i(N.c),s=c.isAMP,u=c.isSecondaryAMP,s()&&!u()){e.next=17;break}return e.next=15,r.commonActions.await(o(g.g).getWebContainers(t));case 15:(l=e.sent).length?1===l.length&&(a(g.g).setContainerID(l[0].publicId),a(g.g).setInternalContainerID(l[0].containerId)):(a(g.g).setContainerID(g.b),a(g.g).setInternalContainerID(""));case 17:if(!s()){e.next=22;break}return e.next=20,r.commonActions.await(o(g.g).getAMPContainers(t));case 20:(f=e.sent).length?1===f.length&&(a(g.g).setAMPContainerID(f[0].publicId),a(g.g).setInternalAMPContainerID(f[0].containerId)):(a(g.g).setAMPContainerID(g.b),a(g.g).setInternalAMPContainerID(""));case 22:case"end":return e.stop()}}),e)})))},te={getAccounts:y.a.mark((function e(){var t,n,a,i,o,c;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(n=e.sent,a=n.select,i=n.dispatch,o=a(g.g).getAccounts()){e.next=11;break}return e.next=9,J.actions.fetchGetAccounts();case 9:c=e.sent,o=c.response;case 11:1!==(null===(t=o)||void 0===t?void 0:t.length)||a(g.g).getAccountID()||i(g.g).selectAccount(o[0].accountId);case 12:case"end":return e.stop()}}),e)}))},ne={getAccounts:function(e){return e.accounts},isDoingGetAccounts:Object(r.createRegistrySelector)((function(e){return function(){return e(g.g).isFetchingGetAccounts()}}))},re=Object(r.combineStores)(J,{initialState:Q,actions:ee,reducer:function(e,t){switch(t.type){case"RESET_ACCOUNTS":return Z(Z({},e),{},{accounts:void 0,settings:Z(Z({},e.settings),{},{accountID:void 0,ampContainerID:void 0,containerID:void 0,internalAMPContainerID:void 0,internalContainerID:void 0})});default:return e}},resolvers:te,selectors:ne}),ae=(re.initialState,re.actions,re.controls,re.reducer,re.resolvers,re.selectors,re),ie=y.a.mark(fe);function oe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?oe(Object(n),!0).forEach((function(t){m()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):oe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var se=Object(Y.a)({baseName:"getContainers",argsToParams:function(e){return{accountID:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.accountID;_()(Object($.b)(t),"A valid accountID is required to fetch containers.")},controlCallback:function(e){var t=e.accountID;return Object(k.get)("modules",d.a,"containers",{accountID:t},{useCache:!1})},reducerCallback:function(e,t,n){var r=n.accountID;return ce(ce({},e),{},{containers:ce(ce({},e.containers),{},m()({},r,t))})}}),ue=Object(Y.a)({baseName:"createContainer",argsToParams:function(e,t,n){return{accountID:e,usageContext:t,containerName:n.containerName}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.accountID,n=e.usageContext,r=e.containerName;_()(Object($.b)(t),"A valid accountID is required to create a container."),_()(Object($.h)(n),"A valid usageContext is required to create a container."),_()(Object($.e)(r),"A valid containerName is required to create a container.")},controlCallback:function(e){var t=e.accountID,n=e.usageContext,r=e.containerName;return Object(k.set)("modules",d.a,"create-container",{accountID:t,usageContext:n,name:r})},reducerCallback:Object(r.createReducer)((function(e,t,n){var r=n.accountID;e.containers[r]||(e.containers[r]=[]),e.containers[r].push(t)}))}),le={createContainer:Object(I.f)((function(e,t,n){var r=n.containerName;_()(Object($.b)(e),"A valid accountID is required to create a container."),_()(Object($.h)(t),"A valid usageContext is required to create a container."),_()(Object($.e)(r),"A valid containerName is required to create a container.")}),y.a.mark((function e(t,n,r){var a,i,o,c;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=r.containerName,e.next=3,ue.actions.fetchCreateContainer(t,n,{containerName:a});case 3:return i=e.sent,o=i.response,c=i.error,e.abrupt("return",{response:o,error:c});case 7:case"end":return e.stop()}}),e)}))),selectContainerByID:Object(I.f)((function(e){_()(Object($.d)(e),"A valid container ID is required to select a container by ID.")}),y.a.mark((function e(t){var n,a,i,o,c,s;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(n=e.sent,a=n.select,i=n.dispatch,o=n.resolveSelect,c=a(g.g).getAccountID(),Object($.b)(c)){e.next=9;break}return e.abrupt("return");case 9:return e.next=11,r.commonActions.await(o(g.g).getContainers(c));case 11:if(s=a(g.g).getContainerByID(c,t)){e.next=14;break}return e.abrupt("return");case 14:s.usageContext.includes(g.d)?(i(g.g).setContainerID(t),i(g.g).setInternalContainerID(s.containerId)):s.usageContext.includes(g.c)&&(i(g.g).setAMPContainerID(t),i(g.g).setInternalAMPContainerID(s.containerId));case 15:case"end":return e.stop()}}),e)})))};function fe(e){var t,n;return y.a.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,r.commonActions.getRegistry();case 2:return t=a.sent,n=t.resolveSelect,a.next=6,r.commonActions.await(n(g.g).getContainers(e));case 6:case"end":return a.stop()}}),ie)}var ge={getContainers:y.a.mark((function e(t){var n;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Object($.b)(t)){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,r.commonActions.getRegistry();case 4:if(n=e.sent,(0,n.select)(g.g).getContainers(t)){e.next=9;break}return e.next=9,se.actions.fetchGetContainers(t);case 9:case"end":return e.stop()}}),e)})),getWebContainers:fe,getAMPContainers:fe},de={getContainerByID:Object(r.createRegistrySelector)((function(e){return function(t,n,r){var a=e(g.g).getContainers(n);if(void 0!==a)return a.find((function(e){var t=e.publicId;return r===t}))||null}})),getWebContainers:Object(r.createRegistrySelector)((function(e){return function(t,n){var r=e(g.g).getContainers(n);if(Array.isArray(r))return r.filter((function(e){return e.usageContext.includes(g.d)}))}})),getAMPContainers:Object(r.createRegistrySelector)((function(e){return function(t,n){var r=e(g.g).getContainers(n);if(Array.isArray(r))return r.filter((function(e){return e.usageContext.includes(g.c)}))}})),getContainers:function(e,t){return e.containers[t]},isDoingGetContainers:Object(r.createRegistrySelector)((function(e){return function(t,n){return e(g.g).isFetchingGetContainers(n)}})),isDoingCreateContainer:function(e){return Object.values(e.isFetchingCreateContainer).some(Boolean)},getPrimaryContainerID:Object(r.createRegistrySelector)((function(e){return function(){var t=e(N.c).isPrimaryAMP();if(void 0!==t)return t?e(g.g).getAMPContainerID():e(g.g).getContainerID()}}))},pe=Object(r.combineStores)(se,ue,{initialState:{containers:{}},actions:le,controls:{},resolvers:ge,selectors:de}),be=(pe.initialState,pe.actions,pe.controls,pe.reducer,pe.resolvers,pe.selectors,pe),me=n(583),ve=n(765),he=Object(me.a)({storeName:g.g,tagMatchers:ve.a,isValidTag:$.d}),Oe=(he.initialState,he.actions,he.controls,he.reducer,he.resolvers,he.selectors,he),ye=n(72);function je(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Se(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?je(Object(n),!0).forEach((function(t){m()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):je(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ee,_e=Object(Y.a)({baseName:"getLiveContainerVersion",argsToParams:function(e,t){return{accountID:e,internalContainerID:t}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.accountID,n=e.internalContainerID;_()(Object($.b)(t),"A valid accountID is required to fetch or receive a live container version."),_()(Object($.g)(n),"A valid internalContainerID is required to fetch or receive a live container version.")},controlCallback:(Ee=S()(y.a.mark((function e(t){var n,r;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.accountID,r=t.internalContainerID,e.prev=1,e.next=4,Object(k.get)("modules",d.a,"live-container-version",{accountID:n,internalContainerID:r},{useCache:!1});case 4:return e.abrupt("return",e.sent);case 7:if(e.prev=7,e.t0=e.catch(1),404!==e.t0.code||!e.t0.message.includes("container version not found")){e.next=11;break}return e.abrupt("return",null);case 11:throw e.t0;case 12:case"end":return e.stop()}}),e,null,[[1,7]])}))),function(e){return Ee.apply(this,arguments)}),reducerCallback:function(e,t,n){var r=n.accountID,a=n.internalContainerID;return Se(Se({},e),{},{liveContainerVersions:Se(Se({},e.liveContainerVersions),{},m()({},"".concat(r,"::").concat(a),t))})}}),ke={getLiveContainerVersion:y.a.mark((function e(t,n){var a;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Object($.b)(t)&&Object($.g)(n)){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,r.commonActions.getRegistry();case 4:if(a=e.sent,void 0!==(0,a.select)(g.g).getLiveContainerVersion(t,n)){e.next=9;break}return e.next=9,_e.actions.fetchGetLiveContainerVersion(t,n);case 9:case"end":return e.stop()}}),e)}))},we={getLiveContainerGoogleTag:Object(r.createRegistrySelector)((function(e){return function(t,n,r){var a=e(g.g).getLiveContainerVersion(n,r);if(void 0!==a)return(null==a?void 0:a.tag)&&a.tag.find((function(e){return"googtag"===e.type}))||null}})),getLiveContainerGoogleTagID:Object(r.createRegistrySelector)((function(e){return function(t,n,r){var a=e(g.g).getLiveContainerGoogleTag(n,r);if(void 0!==a){if(null==a?void 0:a.parameter){var i,o,c=null===(i=a.parameter.find((function(e){return"tagId"===e.key})))||void 0===i?void 0:i.value;if(null===(o=c)||void 0===o?void 0:o.startsWith("{{")){var s;c=c.replace(/(\{\{|\}\})/g,"");var u=e(g.g).getLiveContainerVariable(n,r,c);c=null==u||null===(s=u.parameter.find((function(e){return"value"===e.key})))||void 0===s?void 0:s.value}if(Object(ye.c)(c))return c}return null}}})),getCurrentGTMGoogleTagID:Object(r.createRegistrySelector)((function(e){return function(){var t=e(g.g).getAccountID();if(!Object($.b)(t))return null;var n=e(g.g).getInternalContainerID();return Object($.g)(n)?e(g.g).getLiveContainerGoogleTagID(t,n):null}})),getLiveContainerVariable:Object(r.createRegistrySelector)((function(e){return function(t,n,r,a){var i=e(g.g).getLiveContainerVersion(n,r);if(void 0!==i)return(null==i?void 0:i.variable)&&i.variable.find((function(e){return e.name===a}))||null}})),getLiveContainerVersion:function(e,t,n){return e.liveContainerVersions["".concat(t,"::").concat(n)]},isDoingGetLiveContainerVersion:Object(r.createRegistrySelector)((function(e){return function(t,n,r){return e(g.g).isFetchingGetLiveContainerVersion(n,r)}}))},Ae=Object(r.combineStores)(_e,{initialState:{liveContainerVersions:{}},resolvers:ke,selectors:we}),Ce=(Ae.initialState,Ae.actions,Ae.controls,Ae.reducer,Ae.resolvers,Ae.selectors,Ae),De=n(176),Ne=n(7),Ie={selectors:{getServiceURL:Object(r.createRegistrySelector)((function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.path,a=n.query,i="https://tagmanager.google.com/";if(a&&(i=Object(De.a)(i,a)),r){var o="/".concat(r.replace(/^\//,""));i="".concat(i,"#").concat(o)}var c=e(Ne.a).getAccountChooserURL(i);if(void 0!==c)return c}}))}},xe=Object(r.combineStores)(z,ae,be,Oe,Ce,Object(p.a)(g.g),T,Ie);xe.initialState,xe.actions,xe.controls,xe.reducer,xe.resolvers,xe.selectors;a.a.registerStore(g.g,xe),o.a.registerModule(d.a,{storeName:g.g,SettingsEditComponent:u.a,SettingsViewComponent:l.a,SetupComponent:s.a,Icon:f.a,features:[Object(c.__)("You will not be able to create tags without updating code","google-site-kit")]})},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var r="core/site",a="primary",i="secondary"},130:function(e,t,n){"use strict";n.d(t,"d",(function(){return c})),n.d(t,"b",(function(){return s})),n.d(t,"a",(function(){return u})),n.d(t,"c",(function(){return l})),n.d(t,"e",(function(){return f}));var r=n(36),a=n.n(r),i=n(15),o=n(186);function c(e){var t=function(e){return"string"==typeof e&&/^[a-zA-Z0-9_]+$/.test(e)};return"string"==typeof e?e.split(",").every(t):Object(o.c)(e,(function(e){var n=e.hasOwnProperty("name")&&t(e.name);if(!e.hasOwnProperty("expression"))return n;var r="string"==typeof e.expression;return n&&r}),t)}function s(e){return Object(o.c)(e,(function(e){return e.hasOwnProperty("name")&&"string"==typeof e.name}))}function u(e){var t=["string"];return Object.keys(e).every((function(n){if(t.includes(a()(e[n])))return!0;if(Array.isArray(e[n]))return e[n].every((function(e){return t.includes(a()(e))}));if(Object(i.isPlainObject)(e[n])){var r=Object.keys(e[n]);return!!r.includes("filterType")&&!("emptyFilter"!==e[n].filterType&&!r.includes("value"))}return!1}))}function l(e){var t=["string"],n=["numericFilter","betweenFilter"];return Object.values(e).every((function(e){if(t.includes(a()(e)))return!0;if(Array.isArray(e))return e.every((function(e){return t.includes(a()(e))}));if(!Object(i.isPlainObject)(e))return!1;var r=e.filterType,o=e.value,c=e.fromValue,s=e.toValue;if(r&&!n.includes(r))return!1;var u=Object.keys(e);return r&&"numericFilter"!==r?"betweenFilter"===r&&(u.includes("fromValue")&&u.includes("toValue")&&[c,s].every((function(e){return!Object(i.isPlainObject)(e)||"int64Value"in e}))):u.includes("operation")&&u.includes("value")&&(!Object(i.isPlainObject)(o)||"int64Value"in o)}))}function f(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(i.isPlainObject)(e)&&((!e.hasOwnProperty("desc")||"boolean"==typeof e.desc)&&(e.metric?!e.dimension&&"string"==typeof(null===(t=e.metric)||void 0===t?void 0:t.metricName):!!e.dimension&&"string"==typeof(null===(n=e.dimension)||void 0===n?void 0:n.dimensionName)));var t,n}))}},134:function(e,t,n){"use strict";var r=n(1);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},135:function(e,t,n){"use strict";var r=n(1);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},136:function(e,t,n){"use strict";var r=n(1);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},139:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Icon}));var r,a=n(5),i=n.n(a),o=n(0),c=n.n(o),s=n(111),u=n(71),l=n(97),f=n(38),g=(r={},i()(r,f.a.NEW,l.a),i()(r,f.a.SUCCESS,s.a),i()(r,f.a.INFO,u.a),i()(r,f.a.WARNING,u.a),i()(r,f.a.ERROR,u.a),r);function Icon(t){var n=t.type,r=g[n]||u.a;return e.createElement(r,{width:24,height:24})}Icon.propTypes={type:c.a.oneOf(Object.values(f.a))}}).call(this,n(3))},140:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Title}));var r=n(10),a=n.n(r),i=n(0),o=n.n(i);function Title(t){var n=t.className,r=t.children;return e.createElement("p",{className:a()("googlesitekit-notice__title",n)},r)}Title.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(10),a=n.n(r),i=n(0),o=n.n(i);function Description(t){var n=t.className,r=t.children;return e.createElement("p",{className:a()("googlesitekit-notice__description",n)},r)}Description.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},153:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StoreErrorNotices}));var r=n(0),a=n.n(r),i=n(4),o=n(59),c=n(19),s=n(34),u=n(177);function StoreErrorNotices(t){var n=t.hasButton,r=void 0!==n&&n,a=t.moduleSlug,l=t.storeName,f=Object(i.useSelect)((function(e){return e(l).getErrors()})),g=Object(i.useSelect)((function(e){return e(c.a).getModule(a)})),d=[];return f.filter((function(e){return!(!(null==e?void 0:e.message)||d.includes(e.message))&&(d.push(e.message),!0)})).map((function(t,n){var a=t.message;return Object(s.e)(t)&&(a=Object(u.a)(a,g)),e.createElement(o.a,{key:n,error:t,hasButton:r,storeName:l,message:a})}))}StoreErrorNotices.propTypes={hasButton:a.a.bool,storeName:a.a.string.isRequired,moduleSlug:a.a.string}}).call(this,n(3))},177:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),a=n(86),i=n(29);function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},o=n.slug,c=void 0===o?"":o,s=n.name,u=void 0===s?"":s,l=n.owner,f=void 0===l?{}:l;if(!c||!u)return e;var g="",d="";return i.g===c?e.match(/account/i)?g=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?g=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(g=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):a.a===c&&(g=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),g||(g=Object(r.sprintf)(/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),u)),f&&f.login&&(d=Object(r.sprintf)(/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),f.login)),d||(d=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(g," ").concat(d)}},18:function(e,t,n){"use strict";var r=n(1),a=n(65);t.a=function(){return Object(r.useContext)(a.b)}},186:function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"d",(function(){return s}));var r=n(36),a=n.n(r);function i(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return"string"==typeof e?n(e):!("object"!==a()(e)||!t(e))||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e?n(e):"object"===a()(e)&&t(e)}))}function o(e){var t=e.startDate,n=e.endDate,r=t&&t.match(/^\d{4}-\d{2}-\d{2}$/),a=n&&n.match(/^\d{4}-\d{2}-\d{2}$/);return r&&a}function c(e){var t=function(e){var t=e.hasOwnProperty("fieldName")&&!!e.fieldName,n=e.hasOwnProperty("sortOrder")&&/(ASCENDING|DESCENDING)/i.test(e.sortOrder.toString());return t&&n};return Array.isArray(e)?e.every((function(e){return"object"===a()(e)&&t(e)})):"object"===a()(e)&&t(e)}function s(e){return"string"==typeof e||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e}))}},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="core/modules",a="insufficient_module_dependencies"},192:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(0),a=n.n(r),i=" ";function DisplaySetting(e){return e.value||i}DisplaySetting.propTypes={value:a.a.oneOfType([a.a.string,a.a.bool,a.a.number])},t.b=DisplaySetting},198:function(e,t){e.exports=googlesitekit.modules},2:function(e,t){e.exports=googlesitekit.i18n},207:function(e,t,n){"use strict";n.d(t,"a",(function(){return S})),n.d(t,"b",(function(){return E})),n.d(t,"c",(function(){return _})),n.d(t,"g",(function(){return k})),n.d(t,"f",(function(){return w})),n.d(t,"d",(function(){return A})),n.d(t,"e",(function(){return C}));var r=n(16),a=n.n(r),i=n(6),o=n.n(i),c=n(5),s=n.n(c),u=n(12),l=n.n(u),f=n(15),g=n(46),d=n(4),p=n(63),b=n(93),m=n(50),v=n(68);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var y=v.a.clearError,j=v.a.receiveError,S="cannot submit changes while submitting changes",E="cannot submit changes if settings have not changed",_=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=r.ownedSettingsSlugs,i=void 0===a?void 0:a,c=r.storeName,u=void 0===c?void 0:c,v=r.settingSlugs,h=void 0===v?[]:v,S=r.initialSettings,E=void 0===S?void 0:S,_=r.validateHaveSettingsChanged,k=void 0===_?C():_;l()(e,"type is required."),l()(t,"identifier is required."),l()(n,"datapoint is required.");var w=u||"".concat(e,"/").concat(t),A={ownedSettingsSlugs:i,settings:E,savedSettings:void 0},D=Object(m.a)({baseName:"getSettings",controlCallback:function(){return Object(g.get)(e,t,n,{},{useCache:!1})},reducerCallback:function(e,t){return O(O({},e),{},{savedSettings:O({},t),settings:O(O({},t),e.settings||{})})}}),N=Object(m.a)({baseName:"saveSettings",controlCallback:function(r){var a=r.values;return Object(g.set)(e,t,n,a)},reducerCallback:function(e,t){return O(O({},e),{},{savedSettings:O({},t),settings:O({},t)})},argsToParams:function(e){return{values:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.values;l()(Object(f.isPlainObject)(t),"values is required.")}}),I={},x={setSettings:function(e){return l()(Object(f.isPlainObject)(e),"values is required."),{payload:{values:e},type:"SET_SETTINGS"}},rollbackSettings:function(){return{payload:{},type:"ROLLBACK_SETTINGS"}},rollbackSetting:function(e){return l()(e,"setting is required."),{payload:{setting:e},type:"ROLLBACK_SETTING"}},saveSettings:o.a.mark((function e(){var t,n,r,a,i;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,y("saveSettings",[]);case 5:return n=t.select(w).getSettings(),e.next=8,N.actions.fetchSaveSettings(n);case 8:if(r=e.sent,a=r.response,!(i=r.error)){e.next=14;break}return e.next=14,j(i,"saveSettings",[]);case 14:return e.abrupt("return",{response:a,error:i});case 15:case"end":return e.stop()}}),e)}))},P={},T=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:A,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"SET_SETTINGS":var a=r.values;return O(O({},e),{},{settings:O(O({},e.settings||{}),a)});case"ROLLBACK_SETTINGS":return O(O({},e),{},{settings:e.savedSettings});case"ROLLBACK_SETTING":var i=r.setting;return e.savedSettings[i]?O(O({},e),{},{settings:O(O({},e.settings||{}),{},s()({},i,e.savedSettings[i]))}):O({},e);default:return void 0!==I[n]?I[n](e,{type:n,payload:r}):e}},R={getSettings:o.a.mark((function e(){var t;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d.commonActions.getRegistry();case 2:if(t=e.sent,t.select(w).getSettings()){e.next=7;break}return e.next=7,D.actions.fetchGetSettings();case 7:case"end":return e.stop()}}),e)}))},M=Object(p.g)(k),L=M.safeSelector,F=M.dangerousSelector,G={haveSettingsChanged:L,__dangerousHaveSettingsChanged:F,getSettings:function(e){return e.settings},hasSettingChanged:function(e,t){l()(t,"setting is required.");var n=e.settings,r=e.savedSettings;return!(!n||!r)&&!Object(f.isEqual)(n[t],r[t])},isDoingSaveSettings:function(e){return Object.values(e.isFetchingSaveSettings).some(Boolean)},getOwnedSettingsSlugs:function(e){return e.ownedSettingsSlugs},haveOwnedSettingsChanged:Object(d.createRegistrySelector)((function(e){return function(){var t=e(w).getOwnedSettingsSlugs();return e(w).haveSettingsChanged(t)}}))};h.forEach((function(e){var t=Object(b.b)(e),n=Object(b.a)(e);x["set".concat(t)]=function(e){return l()(void 0!==e,"value is required for calls to set".concat(t,"().")),{payload:{value:e},type:"SET_".concat(n)}},I["SET_".concat(n)]=function(t,n){var r=n.payload.value;return O(O({},t),{},{settings:O(O({},t.settings||{}),{},s()({},e,r))})},G["get".concat(t)]=Object(d.createRegistrySelector)((function(t){return function(){return(t(w).getSettings()||{})[e]}}))}));var V=Object(d.combineStores)(d.commonStore,D,N,{initialState:A,actions:x,controls:P,reducer:T,resolvers:R,selectors:G});return O(O({},V),{},{STORE_NAME:w})};function k(e,t){return function(){var n=a()(o.a.mark((function n(r){var a,i,c,s;return o.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(a=r.select,i=r.dispatch,!a(t).haveSettingsChanged()){n.next=8;break}return n.next=4,i(t).saveSettings();case 4:if(c=n.sent,!(s=c.error)){n.next=8;break}return n.abrupt("return",{error:s});case 8:return n.next=10,Object(g.invalidateCache)("modules",e);case 10:return n.abrupt("return",{});case 11:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function w(e){return function(t){var n=t.select,r=t.dispatch;return n(e).haveSettingsChanged()?r(e).rollbackSettings():{}}}function A(e){return function(t){var n=Object(p.e)(t)(e),r=n.haveSettingsChanged,a=n.isDoingSubmitChanges;l()(!a(),S),l()(r(),E)}}function C(){return function(e,t,n){var r=t.settings,a=t.savedSettings;n&&l()(!Object(f.isEqual)(Object(f.pick)(r,n),Object(f.pick)(a,n)),E),l()(!Object(f.isEqual)(r,a),E)}}},21:function(e,t,n){"use strict";(function(e){var r=n(20),a=n.n(r),i=n(22),o=n.n(i),c=n(10),s=n.n(c),u=n(0),l=n.n(u),f=n(158),g=n(1),d=n(2),p=n(134),b=n(135),m=n(136),v=n(73),h=n(78),O=Object(g.forwardRef)((function(t,n){var r,i=t["aria-label"],c=t.secondary,u=void 0!==c&&c,l=t.arrow,g=void 0!==l&&l,O=t.back,y=void 0!==O&&O,j=t.caps,S=void 0!==j&&j,E=t.children,_=t.className,k=void 0===_?"":_,w=t.danger,A=void 0!==w&&w,C=t.disabled,D=void 0!==C&&C,N=t.external,I=void 0!==N&&N,x=t.hideExternalIndicator,P=void 0!==x&&x,T=t.href,R=void 0===T?"":T,M=t.inverse,L=void 0!==M&&M,F=t.noFlex,G=void 0!==F&&F,V=t.onClick,U=t.small,B=void 0!==U&&U,q=t.standalone,H=void 0!==q&&q,W=t.linkButton,K=void 0!==W&&W,z=t.to,$=t.leadingIcon,Y=t.trailingIcon,X=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),Z=R||z||!V?z?"ROUTER_LINK":I?"EXTERNAL_LINK":"LINK":D?"BUTTON_DISABLED":"BUTTON",J="BUTTON"===Z||"BUTTON_DISABLED"===Z?"button":"ROUTER_LINK"===Z?f.b:"a",Q=("EXTERNAL_LINK"===Z&&(r=Object(d._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===Z&&(r=Object(d._x)("(disabled)","screen reader text","google-site-kit")),r?i?"".concat(i," ").concat(r):"string"==typeof E?"".concat(E," ").concat(r):void 0:i),ee=$,te=Y;return y&&(ee=e.createElement(m.a,{width:14,height:14})),I&&!P&&(te=e.createElement(v.a,{width:14,height:14})),g&&!L&&(te=e.createElement(p.a,{width:14,height:14})),g&&L&&(te=e.createElement(b.a,{width:14,height:14})),e.createElement(J,a()({"aria-label":Q,className:s()("googlesitekit-cta-link",k,{"googlesitekit-cta-link--secondary":u,"googlesitekit-cta-link--inverse":L,"googlesitekit-cta-link--small":B,"googlesitekit-cta-link--caps":S,"googlesitekit-cta-link--danger":A,"googlesitekit-cta-link--disabled":D,"googlesitekit-cta-link--standalone":H,"googlesitekit-cta-link--link-button":K,"googlesitekit-cta-link--no-flex":!!G}),disabled:D,href:"LINK"!==Z&&"EXTERNAL_LINK"!==Z||D?void 0:R,onClick:V,rel:"EXTERNAL_LINK"===Z?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===Z?"_blank":void 0,to:z},X),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},E),!!te&&e.createElement(h.a,{marginLeft:5},te))}));O.propTypes={arrow:l.a.bool,back:l.a.bool,caps:l.a.bool,children:l.a.node,className:l.a.string,danger:l.a.bool,disabled:l.a.bool,external:l.a.bool,hideExternalIndicator:l.a.bool,href:l.a.string,inverse:l.a.bool,leadingIcon:l.a.node,linkButton:l.a.bool,noFlex:l.a.bool,onClick:l.a.func,small:l.a.bool,standalone:l.a.bool,to:l.a.string,trailingIcon:l.a.node},t.a=O}).call(this,n(3))},276:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"d",(function(){return s})),n.d(t,"e",(function(){return u})),n.d(t,"a",(function(){return l})),n.d(t,"f",(function(){return f})),n.d(t,"g",(function(){return g})),n.d(t,"h",(function(){return d}));var r=n(9),a=n(47),i=n(627);function o(e){return Object(r.x)(e)}function c(e){return e===a.a||o(e)}function s(e){return"string"==typeof e&&/^GTM-[A-Z0-9]+$/.test(e)}function u(e){return"string"==typeof e&&Object(i.a)(e).length>0}function l(e,t){var n=Object(i.a)(e);return!Array.isArray(t)||!t.some((function(e){var t=e.name;return Object(i.a)(t)===n}))}function f(e){return e===a.b||s(e)}function g(e){return Object(r.x)(e)}function d(e){return[a.d,a.c].includes(e)}},278:function(e,t,n){"use strict";(function(e){var r=n(56),a=n.n(r),i=n(279),o=e._googlesitekitAPIFetchData||{},c=o.nonce,s=o.nonceEndpoint,u=o.preloadedData,l=o.rootURL;a.a.nonceEndpoint=s,a.a.nonceMiddleware=a.a.createNonceMiddleware(c),a.a.rootURLMiddleware=a.a.createRootURLMiddleware(l),a.a.preloadingMiddleware=Object(i.a)(u),a.a.use(a.a.nonceMiddleware),a.a.use(a.a.mediaUploadMiddleware),a.a.use(a.a.rootURLMiddleware),a.a.use(a.a.preloadingMiddleware),t.default=a.a}).call(this,n(28))},279:function(e,t,n){"use strict";var r=n(269);t.a=function(e){var t=Object.keys(e).reduce((function(t,n){return t[Object(r.getStablePath)(n)]=e[n],t}),{}),n=!1;return function(e,a){if(n)return a(e);setTimeout((function(){n=!0}),3e3);var i=e.parse,o=void 0===i||i,c=e.path;if("string"==typeof e.path){var s,u=(null===(s=e.method)||void 0===s?void 0:s.toUpperCase())||"GET",l=Object(r.getStablePath)(c);if(o&&"GET"===u&&t[l]){var f=Promise.resolve(t[l].body);return delete t[l],f}if("OPTIONS"===u&&t[u]&&t[u][l]){var g=Promise.resolve(t[u][l]);return delete t[u][l],g}}return a(e)}}},280:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(5),a=n.n(r),i=n(22),o=n.n(i),c=n(66),s=n.n(c),u=n(15);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var g=s()((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.metrics,n=e.dimensions,r=o()(e,["metrics","dimensions"]);return f({metrics:d(t),dimensions:p(n)},r)})),d=function(e){return Object(u.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(u.isPlainObject)(e)}))},p=function(e){return Object(u.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(u.isPlainObject)(e)}))}},29:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"f",(function(){return c})),n.d(t,"k",(function(){return s})),n.d(t,"j",(function(){return u})),n.d(t,"h",(function(){return l})),n.d(t,"i",(function(){return f})),n.d(t,"e",(function(){return g})),n.d(t,"g",(function(){return d}));var r=1,a=2,i=3,o="enhanced-measurement-activation-banner-tooltip-state",c="enhanced-measurement-activation-banner-dismissed-item",s="_r.explorerCard..selmet",u="_r.explorerCard..seldim",l="_r..dataFilters",f="_r..nav",g="key-metrics-connect-ga4-cta-widget",d="analytics-4"},30:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},33:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return u})),n.d(t,"d",(function(){return l})),n.d(t,"b",(function(){return f}));n(15);var r=n(2),a="missing_required_scopes",i="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===a}function s(e){var t;return[i,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function u(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function l(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||u(e))}function f(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},35:function(e,t,n){"use strict";(function(e){var r=n(5),a=n.n(r),i=n(0),o=n.n(i),c=n(10),s=n.n(c),u=n(1),l=n(139),f=n(140),g=n(141),d=n(105),p=n(106),b=n(38);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=Object(u.forwardRef)((function(t,n){var r=t.className,a=t.title,i=t.description,o=t.dismissButton,c=t.ctaButton,u=t.type,m=void 0===u?b.a.INFO:u,v=t.children,h=t.hideIcon;return e.createElement("div",{ref:n,className:s()("googlesitekit-notice","googlesitekit-notice--".concat(m),r)},!h&&e.createElement("div",{className:"googlesitekit-notice__icon"},e.createElement(l.a,{type:m})),e.createElement("div",{className:"googlesitekit-notice__content"},a&&e.createElement(f.a,null,a),i&&e.createElement(g.a,null,i)),((null==o?void 0:o.label)||(null==o?void 0:o.onClick)||(null==c?void 0:c.label)&&((null==c?void 0:c.onClick)||(null==c?void 0:c.href))||v)&&e.createElement("div",{className:"googlesitekit-notice__action"},v,((null==o?void 0:o.label)||(null==o?void 0:o.onClick))&&e.createElement(p.a,{label:o.label,onClick:o.onClick,disabled:o.disabled}),(null==c?void 0:c.label)&&((null==c?void 0:c.onClick)||(null==c?void 0:c.href))&&e.createElement(d.a,{label:c.label,onClick:c.onClick,inProgress:c.inProgress,disabled:c.disabled,href:c.href,external:c.external,hideExternalIndicator:c.hideExternalIndicator})))}));h.TYPES=b.a,h.propTypes={className:o.a.string,title:o.a.oneOfType([o.a.string,o.a.object]),description:o.a.node,type:o.a.oneOf(Object.values(b.a)),dismissButton:o.a.shape(p.a.propTypes),ctaButton:o.a.shape(v(v({},d.a.propTypes),{},{label:o.a.string})),children:o.a.node,hideIcon:o.a.bool},t.a=h}).call(this,n(3))},357:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(15),a=n(130);function i(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(r.isPlainObject)(e)&&(!(!e.hasOwnProperty("fieldNames")||!Array.isArray(e.fieldNames)||0===e.fieldNames.length)&&(!(!e.hasOwnProperty("limit")||"number"!=typeof e.limit)&&!(e.hasOwnProperty("orderby")&&!Object(a.e)(e.orderby))))}))}},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return f})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return O})),n.d(t,"c",(function(){return y})),n.d(t,"e",(function(){return j})),n.d(t,"b",(function(){return S}));var r=n(6),a=n.n(r),i=n(16),o=n.n(i),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return u(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var l,f="googlesitekit_",g="".concat(f).concat("1.157.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),d=["sessionStorage","localStorage"],p=[].concat(d),b=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,i="__storage_test__",r.setItem(i,i),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function m(){return v.apply(this,arguments)}function v(){return(v=o()(a.a.mark((function t(){var n,r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===l){t.next=2;break}return t.abrupt("return",l);case 2:n=s(p),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(i=r.value,!l){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,b(i);case 11:if(!t.sent){t.next=13;break}l=e[i];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===l&&(l=null),t.abrupt("return",l);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(a.a.mark((function e(t){var n,r,i,o,c,s,u;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(g).concat(t)))){e.next=10;break}if(i=JSON.parse(r),o=i.timestamp,c=i.ttl,s=i.value,u=i.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:u});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),O=function(){var t=o()(a.a.mark((function t(n,r){var i,o,s,u,l,f,d,p,b=arguments;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=b.length>2&&void 0!==b[2]?b[2]:{},o=i.ttl,s=void 0===o?c.b:o,u=i.timestamp,l=void 0===u?Math.round(Date.now()/1e3):u,f=i.isError,d=void 0!==f&&f,t.next=3,m();case 3:if(!(p=t.sent)){t.next=14;break}return t.prev=5,p.setItem("".concat(g).concat(n),JSON.stringify({timestamp:l,ttl:s,value:r,isError:d})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),y=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,m();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,i=n.startsWith(f)?n:"".concat(g).concat(n),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),j=function(){var t=o()(a.a.mark((function t(){var n,r,i,o;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,m();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],i=0;i<n.length;i++)0===(o=n.key(i)).indexOf(f)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),S=function(){var e=o()(a.a.mark((function e(){var t,n,r,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m();case 2:if(!e.sent){e.next=25;break}return e.next=6,j();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return i=r.value,e.next=14,y(i);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},373:function(e,t,n){"use strict";var r=n(934);n.d(t,"b",(function(){return r.a}));var a=n(935);n.d(t,"c",(function(){return a.a}));n(764);var i=n(936);n.d(t,"a",(function(){return i.a}));var o=n(937);n.d(t,"d",(function(){return o.a}));n(620),n(621);var c=n(938);n.d(t,"e",(function(){return c.a}));var s=n(939);n.d(t,"f",(function(){return s.a}));n(622),n(766);var u=n(940);n.d(t,"g",(function(){return u.a}))},375:function(e,t,n){"use strict";n.d(t,"a",(function(){return r.a})),n.d(t,"c",(function(){return a.b})),n.d(t,"d",(function(){return a.d})),n.d(t,"e",(function(){return a.e})),n.d(t,"b",(function(){return a.a})),n.d(t,"f",(function(){return a.f})),n.d(t,"g",(function(){return a.g}));var r=n(627),a=n(276);n(765)},38:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={NEW:"new",SUCCESS:"success",WARNING:"warning",INFO:"info",ERROR:"error"}},389:function(e,t,n){"use strict";var r=n(1);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M0 19h22L11 0 0 19zm12-3h-2v-2h2v2zm0-4h-2V8h2v4z",fill:"currentColor"});t.a=function SvgWarningV2(e){return r.createElement("svg",a({viewBox:"0 0 22 19"},e),i)}},395:function(e,t,n){"use strict";n.d(t,"a",(function(){return p})),n.d(t,"b",(function(){return b}));var r=n(6),a=n.n(r),i=n(16),o=n.n(i),c=n(12),s=n.n(c),u=n(15),l=n(278),f=n(115),g=n(176),d=n(13),p=function(e,t){var n=t.find((function(t){return t.test(e)}));return!!n&&n.exec(e)[1]},b=Object(u.memoize)(function(){var e=o()(a.a.mark((function e(t){var n,r,i,o;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.homeURL,r=t.ampMode,s()(Object(f.a)(n),"homeURL must be valid URL"),i=[n],d.b!==r){e.next=14;break}return e.prev=4,e.next=7,Object(l.default)({path:"/wp/v2/posts?per_page=1"}).then((function(e){return e.slice(0,1).map((function(e){return Object(g.a)(e.link,{amp:1})})).pop()}));case 7:(o=e.sent)&&i.push(o),e.next=14;break;case 11:return e.prev=11,e.t0=e.catch(4),e.abrupt("return",i);case 14:return e.abrupt("return",i);case 15:case"end":return e.stop()}}),e,null,[[4,11]])})));return function(t){return e.apply(this,arguments)}}())},396:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="tagmanager"},4:function(e,t){e.exports=googlesitekit.data},40:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return y})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return O}));var r=n(108),a=e._googlesitekitTrackingData||{},i=a.activeModules,o=void 0===i?[]:i,c=a.isSiteKitScreen,s=a.trackingEnabled,u=a.trackingID,l=a.referenceSiteURL,f=a.userIDHash,g=a.isAuthenticated,d={activeModules:o,trackingEnabled:s,trackingID:u,referenceSiteURL:l,userIDHash:f,isSiteKitScreen:c,userRoles:a.userRoles,isAuthenticated:g,pluginVersion:"1.157.0"},p=Object(r.a)(d),b=p.enableTracking,m=p.disableTracking,v=(p.isTrackingEnabled,p.initializeSnippet),h=p.trackEvent,O=p.trackEventOnce;function y(e){e?b():m()}c&&s&&v()}).call(this,n(28))},43:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="_googlesitekitDataLayer",a="data-googlesitekit-gtag"},448:function(e,t,n){"use strict";function r(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return e.reduce((function(e,t,r){return e+t+encodeURIComponent(n[r]||"")}),"")}n.d(t,"a",(function(){return r}))},46:function(e,t){e.exports=googlesitekit.api},47:function(e,t,n){"use strict";n.d(t,"g",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"f",(function(){return s})),n.d(t,"h",(function(){return u})),n.d(t,"e",(function(){return l})),n.d(t,"i",(function(){return f}));var r="modules/tagmanager",a="account_create",i="container_create",o="web",c="amp",s="tagmanagerSetup",u="https://www.googleapis.com/auth/tagmanager.readonly",l="https://www.googleapis.com/auth/tagmanager.edit.containers",f="SETUP_WITH_ANALYTICS"},50:function(e,t,n){"use strict";n.d(t,"a",(function(){return y}));var r=n(6),a=n.n(r),i=n(5),o=n.n(i),c=n(12),s=n.n(c),u=n(15),l=n(68),f=n(93),g=n(9);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var b=function(e){return e},m=function(){return{}},v=function(){},h=l.a.clearError,O=l.a.receiveError,y=function(e){var t,n,r=a.a.mark(R),i=e.baseName,c=e.controlCallback,l=e.reducerCallback,d=void 0===l?b:l,y=e.argsToParams,j=void 0===y?m:y,S=e.validateParams,E=void 0===S?v:S;s()(i,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof d,"reducerCallback must be a function."),s()("function"==typeof j,"argsToParams must be a function."),s()("function"==typeof E,"validateParams must be a function.");try{E(j()),n=!1}catch(e){n=!0}var _=Object(f.b)(i),k=Object(f.a)(i),w="FETCH_".concat(k),A="START_".concat(w),C="FINISH_".concat(w),D="CATCH_".concat(w),N="RECEIVE_".concat(k),I="fetch".concat(_),x="receive".concat(_),P="isFetching".concat(_),T=o()({},P,{});function R(e,t){var n,o;return a.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,{payload:{params:e},type:A};case 2:return r.next=4,h(i,t);case 4:return r.prev=4,r.next=7,{payload:{params:e},type:w};case 7:return n=r.sent,r.next=10,M[x](n,e);case 10:return r.next=12,{payload:{params:e},type:C};case 12:r.next=21;break;case 14:return r.prev=14,r.t0=r.catch(4),o=r.t0,r.next=19,O(o,i,t);case 19:return r.next=21,{payload:{params:e},type:D};case 21:return r.abrupt("return",{response:n,error:o});case 22:case"end":return r.stop()}}),r,null,[[4,14]])}var M=(t={},o()(t,I,(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=j.apply(void 0,t);return E(r),R(r,t)})),o()(t,x,(function(e,t){return s()(void 0!==e,"response is required."),n?(s()(Object(u.isPlainObject)(t),"params is required."),E(t)):t={},{payload:{response:e,params:t},type:N}})),t),L=o()({},w,(function(e){var t=e.payload;return c(t.params)})),F=o()({},P,(function(e){if(void 0===e[P])return!1;var t;try{for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];t=j.apply(void 0,r),E(t)}catch(e){return!1}return!!e[P][Object(g.H)(t)]}));return{initialState:T,actions:M,controls:L,reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case A:var a=r.params;return p(p({},e),{},o()({},P,p(p({},e[P]),{},o()({},Object(g.H)(a),!0))));case N:var i=r.response,c=r.params;return d(e,i,c);case C:var s=r.params;return p(p({},e),{},o()({},P,p(p({},e[P]),{},o()({},Object(g.H)(s),!1))));case D:var u=r.params;return p(p({},e),{},o()({},P,p(p({},e[P]),{},o()({},Object(g.H)(u),!1))));default:return e}},resolvers:{},selectors:F}}},529:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccessibleWarningIcon}));var r=n(0),a=n.n(r),i=n(1),o=n(2),c=n(116),s=n(389);function AccessibleWarningIcon(t){var n=t.height,r=void 0===n?12:n,a=t.screenReaderText,u=void 0===a?Object(o.__)("Error","google-site-kit"):a,l=t.width,f=void 0===l?14:l;return e.createElement(i.Fragment,null,e.createElement(c.a,null,u),e.createElement(s.a,{width:f,height:r}))}AccessibleWarningIcon.propTypes={height:a.a.number,screenReaderText:a.a.string,width:a.a.number}}).call(this,n(3))},583:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var r=n(5),a=n.n(r),i=n(6),o=n.n(i),c=n(16),s=n.n(c),u=n(12),l=n.n(u),f=n(4),g=n(13),d=n(395);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return v(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return v(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.storeName,n=e.isValidTag,r=e.tagMatchers;l()("string"==typeof t&&t,"storeName is required."),l()("function"==typeof n,"isValidTag must be a function."),l()(Array.isArray(r),"tagMatchers must be an Array.");var i={existingTag:void 0},c={fetchGetExistingTag:function(){return{payload:{},type:"FETCH_GET_EXISTING_TAG"}},receiveGetExistingTag:function(e){return l()(null===e||"string"==typeof e,"existingTag must be a tag string or null."),{payload:{existingTag:n(e)?e:null},type:"RECEIVE_GET_EXISTING_TAG"}}},u=a()({},"FETCH_GET_EXISTING_TAG",Object(f.createRegistryControl)((function(e){return s()(o.a.mark((function t(){var n,a,i,c,s,u,l,f,p,b;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.select(g.c).getHomeURL(),a=e.select(g.c).getAMPMode(),t.next=4,Object(d.b)({homeURL:n,ampMode:a});case 4:i=t.sent,c=e.resolveSelect(g.c),s=c.getHTMLForURL,u=m(i),t.prev=7,u.s();case 9:if((l=u.n()).done){t.next=19;break}return f=l.value,t.next=13,s(f);case 13:if(p=t.sent,!(b=Object(d.a)(p,r))){t.next=17;break}return t.abrupt("return",b);case 17:t.next=9;break;case 19:t.next=24;break;case 21:t.prev=21,t.t0=t.catch(7),u.e(t.t0);case 24:return t.prev=24,u.f(),t.finish(24);case 27:return t.abrupt("return",null);case 28:case"end":return t.stop()}}),t,null,[[7,21,24,27]])})))}))),p=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"RECEIVE_GET_EXISTING_TAG":var a=r.existingTag;return b(b({},e),{},{existingTag:a});default:return e}},v={getExistingTag:o.a.mark((function e(){var n,r;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:if(void 0!==(n=e.sent).select(t).getExistingTag()){e.next=8;break}return e.next=6,c.fetchGetExistingTag();case 6:r=e.sent,n.dispatch(t).receiveGetExistingTag(r);case 8:case"end":return e.stop()}}),e)}))},h={getExistingTag:function(e){return e.existingTag},hasExistingTag:Object(f.createRegistrySelector)((function(e){return function(){var n=e(t).getExistingTag();if(void 0!==n)return!!n}}))},O={initialState:i,actions:c,controls:u,reducer:p,resolvers:v,selectors:h};return b(b({},O),{},{STORE_NAME:t})}},59:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var r=n(0),a=n.n(r),i=n(1),o=n(2),c=n(115),s=n(4),u=n(34),l=n(35),f=n(9);function ErrorNotice(t){var n,r=t.className,a=t.error,g=t.hasButton,d=void 0!==g&&g,p=t.storeName,b=t.message,m=void 0===b?a.message:b,v=t.noPrefix,h=void 0!==v&&v,O=t.skipRetryMessage,y=t.hideIcon,j=void 0!==y&&y,S=Object(s.useDispatch)(),E=Object(s.useSelect)((function(e){return p?e(p).getSelectorDataForError(a):null})),_=Object(i.useCallback)((function(){S(E.storeName).invalidateResolution(E.name,E.args)}),[S,E]);if(!m||Object(u.f)(a))return null;var k=d&&Object(u.d)(a,E),w=m;d||O||(w=Object(o.sprintf)(/* translators: %s: Error message from Google API. */
Object(o.__)("%s (Please try again.)","google-site-kit"),w)),h||(w=Object(o.sprintf)(/* translators: $%s: Error message */
Object(o.__)("Error: %s","google-site-kit"),w));var A=null==a||null===(n=a.data)||void 0===n?void 0:n.reconnectURL;A&&Object(c.a)(A)&&(w=Object(o.sprintf)(/* translators: 1: Original error message 2: Reconnect URL */
Object(o.__)('%1$s To fix this, <a href="%2$s">redo the plugin setup</a>.',"google-site-kit"),w,A));return e.createElement(l.a,{className:r,type:l.a.TYPES.ERROR,description:e.createElement("span",{dangerouslySetInnerHTML:Object(f.F)(w,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}),ctaButton:k?{label:Object(o.__)("Retry","google-site-kit"),onClick:_}:void 0,hideIcon:j})}ErrorNotice.propTypes={className:a.a.string,error:a.a.shape({message:a.a.string}),hasButton:a.a.bool,storeName:a.a.string,message:a.a.string,noPrefix:a.a.bool,skipRetryMessage:a.a.bool,hideIcon:a.a.bool}}).call(this,n(3))},61:function(e,t,n){"use strict";(function(e){var r,a;n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var i=new Set((null===(r=e)||void 0===r||null===(a=r._googlesitekitBaseData)||void 0===a?void 0:a.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;return t instanceof Set&&t.has(e)}}).call(this,n(28))},62:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(43);function a(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},620:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ContainerNameTextField}));var r=n(5),a=n.n(r),i=n(0),o=n.n(i),c=n(10),s=n.n(c),u=n(1),l=n(2),f=n(4),g=n(47),d=n(30),p=n(529),b=n(11),m=n(375);function ContainerNameTextField(t){var n=t.label,r=t.name,i=Object(f.useSelect)((function(e){var t=e(g.g).getAccountID();return e(g.g).getContainers(t)})),o=Object(f.useSelect)((function(e){return e(d.a).getValue(g.f,r)})),c=Object(f.useDispatch)(d.a).setValues,v=Object(u.useCallback)((function(e){var t=e.currentTarget;c(g.f,a()({},r,t.value))}),[r,c]),h=Object(m.b)(o,i),O=!(!o||h)&&Object(l.__)("A container with this name already exists","google-site-kit"),y=!(!o||h)&&e.createElement("span",{className:"googlesitekit-text-field-icon--error"},e.createElement(p.a,null));return e.createElement("div",{className:s()("googlesitekit-tagmanager-containername","googlesitekit-tagmanager-".concat(r))},e.createElement(b.TextField,{className:s()({"mdc-text-field--error":!o||!h}),label:n,helperText:O,trailingIcon:y,id:r,name:r,value:o,onChange:v,outlined:!0}))}ContainerNameTextField.propTypes={label:o.a.string.isRequired,name:o.a.string.isRequired}}).call(this,n(3))},621:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ContainerSelect}));var r=n(20),a=n.n(r),i=n(22),o=n.n(i),c=n(10),s=n.n(c),u=n(0),l=n.n(u),f=n(2),g=n(11),d=n(4),p=n(47),b=n(375);function ContainerSelect(t){var n=t.containers,r=t.className,i=t.value,c=o()(t,["containers","className","value"]),u=Object(d.useSelect)((function(e){return e(p.g).getAccountID()})),l=Object(d.useSelect)((function(e){return e(p.g).hasFinishedResolution("getAccounts")})),m=Object(d.useSelect)((function(e){return e(p.g).hasFinishedResolution("getContainers",[u])}));return l&&m?e.createElement(g.Select,a()({className:s()("googlesitekit-tagmanager__select-container",r),disabled:!Object(b.c)(u),value:i,enhanced:!0,outlined:!0},c),(n||[]).concat({publicId:p.b,name:Object(f.__)("Set up a new container","google-site-kit")}).map((function(t){var n=t.publicId,r=t.name,a=t.containerId;return e.createElement(g.Option,{key:n,value:n,"data-internal-id":a},n===p.b?r:Object(f.sprintf)(/* translators: 1: container name, 2: container ID */
Object(f._x)("%1$s (%2$s)","Tag Manager container name and ID","google-site-kit"),r,n))}))):e.createElement(g.ProgressBar,{small:!0})}ContainerSelect.propTypes={containers:l.a.arrayOf(l.a.object)}}).call(this,n(3))},622:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UseSnippetSwitch}));var r=n(0),a=n.n(r),i=n(1),o=n(2),c=n(4),s=n(11),u=n(47),l=n(9),f=n(18);function UseSnippetSwitch(t){var n=t.description,r=Object(c.useSelect)((function(e){return e(u.g).getUseSnippet()})),a=Object(f.a)(),g=Object(c.useDispatch)(u.g).setUseSnippet,d=Object(i.useCallback)((function(){var e=!r;g(e),Object(l.I)("".concat(a,"_tagmanager"),e?"enable_tag":"disable_tag")}),[g,r,a]);return void 0===r?null:e.createElement("div",{className:"googlesitekit-tagmanager-usesnippet"},e.createElement(s.Switch,{label:Object(o.__)("Let Site Kit place code on your site","google-site-kit"),checked:r,onClick:d,hideLabel:!1}),n)}UseSnippetSwitch.propTypes={description:a.a.node}}).call(this,n(3))},627:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(15);function a(e){var t=e;return t=(t=(t=(t=(t=(t=Object(r.unescape)(t)).trim()).replace(/^_+/,"")).normalize("NFD").replace(/[\u0300-\u036f]/g,"")).replace(/[^a-zA-Z0-9_., -]/g,"")).replace(/\s+/g," ")}},63:function(e,t,n){"use strict";n.d(t,"a",(function(){return w})),n.d(t,"b",(function(){return A})),n.d(t,"c",(function(){return C})),n.d(t,"d",(function(){return N})),n.d(t,"e",(function(){return I})),n.d(t,"g",(function(){return P})),n.d(t,"f",(function(){return T}));var r,a=n(6),i=n.n(a),o=n(27),c=n.n(o),s=n(5),u=n.n(s),l=n(12),f=n.n(l),g=n(66),d=n.n(g),p=n(15),b=n(125);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){u()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.reduce((function(e,t){return v(v({},e),t)}),{}),a=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),i=D(a);return f()(0===i.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(i.join(", "),". Check your data stores for duplicates.")),r},O=h,y=h,j=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,a=[].concat(t);return"function"!=typeof a[0]&&(r=a.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return a.reduce((function(e,n){return n(e,t)}),e)}},S=h,E=h,_=h,k=function(e){return e},w=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=_.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:r,controls:y.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:O.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:j.apply(void 0,[r].concat(c()(t.map((function(e){return e.reducer||k}))))),resolvers:S.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:E.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},A={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},C=(r={},u()(r,"GET_REGISTRY",Object(b.a)((function(e){return function(){return e}}))),u()(r,"AWAIT",(function(e){return e.payload.value})),r),D=function(e){for(var t=[],n={},r=0;r<e.length;r++){var a=e[r];n[a]=n[a]>=1?n[a]+1:1,n[a]>1&&t.push(a)}return t},N={actions:A,controls:C,reducer:k},I=function(e){return function(t){return x(e(t))}},x=d()((function(e){return Object(p.mapValues)(e,(function(e,t){return function(){var n=e.apply(void 0,arguments);return f()(void 0!==n,"".concat(t,"(...) is not resolved")),n}}))}));function P(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.negate,r=void 0!==n&&n,a=Object(b.b)((function(t){return function(n){var a=!r,i=!!r;try{for(var o=arguments.length,c=new Array(o>1?o-1:0),s=1;s<o;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,n].concat(c)),a}catch(e){return i}}})),i=Object(b.b)((function(t){return function(n){for(var r=arguments.length,a=new Array(r>1?r-1:0),i=1;i<r;i++)a[i-1]=arguments[i];e.apply(void 0,[t,n].concat(a))}}));return{safeSelector:a,dangerousSelector:i}}function T(e,t){return f()("function"==typeof e,"a validator function is required."),f()("function"==typeof t,"an action creator function is required."),f()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},65:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(1),a=Object(r.createContext)(""),i=(a.Consumer,a.Provider);t.b=a},68:function(e,t,n){"use strict";n.d(t,"a",(function(){return m})),n.d(t,"b",(function(){return v}));var r=n(5),a=n.n(r),i=n(36),o=n.n(i),c=n(125),s=n(12),u=n.n(s),l=n(103),f=n.n(l),g=n(9);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function b(e,t){if(t&&Array.isArray(t)){var n=t.map((function(e){return"object"===o()(e)?Object(g.H)(e):e}));return"".concat(e,"::").concat(f()(JSON.stringify(n)))}return e}var m={receiveError:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(e,"error is required."),u()(t,"baseName is required."),u()(n&&Array.isArray(n),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:n}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return u()(e,"baseName is required."),u()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function v(e){u()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(n,"selectorName is required."),t.getError(e,n,r)},getErrorForAction:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(n,"actionName is required."),t.getError(e,n,r)},getError:function(e,t,n){var r=e.errors;return u()(t,"baseName is required."),r[b(t,n)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var n=Object.keys(e.errors).find((function(n){return e.errors[n]===t}));return n?{baseName:n.substring(0,n.indexOf("::")),args:e.errorArgs[n]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(n,r){var a=t(e).getMetaDataForError(r);if(a){var i=a.baseName,o=a.args;if(!!t(e)[i])return{storeName:e,name:i,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:m,controls:{},reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case"RECEIVE_ERROR":var i=r.baseName,o=r.args,c=r.error,s=b(i,o);return p(p({},e),{},{errors:p(p({},e.errors||{}),{},a()({},s,c)),errorArgs:p(p({},e.errorArgs||{}),{},a()({},s,o))});case"CLEAR_ERROR":var u=r.baseName,l=r.args,f=p({},e),g=b(u,l);return f.errors=p({},e.errors||{}),f.errorArgs=p({},e.errorArgs||{}),delete f.errors[g],delete f.errorArgs[g],f;case"CLEAR_ERRORS":var d=r.baseName,m=p({},e);if(d)for(var v in m.errors=p({},e.errors||{}),m.errorArgs=p({},e.errorArgs||{}),m.errors)(v===d||v.startsWith("".concat(d,"::")))&&(delete m.errors[v],delete m.errorArgs[v]);else m.errors={},m.errorArgs={};return m;default:return e}},resolvers:{},selectors:t}}},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a})),n.d(t,"e",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return u})),n.d(t,"O",(function(){return l})),n.d(t,"K",(function(){return f})),n.d(t,"L",(function(){return g})),n.d(t,"J",(function(){return d})),n.d(t,"I",(function(){return p})),n.d(t,"N",(function(){return b})),n.d(t,"f",(function(){return m})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return O})),n.d(t,"l",(function(){return y})),n.d(t,"m",(function(){return j})),n.d(t,"n",(function(){return S})),n.d(t,"o",(function(){return E})),n.d(t,"q",(function(){return _})),n.d(t,"s",(function(){return k})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return A})),n.d(t,"w",(function(){return C})),n.d(t,"u",(function(){return D})),n.d(t,"v",(function(){return N})),n.d(t,"x",(function(){return I})),n.d(t,"y",(function(){return x})),n.d(t,"A",(function(){return P})),n.d(t,"B",(function(){return T})),n.d(t,"C",(function(){return R})),n.d(t,"D",(function(){return M})),n.d(t,"k",(function(){return L})),n.d(t,"F",(function(){return F})),n.d(t,"z",(function(){return G})),n.d(t,"G",(function(){return V})),n.d(t,"E",(function(){return U})),n.d(t,"i",(function(){return B})),n.d(t,"p",(function(){return q})),n.d(t,"Q",(function(){return H})),n.d(t,"P",(function(){return W}));var r="core/user",a="connected_url_mismatch",i="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",u="googlesitekit_setup",l="googlesitekit_view_dashboard",f="googlesitekit_manage_options",g="googlesitekit_read_shared_module_data",d="googlesitekit_manage_module_sharing_options",p="googlesitekit_delegate_module_sharing_management",b="googlesitekit_update_plugins",m="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",O="kmAnalyticsNewVisitors",y="kmAnalyticsPopularAuthors",j="kmAnalyticsPopularContent",S="kmAnalyticsPopularProducts",E="kmAnalyticsReturningVisitors",_="kmAnalyticsTopCities",k="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",A="kmAnalyticsTopCitiesDrivingPurchases",C="kmAnalyticsTopDeviceDrivingPurchases",D="kmAnalyticsTopConvertingTrafficSource",N="kmAnalyticsTopCountries",I="kmAnalyticsTopPagesDrivingLeads",x="kmAnalyticsTopRecentTrendingPages",P="kmAnalyticsTopTrafficSource",T="kmAnalyticsTopTrafficSourceDrivingAddToCart",R="kmAnalyticsTopTrafficSourceDrivingLeads",M="kmAnalyticsTopTrafficSourceDrivingPurchases",L="kmAnalyticsPagesPerVisit",F="kmAnalyticsVisitLength",G="kmAnalyticsTopReturningVisitorPages",V="kmSearchConsolePopularKeywords",U="kmAnalyticsVisitsPerVisitor",B="kmAnalyticsMostEngagingPages",q="kmAnalyticsTopCategories",H=[m,v,h,O,y,j,S,E,q,_,k,w,A,C,D,N,x,P,T,L,F,G,U,B,q],W=[].concat(H,[V])},71:function(e,t,n){"use strict";var r=n(1);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M12 1c6.075 0 11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12 5.925 1 12 1zm0 14a1.5 1.5 0 100 3 1.5 1.5 0 000-3zm-1-2h2V6h-2v7z",fill:"currentColor"});t.a=function SvgWarningNotice(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"none"},e),i)}},72:function(e,t,n){"use strict";n.d(t,"b",(function(){return g})),n.d(t,"e",(function(){return d})),n.d(t,"f",(function(){return p})),n.d(t,"g",(function(){return b})),n.d(t,"i",(function(){return m})),n.d(t,"h",(function(){return v})),n.d(t,"d",(function(){return h})),n.d(t,"c",(function(){return O})),n.d(t,"l",(function(){return y})),n.d(t,"k",(function(){return j})),n.d(t,"j",(function(){return S}));var r=n(12),a=n.n(r),i=n(15),o=n(8),c=n(9);n.d(t,"a",(function(){return c.x}));var s=n(186),u=n(280),l=n(130),f=n(357);function g(e){return e===o.a||Object(c.x)(e)}function d(e){return"string"==typeof e&&/^\d+$/.test(e)}function p(e){return e===o.s||d(e)}function b(e){return"string"==typeof e&&/^\d+$/.test(e)}function m(e){return e===o.z||b(e)}function v(e){return"string"==typeof e&&e.trim().length>0}function h(e){return"string"==typeof e&&/^G-[a-zA-Z0-9]+$/.test(e)}function O(e){return"string"==typeof e&&/^(G|GT|AW)-[a-zA-Z0-9]+$/.test(e)}function y(e){a()(Object(i.isPlainObject)(e),"options for Analytics 4 report must be an object."),a()(Object(s.a)(e),"Either date range or start/end dates must be provided for Analytics 4 report.");var t=Object(u.a)(e),n=t.metrics,r=t.dimensions,o=t.dimensionFilters,c=t.metricFilters,f=t.orderby;a()(n.length,"Requests must specify at least one metric for an Analytics 4 report."),a()(Object(l.d)(n),'metrics for an Analytics 4 report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property. Metric names must match the expression ^[a-zA-Z0-9_]+$.'),r&&a()(Object(l.b)(r),'dimensions for an Analytics 4 report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property.'),o&&a()(Object(l.a)(o),"dimensionFilters for an Analytics 4 report must be a map of dimension names as keys and dimension values as values."),c&&a()(Object(l.c)(c),"metricFilters for an Analytics 4 report must be a map of metric names as keys and filter value(s) as numeric fields, depending on the filterType."),f&&a()(Object(l.e)(f),'orderby for an Analytics 4 report must be an array of OrderBy objects where each object should have either a "metric" or "dimension" property, and an optional "desc" property.')}function j(e){a()(Object(i.isPlainObject)(e),"options for Analytics 4 pivot report must be an object."),a()(Object(s.a)(e),"Start/end dates must be provided for Analytics 4 pivot report.");var t=Object(u.a)(e),n=t.metrics,r=t.dimensions,o=t.dimensionFilters,c=t.metricFilters,g=t.pivots,d=t.orderby,p=t.limit;a()(n.length,"Requests must specify at least one metric for an Analytics 4 pivot report."),a()(Object(l.d)(n),'metrics for an Analytics 4 pivot report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property. Metric names must match the expression ^[a-zA-Z0-9_]+$.'),a()(Object(f.a)(g),'pivots for an Analytics 4 pivot report must be an array of objects. Each object must have a "fieldNames" property and a "limit".'),d&&a()(Array.isArray(d),"orderby for an Analytics 4 pivot report must be passed within a pivot."),p&&a()("number"==typeof p,"limit for an Analytics 4 pivot report must be passed within a pivot."),r&&a()(Object(l.b)(r),'dimensions for an Analytics 4 pivot report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property.'),o&&a()(Object(l.a)(o),"dimensionFilters for an Analytics 4 pivot report must be a map of dimension names as keys and dimension values as values."),c&&a()(Object(l.c)(c),"metricFilters for an Analytics 4 pivot report must be a map of metric names as keys and filter value(s) as numeric fields, depending on the filterType.")}function S(e){var t=["displayName","description","membershipDurationDays","eventTrigger","exclusionDurationMode","filterClauses"];a()(Object(i.isPlainObject)(e),"Audience must be an object."),Object.keys(e).forEach((function(e){a()(t.includes(e),'Audience object must contain only valid keys. Invalid key: "'.concat(e,'"'))})),["displayName","description","membershipDurationDays","filterClauses"].forEach((function(t){a()(e[t],'Audience object must contain required keys. Missing key: "'.concat(t,'"'))})),a()(Object(i.isArray)(e.filterClauses),"filterClauses must be an array with AudienceFilterClause objects.")}},73:function(e,t,n){"use strict";var r=n(1);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},76:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(36),a=n.n(r),i=n(88),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:i.a.sanitize(e,t)}};function c(e){var t,n="object"===a()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},762:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupErrorNotice}));var r=n(4),a=n(47),i=n(19),o=n(8),c=n(29),s=n(153),u=n(59);function SetupErrorNotice(){var t=Object(r.useSelect)((function(e){return e(i.a).isModuleAvailable(c.g)})),n=[Object(r.useSelect)((function(e){return e(i.a).getErrorForAction("activateModule",[c.g])})),Object(r.useSelect)((function(e){if(!t)return!1;var n=e(o.r).getSettings();return e(o.r).getErrorForAction("saveSettings",[n])}))].filter(Boolean);return n.length?n.map((function(t){return e.createElement(u.a,{key:null==t?void 0:t.message,error:t})})):e.createElement(s.a,{moduleSlug:"tagmanager",storeName:a.g})}}).call(this,n(3))},763:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupForm}));var r=n(6),a=n.n(r),i=n(16),o=n.n(i),c=n(14),s=n.n(c),u=n(0),l=n.n(u),f=n(1),g=n(2),d=n(4),p=n(11),b=n(47),m=n(30),v=n(7),h=n(19),O=n(33),y=n(34),j=n(37),S=n(373),E=n(762),_=n(767),k=n(29);function SetupForm(t){var n=t.finishSetup,r=Object(d.useSelect)((function(e){return e(b.g).canSubmitChanges()})),i=Object(d.useSelect)((function(e){return e(b.g).getCurrentGTMGoogleTagID()})),c=Object(d.useSelect)((function(e){return e(h.a).isModuleAvailable(k.g)})),u=Object(d.useSelect)((function(e){return e(h.a).isModuleActive(k.g)})),l=Object(d.useSelect)((function(e){return e(v.a).hasScope(b.e)})),w=Object(d.useSelect)((function(e){return e(m.a).getValue(b.f,"autoSubmit")}),[]),A=Object(d.useSelect)((function(e){return e(m.a).getValue(b.f,"submitMode")}),[]),C=Object(d.useSelect)((function(e){return e(b.g).hasExistingTag()})),D=Object(d.useSelect)((function(e){return e(b.g).isDoingSubmitChanges()||e(O.a).isNavigating()||e(m.a).getValue(b.f,"submitInProgress")})),N=Object(f.useState)(!1),I=s()(N,2),x=I[0],P=I[1],T=Object(d.useDispatch)(m.a).setValues,R=Object(d.useDispatch)(h.a).activateModule,M=Object(d.useDispatch)(b.g).submitChanges,L=Object(f.useCallback)(o()(a.a.mark((function e(){var t,r,i,c,s,l,f=arguments;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=f.length>0&&void 0!==f[0]?f[0]:{},r=t.submitMode,i=function(){var e=o()(a.a.mark((function e(t){var n,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t();case 2:if(e.t0=e.sent,e.t0){e.next=5;break}e.t0={};case 5:if(n=e.t0,!(r=n.error)){e.next=9;break}throw r;case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),T(b.f,{submitMode:r,submitInProgress:!0}),e.prev=3,e.next=6,i((function(){return M()}));case 6:if(T(b.f,{autoSubmit:!1}),r!==b.i||u){e.next=20;break}return e.next=10,R(k.g);case 10:if(c=e.sent,s=c.response,!(l=c.error)){e.next=15;break}throw l;case 15:return e.next=17,Object(j.f)("module_setup",k.g,{ttl:300});case 17:n(s.moduleReauthURL),e.next=21;break;case 20:n();case 21:e.next=26;break;case 23:e.prev=23,e.t0=e.catch(3),Object(y.f)(e.t0)&&T(b.f,{autoSubmit:!0});case 26:T(b.f,{submitInProgress:!1});case 27:case"end":return e.stop()}}),e,null,[[3,23]])}))),[n,u,R,M,T]);Object(f.useEffect)((function(){w&&l&&L({submitMode:A})}),[l,w,L,A]);var F=!(!i||!c||u),G=Object(f.useCallback)((function(e){e.preventDefault();var t=F?b.i:"";L({submitMode:t})}),[L,F]),V=Object(f.useCallback)((function(){return L()}),[L]);return e.createElement("form",{className:"googlesitekit-tagmanager-setup__form",onSubmit:G},e.createElement(E.a,null),e.createElement(S.e,{isSetup:!0}),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(S.c,null),e.createElement(S.g,null),e.createElement(S.a,null),e.createElement(S.f,null)),e.createElement(S.d,null),C&&e.createElement(_.a,null),e.createElement("div",{className:"googlesitekit-setup-module__action"},F&&e.createElement(f.Fragment,null,e.createElement(p.SpinnerButton,{disabled:!r,isSaving:x&&D,onClick:function(){return P(!0)}},Object(g.__)("Continue to Analytics setup","google-site-kit")),e.createElement(p.Button,{className:"googlesitekit-setup-module__sub-action",type:"button",onClick:V,disabled:!r,tertiary:!0},Object(g.__)("Complete setup without Analytics","google-site-kit"))),!F&&e.createElement(p.SpinnerButton,{disabled:!r||D,isSaving:D},Object(g.__)("Complete setup","google-site-kit"))))}SetupForm.propTypes={finishSetup:l.a.func}}).call(this,n(3))},764:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AMPContainerNameTextField}));var r=n(84),a=n(2),i=n(115),o=n(4),c=n(47),s=n(13),u=n(30),l=n(620);function AMPContainerNameTextField(){var t=Object(o.useSelect)((function(e){return e(c.g).getAMPContainerID()})),n=Object(o.useSelect)((function(e){return e(s.c).getSiteName()})),f=Object(o.useSelect)((function(e){return e(s.c).getReferenceSiteURL()})),g=Object(o.useSelect)((function(e){return e(u.a).getValue(c.f,"ampContainerName")}),[]),d=n;!d&&Object(i.a)(f)&&(d=new URL(f).hostname),d+=" AMP";var p=Object(o.useDispatch)(u.a).setValues;return Object(r.a)((function(){g||p(c.f,{ampContainerName:d})})),t!==c.b?null:e.createElement(l.a,{name:"ampContainerName",label:Object(a.__)("AMP Container Name","google-site-kit")})}}).call(this,n(3))},765:function(e,t,n){"use strict";t.a=[/<script[^>]*>[^>]+?www.googletagmanager.com\/gtm[^>]+?['|"](GTM-[0-9A-Z]+)['|"]/,/<script[^>]*src=['|"]https:\/\/www.googletagmanager.com\/gtm\.js\?id=(GTM-[0-9A-Z]+)['|"]/,/<script[^>]*src=['|"]https:\/\/www.googletagmanager.com\/ns.html\?id=(GTM-[0-9A-Z]+)['|"]/,/<amp-analytics [^>]*config=['|"]https:\/\/www.googletagmanager.com\/amp.json\?id=(GTM-[0-9A-Z]+)['|"]/]},766:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebContainerNameTextField}));var r=n(84),a=n(2),i=n(115),o=n(4),c=n(47),s=n(13),u=n(30),l=n(620);function WebContainerNameTextField(){var t=Object(o.useSelect)((function(e){return e(c.g).getContainerID()})),n=Object(o.useSelect)((function(e){return e(s.c).getSiteName()})),f=Object(o.useSelect)((function(e){return e(s.c).isAMP()})),g=Object(o.useSelect)((function(e){return e(s.c).getReferenceSiteURL()})),d=Object(o.useSelect)((function(e){return e(u.a).getValue(c.f,"containerName")}),[]),p=n;!p&&Object(i.a)(g)&&(p=new URL(g).hostname);var b=Object(o.useDispatch)(u.a).setValues;if(Object(r.a)((function(){d||b(c.f,{containerName:p})})),t!==c.b)return null;var m=f?Object(a.__)("Web Container Name","google-site-kit"):Object(a.__)("Container Name","google-site-kit");return e.createElement(l.a,{name:"containerName",label:m})}}).call(this,n(3))},767:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupUseSnippetSwitch}));var r=n(1),a=n(2),i=n(4),o=n(47),c=n(622);function SetupUseSnippetSwitch(){var t=Object(i.useSelect)((function(e){return e(o.g).getPrimaryContainerID()})),n=Object(i.useSelect)((function(e){return e(o.g).getExistingTag()})),s=t===n?e.createElement(r.Fragment,null,e.createElement("p",null,Object(a.sprintf)(/* translators: %s: existing tag ID */
Object(a.__)("A tag %s for the selected container already exists on the site","google-site-kit"),n)),e.createElement("p",null,Object(a.__)("Make sure you remove it if you want to place the same tag via Site Kit, otherwise they will be duplicated","google-site-kit"))):e.createElement(r.Fragment,null,e.createElement("p",null,Object(a.sprintf)(/* translators: %s: existing tag ID */
Object(a.__)("An existing tag %s was found on the page","google-site-kit"),n)),e.createElement("p",null,Object(a.__)("If you prefer to collect data using that existing tag, please select the corresponding account and property above","google-site-kit")));return e.createElement(c.a,{description:s})}}).call(this,n(3))},768:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsForm}));var r=n(0),a=n.n(r),i=n(42),o=n(2),c=n(4),s=n(373),u=n(153),l=n(47),f=n(396),g=n(19),d=n(769),p=n(35);function SettingsForm(t){var n,r=t.hasModuleAccess,a=Object(c.useSelect)((function(e){return e(g.a).getModule(f.a)})),b=(null==a||null===(n=a.owner)||void 0===n?void 0:n.login)?"<strong>".concat(a.owner.login,"</strong>"):Object(o.__)("Another admin","google-site-kit");return e.createElement("div",{className:"googlesitekit-tagmanager-settings-fields"},e.createElement(u.a,{moduleSlug:"tagmanager",storeName:l.g}),e.createElement(s.e,null),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(s.c,{hasModuleAccess:r}),e.createElement(s.g,{hasModuleAccess:r}),e.createElement(s.a,{hasModuleAccess:r}),e.createElement(s.f,null)),!1===r&&e.createElement(p.a,{className:"googlesitekit-notice--bottom-margin",type:p.a.TYPES.INFO,description:Object(i.a)(Object(o.sprintf)(/* translators: 1: module owner's name, 2: module name */
Object(o.__)("%1$s configured %2$s and you don’t have access to this %2$s account. Contact them to share access or change the %2$s account.","google-site-kit"),b,null==a?void 0:a.name),{strong:e.createElement("strong",null)})}),e.createElement(s.d,null),e.createElement("div",{className:"googlesitekit-setup-module__inputs googlesitekit-setup-module__inputs--multiline"},e.createElement(d.a,null)))}SettingsForm.propTypes={hasModuleAccess:a.a.bool},SettingsForm.defaultProps={hasModuleAccess:!0}}).call(this,n(3))},769:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsUseSnippetSwitch}));var r=n(1),a=n(2),i=n(4),o=n(622),c=n(47);function SettingsUseSnippetSwitch(){var t,n=Object(i.useSelect)((function(e){return e(c.g).getUseSnippet()})),s=Object(i.useSelect)((function(e){return e(c.g).getPrimaryContainerID()})),u=Object(i.useSelect)((function(e){return e(c.g).getExistingTag()}));return t=u?s===u?e.createElement(r.Fragment,null,e.createElement("p",null,Object(a.sprintf)(/* translators: %s: existing tag ID */
Object(a.__)("A tag %s for the selected container already exists on the site","google-site-kit"),u)),e.createElement("p",null,Object(a.__)("Consider removing the existing tag to avoid loading both tags on your site","google-site-kit"))):e.createElement(r.Fragment,null,e.createElement("p",null,Object(a.sprintf)(/* translators: %s: existing tag ID */
Object(a.__)("An existing tag %s was found on the page","google-site-kit"),u)),e.createElement("p",null,Object(a.__)("If you prefer to collect data using that existing tag, please select the corresponding account and property above","google-site-kit"))):n?e.createElement("p",null,Object(a.__)("Site Kit will add the code automatically","google-site-kit")):e.createElement("p",null,Object(a.__)("Site Kit will not add the code to your site","google-site-kit")),e.createElement(o.a,{description:t})}}).call(this,n(3))},78:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(0),a=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,a=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:a}},n)}IconWrapper.propTypes={children:a.a.node.isRequired,marginLeft:a.a.number,marginRight:a.a.number}}).call(this,n(3))},788:function(e,t,n){"use strict";var r=n(1);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",transform:"translate(.422 .422)"},r.createElement("path",{d:"M16.091 30.725l14.461-14.543 6.817 6.856L22.908 37.58z",fill:"#8ab4f8"}),r.createElement("path",{d:"M22.91 8.496L16.09 1.64 1.63 16.182a4.867 4.867 0 000 6.854L16.09 37.58l6.817-6.855L11.856 19.61z",fill:"#4285f4"}),r.createElement("ellipse",{cx:19.5,cy:34.153,fill:"#1967d2",rx:4.82,ry:4.847}),r.createElement("path",{d:"M37.37 16.182L22.91 1.639a4.801 4.801 0 00-6.817 0 4.867 4.867 0 000 6.855l14.46 14.542a4.801 4.801 0 006.817 0 4.867 4.867 0 000-6.854z",fill:"#8ab4f8"}));t.a=function SvgTagmanager(e){return r.createElement("svg",a({viewBox:"0 0 40 40"},e),i)}},789:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(1),a=n(4),i=n(47);function o(){var e=Object(a.useSelect)((function(e){return e(i.g).getExistingTag()})),t=Object(a.useSelect)((function(e){return e(i.g).getPrimaryContainerID()})),n=Object(r.useRef)(!0),o=Object(a.useDispatch)(i.g).setUseSnippet;Object(r.useEffect)((function(){if(e&&void 0!==t){if(""===t||n.current)return void(n.current=!1);o(e!==t)}}),[t,e,o])}},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"s",(function(){return i})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return s})),n.d(t,"g",(function(){return u})),n.d(t,"p",(function(){return l})),n.d(t,"j",(function(){return f})),n.d(t,"i",(function(){return g})),n.d(t,"k",(function(){return d})),n.d(t,"m",(function(){return p})),n.d(t,"n",(function(){return b})),n.d(t,"h",(function(){return m})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return O})),n.d(t,"u",(function(){return y})),n.d(t,"v",(function(){return j})),n.d(t,"f",(function(){return S})),n.d(t,"l",(function(){return E})),n.d(t,"e",(function(){return _})),n.d(t,"t",(function(){return k})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return A})),n.d(t,"b",(function(){return C}));var r="modules/analytics-4",a="account_create",i="property_create",o="webdatastream_create",c="analyticsSetup",s=10,u=1,l="https://www.googleapis.com/auth/tagmanager.readonly",f="enhanced-measurement-form",g="enhanced-measurement-enabled",d="enhanced-measurement-should-dismiss-activation-banner",p="analyticsAccountCreate",b="analyticsCustomDimensionsCreate",m="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",O="dashboardAllTrafficWidgetDimensionValue",y="dashboardAllTrafficWidgetActiveRowIndex",j="dashboardAllTrafficWidgetLoaded",S={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},E={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},_=[E.CONTACT,E.GENERATE_LEAD,E.SUBMIT_LEAD_FORM],k={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},w="audiencePermissionsSetup",A="audienceTileCustomDimensionCreate",C="audience-selection-panel-expirable-new-badge-"},85:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(115);function a(e){try{return new URL(e).pathname}catch(e){}return null}function i(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),a=e.replace(n.origin,"");if(a.length<t)return a;var i=a.length-Math.floor(t)+1;return"…"+a.substr(i)}},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="search-console"},87:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return E})),n.d(t,"d",(function(){return _})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return A})),n.d(t,"b",(function(){return C}));var r=n(14),a=n.n(r),i=n(36),o=n.n(i),c=n(5),s=n.n(c),u=n(22),l=n.n(u),f=n(15),g=n(66),d=n.n(g),p=n(2);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=y(e,t),r=n.formatUnit,a=n.formatDecimal;try{return r()}catch(e){return a()}},h=function(e){var t=O(e),n=t.hours,r=t.minutes,a=t.seconds;return a=("0"+a).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(a):"".concat(n,":").concat(r,":").concat(a)},O=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},y=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=O(e),r=n.hours,a=n.minutes,i=n.seconds;return{hours:r,minutes:a,seconds:i,formatUnit:function(){var n=t.unitDisplay,o=m(m({unitDisplay:void 0===n?"short":n},l()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(i,m(m({},o),{},{unit:"second"})):Object(p.sprintf)(/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?w(i,m(m({},o),{},{unit:"second"})):"",a?w(a,m(m({},o),{},{unit:"minute"})):"",r?w(r,m(m({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(p.sprintf)(
// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(p.__)("%ds","google-site-kit"),i);if(0===e)return t;var n=Object(p.sprintf)(
// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(p.__)("%dm","google-site-kit"),a),o=Object(p.sprintf)(
// translators: %s: number of hours with "h" as the abbreviated unit.
Object(p.__)("%dh","google-site-kit"),r);return Object(p.sprintf)(/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?t:"",a?n:"",r?o:"").trim()}}},j=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},S=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(p.sprintf)(
// translators: %s: an abbreviated number in millions.
Object(p.__)("%sM","google-site-kit"),w(j(e),e%10==0?{}:t)):1e4<=e?Object(p.sprintf)(
// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(j(e))):1e3<=e?Object(p.sprintf)(
// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(j(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function E(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(f.isPlainObject)(e)&&(t=m({},e)),t}function _(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(f.isFinite)(e)?e:Number(e),Object(f.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=E(t),r=n.style,a=void 0===r?"metric":r;return"metric"===a?S(e):"duration"===a?v(e,n):"durationISO"===a?h(e):w(e,n)}var k=d()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?C():n,i=l()(t,["locale"]);try{return new Intl.NumberFormat(r,i).format(e)}catch(t){k("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(i)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],u={},f=0,g=Object.entries(i);f<g.length;f++){var d=a()(g[f],2),p=d[0],b=d[1];c[p]&&b===c[p]||(s.includes(p)||(u[p]=b))}try{return new Intl.NumberFormat(r,u).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},A=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?C():n,a=t.style,i=void 0===a?"long":a,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:i,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var u=Object(p.__)(", ","google-site-kit");return e.join(u)},C=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(f.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},88:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a}));var r=n(160),a=n.n(r)()(e)}).call(this,n(28))},89:function(e,t,n){"use strict";(function(e){var r=n(0),a=n.n(r),i=n(10),o=n.n(i);function ChangeArrow(t){var n=t.direction,r=t.invertColor,a=t.width,i=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:a,height:i,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:a.a.string,invertColor:a.a.bool,width:a.a.number,height:a.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(3))},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return a.b})),n.d(t,"J",(function(){return a.c})),n.d(t,"F",(function(){return i.a})),n.d(t,"K",(function(){return i.b})),n.d(t,"H",(function(){return l})),n.d(t,"m",(function(){return f.a})),n.d(t,"B",(function(){return f.d})),n.d(t,"C",(function(){return f.e})),n.d(t,"y",(function(){return f.c})),n.d(t,"r",(function(){return f.b})),n.d(t,"z",(function(){return b})),n.d(t,"j",(function(){return m})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return E})),n.d(t,"c",(function(){return _})),n.d(t,"e",(function(){return k})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return A})),n.d(t,"f",(function(){return C})),n.d(t,"n",(function(){return D})),n.d(t,"w",(function(){return N})),n.d(t,"p",(function(){return I})),n.d(t,"G",(function(){return x})),n.d(t,"s",(function(){return P})),n.d(t,"v",(function(){return T})),n.d(t,"k",(function(){return R})),n.d(t,"o",(function(){return M.b})),n.d(t,"h",(function(){return M.a})),n.d(t,"t",(function(){return L.b})),n.d(t,"q",(function(){return L.a})),n.d(t,"A",(function(){return L.c})),n.d(t,"x",(function(){return F})),n.d(t,"u",(function(){return G})),n.d(t,"E",(function(){return B})),n.d(t,"D",(function(){return q.a})),n.d(t,"g",(function(){return H})),n.d(t,"L",(function(){return W})),n.d(t,"l",(function(){return K}));var r=n(15),a=n(40),i=n(76),o=n(36),c=n.n(o),s=n(103),u=n.n(s),l=function(e){return u()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var a=t[r];a&&"object"===c()(a)&&!Array.isArray(a)&&(a=e(a)),n[r]=a})),n}(e)))};n(104);var f=n(87);function g(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function d(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function p(e){return e.replace(/\n/gi,"<br>")}function b(e){for(var t=e,n=0,r=[g,d,p];n<r.length;n++){t=(0,r[n])(t)}return t}var m=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(14),O=n.n(h),y=n(12),j=n.n(y),S=n(2),E="Invalid dateString parameter, it must be a string.",_='Invalid date range, it must be a string with the format "last-x-days".',k=60,w=60*k,A=24*w,C=7*A;function D(){var e=function(e){return Object(S.sprintf)(/* translators: %s: number of days */
Object(S._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function N(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function I(e){j()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function x(e){j()(N(e),E);var t=e.split("-"),n=O()(t,3),r=n[0],a=n[1],i=n[2];return new Date(r,a-1,i)}function P(e,t){return I(R(e,t*A))}function T(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function R(e,t){j()(N(e)||Object(r.isDate)(e)&&!isNaN(e),E);var n=N(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var M=n(107),L=n(85);function F(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function G(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var V=n(27),U=n.n(V),B=function(e){return Array.isArray(e)?U()(e).sort():e},q=n(92);function H(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var W=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},K=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},92:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(12),a=n.n(r),i=function(e,t){var n=t.dateRangeLength;a()(Array.isArray(e),"report must be an array to partition."),a()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},93:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return i}));var r=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},a=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function i(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},934:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return AccountCreate}));var a=n(1),i=n(2),o=n(4),c=n(11),s=n(47),u=n(7),l=n(153),f=n(40),g=n(18);function AccountCreate(){var t=Object(g.a)(),n=Object(o.useSelect)((function(e){return e(s.g).hasFinishedResolution("getAccounts")})),d=Object(o.useSelect)((function(e){return e(u.a).hasFinishedResolution("getUser")})),p=Object(o.useSelect)((function(e){return e(s.g).getServiceURL({path:"admin/accounts/create"})})),b=Object(o.useDispatch)(s.g).resetAccounts,m=Object(a.useCallback)((function(){b()}),[b]),v=Object(a.useCallback)((function(){Object(f.b)("".concat(t,"_tagmanager"),"create_account"),e.window.open(p,"_blank")}),[p,t]);return n&&d?r.createElement("div",null,r.createElement(l.a,{moduleSlug:"tagmanager",storeName:s.g}),r.createElement("p",null,Object(i.__)("To create a new account, click the button below which will open the Google Tag Manager account creation screen in a new window.","google-site-kit")),r.createElement("p",null,Object(i.__)("Once completed, click the link below to re-fetch your accounts to continue.","google-site-kit")),r.createElement("div",{className:"googlesitekit-setup-module__action"},r.createElement(c.Button,{onClick:v},Object(i.__)("Create an account","google-site-kit")),r.createElement("div",{className:"googlesitekit-setup-module__sub-action"},r.createElement(c.Button,{onClick:m,tertiary:!0},Object(i.__)("Re-fetch My Account","google-site-kit"))))):r.createElement(c.ProgressBar,null)}}).call(this,n(28),n(3))},935:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccountSelect}));var r=n(0),a=n.n(r),i=n(1),o=n(2),c=n(11),s=n(4),u=n(47),l=n(40),f=n(18);function AccountSelect(t){var n=t.hasModuleAccess,r=Object(f.a)(),a=Object(s.useSelect)((function(e){return e(u.g).getAccounts()})),g=Object(s.useSelect)((function(e){return e(u.g).hasFinishedResolution("getAccounts")})),d=Object(s.useSelect)((function(e){return e(u.g).getAccountID()})),p=Object(s.useDispatch)(u.g).selectAccount,b=Object(i.useCallback)((function(e,t){var n=t.dataset.value;if(d!==n){var a=n===u.a?"change_account_new":"change_account";Object(l.b)("".concat(r,"_tagmanager"),a),p(n)}}),[d,p,r]);return g?!1===n?e.createElement(c.Select,{className:"googlesitekit-tagmanager__select-account",label:Object(o.__)("Account","google-site-kit"),value:d,enhanced:!0,outlined:!0,disabled:!0},e.createElement(c.Option,{value:d},d)):e.createElement(c.Select,{className:"googlesitekit-tagmanager__select-account",label:Object(o.__)("Account","google-site-kit"),value:d,onEnhancedChange:b,enhanced:!0,outlined:!0},(a||[]).concat({accountId:u.a,name:Object(o.__)("Set up a new account","google-site-kit")}).map((function(t){var n=t.accountId,r=t.name;return e.createElement(c.Option,{key:n,value:n},r)}))):e.createElement(c.ProgressBar,{small:!0})}AccountSelect.propTypes={hasModuleAccess:a.a.bool}}).call(this,n(3))},936:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AMPContainerSelect}));var r=n(0),a=n.n(r),i=n(1),o=n(2),c=n(11),s=n(4),u=n(47),l=n(13),f=n(621),g=n(40),d=n(18);function AMPContainerSelect(t){var n=t.hasModuleAccess,r=Object(d.a)(),a=Object(s.useSelect)((function(e){return e(u.g).getAccountID()})),p=Object(s.useSelect)((function(e){return e(u.g).getAMPContainerID()})),b=Object(s.useSelect)((function(e){return!1===n?null:e(u.g).getAMPContainers(a)})),m=Object(s.useSelect)((function(e){return e(l.c).isAMP()})),v=Object(s.useDispatch)(u.g),h=v.setAMPContainerID,O=v.setInternalAMPContainerID,y=Object(i.useCallback)((function(e,t){var n=t.dataset,a=n.value,i=n.internalId;if(p!==a){var o=a===u.b?"change_amp_container_new":"change_amp_container";Object(g.b)("".concat(r,"_tagmanager"),o),h(a),O(i||"")}}),[p,h,O,r]);if(!m)return null;var j=Object(o.__)("AMP Container","google-site-kit");return!1===n?e.createElement(c.Select,{className:"googlesitekit-tagmanager__select-container--amp",label:j,value:p,enhanced:!0,outlined:!0,disabled:!0},e.createElement(c.Option,{value:p},p)):e.createElement(f.a,{className:"googlesitekit-tagmanager__select-container--amp",label:j,value:p,containers:b,onEnhancedChange:y})}AMPContainerSelect.propTypes={hasModuleAccess:a.a.bool}}).call(this,n(3))},937:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ContainerNames}));var r=n(4),a=n(764),i=n(766),o=n(47);function ContainerNames(){var t=Object(r.useSelect)((function(e){return e(o.g).getContainerID()})),n=Object(r.useSelect)((function(e){return e(o.g).getAMPContainerID()}));return t!==o.b&&n!==o.b?null:e.createElement("div",{className:"googlesitekit-setup-module__inputs googlesitekit-setup-module__inputs--collapsed"},t===o.b&&e.createElement(i.a,null),n===o.b&&e.createElement(a.a,null))}}).call(this,n(3))},938:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return FormInstructions}));var r=n(0),a=n.n(r),i=n(2),o=n(4),c=n(13),s=n(19),u=n(47),l=n(29);function FormInstructions(t){var n=t.isSetup,r=Object(o.useSelect)((function(e){return e(c.c).isSecondaryAMP()})),a=Object(o.useSelect)((function(e){return e(s.a).isModuleAvailable(l.g)})),f=Object(o.useSelect)((function(e){return e(s.a).isModuleActive(l.g)})),g=Object(o.useSelect)((function(e){return e(u.g).getCurrentGTMGoogleTagID()}));return a&&!f&&g?e.createElement("p",null,Object(i.__)("Looks like you may be using Google Analytics within your Google Tag Manager configuration. Activate the Google Analytics module in Site Kit to see relevant insights in your dashboard.","google-site-kit")):r?e.createElement("p",null,n?Object(i.__)("Looks like your site is using paired AMP. Please select your Tag Manager account and relevant containers below. You can change these later in your settings.","google-site-kit"):Object(i.__)("Looks like your site is using paired AMP. Please select your Tag Manager account and relevant containers below.","google-site-kit")):e.createElement("p",null,n?Object(i.__)("Please select your Tag Manager account and container below. You can change these later in your settings.","google-site-kit"):Object(i.__)("Please select your Tag Manager account and container below","google-site-kit"))}FormInstructions.propTypes={isSetup:a.a.bool}}).call(this,n(3))},939:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TagCheckProgress}));var r=n(10),a=n.n(r),i=n(0),o=n.n(i),c=n(2),s=n(4),u=n(11),l=n(47);function TagCheckProgress(t){var n=t.className;return Object(s.useSelect)((function(e){var t=e(l.g).getAccountID(),n=e(l.g).getInternalContainerID(),r=e(l.g).getInternalAMPContainerID();return e(l.g).isResolving("getLiveContainerVersion",[t,n])||e(l.g).isResolving("getLiveContainerVersion",[t,r])}))?e.createElement("div",{className:a()(n)},e.createElement("small",null,Object(c.__)("Checking tags…","google-site-kit")),e.createElement(u.ProgressBar,{small:!0,compress:!0})):null}TagCheckProgress.propTypes={className:o.a.string},TagCheckProgress.defaultProps={className:""}}).call(this,n(3))},940:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebContainerSelect}));var r=n(0),a=n.n(r),i=n(1),o=n(2),c=n(11),s=n(4),u=n(47),l=n(13),f=n(621),g=n(40),d=n(18);function WebContainerSelect(t){var n=t.hasModuleAccess,r=Object(d.a)(),a=Object(s.useSelect)((function(e){return e(u.g).getAccountID()})),p=Object(s.useSelect)((function(e){return e(u.g).getContainerID()})),b=Object(s.useSelect)((function(e){return!1===n?null:e(u.g).getWebContainers(a)})),m=Object(s.useSelect)((function(e){return e(l.c).isAMP()})),v=Object(s.useDispatch)(u.g),h=v.setContainerID,O=v.setInternalContainerID,y=Object(i.useCallback)((function(e,t){var n=t.dataset,a=n.value,i=n.internalId;if(p!==a){var o=a===u.b?"change_container_new":"change_container";Object(g.b)("".concat(r,"_tagmanager"),o),h(a),O(i||"")}}),[p,h,O,r]),j=m?Object(o.__)("Web Container","google-site-kit"):Object(o.__)("Container","google-site-kit");return!1===n?e.createElement(c.Select,{className:"googlesitekit-tagmanager__select-container--web",label:j,value:p,enhanced:!0,outlined:!0,disabled:!0},e.createElement(c.Option,{value:p},p)):e.createElement(f.a,{className:"googlesitekit-tagmanager__select-container--web",label:j,value:p,containers:b,onEnhancedChange:y})}WebContainerSelect.propTypes={hasModuleAccess:a.a.bool}}).call(this,n(3))},941:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupMain}));var r=n(0),a=n.n(r),i=n(2),o=n(4),c=n(11),s=n(788),u=n(763),l=n(47),f=n(789),g=n(373);function SetupMain(t){var n,r=t.finishSetup,a=Object(o.useSelect)((function(e){return e(l.g).getAccounts()})),d=Object(o.useSelect)((function(e){return e(l.g).getAccountID()})),p=Object(o.useSelect)((function(e){return e(l.g).hasExistingTag()})),b=Object(o.useSelect)((function(e){return e(l.g).hasFinishedResolution("getAccounts")})),m=l.a===d;return Object(f.a)(),n=b&&void 0!==p?m||!(null==a?void 0:a.length)?e.createElement(g.b,null):e.createElement(u.a,{finishSetup:r}):e.createElement(c.ProgressBar,null),e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--tagmanager"},e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement("div",{className:"googlesitekit-setup-module__logo"},e.createElement(s.a,{width:"40",height:"40"})),e.createElement("h2",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(i._x)("Tag Manager","Service name","google-site-kit"))),e.createElement("div",{className:"googlesitekit-setup-module__step"},n))}SetupMain.propTypes={finishSetup:a.a.func},SetupMain.defaultProps={finishSetup:function(){}}}).call(this,n(3))},942:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsEdit}));var r=n(4),a=n(11),i=n(19),o=n(47),c=n(396),s=n(789),u=n(373),l=n(768);function SettingsEdit(){var t,n=Object(r.useSelect)((function(e){return e(o.g).getAccounts()}))||[],f=Object(r.useSelect)((function(e){return e(o.g).getAccountID()})),g=Object(r.useSelect)((function(e){return e(o.g).hasExistingTag()})),d=Object(r.useSelect)((function(e){return e(o.g).isDoingSubmitChanges()})),p=Object(r.useSelect)((function(e){return e(o.g).hasFinishedResolution("getAccounts")})),b=Object(r.useSelect)((function(e){return e(i.a).hasModuleOwnershipOrAccess(c.a)})),m=o.a===f;return Object(s.a)(),t=d||!p||void 0===b||void 0===g?e.createElement(a.ProgressBar,null):m||!(null==n?void 0:n.length)?e.createElement(u.b,null):e.createElement(l.a,{hasModuleAccess:b}),e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--tagmanager"},t)}}).call(this,n(3))},943:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsView}));var r=n(547),a=n.n(r),i=n(1),o=n(42),c=n(2),s=n(4),u=n(192),l=n(21),f=n(153),g=n(116),d=n(13),p=n(47),b=n(448);function m(){var e=a()(["/container/accounts/","/containers/",""]);return m=function(){return e},e}function v(){var e=a()(["/container/accounts/","/containers/",""]);return v=function(){return e},e}function SettingsView(){var t=Object(s.useSelect)((function(e){return e(p.g).getAccountID()})),n=Object(s.useSelect)((function(e){return e(p.g).getContainerID()})),r=Object(s.useSelect)((function(e){return e(p.g).getAMPContainerID()})),a=Object(s.useSelect)((function(e){return e(p.g).getUseSnippet()})),h=Object(s.useSelect)((function(e){return e(p.g).hasExistingTag()})),O=Object(s.useSelect)((function(e){return e(d.c).isAMP()})),y=Object(s.useSelect)((function(e){return e(d.c).isSecondaryAMP()})),j=Object(s.useSelect)((function(e){return e(p.g).getInternalContainerID()})),S=Object(s.useSelect)((function(e){return e(p.g).getInternalAMPContainerID()})),E=Object(s.useSelect)((function(e){return e(p.g).getServiceURL({path:Object(b.a)(v(),t,j)})})),_=Object(s.useSelect)((function(e){return e(p.g).getServiceURL({path:Object(b.a)(m(),t,S)})}));return e.createElement(i.Fragment,null,e.createElement(f.a,{moduleSlug:"tagmanager",storeName:p.g}),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(c.__)("Account","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:t}))),(!O||y)&&e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},y&&e.createElement("span",null,Object(c.__)("Web Container ID","google-site-kit")),!y&&e.createElement("span",null,Object(c.__)("Container ID","google-site-kit"))),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:n}))),E&&e.createElement("div",{className:"googlesitekit-settings-module__meta-item googlesitekit-settings-module__meta-item--data-only"},e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data googlesitekit-settings-module__meta-item-data--tiny"},e.createElement(l.a,{href:E,external:!0},Object(o.a)(Object(c.sprintf)(/* translators: %s: Appropriate container term. */
Object(c.__)("Edit <VisuallyHidden>%s </VisuallyHidden>in Tag Manager","google-site-kit"),y?Object(c.__)("web container","google-site-kit"):Object(c.__)("container","google-site-kit")),{VisuallyHidden:e.createElement(g.a,null)}))))),O&&e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},y&&e.createElement("span",null,Object(c.__)("AMP Container ID","google-site-kit")),!y&&e.createElement("span",null,Object(c.__)("Container ID","google-site-kit"))),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:r}))),_&&e.createElement("div",{className:"googlesitekit-settings-module__meta-item googlesitekit-settings-module__meta-item--data-only"},e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data googlesitekit-settings-module__meta-item-data--tiny"},e.createElement(l.a,{href:_,external:!0},Object(o.a)(Object(c.sprintf)(/* translators: %s: Appropriate container term. */
Object(c.__)("Edit <VisuallyHidden>%s </VisuallyHidden>in Tag Manager","google-site-kit"),y?Object(c.__)("AMP container","google-site-kit"):Object(c.__)("container","google-site-kit")),{VisuallyHidden:e.createElement(g.a,null)})))))),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(c.__)("Tag Manager Code Snippet","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},a&&e.createElement("span",null,Object(c.__)("Snippet is inserted","google-site-kit")),!a&&e.createElement("span",null,Object(c.__)("Snippet is not inserted","google-site-kit"))),h&&e.createElement("p",null,Object(c.__)("Placing two tags at the same time is not recommended.","google-site-kit")))))}}).call(this,n(3))},97:function(e,t,n){"use strict";var r=n(1);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"none"},e),i)}}},[[1299,1,0]]]);