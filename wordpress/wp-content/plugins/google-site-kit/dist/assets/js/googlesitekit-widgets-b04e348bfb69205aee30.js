(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[30],{101:function(e,t,n){"use strict";(function(e,i){n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return I})),n.d(t,"a",(function(){return TourTooltips}));var r=n(5),a=n.n(r),o=n(84),c=n(32),l=n(0),s=n.n(l),u=n(2),d=n(4),g=n(26),f=n(7),m=n(40),p=n(119),b=n(18);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}var h={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},j={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},I={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},y="feature_tooltip_view",E="feature_tooltip_advance",O="feature_tooltip_return",M="feature_tooltip_dismiss",k="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,r=t.tourID,l=t.gaEventCategory,s=t.callback,u="".concat(r,"-step"),N="".concat(r,"-run"),_=Object(d.useDispatch)(g.b).setValue,D=Object(d.useDispatch)(f.a).dismissTour,A=Object(d.useRegistry)(),T=Object(b.a)(),S=Object(d.useSelect)((function(e){return e(g.b).getValue(u)||0})),w=Object(d.useSelect)((function(e){return e(g.b).getValue(N)&&!1===e(f.a).isTourDismissed(r)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(r)),_(N,!0)}));var C=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return i.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,i=e.lifecycle,r=e.size,a=e.status,o=e.type,s=t+1,u="function"==typeof l?l(T):l;o===c.b.TOOLTIP&&i===c.c.TOOLTIP?Object(m.b)(u,y,s):n===c.a.CLOSE&&i===c.c.COMPLETE?Object(m.b)(u,M,s):n===c.a.NEXT&&a===c.d.FINISHED&&o===c.b.TOUR_END&&r===s&&Object(m.b)(u,k,s),i===c.c.COMPLETE&&a!==c.d.FINISHED&&(n===c.a.PREV&&Object(m.b)(u,O,s),n===c.a.NEXT&&Object(m.b)(u,E,s))}(t);var n=t.action,i=t.index,a=t.status,o=t.step,d=t.type,g=n===c.a.CLOSE,f=!g&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),p=[c.d.FINISHED,c.d.SKIPPED].includes(a),b=g&&d===c.b.STEP_AFTER,v=p||b;if(c.b.STEP_BEFORE===d){var h,j,I=o.target;"string"==typeof o.target&&(I=e.document.querySelector(o.target)),null===(h=I)||void 0===h||null===(j=h.scrollIntoView)||void 0===j||j.call(h,{block:"center"})}f?function(e,t){_(u,e+(t===c.a.PREV?-1:1))}(i,n):v&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(r)),D(r)),s&&s(t,A)},floaterProps:I,locale:j,run:w,stepIndex:S,steps:C,styles:h,tooltipComponent:p.a,continuous:!0,disableOverlayClose:!0,disableScrolling:!0,showProgress:!0})}TourTooltips.propTypes={steps:s.a.arrayOf(s.a.object).isRequired,tourID:s.a.string.isRequired,gaEventCategory:s.a.oneOfType([s.a.string,s.a.func]).isRequired,callback:s.a.func}}).call(this,n(28),n(3))},102:function(e,t,n){"use strict";(function(e){var i=n(0),r=n.n(i),a=n(10),o=n.n(a),c=n(11),l=n(21);function CTA(t){var n=t.title,i=t.headerText,r=t.headerContent,a=t.description,s=t.ctaLink,u=t.ctaLabel,d=t.ctaLinkExternal,g=t.ctaType,f=t.error,m=t.onClick,p=t["aria-label"],b=t.children;return e.createElement("div",{className:o()("googlesitekit-cta",{"googlesitekit-cta--error":f})},(i||r)&&e.createElement("div",{className:"googlesitekit-cta__header"},i&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},i),r),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),a&&"string"==typeof a&&e.createElement("p",{className:"googlesitekit-cta__description"},a),a&&"string"!=typeof a&&e.createElement("div",{className:"googlesitekit-cta__description"},a),u&&"button"===g&&e.createElement(c.Button,{"aria-label":p,href:s,onClick:m},u),u&&"link"===g&&e.createElement(l.a,{href:s,onClick:m,"aria-label":p,external:d,hideExternalIndicator:d,arrow:!0},u),b))}CTA.propTypes={title:r.a.string.isRequired,headerText:r.a.string,description:r.a.oneOfType([r.a.string,r.a.node]),ctaLink:r.a.string,ctaLinkExternal:r.a.bool,ctaLabel:r.a.string,ctaType:r.a.string,"aria-label":r.a.string,error:r.a.bool,onClick:r.a.func,children:r.a.node,headerContent:r.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(3))},1021:function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(12),o=n.n(a),c=n(4),l=n(49),s=n(1022);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var g=Object.keys(l.b).map((function(e){return"WIDGET_AREA_STYLES.".concat(e)})).join(", "),f={assignWidgetArea:function(e,t){return{payload:{slug:e,contextSlugs:"string"==typeof t?[t]:t},type:"ASSIGN_WIDGET_AREA"}},registerWidgetArea:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.priority,i=void 0===n?10:n,r=t.style,a=void 0===r?l.b.BOXES:r,c=t.title,s=t.subtitle,u=t.Icon,d=t.hasNewBadge,f=void 0!==d&&d,m=t.CTA,p=t.Footer,b=t.filterActiveWidgets;return o()(e,"slug is required."),o()(Object.values(l.b).includes(a),"settings.style must be one of: ".concat(g,".")),{payload:{slug:e,settings:{priority:i,style:a,title:c,subtitle:s,Icon:u,hasNewBadge:f,CTA:m,Footer:p,filterActiveWidgets:b}},type:"REGISTER_WIDGET_AREA"}}},m=Object(c.createReducer)((function(t,n){var i=n.type,r=n.payload;switch(i){case"ASSIGN_WIDGET_AREA":var a=r.slug;return r.contextSlugs.forEach((function(e){void 0===t.contextAssignments[e]&&(t.contextAssignments[e]=[]),t.contextAssignments[e].includes(a)||t.contextAssignments[e].push(a)})),t;case"REGISTER_WIDGET_AREA":var o=r.slug,c=r.settings;return void 0!==t.areas[o]?(e.console.warn('Could not register widget area with slug "'.concat(o,'". Widget area "').concat(o,'" is already registered.')),t):(t.areas[o]=d(d({},c),{},{slug:o}),t);default:return t}})),p={isWidgetAreaActive:Object(c.createRegistrySelector)((function(e){return function(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};o()(n,"widgetAreaSlug is required to check a widget area is active.");var r=i.modules,a=e(l.a).getWidgetArea(n),c=e(l.a).getWidgets(n,{modules:r});return a.filterActiveWidgets&&(c=a.filterActiveWidgets(e,c)),c.some((function(t){return e(l.a).isWidgetActive(t.slug)}))}})),isWidgetAreaRegistered:function(e,t){return void 0!==e.areas[t]},getWidgetAreas:function(e,t){o()(t,"contextSlug is required.");var n=e.areas,i=e.contextAssignments;return Object(s.a)(Object.values(n).filter((function(e){return i[t]&&i[t].includes(e.slug)})),"priority")},getWidgetArea:function(e,t){return o()(t,"slug is required."),e.areas[t]||null}};t.a={initialState:{areas:{},contextAssignments:{}},actions:f,controls:{},reducer:m,resolvers:{},selectors:p}}).call(this,n(28))},1022:function(e,t,n){"use strict";function i(e,t){return e.sort((function(e,n){return e[t]>n[t]?1:e[t]<n[t]?-1:0}))}n.d(t,"a",(function(){return i}))},1023:function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(12),o=n.n(a),c=n(15),l=n(624),s=n(4),u=n(251),d=n(49);function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m=Object.keys(d.c).map((function(e){return"WIDGET_WIDTHS.".concat(e)})).join(", "),p={assignWidget:function(e,t){return{payload:{slug:e,areaSlugs:"string"==typeof t?[t]:t},type:"ASSIGN_WIDGET"}},registerWidget:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.Component,i=t.priority,r=void 0===i?10:i,a=t.width,c=void 0===a?d.c.QUARTER:a,l=t.wrapWidget,s=void 0===l||l,g=t.modules,f=t.isActive,p=t.isPreloaded,b=t.hideOnBreakpoints,v=Object.values(d.c);return o()(n,"component is required to register a widget."),o()(Array.isArray(c)&&c.some(v.includes,v)||!Array.isArray(c)&&v.includes(c),"Widget width should be one of: ".concat(m,', but "').concat(c,'" was provided.')),{payload:{slug:e,settings:{Component:n,priority:r,width:c,wrapWidget:s,modules:Object(u.f)(g),isActive:f,isPreloaded:p,hideOnBreakpoints:b}},type:"REGISTER_WIDGET"}},setWidgetState:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return{payload:{slug:e,Component:t,metadata:n},type:"SET_WIDGET_STATE"}},unsetWidgetState:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return{payload:{slug:e,Component:t,metadata:n},type:"UNSET_WIDGET_STATE"}}},b=Object(s.createReducer)((function(t,n){var i=n.type,r=n.payload;switch(i){case"ASSIGN_WIDGET":var a=r.slug;return r.areaSlugs.forEach((function(e){void 0===t.areaAssignments[e]&&(t.areaAssignments[e]=[]),t.areaAssignments[e].includes(a)||t.areaAssignments[e].push(a)})),t;case"REGISTER_WIDGET":var o=r.slug,c=r.settings;return void 0!==t.widgets[o]?(e.console.warn('Could not register widget with slug "'.concat(o,'". Widget "').concat(o,'" is already registered.')),t):(t.widgets[o]=f(f({},c),{},{slug:o}),t);case"SET_WIDGET_STATE":var s=r.slug,u=r.Component,d=r.metadata;return t.widgetStates[s]={Component:u,metadata:d},t;case"UNSET_WIDGET_STATE":var g,m,p,b,v=r.slug,h=r.Component,j=r.metadata;return(null===(g=t.widgetStates)||void 0===g||null===(m=g[v])||void 0===m?void 0:m.Component)===h&&Object(l.b)(null===(p=t.widgetStates)||void 0===p||null===(b=p[v])||void 0===b?void 0:b.metadata)===j&&delete t.widgetStates[v],t;default:return t}})),v={isWidgetActive:Object(s.createRegistrySelector)((function(e){return function(t,n){return o()(n,"slug is required to check a widget is active."),!Object(u.e)(e(d.a).getWidgetState(n))}})),isWidgetRegistered:function(e,t){return void 0!==e.widgets[t]},isWidgetPreloaded:Object(s.createRegistrySelector)((function(e){return function(t,n){var i,r;return!!(null===(i=t.widgets[n])||void 0===i||null===(r=i.isPreloaded)||void 0===r?void 0:r.call(i,e))}})),getWidgets:Object(s.createRegistrySelector)((function(e){return function(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=i.modules;o()(n,"widgetAreaSlug is required.");var a=t.areaAssignments,l=Object.values(t.widgets).filter((function(e){var t;return null===(t=a[n])||void 0===t?void 0:t.includes(e.slug)})).filter((function(t){return"function"!=typeof t.isActive||(!!t.isActive(e)||"function"==typeof t.isPreloaded&&t.isPreloaded(e))}));if(r){var s=Object(u.f)(r);l=l.filter((function(e){var t;return!(null===(t=e.modules)||void 0===t?void 0:t.length)||Object(c.intersection)(e.modules,s).length===e.modules.length}))}return l.sort((function(e,t){return e.priority-t.priority}))}})),getWidget:function(e,t){return o()(t,"slug is required to get a widget."),e.widgets[t]||null},getWidgetState:function(e,t){return e.widgetStates[t]||null},getWidgetStates:function(e){return e.widgetStates}};t.a={initialState:{areaAssignments:{},widgets:{},widgetStates:{}},actions:p,controls:{},reducer:b,resolvers:{},selectors:v}}).call(this,n(28))},1025:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M7.999 1.333v13.334M1.332 8h13.333",stroke:"currentColor",strokeWidth:2,strokeLinecap:"square"});t.a=function SvgPlus(e){return i.createElement("svg",r({viewBox:"0 0 16 16",fill:"none"},e),a)}},1026:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsNewBadge}));var i=n(14),r=n.n(i),a=n(1),o=n(2),c=n(4),l=n(13),s=n(79);function KeyMetricsNewBadge(){var t=Object(c.useSelect)((function(e){return e(l.c).getKeyMetricsSetupNew()})),n=Object(c.useSelect)((function(e){return e(l.c).isKeyMetricsSetupCompleted()})),i=Object(a.useState)(n),u=r()(i,1)[0],d=Object(a.useState)(t),g=r()(d,2),f=g[0],m=g[1];return Object(a.useEffect)((function(){!u&&n&&m(!0)}),[u,n]),n||!f?null:e.createElement(s.a,{className:"googlesitekit-new-badge",label:Object(o.__)("New","google-site-kit")})}}).call(this,n(3))},1027:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricsWidgetSubtitle}));var i=n(2),r=n(1),a=n(13),o=n(4);function MetricsWidgetSubtitle(){return Object(o.useSelect)((function(e){return e(a.c).isKeyMetricsSetupCompleted()}))?e.createElement(r.Fragment,null,Object(i.__)("Track progress towards your goals with tailored metrics and important user interaction metrics","google-site-kit")):null}}).call(this,n(3))},104:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return u}));var i,r=n(52),a=n.n(r),o=n(53),c=n.n(o),l=function(t){var n=e[t];if(!n)return!1;try{var i="__storage_test__";return n.setItem(i,i),n.removeItem(i),!0}catch(e){return e instanceof DOMException&&(22===e.code||1014===e.code||"QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name)&&0!==n.length}},s=function(){function NullStorage(){a()(this,NullStorage)}return c()(NullStorage,[{key:"key",value:function(){return null}},{key:"getItem",value:function(){return null}},{key:"setItem",value:function(){}},{key:"removeItem",value:function(){}},{key:"clear",value:function(){}},{key:"length",get:function(){return 0}}]),NullStorage}(),u=function(){return i||(i=l("sessionStorage")?e.sessionStorage:l("localStorage")?e.localStorage:new s),i}}).call(this,n(28))},105:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTAButton}));var i=n(0),r=n.n(i),a=n(11),o=n(73);function CTAButton(t){var n,i=t.label,r=t.disabled,c=t.inProgress,l=t.onClick,s=t.href,u=t.external,d=t.hideExternalIndicator;return u&&!d&&(n=e.createElement(o.a,{width:14,height:14})),e.createElement(a.SpinnerButton,{className:"googlesitekit-notice__cta",disabled:r,isSaving:c,onClick:l,href:s,target:u?"_blank":"_self",trailingIcon:n},i)}CTAButton.propTypes={label:r.a.string.isRequired,disabled:r.a.bool,inProgress:r.a.bool,onClick:r.a.func,href:r.a.string,external:r.a.bool,hideExternalIndicator:r.a.bool}}).call(this,n(3))},106:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DismissButton}));var i=n(0),r=n.n(i),a=n(2),o=n(11);function DismissButton(t){var n=t.label,i=void 0===n?Object(a.__)("Got it","google-site-kit"):n,r=t.onClick,c=t.disabled;return e.createElement(o.Button,{onClick:r,disabled:c,tertiary:!0},i)}DismissButton.propTypes={label:r.a.string,onClick:r.a.func.isRequired,disabled:r.a.bool}}).call(this,n(3))},107:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var i=n(245),r=n(89),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(i.a)(e.createElement(r.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(3))},108:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var i=n(5),r=n.n(i),a=n(15),o=n(109),c=n(110);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,r=s(s({},u),t);r.referenceSiteURL&&(r.referenceSiteURL=r.referenceSiteURL.toString().replace(/\/+$/,""));var l=Object(o.a)(r,n),d=Object(c.a)(r,n,l,i),g={},f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=JSON.stringify(t);g[i]||(g[i]=Object(a.once)(d)),g[i].apply(g,t)};return{enableTracking:function(){r.trackingEnabled=!0},disableTracking:function(){r.trackingEnabled=!1},initializeSnippet:l,isTrackingEnabled:function(){return!!r.trackingEnabled},trackEvent:d,trackEventOnce:f}}}).call(this,n(28))},109:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var i=n(62),r=n(43),a=n(61);function o(t,n){var o,c=Object(i.a)(n),l=t.activeModules,s=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,g=void 0===d?[]:d,f=t.isAuthenticated,m=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(r.b,"]"))),!o){o=!0;var i=(null==g?void 0:g.length)?g.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:s,plugin_version:m||"",enabled_features:Array.from(a.a).join(","),active_modules:l.join(","),authenticated:f?"1":"0",user_properties:{user_roles:i,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(r.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(r.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(r.a)}}}}}).call(this,n(28))},11:function(e,t){e.exports=googlesitekit.components},110:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var i=n(6),r=n.n(i),a=n(5),o=n.n(a),c=n(16),l=n.n(c),s=n(62);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n,i){var a=Object(s.a)(t);return function(){var t=l()(r.a.mark((function t(o,c,l,s){var u;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:l,value:s},t.abrupt("return",new Promise((function(e){var t,n,r=setTimeout((function(){i.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),l=function(){clearTimeout(r),e()};a("event",c,d(d({},u),{},{event_callback:l})),(null===(t=i._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&l()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,i,r){return t.apply(this,arguments)}}()}},111:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"none"},e),a)}},112:function(e,t,n){"use strict";var i=n(131);n.d(t,"a",(function(){return i.a}));var r=n(132);n.d(t,"c",(function(){return r.a}));var a=n(133);n.d(t,"b",(function(){return a.a}))},114:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return i.createElement("svg",r({viewBox:"0 0 14 14",fill:"none"},e),a)}},116:function(e,t,n){"use strict";(function(e){var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(10),u=n.n(s);function VisuallyHidden(t){var n=t.className,i=t.children,a=o()(t,["className","children"]);return i?e.createElement("span",r()({},a,{className:u()("screen-reader-text",n)}),i):null}VisuallyHidden.propTypes={className:l.a.string,children:l.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(3))},117:function(e,t,n){"use strict";var i=n(459);n.d(t,"a",(function(){return i.a}));var r=n(460);n.d(t,"b",(function(){return r.a}));var a=n(461);n.d(t,"c",(function(){return a.a}));var o=n(462);n.d(t,"d",(function(){return o.a}));var c=n(463);n.d(t,"e",(function(){return c.a}));var l=n(464);n.d(t,"f",(function(){return l.a}));var s=n(277);n.d(t,"g",(function(){return s.a}));var u=n(208);n.d(t,"h",(function(){return u.a}));n(377)},119:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var i=n(20),r=n.n(i),a=n(162),o=n.n(a),c=n(10),l=n.n(c),s=n(0),u=n.n(s),d=n(2),g=n(11),f=n(164),m=n(114);function TourTooltip(t){var n=t.backProps,i=t.closeProps,c=t.index,s=t.primaryProps,u=t.size,p=t.step,b=t.tooltipProps,v=u>1?Object(f.a)(u):[],h=function(e){return l()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",r()({className:l()("googlesitekit-tour-tooltip",p.className)},b),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},p.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},p.content)),e.createElement(a.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},v.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:h(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(g.Button,r()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),p.cta,s.title&&e.createElement(g.Button,r()({className:"googlesitekit-tooltip-button",text:!0},s),s.title))),e.createElement(g.Button,{className:"googlesitekit-tooltip-close",icon:e.createElement(m.a,{width:"14",height:"14"}),onClick:i.onClick,"aria-label":Object(d.__)("Close","google-site-kit"),text:!0,hideTooltipTitle:!0})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(3))},120:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(15),r=function(e){return Object(i.isFinite)(e)?e:0}},126:function(e,t,n){"use strict";var i=n(340),r=n(328);n.d(t,"b",(function(){return r.a}));var a=n(329);n.d(t,"c",(function(){return a.a}));var o=n(330);n.d(t,"d",(function(){return o.a}));var c=n(331);n.d(t,"a",(function(){return c.a})),t.e=i.a},127:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return b}));var i=n(5),r=n.n(i),a=n(20),o=n.n(a),c=n(14),l=n.n(c),s=n(22),u=n.n(s),d=n(222),g=n(0),f=n.n(g),m=n(1);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function b(t){function WithIntersectionObserverComponent(n){var i=n.onInView,r=u()(n,["onInView"]),a=Object(m.useRef)(),c=Object(d.a)(a,{root:null,threshold:.45}),s=Object(m.useState)(!1),g=l()(s,2),f=g[0],p=g[1],b=!!(null==c?void 0:c.isIntersecting)&&!!(null==c?void 0:c.intersectionRatio);return Object(m.useEffect)((function(){c&&b&&!f&&(i(),p(!0))}),[f,b,c,i]),e.createElement(t,o()({ref:a},r))}return WithIntersectionObserverComponent.displayName="WithIntersectionObserverComponent",(t.displayName||t.name)&&(WithIntersectionObserverComponent.displayName+="(".concat(t.displayName||t.name,")")),WithIntersectionObserverComponent.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({onInView:f.a.func.isRequired},t.propTypes),WithIntersectionObserverComponent}}).call(this,n(3))},1275:function(e,t,n){"use strict";n.r(t),function(e){n.d(t,"WIDGET_WIDTHS",(function(){return c})),n.d(t,"WIDGET_AREA_STYLES",(function(){return l})),n.d(t,"registerWidgetArea",(function(){return s})),n.d(t,"registerWidget",(function(){return u})),n.d(t,"assignWidgetArea",(function(){return d})),n.d(t,"assignWidget",(function(){return g})),n.d(t,"isWidgetAreaRegistered",(function(){return f})),n.d(t,"isWidgetRegistered",(function(){return m}));var i=n(4),r=n.n(i),a=n(810);Object(a.b)(r.a);var o=Object(a.a)(r.a);Object(a.c)(o),void 0===e.googlesitekit&&(e.googlesitekit={}),e.googlesitekit.widgets=o,t.default=o;var c=o.WIDGET_WIDTHS,l=o.WIDGET_AREA_STYLES,s=o.registerWidgetArea,u=o.registerWidget,d=o.assignWidgetArea,g=o.assignWidget,f=o.isWidgetAreaRegistered,m=o.isWidgetRegistered}.call(this,n(28))},128:function(e,t,n){"use strict";n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return d}));var i,r=n(5),a=n.n(r),o=n(49),c=n(183),l=n(173),s=(i={},a()(i,o.c.QUARTER,3),a()(i,o.c.HALF,6),a()(i,o.c.FULL,12),i),u="googlesitekit-hidden",d=[c.a,l.a]},129:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(225),r=n(15),a=n(1);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object(i.b)((function(){return r.debounce.apply(void 0,t)}),t);return Object(a.useEffect)((function(){return function(){return o.cancel()}}),[o]),o}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var i="core/site",r="primary",a="secondary"},131:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var i=n(20),r=n.n(i),a=n(5),o=n.n(a),c=n(22),l=n.n(c),s=n(0),u=n.n(s),d=n(10),g=n.n(d);function Cell(t){var n,i=t.className,a=t.alignTop,c=t.alignMiddle,s=t.alignBottom,u=t.alignRight,d=t.alignLeft,f=t.smAlignRight,m=t.mdAlignRight,p=t.lgAlignRight,b=t.smSize,v=t.smStart,h=t.smOrder,j=t.mdSize,I=t.mdStart,y=t.mdOrder,E=t.lgSize,O=t.lgStart,M=t.lgOrder,k=t.size,N=t.children,_=l()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",r()({},_,{className:g()(i,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":s,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":m,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(k),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--span-".concat(E,"-desktop"),12>=E&&E>0),o()(n,"mdc-layout-grid__cell--start-".concat(O,"-desktop"),12>=O&&O>0),o()(n,"mdc-layout-grid__cell--order-".concat(M,"-desktop"),12>=M&&M>0),o()(n,"mdc-layout-grid__cell--span-".concat(j,"-tablet"),8>=j&&j>0),o()(n,"mdc-layout-grid__cell--start-".concat(I,"-tablet"),8>=I&&I>0),o()(n,"mdc-layout-grid__cell--order-".concat(y,"-tablet"),8>=y&&y>0),o()(n,"mdc-layout-grid__cell--span-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),N)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(3))},132:function(e,t,n){"use strict";(function(e){var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(10),u=n.n(s),d=n(1),g=Object(d.forwardRef)((function(t,n){var i=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",r()({ref:n,className:u()("mdc-layout-grid__inner",i)},c),a)}));g.displayName="Row",g.propTypes={className:l.a.string,children:l.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(3))},133:function(e,t,n){"use strict";(function(e){var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(10),u=n.n(s),d=n(1),g=Object(d.forwardRef)((function(t,n){var i=t.alignLeft,a=t.fill,c=t.className,l=t.children,s=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",r()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":i,"mdc-layout-grid--collapsed":s,"mdc-layout-grid--fill":a})},d,{ref:n}),l)}));g.displayName="Grid",g.propTypes={alignLeft:l.a.bool,fill:l.a.bool,className:l.a.string,collapsed:l.a.bool,children:l.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(3))},134:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{fill:"none",fillRule:"evenodd"},i.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),i.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return i.createElement("svg",r({viewBox:"0 0 13 13"},e),a)}},135:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{fill:"none",fillRule:"evenodd"},i.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),i.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return i.createElement("svg",r({viewBox:"0 0 13 13"},e),a)}},136:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},137:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InfoTooltip}));var i=n(10),r=n.n(i),a=n(0),o=n.n(a),c=n(11),l=n(339);function InfoTooltip(t){var n=t.onOpen,i=t.title,a=t.tooltipClassName;return i?e.createElement(c.Tooltip,{className:"googlesitekit-info-tooltip",tooltipClassName:r()("googlesitekit-info-tooltip__content",a),title:i,placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,onOpen:n,interactive:!0},e.createElement("span",null,e.createElement(l.a,{width:"16",height:"16"}))):null}InfoTooltip.propTypes={onOpen:o.a.func,title:o.a.oneOfType([o.a.string,o.a.element]),tooltipClassName:o.a.string}}).call(this,n(3))},139:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Icon}));var i,r=n(5),a=n.n(r),o=n(0),c=n.n(o),l=n(111),s=n(71),u=n(97),d=n(38),g=(i={},a()(i,d.a.NEW,u.a),a()(i,d.a.SUCCESS,l.a),a()(i,d.a.INFO,s.a),a()(i,d.a.WARNING,s.a),a()(i,d.a.ERROR,s.a),i);function Icon(t){var n=t.type,i=g[n]||s.a;return e.createElement(i,{width:24,height:24})}Icon.propTypes={type:c.a.oneOf(Object.values(d.a))}}).call(this,n(3))},140:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Title}));var i=n(10),r=n.n(i),a=n(0),o=n.n(a);function Title(t){var n=t.className,i=t.children;return e.createElement("p",{className:r()("googlesitekit-notice__title",n)},i)}Title.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var i=n(10),r=n.n(i),a=n(0),o=n.n(a);function Description(t){var n=t.className,i=t.children;return e.createElement("p",{className:r()("googlesitekit-notice__description",n)},i)}Description.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},142:function(e,t,n){"use strict";(function(e){var i=n(0),r=n.n(i),a=n(10),o=n.n(a),c=n(212),l=n(42),s=n(2),u=n(11),d=n(98);function ModalDialog(t){var n=t.className,i=void 0===n?"":n,r=t.dialogActive,a=void 0!==r&&r,g=t.handleCancel,f=void 0===g?null:g,m=t.onOpen,p=void 0===m?null:m,b=t.onClose,v=void 0===b?null:b,h=t.title,j=void 0===h?null:h,I=t.provides,y=t.handleConfirm,E=t.subtitle,O=t.confirmButton,M=void 0===O?null:O,k=t.dependentModules,N=t.danger,_=void 0!==N&&N,D=t.inProgress,A=void 0!==D&&D,T=t.small,S=void 0!==T&&T,w=t.medium,C=void 0!==w&&w,z=t.buttonLink,R=void 0===z?null:z,x=Object(c.a)(ModalDialog),P="googlesitekit-dialog-description-".concat(x),L=!(!I||!I.length);return e.createElement(u.Dialog,{open:a,onOpen:p,onClose:v,"aria-describedby":L?P:void 0,tabIndex:"-1",className:o()(i,{"googlesitekit-dialog-sm":S,"googlesitekit-dialog-md":C})},e.createElement(u.DialogTitle,null,_&&e.createElement(d.a,{width:28,height:28}),j),E?e.createElement("p",{className:"mdc-dialog__lead"},E):[],e.createElement(u.DialogContent,null,L&&e.createElement("section",{id:P,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},I.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),k&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(l.a)(Object(s.sprintf)(/* translators: %s is replaced with the dependent modules. */
Object(s.__)("<strong>Note:</strong> %s","google-site-kit"),k),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",onClick:f,disabled:A,tertiary:!0},Object(s.__)("Cancel","google-site-kit")),R?e.createElement(u.Button,{href:R,onClick:y,target:"_blank",danger:_},M):e.createElement(u.SpinnerButton,{onClick:y,danger:_,disabled:A,isSaving:A},M||Object(s.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:r.a.string,dialogActive:r.a.bool,handleDialog:r.a.func,handleConfirm:r.a.func.isRequired,onOpen:r.a.func,onClose:r.a.func,title:r.a.string,confirmButton:r.a.string,danger:r.a.bool,small:r.a.bool,medium:r.a.bool,buttonLink:r.a.string},t.a=ModalDialog}).call(this,n(3))},143:function(e,t,n){"use strict";(function(e){var i=n(0),r=n.n(i),a=n(10),o=n.n(a),c=n(42),l=n(2),s=n(21),u=n(39);function SourceLink(t){var n=t.name,i=t.href,r=t.className,a=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",r)},Object(c.a)(Object(l.sprintf)(/* translators: %s: source link */
Object(l.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(s.a,{key:"link",href:i,external:a})}))}SourceLink.propTypes={name:r.a.string,href:r.a.string,className:r.a.string,external:r.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(3))},144:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportErrorActions}));var i=n(5),r=n.n(i),a=n(0),o=n.n(a),c=n(1),l=n(42),s=n(2),u=n(4),d=n(11),g=n(13),f=n(19),m=n(34),p=n(39),b=n(21);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportErrorActions(t){var n=t.moduleSlug,i=t.error,r=t.GetHelpLink,a=t.hideGetHelpLink,o=t.buttonVariant,v=t.onRetry,j=t.onRequestAccess,I=t.getHelpClassName,y=t.RequestAccessButton,E=t.RetryButton,O=Object(p.a)(),M=Object(u.useSelect)((function(e){return e(f.a).getModuleStoreName(n)})),k=Object(u.useSelect)((function(e){var t;return"function"==typeof(null===(t=e(M))||void 0===t?void 0:t.getServiceEntityAccessURL)?e(M).getServiceEntityAccessURL():null})),N=Array.isArray(i)?i:[i],_=Object(u.useSelect)((function(e){return N.map((function(t){var n,i=null===(n=e(M))||void 0===n?void 0:n.getSelectorDataForError(t);return h(h({},t),{},{selectorData:i})}))})),D=null==_?void 0:_.filter((function(e){return Object(m.d)(e,e.selectorData)&&"getReport"===e.selectorData.name})),A=!!D.length,T=Object(u.useSelect)((function(e){var t=h({},A?D[0]:N[0]);return Object(m.e)(t)&&(t.code="".concat(n,"_insufficient_permissions")),e(g.c).getErrorTroubleshootingLinkURL(t)})),S=Object(u.useDispatch)(),w=N.some((function(e){return Object(m.e)(e)})),C=Object(c.useCallback)((function(){D.forEach((function(e){var t=e.selectorData;S(t.storeName).invalidateResolution(t.name,t.args)})),null==v||v()}),[S,D,v]),z=k&&w&&!O;return e.createElement("div",{className:"googlesitekit-report-error-actions"},z&&("function"==typeof y?e.createElement(y,{requestAccessURL:k}):e.createElement(d.Button,{onClick:j,href:k,target:"_blank",danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Request access","google-site-kit"))),A&&e.createElement(c.Fragment,null,"function"==typeof E?e.createElement(E,{handleRetry:C}):e.createElement(d.Button,{onClick:C,danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Retry","google-site-kit")),!a&&e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(l.a)(Object(s.__)("Retry didn’t work? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(b.a,{href:T,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))}))),!A&&!a&&e.createElement("div",{className:I},"function"==typeof r?e.createElement(r,{linkURL:T}):e.createElement(b.a,{href:T,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))))}ReportErrorActions.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired,GetHelpLink:o.a.elementType,hideGetHelpLink:o.a.bool,buttonVariant:o.a.string,onRetry:o.a.func,onRequestAccess:o.a.func,getHelpClassName:o.a.string,RequestAccessButton:o.a.elementType,RetryButton:o.a.elementType}}).call(this,n(3))},145:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LearnMoreLink}));var i=n(0),r=n.n(i),a=n(2),o=n(21);function LearnMoreLink(t){var n=t.href,i=t.className,r=t.label,c=void 0===r?Object(a.__)("Learn more","google-site-kit"):r,l=t.onClick,s=void 0===l?function(){}:l;return n?e.createElement(o.a,{href:n,className:i,onClick:s,external:!0},c):null}LearnMoreLink.propTypes={href:r.a.string.isRequired,className:r.a.string,label:r.a.string,onClick:r.a.func}}).call(this,n(3))},146:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTAButton}));var i=n(0),r=n.n(i),a=n(11);function CTAButton(t){var n=t.label,i=t.disabled,r=t.inProgress,o=t.onClick,c=t.href;return n&&(o||c)?e.createElement(a.SpinnerButton,{className:"googlesitekit-banner__cta",disabled:i||r,isSaving:r,onClick:o,href:c},n):null}CTAButton.propTypes={label:r.a.string.isRequired,disabled:r.a.bool,inProgress:r.a.bool,onClick:r.a.func,href:r.a.string}}).call(this,n(3))},147:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DismissButton}));var i=n(0),r=n.n(i),a=n(2),o=n(11);function DismissButton(t){var n=t.className,i=t.label,r=void 0===i?Object(a.__)("Maybe later","google-site-kit"):i,c=t.tertiary,l=void 0===c||c,s=t.onClick,u=t.disabled;return s?e.createElement(o.Button,{className:n,onClick:s,disabled:u,tertiary:l},r):null}DismissButton.propTypes={className:r.a.string,label:r.a.string,tertiary:r.a.bool,onClick:r.a.func,disabled:r.a.bool}}).call(this,n(3))},148:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return DismissButton})),n.d(t,"a",(function(){return c}));var i=n(0),r=n.n(i),a=n(2),o=n(11);function DismissButton(t){var n=t.label,i=void 0===n?Object(a.__)("Maybe later","google-site-kit"):n,r=t.onClick,c=t.disabled;return e.createElement(o.Button,{onClick:r,disabled:c,tertiary:!0},i)}var c={label:r.a.string,onClick:r.a.func,disabled:r.a.bool};DismissButton.propTypes=c}).call(this,n(3))},150:function(e,t,n){"use strict";var i=n(1),r=Object(i.createContext)(!1);t.a=r},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var i=n(5),r=n.n(i),a=n(2),o=n(7),c=n(13),l=n(8);function s(e,t,n){return e(l.r).hasConversionReportingEvents(this.requiredConversionEventName)||e(o.a).isKeyMetricActive(n)}var u,d=n(25);function g(e,t){return!t||!(!t||!e(l.r).getAdSenseLinked())}function f(e,t){return!t||e(l.r).hasCustomDimensions(this.requiredCustomDimensions)}var m=(u={},r()(u,o.f,{title:Object(a.__)("Top earning pages","google-site-kit"),description:Object(a.__)("Pages that generated the most AdSense revenue","google-site-kit"),infoTooltip:Object(a.__)("Pages that generated the most AdSense revenue","google-site-kit"),displayInSelectionPanel:g,displayInList:g,metadata:{group:d.b.SLUG}}),r()(u,o.y,{title:Object(a.__)("Top recent trending pages","google-site-kit"),description:Object(a.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),infoTooltip:Object(a.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_date"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:d.b.SLUG}}),r()(u,o.l,{title:Object(a.__)("Most popular authors by pageviews","google-site-kit"),description:Object(a.__)("Authors whose posts got the most visits","google-site-kit"),infoTooltip:Object(a.__)("Authors whose posts got the most visits","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_author"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:d.b.SLUG}}),r()(u,o.p,{title:Object(a.__)("Top categories by pageviews","google-site-kit"),description:Object(a.__)("Categories that your site visitors viewed the most","google-site-kit"),infoTooltip:Object(a.__)("Categories that your site visitors viewed the most","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_categories"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:d.b.SLUG}}),r()(u,o.m,{title:Object(a.__)("Most popular content by pageviews","google-site-kit"),description:Object(a.__)("Pages that brought in the most visitors","google-site-kit"),infoTooltip:Object(a.__)("Pages your visitors read the most","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.n,{title:Object(a.__)("Most popular products by pageviews","google-site-kit"),description:Object(a.__)("Products that brought in the most visitors","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_type"],displayInSelectionPanel:function(e){return e(o.a).isKeyMetricActive(o.n)||e(c.c).getProductPostType()},displayInWidgetArea:f,metadata:{group:d.f.SLUG}}),r()(u,o.k,{title:Object(a.__)("Pages per visit","google-site-kit"),description:Object(a.__)("Number of pages visitors viewed per session on average","google-site-kit"),infoTooltip:Object(a.__)("Number of pages visitors viewed per session on average","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.F,{title:Object(a.__)("Visit length","google-site-kit"),description:Object(a.__)("Average duration of engaged visits","google-site-kit"),infoTooltip:Object(a.__)("Average duration of engaged visits","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.E,{title:Object(a.__)("Visits per visitor","google-site-kit"),description:Object(a.__)("Average number of sessions per site visitor","google-site-kit"),infoTooltip:Object(a.__)("Average number of sessions per site visitor","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.i,{title:Object(a.__)("Most engaging pages","google-site-kit"),description:Object(a.__)("Pages with the highest engagement rate","google-site-kit"),infoTooltip:Object(a.__)("Pages with the highest engagement rate","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.h,{title:Object(a.__)("Least engaging pages","google-site-kit"),description:Object(a.__)("Pages with the highest percentage of visitors that left without engagement with your site","google-site-kit"),infoTooltip:Object(a.__)("Percentage of visitors that left without engagement with your site","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.z,{title:Object(a.__)("Top pages by returning visitors","google-site-kit"),description:Object(a.__)("Pages that attracted the most returning visitors","google-site-kit"),infoTooltip:Object(a.__)("Pages that attracted the most returning visitors","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.j,{title:Object(a.__)("New visitors","google-site-kit"),description:Object(a.__)("How many new visitors you got and how the overall audience changed","google-site-kit"),infoTooltip:Object(a.__)("Portion of visitors who visited your site for the first time in this timeframe","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.o,{title:Object(a.__)("Returning visitors","google-site-kit"),description:Object(a.__)("Portion of people who visited your site more than once","google-site-kit"),infoTooltip:Object(a.__)("Portion of your site’s visitors that returned at least once in this timeframe","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.A,{title:Object(a.__)("Top traffic source","google-site-kit"),description:Object(a.__)("Channel which brought in the most visitors to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most visitors to your site","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.B,{title:Object(a.__)("Top traffic source driving add to cart","google-site-kit"),description:Object(a.__)("Channel which brought in the most add to cart events to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most add to cart events to your site","google-site-kit"),requiredConversionEventName:[l.l.ADD_TO_CART],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.C,{title:Object(a.__)("Top traffic source driving leads","google-site-kit"),description:Object(a.__)("Channel which brought in the most leads to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most leads to your site","google-site-kit"),requiredConversionEventName:[l.l.SUBMIT_LEAD_FORM,l.l.CONTACT,l.l.GENERATE_LEAD],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.e.SLUG}}),r()(u,o.D,{title:Object(a.__)("Top traffic source driving purchases","google-site-kit"),description:Object(a.__)("Channel which brought in the most purchases to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most purchases to your site","google-site-kit"),requiredConversionEventName:[l.l.PURCHASE],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.g,{title:Object(a.__)("Most engaged traffic source","google-site-kit"),description:Object(a.__)("Visitors coming via this channel spent the most time on your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most visitors who had a meaningful engagement with your site","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.u,{title:Object(a.__)("Top converting traffic source","google-site-kit"),description:Object(a.__)("Channel which brought in the most visits that resulted in key events","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in visitors who generated the most key events","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.q,{title:Object(a.__)("Top cities driving traffic","google-site-kit"),description:Object(a.__)("Which cities you get the most visitors from","google-site-kit"),infoTooltip:Object(a.__)("The cities where most of your visitors came from","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.s,{title:Object(a.__)("Top cities driving leads","google-site-kit"),description:Object(a.__)("Cities driving the most contact form submissions","google-site-kit"),infoTooltip:Object(a.__)("Cities driving the most contact form submissions","google-site-kit"),requiredConversionEventName:[l.l.SUBMIT_LEAD_FORM,l.l.CONTACT,l.l.GENERATE_LEAD],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.e.SLUG}}),r()(u,o.r,{title:Object(a.__)("Top cities driving add to cart","google-site-kit"),description:Object(a.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),infoTooltip:Object(a.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),requiredConversionEventName:[l.l.ADD_TO_CART],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.t,{title:Object(a.__)("Top cities driving purchases","google-site-kit"),description:Object(a.__)("Cities driving the most purchases","google-site-kit"),infoTooltip:Object(a.__)("Cities driving the most purchases","google-site-kit"),requiredConversionEventName:[l.l.PURCHASE],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.w,{title:Object(a.__)("Top device driving purchases","google-site-kit"),description:Object(a.__)("Top device driving the most purchases","google-site-kit"),infoTooltip:Object(a.__)("Top device driving the most purchases","google-site-kit"),requiredConversionEventName:[l.l.PURCHASE],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.v,{title:Object(a.__)("Top countries driving traffic","google-site-kit"),description:Object(a.__)("Which countries you get the most visitors from","google-site-kit"),infoTooltip:Object(a.__)("The countries where most of your visitors came from","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.G,{title:Object(a.__)("Top performing keywords","google-site-kit"),description:Object(a.__)("What people searched for before they came to your site","google-site-kit"),infoTooltip:Object(a.__)("The top search queries for your site by highest clickthrough rate","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.x,{title:Object(a.__)("Top pages driving leads","google-site-kit"),description:Object(a.__)("Pages on which forms are most frequently submitted","google-site-kit"),requiredConversionEventName:[l.l.SUBMIT_LEAD_FORM,l.l.CONTACT,l.l.GENERATE_LEAD],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.e.SLUG}}),u)},156:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(1),r=n(4),a=n(49);function o(e,t,n){var o=Object(r.useDispatch)(a.a),c=o.setWidgetState,l=o.unsetWidgetState;Object(i.useEffect)((function(){return c(e,t,n),function(){l(e,t,n)}}),[e,t,n,c,l])}},164:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},166:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NoticeNotification}));var i=n(20),r=n.n(i),a=n(6),o=n.n(a),c=n(5),l=n.n(c),s=n(16),u=n.n(s),d=n(22),g=n.n(d),f=n(35),m=n(69),p=n(4),b=n(41),v=n(17),h=n(0),j=n.n(h);function I(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?I(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):I(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function NoticeNotification(t){var n=t.notificationID,i=t.children,a=t.dismissButton,c=t.ctaButton,l=t.gaTrackingEventArgs,s=g()(t,["notificationID","children","dismissButton","ctaButton","gaTrackingEventArgs"]),d=Object(m.a)(n),h=Object(p.useDispatch)(b.a).dismissNotification,j=function(){var e=u()(o.a.mark((function e(t){var i;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==a||null===(i=a.onClick)||void 0===i?void 0:i.call(a,t);case 2:d.dismiss(null==l?void 0:l.label,null==l?void 0:l.value),h(n,y({},(null==a?void 0:a.dismissOptions)||{}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),I=function(){var e=u()(o.a.mark((function e(t){var n;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==c||null===(n=c.onClick)||void 0===n?void 0:n.call(c,t);case 2:d.confirm(null==l?void 0:l.label,null==l?void 0:l.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(v.e,null,e.createElement(v.k,null,e.createElement(v.a,{size:12,alignMiddle:!0},e.createElement(f.a,r()({dismissButton:y(y({},a),{},{onClick:j}),ctaButton:y(y({},c),{},{onClick:I})},s),i))))}NoticeNotification.propTypes={notificationID:j.a.string.isRequired,children:j.a.node,dismissButton:j.a.oneOfType([j.a.bool,j.a.object]),ctaButton:j.a.object,gaTrackingEventArgs:j.a.object}}).call(this,n(3))},168:function(e,t,n){"use strict";(function(e,i){var r=n(5),a=n.n(r),o=n(20),c=n.n(o),l=n(22),s=n.n(l),u=n(0),d=n.n(u),g=n(1),f=n(259),m=n(11),p=n(142);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}var v=null;function RefocusableModalDialog(e){var t=e.dialogActive,n=void 0!==t&&t,r=e.refocusQuerySelector,a=void 0===r?null:r,o=s()(e,["dialogActive","refocusQuerySelector"]),l=Object(g.useCallback)((function(){setTimeout((function(){var e=a?document.querySelector(a):v;e&&document.body.contains(e)&&e.focus(),a||(v=null)}))}),[a]),u=Object(f.a)(n);return Object(g.useEffect)((function(){return!0===u&&!1===n&&l(),function(){l()}}),[u,n,l]),i.createElement(p.a,c()({dialogActive:n},o))}!function(){if(void 0!==e&&e.document&&!e._googlesitekitModalFocusTrackerInitialized){var t=function(e){var t=e.target.closest("button, a, input");t&&!t.classList.contains("mdc-dialog__cancel-button")&&(v=t)};e.document.addEventListener("mousedown",t),e.document.addEventListener("keydown",(function(e){"Enter"!==e.key&&" "!==e.key||t(e)})),e._googlesitekitModalFocusTrackerInitialized=!0}}(),RefocusableModalDialog.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({dialogActive:d.a.bool,refocusQuerySelector:d.a.string},m.Dialog.propTypes),t.a=RefocusableModalDialog}).call(this,n(28),n(3))},17:function(e,t,n){"use strict";var i=n(261);n.d(t,"i",(function(){return i.a}));var r=n(333);n.d(t,"f",(function(){return r.a}));var a=n(334);n.d(t,"h",(function(){return a.a}));var o=n(335);n.d(t,"j",(function(){return o.a}));var c=n(332);n.d(t,"g",(function(){return c.a}));var l=n(96),s=n.n(l);n.d(t,"b",(function(){return s.a})),n.d(t,"c",(function(){return l.DialogContent})),n.d(t,"d",(function(){return l.DialogFooter}));var u=n(112);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},170:function(e,t,n){"use strict";var i=n(150),r=(i.a.Consumer,i.a.Provider);t.a=r},173:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RecoverableModules}));var i=n(0),r=n.n(i),a=n(2),o=n(4),c=n(19),l=n(102);function RecoverableModules(t){var n=t.moduleSlugs,i=Object(o.useSelect)((function(e){var t=e(c.a).getModules();if(void 0!==t)return n.map((function(e){return t[e].name}))}));if(void 0===i)return null;var r=1===i.length?Object(a.sprintf)(/* translators: %s: Module name */
Object(a.__)("%s data was previously shared by an admin who no longer has access. Please contact another admin to restore it.","google-site-kit"),i[0]):Object(a.sprintf)(/* translators: %s: List of module names */
Object(a.__)("The data for the following modules was previously shared by an admin who no longer has access: %s. Please contact another admin to restore it.","google-site-kit"),i.join(Object(a._x)(", ","Recoverable modules","google-site-kit")));return e.createElement(l.a,{title:Object(a.__)("Data Unavailable","google-site-kit"),description:r})}RecoverableModules.propTypes={moduleSlugs:r.a.arrayOf(r.a.string).isRequired}}).call(this,n(3))},175:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var i=n(2),r="audience-segmentation-info-notice-ui",a="audience-segmentation-info-notice",o=[{slug:"new-visitors",content:Object(i.__)("The higher the portion of new visitors you have, the more your audience is growing. Looking at what content brings them to your site may give you insights on how to reach even more people.","google-site-kit")},{slug:"compare-metrics",content:Object(i.__)("Select up to three visitor groups to display on the dashboard and easily compare metrics between them.","google-site-kit")},{slug:"custom-audiences",content:Object(i.__)("Configure your own custom audiences in Analytics to gain deeper insights into visitor behavior, for example consider creating an “Existing customers” or “Subscribers” segment, depending on what goals you have for your site.","google-site-kit")},{slug:"purchasers",content:Object(i.__)("Select the Purchasers visitor group to gain insights into which visitors bring the most revenue to your site.","google-site-kit")},{slug:"returning-visitors",content:Object(i.__)("The more returning visitors your site has, the stronger and more loyal an audience you’re building. Check which content brings people back to your site - it might help you create a strategy to build a community.","google-site-kit")},{slug:"compare-new-returning",content:Object(i.__)("Compare the ratio of “new” to “returning” visitors – this can give you insights on whether you have more people stopping by as a one-off, or more loyal visitors.","google-site-kit")},{slug:"compare-cities",content:Object(i.__)("Check the cities which bring you more new vs more returning visitors – there might be new audiences you could engage with in locations you hadn’t thought about.","google-site-kit")}]},177:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(2),r=n(86),a=n(29);function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},o=n.slug,c=void 0===o?"":o,l=n.name,s=void 0===l?"":l,u=n.owner,d=void 0===u?{}:u;if(!c||!s)return e;var g="",f="";return a.g===c?e.match(/account/i)?g=Object(i.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?g=Object(i.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(g=Object(i.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):r.a===c&&(g=Object(i.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),g||(g=Object(i.sprintf)(/* translators: %s: module name */
Object(i.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),s)),d&&d.login&&(f=Object(i.sprintf)(/* translators: %s: owner name */
Object(i.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),d.login)),f||(f=Object(i.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(g," ").concat(f)}},179:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportError}));var i=n(5),r=n.n(i),a=n(0),o=n.n(a),c=n(15),l=n(1),s=n(2),u=n(4),d=n(19),g=n(34),f=n(177),m=n(88),p=n(102),b=n(144),v=n(39),h=n(59);function j(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function I(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?j(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportError(t){var n,i=t.moduleSlug,r=t.error,a=Object(v.a)(),o=Object(u.useSelect)((function(e){return e(d.a).getModule(i)})),j=Array.isArray(r)?r:[r],y=function(e){return Object(g.e)(e)?a?(n=Object(s.sprintf)(/* translators: %s: module name */
Object(s.__)("Access lost to %s","google-site-kit"),null==o?void 0:o.name),Object(s.sprintf)(/* translators: %s: module name */
Object(s.__)("The administrator sharing this module with you has lost access to the %s service, so you won’t be able to see stats from it on the Site Kit dashboard. You can contact them or another administrator to restore access.","google-site-kit"),null==o?void 0:o.name)):(n=Object(s.sprintf)(/* translators: %s: module name */
Object(s.__)("Insufficient permissions in %s","google-site-kit"),null==o?void 0:o.name),Object(f.a)(e.message,o)):Object(g.b)(e)},E=Object(c.uniqWith)(j.map((function(e){var t;return I(I({},e),{},{message:y(e),reconnectURL:null===(t=e.data)||void 0===t?void 0:t.reconnectURL})})),(function(e,t){return e.message===t.message&&e.reconnectURL===t.reconnectURL})),O=j.some((function(e){return Object(g.e)(e)}));O||1!==E.length?!O&&E.length>1&&(n=Object(s.sprintf)(/* translators: %s: module name */
Object(s.__)("Data errors in %s","google-site-kit"),null==o?void 0:o.name)):n=Object(s.sprintf)(/* translators: %s: module name */
Object(s.__)("Data error in %s","google-site-kit"),null==o?void 0:o.name);var M=e.createElement(l.Fragment,null,E.map((function(t){var n;return(null==t||null===(n=t.data)||void 0===n?void 0:n.reconnectURL)?e.createElement(h.a,{key:t.message,error:t,message:t.message}):e.createElement("p",{key:t.message},m.a.sanitize(t.message,{ALLOWED_TAGS:[]}))})));return e.createElement(p.a,{title:n,description:M,error:!0},e.createElement(b.a,{moduleSlug:i,error:r}))}ReportError.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired}}).call(this,n(3))},18:function(e,t,n){"use strict";var i=n(1),r=n(65);t.a=function(){return Object(i.useContext)(r.b)}},183:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportZero}));var i=n(0),r=n.n(i),a=n(2),o=n(4),c=n(19),l=n(102);function ReportZero(t){var n=t.moduleSlug,i=Object(o.useSelect)((function(e){return e(c.a).getModule(n)}));return e.createElement(l.a,{title:Object(a.sprintf)(/* translators: %s: Module name */
Object(a.__)("%s Gathering Data","google-site-kit"),null==i?void 0:i.name),description:Object(a.sprintf)(/* translators: %s: Module name */
Object(a.__)("%s data is not yet available, please check back later","google-site-kit"),null==i?void 0:i.name)})}ReportZero.propTypes={moduleSlug:r.a.string.isRequired}}).call(this,n(3))},184:function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(10),o=n.n(a),c=n(0),l=n.n(c),s=n(1),u=n(23),d=n(228),g=n(229),f=n(230),m=n(145),p=n(146),b=n(147),v=n(231),h=n(35),j=Object(s.forwardRef)((function(t,n){var i=t.className,a=t.title,c=t.description,l=t.additionalDescription,s=t.errorText,j=t.helpText,I=t.learnMoreLink,y=t.dismissButton,E=t.ctaButton,O=t.svg,M=t.footer,k=Object(u.e)(),N=k===u.b||k===u.c,_=null;N&&(null==O?void 0:O.mobile)?_=O.mobile:!N&&(null==O?void 0:O.desktop)&&(_=O.desktop);var D=(null==O?void 0:O.verticalPosition)?O.verticalPosition:"center";return e.createElement("div",{ref:n,className:o()("googlesitekit-banner",i)},e.createElement("div",{className:"googlesitekit-banner__content"},e.createElement(d.a,null,a),e.createElement(g.a,null,c," ",(null==I?void 0:I.href)&&e.createElement(m.a,I),l&&e.createElement("div",{className:"googlesitekit-banner__additional-description"},l)),j&&e.createElement(f.a,null,j),s&&e.createElement(h.a,{type:"error",description:s}),e.createElement("div",{className:"googlesitekit-notice__action"},E&&e.createElement(p.a,E),(null==y?void 0:y.onClick)&&e.createElement(b.a,y))),_&&e.createElement("div",{className:o()("googlesitekit-banner__svg-wrapper",r()({},"googlesitekit-banner__svg-wrapper--".concat(D),D)),style:{backgroundImage:"url(".concat(_,")")}}),M&&e.createElement(v.a,null,M))}));j.propTypes={title:l.a.string,description:l.a.oneOfType([l.a.string,l.a.node]),additionalDescription:l.a.oneOfType([l.a.string,l.a.node]),errorText:l.a.string,helpText:l.a.string,learnMoreLink:l.a.shape(m.a.propTypes),dismissButton:l.a.shape(b.a.propTypes),ctaButton:l.a.shape(p.a.propTypes),svg:l.a.shape({desktop:l.a.elementType,mobile:l.a.elementType,verticalPosition:l.a.oneOf(["top","center","bottom"])}),footer:l.a.node},t.a=j}).call(this,n(3))},185:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileLoading}));var i=n(48);function AudienceTileLoading(){return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-loading"},e.createElement(i.a,{width:"100%",height:"20px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}))}}).call(this,n(3))},187:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var i=n(6),r=n.n(i),a=n(16),o=n.n(a),c=n(1),l=n(4),s=n(13),u=n(7),d=n(19),g=n(33),f=n(37),m=n(40),p=n(18);function b(e){var t=Object(p.a)(),n=Object(l.useSelect)((function(t){return t(d.a).getModule(e)})),i=Object(l.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),a=Object(l.useDispatch)(d.a).activateModule,b=Object(l.useDispatch)(g.a).navigateTo,v=Object(l.useDispatch)(s.c).setInternalServerError,h=Object(c.useCallback)(o()(r.a.mark((function n(){var i,o,c;return r.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,a(e);case 2:if(i=n.sent,o=i.error,c=i.response,o){n.next=13;break}return n.next=8,Object(m.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(f.f)("module_setup",e,{ttl:300});case 10:b(c.moduleReauthURL),n.next=14;break;case 13:v({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[a,e,b,v,t]);return(null==n?void 0:n.name)&&i?h:null}},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r}));var i="core/modules",r="insufficient_module_dependencies"},193:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeBadge}));var i=n(0),r=n.n(i),a=n(10),o=n.n(a),c=n(9);function ChangeBadge(t){var n=t.previousValue,i=t.currentValue,r=t.isAbsolute?i-n:Object(c.g)(n,i),a=r<0,l=0===r;return null===r?null:e.createElement("div",{className:o()("googlesitekit-change-badge",{"googlesitekit-change-badge--negative":a,"googlesitekit-change-badge--zero":l})},Object(c.B)(r,{style:"percent",signDisplay:"exceptZero",maximumFractionDigits:1}))}ChangeBadge.propTypes={isAbsolute:r.a.bool,previousValue:r.a.number.isRequired,currentValue:r.a.number.isRequired}}).call(this,n(3))},2:function(e,t){e.exports=googlesitekit.i18n},203:function(e,t,n){"use strict";var i=n(247);n.d(t,"b",(function(){return i.a}));var r=n(260);n.d(t,"a",(function(){return r.a}))},208:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileWrapper}));var i=n(10),r=n.n(i),a=n(15),o=n(0),c=n.n(o),l=n(1),s=n(2),u=n(154),d=n(475),g=n(476),f=n(317),m=n(407),p=n(144),b=n(34),v=n(9),h=n(18);function MetricTileWrapper(t){var n,i,o,c=t.className,j=t.children,I=t.error,y=t.loading,E=t.moduleSlug,O=t.Widget,M=t.widgetSlug,k=t.title,N=void 0===k?null===(n=u.a[M])||void 0===n?void 0:n.title:k,_=t.infoTooltip,D=void 0===_?(null===(i=u.a[M])||void 0===i?void 0:i.infoTooltip)||(null===(o=u.a[M])||void 0===o?void 0:o.description):_,A=Object(h.a)(),T=!!I&&Object(a.castArray)(I).some(b.e),S=Object(l.useCallback)((function(){Object(v.I)("".concat(A,"_kmw"),"data_loading_error_retry")}),[A]);return Object(l.useEffect)((function(){I&&Object(v.I)("".concat(A,"_kmw"),"data_loading_error")}),[A,I]),I?e.createElement(f.a,{title:T?Object(s.__)("Insufficient permissions","google-site-kit"):Object(s.__)("Data loading failed","google-site-kit"),headerText:N,infoTooltip:D},e.createElement(p.a,{moduleSlug:E,error:I,onRetry:S,GetHelpLink:T?d.a:void 0,getHelpClassName:"googlesitekit-error-retry-text"})):e.createElement(O,{noPadding:!0},e.createElement("div",{className:r()("googlesitekit-km-widget-tile",c)},e.createElement(m.a,{title:N,infoTooltip:D,loading:y}),e.createElement("div",{className:"googlesitekit-km-widget-tile__body"},y&&e.createElement(g.a,null),!y&&j)))}MetricTileWrapper.propTypes={Widget:c.a.elementType.isRequired,loading:c.a.bool,title:c.a.string,infoTooltip:c.a.oneOfType([c.a.string,c.a.element]),moduleSlug:c.a.string.isRequired}}).call(this,n(3))},209:function(e,t,n){"use strict";(function(e){var i=n(15),r=n(0),a=n.n(r),o=n(1),c=n(60),l=n(4),s=n(34),u=n(26),d=n(175),g=n(477),f=n(127),m=n(9),p=n(18),b=n(29),v=Object(f.a)(g.a);function AudienceSegmentationErrorWidget(t){var n=t.Widget,r=t.errors,a=t.onRetry,c=t.showRetryButton,g=Object(p.a)(),f=Object(l.useDispatch)(u.b).setValue,b=r?Object(i.castArray)(r):[],h=b.some(s.e);return Object(o.useEffect)((function(){f(d.b,!0)}),[f]),e.createElement(v,{Widget:n,errors:b,onRetry:function(){Object(m.I)("".concat(g,"_audiences-all-tiles"),"data_loading_error_retry").finally((function(){f(d.b,!1),null==a||a()}))},onRequestAccess:function(){Object(m.I)("".concat(g,"_audiences-all-tiles"),"insufficient_permissions_error_request_access")},showRetryButton:c,onInView:function(){var e=h?"insufficient_permissions_error":"data_loading_error";Object(m.I)("".concat(g,"_audiences-all-tiles"),e)}})}AudienceSegmentationErrorWidget.propTypes={Widget:a.a.elementType.isRequired,errors:a.a.oneOfType([a.a.object,a.a.arrayOf(a.a.object)]).isRequired,onRetry:a.a.func,showRetryButton:a.a.bool},t.a=Object(c.a)({moduleName:b.g})(AudienceSegmentationErrorWidget)}).call(this,n(3))},21:function(e,t,n){"use strict";(function(e){var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(10),l=n.n(c),s=n(0),u=n.n(s),d=n(158),g=n(1),f=n(2),m=n(134),p=n(135),b=n(136),v=n(73),h=n(78),j=Object(g.forwardRef)((function(t,n){var i,a=t["aria-label"],c=t.secondary,s=void 0!==c&&c,u=t.arrow,g=void 0!==u&&u,j=t.back,I=void 0!==j&&j,y=t.caps,E=void 0!==y&&y,O=t.children,M=t.className,k=void 0===M?"":M,N=t.danger,_=void 0!==N&&N,D=t.disabled,A=void 0!==D&&D,T=t.external,S=void 0!==T&&T,w=t.hideExternalIndicator,C=void 0!==w&&w,z=t.href,R=void 0===z?"":z,x=t.inverse,P=void 0!==x&&x,L=t.noFlex,B=void 0!==L&&L,G=t.onClick,W=t.small,Z=void 0!==W&&W,V=t.standalone,U=void 0!==V&&V,F=t.linkButton,H=void 0!==F&&F,Y=t.to,Q=t.leadingIcon,X=t.trailingIcon,J=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),K=R||Y||!G?Y?"ROUTER_LINK":S?"EXTERNAL_LINK":"LINK":A?"BUTTON_DISABLED":"BUTTON",q="BUTTON"===K||"BUTTON_DISABLED"===K?"button":"ROUTER_LINK"===K?d.b:"a",$=("EXTERNAL_LINK"===K&&(i=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===K&&(i=Object(f._x)("(disabled)","screen reader text","google-site-kit")),i?a?"".concat(a," ").concat(i):"string"==typeof O?"".concat(O," ").concat(i):void 0:a),ee=Q,te=X;return I&&(ee=e.createElement(b.a,{width:14,height:14})),S&&!C&&(te=e.createElement(v.a,{width:14,height:14})),g&&!P&&(te=e.createElement(m.a,{width:14,height:14})),g&&P&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(q,r()({"aria-label":$,className:l()("googlesitekit-cta-link",k,{"googlesitekit-cta-link--secondary":s,"googlesitekit-cta-link--inverse":P,"googlesitekit-cta-link--small":Z,"googlesitekit-cta-link--caps":E,"googlesitekit-cta-link--danger":_,"googlesitekit-cta-link--disabled":A,"googlesitekit-cta-link--standalone":U,"googlesitekit-cta-link--link-button":H,"googlesitekit-cta-link--no-flex":!!B}),disabled:A,href:"LINK"!==K&&"EXTERNAL_LINK"!==K||A?void 0:R,onClick:G,rel:"EXTERNAL_LINK"===K?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===K?"_blank":void 0,to:Y},J),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},O),!!te&&e.createElement(h.a,{marginLeft:5},te))}));j.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=j}).call(this,n(3))},220:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M2 5.309l1.474 2.14c.69 1.001 1.946 1.001 2.636 0L10 1.8",stroke:"currentColor",strokeWidth:1.6,strokeLinecap:"square"});t.a=function SvgCheck2(e){return i.createElement("svg",r({viewBox:"0 0 12 9",fill:"none"},e),a)}},228:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Title}));var i=n(10),r=n.n(i),a=n(0),o=n.n(a);function Title(t){var n=t.className,i=t.children;return e.createElement("p",{className:r()("googlesitekit-banner__title",n)},i)}Title.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},229:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var i=n(10),r=n.n(i),a=n(0),o=n.n(a);function Description(t){var n=t.className,i=t.children;return e.createElement("div",{className:r()("googlesitekit-banner__description",n)},i)}Description.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},23:function(e,t,n){"use strict";n.d(t,"d",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return l}));var i=n(90),r="xlarge",a="desktop",o="tablet",c="small";function l(){var e=Object(i.a)();return e>1280?r:e>960?a:e>600?o:c}},230:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HelpText}));var i=n(10),r=n.n(i),a=n(0),o=n.n(a);function HelpText(t){var n=t.className,i=t.children;return e.createElement("p",{className:r()("googlesitekit-banner__help-text",n)},i)}HelpText.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},231:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Footer}));var i=n(10),r=n.n(i),a=n(0),o=n.n(a);function Footer(t){var n=t.className,i=t.children;return e.createElement("div",{className:r()("googlesitekit-banner__footer",n)},i)}Footer.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},239:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupCTA}));var i=n(20),r=n.n(i),a=n(6),o=n.n(a),c=n(5),l=n.n(c),s=n(16),u=n.n(s),d=n(22),g=n.n(d),f=n(10),m=n.n(f),p=n(0),b=n.n(p),v=n(1),h=n(4),j=n(41),I=n(69),y=n(184),E=n(145),O=n(146),M=n(147),k=n(17),N=n(11);function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function D(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SetupCTA(t){var n=t.notificationID,i=t.title,a=t.description,c=t.errorText,l=t.helpText,s=t.learnMoreLink,d=t.dismissButton,f=t.ctaButton,p=t.svg,b=t.footer,E=t.dismissOptions,O=t.gaTrackingEventArgs,M=t.waitingProgress,_=g()(t,["notificationID","title","description","errorText","helpText","learnMoreLink","dismissButton","ctaButton","svg","footer","dismissOptions","gaTrackingEventArgs","waitingProgress"]),A=Object(I.a)(n,null==O?void 0:O.category),T=Object(h.useDispatch)(j.a).dismissNotification,S=function(){var e=u()(o.a.mark((function e(t){var i;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==d||null===(i=d.onClick)||void 0===i?void 0:i.call(d,t);case 2:A.dismiss(null==O?void 0:O.label,null==O?void 0:O.value),T(n,D({},E));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),w=function(){var e=u()(o.a.mark((function e(t){var n;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return A.confirm(null==O?void 0:O.label,null==O?void 0:O.value),e.next=3,null==f||null===(n=f.onClick)||void 0===n?void 0:n.call(f,t);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),C=function(){var e=u()(o.a.mark((function e(t){var n;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return A.clickLearnMore(null==O?void 0:O.label,null==O?void 0:O.value),e.next=3,null==s||null===(n=s.onClick)||void 0===n?void 0:n.call(s,t);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(v.Fragment,null,!!M&&e.createElement(N.ProgressBar,r()({className:"googlesitekit-banner__progress-bar"},M)),e.createElement("div",{className:m()("googlesitekit-widget-context",{"googlesitekit-widget-context--with-progress-bar":!!M})},e.createElement(k.e,null,e.createElement(k.k,null,e.createElement(k.a,{size:12},e.createElement(y.a,r()({className:"googlesitekit-banner--setup-cta",title:i,description:a,errorText:c,helpText:l,learnMoreLink:s&&D(D({},s),{},{onClick:C}),dismissButton:d&&D(D({},d),{},{onClick:S}),ctaButton:f&&D(D({},f),{},{onClick:w}),svg:p,footer:b},_)))))))}SetupCTA.propTypes={notificationID:b.a.string,title:b.a.string,description:b.a.oneOfType([b.a.string,b.a.node]),errorText:b.a.string,helpText:b.a.string,learnMoreLink:b.a.shape(E.a.propTypes),dismissButton:b.a.shape(M.a.propTypes),ctaButton:b.a.shape(O.a.propTypes),svg:b.a.shape({desktop:b.a.elementType,mobile:b.a.elementType,verticalPosition:b.a.oneOf(["top","center","bottom"])}),footer:b.a.node,dismissOptions:b.a.object,gaTrackingEventArgs:b.a.shape({category:b.a.string,label:b.a.string,value:b.a.number}),waitingProgress:b.a.shape(N.ProgressBar.propTypes)}}).call(this,n(3))},24:function(e,t,n){"use strict";n.d(t,"n",(function(){return i})),n.d(t,"l",(function(){return r})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return l})),n.d(t,"s",(function(){return s})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"u",(function(){return m})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return b})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return j})),n.d(t,"a",(function(){return I})),n.d(t,"d",(function(){return y})),n.d(t,"c",(function(){return E})),n.d(t,"f",(function(){return O})),n.d(t,"g",(function(){return M}));var i="mainDashboard",r="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",l="activation",s="splash",u="adminBar",d="adminBarViewOnly",g="settings",f="adBlockingRecovery",m="wpDashboard",p="wpDashboardViewOnly",b="moduleSetup",v="metricSelection",h="key-metrics",j="traffic",I="content",y="speed",E="monetization",O=[i,r,a,o,c,s,g,b,v],M=[a,o,d,p]},247:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n(5),r=n.n(i),a=n(1),o=n(4),c=n(26);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function s(e){var t=Object(o.useDispatch)(c.b).setValue;return Object(a.useCallback)((function(){t("admin-menu-tooltip",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({isTooltipVisible:!0},e))}),[t,e])}},248:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return m})),n.d(t,"c",(function(){return b})),n.d(t,"b",(function(){return v}));var i=n(20),r=n.n(i),a=n(66),o=n.n(a),c=n(273),l=n(336),s=n(337),u=n(249),d=n(274),g=n(338),f=n(1),m=o()((function(e){return{widgetSlug:e,Widget:p(e)(c.a),WidgetRecoverableModules:p(e)(d.a),WidgetReportZero:p(e)(l.a),WidgetReportError:p(e)(s.a),WidgetNull:p(e)(u.a)}}));function p(t){return function(n){var i=Object(f.forwardRef)((function(i,a){return e.createElement(n,r()({},i,{ref:a,widgetSlug:t}))}));return i.displayName="WithWidgetSlug",(n.displayName||n.name)&&(i.displayName+="(".concat(n.displayName||n.name,")")),i}}var b=function(t){var n=m(t);return function(t){function DecoratedComponent(i){return e.createElement(t,r()({},i,n))}return DecoratedComponent.displayName="WithWidgetComponentProps",(t.displayName||t.name)&&(DecoratedComponent.displayName+="(".concat(t.displayName||t.name,")")),DecoratedComponent}},v=function(t){return function(n){function DecoratedComponent(i){return e.createElement(n,r()({},i,{WPDashboardReportError:p(t)(g.a)}))}return DecoratedComponent.displayName="WithWPDashboardWidgetComponentProps",(n.displayName||n.name)&&(DecoratedComponent.displayName+="(".concat(n.displayName||n.name,")")),DecoratedComponent}}}).call(this,n(3))},249:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetNull}));var i=n(5),r=n.n(i),a=n(0),o=n.n(a),c=n(156),l=n(99);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}var u={};function WidgetNull(t){var n=t.widgetSlug;return Object(c.a)(n,l.a,u),e.createElement(l.a,null)}WidgetNull.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:o.a.string.isRequired},l.a.propTypes)}).call(this,n(3))},25:function(e,t,n){"use strict";n.d(t,"l",(function(){return r})),n.d(t,"k",(function(){return a})),n.d(t,"j",(function(){return o})),n.d(t,"i",(function(){return c})),n.d(t,"a",(function(){return l})),n.d(t,"o",(function(){return s})),n.d(t,"n",(function(){return u})),n.d(t,"m",(function(){return d})),n.d(t,"c",(function(){return g})),n.d(t,"g",(function(){return f})),n.d(t,"h",(function(){return m})),n.d(t,"d",(function(){return p})),n.d(t,"e",(function(){return b})),n.d(t,"f",(function(){return v})),n.d(t,"b",(function(){return h}));var i=n(2),r="key-metrics-setup-cta-widget",a="googlesitekit-key-metrics-selection-panel-opened",o="key-metrics-selection-form",c="key-metrics-selected",l="key-metrics-effective-selection",s="key-metrics-unstaged-selection",u=2,d=8,g={SLUG:"current-selection",LABEL:Object(i.__)("Current selection","google-site-kit")},f={SLUG:"suggested",LABEL:Object(i.__)("Suggested","google-site-kit")},m={SLUG:"visitors",LABEL:Object(i.__)("Visitors","google-site-kit")},p={SLUG:"driving-traffic",LABEL:Object(i.__)("Driving traffic","google-site-kit")},b={SLUG:"generating-leads",LABEL:Object(i.__)("Generating leads","google-site-kit")},v={SLUG:"selling-products",LABEL:Object(i.__)("Selling products","google-site-kit")},h={SLUG:"content-performance",LABEL:Object(i.__)("Content performance","google-site-kit")}},251:function(e,t,n){"use strict";n.d(t,"d",(function(){return g})),n.d(t,"b",(function(){return m})),n.d(t,"c",(function(){return p.a})),n.d(t,"g",(function(){return p.c})),n.d(t,"a",(function(){return c.a})),n.d(t,"f",(function(){return b})),n.d(t,"e",(function(){return s}));var i=n(14),r=n.n(i),a=n(27),o=n.n(a),c=n(128),l=n(99);function s(e){return!!e&&e.Component===l.a}function u(e,t){if(9!==t)return[e,t];for(var n=(e=o()(e)).length-1;0!==t&&n>=0;)3===e[n]?(t-=3,e[n]=4):6===e[n]&&(t-=6,e[n]=8),n--;return[e,t]}function d(e,t){return(Array.isArray(t.width)?t.width:[t.width]).map((function(t){return{counter:e+c.c[t],width:t}}))}function g(e,t){var n=[],i=[];if(!(null==e?void 0:e.length))return{columnWidths:n,rowIndexes:i};var a=0,o=0,l=function(e,t){return e.counter-t.counter},g=function(e,t){var n=e.counter;return t.counter-n},f=function(e){return e.counter<=12};if(e.forEach((function(m,p){if(s(t[m.slug]))return n.push(0),void i.push(o);var b=d(a,m),v=function(e,t,n){for(;++e<t.length;)if(!s(n[t[e].slug]))return t[e];return null}(p,e,t);null!==v&&0!==d(b.sort(l)[0].counter,v).filter(f).length||b.some(f)&&(b=(b=b.sort(g)).filter(f));var h=b[0].width;if(i.push(o),(a+=c.c[h])>12){if(a-=c.c[h],i[p]++,9===a){var j=u(n,a),I=r()(j,2);n=I[0],a=I[1]}a=c.c[h],o++}else 12===a&&(a=0,o++);n.push(c.c[h])})),9===a){var m=u(n,a),p=r()(m,2);n=p[0],a=p[1]}return{columnWidths:n,rowIndexes:i}}var f=n(15);function m(e,t,n){var i=n.columnWidths,r=n.rowIndexes,a=[],l=o()(i);if(!(null==e?void 0:e.length))return{gridColumnWidths:l,overrideComponents:a};var s=null,u=-1,d=[];if(function(e,t){for(var n={},i=0;i<e.length;i++){var r,a=e[i],o=null==t?void 0:t[a.slug],l=null==o?void 0:o.Component,s=null==o||null===(r=o.metadata)||void 0===r?void 0:r.moduleSlug,u=c.b.includes(l);if(!l||!s||!u)return!1;if(n[s]){if(n[s]!==l)return!1}else n[s]=l}return!(Object.keys(n).length>1)}(e,t)){var g=Array.from({length:e.length-1}).fill(0);return{overrideComponents:[t[e[0].slug]],gridColumnWidths:[12].concat(o()(g))}}return e.forEach((function(n,o){var c,g,m,p,b;if(a.push(null),s=t[n.slug],u=r[o],s)if(g=s,m=t[null===(c=e[o+1])||void 0===c?void 0:c.slug],p=u,b=r[o+1],p===b&&Object(f.isEqual)(g,m))d.push(i[o]),l[o]=0;else if(d.length>0){d.push(i[o]);var v=d.reduce((function(e,t){return e+t}),0);a[o]=s,l[o]=v,d=[]}})),{gridColumnWidths:l,overrideComponents:a}}var p=n(248);function b(e){return(Array.isArray(e)?e:[e]).filter((function(e){return"string"==typeof e&&e.length>0}))}},252:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));n(8);function i(e){var t;return 400===(null==e?void 0:e.code)&&(null==e||null===(t=e.message)||void 0===t?void 0:t.includes("is not a valid dimension"))}},253:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceErrorModal}));var i=n(0),r=n.n(i),a=n(42),o=n(2),c=n(4),l=n(21),s=n(168),u=n(75),d=n(13),g=n(8),f=n(34),m=n(9);function AudienceErrorModal(t){var n=t.apiErrors,i=t.hasOAuthError,r=t.inProgress,p=t.title,b=t.description,v=t.trackEventCategory,h=t.onCancel,j=void 0===h?function(){}:h,I=t.onRetry,y=void 0===I?function(){}:I,E=Array.isArray(n)?n:[n],O=Object(c.useSelect)((function(e){return e(d.c).getErrorTroubleshootingLinkURL({code:"analytics-4_insufficient_permissions"})})),M=Object(c.useSelect)((function(e){return e(g.r).getServiceEntityAccessURL()})),k=Object(c.useSelect)((function(e){return e(d.c).getErrorTroubleshootingLinkURL({code:"access_denied"})}));if(!E.length&&!i)return null;var N,_,D,A,T=E.some((function(e){return Object(f.e)(e)}));return i?(N=Object(o.__)("Analytics update failed","google-site-kit"),_=Object(a.a)(Object(o.__)("Setup was interrupted because you did not grant the necessary permissions. <HelpLink />","google-site-kit"),{HelpLink:e.createElement(l.a,{href:k,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))}),D=Object(o.__)("Retry","google-site-kit")):T?(N=Object(o.__)("Insufficient permissions","google-site-kit"),_=Object(a.a)(Object(o.__)("You’ll need to contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(l.a,{href:O,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))}),D=Object(o.__)("Request access","google-site-kit"),A=M):(N=p||Object(o.__)("Failed to set up visitor groups","google-site-kit"),_=b||Object(o.__)("Oops! Something went wrong. Retry enabling groups.","google-site-kit"),D=Object(o.__)("Retry","google-site-kit")),e.createElement(u.a,null,e.createElement(s.a,{buttonLink:A,title:N,subtitle:_,handleConfirm:function(){var e;e=i?"auth_error_retry":T?"insufficient_permissions_error_request_access":"setup_error_retry",Object(m.I)(v,e).finally((function(){T||y()}))},confirmButton:D,handleCancel:function(){var e;e=i?"auth_error_cancel":T?"insufficient_permissions_error_cancel":"setup_error_cancel",Object(m.I)(v,e).finally(j)},onOpen:function(){var e;e=i?"auth_error":T?"insufficient_permissions_error":"setup_error",Object(m.I)(v,e)},onClose:j,inProgress:r,refocusQuerySelector:"#audience_segmentation_setup_cta-notification .googlesitekit-banner__cta",danger:!0,dialogActive:!0}))}AudienceErrorModal.propTypes={apiErrors:r.a.oneOfType([r.a.arrayOf(r.a.object),r.a.object,r.a.array]),hasOAuthError:r.a.bool,inProgress:r.a.bool,title:r.a.string,description:r.a.string,trackEventCategory:r.a.string,onCancel:r.a.func,onRetry:r.a.func}}).call(this,n(3))},254:function(e,t,n){"use strict";(function(e){var i=n(10),r=n.n(i),a=n(0),o=n.n(a),c=n(1),l=n(11),s=n(515),u=Object(c.forwardRef)((function(t,n){var i=t.className,a=t.content,o=t.dismissLabel,c=t.Icon,u=void 0===c?s.a:c,d=t.onDismiss;return e.createElement("div",{ref:n,className:r()("googlesitekit-audience-segmentation-info-notice",i)},e.createElement(u,{width:"20",height:"20"}),e.createElement("div",{className:"googlesitekit-audience-segmentation-info-notice__body"},e.createElement("p",null,a),o&&e.createElement(l.Button,{onClick:d,className:"googlesitekit-audience-segmentation-info-notice__dismiss",tertiary:!0},o)))}));u.propTypes={className:o.a.string,content:o.a.string.isRequired,dismissLabel:o.a.string,Icon:o.a.elementType,onDismiss:o.a.func},t.a=u}).call(this,n(3))},257:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BadgeWithTooltip}));var i=n(0),r=n.n(i),a=n(10),o=n.n(a),c=n(137);function BadgeWithTooltip(t){var n=t.className,i=void 0===n?"":n,r=t.label,a=t.onTooltipOpen,l=t.tooltipTitle;return e.createElement("span",{className:o()("googlesitekit-badge-with-tooltip","googlesitekit-badge",i)},r,l&&e.createElement(c.a,{onOpen:a,title:l}))}BadgeWithTooltip.propTypes={onTooltipOpen:r.a.func,tooltipTitle:r.a.node,className:r.a.string,label:r.a.node.isRequired}}).call(this,n(3))},258:function(e,t,n){"use strict";n.d(t,"a",(function(){return SurveyViewTrigger}));var i=n(1),r=n(0),a=n.n(r),o=n(4),c=n(13),l=n(7);function SurveyViewTrigger(e){var t=e.triggerID,n=e.ttl,r=void 0===n?0:n,a=Object(o.useSelect)((function(e){return e(c.c).isUsingProxy()})),s=Object(o.useDispatch)(l.a).triggerSurvey;return Object(i.useEffect)((function(){a&&s(t,{ttl:r})}),[a,t,r,s]),null}SurveyViewTrigger.propTypes={triggerID:a.a.string.isRequired,ttl:a.a.number}},26:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r}));var i="core/ui",r="activeContextID"},260:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminMenuTooltip}));var i=n(1),r=n(4),a=n(262),o=n(26),c=n(9),l=n(18),s=n(23);function AdminMenuTooltip(){var t=Object(l.a)(),n=Object(r.useDispatch)(o.b).setValue,u=Object(s.e)(),d=Object(r.useSelect)((function(e){return e(o.b).getValue("admin-menu-tooltip")||{isTooltipVisible:!1}})),g=d.isTooltipVisible,f=void 0!==g&&g,m=d.tooltipSlug,p=d.title,b=d.content,v=d.dismissLabel,h=Object(i.useCallback)((function(){m&&Object(c.I)("".concat(t,"_").concat(m),"tooltip_dismiss"),n("admin-menu-tooltip",void 0)}),[n,m,t]);if(!f)return null;var j=u===s.b||u===s.c;return e.createElement(a.a,{target:j?"body":'#adminmenu [href*="page=googlesitekit-settings"]',placement:j?"center":"auto",className:j?"googlesitekit-tour-tooltip__modal_step":"",disableOverlay:!j,slug:"ga4-activation-banner-admin-menu-tooltip",title:p,content:b,dismissLabel:v,onView:function(){Object(c.I)("".concat(t,"_").concat(m),"tooltip_view")},onDismiss:h})}}).call(this,n(3))},262:function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return JoyrideTooltip}));var r=n(5),a=n.n(r),o=n(14),c=n.n(o),l=n(0),s=n(32),u=n(456),d=n(1),g=n(119),f=n(75),m=n(101),p=n(23);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function JoyrideTooltip(t){var n=t.title,r=t.content,a=t.dismissLabel,o=t.disableOverlay,l=void 0===o||o,b=t.target,h=t.cta,j=void 0!==h&&h,I=t.className,y=t.styles,E=void 0===y?{}:y,O=t.slug,M=void 0===O?"":O,k=t.placement,N=void 0===k?"auto":k,_=t.onDismiss,D=void 0===_?function(){}:_,A=t.onView,T=void 0===A?function(){}:A,S=t.onTourStart,w=void 0===S?function(){}:S,C=t.onTourEnd,z=void 0===C?function(){}:C,R=function(){return!!e.document.querySelector(b)},x=Object(d.useState)(R),P=c()(x,2),L=P[0],B=P[1],G=Object(p.e)(),W=G===p.b||G===p.c,Z=Object(d.useState)(!0),V=c()(Z,2),U=V[0],F=V[1],H=Object(d.useRef)(W);if(Object(u.a)((function(){R()&&B(!0)}),L?null:250),Object(d.useEffect)((function(){if(L&&e.ResizeObserver){var t=e.document.querySelector(b),n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}),[b,L]),Object(d.useEffect)((function(){if(H.current!==W){F(!1);var e=setTimeout((function(){F(!0)}),50);return H.current=W,function(){return clearTimeout(e)}}}),[W]),!L)return null;var Y=[{title:n,target:b,content:r,disableBeacon:!0,isFixed:!0,placement:N,cta:j,className:I}],Q={close:a,last:a};return i.createElement(f.a,{slug:M},i.createElement(s.e,{callback:function(t){switch(t.type){case s.b.TOUR_START:w(),e.document.body.classList.add("googlesitekit-showing-tooltip");break;case s.b.TOUR_END:z(),e.document.body.classList.remove("googlesitekit-showing-tooltip");break;case s.b.STEP_AFTER:D();break;case s.b.TOOLTIP:T()}},disableOverlay:l,spotlightPadding:0,floaterProps:m.b,locale:Q,steps:Y,styles:v(v(v({},m.c),E),{},{options:v(v({},m.c.options),null==E?void 0:E.options),spotlight:v(v({},m.c.spotlight),null==E?void 0:E.spotlight)}),tooltipComponent:g.a,run:U,disableScrolling:!0}))}JoyrideTooltip.propTypes={title:l.PropTypes.node,content:l.PropTypes.string,disableOverlay:l.PropTypes.bool,dismissLabel:l.PropTypes.string,target:l.PropTypes.string.isRequired,onDismiss:l.PropTypes.func,onShow:l.PropTypes.func,className:l.PropTypes.string,styles:l.PropTypes.object,slug:l.PropTypes.string,placement:l.PropTypes.string,onView:l.PropTypes.func}}).call(this,n(28),n(3))},267:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return f})),n.d(t,"b",(function(){return SpinnerButton}));var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(10),u=n.n(s),d=n(359),g=n(268),f={BEFORE:"before",AFTER:"after"};function SpinnerButton(t){var n=t.className,i=t.onClick,a=void 0===i?function(){}:i,c=t.isSaving,l=void 0!==c&&c,s=t.spinnerPosition,m=void 0===s?f.AFTER:s,p=o()(t,["className","onClick","isSaving","spinnerPosition"]);return e.createElement(d.a,r()({className:u()(n,"googlesitekit-button-icon--spinner",{"googlesitekit-button-icon--spinner__running":l,"googlesitekit-button-icon--spinner__before":m===f.BEFORE,"googlesitekit-button-icon--spinner__after":m===f.AFTER}),icon:l&&m===f.BEFORE?e.createElement(g.a,{size:14}):void 0,trailingIcon:l&&m===f.AFTER?e.createElement(g.a,{size:14}):void 0,onClick:a},p))}SpinnerButton.propTypes={className:l.a.string,onClick:l.a.func,isSaving:l.a.bool,spinnerPosition:l.a.oneOf(Object.values(f))}}).call(this,n(3))},268:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CircularProgress}));var i=n(639);function CircularProgress(t){return e.createElement(i.a,t)}}).call(this,n(3))},273:function(e,t,n){"use strict";(function(e){var i=n(10),r=n.n(i),a=n(0),o=n.n(a),c=n(1),l=Object(c.forwardRef)((function(t,n){var i=t.children,a=t.className,o=t.widgetSlug,c=t.noPadding,l=t.Header,s=t.Footer;return e.createElement("div",{className:r()("googlesitekit-widget","googlesitekit-widget--".concat(o),{"googlesitekit-widget--no-padding":c},{"googlesitekit-widget--with-header":l},a),ref:n},l&&e.createElement("div",{className:"googlesitekit-widget__header"},e.createElement(l,null)),e.createElement("div",{className:"googlesitekit-widget__body"},i),s&&e.createElement("div",{className:"googlesitekit-widget__footer"},e.createElement(s,null)))}));l.defaultProps={children:void 0,noPadding:!1},l.propTypes={children:o.a.node,widgetSlug:o.a.string.isRequired,noPadding:o.a.bool,Header:o.a.elementType,Footer:o.a.elementType},t.a=l}).call(this,n(3))},274:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetRecoverableModules}));var i=n(5),r=n.n(i),a=n(20),o=n.n(a),c=n(27),l=n.n(c),s=n(22),u=n.n(s),d=n(0),g=n.n(d),f=n(1),m=n(156),p=n(173);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function WidgetRecoverableModules(t){var n=t.widgetSlug,i=t.moduleSlugs,r=u()(t,["widgetSlug","moduleSlugs"]),a=Object(f.useMemo)((function(){return{moduleSlug:l()(i).sort().join(","),moduleSlugs:i}}),[i]);return Object(m.a)(n,p.a,a),e.createElement(p.a,o()({moduleSlugs:i},r))}WidgetRecoverableModules.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:g.a.string.isRequired},p.a.propTypes)}).call(this,n(3))},277:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileText}));var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(193),u=n(9),d=n(208);function MetricTileText(t){var n=t.metricValue,i=t.metricValueFormat,a=t.subText,c=t.previousValue,l=t.currentValue,g=o()(t,["metricValue","metricValueFormat","subText","previousValue","currentValue"]),f=Object(u.m)(i);return e.createElement(d.a,r()({className:"googlesitekit-km-widget-tile--text"},g),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-container"},e.createElement("div",{className:"googlesitekit-km-widget-tile__metric"},n),e.createElement("p",{className:"googlesitekit-km-widget-tile__subtext"},a)),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-change-container"},e.createElement(s.a,{previousValue:c,currentValue:l,isAbsolute:"percent"===(null==f?void 0:f.style)})))}MetricTileText.propTypes={metricValue:l.a.oneOfType([l.a.string,l.a.number]),subtext:l.a.string,previousValue:l.a.number,currentValue:l.a.number}}).call(this,n(3))},29:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"f",(function(){return c})),n.d(t,"k",(function(){return l})),n.d(t,"j",(function(){return s})),n.d(t,"h",(function(){return u})),n.d(t,"i",(function(){return d})),n.d(t,"e",(function(){return g})),n.d(t,"g",(function(){return f}));var i=1,r=2,a=3,o="enhanced-measurement-activation-banner-tooltip-state",c="enhanced-measurement-activation-banner-dismissed-item",l="_r.explorerCard..selmet",s="_r.explorerCard..seldim",u="_r..dataFilters",d="_r..nav",g="key-metrics-connect-ga4-cta-widget",f="analytics-4"},30:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i="core/forms"},316:function(e,t,n){"use strict";(function(e){var i=n(0),r=n.n(i),a=n(10),o=n.n(a),c=n(1),l=n(2),s=n(11),u=n(79),d=n(21);function NewBadge(t){var n=t.tooltipTitle,i=t.learnMoreLink,r=t.forceOpen,a=t.hasLeftSpacing,g=t.hasNoSpacing,f=t.onLearnMoreClick,m=void 0===f?function(){}:f,p=e.createElement(u.a,{className:o()("googlesitekit-new-badge",{"googlesitekit-new-badge--has-no-spacing":g}),label:Object(l.__)("New","google-site-kit"),hasLeftSpacing:a});return n?e.createElement(s.Tooltip,{tooltipClassName:"googlesitekit-new-badge__tooltip",title:e.createElement(c.Fragment,null,n,e.createElement("br",null),e.createElement(d.a,{href:i,onClick:m,external:!0,hideExternalIndicator:!0},Object(l.__)("Learn more","google-site-kit"))),placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,open:r,interactive:!0},p):p}NewBadge.propTypes={tooltipTitle:r.a.string,learnMoreLink:r.a.string,forceOpen:r.a.bool,onLearnMoreClick:r.a.func,hasLeftSpacing:r.a.bool,hasNoSpacing:r.a.bool},t.a=NewBadge}).call(this,n(3))},317:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileError}));var i=n(102),r=n(137);function MetricTileError(t){var n=t.children,a=t.headerText,o=t.infoTooltip,c=t.title;return e.createElement("div",{className:"googlesitekit-km-widget-tile--error"},e.createElement(i.a,{title:c,headerText:a,headerContent:o&&e.createElement(r.a,{title:o}),description:"",error:!0},n))}}).call(this,n(3))},318:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileMetric}));var i=n(0),r=n.n(i),a=n(9);function AudienceTileMetric(t){var n=t.TileIcon,i=t.title,r=t.metricValue,o=t.Badge,c=t.metricValueFormat;return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__icon"},e.createElement(n,null)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__container"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__value"},Object(a.B)(r,c)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__title"},i)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__badge-container"},e.createElement(o,null)))}AudienceTileMetric.propTypes={TileIcon:r.a.elementType.isRequired,title:r.a.string.isRequired,metricValue:r.a.number.isRequired,Badge:r.a.elementType.isRequired,metricValueFormat:r.a.object}}).call(this,n(3))},322:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OverlayNotification}));var i=n(20),r=n.n(i),a=n(6),o=n.n(a),c=n(5),l=n.n(c),s=n(16),u=n.n(s),d=n(22),g=n.n(d),f=n(0),m=n.n(f),p=n(4),b=n(41),v=n(69),h=n(378);function j(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function I(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?j(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function OverlayNotification(t){var n=t.notificationID,i=t.ctaButton,a=t.dismissButton,c=t.gaTrackingEventArgs,l=g()(t,["notificationID","ctaButton","dismissButton","gaTrackingEventArgs"]),s=Object(v.a)(n,null==c?void 0:c.category,{confirmAction:null==c?void 0:c.confirmAction,dismissAction:null==c?void 0:c.dismissAction}),d=Object(p.useDispatch)(b.a).dismissNotification,f=function(){var e=u()(o.a.mark((function e(t){var i;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==a||null===(i=a.onClick)||void 0===i?void 0:i.call(a,t);case 2:s.dismiss(null==c?void 0:c.label,null==c?void 0:c.value),d(n,I({},a.dismissOptions));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),m=function(){var e=u()(o.a.mark((function e(t){var n;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s.confirm(null==c?void 0:c.label,null==c?void 0:c.value),e.next=3,null==i||null===(n=i.onClick)||void 0===n?void 0:n.call(i,t);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(h.a,r()({ctaButton:I(I({},i),{},{onClick:m}),dismissButton:I(I({},a),{},{onClick:f})},l,{visible:!0}))}OverlayNotification.propTypes={notificationID:m.a.string,ctaButton:m.a.object,dismissButton:m.a.oneOfType([m.a.object,m.a.bool])}}).call(this,n(3))},323:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.149 7.96l-5.166 5.166a.344.344 0 00-.094.176l-.35 1.755a.344.344 0 00.404.404l1.755-.35a.344.344 0 00.175-.095l5.166-5.165-1.89-1.89zm2.301-1.814a1.031 1.031 0 00-1.458 0L6.497 12.64a1.031 1.031 0 00-.282.527l-.35 1.755a1.031 1.031 0 001.213 1.213l1.754-.35c.2-.04.383-.139.527-.283l6.495-6.494a1.031 1.031 0 000-1.459L14.45 6.146z"}),o=i.createElement("path",{d:"M12.149 7.96l.117-.116a.165.165 0 00-.234 0l.117.117zm-5.166 5.166l-.116-.116.116.116zm-.094.176l.162.033-.162-.033zm-.35 1.755l.161.032-.162-.032zm.404.404l.032.162-.032-.162zm1.755-.35l.032.161-.032-.162zm.175-.095l.117.117-.117-.117zm5.166-5.165l.116.116a.165.165 0 000-.233l-.116.117zm-1.047-3.705l.116.116-.116-.116zm1.458 0l-.116.116.116-.116zM6.497 12.64l.117.117-.117-.117zm-.282.527l-.162-.032.162.032zm-.35 1.755l.161.032-.162-.032zm1.213 1.213l-.033-.162.033.162zm1.754-.35l.033.161-.033-.162zm.527-.283l.117.117-.117-.117zm6.495-6.494l-.117-.117.117.117zm0-1.459l.117-.116-.117.116zm-3.822.295L6.867 13.01l.233.233 5.166-5.165-.234-.234zM6.867 13.01a.509.509 0 00-.14.26l.324.065a.18.18 0 01.05-.092l-.234-.233zm-.14.26l-.35 1.754.323.065.351-1.755-.323-.064zm-.35 1.754a.509.509 0 00.598.599l-.064-.324a.179.179 0 01-.21-.21l-.324-.065zm.598.599l1.755-.35-.065-.325-1.754.351.064.324zm1.755-.35a.508.508 0 00.26-.14l-.233-.233a.18.18 0 01-.092.048l.065.324zm.26-.14l5.165-5.166-.233-.233L8.757 14.9l.233.233zm3.042-7.055l1.89 1.89.233-.234-1.89-1.89-.233.234zm1.076-1.816a.866.866 0 011.226 0l.233-.233a1.196 1.196 0 00-1.692 0l.233.233zm-6.494 6.495l6.494-6.495-.233-.233-6.494 6.495.233.233zm-.237.443a.866.866 0 01.237-.443l-.233-.233c-.167.167-.281.38-.328.61l.324.066zm-.35 1.754l.35-1.754-.324-.065-.35 1.755.323.064zm1.018 1.02a.866.866 0 01-1.019-1.02l-.323-.065a1.196 1.196 0 001.407 1.408l-.065-.324zm1.755-.351l-1.755.35.065.324 1.755-.35-.065-.324zm.443-.237a.866.866 0 01-.443.237l.065.323c.231-.046.444-.16.611-.327l-.233-.233zm6.494-6.495l-6.494 6.495.233.233 6.495-6.494-.234-.234zm0-1.225a.866.866 0 010 1.225l.234.234a1.196 1.196 0 000-1.692l-.234.233zm-1.403-1.404l1.403 1.404.234-.233-1.404-1.404-.233.233z"});t.a=function SvgPencilAlt(e){return i.createElement("svg",r({viewBox:"0 0 22 22",fill:"currentColor"},e),a,o)}},328:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelHeader}));var i=n(0),r=n.n(i),a=n(21),o=n(114);function SelectionPanelHeader(t){var n=t.children,i=t.title,r=t.onCloseClick;return e.createElement("header",{className:"googlesitekit-selection-panel-header"},e.createElement("div",{className:"googlesitekit-selection-panel-header__row"},e.createElement("h3",null,i),e.createElement(a.a,{className:"googlesitekit-selection-panel-header__close",onClick:r,linkButton:!0},e.createElement(o.a,{width:"15",height:"15"}))),n)}SelectionPanelHeader.propTypes={children:r.a.node,title:r.a.string,onCloseClick:r.a.func}}).call(this,n(3))},329:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItem}));var i=n(0),r=n.n(i),a=n(2),o=n(342),c=n(79);function SelectionPanelItem(t){var n=t.children,i=t.id,r=t.slug,l=t.title,s=t.description,u=t.isItemSelected,d=t.isItemDisabled,g=t.onCheckboxChange,f=t.subtitle,m=t.suffix,p=t.badge,b=t.isNewlyDetected;return e.createElement("div",{className:"googlesitekit-selection-panel-item"},e.createElement(o.a,{badge:p,checked:u,disabled:d,id:i,onChange:g,title:l,value:r},f&&e.createElement("span",{className:"googlesitekit-selection-panel-item__subtitle"},f),s,n),b&&e.createElement(c.a,{label:Object(a.__)("New","google-site-kit")}),m&&e.createElement("span",{className:"googlesitekit-selection-panel-item__suffix"},m))}SelectionPanelItem.propTypes={children:r.a.node,id:r.a.string,slug:r.a.string,title:r.a.string,description:r.a.string,isItemSelected:r.a.bool,isItemDisabled:r.a.bool,onCheckboxChange:r.a.func,subtitle:r.a.string,suffix:r.a.node,badge:r.a.node,isNewlyDetected:r.a.bool}}).call(this,n(3))},33:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i="core/location"},330:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItems}));var i=n(20),r=n.n(i),a=n(0),o=n.n(a),c=n(1),l=n(2);function SelectionPanelItems(t){var n=t.currentSelectionTitle,i=void 0===n?Object(l.__)("Current selection","google-site-kit"):n,a=t.availableItemsTitle,o=void 0===a?Object(l.__)("Additional items","google-site-kit"):a,s=t.savedItemSlugs,u=void 0===s?[]:s,d=t.availableSavedItems,g=void 0===d?{}:d,f=t.availableUnsavedItems,m=void 0===f?{}:f,p=t.ItemComponent,b=t.notice,v=function(t){return Object.keys(t).map((function(n){return e.createElement(p,r()({key:n,slug:n,savedItemSlugs:u},t[n]))}))},h=Object.keys(m).length;return e.createElement("div",{className:"googlesitekit-selection-panel-items"},0!==u.length&&e.createElement(c.Fragment,null,e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},i),e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},v(g)),h>0&&e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},o)),h>0&&e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},v(m)),b)}SelectionPanelItems.propTypes={currentSelectionTitle:o.a.string,availableItemsTitle:o.a.string,savedItemSlugs:o.a.array,availableSavedItems:o.a.object,availableUnsavedItems:o.a.object,ItemComponent:o.a.elementType,notice:o.a.node}}).call(this,n(3))},331:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelFooter}));var i=n(6),r=n.n(i),a=n(16),o=n.n(a),c=n(14),l=n.n(c),s=n(15),u=n(0),d=n.n(u),g=n(1),f=n(42),m=n(2),p=n(4),b=n(11),v=n(59),h=n(9),j=n(8),I=n(48),y=n(35);function SelectionPanelFooter(t){var n=t.savedItemSlugs,i=void 0===n?[]:n,a=t.selectedItemSlugs,c=void 0===a?[]:a,u=t.saveSettings,d=void 0===u?function(){}:u,E=t.saveError,O=t.itemLimitError,M=t.minSelectedItemCount,k=void 0===M?0:M,N=t.maxSelectedItemCount,_=void 0===N?0:N,D=t.isBusy,A=t.onSaveSuccess,T=void 0===A?function(){}:A,S=t.onCancel,w=void 0===S?function(){}:S,C=t.isOpen,z=t.closePanel,R=void 0===z?function(){}:z,x=Object(g.useState)(null),P=l()(x,2),L=P[0],B=P[1],G=Object(g.useState)(!1),W=l()(G,2),Z=W[0],V=W[1],U=Object(p.useSelect)((function(e){return e(j.r).isFetchingSyncAvailableAudiences()})),F=Object(g.useMemo)((function(){return!Object(s.isEqual)(Object(h.E)(c),Object(h.E)(i))}),[i,c]),H=(null==i?void 0:i.length)>0&&F?Object(m.__)("Apply changes","google-site-kit"):Object(m.__)("Save selection","google-site-kit"),Y=Object(g.useCallback)(o()(r.a.mark((function e(){var t;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d(c);case 2:t=e.sent,t.error||(T(),R(),B(H),V(!0));case 5:case"end":return e.stop()}}),e)}))),[d,c,T,R,H]),Q=Object(g.useCallback)((function(){R(),w()}),[R,w]),X=Object(g.useState)(null),J=l()(X,2),K=J[0],q=J[1];Object(g.useEffect)((function(){null!==K&&K!==C&&C&&(B(null),V(!1)),q(C)}),[C,K]);var $=(null==c?void 0:c.length)||0,ee=U?e.createElement(I.a,{width:"89px",height:"20px"}):e.createElement("p",{className:"googlesitekit-selection-panel-footer__item-count"},Object(f.a)(Object(m.sprintf)(/* translators: 1: Number of selected items. 2: Maximum number of items that can be selected. */
Object(m.__)("%1$d selected <MaxCount>(up to %2$d)</MaxCount>","google-site-kit"),$,_),{MaxCount:e.createElement("span",{className:"googlesitekit-selection-panel-footer__item-count--max-count"})}));return e.createElement("footer",{className:"googlesitekit-selection-panel-footer"},E&&e.createElement(v.a,{error:E}),e.createElement("div",{className:"googlesitekit-selection-panel-footer__content"},F&&O?e.createElement(y.a,{type:y.a.TYPES.ERROR,description:O}):ee,e.createElement("div",{className:"googlesitekit-selection-panel-footer__actions"},e.createElement(b.Button,{onClick:Q,disabled:D,tertiary:!0},Object(m.__)("Cancel","google-site-kit")),e.createElement(b.SpinnerButton,{onClick:Y,isSaving:D,disabled:$<k||$>_||D||!C&&Z},L||H))))}SelectionPanelFooter.propTypes={savedItemSlugs:d.a.array,selectedItemSlugs:d.a.array,saveSettings:d.a.func,saveError:d.a.object,itemLimitError:d.a.string,minSelectedItemCount:d.a.number,maxSelectedItemCount:d.a.number,isBusy:d.a.bool,onSaveSuccess:d.a.func,onCancel:d.a.func,isOpen:d.a.bool,closePanel:d.a.func}}).call(this,n(3))},336:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportZero}));var i=n(5),r=n.n(i),a=n(20),o=n.n(a),c=n(22),l=n.n(c),s=n(0),u=n.n(s),d=n(1),g=n(156),f=n(183);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function WidgetReportZero(t){var n=t.widgetSlug,i=t.moduleSlug,r=l()(t,["widgetSlug","moduleSlug"]),a=Object(d.useMemo)((function(){return{moduleSlug:i}}),[i]);return Object(g.a)(n,f.a,a),e.createElement(f.a,o()({moduleSlug:i},r))}WidgetReportZero.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:u.a.string.isRequired},f.a.propTypes)}).call(this,n(3))},337:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportError}));var i=n(5),r=n.n(i),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(179);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function WidgetReportError(t){t.widgetSlug;var n=o()(t,["widgetSlug"]);return e.createElement(s.a,n)}WidgetReportError.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:l.a.string.isRequired},s.a.propTypes)}).call(this,n(3))},338:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPDashboardReportError}));var i=n(0),r=n.n(i),a=n(568),o=n(212),c=n(4),l=n(26),s=n(179);function WPDashboardReportError(t){var n=t.moduleSlug,i=t.error,r=Object(o.a)(WPDashboardReportError,"WPDashboardReportError"),u=Object(c.useDispatch)(l.b).setValue,d=i.message,g=Object(c.useSelect)((function(e){return e(l.b).getValue("WPDashboardReportError-".concat(n,"-").concat(d))}));return Object(a.a)((function(){u("WPDashboardReportError-".concat(n,"-").concat(d),r)}),(function(){u("WPDashboardReportError-".concat(n,"-").concat(d),void 0)})),g!==r?null:e.createElement(s.a,{moduleSlug:n,error:i})}WPDashboardReportError.propTypes={moduleSlug:r.a.string.isRequired,error:r.a.object.isRequired}}).call(this,n(3))},339:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M7.334 11.333h1.333v-4H7.334v4zM8.001 6a.658.658 0 00.667-.667.605.605 0 00-.2-.467.605.605 0 00-.467-.2.658.658 0 00-.667.667c0 .189.061.35.183.483A.69.69 0 008.001 6zm0 8.666a6.583 6.583 0 01-2.6-.516 6.85 6.85 0 01-2.117-1.434A6.85 6.85 0 011.851 10.6 6.582 6.582 0 011.334 8c0-.923.172-1.79.517-2.6a6.85 6.85 0 011.433-2.117c.6-.6 1.306-1.072 2.117-1.417A6.404 6.404 0 018 1.333c.922 0 1.789.178 2.6.533a6.618 6.618 0 012.116 1.417c.6.6 1.072 1.306 1.417 2.117.355.81.533 1.677.533 2.6 0 .922-.178 1.789-.533 2.6a6.619 6.619 0 01-1.417 2.116 6.85 6.85 0 01-2.116 1.434 6.583 6.583 0 01-2.6.516zm0-1.333c1.489 0 2.75-.517 3.783-1.55s1.55-2.294 1.55-3.783c0-1.49-.517-2.75-1.55-3.784-1.033-1.033-2.294-1.55-3.783-1.55-1.49 0-2.75.517-3.784 1.55C3.184 5.25 2.667 6.511 2.667 8c0 1.489.517 2.75 1.55 3.783 1.034 1.033 2.295 1.55 3.784 1.55z",fill:"currentColor"});t.a=function SvgInfoGreen(e){return i.createElement("svg",r({viewBox:"0 0 16 16",fill:"none"},e),a)}},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return l})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(15);var i=n(2),r="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===r}function l(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function s(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||l(e)||c(e)||s(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(i.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(i.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},340:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanel}));var i=n(10),r=n.n(i),a=n(0),o=n.n(a),c=n(341);function SelectionPanel(t){var n=t.children,i=t.isOpen,a=t.isLoading,o=t.onOpen,l=t.closePanel,s=t.className,u=null==s?void 0:s.split(/\s+/).map((function(e){return".".concat(e)})).join(""),d=u?"".concat(u," .googlesitekit-selection-panel-item .googlesitekit-selection-box input"):".googlesitekit-selection-panel-item .googlesitekit-selection-box input";return e.createElement(c.a,{className:r()("googlesitekit-selection-panel",s),isOpen:i,isLoading:a,onOpen:o,closeSheet:l,focusTrapOptions:{initialFocus:d}},n)}SelectionPanel.propTypes={children:o.a.node,isOpen:o.a.bool,isLoading:o.a.bool,onOpen:o.a.func,closePanel:o.a.func,className:o.a.string}}).call(this,n(3))},341:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SideSheet}));var i=n(5),r=n.n(i),a=n(10),o=n.n(a),c=n(427),l=n.n(c),s=n(0),u=n.n(s),d=n(213),g=n(397),f=n(1),m=n(57),p=n(75);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SideSheet(t){var n=t.className,i=t.children,r=t.isOpen,a=t.isLoading,c=t.onOpen,s=void 0===c?function(){}:c,u=t.closeSheet,b=void 0===u?function(){}:u,h=t.focusTrapOptions,j=void 0===h?{}:h,I=Object(f.useRef)();return Object(f.useEffect)((function(){r?(s(),document.body.classList.add("googlesitekit-side-sheet-scroll-lock")):document.body.classList.remove("googlesitekit-side-sheet-scroll-lock")}),[r,s]),Object(d.a)(I,b),Object(g.a)((function(e){return r&&m.c===e.keyCode}),b),e.createElement(p.a,null,e.createElement(l.a,{active:!!r&&!a,focusTrapOptions:v({fallbackFocus:"body"},j)},e.createElement("section",{ref:I,className:o()("googlesitekit-side-sheet",n,{"googlesitekit-side-sheet--open":r}),role:"dialog","aria-modal":"true","aria-hidden":!r,tabIndex:"0"},i)),r&&e.createElement("span",{className:"googlesitekit-side-sheet-overlay"}))}SideSheet.propTypes={className:u.a.string,children:u.a.node,isOpen:u.a.bool,isLoading:u.a.bool,onOpen:u.a.func,closeSheet:u.a.func,focusTrapOptions:u.a.object}}).call(this,n(3))},342:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionBox}));var i=n(0),r=n.n(i),a=n(10),o=n.n(a),c=n(11);function SelectionBox(t){var n=t.badge,i=t.checked,r=t.children,a=t.disabled,l=t.id,s=t.onChange,u=t.title,d=t.value;return e.createElement("div",{className:o()("googlesitekit-selection-box",{"googlesitekit-selection-box--disabled":a})},e.createElement(c.Checkbox,{checked:i,description:r,disabled:a,id:l,name:l,onChange:s,value:d,badge:n},u))}SelectionBox.propTypes={badge:r.a.node,checked:r.a.bool,children:r.a.node,disabled:r.a.bool,id:r.a.string,onChange:r.a.func,title:r.a.string,value:r.a.string}}).call(this,n(3))},35:function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(0),o=n.n(a),c=n(10),l=n.n(c),s=n(1),u=n(139),d=n(140),g=n(141),f=n(105),m=n(106),p=n(38);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=Object(s.forwardRef)((function(t,n){var i=t.className,r=t.title,a=t.description,o=t.dismissButton,c=t.ctaButton,s=t.type,b=void 0===s?p.a.INFO:s,v=t.children,h=t.hideIcon;return e.createElement("div",{ref:n,className:l()("googlesitekit-notice","googlesitekit-notice--".concat(b),i)},!h&&e.createElement("div",{className:"googlesitekit-notice__icon"},e.createElement(u.a,{type:b})),e.createElement("div",{className:"googlesitekit-notice__content"},r&&e.createElement(d.a,null,r),a&&e.createElement(g.a,null,a)),((null==o?void 0:o.label)||(null==o?void 0:o.onClick)||(null==c?void 0:c.label)&&((null==c?void 0:c.onClick)||(null==c?void 0:c.href))||v)&&e.createElement("div",{className:"googlesitekit-notice__action"},v,((null==o?void 0:o.label)||(null==o?void 0:o.onClick))&&e.createElement(m.a,{label:o.label,onClick:o.onClick,disabled:o.disabled}),(null==c?void 0:c.label)&&((null==c?void 0:c.onClick)||(null==c?void 0:c.href))&&e.createElement(f.a,{label:c.label,onClick:c.onClick,inProgress:c.inProgress,disabled:c.disabled,href:c.href,external:c.external,hideExternalIndicator:c.hideExternalIndicator})))}));h.TYPES=p.a,h.propTypes={className:o.a.string,title:o.a.oneOfType([o.a.string,o.a.object]),description:o.a.node,type:o.a.oneOf(Object.values(p.a)),dismissButton:o.a.shape(m.a.propTypes),ctaButton:o.a.shape(v(v({},f.a.propTypes),{},{label:o.a.string})),children:o.a.node,hideIcon:o.a.bool},t.a=h}).call(this,n(3))},358:function(e,t,n){"use strict";(function(e){var i=n(0),r=n.n(i),a=n(4),o=n(60),c=n(8),l=n(29),s=n(7),u=n(508),d=n(127),g=n(9),f=n(18),m=Object(d.a)(u.a);function NoAudienceBannerWidget(t){var n=t.Widget,i=t.WidgetNull,r=Object(f.a)(),o=Object(a.useSelect)((function(e){var t=e(c.r).getOrSyncAvailableAudiences();return null==t?void 0:t.map((function(e){return e.name}))})),l=Object(a.useSelect)((function(e){return e(s.a).getConfiguredAudiences()})),u=Object(a.useSelect)((function(e){return e(s.a).didSetAudiences()})),d=null==l?void 0:l.every((function(e){return Array.isArray(o)&&!o.includes(e)}));return l&&(0===(null==l?void 0:l.length)||d)?e.createElement(n,{noPadding:!0},e.createElement(m,{onInView:function(){Object(g.I)("".concat(r,"_audiences-no-audiences"),"view_banner",u?"no-longer-available":"none-selected")}})):e.createElement(i,null)}NoAudienceBannerWidget.propTypes={Widget:r.a.elementType.isRequired,WidgetNull:r.a.elementType.isRequired},t.a=Object(o.a)({moduleName:l.g})(NoAudienceBannerWidget)}).call(this,n(3))},359:function(e,t,n){"use strict";(function(e){var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(428),u=n(1),d=n(2),g=n(17),f=n(386),m=n(387),p=Object(u.forwardRef)((function(t,n){var i=t.children,a=t.href,c=void 0===a?null:a,l=t.text,p=void 0!==l&&l,b=t.className,v=void 0===b?"":b,h=t.danger,j=void 0!==h&&h,I=t.disabled,y=void 0!==I&&I,E=t.target,O=t.icon,M=void 0===O?null:O,k=t.trailingIcon,N=void 0===k?null:k,_=t["aria-label"],D=t.title,A=void 0===D?null:D,T=t.customizedTooltip,S=void 0===T?null:T,w=t.tooltip,C=void 0!==w&&w,z=t.inverse,R=void 0!==z&&z,x=t.hideTooltipTitle,P=void 0!==x&&x,L=t.tooltipEnterDelayInMS,B=void 0===L?100:L,G=t.tertiary,W=void 0!==G&&G,Z=t.callout,V=void 0!==Z&&Z,U=t.calloutStyle,F=void 0===U?null:U,H=o()(t,["children","href","text","className","danger","disabled","target","icon","trailingIcon","aria-label","title","customizedTooltip","tooltip","inverse","hideTooltipTitle","tooltipEnterDelayInMS","tertiary","callout","calloutStyle"]),Y=Object(u.useCallback)((function(e){null!==e&&g.i.attachTo(e)}),[]),Q=Object(s.a)(n,Y),X=P?null:A||S||_;return e.createElement(m.a,{disabled:y,tooltip:C,tooltipTitle:X,hasIconOnly:!!M&&void 0===i,tooltipEnterDelayInMS:B},e.createElement(f.a,r()({href:c,disabled:y,className:v,danger:j,text:p,tertiary:W,inverse:R,callout:V,calloutStyle:F,ref:Q,"aria-label":function(){var e=_;if("_blank"!==E)return e;var t=Object(d._x)("(opens in a new tab)","screen reader text","google-site-kit");return"string"==typeof i&&(e=e||i),e?"".concat(e," ").concat(t):t}(),target:E||"_self"},H),M,i&&e.createElement("span",{className:"mdc-button__label"},i),N))}));p.propTypes={onClick:l.a.func,children:l.a.node,href:l.a.string,text:l.a.bool,className:l.a.string,danger:l.a.bool,disabled:l.a.bool,icon:l.a.element,trailingIcon:l.a.element,title:l.a.string,customizedTooltip:l.a.element,tooltip:l.a.bool,inverse:l.a.bool,hideTooltipTitle:l.a.bool,callout:l.a.bool,calloutStyle:l.a.oneOf(["primary","warning","error"])},t.a=p}).call(this,n(3))},360:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Tooltip}));var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(10),u=n.n(s),d=n(640),g=n(1);function Tooltip(t){var n=t.children,i=t.popperClassName,a=t.tooltipClassName,c=t.onOpen,l=t.onClose,s=o()(t,["children","popperClassName","tooltipClassName","onOpen","onClose"]),f=Object(g.useRef)(!1),m=c?function(){f.current||(f.current=!0,null==c||c())}:void 0,p=c?function(){f.current=!1,null==l||l()}:l;return e.createElement(d.a,r()({classes:{popper:u()("googlesitekit-tooltip-popper",i),tooltip:u()("googlesitekit-tooltip",a)},onOpen:m,onClose:p},s,{arrow:!0}),n)}Tooltip.propTypes={children:l.a.node,popperClassName:l.a.string,tooltipClassName:l.a.string,onOpen:l.a.func,onClose:l.a.func}}).call(this,n(3))},361:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GetHelpLink}));var i=n(0),r=n.n(i),a=n(42),o=n(2),c=n(21);function GetHelpLink(t){var n=t.linkURL;return Object(a.a)(Object(o.__)("Contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(c.a,{href:n,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))})}GetHelpLink.propTypes={linkURL:r.a.string.isRequired}}).call(this,n(3))},362:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var i=n(5),r=n.n(i),a=n(4),o=n(7),c=n(8);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t){return Object(a.useSelect)((function(n){return t.reduce((function(t,i){var r=n(c.r).getPartialDataSiteKitAudience(i);if(void 0===r)return{};var a={};r?a.newVsReturning="new-visitors"===r.audienceSlug?"new":"returning":a.audienceResourceName=i;var o=n(c.r).hasFinishedResolution("getReport",[s(s({},e),{},{dimensionFilters:s(s({},e.dimensionFilters),a)})]);return t[i]=o,t}),{})}))}function d(e,t){return Object(a.useSelect)((function(n){return t.reduce((function(t,i){var r=n(c.r).getPartialDataSiteKitAudience(i);if(void 0===r)return t;var a={};r?a.newVsReturning="new-visitors"===r.audienceSlug?"new":"returning":a.audienceResourceName=i;var o=n(c.r).getErrorForSelector("getReport",[s(s({},e),{},{dimensionFilters:s(s({},e.dimensionFilters),a)})]);return o&&(t[i]=o),t}),{})}))}function g(e){var t,n,i,r,l=e.isSiteKitAudiencePartialData,g=e.siteKitAudiences,f=e.otherAudiences,m=Object(a.useSelect)((function(e){return e(o.a).getConfiguredAudiences()})),p={audienceResourceName:m},b=Object(a.useSelect)((function(e){return e(o.a).getDateRangeDates({offsetDays:c.g,compare:!0})})),v=b.startDate,h=b.endDate,j=void 0===l?void 0:f.length>0||!1===l,I=g.length>0&&l,y=s(s({},b),{},{dimensions:[{name:"audienceResourceName"}],dimensionFilters:p,metrics:[{name:"totalUsers"},{name:"sessionsPerUser"},{name:"screenPageViewsPerSession"},{name:"screenPageViews"}]}),E=Object(a.useInViewSelect)((function(e){if(void 0!==j)return j?e(c.r).getReport(y):null}),[j,y]),O=Object(a.useSelect)((function(e){if(void 0!==j)return!j||e(c.r).hasFinishedResolution("getReport",[y])})),M=Object(a.useSelect)((function(e){if(void 0!==j)return j?e(c.r).getErrorForSelector("getReport",[y]):null})),k=s(s({},b),{},{dimensions:[{name:"newVsReturning"}],dimensionFilters:{newVsReturning:["new","returning"]},metrics:[{name:"totalUsers"},{name:"sessionsPerUser"},{name:"screenPageViewsPerSession"},{name:"screenPageViews"}]}),N=Object(a.useInViewSelect)((function(e){if(void 0!==I)return I?e(c.r).getReport(k):null}),[I,k]),_=Object(a.useSelect)((function(e){if(void 0!==I)return!I||e(c.r).hasFinishedResolution("getReport",[k])})),D=Object(a.useSelect)((function(e){if(void 0!==I)return I?e(c.r).getErrorForSelector("getReport",[k]):null})),A={startDate:v,endDate:h,metrics:[{name:"screenPageViews"}]},T=Object(a.useInViewSelect)((function(e){return e(c.r).getReport(A)})),S=Object(a.useSelect)((function(e){return e(c.r).hasFinishedResolution("getReport",[A])})),w=Object(a.useSelect)((function(e){return e(c.r).getErrorForSelector("getReport",[A])})),C=Number(null==T||null===(t=T.totals)||void 0===t||null===(n=t[0])||void 0===n||null===(i=n.metricValues)||void 0===i||null===(r=i[0])||void 0===r?void 0:r.value)||0,z={startDate:v,endDate:h,dimensions:["city"],metrics:[{name:"totalUsers"}],orderby:[{metric:{metricName:"totalUsers"},desc:!0}],limit:4},R=Object(a.useInViewSelect)((function(e){return e(c.r).getReportForAllAudiences(z,m)})),x=u(z,m),P=d(z,m),L={startDate:v,endDate:h,dimensions:["pagePath"],metrics:[{name:"screenPageViews"}],dimensionFilters:{"customEvent:googlesitekit_post_type":{filterType:"stringFilter",matchType:"EXACT",value:"post"}},orderby:[{metric:{metricName:"screenPageViews"},desc:!0}],limit:3},B=Object(a.useInViewSelect)((function(e){return e(c.r).getReportForAllAudiences(L,m)})),G=u(L,m),W=d(L,m),Z={startDate:v,endDate:h,dimensions:["pagePath","pageTitle"],metrics:[{name:"screenPageViews"}],dimensionFilters:{"customEvent:googlesitekit_post_type":{filterType:"stringFilter",matchType:"EXACT",value:"post"}},orderby:[{metric:{metricName:"screenPageViews"},desc:!0}],limit:15};return{report:E,reportLoaded:O,reportError:M,siteKitAudiencesReport:N,siteKitAudiencesReportLoaded:_,siteKitAudiencesReportError:D,totalPageviews:C,totalPageviewsReportLoaded:S,totalPageviewsReportError:w,topCitiesReport:R,topCitiesReportsLoaded:x,topCitiesReportErrors:P,topContentReport:B,topContentReportsLoaded:G,topContentReportErrors:W,topContentPageTitlesReport:Object(a.useInViewSelect)((function(e){return e(c.r).getReportForAllAudiences(Z,m)})),topContentPageTitlesReportsLoaded:u(Z,m),topContentPageTitlesReportErrors:d(Z,m)}}},363:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileNoData}));var i=n(2);function AudienceTileNoData(){return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__no-data"},Object(i.__)("No data to show yet","google-site-kit"))}}).call(this,n(3))},364:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PartialDataNotice}));var i=n(0),r=n.n(i);function PartialDataNotice(t){var n=t.content;return e.createElement("span",{className:"googlesitekit-audience-segmentation-partial-data-notice"},n)}PartialDataNotice.propTypes={content:r.a.node}}).call(this,n(3))},365:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTooltipMessage}));var i=n(0),r=n.n(i),a=n(1),o=n(42),c=n(2),l=n(4),s=n(21),u=n(13);function AudienceTooltipMessage(t){var n=t.audienceSlug,i=t.audienceName,r=Object(l.useSelect)((function(e){return e(u.c).getDocumentationLinkURL("visitor-group-insights")}));return Object(a.useMemo)((function(){switch(n){case"new-visitors":return Object(o.a)(Object(c.sprintf)(/* translators: %s: is the audience name */
Object(c.__)('%s are people who visited your site for the first time. Note that under some circumstances it\'s possible for a visitor to be counted in both the "new" and "returning" groups. <link>Learn more</link>',"google-site-kit"),"<strong>New visitors</strong>"),{strong:e.createElement("strong",null),link:e.createElement(s.a,{href:r,external:!0,hideExternalIndicator:!0})});case"returning-visitors":return Object(o.a)(Object(c.sprintf)(/* translators: %s: is the audience name */
Object(c.__)('%s are people who have visited your site at least once before. Note that under some circumstances it\'s possible for a visitor to be counted in both the "new" and "returning" groups. <link>Learn more</link>',"google-site-kit"),"<strong>Returning visitors</strong>"),{strong:e.createElement("strong",null),link:e.createElement(s.a,{href:r,external:!0,hideExternalIndicator:!0})});default:return Object(o.a)(Object(c.sprintf)(/* translators: %s: is the audience name */
Object(c.__)("%s is an audience that already exists in your Analytics property. Note that it's possible for a visitor to be counted in more than one group. <link>Learn more</link>","google-site-kit"),"<strong>".concat(i,"</strong>")),{strong:e.createElement("strong",null),link:e.createElement(s.a,{href:r,external:!0,hideExternalIndicator:!0})})}}),[n,i,r])}AudienceTooltipMessage.propTypes={audienceSlug:r.a.string.isRequired}}).call(this,n(3))},366:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M2.675 72.31a29.248 29.248 0 005.678 8.74c9.451 9.955 23.416 10.799 36.223 8.308a88.838 88.838 0 0035.776-15.752c6.09-4.513 12.104-10.113 20.167-10.363 3.027-.093 6.158.741 8.445 2.71 4.753 4.063 4.668 11.012 8.377 15.829 4.932 6.405 12.026 8.389 19.764 9.128 21.862 2.086 47.902-4.758 62.939-21.412 13.426-14.868 15.038-38.526-1.214-52.08-7.425-6.192-17.606-9.03-27.216-7.584-7.345 1.105-14.801 4.467-22.404 5.401-8.954 1.103-14.49-2.659-21.734-7.04C114.852.58 98.164-2.345 83.874 2.072 70.195 6.301 60.35 17.846 47.04 22.918c-11.502 4.385-25.089 3.717-35.082 10.86C.133 42.228-2.84 59.286 2.675 72.31z",fill:"#B8E6CA"}),o=i.createElement("path",{d:"M108.273 109c54.612 0 98.883-1.735 98.883-3.874 0-2.14-44.271-3.875-98.883-3.875-54.611 0-98.882 1.735-98.882 3.875 0 2.139 44.27 3.874 98.882 3.874z",fill:"#161B18",opacity:.1}),c=i.createElement("path",{d:"M108.273 109c54.612 0 98.883-1.735 98.883-3.874 0-2.14-44.271-3.875-98.883-3.875-54.611 0-98.882 1.735-98.882 3.875 0 2.139 44.27 3.874 98.882 3.874z",fill:"#CBD0D3"}),l=i.createElement("path",{d:"M134.765 53.225c-1.065 16.927-6.936 32.112-3.012 51.193h-4.468M139.814 104.418h-4.47l7.9-51.193",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),s=i.createElement("path",{d:"M120.504 36.651c-3.814 1.73-11.135 5.58-11.135 13.398M147.266 35.787c3.493 1.787 11.06 7.678 11.977 13.225",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),u=i.createElement("path",{d:"M151.555 75.952l-3.102.282-18.926 1.719-11.063 1.002-1.049-24.735-1.825-43.046 34.528-1.262.697 32.058.094 4.316.642 29.565.004.1z",fill:"#77AD8C"}),d=i.createElement("path",{d:"M148.453 76.234l1.78-.162 1.323-.12-1.439-66.042-1.983.091 1.281 63.2-24.976 2.127.862-19.15-7.844-1.074.86 20.438.107 2.465.041.946 11.063-1.002 18.927-1.719-.002.002z",fill:"#5C9271"}),g=i.createElement("path",{d:"M135.891 70.752c.032.916-.392-20.197-.629-27.044-6.628-3.008-13.797-3.559-20.67-1.228l.628 27.043c6.874-2.33 14.043-1.779 20.671 1.23z",fill:"#CBD0D3"}),f=i.createElement("path",{d:"M135.926 70.752c.01.916-.547-20.194-.629-27.044 6.481-3.306 13.617-4.182 20.592-2.166l.629 27.043c-6.975-2.015-14.111-1.139-20.592 2.167z",fill:"#EBEEF0"}),m=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M133.233 60.796c-.083-3.907-.202-9.298-.333-14.96l.196-.005a8403.731 8403.731 0 01.43 19.559l.023 1.18.004.262a.337.337 0 010 .046v.004l-.001.008a.179.179 0 01-.*************** 0 01-.1-.08l-.002-.007v-.004-.003l-.001-.02h.001v-.025l-.004-.26-.023-1.18-.097-4.598zm.126 6.063a.19.19 0 01.093-.08.19.19 0 01.097.073l-.19.007zm.194.01v-.001.001zM130.917 60.007c-.084-3.912-.203-9.29-.334-14.911l.197-.004a10934.46 10934.46 0 01.432 19.528l.024 1.198.004.271.001.05v.008l-.004.02c-.054.062-.17.035-.189-.002a.054.054 0 01-.003-.015l-.001-.005v-.018-.035l-.005-.27-.024-1.198c-.022-1.071-.055-2.66-.098-4.617zm.129 6.12l.193-.006-.001-.006c-.02-.037-.135-.063-.19-.002a.079.079 0 00-.002.014zM128.613 59.401c-.086-3.923-.206-9.285-.335-14.834l.197-.005a9889.942 9889.942 0 01.459 20.704l.005.284.001.055v.008l-.001.006a.179.179 0 01-.*************** 0 01-.1-.082l-.001-.007-.001-.017h.001l-.001-.047-.005-.283-.025-1.221-.1-4.648zm.131 6.199l.195-.007a.192.192 0 00-.1-.081.187.187 0 00-.095.086v.002zM126.303 58.977c-.087-3.938-.207-9.284-.334-14.744l.196-.005c.127 5.46.248 10.807.335 14.744l.102 4.688a412.921 412.921 0 01.032 1.545l.001.061v.012l-.001.009c-.005.02-.109.084-.191.017a.156.156 0 01-.004-.02v-.004-.002-.002l-.001-.007h.001v-.001l-.001-.06-.006-.296-.026-1.248-.103-4.688zm.136 6.292l.195-.007a.08.08 0 00-.004-.018c-.081-.066-.186-.003-.19.017l-.001.008zm.196-.002zM124.002 58.73c-.088-3.958-.209-9.292-.334-14.653l.196-.004a14128.617 14128.617 0 01.439 19.391l.028 1.28.006.312.002.068v.015l-.002.014c-.08.078-.19.014-.194-.004l-.001-.007v-.002-.007l.196-.006v-.005c-.005-.018-.114-.082-.194-.004a.157.157 0 00-.002.014v-.006l-.001-.066-.006-.312-.028-1.28-.105-4.738zM121.709 58.698c-.09-3.97-.211-9.281-.333-14.54l.196-.005a15730.945 15730.945 0 01.44 19.316l.029 1.306.006.327.002.074v.021a.191.191 0 01-.021.056.165.165 0 01-.175-.05v-.006l-.001-.004.197-.007-.001-.003a.163.163 0 00-.174-.05.193.193 0 00-.021.056v.003l-.001-.013-.001-.073-.007-.326-.028-1.307-.107-4.775zM119.414 58.855c-.091-3.986-.212-9.28-.332-14.44l.196-.005c.12 5.16.242 10.454.333 14.44l.109 4.818.03 1.336.**************.001.017v.006c0 .004-.098.096-.196.009l-.001-.007v-.002l.197-.006-.001-.006c-.098-.087-.196.005-.196.01v.001V65.43l-.002-.081-.007-.34-.03-1.336-.11-4.819zM116.797 44.873l.196-.004.489 21.033h-.01a271.267 271.267 0 01-.186.005l-.489-21.034z",fill:"#CBD0D3"}),p=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M133.317 67.22c-5.153-1.86-10.563-2.237-15.85-.939a.388.388 0 11-.188-.754c5.447-1.338 11.016-.948 16.306.963a.388.388 0 11-.268.73zM133.267 64.222c-5.159-1.867-10.574-2.245-15.866-.946a.388.388 0 11-.189-.754c5.454-1.34 11.027-.947 16.323.97a.387.387 0 01.234.498.392.392 0 01-.502.232zM133.204 61.217c-5.162-1.867-10.576-2.245-15.866-.946a.393.393 0 01-.475-.284.389.389 0 01.287-.47c5.45-1.339 11.023-.946 16.323.97a.388.388 0 01.234.498.393.393 0 01-.503.232zM133.134 58.212c-5.163-1.866-10.576-2.245-15.866-.946a.388.388 0 11-.189-.755c5.451-1.338 11.024-.945 16.323.97a.386.386 0 01.234.499.392.392 0 01-.502.232zM133.071 55.213c-5.163-1.872-10.584-2.252-15.881-.95a.388.388 0 11-.189-.754c5.459-1.342 11.038-.947 16.339.974a.388.388 0 01.234.498.393.393 0 01-.503.232zM133.001 52.208c-5.164-1.87-10.584-2.252-15.882-.95a.389.389 0 11-.188-.755c5.458-1.34 11.038-.945 16.339.975a.388.388 0 11-.269.73z",fill:"#B8BDB9"}),b=i.createElement("path",{d:"M116.886 44.87c5.378-1.32 10.878-.934 16.11.963l.054 2.999c-5.227-1.891-10.721-2.276-16.094-.957l-.07-3.004z",fill:"#B8BDB9"}),v=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M117.284 45.175l.051 2.211c5.118-1.161 10.331-.806 15.312.893l-.039-2.17c-4.989-1.759-10.21-2.127-15.324-.934zm-.494-.68c5.459-1.342 11.038-.948 16.339.974l.253.091.069 3.832-.536-.194c-5.159-1.867-10.574-2.245-15.866-.945l-.475.116-.089-3.8.305-.075z",fill:"#B8BDB9"}),h=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M137.87 60.692a7073.26 7073.26 0 01-.362-14.961l.196-.005a8741.186 8741.186 0 00.48 19.558 754.986 754.986 0 00.04 1.44v.025h.001l.001.02-.001.003v.012a.187.187 0 01-.*************** 0 01-.097-.08l-.002-.008v-.003l-.002-.046-.008-.262-.032-1.18c-.028-1.06-.068-2.644-.117-4.597zm.352 6.057a.189.189 0 00-.096-.076.184.184 0 00-.093.078l.189-.002zm-.193.018v0zM140.148 59.798c-.098-3.912-.228-9.29-.359-14.91l.196-.005a10306.992 10306.992 0 00.507 20.724l.008.27.001.036h.001v.023a.13.13 0 01-.002.015c-.018.038-.132.07-.189.01l-.005-.02v-.004-.004l-.002-.05a1047.874 1047.874 0 01-.04-1.47c-.027-1.07-.068-2.659-.116-4.615zm.352 6.115l-.003-.014c-.057-.06-.172-.027-.189.01l-.001.006.193-.002zM142.426 59.087c-.097-3.922-.227-9.284-.356-14.834l.197-.004c.129 5.55.258 10.911.355 14.834l.116 4.647.032 1.22.008.284.002.047v.024a.185.185 0 01-.*************** 0 01-.099-.082l-.001-.006v-.008l-.002-.055a22.176 22.176 0 01-.008-.284l-.032-1.22-.116-4.648zm.353 6.194v-.002a.187.187 0 00-.099-.082.18.18 0 00-.095.086l.194-.002zM144.711 58.559c-.096-3.937-.225-9.283-.352-14.744l.197-.004c.127 5.46.255 10.806.351 14.743l.115 4.688.032 1.247.008.297.002.059v.001h.001V64.857l-.001.004a.121.121 0 01-.003.02c-.078.07-.186.012-.191-.008l-.002-.01v-.004-.007l-.002-.06-.008-.298-.032-1.248-.115-4.687zm.353 6.287l-.001-.008c-.006-.02-.113-.078-.191-.008l-.004.018.196-.002zm-.196.007c0 .001 0 0 0 0z",fill:"#EBEEF0"}),j=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M137.979 66.391c5.195-2.149 10.74-2.792 16.244-1.703a.389.389 0 11-.153.762c-5.342-1.057-10.728-.435-15.789 1.659a.394.394 0 01-.513-.21.388.388 0 01.211-.508zM137.889 63.393c5.201-2.155 10.751-2.8 16.261-1.71a.39.39 0 01.308.457.392.392 0 01-.461.305c-5.348-1.058-10.739-.434-15.806 1.665a.392.392 0 01-.512-.209.387.387 0 01.21-.508zM137.823 60.389c5.2-2.155 10.749-2.8 16.256-1.71a.388.388 0 11-.153.762c-5.345-1.057-10.735-.434-15.801 1.665a.394.394 0 01-.513-.21.388.388 0 01.211-.508zM137.749 57.383c5.2-2.154 10.748-2.8 16.256-1.71a.388.388 0 11-.154.762c-5.344-1.057-10.735-.433-15.8 1.665a.393.393 0 01-.513-.209.388.388 0 01.211-.508zM137.663 54.385c5.206-2.16 10.761-2.807 16.276-1.716a.389.389 0 11-.153.763c-5.352-1.06-10.75-.434-15.821 1.67a.394.394 0 01-.513-.208.388.388 0 01.211-.509zM137.596 51.38c5.206-2.16 10.762-2.808 16.277-1.716a.39.39 0 01.308.457.393.393 0 01-.462.305c-5.352-1.06-10.749-.432-15.82 1.67a.393.393 0 01-.513-.208.387.387 0 01.21-.508zM137.542 48.368c5.201-2.154 10.751-2.8 16.26-1.71a.389.389 0 11-.153.763c-5.346-1.058-10.739-.434-15.805 1.665a.393.393 0 01-.513-.21.388.388 0 01.211-.508zM137.456 45.37c5.206-2.16 10.761-2.808 16.276-1.716a.39.39 0 01.308.457.392.392 0 01-.461.306c-5.353-1.06-10.75-.434-15.821 1.67a.394.394 0 01-.513-.209.388.388 0 01.211-.508z",fill:"#CBD0D3"}),I=i.createElement("path",{d:"M137.608 45.729c5.139-2.133 10.618-2.768 16.049-1.693l.21 9.014c-5.434-1.075-10.91-.439-16.049 1.693l-.21-9.014z",fill:"#CBD0D3"}),y=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M138.005 45.986l.19 8.184c4.901-1.928 10.097-2.52 15.267-1.592l-.191-8.22c-5.162-.96-10.364-.356-15.266 1.628zm-.55-.616c5.206-2.16 10.765-2.807 16.277-1.716a.39.39 0 01.315.372l.21 9.015a.387.387 0 01-.14.307.394.394 0 01-.329.083c-5.352-1.06-10.749-.434-15.82 1.67a.395.395 0 01-.365-.032.387.387 0 01-.178-.317l-.21-9.015a.389.389 0 01.24-.367z",fill:"#CBD0D3"}),E=i.createElement("path",{d:"M159.24 49.011c.761 4.603-4.117 7.506-7.486 6.434M109.37 50.05c.001 4.55 5.159 7.83 8.838 6.226",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),O=i.createElement("path",{d:"M31.757 63.326l-5.175 13.74a22.86 22.86 0 00-.534 1.593c-1.686 5.718-.919 11.872 1.862 17.155l4.678 8.598h-4.473",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),M=i.createElement("path",{d:"M39.988 67.196l.768 37.221",stroke:"#161B18",strokeWidth:1.472,strokeMiterlimit:10,strokeLinecap:"round"}),k=i.createElement("path",{d:"M31.757 63.326a27.536 27.536 0 00-2.058 5.225",stroke:"#1967D2",strokeWidth:2.748,strokeLinejoin:"round"}),N=i.createElement("path",{d:"M47.8 52.642c4.738 2.161 9.71 4.53 10.766 11.237M13.781 61.718c-2.557 3.62-6.986 9.225-5.039 14.72",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),_=i.createElement("path",{d:"M1.535 51.315L54.34 35.316s5.107 29.415-22.04 34.95c-.009 0-18.561 4.185-30.764-18.952z",fill:"#70B2F5"}),D=i.createElement("path",{d:"M1.535 51.315L54.34 35.316s5.107 29.415-22.04 34.95c-.009 0-18.561 4.185-30.764-18.952z",fill:"#77AD8C"}),A=i.createElement("path",{d:"M33.856 67.557S16.353 71.503 4.163 50.519l-2.628.796C13.738 74.452 32.29 70.266 32.29 70.266c10.329-2.105 15.985-7.67 19.032-13.753-3.297 4.975-8.696 9.256-17.466 11.044z",fill:"#5C9271"}),T=i.createElement("path",{d:"M45.22 104.418h-4.47",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),S=i.createElement("path",{d:"M38.095 85.802c.172.861-3.494-19.049-4.776-25.49-6.736-1.845-13.604-1.284-19.746 1.959l4.776 25.49c6.143-3.243 13.01-3.804 19.746-1.96z",fill:"#CBD0D3"}),w=i.createElement("path",{d:"M38.129 85.796c.152.865-3.64-19.022-4.776-25.491 5.62-4.106 12.236-6.013 19.146-5.159l4.776 25.49c-6.91-.853-13.525 1.053-19.146 5.16z",fill:"#EBEEF0"}),C=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M34.042 76.784c-.683-3.684-1.629-8.766-2.629-14.104l.185-.033A7851.454 7851.454 0 0135.233 82.2a55.731 55.731 0 01.052.29v.011a.174.174 0 01-.*************** 0 01-.106-.06l-.003-.007v-.003l-.001-.003v-.001a3796.705 3796.705 0 00-1.057-5.736zm1.057 5.717a.18.18 0 01.075-.09c.*************.103.055l-.178.035zm.184-.02zM31.73 76.386A9015.12 9015.12 0 0029.11 62.33l.184-.034a9007.929 9007.929 0 013.637 19.54 80.445 80.445 0 01.055.307v.024c-.042.066-.155.058-.18.026a.162.162 0 01-.004-.013l-.001-.006a.528.528 0 01-.004-.016h.001l-.006-.034a4684.226 4684.226 0 00-1.06-5.738zm1.068 5.771l.181-.035a.058.058 0 00-.002-.005c-.024-.032-.137-.04-.178.026l-.001.014zM29.453 76.162c-.687-3.698-1.63-8.753-2.61-13.984l.185-.033a10403.315 10403.315 0 013.636 19.517 66.685 66.685 0 01.058.32v.008l.001.006a.176.176 0 01-.*************** 0 01-.107-.063l-.002-.006-.003-.016a4941.913 4941.913 0 00-1.082-5.845zm1.083 5.845l.183-.035a.181.181 0 00-.107-.063.176.176 0 00-.076.096v.002zM27.204 76.11c-.69-3.712-1.631-8.752-2.596-13.898l.185-.034a13167.584 13167.584 0 013.687 19.775l.01.057.002.007v.013c0 .02-.09.095-.177.045a.185.185 0 01-.008-.023v-.001-.002l-.002-.007a6801.028 6801.028 0 00-1.1-5.932zm1.101 5.932l.184-.035a.257.257 0 00-.006-.017c-.088-.05-.177.025-.178.045v.007zm.185-.03v-.001zM24.99 76.224c-.695-3.732-1.634-8.76-2.58-13.813l.184-.033a16040.945 16040.945 0 013.637 19.486l.055.294c.005.03.01.052.011.064a.39.39 0 01.003.028c-.063.086-.176.042-.183.025a.13.13 0 01-.002-.006v-.003l-.002-.005.184-.036v-.005c-.008-.016-.12-.06-.184.026V82.254a16.67 16.67 0 00-.066-.357l-.224-1.206-.832-4.467zM22.813 76.54c-.698-3.743-1.634-8.749-2.563-13.706l.185-.034a20172.54 20172.54 0 013.701 19.818l.003.014v.005a.19.19 0 01-.01.057c-.09.047-.172-.018-.174-.022v-.006l-.002-.002.185-.037-.001-.003c-.001-.004-.083-.069-.173-.021a.191.191 0 00-.01.059l-.003-.012-.012-.07a302.24 302.24 0 00-.057-.307l-.23-1.232-.839-4.501zM20.668 77.034l-2.547-13.61.184-.034a29372.29 29372.29 0 013.69 19.733l.015.076.003.017v.005c.001.004-.077.105-.183.038a.117.117 0 01-.001-.006v-.002l.184-.035-.002-.006c-.106-.067-.184.035-.183.038v.002-.002l-.003-.016-.014-.076-.06-.32-.235-1.26-.848-4.542zM16.023 64.202l.185-.034 3.715 19.826-.01.001-.165.03-.01.002-3.715-19.825z",fill:"#CBD0D3"}),z=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M35.112 82.85c-5.163-.983-10.34-.522-15.14 1.505a.377.377 0 01-.493-.197.37.37 0 01.198-.488c4.947-2.089 10.276-2.56 15.576-1.551a.373.373 0 01.298.436.375.375 0 01-.44.295zM34.601 80.02c-5.17-.987-10.35-.527-15.157 1.502a.377.377 0 01-.493-.197.37.37 0 01.198-.488c4.953-2.09 10.287-2.561 15.593-1.548a.373.373 0 01.299.436.375.375 0 01-.44.296zM34.078 77.187c-5.173-.986-10.353-.526-15.157 1.501a.377.377 0 01-.493-.197.37.37 0 01.198-.488c4.95-2.09 10.283-2.56 15.593-1.547a.373.373 0 11-.141.731zM33.547 74.355c-5.173-.986-10.354-.527-15.157 1.5a.377.377 0 01-.493-.196.371.371 0 01.198-.489c4.95-2.089 10.282-2.559 15.593-1.547a.373.373 0 01.298.436.375.375 0 01-.44.296zM33.023 71.527c-5.175-.991-10.362-.532-15.172 1.5a.377.377 0 01-.493-.197.37.37 0 01.198-.489c4.957-2.093 10.296-2.563 15.609-1.545a.373.373 0 11-.142.73zM32.492 68.695c-5.175-.99-10.362-.532-15.172 1.5a.377.377 0 01-.494-.198.37.37 0 01.198-.488c4.957-2.093 10.297-2.562 15.61-1.546a.373.373 0 11-.142.732z",fill:"#B8BDB9"}),R=i.createElement("path",{d:"M16.109 64.187c4.884-2.062 10.147-2.527 15.39-1.523l.515 2.83c-5.238-1-10.495-.535-15.374 1.525l-.531-2.832z",fill:"#B8BDB9"}),x=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.538 64.414l.39 2.084c4.663-1.872 9.65-2.323 14.625-1.468l-.373-2.047c-4.991-.91-9.988-.47-14.642 1.431zm-.572-.57c4.957-2.093 10.296-2.563 15.609-1.545l.253.048.658 3.614-.538-.102c-5.169-.987-10.35-.527-15.157 1.502l-.43.182-.672-3.582.277-.117z",fill:"#B8BDB9"}),P=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M38.41 75.985c-.698-3.682-1.656-8.762-2.656-14.1l.185-.033a7931.958 7931.958 0 003.477 18.43 592.343 592.343 0 00.26 1.357l.005.023h.001a.711.711 0 01.004.022v.011a.173.173 0 01-.*************** 0 01-.105-.06.16.16 0 01-.003-.01 3677.586 3677.586 0 01-1.092-5.733zm1.269 5.677a.182.182 0 00-.102-.057.181.181 0 00-.077.088l.179-.03zm-.18.046v0zM40.426 74.796c-.697-3.687-1.652-8.755-2.645-14.053l.185-.034a9325.56 9325.56 0 003.684 19.53l.049.255.006.034h.001a.53.53 0 01.003.016v.019c-.01.04-.113.087-.176.04a.164.164 0 01-.008-.023l-.001-.004-.01-.046-.049-.256c-.046-.242-.12-.624-.215-1.128a5105.31 5105.31 0 01-.824-4.35zm1.278 5.732a.194.194 0 00-.005-.013c-.063-.047-.166 0-.177.039v.005l.182-.031zM42.474 73.78c-.698-3.697-1.65-8.75-2.63-13.98l.184-.034a10585.806 10585.806 0 003.678 19.51l.05.266.01.044.003.016a.07.07 0 010 .006.176.176 0 01-.*************** 0 01-.106-.063l-.002-.006a2.407 2.407 0 01-.012-.06l-.051-.266-.219-1.15c-.194-1.023-.48-2.532-.828-4.38zm1.291 5.806v-.001a.178.178 0 00-.106-.063.178.178 0 00-.077.096l.183-.032zM44.553 72.934c-.7-3.71-1.647-8.748-2.612-13.895l.185-.034a13096.468 13096.468 0 003.722 19.768l.011.055v.001l.002.007v.027c-.062.078-.172.039-.181.021a.116.116 0 01-.005-.02l-.011-.056a264.523 264.523 0 01-.277-1.456l-.834-4.418zm1.306 5.895l-.002-.007c-.008-.017-.119-.057-.182.021v.018l.184-.032zm-.184.037z",fill:"#EBEEF0"}),L=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M39.398 81.36c4.582-2.818 9.729-4.263 15.104-*************.37.18.362.386a.374.374 0 01-.389.358c-5.217-.193-10.217 1.208-14.681 3.953a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM38.848 78.537c4.587-2.824 9.738-4.273 15.12-*************.369.18.361.386a.374.374 0 01-.389.358c-5.222-.193-10.227 1.211-14.695 3.962a.378.378 0 01-.518-.12.37.37 0 01.12-.513zM38.316 75.705c4.587-2.824 9.737-4.272 15.115-*************.37.18.362.386a.374.374 0 01-.389.358c-5.22-.193-10.223 1.211-14.69 3.962a.378.378 0 01-.518-.12.37.37 0 01.12-.513zM37.789 72.873c4.587-2.824 9.736-4.272 15.115-*************.37.18.362.386a.374.374 0 01-.389.358c-5.22-.193-10.223 1.211-14.691 3.962a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM37.243 70.05c4.591-2.83 9.747-4.282 15.134-*************.37.18.362.386a.374.374 0 01-.39.358c-5.227-.194-10.236 1.213-14.709 3.97a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM36.712 67.216c4.591-2.829 9.747-4.281 15.134-************.369.18.361.385a.374.374 0 01-.389.358c-5.227-.194-10.236 1.214-14.71 3.97a.378.378 0 01-.516-.12.37.37 0 01.12-.513zM36.195 64.376c4.588-2.824 9.74-4.273 15.12-4.074.207.008.37.181.362.386a.374.374 0 01-.39.359c-5.22-.194-10.226 1.21-14.695 3.961a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM35.65 61.552c4.59-2.83 9.747-4.281 15.133-*************.37.18.362.386a.374.374 0 01-.39.358c-5.227-.194-10.236 1.213-14.709 3.97a.378.378 0 01-.517-.12.37.37 0 01.12-.513z",fill:"#CBD0D3"}),B=i.createElement("path",{d:"M35.851 61.868c4.532-2.793 9.618-4.222 14.922-4.025l1.592 8.497c-5.307-.198-10.39 1.232-14.922 4.025l-1.592-8.497z",fill:"#CBD0D3"}),G=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M36.268 62.051l1.446 7.715c4.338-2.565 9.162-3.91 14.197-3.812l-1.452-7.749c-5.032-.127-9.86 1.23-14.191 3.846zm-.615-.5c4.591-2.83 9.75-4.28 15.134-4.08a.375.375 0 01.355.304l1.593 8.497a.37.37 0 01-.085.31.376.376 0 01-.298.13c-5.228-.195-10.237 1.212-14.71 3.97a.378.378 0 01-.568-.25l-1.592-8.496a.37.37 0 01.171-.384z",fill:"#CBD0D3"}),W=i.createElement("path",{d:"M58.565 63.879c.876 5.566-4.736 9.076-8.612 7.78M8.738 76.438c1.616 4.56 7.623 6.458 13.652 0",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),Z=i.createElement("path",{d:"M160.68 68.356c.934 6.676 1.531 14.409 0 20.996M171.536 72.568l-3.781 31.849h-4.47",stroke:"#161B18",strokeWidth:1.472,strokeMiterlimit:10,strokeLinecap:"round"}),V=i.createElement("path",{d:"M187.455 104.418h-4.471c.637-10.18 1.817-24.67 1.817-24.67",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),U=i.createElement("path",{d:"M199.766 66.904c2.35 3.645 6.395 13.017 4.381 17.69M161.004 59.99c-3.656 2.734-9.85 8.336-9.904 15.127",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),F=i.createElement("path",{d:"M161.004 59.99c-3.656 2.734-9.85 8.336-9.904 15.127-.045 5.634 4.35 10.804 12.101 6.915",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),H=i.createElement("path",{d:"M188.416 36.69c5.324 1.935 9.926 5.533 12.45 10.684 5.586 11.402.195 27.178-11.38 32.714-10.499 5.032-24.499 1.152-30.83-8.532-6.33-9.683-4.157-23.882 4.792-31.286 6.7-5.538 16.706-6.574 24.968-3.58z",fill:"#77AD8C"}),Y=i.createElement("path",{d:"M200.868 47.374a19.785 19.785 0 00-4.03-5.505c.638.885 1.2 1.822 1.68 2.8 5.587 11.402.196 27.177-11.38 32.714-9.081 4.35-20.778 2.028-27.868-4.939 6.584 9.03 20.044 12.517 30.218 7.648 11.568-5.54 16.966-21.316 11.38-32.718z",fill:"#5C9271"}),Q=i.createElement("path",{d:"M182.034 67.78c-2.79 3.71-7.987 4.925-11.944.505",stroke:"#161B18",strokeWidth:1.105,strokeMiterlimit:10,strokeLinecap:"round"}),X=i.createElement("path",{d:"M175.979 96.185c-.089.946 2.303-20.818 2.977-27.89-6.413-3.959-13.71-5.464-21.09-3.967l-2.976 27.888c7.38-1.496 14.677.009 21.089 3.969z",fill:"#CBD0D3"}),J=i.createElement("path",{d:"M176.015 96.189c-.112.943 2.142-20.835 2.976-27.89 7.107-2.55 14.561-2.518 21.463.468l-2.976 27.888c-6.902-2.985-14.356-3.017-21.463-.466z",fill:"#EBEEF0"}),K=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M174.58 85.6c.437-4.028 1.037-9.587 1.66-15.427l.203.022a8024.607 8024.607 0 01-2.178 20.167 469.931 469.931 0 01-.165 1.486l-.006.048-.001.004a.035.035 0 01-.*************** 0 01-.*************** 0 01-.093-.094v-.009-.003l.001-.003v-.001l.002-.02h.001l.003-.026.03-.268.135-1.217.517-4.74zm-.683 6.25a.196.196 0 01.106-.07.2.2 0 01.09.089l-.196-.018zm.198.037v-.002.002zM172.305 84.486c.438-4.034 1.035-9.579 1.654-15.376l.203.022a10437.849 10437.849 0 01-2.172 20.136l-.136 1.235-.031.28-.006.05-.001.005-.001.004-.007.02c-.063.057-.179.014-.194-.027l-.001-.015.001-.006.001-.018h.001l.004-.036.032-.28.135-1.234c.121-1.104.3-2.743.518-4.76zm-.688 6.31l.2.019-.001-.006c-.015-.04-.13-.083-.194-.028a.101.101 0 00-.005.015zM170.011 83.561c.437-4.044 1.033-9.574 1.643-15.297l.203.022a10697.615 10697.615 0 01-2.163 20.089l-.138 1.259-.033.292-.007.057-.001.008-.001.007a.196.196 0 01-.************* 0 01-.092-.097v-.007l.001-.018h.001l.006-.049.032-.291.138-1.258.52-4.793zm-.696 6.391l.201.019a.192.192 0 00-.091-.097.189.189 0 00-.109.077l-.001.001zM167.698 82.823c.438-4.06 1.03-9.573 1.631-15.204l.202.021a14091.811 14091.811 0 01-2.153 20.038l-.141 1.287-.033.306-.008.063-.001.007v.004a.05.05 0 01-.002.01c-.008.02-.124.072-.199-.008-.001-.008-.002-.019-.001-.021v-.004-.002-.002l.001-.007v-.001l.007-.061.034-.306.14-1.287c.124-1.136.304-2.803.523-4.833zm-.704 6.488l.202.018a.171.171 0 00-.002-.018c-.074-.08-.19-.028-.198-.008l-.002.008zm.202.024zM165.365 82.269c.44-4.083 1.03-9.583 1.62-15.111l.202.021a15535.246 15535.246 0 01-2.146 19.997l-.143 1.319-.035.322-.008.07-.002.015-.004.015c-.092.07-.196-.01-.198-.03v-.01l.001-.006.202.019v-.005c-.002-.02-.106-.1-.198-.03a.088.088 0 00-.004.014v.002l.001-.008.007-.068.036-.322.143-1.319.526-4.885zM163.009 81.934c.44-4.093 1.027-9.57 1.606-14.994l.202.021a31558.74 31558.74 0 01-2.135 19.918l-.146 1.348-.037.336-.008.077-.002.016-.001.006a.204.204 0 01-.029.055.17.17 0 01-.173-.075v-.007l.001-.003.202.019.001-.004c0-.005-.066-.097-.173-.075a.225.225 0 00-.03.055v.003l.001-.012.009-.076.037-.337.145-1.347.53-4.924zM160.626 81.796c.441-4.111 1.025-9.57 1.593-14.891l.203.02c-.568 5.322-1.153 10.781-1.593 14.892l-.533 4.968-.148 1.378-.038.35a8.768 8.768 0 01-.009.084l-.002.018-.001.006c0 .004-.113.086-.202-.016V88.596l.203.02v-.007c-.089-.102-.202-.02-.202-.016l-.001.002.001-.002.001-.017a8.44 8.44 0 00.009-.084l.038-.35.148-1.378.533-4.968zM159.809 67.077l.202.02-2.315 21.692h-.01l-.182-.02h-.01l2.315-21.692z",fill:"#CBD0D3"}),q=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M173.801 92.217c-5.049-2.588-10.56-3.683-16.171-3.04a.406.406 0 01-.45-.354.405.405 0 01.358-.447c5.78-.662 11.454.468 16.637 3.125a.401.401 0 01.174.543.408.408 0 01-.548.173zM174.149 89.126c-5.054-2.595-10.57-3.692-16.187-3.048a.406.406 0 01-.45-.354.405.405 0 01.358-.447c5.787-.663 11.465.47 16.654 3.134a.401.401 0 01.173.543.408.408 0 01-.548.172zM174.49 86.028c-5.059-2.594-10.575-3.691-16.188-3.048a.405.405 0 01-.45-.354.405.405 0 01.358-.447c5.783-.662 11.461.47 16.653 3.134a.4.4 0 01.174.543.407.407 0 01-.547.172zM174.822 82.93c-5.059-2.595-10.575-3.692-16.188-3.049a.405.405 0 01-.45-.354.405.405 0 01.358-.446c5.783-.663 11.461.47 16.653 3.133a.4.4 0 01.174.543.407.407 0 01-.547.173zM175.157 79.838c-5.059-2.6-10.581-3.7-16.202-3.055a.404.404 0 11-.093-.8c5.792-.665 11.476.47 16.669 3.14a.4.4 0 01.174.543.409.409 0 01-.548.172zM175.485 76.739c-5.059-2.6-10.581-3.7-16.202-3.056a.404.404 0 11-.093-.8c5.792-.665 11.476.472 16.67 3.14a.401.401 0 01.173.543.408.408 0 01-.548.173z",fill:"#B8BDB9"}),$=i.createElement("path",{d:"M159.902 67.086c5.707-.655 11.31.463 16.436 3.098l-.346 3.09c-5.121-2.628-10.719-3.743-16.42-3.09l.33-3.098z",fill:"#B8BDB9"}),ee=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M160.27 67.451l-.243 2.28c5.417-.524 10.73.523 15.624 2.922l.251-2.237c-4.894-2.461-10.214-3.522-15.632-2.965zm-.416-.765c5.792-.665 11.476.47 16.67 3.14l.247.127-.442 3.949-.526-.27c-5.054-2.594-10.571-3.691-16.187-3.047l-.504.057.419-3.919.323-.037z",fill:"#B8BDB9"}),te=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M179.36 86.1c.423-4.031 1.009-9.591 1.632-15.431l.203.021c-.623 5.84-1.21 11.4-1.632 15.43-.211 2.015-.382 3.648-.495 4.742l-.125 1.218-.027.269-.002.025h.001a.222.222 0 01-.003.02l-.001.004v.004a.035.035 0 01-.*************** 0 01-.************** 0 01-.09-.094v-.01-.002l.004-.049.027-.27.125-1.217.495-4.743zm-.449 6.274a.193.193 0 00-.088-.09.197.197 0 00-.107.067l.195.023zm-.201-.007v0zM181.821 85.478c.424-4.035 1.01-9.582 1.628-15.378l.203.02a9452.743 9452.743 0 00-2.255 21.376l-.029.28a.595.595 0 01-.003.036h.001a.189.189 0 01-.003.018l-.001.006-.004.015c-.023.037-.145.055-.195-.013-.002-.008-.002-.02-.002-.022v-.004-.004l.005-.052.028-.28.128-1.236.499-4.762zm-.457 6.334a.084.084 0 00-.002-.015c-.051-.068-.172-.05-.196-.013l-.001.005.199.023zM184.261 85.046c.426-4.045 1.011-9.576 1.622-15.299l.202.021c-.61 5.723-1.195 11.254-1.621 15.3-.213 2.022-.387 3.674-.503 4.793l-.131 1.26-.03.291-.005.05h.001l-.002.017a.024.024 0 01-.************* 0 01-.************** 0 01-.091-.097l.001-.007v-.008l.005-.057.03-.293.131-1.26.503-4.794zm-.466 6.415v-.002a.189.189 0 00-.09-.097.193.193 0 00-.11.076l.2.023zM186.679 84.801c.429-4.06 1.013-9.574 1.614-15.205l.202.02a14313.317 14313.317 0 00-2.288 21.634l-.007.061v.001h.001l-.001.008v.002l-.001.002v.003l-.006.021c-.09.062-.193-.012-.196-.033v-.01-.005l.001-.007.006-.063.031-.306.135-1.288.509-4.835zm-.479 6.511v-.008c-.003-.02-.106-.095-.195-.033l-.006.018.201.023zm-.202-.018z",fill:"#EBEEF0"}),ne=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M178.707 91.973c5.63-1.53 11.417-1.465 16.931.375.212.07.327.3.255.51a.408.408 0 01-.515.254c-5.351-1.786-10.973-1.852-16.457-.361a.407.407 0 01-.499-.282.403.403 0 01.285-.496zM179.019 88.88c5.636-1.536 11.429-1.473 16.948.368a.403.403 0 11-.259.764c-5.357-1.787-10.984-1.851-16.475-.356a.403.403 0 11-.214-.777zM179.351 85.78c5.635-1.534 11.427-1.471 16.943.369a.403.403 0 11-.259.764c-5.354-1.786-10.98-1.85-16.47-.356a.403.403 0 11-.214-.777zM179.683 82.682c5.635-1.535 11.427-1.472 16.943.368a.403.403 0 11-.259.764c-5.354-1.786-10.98-1.85-16.469-.355a.403.403 0 11-.215-.777zM179.994 79.588c5.643-1.54 11.442-1.479 16.966.365.213.071.327.3.255.51a.407.407 0 01-.515.254c-5.361-1.79-10.994-1.853-16.49-.352a.403.403 0 11-.216-.777zM180.323 76.489c5.642-1.54 11.441-1.479 16.965.365.213.072.327.3.255.51a.407.407 0 01-.515.254c-5.361-1.79-10.994-1.852-16.49-.352a.403.403 0 11-.215-.777zM180.671 73.385c5.637-1.534 11.43-1.471 16.949.37.212.071.327.3.255.51a.407.407 0 01-.515.254c-5.356-1.787-10.984-1.852-16.474-.357a.402.402 0 11-.215-.777zM180.987 70.291c5.642-1.54 11.441-1.478 16.965.366.213.071.327.3.255.51a.406.406 0 01-.514.254c-5.362-1.79-10.995-1.853-16.491-.353a.403.403 0 11-.215-.777z",fill:"#CBD0D3"}),ie=i.createElement("path",{d:"M181.096 70.68c5.569-1.52 11.288-1.457 16.728.358l-.992 9.297c-5.443-1.817-11.159-1.88-16.728-.36l.992-9.296z",fill:"#CBD0D3"}),re=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M181.469 70.996l-.901 8.44c5.297-1.34 10.719-1.27 15.911.361l.905-8.477c-5.179-1.662-10.609-1.722-15.915-.323zm-.482-.705c5.642-1.54 11.444-1.477 16.965.366.18.06.294.236.274.424l-.992 9.296a.404.404 0 01-.534.34c-5.361-1.79-10.994-1.853-16.49-.352a.409.409 0 01-.371-.082.402.402 0 01-.141-.349l.992-9.296a.404.404 0 01.297-.347z",fill:"#CBD0D3"}),ae=i.createElement("path",{d:"M204.146 84.595c-1.671 3.879-7.751 2.74-10.354-.297M151.096 75.116c-.045 5.635 4.349 10.805 12.1 6.915",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),oe=i.createElement("path",{d:"M91.429 81.618c-.729 8.2-.457 15.965 1.975 22.796h4.47M80.227 81.238c-.76 8.178-.245 15.966 2.153 23.178h-4.478",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),ce=i.createElement("path",{d:"M118.418 79.006c7.32 3.89 10.2 8.445 9.473 12.335M60.719 71.227c-7.51 3.313-11.627 6.373-11.627 13.398",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),le=i.createElement("path",{d:"M57.84 49.763l-3.38 33.985 68.342 6.303 1.443-32.81-66.405-7.478z",fill:"#77AD8C"}),se=i.createElement("path",{d:"M57.476 82.268l2.638-32.25-2.273-.255-3.38 33.985 68.341 6.303.127-2.876-65.453-4.907z",fill:"#5C9271"}),ue=i.createElement("path",{d:"M80.625 72.749c3.832 4.721 11.357 6.736 17.468 1.896",stroke:"#161B18",strokeWidth:1.105,strokeMiterlimit:10,strokeLinecap:"round"}),de=i.createElement("path",{d:"M86.74 98.236c-.1.945 2.55-20.79 3.308-27.852-6.365-4.035-13.644-5.624-21.041-4.213L65.7 94.023c7.397-1.41 14.676.179 21.04 4.213z",fill:"#CBD0D3"}),ge=i.createElement("path",{d:"M86.776 98.24c-.123.942 2.39-20.808 3.308-27.852 7.137-2.468 14.59-2.35 21.455.717l-3.307 27.851c-6.865-3.065-14.319-3.184-21.456-.716z",fill:"#EBEEF0"}),fe=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M85.467 87.637c.485-4.024 1.151-9.575 1.844-15.407l.202.023a8546.627 8546.627 0 01-2.418 20.141 653.021 653.021 0 01-.192 1.544.19.19 0 01-.*************** 0 01-.091-.095v-.009-.003-.005l.002-.02h.001l.004-.025.033-.268.15-1.215.573-4.733zm-.757 6.242a.197.197 0 01.106-.069c.*************.089.09l-.195-.021zm.197.038v-.001.001zM83.205 86.496c.485-4.028 1.149-9.566 1.836-15.355l.202.023a9660.973 9660.973 0 01-2.561 21.343 80.824 80.824 0 01-.042.335l-.001.004a.187.187 0 01-.007.02c-.065.056-.18.012-.194-.03a.164.164 0 010-.02c0-.005 0-.011.002-.019l.005-.036.035-.278.15-1.233.575-4.754zm-.763 6.302l.199.02v-.005c-.015-.041-.13-.085-.194-.03a.183.183 0 00-.005.015zM80.927 85.544c.485-4.04 1.146-9.561 1.825-15.277l.202.023A11320.95 11320.95 0 0180.4 91.61a113.556 113.556 0 01-.************ 0 01-.*************** 0 01-.************ 0 01-.09-.1.14.14 0 01.001-.023l.007-.05.036-.29.153-1.257.577-4.786zm-.772 6.383l.201.02a.193.193 0 00-.09-.097.194.194 0 00-.11.075v.002zM78.619 84.779c.486-4.055 1.144-9.56 1.811-15.184l.203.024a14137.83 14137.83 0 01-2.593 21.664l-.001.007a.093.093 0 01-.003.014c-.008.02-.124.07-.198-.01a.187.187 0 01-.002-.025v-.002-.001l.001-.008.001-.001.008-.06.037-.306.156-1.285.58-4.827zm-.781 6.48l.202.02-.002-.019c-.074-.08-.19-.03-.198-.01l-.002.008zm.201.026zM76.295 84.198c.488-4.077 1.144-9.57 1.8-15.091l.201.023a17204.58 17204.58 0 01-2.59 21.68l-.002.015-.004.014c-.093.07-.196-.012-.198-.032V90.798v-.007l.203.022v-.005c-.001-.02-.104-.101-.198-.033a.244.244 0 00-.004.015v.001l.001-.007.009-.069c.008-.067.02-.174.039-.32l.158-1.318c.14-1.157.341-2.84.585-4.88zM73.94 83.836c.489-4.088 1.14-9.558 1.784-14.975l.202.024a21886.98 21886.98 0 01-2.584 21.65l-.002.017v.005a.206.206 0 01-.03.055.17.17 0 01-.172-.077v-.01l.203.021v-.003a.17.17 0 00-.172-.077.208.208 0 00-.03.054v.004l.001-.013.01-.076.04-.336.162-1.346.588-4.917zM71.563 83.67l1.77-14.872.201.024a33930.12 33930.12 0 01-2.568 21.56l-.01.083-.002.018v.006c-.001.004-.115.084-.203-.019V90.461l.203.022v-.006c-.088-.103-.201-.023-.202-.019v.002-.001l.002-.018.01-.083.042-.35.165-1.376.592-4.962zM70.918 68.942l.202.023-2.572 21.664-.01-.001a70.908 70.908 0 01-.182-.022h-.01l2.572-21.664z",fill:"#CBD0D3"}),me=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M84.613 94.243c-5.017-2.647-10.516-3.805-16.133-3.227a.405.405 0 01-.446-.36.404.404 0 01.362-.442c5.789-.596 11.448.6 16.6 3.318a.401.401 0 01.167.545.408.408 0 01-.55.166zM84.997 91.157c-5.023-2.653-10.527-3.815-16.15-3.236a.405.405 0 01-.446-.36.404.404 0 01.363-.442c5.794-.596 11.459.603 16.615 3.327a.4.4 0 01.167.545.408.408 0 01-.55.166zM85.372 88.063c-5.028-2.653-10.53-3.814-16.15-3.236a.405.405 0 01-.446-.36.404.404 0 01.363-.442c5.791-.595 11.455.604 16.615 3.327a.4.4 0 01.167.545.408.408 0 01-.55.166zM85.739 84.969c-5.027-2.653-10.53-3.815-16.15-3.237a.405.405 0 01-.446-.359.404.404 0 01.363-.442c5.791-.596 11.455.603 16.615 3.326a.401.401 0 01.168.545.408.408 0 01-.55.167zM86.113 81.88c-5.027-2.658-10.536-3.822-16.165-3.243a.405.405 0 01-.445-.359.404.404 0 01.362-.442c5.8-.598 11.47.604 16.631 3.333a.401.401 0 01.167.546.408.408 0 01-.55.165zM86.477 78.786c-5.028-2.658-10.537-3.823-16.165-3.243a.405.405 0 01-.446-.36.404.404 0 01.362-.442c5.8-.597 11.47.606 16.632 3.334a.401.401 0 01.167.545.408.408 0 01-.55.166z",fill:"#B8BDB9"}),pe=i.createElement("path",{d:"M71.012 68.953c5.714-.588 11.303.594 16.398 3.288l-.383 3.087c-5.09-2.688-10.674-3.868-16.383-3.28l.368-3.095z",fill:"#B8BDB9"}),be=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M71.377 69.322l-.27 2.277c5.424-.461 10.724.647 15.589 3.103l.277-2.234c-4.864-2.518-10.171-3.64-15.596-3.146zm-.406-.77c5.8-.597 11.47.604 16.63 3.334l.247.13-.49 3.943-.522-.276c-5.023-2.652-10.526-3.814-16.15-3.235l-.504.052.465-3.915.323-.033z",fill:"#B8BDB9"}),ve=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M90.243 88.191c.47-4.025 1.123-9.578 1.816-15.41l.202.023a8576.285 8576.285 0 00-2.367 20.146 755.542 755.542 0 00-.172 1.51h.001a.8.8 0 01-.003.02v.006l-.001.002a.145.145 0 01-.************* 0 01-.************* 0 01-.089-.096v-.008-.004l.005-.047.03-.27.14-1.216.551-4.737zm-.523 6.269a.194.194 0 00-.088-.091.197.197 0 00-.107.066l.195.025zm-.201-.01zM92.712 87.598c.472-4.03 1.124-9.57 1.811-15.359l.203.024a9910.386 9910.386 0 00-2.51 21.348l-.031.279-.004.036c0 .008 0 .014-.002.018v.006l-.005.015c-.024.036-.146.053-.195-.016a.181.181 0 01-.002-.021v-.005-.004l.006-.051.031-.28.143-1.234.555-4.756zm-.532 6.328c0-.004 0-.01-.002-.015-.05-.069-.171-.052-.195-.016l-.002.006.199.025zM95.153 87.195c.475-4.041 1.125-9.564 1.804-15.28l.202.023a11129.421 11129.421 0 00-2.509 21.325 89.368 89.368 0 00-.038.34.504.504 0 01-.002.018l-.002.007a.192.192 0 01-.************* 0 01-.09-.098v-.007l.001-.008.007-.057.033-.293a5635.92 5635.92 0 01.706-6.046zm-.542 6.409v-.002c0-.007-.03-.07-.09-.099a.194.194 0 00-.11.075l.2.026zM97.576 86.978c.478-4.056 1.127-9.562 1.795-15.186l.202.023a13876.904 13876.904 0 00-2.546 21.606l-.007.06v.013l-.001.004a.203.203 0 01-.007.02c-.09.062-.192-.014-.195-.035v-.01-.004l.001-.008.007-.063.035-.306.15-1.285.566-4.83zm-.556 6.505v-.008c-.003-.021-.104-.097-.195-.036a.18.18 0 00-.006.018l.201.026zm-.202-.02z",fill:"#EBEEF0"}),he=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M89.522 94.057c5.648-1.465 11.434-1.333 16.925.57a.401.401 0 01.249.514.407.407 0 01-.517.247c-5.33-1.848-10.95-1.978-16.452-.552a.403.403 0 11-.205-.78zM89.865 90.966c5.655-1.47 11.446-1.34 16.943.566a.4.4 0 01.249.514.407.407 0 01-.517.247c-5.336-1.85-10.961-1.979-16.47-.547a.403.403 0 11-.206-.78zM90.236 87.872c5.654-1.47 11.444-1.34 16.938.565a.4.4 0 01.249.513.407.407 0 01-.517.248c-5.333-1.849-10.957-1.978-16.464-.547a.403.403 0 11-.205-.78zM90.603 84.776c5.654-1.469 11.444-1.339 16.939.566a.402.402 0 11-.269.76c-5.332-1.848-10.957-1.977-16.464-.546a.403.403 0 11-.206-.78zM90.954 81.687c5.66-1.474 11.458-1.346 16.96.563a.402.402 0 01.249.513.408.408 0 01-.518.247c-5.339-1.852-10.971-1.98-16.485-.544a.403.403 0 11-.206-.78zM91.317 78.592c5.66-1.473 11.458-1.346 16.961.563a.402.402 0 11-.269.76c-5.34-1.852-10.972-1.979-16.486-.544a.403.403 0 11-.206-.78zM91.705 75.492c5.654-1.469 11.447-1.339 16.943.567a.402.402 0 01.249.513.407.407 0 01-.518.247c-5.334-1.849-10.96-1.978-16.469-.548a.403.403 0 11-.205-.78zM92.056 72.402c5.66-1.474 11.458-1.345 16.96.563a.402.402 0 01.249.513.407.407 0 01-.518.248c-5.34-1.852-10.972-1.98-16.486-.544a.403.403 0 11-.206-.78z",fill:"#CBD0D3"}),je=i.createElement("path",{d:"M92.158 72.792c5.587-1.455 11.305-1.326 16.723.553l-1.103 9.284c-5.421-1.88-11.136-2.008-16.723-.553l1.103-9.284z",fill:"#CBD0D3"}),Ie=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M92.53 73.113l-1.002 8.43c5.313-1.28 10.734-1.147 15.906.545l1.005-8.467c-5.159-1.721-10.587-1.844-15.91-.508zm-.475-.71c5.66-1.475 11.461-1.345 16.961.562.179.062.291.24.269.427l-1.103 9.285a.4.4 0 01-.188.294.41.41 0 01-.35.039c-5.339-1.852-10.971-1.98-16.485-.544a.408.408 0 01-.37-.086.4.4 0 01-.136-.35l1.102-9.285c.02-.164.139-.3.3-.343z",fill:"#CBD0D3"}),ye=i.createElement("path",{d:"M127.892 91.34c-1.329 7.115-12.918 8.843-24.256 0M49.088 84.625c0 6.05 9.181 11.081 24.545 3.457",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"});t.a=function SvgNoAudienceBannerGraphic(e){return i.createElement("svg",r({viewBox:"0 0 211 109",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,b,v,h,j,I,y,E,O,M,k,N,_,D,A,T,S,w,C,z,R,x,P,L,B,G,W,Z,V,U,F,H,Y,Q,X,J,K,q,$,ee,te,ne,ie,re,ae,oe,ce,le,se,ue,de,ge,fe,me,pe,be,ve,he,je,Ie,ye)}},367:function(e,t,n){"use strict";(function(e){var i=n(10),r=n.n(i),a=n(0),o=n.n(a),c=n(1),l=Object(c.forwardRef)((function(t,n){var i=t.className,a=t.children,o=t.Icon,c=t.SVGGraphic;return e.createElement("div",{ref:n,className:r()("googlesitekit-lean-cta-banner",i)},e.createElement("div",{className:"googlesitekit-lean-cta-banner__body"},o&&e.createElement("div",{className:"googlesitekit-lean-cta-banner__body-icon"},e.createElement(o,{width:"32",height:"32"})),e.createElement("div",{className:"googlesitekit-lean-cta-banner__body-content"},a)),c&&e.createElement("div",{className:"googlesitekit-lean-cta-banner__graphic"},e.createElement(c,null)))}));l.propTypes={className:o.a.string,children:o.a.node.isRequired,Icon:o.a.elementType,SVGGraphic:o.a.elementType},t.a=l}).call(this,n(3))},368:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M2.956 80.014a32.365 32.365 0 006.283 9.673c10.459 11.015 25.911 11.949 40.083 9.193A98.307 98.307 0 0088.91 81.449c6.738-4.994 13.394-11.19 22.316-11.467 3.35-.103 6.814.82 9.345 2.998 5.259 4.497 5.165 12.186 9.269 17.516 5.458 7.088 13.308 9.283 21.87 10.101 24.191 2.309 53.006-5.265 69.646-23.694 14.857-16.452 16.64-42.63-1.343-57.629-8.216-6.852-19.483-9.992-30.117-8.392-8.127 1.223-16.378 4.942-24.791 5.977-9.908 1.22-16.033-2.943-24.05-7.79C127.086.641 108.62-2.597 92.807 2.292 77.671 6.972 66.777 19.747 52.048 25.36c-12.727 4.852-27.762 4.114-38.82 12.017C.143 46.727-3.146 65.603 2.956 80.014z",fill:"#F3F5F7"}),o=i.createElement("path",{d:"M118.945 116.194c32.642 0 59.104-1.654 59.104-3.694s-26.462-3.694-59.104-3.694c-32.643 0-59.105 1.654-59.105 3.694s26.462 3.694 59.105 3.694z",fill:"#161B18",opacity:.1}),c=i.createElement("path",{d:"M118.945 116.194c32.642 0 59.104-1.654 59.104-3.694s-26.462-3.694-59.104-3.694c-32.643 0-59.105 1.654-59.105 3.694s26.462 3.694 59.105 3.694z",fill:"#CBD0D3"}),l=i.createElement("path",{d:"M99.725 51.387c1.758 6.518 7.872 11.126 14.356 13.01 6.484 1.882 13.377 1.514 20.12 1.177 3.188-.158 6.449-.298 9.503.627 3.054.925 5.912 3.137 6.724 6.222.466 1.773.121 3.686-.787 5.274",stroke:"#161B18",strokeWidth:1.396,strokeLinecap:"round",strokeLinejoin:"round"}),s=i.createElement("path",{d:"M87.114 62.487c-1.015 16.075-6.61 30.497-2.87 48.618h-4.26M91.929 111.105h-4.261l7.53-48.618",stroke:"#161B18",strokeWidth:1.4,strokeLinecap:"round",strokeLinejoin:"round"}),u=i.createElement("path",{d:"M73.527 57.419c-3.635 1.642-10.613 5.299-10.613 12.724",stroke:"#000",strokeWidth:1.396,strokeLinecap:"round"}),d=i.createElement("path",{d:"M103.118 84.07l-2.957.269-18.04 1.632-10.545.952-1-23.491-1.74-40.88 32.912-1.199.664 30.445.09 4.099.612 28.078.004.095z",fill:"#CBD0D3"}),g=i.createElement("path",{d:"M100.163 84.338l1.697-.154 1.261-.114-1.371-62.719-1.891.087 1.017 59.457-29.439 2.786.103 2.34.04.9 10.544-.952 18.041-1.632-.002.001z",fill:"#999F9B"}),f=i.createElement("path",{d:"M62.912 70.143c0 4.321 4.917 7.437 8.424 5.913",stroke:"#000",strokeWidth:1.396,strokeLinecap:"round"}),m=i.createElement("path",{d:"M159.169 21.79l-22.985 89.068",stroke:"#7B807D",strokeWidth:3.607,strokeMiterlimit:10,strokeLinecap:"round"}),p=i.createElement("path",{d:"M157.57 14.896l-34.151 34.351a3.61 3.61 0 00.016 5.1 3.608 3.608 0 001.62.929l46.822 12.4a3.606 3.606 0 004.404-4.435l-12.674-46.745a3.599 3.599 0 00-2.557-2.542 3.605 3.605 0 00-3.48.942z",fill:"#E77D5B"}),b=i.createElement("path",{d:"M153.345 35.252l2.003-7.566 3.905 1.034-2.003 7.566-2.874 9.163-3.103-.822 2.072-9.375zm-2.709 18.123a2.77 2.77 0 01-1.715-1.274 2.768 2.768 0 01-.259-2.121c.197-.744.619-1.304 1.265-1.68a2.77 2.77 0 012.121-.259c.744.197 1.304.619 1.68 1.266.375.646.465 1.342.268 2.085a2.77 2.77 0 01-1.275 1.715c-.646.376-1.342.465-2.085.268z",fill:"#962C0A"}),v=i.createElement("path",{d:"M149.639 77.697a6.848 6.848 0 01-3.747 3.098c-3.335 1.14-7.399-.673-8.778-3.916",stroke:"#161B18",strokeWidth:1.396,strokeLinecap:"round",strokeLinejoin:"round"});t.a=function SvgAudienceSegmentationErrorFullWidth(e){return i.createElement("svg",r({viewBox:"0 0 233 117",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,b,v)}},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return j})),n.d(t,"c",(function(){return I})),n.d(t,"e",(function(){return y})),n.d(t,"b",(function(){return E}));var i=n(6),r=n.n(i),a=n(16),o=n.n(a),c=(n(27),n(9));function l(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}var u,d="googlesitekit_",g="".concat(d).concat("1.157.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),f=["sessionStorage","localStorage"],m=[].concat(f),p=function(){var t=o()(r.a.mark((function t(n){var i,a;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(i=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",i.setItem(a,a),i.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==i.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return v.apply(this,arguments)}function v(){return(v=o()(r.a.mark((function t(){var n,i,a;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=l(m),t.prev=3,n.s();case 5:if((i=n.n()).done){t.next=15;break}if(a=i.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(a);case 11:if(!t.sent){t.next=13;break}u=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(r.a.mark((function e(t){var n,i,a,o,c,l,s;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(n=e.sent)){e.next=10;break}if(!(i=n.getItem("".concat(g).concat(t)))){e.next=10;break}if(a=JSON.parse(i),o=a.timestamp,c=a.ttl,l=a.value,s=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:l,isError:s});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),j=function(){var t=o()(r.a.mark((function t(n,i){var a,o,l,s,u,d,f,m,p=arguments;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=p.length>2&&void 0!==p[2]?p[2]:{},o=a.ttl,l=void 0===o?c.b:o,s=a.timestamp,u=void 0===s?Math.round(Date.now()/1e3):s,d=a.isError,f=void 0!==d&&d,t.next=3,b();case 3:if(!(m=t.sent)){t.next=14;break}return t.prev=5,m.setItem("".concat(g).concat(n),JSON.stringify({timestamp:u,ttl:l,value:i,isError:f})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),I=function(){var t=o()(r.a.mark((function t(n){var i,a;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(i=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(d)?n:"".concat(g).concat(n),i.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),y=function(){var t=o()(r.a.mark((function t(){var n,i,a,o;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,i=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(d)&&i.push(o);return t.abrupt("return",i);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),E=function(){var e=o()(r.a.mark((function e(){var t,n,i,a;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,y();case 6:t=e.sent,n=l(t),e.prev=8,n.s();case 10:if((i=n.n()).done){e.next=16;break}return a=i.value,e.next=14,I(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},377:function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return ChipTabGroup}));var r,a=n(20),o=n.n(a),c=n(27),l=n.n(c),s=n(14),u=n.n(s),d=n(5),g=n.n(d),f=n(84),m=n(442),p=n(1),b=n(259),v=n(2),h=n(4),j=n(11),I=n(25),y=n(30),E=n(8),O=n(29),M=n(26),k=n(7),N=n(19),_=n(383),D=n(384),A=n(385),T=n(23),S=n(129),w=n(220),C=n(97),z=n(99),R=(r={},g()(r,I.c.SLUG,w.a),g()(r,I.g.SLUG,C.a),r);function ChipTabGroup(t){var n=t.allMetricItems,r=t.savedItemSlugs,a=Object(p.useRef)(),c=Object(p.useState)(I.c.SLUG),s=u()(c,2),d=s[0],w=s[1],C=Object(p.useState)(0),x=u()(C,2),P=x[0],L=x[1],B=Object(T.e)()===T.b,G=Object(h.useSelect)((function(e){return e(y.a).getValue(I.j,I.i)})),W=Object(h.useSelect)((function(e){return e(y.a).getValue(I.j,I.a)||[]})),Z=Object(h.useSelect)((function(e){return e(y.a).getValue(I.j,I.o)||[]})),V=Object(h.useSelect)((function(e){return e(k.a).isUserInputCompleted()})),U=Object(h.useSelect)((function(e){var t,n=e(k.a).getUserPickedMetrics();if(null==n?void 0:n.length){var i=e(E.r).getKeyMetricsConversionEventWidgets();return Object.keys(i).filter((function(e){return n.some((function(t){return i[e].includes(t)}))}))}var r=e(k.a).getUserInputSettings();return null==r||null===(t=r.includeConversionEvents)||void 0===t?void 0:t.values})),F=Object(h.useSelect)((function(e){return e(N.a).isModuleConnected(O.g)})),H=Object(h.useSelect)((function(e){return F?e(E.r).getDetectedEvents():[]})),Y=Object(h.useSelect)((function(e){return e(k.a).getAnswerBasedMetrics(null,[].concat(l()(U||[]),l()(H||[])))})),Q=[E.l.SUBMIT_LEAD_FORM,E.l.CONTACT,E.l.GENERATE_LEAD].filter((function(e){return(null==H?void 0:H.includes(e))||(null==U?void 0:U.includes(e))})),X=[E.l.ADD_TO_CART,E.l.PURCHASE].filter((function(e){return(null==H?void 0:H.includes(e))||(null==U?void 0:U.includes(e))})),J=Object(p.useMemo)((function(){return[I.h,I.d].concat(l()((null==Q?void 0:Q.length)?[I.e]:[]),l()((null==X?void 0:X.length)?[I.f]:[]),[I.b])}),[Q,X]),K=Object(p.useMemo)((function(){return V&&(null==Y?void 0:Y.length)?[I.c,I.g]:[I.c]}),[V,Y]),q=Object(p.useMemo)((function(){return[].concat(l()(K),l()(J))}),[K,J]),$=Object(h.useSelect)((function(e){if(!F)return[];var t=e(E.r).getNewBadgeEvents();if((null==H?void 0:H.length)&&(null==t?void 0:t.length)){var n=H.filter((function(e){return E.e.includes(e)})),i=t.filter((function(e){return E.e.includes(e)})),r=t.filter((function(e){return!E.e.includes(e)}));if((null==n?void 0:n.length)>1&&i.length>0)return r}return t})),ee=Object(h.useSelect)((function(e){return F?e(E.r).getKeyMetricsConversionEventWidgets():[]})),te=Object(p.useCallback)((function(){var e,t,n,i=null===(e=a.current)||void 0===e?void 0:e.querySelector(".mdc-tab-scroller__scroll-content");if(B){var r=null===(t=a.current)||void 0===t?void 0:t.querySelectorAll(".googlesitekit-chip-tab-group__tab-items .mdc-tab");if((null==r?void 0:r.length)&&i){var o=null===(n=a.current)||void 0===n?void 0:n.getBoundingClientRect(),c=[];r.forEach((function(e,t){var n=e.getBoundingClientRect();n.left>=o.left&&n.right<=o.right&&c.push(t)}));var l=r[c.length];if(l){var s=l.getBoundingClientRect();(s.left>=o.right||s.left-o.right<0&&-(s.left-o.right)<=20)&&("2px"===i.style.columnGap?i.style.columnGap="20px":i.style.columnGap="2px",te())}}}}),[B]),ne=g()({},I.c.SLUG,0),ie={},re={},ae=function(e){var t,i=n[e].group;if((i===d||d===I.c.SLUG&&W.includes(e))&&(ie[e]=n[e]),d===I.g.SLUG&&Y.includes(e)&&Y.includes(e)&&(ie[e]=n[e]),!ne[i]){var r=Object.keys(n).filter((function(e){return!(n[e].group!==i||!(null==G?void 0:G.includes(e)))})).length;ne[i]=r}(null==$?void 0:$.length)&&($.some((function(t){return ee[t].includes(e)}))&&(re[i]=[].concat(l()(null!==(t=re[i])&&void 0!==t?t:[]),[e])))};for(var oe in n)ae(oe);var ce=Object(h.useDispatch)(y.a).setValues,le=Object(p.useCallback)((function(){var e;ce(I.j,(e={},g()(e,I.i,G),g()(e,I.a,[].concat(l()(W),l()(Z))),g()(e,I.o,[]),e))}),[G,W,Z,ce]),se=Object(p.useCallback)((function(e,t){if(e)w(e);else{var n=q[t];L(t),w(n.SLUG)}Z.length&&le()}),[q,Z,w,le]),ue=Object(h.useSelect)((function(e){return e(M.b).getValue(I.k)})),de=Object(b.a)(ue),ge=Object.keys(re);Object(p.useEffect)((function(){if(!de&&ue)if(w(I.c.SLUG),L(0),ge.length&&B){var e=q.find((function(e){return e.SLUG===ge[0]}));L(q.indexOf(e)),w(e.SLUG)}else L(0),w(I.c.SLUG);de&&!ue&&le(),!de&&ue&&te()}),[ue,de,Z,q,B,ge,le,te]);var fe=Object(S.a)(te,50);Object(f.a)((function(){e.addEventListener("resize",fe)})),Object(m.a)((function(){return e.removeEventListener("resize",fe)}));var me=[[].concat(l()(K),l()(J.slice(0,2))),l()(J.slice(2))];return i.createElement("div",{className:"googlesitekit-chip-tab-group"},i.createElement("div",{className:"googlesitekit-chip-tab-group__tab-items",ref:a},!B&&me.map((function(e){return i.createElement("div",{key:"row-".concat(e[0].SLUG),className:"googlesitekit-chip-tab-group__tab-items-row"},e.map((function(e){return i.createElement(_.a,{key:e.SLUG,slug:e.SLUG,label:e.LABEL,hasNewBadge:!!(null==re?void 0:re[e.SLUG]),isActive:e.SLUG===d,onClick:se,selectedCount:ne[e.SLUG]})})))})),B&&i.createElement(j.TabBar,{activeIndex:P,handleActiveIndexUpdate:function(e){return se(null,e)}},q.map((function(e,t){var n=R[e.SLUG]||z.a;return i.createElement(j.Tab,{key:t,"aria-label":e.LABEL},i.createElement(n,{width:12,height:12,className:"googlesitekit-chip-tab-group__chip-item-svg googlesitekit-chip-tab-group__tab-item-mobile-svg googlesitekit-chip-tab-group__chip-item-svg__".concat(e.SLUG)}),e.LABEL,ne[e.SLUG]>0&&i.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-count"},"(",ne[e.SLUG],")"),!!(null==re?void 0:re[e.SLUG])&&i.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-new-dot"}))})))),i.createElement("div",{className:"googlesitekit-chip-tab-group__tab-item"},Object.keys(ie).map((function(e){var t,n=ie[e].group,a=null==re||null===(t=re[n])||void 0===t?void 0:t.includes(e);return i.createElement(D.a,o()({key:e,slug:e,savedItemSlugs:r,isNewlyDetected:a},ie[e]))})),!Object.keys(ie).length&&i.createElement("div",{className:"googlesitekit-chip-tab-group__graphic"},i.createElement(A.a,{height:250}),i.createElement("p",null,Object(v.__)("No metrics were selected yet","google-site-kit")))))}}).call(this,n(28),n(3))},378:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OverlayCard}));var i=n(5),r=n.n(i),a=n(22),o=n.n(a),c=n(598),l=n(0),s=n.n(l),u=n(10),d=n.n(u),g=n(23),f=n(379),m=n(148);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function OverlayCard(t){var n=t.visible,i=t.className,r=o()(t,["visible","className"]),a=Object(g.e)();if(!n)return null;var l=e.createElement("div",{className:d()("googlesitekit-overlay-card",i)},e.createElement(f.a,r));return a===g.b?l:e.createElement(c.a,{direction:"up",in:n},l)}OverlayCard.propTypes={className:s.a.string,title:s.a.string,description:s.a.oneOfType([s.a.string,s.a.object]),ctaButton:s.a.shape(b(b({},m.a),{},{href:s.a.string,target:s.a.string,trailingIcon:s.a.object})),dismissButton:s.a.shape(m.a),GraphicDesktop:s.a.elementType,GraphicMobile:s.a.elementType,visible:s.a.bool},OverlayCard.defaultProps={visible:!1}}).call(this,n(3))},379:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Body}));var i=n(5),r=n.n(i),a=n(0),o=n.n(a),c=n(1),l=n(23),s=n(380),u=n(381),d=n(148),g=n(382);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Body(t){var n=t.title,i=t.description,r=t.ctaButton,a=t.dismissButton,o=t.GraphicDesktop,f=t.GraphicMobile,m=Object(l.e)();return e.createElement(c.Fragment,null,m!==l.b&&o&&e.createElement("div",{className:"googlesitekit-overlay-card__graphic"},e.createElement(o,null)),e.createElement("div",{className:"googlesitekit-overlay-card__body"},n&&e.createElement(s.a,null,n),i&&e.createElement(u.a,null,i)),(r||a)&&e.createElement("div",{className:"googlesitekit-overlay-card__actions"},a&&e.createElement(d.b,a),r&&e.createElement(g.a,r)),m===l.b&&f&&e.createElement(f,null))}Body.propTypes={title:o.a.node,description:o.a.node,ctaButton:o.a.shape(m(m({},d.a),{},{href:o.a.string,target:o.a.string,trailingIcon:o.a.element})),dismissButton:o.a.shape(m({},d.a)),GraphicDesktop:o.a.elementType,GraphicMobile:o.a.elementType}}).call(this,n(3))},38:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i={NEW:"new",SUCCESS:"success",WARNING:"warning",INFO:"info",ERROR:"error"}},380:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Title}));var i=n(0),r=n.n(i);function Title(t){var n=t.children;return e.createElement("h3",{className:"googlesitekit-overlay-card__title"},n)}Title.propTypes={children:r.a.node.isRequired}}).call(this,n(3))},381:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var i=n(0),r=n.n(i);function Description(t){var n=t.children;return e.createElement("p",{className:"googlesitekit-overlay-card__description"},n)}Description.propTypes={children:r.a.node.isRequired}}).call(this,n(3))},382:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTAButton}));var i=n(5),r=n.n(i),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(11),u=n(148);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function CTAButton(t){var n=t.label,i=o()(t,["label"]);return e.createElement(s.Button,i,n)}CTAButton.propTypes=g(g({},u.a),{},{href:l.a.string,target:l.a.string,trailingIcon:l.a.object})}).call(this,n(3))},383:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Chip}));var i,r=n(5),a=n.n(r),o=n(0),c=n.n(o),l=n(10),s=n.n(l),u=n(11),d=n(25),g=n(220),f=n(97),m=n(99),p=(i={},a()(i,d.c.SLUG,g.a),a()(i,d.g.SLUG,f.a),i);function Chip(t){var n=t.slug,i=t.label,r=t.isActive,a=t.onClick,o=t.hasNewBadge,c=void 0!==o&&o,l=t.selectedCount,d=void 0===l?0:l,g=p[n]||m.a;return e.createElement(u.Button,{className:s()("googlesitekit-chip-tab-group__chip-item",{"googlesitekit-chip-tab-group__chip-item--active":r}),icon:e.createElement(g,{width:12,height:12,className:"googlesitekit-chip-tab-group__chip-item-svg googlesitekit-chip-tab-group__chip-item-svg__".concat(n)}),trailingIcon:d>0?e.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-count"},"(",d,")"):null,onClick:function(){return a(n)}},i,c&&e.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-new-dot"}))}Chip.propTypes={slug:c.a.string.isRequired,label:c.a.string.isRequired,isActive:c.a.bool,hasNewBadge:c.a.bool,selectedCount:c.a.number,onClick:c.a.func.isRequired}}).call(this,n(3))},384:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricItem}));var i=n(5),r=n.n(i),a=n(27),o=n.n(a),c=n(0),l=n.n(c),s=n(1),u=n(2),d=n(4),g=n(30),f=n(49),m=n(19),p=n(25),b=n(126);function MetricItem(t){var n=t.slug,i=t.title,a=t.description,c=t.isNewlyDetected,l=t.savedItemSlugs,v=void 0===l?[]:l,h=Object(d.useSelect)((function(e){var t=e(m.a).getModule,i=e(f.a).getWidget(n);return null==i?void 0:i.modules.reduce((function(e,n){var i=t(n);return(null==i?void 0:i.connected)||!(null==i?void 0:i.name)?e:[].concat(o()(e),[i.name])}),[])})),j=Object(d.useSelect)((function(e){return e(g.a).getValue(p.j,p.i)})),I=Object(d.useSelect)((function(e){return e(g.a)})).getValue,y=Object(d.useDispatch)(g.a).setValues,E=Object(s.useCallback)((function(e){var t,i=I(p.j,p.i),a=e.target.checked?i.concat([n]):i.filter((function(e){return e!==n}));y(p.j,(t={},r()(t,p.i,a),r()(t,p.o,a),t))}),[I,y,n]),O=null==j?void 0:j.includes(n),M=!v.includes(n)&&h.length>0,k="key-metric-selection-checkbox-".concat(n);return e.createElement(b.c,{id:k,slug:n,title:i,description:a,isNewlyDetected:c,isItemSelected:O,isItemDisabled:M,onCheckboxChange:E},h.length>0&&e.createElement("div",{className:"googlesitekit-selection-panel-item-error"},Object(u.sprintf)(/* translators: %s: module names. */
Object(u._n)("%s is disconnected, no data to show","%s are disconnected, no data to show",h.length,"google-site-kit"),h.join(Object(u.__)(" and ","google-site-kit")))))}MetricItem.propTypes={slug:l.a.string.isRequired,title:l.a.string.isRequired,description:l.a.string.isRequired,isNewlyDetected:l.a.bool,savedItemSlugs:l.a.array}}).call(this,n(3))},385:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M59.238 58.571c-2.136 20.178 4.272 29.099 20.48 53.216 16.209 24.118-29.092 62.914 5.475 101.268 33.827 37.532 69.419.009 111.314-4.555 29.443-3.208 57.819 12.98 90.86 5.9 33.04-7.08 46.385-42.599 43.153-68.059-5.59-44.041-26.24-49.107-34.893-66.461-8.654-17.354 2.902-52.997-30.287-73.16-33.19-20.163-76.71 14.42-112.503 12.37-20.651-1.182-40.932-4.995-59.264.86-18.53 5.918-32.662 22.571-34.335 38.621z",fill:"#B8E6CA"}),o=i.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter0_d_2200_11981)"},i.createElement("rect",{x:242.455,y:45.266,width:130.621,height:89.651,rx:10.957,transform:"rotate(15 242.455 45.266)",fill:"#fff"})),c=i.createElement("rect",{x:253.726,y:64.785,width:24.903,height:7.969,rx:3.985,transform:"rotate(15 253.726 64.785)",fill:"#EBEEF0"}),l=i.createElement("rect",{x:249.342,y:81.144,width:49.806,height:19.923,rx:9.961,transform:"rotate(15 249.342 81.144)",fill:"#FFDED3"}),s=i.createElement("rect",{x:240.436,y:114.357,width:99.428,height:8.773,rx:3.985,transform:"rotate(15 240.436 114.357)",fill:"#EBEEF0"}),u=i.createElement("path",{d:"M256.195 90.198l4.644 8.044m0 0l1.412-4.986m-1.412 4.986l-5.023-1.27",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),d=i.createElement("rect",{x:268.706,y:93.551,width:19.923,height:5.977,rx:1.992,transform:"rotate(15 268.706 93.55)",fill:"#fff"}),g=i.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter1_d_2200_11981)"},i.createElement("rect",{x:13.887,y:79.094,width:130.621,height:89.68,rx:10.957,transform:"rotate(-15 13.887 79.094)",fill:"#fff"})),f=i.createElement("rect",{x:32.989,y:90.122,width:62.386,height:7.798,rx:3.899,transform:"rotate(-15 32.99 90.122)",fill:"#EBEEF0"}),m=i.createElement("rect",{x:37.691,y:106.902,width:49.806,height:19.923,rx:9.961,transform:"rotate(-15 37.691 106.902)",fill:"#FFDED3"}),p=i.createElement("rect",{x:46.612,y:140.967,width:99.428,height:7.798,rx:3.899,transform:"rotate(-15 46.612 140.967)",fill:"#EBEEF0"}),b=i.createElement("path",{d:"M48.152 111.318l8.044 4.645m0 0l-1.27-5.024m1.27 5.024l-4.986 1.411",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),v=i.createElement("rect",{x:60.663,y:107.966,width:19.923,height:5.977,rx:1.992,transform:"rotate(-15 60.663 107.966)",fill:"#fff"}),h=i.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter2_d_2200_11981)"},i.createElement("rect",{x:126.251,y:37.4,width:130.621,height:89.68,rx:10.957,fill:"#fff"})),j=i.createElement("rect",{x:143.013,y:53.134,width:98.333,height:7.867,rx:3.933,fill:"#EBEEF0"}),I=i.createElement("rect",{x:142.369,y:70.423,width:49.806,height:19.923,rx:9.961,fill:"#B8E6CA"}),y=i.createElement("rect",{x:143.013,y:105.84,width:33.04,height:7.867,rx:3.933,fill:"#EBEEF0"}),E=i.createElement("path",{d:"M151.336 84.036l6.568-6.567m0 0l-5.182-.073m5.182.073l.073 5.18",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),O=i.createElement("rect",{x:164.287,y:77.395,width:19.923,height:5.977,rx:1.992,fill:"#fff"}),M=i.createElement("path",{d:"M59.237 58.571C57.1 78.75 63.509 87.67 79.717 111.787c16.209 24.118-29.091 62.914 5.475 101.268 33.827 37.532 69.419.009 111.314-4.555 29.444-3.208 57.82 12.98 90.86 5.9s46.385-42.599 43.153-68.059c-5.59-44.041-26.24-49.107-34.893-66.461-8.654-17.354 2.902-52.997-30.287-73.16-33.19-20.163-76.71 14.42-112.503 12.37-20.651-1.182-40.932-4.995-59.264.86C75.042 25.867 60.91 42.52 59.237 58.57z",fill:"#B8E6CA"}),k=i.createElement("g",{mask:"url(#key-metrics-no-selected-items_svg__a)"},i.createElement("path",{d:"M227.674 108.973l11.312-8.418M218.925 98.852l2.868-12.68M205.623 102.87l-5.375-13.037",stroke:"#CBD0D3",strokeWidth:3.147,strokeMiterlimit:10}),i.createElement("path",{d:"M63.953 190.487c16.127 12.193 38.716 10.349 55.335 5.162 16.618-5.187 31.107-14.61 45.314-23.791 6.717-4.337 13.617-8.738 21.496-11.119 7.878-2.381 17.057-2.39 22.958 1.658 3.392 2.328 5.205 5.923 5.36 9.702",stroke:"#3C7251",strokeWidth:9.44,strokeLinejoin:"round"}),i.createElement("path",{d:"M215.831 109.67l-19.169 71.73",stroke:"#CBD0D3",strokeWidth:9.44,strokeMiterlimit:10,strokeLinecap:"round"}),i.createElement("path",{d:"M213.975 116.472l-19.169 71.731",stroke:"#161B18",strokeWidth:9.44,strokeMiterlimit:10})),N=i.createElement("defs",null,i.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter0_d_2200_11981",x:205.773,y:35.772,width:176.33,height:147.36,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:3.985}),i.createElement("feGaussianBlur",{stdDeviation:7.969}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})),i.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter1_d_2200_11981",x:.409,y:35.793,width:176.337,height:147.388,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:3.985}),i.createElement("feGaussianBlur",{stdDeviation:7.969}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})),i.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter2_d_2200_11981",x:110.313,y:25.447,width:162.497,height:121.556,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:3.985}),i.createElement("feGaussianBlur",{stdDeviation:7.969}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})));t.a=function SvgKeyMetricsNoSelectedItems(e){return i.createElement("svg",r({viewBox:"0 0 383 238",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,b,v,h,j,I,y,E,O,i.createElement("mask",{id:"key-metrics-no-selected-items_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:58,y:0,width:273,height:230},M),k,N)}},386:function(e,t,n){"use strict";(function(e){var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(10),l=n.n(c),s=n(0),u=n.n(s),d=n(1),g=Object(d.forwardRef)((function(t,n){var i=t.children,a=t.href,c=t.text,s=t.className,u=t.danger,d=t.disabled,g=t.target,f=t["aria-label"],m=t.inverse,p=t.tertiary,b=t.callout,v=t.calloutStyle,h=o()(t,["children","href","text","className","danger","disabled","target","aria-label","inverse","tertiary","callout","calloutStyle"]),j=a&&!d?"a":"button";return e.createElement(j,r()({className:l()("mdc-button",s,{"mdc-button--raised":!c&&!p&&!b,"mdc-button--danger":u,"mdc-button--inverse":m,"mdc-button--tertiary":p,"mdc-button--callout":b,"mdc-button--callout-primary":b||"primary"===v,"mdc-button--callout-warning":"warning"===v,"mdc-button--callout-error":"error"===v}),href:d?void 0:a,ref:n,disabled:!!d,"aria-label":f,target:g||"_self",role:"a"===j?"button":void 0},h),i)}));g.propTypes={children:u.a.node,href:u.a.string,text:u.a.bool,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,target:u.a.string,"aria-label":u.a.string,inverse:u.a.bool,tertiary:u.a.bool,callout:u.a.bool,calloutStyle:u.a.oneOf(["primary","warning","error"])},t.a=g}).call(this,n(3))},387:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MaybeTooltip}));var i=n(0),r=n.n(i),a=n(360);function MaybeTooltip(t){var n=t.children,i=t.disabled,r=void 0!==i&&i,o=t.tooltip,c=void 0!==o&&o,l=t.tooltipTitle,s=void 0===l?null:l,u=t.hasIconOnly,d=void 0!==u&&u,g=t.tooltipEnterDelayInMS,f=void 0===g?100:g;return!r&&(c&&s||d&&s)?e.createElement(a.a,{title:s,enterDelay:f},n):n}MaybeTooltip.propTypes={children:r.a.node.isRequired,disabled:r.a.bool,tooltip:r.a.bool,tooltipTitle:r.a.oneOfType([r.a.string,r.a.element]),hasIconOnly:r.a.bool,tooltipEnterDelayInMS:r.a.number}}).call(this,n(3))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(24),r=n(18);function a(){var e=Object(r.a)();return i.g.includes(e)}},4:function(e,t){e.exports=googlesitekit.data},40:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return I})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return j}));var i=n(108),r=e._googlesitekitTrackingData||{},a=r.activeModules,o=void 0===a?[]:a,c=r.isSiteKitScreen,l=r.trackingEnabled,s=r.trackingID,u=r.referenceSiteURL,d=r.userIDHash,g=r.isAuthenticated,f={activeModules:o,trackingEnabled:l,trackingID:s,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:r.userRoles,isAuthenticated:g,pluginVersion:"1.157.0"},m=Object(i.a)(f),p=m.enableTracking,b=m.disableTracking,v=(m.isTrackingEnabled,m.initializeSnippet),h=m.trackEvent,j=m.trackEventOnce;function I(e){e?p():b()}c&&l&&v()}).call(this,n(28))},402:function(e,t,n){"use strict";var i=n(253);n.d(t,"b",(function(){return i.a}));n(209);var r=n(431);n.d(t,"c",(function(){return r.b}));n(404);var a=n(465);n.d(t,"e",(function(){return a.a}));var o=n(466);n.d(t,"d",(function(){return o.a}));var c=n(467);n.d(t,"a",(function(){return c.a}));var l=n(468);n.d(t,"f",(function(){return l.a}));var s=n(469);n.d(t,"g",(function(){return s.a}));var u=n(470);n.d(t,"h",(function(){return u.a}));n(254),n(358);var d=n(471);n.d(t,"i",(function(){return d.a}))},404:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return T}));var i=n(14),r=n.n(i),a=n(0),o=n.n(a),c=n(2),l=n(457),s=n(1),u=n(4),d=n(30),g=n(7),f=n(13),m=n(41),p=n(51),b=n(8),v=n(434),h=n(18),j=n(203),I=n(9),y=n(435),E=n(253),O=n(239),M=n(481),k=n(482),N=n(29),_=n(60),D=n(251),A=n(410),T="audience_segmentation_setup_cta-notification";function AudienceSegmentationSetupCTABanner(t){var n=t.id,i=t.Notification,a=Object(h.a)(),o="".concat(a,"_audiences-setup-cta-dashboard"),l=Object(u.useDispatch)(m.a),N=l.dismissNotification,_=l.registerNotification,D=Object(u.useDispatch)(d.a).setValues,T={tooltipSlug:n,title:Object(c.__)("You can always enable groups in Settings later","google-site-kit"),content:Object(c.__)("The visitors group section will be added to your dashboard once you set it up.","google-site-kit"),dismissLabel:Object(c.__)("Got it","google-site-kit")},S=Object(j.b)(T),w=Object(u.useSelect)((function(e){return e(m.a).isNotificationDismissalFinal(n)})),C=Object(u.useSelect)((function(e){return e(d.a).getValue(b.c,"autoSubmit")})),z=Object(s.useState)(!1),R=r()(z,2),x=R[0],P=R[1],L=Object(u.useDispatch)(g.a).dismissItem,B=Object(s.useCallback)((function(){_(A.a,{Component:A.b,areaSlug:p.c.DASHBOARD_TOP,isDismissible:!0}),N(n),L(v.a)}),[_,N,n,L]),G=Object(s.useCallback)((function(){P(!0)}),[P]),W=Object(y.a)({onSuccess:B,onError:G}),Z=W.apiErrors,V=W.failedAudiences,U=W.isSaving,F=W.onEnableGroups,H=Object(u.useDispatch)(g.a).clearPermissionScopeError,Y=Object(u.useDispatch)(f.c).setSetupErrorCode,Q=Object(s.useCallback)((function(){D(b.c,{autoSubmit:!1}),H(),Y(null),P(!1)}),[H,Y,D]),X=Object(u.useSelect)((function(e){return e(f.c).getSetupErrorCode()})),J=C&&"access_denied"===X,K={gaTrackingEventArgs:{category:o}};return e.createElement(s.Fragment,null,e.createElement(i,K,e.createElement(O.a,{notificationID:n,title:Object(c.__)("Learn how different types of visitors interact with your site","google-site-kit"),description:Object(c.__)('Understand what brings new visitors to your site and keeps them coming back. Site Kit can now group your site visitors into relevant segments like "new" and "returning". To set up these new groups, Site Kit needs to update your Google Analytics property.',"google-site-kit"),ctaButton:{label:U?Object(c.__)("Enabling groups","google-site-kit"):Object(c.__)("Enable groups","google-site-kit"),onClick:F,disabled:U,inProgress:U},dismissButton:{label:w?Object(c.__)("Don’t show again","google-site-kit"):Object(c.__)("Maybe later","google-site-kit"),onClick:S,disabled:U},dismissOptions:{expiresInSeconds:w?0:2*I.f},svg:{desktop:M.a,mobile:k.a,verticalPosition:"bottom"},gaTrackingEventArgs:{category:o}})),(x||J)&&e.createElement(E.a,{hasOAuthError:J,apiErrors:Z.length?Z:V,onRetry:F,inProgress:U,onCancel:J?Q:function(){return P(!1)},trackEventCategory:"".concat(a,"_audiences-setup")}))}AudienceSegmentationSetupCTABanner.propTypes={id:o.a.string,Notification:o.a.elementType},t.b=Object(l.a)(Object(_.a)({moduleName:N.g}),Object(D.g)("audienceSegmentationSetupCTA"))(AudienceSegmentationSetupCTABanner)}).call(this,n(3))},405:function(e,t,n){"use strict";t.a="data:image/svg+xml;base64,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"},406:function(e,t,n){"use strict";t.a="data:image/svg+xml;base64,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"},407:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileHeader}));var i=n(0),r=n.n(i),a=n(137),o=n(116);function MetricTileHeader(t){var n=t.title,i=t.infoTooltip,r=t.loading;return e.createElement("div",{className:"googlesitekit-km-widget-tile__title-container"},e.createElement("h3",{className:"googlesitekit-km-widget-tile__title"},n),r?e.createElement(o.a,null,e.createElement(a.a,{title:i})):e.createElement(a.a,{title:i}))}MetricTileHeader.propTypes={title:r.a.string,infoTooltip:r.a.oneOfType([r.a.string,r.a.element]),loading:r.a.bool}}).call(this,n(3))},408:function(e,t,n){"use strict";function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,n=e.filter((function(e){var t=e.dimensionValues;return"(not set)"!==t[0].value&&""!==t[0].value}));return n.slice(0,t)}n.d(t,"a",(function(){return i}))},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var i=n(24),r="core/notifications",a=[i.s,i.n,i.l,i.o,i.m]},410:function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return p})),n.d(t,"b",(function(){return AudienceSegmentationSetupSuccessSubtleNotification}));var r=n(0),a=n.n(r),o=n(2),c=n(1),l=n(166),s=n(38),u=n(94),d=n(23),g=n(4),f=n(41),m=n(7),p="setup-success-notification-audiences";function AudienceSegmentationSetupSuccessSubtleNotification(t){var n=t.id,r=t.Notification,a=Object(d.e)(),b=Object(g.useDispatch)(f.a).dismissNotification,v=Object(g.useSelect)((function(e){return e(m.a).isAudienceSegmentationWidgetHidden()}));Object(c.useEffect)((function(){v&&b(p)}),[b,v]);return void 0===v?null:i.createElement(r,null,i.createElement(l.a,{notificationID:n,type:s.a.SUCCESS,title:Object(o.__)("Success! Visitor groups added to your dashboard","google-site-kit"),description:Object(o.__)("Get to know how different types of visitors interact with your site, e.g. which pages they visit and for how long","google-site-kit"),ctaButton:{label:Object(o.__)("Show me","google-site-kit"),onClick:function(t){t.preventDefault(),b(p),setTimeout((function(){e.scrollTo({top:Object(u.a)(".googlesitekit-widget-area--mainDashboardTrafficAudienceSegmentation",a),behavior:"smooth"})}),50)}},dismissButton:!0}))}AudienceSegmentationSetupSuccessSubtleNotification.propTypes={id:a.a.string.isRequired,Notification:a.a.elementType.isRequired}}).call(this,n(28),n(3))},411:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RetryButton}));var i=n(0),r=n.n(i),a=n(2),o=n(11),c=n(9),l=n(18);function RetryButton(t){var n=t.handleRetry,i=Object(l.a)();return e.createElement(o.Button,{className:"googlesitekit-audience-selection-panel__error-notice-action",onClick:function(){n(),Object(c.I)("".concat(i,"_audiences-sidebar"),"data_loading_error_retry")},tertiary:!0},Object(a.__)("Retry","google-site-kit"))}RetryButton.propTypes={handleRetry:r.a.func.isRequired}}).call(this,n(3))},419:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var i=n(4),r=n(19),a=n(7),o=n(25),c=n(8),l=n(29),s=n(70),u=n(86);function d(){return Object(i.useSelect)((function(e){var t=e(a.a).isItemDismissed(o.l),n=e(a.a).isDismissingItem(o.l),i=g(e,u.a,s.b),r=g(e,l.g,c.r);return!1===t&&!1===n&&i&&r}),[])}function g(e,t,n){if(e(r.a).isModuleConnected(t)){var i=e(n),a=i.isGatheringData,o=i.isDataAvailableOnLoad;return a(),o()}}},420:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var i=n(1),r=n(4),a=n(7),o=n(19),c=n(33);function l(e){var t=Object(r.useSelect)((function(e){return e(a.a).hasCapability(a.K)})),n=Object(r.useSelect)((function(t){return t(o.a).getModuleStoreName(e)})),l=Object(r.useSelect)((function(e){var t;return null===(t=e(n))||void 0===t?void 0:t.getAdminReauthURL()})),s=Object(r.useDispatch)(c.a).navigateTo,u=Object(i.useCallback)((function(){return s(l)}),[l,s]);return l&&t?u:null}},43:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r}));var i="_googlesitekitDataLayer",r="data-googlesitekit-gtag"},431:function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return p})),n.d(t,"b",(function(){return AudienceSegmentationIntroductoryOverlayNotification}));var r=n(0),a=n.n(r),o=n(2),c=n(4),l=n(478),s=n(479),u=n(322),d=n(94),g=n(23),f=n(41),m=n(18),p="audienceSegmentationIntroductoryOverlayNotification";function AudienceSegmentationIntroductoryOverlayNotification(t){var n=t.id,r=t.Notification,a=Object(m.a)(),p=Object(g.e)(),b=Object(c.useDispatch)(f.a).dismissNotification,v={category:"".concat(a,"_audiences-secondary-user-intro")};return i.createElement(r,{gaTrackingEventArgs:v},i.createElement(u.a,{notificationID:n,title:Object(o.__)("New! Visitor groups","google-site-kit"),description:Object(o.__)("You can now learn more about your site visitor groups by comparing different metrics.","google-site-kit"),GraphicDesktop:l.a,GraphicMobile:s.a,ctaButton:{label:Object(o.__)("Show me","google-site-kit"),onClick:function(t){t.preventDefault();setTimeout((function(){e.scrollTo({top:Object(d.a)(".googlesitekit-widget-area--mainDashboardTrafficAudienceSegmentation",p),behavior:"smooth"})}),0),b(n)}},dismissButton:{label:Object(o.__)("Got it","google-site-kit")},gaTrackingEventArgs:v}))}AudienceSegmentationIntroductoryOverlayNotification.propTypes={id:a.a.string.isRequired,Notification:a.a.elementType.isRequired}}).call(this,n(28),n(3))},434:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return SetupSuccess}));var i=n(1),r=n(4),a=n(7),o=n(18),c=n(127),l=n(9),s=n(480),u=Object(c.a)(s.a),d="settings_visitor_groups_setup_success_notification";function SetupSuccess(){var t=Object(o.a)(),n=Object(r.useDispatch)(a.a).dismissItem,c=Object(r.useSelect)((function(e){return e(a.a).isAudienceSegmentationWidgetHidden()})),s=Object(r.useSelect)((function(e){return e(a.a).isItemDismissed(d)})),g=c&&!1===s;return Object(i.useEffect)((function(){g&&n(d)}),[n,g]),void 0===s||s||g?null:e.createElement(u,{onInView:function(){Object(l.I)("".concat(t,"_audiences-setup-cta-settings-success"),"view_notification")}})}}).call(this,n(3))},435:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return h}));var i=n(6),r=n.n(i),a=n(16),o=n.n(a),c=n(14),l=n.n(c),s=n(210),u=n(2),d=n(1),g=n(176),f=n(4),m=n(30),p=n(7),b=n(34),v=n(8);function h(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.redirectURL,i=t.onSuccess,a=t.onError,c=Object(s.a)(),h=Object(d.useState)([]),j=l()(h,2),I=j[0],y=j[1],E=Object(d.useState)([]),O=l()(E,2),M=O[0],k=O[1],N=Object(d.useState)(!1),_=l()(N,2),D=_[0],A=_[1],T=Object(f.useSelect)((function(e){return e(p.a).hasScope(v.h)})),S=Object(f.useSelect)((function(e){return e(m.a).getValue(v.c,"autoSubmit")})),w=Object(f.useDispatch)(m.a),C=w.setValues,z=Object(f.useDispatch)(p.a),R=z.setPermissionScopeError,x=Object(f.useDispatch)(v.r),P=x.enableAudienceGroup,L=x.fetchSyncAvailableCustomDimensions,B=x.determineNeedForAnalytics4EditScope,G=x.syncAvailableAudiences;n||(n=Object(g.a)(e.location.href,{notification:"audience_segmentation"}));var W=Object(d.useCallback)(o()(r.a.mark((function e(){var t,n,i,a,o,c,l,s,u,d;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,G();case 2:if(t=e.sent,!(n=t.error)){e.next=6;break}return e.abrupt("return",{error:n});case 6:return e.next=8,L();case 8:if(i=e.sent,!(a=i.error)){e.next=12;break}return e.abrupt("return",{error:a});case 12:if(T){e.next=24;break}return e.next=15,B();case 15:if(o=e.sent,c=o.error,l=o.needsScope,!c){e.next=22;break}return e.abrupt("return",{error:c});case 22:if(!l){e.next=24;break}return e.abrupt("return",{needsScope:!0});case 24:return C(v.c,{autoSubmit:!1}),e.next=27,P(M);case 27:if(e.t0=e.sent,e.t0){e.next=30;break}e.t0={};case 30:return s=e.t0,u=s.error,d=s.failedSiteKitAudienceSlugs,e.abrupt("return",{error:u,failedSiteKitAudienceSlugs:d});case 34:case"end":return e.stop()}}),e)}))),[P,M,L,T,B,C,G]),Z=Object(d.useCallback)(o()(r.a.mark((function t(){var o,l,s,d,g;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return A(!0),t.next=3,W();case 3:if(o=t.sent,l=o.error,s=o.needsScope,d=o.failedSiteKitAudienceSlugs,!s){t.next=11;break}return C(v.c,{autoSubmit:!0}),R({code:b.a,message:Object(u.__)("Additional permissions are required to create new audiences in Analytics.","google-site-kit"),data:{status:403,scopes:[v.h],skipModal:!0,skipDefaultErrorNotifications:!0,redirectURL:n,errorRedirectURL:e.location.href}}),t.abrupt("return");case 11:l||d?null==a||a():null==i||i(),c()&&(g=function _newArrayIfNotEmpty(e){return e.length?[]:e},l?(y([l]),k(g)):Array.isArray(d)?(k(d),y(g)):(y(g),k(g)),A(!1));case 13:case"end":return t.stop()}}),t)}))),[W,c,C,R,n,a,i]);return Object(d.useEffect)((function(){T&&S&&Z()}),[T,S,Z]),{apiErrors:I,failedAudiences:M,isSaving:D,onEnableGroups:Z}}}).call(this,n(28))},44:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"i",(function(){return r})),n.d(t,"h",(function(){return a})),n.d(t,"f",(function(){return o})),n.d(t,"g",(function(){return c})),n.d(t,"d",(function(){return l})),n.d(t,"e",(function(){return s})),n.d(t,"k",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"b",(function(){return g})),n.d(t,"c",(function(){return f}));var i="audience-segmentation-add-group-notice",r="googlesitekit-audience-selection-panel-opened",a="audience-selection-form",o="audience-selected",c="audience-selection-changed",l="audience-segmentation-creation-notice",s="audience-segmentation-creation-success-notice",u=1,d=3,g="audience-creation-edit-scope-notice",f="audience-creation-form"},459:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeMetricsLink}));var i=n(1),r=n(2),a=n(4),o=n(26),c=n(7),l=n(25),s=n(21),u=n(323),d=n(474),g=n(9),f=n(18),m=n(533);function ChangeMetricsLink(){var t=Object(a.useSelect)((function(e){return e(c.a).getKeyMetrics()})),n=Object(f.a)(),p=Object(a.useDispatch)(o.b).setValue,b=Object(i.useCallback)((function(){p(l.k,!0),Object(g.I)("".concat(n,"_kmw"),"change_metrics")}),[p,n]),v=Array.isArray(t)&&(null==t?void 0:t.length)>0;return Object(m.a)(v),v?e.createElement(i.Fragment,null,e.createElement(s.a,{className:"googlesitekit-widget-area__cta-link googlesitekit-km-change-metrics-cta",onClick:b,leadingIcon:e.createElement(u.a,{width:22,height:22}),secondary:!0,linkButton:!0},Object(r.__)("Change metrics","google-site-kit")),e.createElement(d.a,null)):null}}).call(this,n(3))},460:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InsufficientPermissionsError}));var i=n(0),r=n.n(i),a=n(1),o=n(42),c=n(2),l=n(4),s=n(13),u=n(21),d=n(317),g=n(9),f=n(18);function InsufficientPermissionsError(t){var n=t.moduleSlug,i=t.onRetry,r=t.infoTooltip,m=t.headerText,p=Object(f.a)(),b=Object(l.useSelect)((function(e){return e(s.c).getErrorTroubleshootingLinkURL({code:"".concat(n,"_insufficient_permissions")})}));Object(a.useEffect)((function(){Object(g.J)("".concat(p,"_kmw"),"insufficient_permissions_error")}),[p]);var v=Object(a.useCallback)((function(){Object(g.I)("".concat(p,"_kmw"),"insufficient_permissions_error_retry"),null==i||i()}),[i,p]);return e.createElement(d.a,{title:Object(c.__)("Insufficient permissions","google-site-kit"),headerText:m,infoTooltip:r},e.createElement("div",{className:"googlesitekit-report-error-actions"},e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(o.a)(Object(c.__)("Permissions updated? <a>Retry</a>","google-site-kit"),{a:e.createElement(u.a,{onClick:v})})),e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(o.a)(Object(c.__)("You’ll need to contact your administrator. <a>Learn more</a>","google-site-kit"),{a:e.createElement(u.a,{href:b,external:!0,hideExternalIndicator:!0})}))))}InsufficientPermissionsError.propTypes={moduleSlug:r.a.string.isRequired,onRetry:r.a.func.isRequired,headerText:r.a.string,infoTooltip:r.a.string}}).call(this,n(3))},461:function(e,t,n){"use strict";(function(e){var i=n(6),r=n.n(i),a=n(16),o=n.n(a),c=n(14),l=n.n(c),s=n(0),u=n.n(s),d=n(222),g=n(2),f=n(1),m=n(4),p=n(7),b=n(13),v=n(33),h=n(25),j=n(29),I=n(60),y=n(203),E=n(9),O=n(18),M=n(419),k=n(184),N=n(21),_=n(405),D=n(406);function KeyMetricsSetupCTAWidget(t){var n=t.Widget,i=t.WidgetNull,a=Object(f.useRef)(),c=Object(O.a)(),s="".concat(c,"_kmw-cta-notification"),u=Object(M.a)(),j=Object(m.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-user-input")})),I=Object(m.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-metric-selection")})),A=Object(d.a)(a,{threshold:.25}),T=Object(f.useState)(!1),S=l()(T,2),w=S[0],C=S[1],z=!!(null==A?void 0:A.intersectionRatio),R=Object(m.useDispatch)(p.a).triggerSurvey,x=Object(m.useSelect)((function(e){return e(b.c).isUsingProxy()}));Object(f.useEffect)((function(){z&&!w&&(Object(E.I)("".concat(c,"_kmw-cta-notification"),"view_notification"),x&&R("view_kmw_setup_cta",{ttl:E.f}),C(!0))}),[z,w,c,x,R]);var P={tooltipSlug:h.l,title:Object(g.__)("You can always set up goals in Settings later","google-site-kit"),content:Object(g.__)("The Key Metrics section will be added back to your dashboard once you set your goals in Settings","google-site-kit"),dismissLabel:Object(g.__)("Got it","google-site-kit")},L=Object(y.b)(P),B=Object(m.useDispatch)(p.a).dismissItem,G=Object(f.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(E.I)(s,"dismiss_notification");case 2:return L(),e.next=5,B(h.l);case 5:case"end":return e.stop()}}),e)}))),[s,L,B]),W=Object(m.useDispatch)(v.a).navigateTo,Z=Object(f.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(E.I)(s,"confirm_pick_own_metrics");case 2:W(I);case 3:case"end":return e.stop()}}),e)}))),[s,W,I]),V=Object(f.useCallback)((function(){Object(E.I)(s,"confirm_get_tailored_metrics")}),[s]);return u?e.createElement(n,{noPadding:!0},e.createElement(k.a,{ref:a,className:"googlesitekit-banner--setup-cta",title:Object(g.__)("Get personalized suggestions for user interaction metrics based on your goals","google-site-kit"),description:Object(g.__)("Answer 3 questions and we’ll suggest relevant metrics for your dashboard. These metrics will help you track how users interact with your site.","google-site-kit"),dismissButton:{label:Object(g.__)("Maybe later","google-site-kit"),onClick:G},ctaButton:{label:Object(g.__)("Get tailored metrics","google-site-kit"),href:j,onClick:V},svg:{desktop:_.a,mobile:D.a,verticalPosition:"top"},footer:e.createElement("div",{className:"googlesitekit-widget-key-metrics-footer"},e.createElement("span",null,Object(g.__)("Interested in specific metrics?","google-site-kit")),e.createElement(N.a,{onClick:Z},Object(g.__)("Select your own metrics","google-site-kit")))})):e.createElement(i,null)}KeyMetricsSetupCTAWidget.propTypes={Widget:u.a.elementType.isRequired,WidgetNull:u.a.elementType},t.a=Object(I.a)({moduleName:j.g})(KeyMetricsSetupCTAWidget)}).call(this,n(3))},462:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileNumeric}));var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(9),u=n(193),d=n(208);function MetricTileNumeric(t){var n=t.metricValue,i=t.metricValueFormat,a=t.subText,c=t.previousValue,l=t.currentValue,g=o()(t,["metricValue","metricValueFormat","subText","previousValue","currentValue"]),f=Object(s.m)(i);return e.createElement(d.a,r()({className:"googlesitekit-km-widget-tile--numeric"},g),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-container"},e.createElement("div",{className:"googlesitekit-km-widget-tile__metric"},Object(s.B)(n,f)),e.createElement("p",{className:"googlesitekit-km-widget-tile__subtext"},a)),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-change-container"},e.createElement(u.a,{previousValue:c,currentValue:l,isAbsolute:"percent"===(null==f?void 0:f.style)})))}MetricTileNumeric.propTypes={metricValue:l.a.oneOfType([l.a.string,l.a.number]),metricValueFormat:l.a.oneOfType([l.a.string,l.a.object]),subtext:l.a.string,previousValue:l.a.number,currentValue:l.a.number}}).call(this,n(3))},463:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileTable}));var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(15),u=n(10),d=n.n(u),g=n(208);function MetricTileTable(t){var n=t.rows,i=void 0===n?[]:n,a=t.columns,c=void 0===a?[]:a,l=t.limit,u=t.ZeroState,f=o()(t,["rows","columns","limit","ZeroState"]),m=null;return(null==i?void 0:i.length)>0?m=i.slice(0,l||i.length).map((function(t,n){return e.createElement("div",{key:n,className:"googlesitekit-table__body-row"},c.map((function(n,i){var r=n.Component,a=n.field,o=n.className,c=void 0!==a?Object(s.get)(t,a):void 0;return e.createElement("div",{key:i,className:d()("googlesitekit-table__body-item",o)},r&&e.createElement(r,{row:t,fieldValue:c}),!r&&c)})))})):u&&(m=e.createElement("div",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("div",{className:"googlesitekit-table__body-zero-data"},e.createElement(u,null)))),e.createElement(g.a,r()({className:"googlesitekit-km-widget-tile--table"},f),e.createElement("div",{className:"googlesitekit-km-widget-tile__table"},m))}MetricTileTable.propTypes={rows:l.a.array,columns:l.a.array,limit:l.a.number,ZeroState:l.a.elementType}}).call(this,n(3))},464:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileTablePlainText}));var i=n(0),r=n.n(i);function MetricTileTablePlainText(t){var n=t.content;return e.createElement("p",{className:"googlesitekit-km-widget-tile__table-plain-text"},n)}MetricTileTablePlainText.propTypes={content:r.a.string.isRequired}}).call(this,n(3))},465:function(e,t,n){"use strict";(function(e){var i=n(6),r=n.n(i),a=n(16),o=n.n(a),c=n(14),l=n.n(c),s=n(0),u=n.n(s),d=n(1),g=n(4),f=n(60),m=n(8),p=n(29),b=n(7),v=n(483),h=n(185),j=n(209),I=n(358),y=n(249),E=n(34);function AudienceTilesWidget(t){var n=t.Widget,i=Object(g.useSelect)((function(e){var t=e(m.r).getOrSyncAvailableAudiences();return null==t?void 0:t.map((function(e){return e.name}))})),a=Object(g.useSelect)((function(e){return e(b.a).getConfiguredAudiences()})),c=Object(d.useState)(!1),s=l()(c,2),u=s[0],f=s[1],p=Object(g.useDispatch)(m.r),O=p.clearErrors,M=p.maybeSyncAvailableAudiences,k=p.syncAvailableAudiences,N=Object(g.useSelect)((function(e){return e(m.r).isSettingUpAudiences()})),_=Object(g.useSelect)((function(e){return e(m.r).getErrorForAction("syncAvailableAudiences")}));if(Object(d.useEffect)((function(){u||N||function(){var e=o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,M();case 2:f(!0);case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()()}),[u,N,M]),_){var D=Object(E.e)(_);return e.createElement(j.a,{errors:_,Widget:n,onRetry:D?void 0:o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,O("syncAvailableAudiences");case 2:return e.next=4,k();case 4:case"end":return e.stop()}}),e)}))),showRetryButton:!D})}return(null==a?void 0:a.some((function(e){return null==i?void 0:i.includes(e)})))?e.createElement(v.a,{Widget:n,widgetLoading:!u||!i||!a}):u?e.createElement(I.a,{Widget:n,WidgetNull:y.a}):e.createElement(n,{className:"googlesitekit-widget-audience-tiles",noPadding:!0},e.createElement("div",{className:"googlesitekit-widget-audience-tiles__body"},e.createElement(n,{noPadding:!0},e.createElement(h.a,null)),e.createElement(n,{noPadding:!0},e.createElement(h.a,null))))}AudienceTilesWidget.propTypes={Widget:u.a.elementType.isRequired,WidgetNull:u.a.elementType.isRequired},t.a=Object(f.a)({moduleName:p.g})(AudienceTilesWidget)}).call(this,n(3))},466:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceSelectionPanel}));var i=n(4),r=n(44),a=n(26),o=n(170),c=n(509);function AudienceSelectionPanel(){var t=Object(i.useSelect)((function(e){return e(a.b).getValue(r.i)}));return e.createElement(o.a,{value:{key:"AudienceSelectionPanel",value:!!t}},e.createElement(c.a,null))}}).call(this,n(3))},467:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceAreaFooter}));var i=n(2),r=n(4),a=n(7),o=n(8),c=n(29),l=n(19),s=n(143),u=n(39);function AudienceAreaFooter(){var t=Object(u.a)(),n=Object(r.useSelect)((function(e){return e(a.a).getDateRangeDates({offsetDays:o.g})})),d=Object(r.useSelect)((function(e){return t?null:e(o.r).getServiceReportURL("audiences",{dates:n})}));return Object(r.useSelect)((function(e){return e(l.a).isModuleConnected(c.g)}))?e.createElement(s.a,{className:"googlesitekit-audience-widget__source",name:Object(i._x)("Analytics","Service name","google-site-kit"),href:d,external:!0}):null}}).call(this,n(3))},468:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeGroupsLink}));var i=n(1),r=n(2),a=n(4),o=n(18),c=n(9),l=n(44),s=n(26),u=n(8),d=n(21),g=n(323);function ChangeGroupsLink(){var t=Object(o.a)(),n=Object(a.useInViewSelect)((function(e){return e(u.r).getConfigurableAudiences()}),[]),f=Object(a.useDispatch)(s.b).setValue,m=Object(i.useCallback)((function(){f(l.i,!0),Object(c.I)("".concat(t,"_audiences-sidebar"),"change_groups")}),[f,t]);return Array.isArray(n)&&(null==n?void 0:n.length)>0?e.createElement(d.a,{className:"googlesitekit-widget-area__cta-link",onClick:m,leadingIcon:e.createElement(g.a,{width:22,height:22}),secondary:!0,linkButton:!0},Object(r.__)("Change groups","google-site-kit")):null}}).call(this,n(3))},469:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConnectAnalyticsCTAWidget}));var i=n(0),r=n.n(i),a=n(42),o=n(1),c=n(2),l=n(4),s=n(524),u=n(525),d=n(21),g=n(19),f=n(29),m=n(187),p=n(23),b=n(367);function ConnectAnalyticsCTAWidget(t){var n=t.Widget,i=Object(p.e)()===p.c,r=Object(m.a)(f.g),v=Object(l.useSelect)((function(e){return e(g.a).getModuleIcon(f.g)})),h=i?e.createElement("p",null,Object(a.a)(Object(c.__)("Google Analytics is disconnected, your audience metrics can’t be displayed. <a>Connect Google Analytics</a>","google-site-kit"),{a:e.createElement(d.a,{onClick:r,secondary:!0})})):e.createElement(o.Fragment,null,e.createElement("p",null,Object(c.__)("Google Analytics is disconnected, your audience metrics can’t be displayed","google-site-kit")),e.createElement(d.a,{onClick:r,secondary:!0},Object(c.__)("Connect Google Analytics","google-site-kit")));return e.createElement(n,{noPadding:!0},e.createElement(b.a,{Icon:v,SVGGraphic:i?u.a:s.a},h))}ConnectAnalyticsCTAWidget.propTypes={Widget:r.a.elementType.isRequired}}).call(this,n(3))},470:function(e,t,n){"use strict";(function(e){var i=n(0),r=n.n(i),a=n(1),o=n(2),c=n(4),l=n(7),s=n(9),u=n(60),d=n(254),g=n(175),f=n(8),m=n(29),p=n(26),b=n(127),v=n(18),h=Object(b.a)(d.a);function InfoNoticeWidget(t){var n=t.Widget,i=t.WidgetNull,r=Object(v.a)(),u=Object(c.useInViewSelect)((function(e){var t=e(f.r).getOrSyncAvailableAudiences();return null==t?void 0:t.map((function(e){return e.name}))}),[]),d=Object(c.useInViewSelect)((function(e){return e(l.a).getConfiguredAudiences()}),[]),m=null==d?void 0:d.some((function(e){return null==u?void 0:u.includes(e)})),b=g.a.length,j=Object(c.useInViewSelect)((function(e){return e(l.a).isPromptDismissed(g.c)}),[]),I=Object(c.useSelect)((function(e){return e(p.b).getValue(g.b)})),y=Object(c.useInViewSelect)((function(e){return e(l.a).getPromptDismissCount(g.c)}),[]),E=Object(c.useDispatch)(l.a).dismissPrompt,O=Object(a.useCallback)((function(){void 0!==y&&Object(s.I)("".concat(r,"_audiences-info-notice"),"dismiss_notice",g.a[y].slug).finally((function(){var e=2*s.f,t=y+1<b?e:0;E(g.c,{expiresInSeconds:t})}))}),[y,E,b,r]);if(!0!==m||j||void 0===y||y>=b||!0===I)return e.createElement(i,null);var M=g.a[y],k=M.slug,N=M.content;return e.createElement(n,{noPadding:!0},e.createElement(h,{content:N,dismissLabel:Object(o.__)("Got it","google-site-kit"),onDismiss:O,onInView:function(){Object(s.I)("".concat(r,"_audiences-info-notice"),"view_notice",k)}}))}InfoNoticeWidget.propTypes={Widget:r.a.elementType.isRequired,WidgetNull:r.a.elementType.isRequired},t.a=Object(u.a)({moduleName:m.g})(InfoNoticeWidget)}).call(this,n(3))},471:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SecondaryUserSetupWidget}));var i=n(6),r=n.n(i),a=n(16),o=n.n(a),c=n(14),l=n.n(c),s=n(0),u=n.n(s),d=n(84),g=n(1),f=n(4),m=n(185),p=n(8),b=n(209),v=n(34);function SecondaryUserSetupWidget(t){var n=t.Widget,i=Object(g.useState)(null),a=l()(i,2),c=a[0],s=a[1],u=Object(f.useSelect)((function(e){return e(p.r).isSettingUpAudiences()})),h=Object(f.useDispatch)(p.r).enableSecondaryUserAudienceGroup,j=function(){var e=o()(r.a.mark((function e(){var t,n;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s(null),e.next=3,h();case 3:t=e.sent,(n=t.error)&&s(n);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return Object(d.a)((function(){u||o()(r.a.mark((function e(){var t,n;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:t=e.sent,(n=t.error)&&s(n);case 5:case"end":return e.stop()}}),e)})))()})),c?e.createElement(b.a,{Widget:n,errors:c,onRetry:j,showRetryButton:!Object(v.e)(c)}):e.createElement(n,{className:"googlesitekit-widget-audience-tiles",noPadding:!0},e.createElement("div",{className:"googlesitekit-widget-audience-tiles__body"},e.createElement(n,{noPadding:!0},e.createElement(m.a,null)),e.createElement(n,{noPadding:!0},e.createElement(m.a,null))))}SecondaryUserSetupWidget.propTypes={Widget:u.a.elementType.isRequired}}).call(this,n(3))},474:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupCompletedSurveyTrigger}));var i=n(1),r=n(4),a=n(13),o=n(7),c=n(9),l=n(258);function SetupCompletedSurveyTrigger(){var t=Object(r.useSelect)((function(e){return e(a.c).isKeyMetricsSetupCompleted()})),n=Object(r.useSelect)((function(e){return e(a.c).getKeyMetricsSetupCompletedBy()})),s=Object(r.useSelect)((function(e){return e(o.a).getID()}));return t?e.createElement(i.Fragment,null,e.createElement(l.a,{triggerID:"view_kmw",ttl:c.f}),n===s&&e.createElement(l.a,{triggerID:"view_kmw_setup_completed",ttl:c.f})):null}}).call(this,n(3))},475:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GetHelpLink}));var i=n(0),r=n.n(i),a=n(42),o=n(2),c=n(21);function GetHelpLink(t){var n=t.linkURL;return Object(a.a)(/* translators: %s: get help text. */
Object(o.__)("Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(c.a,{href:n,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))})}GetHelpLink.propTypes={linkURL:r.a.string.isRequired}}).call(this,n(3))},476:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileLoader}));var i=n(48);function MetricTileLoader(){return e.createElement("div",{className:"googlesitekit-km-widget-tile__loading"},e.createElement(i.a,{className:"googlesitekit-km-widget-tile__loading-header",width:"100%",height:"14px"}),e.createElement(i.a,{className:"googlesitekit-km-widget-tile__loading-body",width:"100%",height:"53px"}))}}).call(this,n(3))},477:function(e,t,n){"use strict";(function(e){var i=n(0),r=n.n(i),a=n(1),o=n(2),c=n(11),l=n(17),s=n(23),u=n(368),d=n(34),g=n(144),f=n(361),m=Object(a.forwardRef)((function(t,n){var i=t.Widget,r=t.errors,a=t.onRetry,m=t.onRequestAccess,p=t.showRetryButton,b=Object(s.e)(),v=b===s.b,h=b===s.c,j=r.some(d.e);return e.createElement(i,{ref:n,className:"googlesitekit-audience-segmentation-error-widget",noPadding:!0},e.createElement(l.e,{className:"googlesitekit-audience-segmentation-error__widget-primary-cell",collapsed:!0},e.createElement(l.k,null,e.createElement(l.a,{smSize:6,mdSize:8,lgSize:7},e.createElement("h3",{className:"googlesitekit-publisher-win__title"},j?Object(o.__)("Insufficient permissions","google-site-kit"):Object(o.__)("Your visitor groups data loading failed","google-site-kit")),e.createElement("div",{className:"googlesitekit-widget-audience-segmentation-error__actions"},p&&a?e.createElement(c.Button,{onClick:a,danger:!0},Object(o.__)("Retry","google-site-kit")):e.createElement(g.a,{moduleSlug:"analytics-4",error:r,GetHelpLink:j?f.a:void 0,hideGetHelpLink:!j,buttonVariant:"danger",getHelpClassName:"googlesitekit-error-retry-text",onRetry:a,onRequestAccess:m}))),!v&&!h&&e.createElement(l.a,{className:"googlesitekit-widget-audience-segmentation-error__svg-wrapper",smSize:6,mdSize:3,lgSize:5},e.createElement(u.a,{width:"233px"})),h&&e.createElement(l.a,{className:"googlesitekit-widget-audience-segmentation-error__svg-wrapper",mdSize:8},e.createElement(u.a,{width:"233px"})),v&&e.createElement(l.a,{className:"googlesitekit-widget-audience-segmentation-error__svg-wrapper",smSize:8},e.createElement(u.a,{width:"233px"})))))}));m.propTypes={Widget:r.a.elementType.isRequired,errors:r.a.arrayOf(r.a.object).isRequired,onRetry:r.a.func.isRequired,onRequestAccess:r.a.func.isRequired,showRetryButton:r.a.bool},t.a=m}).call(this,n(3))},478:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{clipPath:"url(#audience-segmentation-introductory-graphic-desktop_svg__clip0_1395_20972)"},i.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#B8E6CA"}),i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-desktop_svg__filter0_d_1395_20972)"},i.createElement("rect",{x:-10,y:25,width:153,height:174,rx:11,fill:"#fff"})),i.createElement("rect",{x:9.031,y:110.641,width:53.016,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:9.031,y:95.688,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:9.031,y:148.703,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M94.672 108.602a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 010 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:9,y:46,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),i.createElement("path",{d:"M94.672 161.617a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 110 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-desktop_svg__filter1_d_1395_20972)"},i.createElement("rect",{x:152,y:25,width:153,height:174,rx:11,fill:"#fff"})),i.createElement("rect",{x:170.955,y:110.641,width:52.805,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:170.955,y:95.688,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:170.955,y:148.703,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M256.256 108.602a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:171,y:46,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),i.createElement("path",{d:"M295 73.5H152",stroke:"#EBEEF0",strokeWidth:2}),i.createElement("path",{d:"M256.256 161.617a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),i.createElement("path",{d:"M143 73.5H0",stroke:"#EBEEF0",strokeWidth:2})),o=i.createElement("defs",null,i.createElement("filter",{id:"audience-segmentation-introductory-graphic-desktop_svg__filter0_d_1395_20972",x:-26,y:13,width:185,height:206,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1395_20972"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1395_20972",result:"shape"})),i.createElement("filter",{id:"audience-segmentation-introductory-graphic-desktop_svg__filter1_d_1395_20972",x:136,y:13,width:185,height:206,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1395_20972"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1395_20972",result:"shape"})),i.createElement("clipPath",{id:"audience-segmentation-introductory-graphic-desktop_svg__clip0_1395_20972"},i.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#fff"})));t.a=function SvgAudienceSegmentationIntroductoryGraphicDesktop(e){return i.createElement("svg",r({viewBox:"0 0 296 163",fill:"none"},e),a,o)}},479:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M41.064 27.146a64.127 64.127 0 016.51-5.868C69.418 4.126 87.464 4.153 112.45 7.283c16.891 2.116 26.759 10.166 49.788 8.9 23.029-1.266 28.929-7.127 57.117-5.25 22.315 1.487 32.324 5.897 52.163 16.213 18.36 9.549 35.031 26.324 43.408 48.509 14.361 38.026-11.243 106.466-45.58 109.693-24.881 2.339-45.414-25.243-70.527-18.855-15.47 3.936-24.646 20.444-36.581 31.339-13.925 12.711-43.922 11.912-60.227 5.129-15.538-6.464-30.653-19.276-35.728-38.145-3.863-14.369-4.916-31.498-15.733-44.622-13.09-15.883-21.087-22.968-25.581-44.54-3.903-18.734 4.494-36.505 16.095-48.508z",fill:"#B8E6CA"}),o=i.createElement("path",{d:"M41.064 27.146a64.127 64.127 0 016.51-5.868C69.418 4.126 87.464 4.153 112.45 7.283c16.891 2.116 26.759 10.166 49.788 8.9 23.029-1.266 28.929-7.127 57.117-5.25 22.315 1.487 32.324 5.897 52.163 16.213 18.36 9.549 35.031 26.324 43.408 48.509 14.361 38.026-11.243 106.466-45.58 109.693-24.881 2.339-45.414-25.243-70.527-18.855-15.47 3.936-24.646 20.444-36.581 31.339-13.925 12.711-43.922 11.912-60.227 5.129-15.538-6.464-30.653-19.276-35.728-38.145-3.863-14.369-4.916-31.498-15.733-44.622-13.09-15.883-21.087-22.968-25.581-44.54-3.903-18.734 4.494-36.505 16.095-48.508z",fill:"#B8E6CA"}),c=i.createElement("g",{mask:"url(#audience-segmentation-introductory-graphic-mobile_svg__a)"},i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-mobile_svg__filter0_d_2898_16651)"},i.createElement("rect",{x:71.449,y:21.433,width:100.401,height:136.493,rx:7.218,fill:"#fff"})),i.createElement("rect",{x:83.941,y:77.631,width:34.79,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:83.941,y:67.819,width:12.489,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:83.941,y:99.983,width:12.489,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("path",{d:"M140.133 76.293a5.798 5.798 0 015.798-5.798h8.921a5.798 5.798 0 010 11.596h-8.921a5.798 5.798 0 01-5.798-5.798z",fill:"#B8E6CA"}),i.createElement("rect",{x:83.926,y:35.213,width:23.624,height:5.906,rx:2.953,fill:"#EBEEF0"}),i.createElement("path",{d:"M140.133 108.458a5.798 5.798 0 015.798-5.798h8.921a5.798 5.798 0 010 11.597h-8.921a5.798 5.798 0 01-5.798-5.799z",fill:"#FFDED3"}),i.createElement("rect",{x:83.043,y:109.796,width:36.574,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("path",{d:"M171.848 53.259H72.103",stroke:"#EBEEF0",strokeWidth:1.312}),i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-mobile_svg__filter1_d_2898_16651)"},i.createElement("rect",{x:184.973,y:21.433,width:100.401,height:136.493,rx:7.218,fill:"#fff"})),i.createElement("rect",{x:197.414,y:77.631,width:34.652,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:197.414,y:67.819,width:12.439,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:197.41,y:99.983,width:12.439,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("path",{d:"M253.391 76.293a5.798 5.798 0 015.798-5.798h8.839a5.798 5.798 0 010 11.596h-8.839a5.798 5.798 0 01-5.798-5.798z",fill:"#B8E6CA"}),i.createElement("rect",{x:197.449,y:35.213,width:23.624,height:5.906,rx:2.953,fill:"#EBEEF0"}),i.createElement("path",{d:"M278.82 53.259h-93.838",stroke:"#EBEEF0",strokeWidth:1.312}),i.createElement("path",{d:"M253.391 108.458a5.798 5.798 0 015.798-5.798h8.839a5.798 5.798 0 010 11.597h-8.839a5.798 5.798 0 01-5.798-5.799z",fill:"#FFDED3"}),i.createElement("rect",{x:196.523,y:109.796,width:36.429,height:6.244,rx:3.122,fill:"#EBEEF0"})),l=i.createElement("defs",null,i.createElement("filter",{id:"audience-segmentation-introductory-graphic-mobile_svg__filter0_d_2898_16651",x:55.449,y:9.433,width:132.402,height:168.493,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2898_16651"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2898_16651",result:"shape"})),i.createElement("filter",{id:"audience-segmentation-introductory-graphic-mobile_svg__filter1_d_2898_16651",x:168.973,y:9.433,width:132.402,height:168.493,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2898_16651"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2898_16651",result:"shape"})),i.createElement("clipPath",{id:"audience-segmentation-introductory-graphic-mobile_svg__clip0_2898_16651"},i.createElement("path",{fill:"#fff",d:"M0 0h343v128H0z"})));t.a=function SvgAudienceSegmentationIntroductoryGraphicMobile(e){return i.createElement("svg",r({viewBox:"0 0 343 123",fill:"none"},e),i.createElement("g",{clipPath:"url(#audience-segmentation-introductory-graphic-mobile_svg__clip0_2898_16651)"},a,i.createElement("mask",{id:"audience-segmentation-introductory-graphic-mobile_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:24,y:5,width:295,height:203},o),c),l)}},48:function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(0),o=n.n(a),c=n(10),l=n.n(c),s=n(23);function PreviewBlock(t){var n,i,a=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,g=t.smallWidth,f=t.smallHeight,m=t.tabletWidth,p=t.tabletHeight,b=t.desktopWidth,v=t.desktopHeight,h=Object(s.e)(),j={width:(n={},r()(n,s.b,g),r()(n,s.c,m),r()(n,s.a,b),r()(n,s.d,b),n),height:(i={},r()(i,s.b,f),r()(i,s.c,p),r()(i,s.a,v),r()(i,s.d,b),i)};return e.createElement("div",{className:l()("googlesitekit-preview-block",a,{"googlesitekit-preview-block--padding":d}),style:{width:j.width[h]||o,height:j.height[h]||c}},e.createElement("div",{className:l()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(3))},480:function(e,t,n){"use strict";(function(e){var i=n(6),r=n.n(i),a=n(16),o=n.n(a),c=n(1),l=n(2),s=n(176),u=n(91),d=n(33),g=n(13),f=n(7),m=n(9),p=n(18),b=n(4),v=n(11),h=n(111),j=Object(c.forwardRef)((function(t,n){var i=Object(p.a)(),a=Object(b.useSelect)((function(e){var t=e(g.c).getAdminURL("googlesitekit-dashboard");return Object(s.a)(t,{widgetArea:u.AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION})})),c=Object(b.useDispatch)(d.a).navigateTo,j=Object(b.useDispatch)(f.a).dismissItem;function I(){return j("settings_visitor_groups_setup_success_notification")}return e.createElement("div",{ref:n,className:"googlesitekit-settings-visitor-groups__setup-success googlesitekit-subtle-notification"},e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},e.createElement(h.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,Object(l.__)("We’ve added the visitor groups section to your dashboard!","google-site-kit"))),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},e.createElement(v.Button,{onClick:function(){Object(m.I)("".concat(i,"_audiences-setup-cta-settings-success"),"dismiss_notification").finally(I)},tertiary:!0},Object(l.__)("Got it","google-site-kit")),e.createElement(v.Button,{onClick:function(){Object(m.I)("".concat(i,"_audiences-setup-cta-settings-success"),"confirm_notification").finally(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,I();case 2:c(a);case 3:case"end":return e.stop()}}),e)}))))}},Object(l.__)("Show me","google-site-kit"))))}));t.a=j}).call(this,n(3))},481:function(e,t,n){"use strict";t.a="data:image/svg+xml;base64,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"},482:function(e,t,n){"use strict";t.a="data:image/svg+xml;base64,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"},483:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTiles}));var i=n(14),r=n.n(i),a=n(0),o=n.n(a),c=n(1),l=n(4),s=n(23),u=n(7),d=n(8),g=n(484),f=n(507),m=n(362),p=n(252);function AudienceTiles(t){var n=t.Widget,i=t.widgetLoading,a=Object(s.e)(),o=a===s.b||a===s.c,b=Object(l.useInViewSelect)((function(e){return e(u.a).getConfiguredAudiences()}),[]),v=Object(l.useInViewSelect)((function(e){return e(d.r).getOrSyncAvailableAudiences()}),[]),h=Object(l.useSelect)((function(e){return e(d.r).getConfiguredSiteKitAndOtherAudiences()}))||[[],[]],j=r()(h,2),I=j[0],y=j[1],E=Object(l.useSelect)((function(e){return e(d.r).hasAudiencePartialData(I)})),O=Object(m.a)({isSiteKitAudiencePartialData:E,siteKitAudiences:I,otherAudiences:y}),M=O.report,k=O.reportLoaded,N=O.reportError,_=O.siteKitAudiencesReport,D=O.siteKitAudiencesReportLoaded,A=O.siteKitAudiencesReportError,T=O.totalPageviewsReportLoaded,S=O.totalPageviewsReportError,w=O.topCitiesReportsLoaded,C=O.topCitiesReportErrors,z=O.topContentReportsLoaded,R=O.topContentReportErrors,x=O.topContentPageTitlesReportsLoaded,P=O.topContentPageTitlesReportErrors,L=null==b?void 0:b.reduce((function(e,t){return e[t]=[],[C,R,P].forEach((function(n){var i=n[t];i&&!Object(p.a)(i)&&e[t].push(i)})),e}),{}),B=Object(l.useSelect)((function(e){return e(u.a).getDismissedItems()})),G=Object(l.useSelect)((function(e){return e(u.a)})).isDismissingItem,W=Object(l.useDispatch)(u.a).dismissItem,Z=Object(c.useRef)({}),V=Object(c.useMemo)((function(){for(var e=[],t=[],n=null==b?void 0:b.slice().filter((function(e){return v.some((function(t){return t.name===e}))})),i=function(){var i,r=n.shift(),a=null==B?void 0:B.includes("audience-tile-".concat(r)),o=I.some((function(e){return e.name===r})),c=M,l=r;o&&E&&(c=_,l="new-visitors"===(null===(i=I.find((function(e){return e.name===r})))||void 0===i?void 0:i.audienceSlug)?"new":"returning");var s=function(e,t){var n,i,r,a=null==e||null===(n=e.rows)||void 0===n?void 0:n.find((function(e){var n,i;return(null===(n=e.dimensionValues)||void 0===n||null===(i=n[0])||void 0===i?void 0:i.value)===t}));return 0===((null==a||null===(i=a.metricValues)||void 0===i||null===(r=i[0])||void 0===r?void 0:r.value)||0)}(c,l),u=n.length+t.length>0;if(a&&s&&u)return"continue";a&&!s&&e.push(r),t.push(r)};(null==n?void 0:n.length)>0;)i();return[e,t]}),[v,b,B,E,M,I,_]),U=r()(V,2),F=U[0],H=U[1];var Y,Q=(Y=[],M&&Y.push(N),_&&Y.push(A),!(!Y.every(Boolean)&&!S)||(null==b?void 0:b.every((function(e){return L[e].length>0}))));Object(c.useEffect)((function(){F.forEach((function(e){var t="audience-tile-".concat(e);Z.current[t]||(W(t,{expiresInSeconds:1}),Z.current[t]=!0)}))}),[F,W,G]);var X=Object(l.useSelect)((function(e){return e(d.r).isFetchingSyncAvailableCustomDimensions()})),J=Object(c.useState)(H[0]),K=r()(J,2),q=K[0],$=K[1],ee=Object(c.useCallback)((function(e){var t=H.indexOf(e);return-1===t?0:t}),[H]);Object(c.useEffect)((function(){H.includes(q)||$(H[0])}),[q,H]);var te=ee(q),ne=i||!k||!D||!T||!w||!z||!x||X;return e.createElement(n,{className:"googlesitekit-widget-audience-tiles",noPadding:!0},!1===Q&&!ne&&o&&H.length>0&&e.createElement(f.a,{activeTileIndex:te,setActiveTile:$,visibleAudiences:H}),e.createElement(g.a,{activeTileIndex:te,allTilesError:Q,individualTileErrors:L,loading:ne,topCitiesReportsLoaded:w,topContentReportsLoaded:z,topContentPageTitlesReportsLoaded:x,visibleAudiences:H,Widget:n}))}AudienceTiles.propTypes={Widget:o.a.elementType.isRequired,widgetLoading:o.a.bool.isRequired}}).call(this,n(3))},484:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Body}));var i=n(27),r=n.n(i),a=n(14),o=n.n(a),c=n(0),l=n.n(c),s=n(1),u=n(4),d=n(7),g=n(8),f=n(252),m=n(408),p=n(362),b=n(23),v=n(39),h=n(209),j=n(185),I=n(485),y=n(488),E=n(365),O=n(505);function Body(t){var n=t.activeTileIndex,i=t.allTilesError,a=t.individualTileErrors,c=t.loading,l=t.topCitiesReportsLoaded,M=t.topContentReportsLoaded,k=t.topContentPageTitlesReportsLoaded,N=t.visibleAudiences,_=t.Widget,D=Object(b.e)(),A=Object(v.a)(),T=D===b.b||D===b.c,S=Object(u.useInViewSelect)((function(e){return e(g.r).getOrSyncAvailableAudiences()}),[]),w=Object(u.useInViewSelect)((function(e){return e(d.a).getConfiguredAudiences()}),[]),C=Object(u.useSelect)((function(e){return e(g.r).getConfiguredSiteKitAndOtherAudiences()}))||[[],[]],z=o()(C,2),R=z[0],x=z[1],P=Object(u.useSelect)((function(e){return e(g.r).hasAudiencePartialData(R)})),L=Object(u.useInViewSelect)((function(e){return null==w?void 0:w.reduce((function(t,n){return t[n]=e(g.r).isAudiencePartialData(n),t}),{})}),[w]),B=Object(p.a)({isSiteKitAudiencePartialData:P,siteKitAudiences:R,otherAudiences:x}),G=B.report,W=B.reportError,Z=B.siteKitAudiencesReport,V=B.totalPageviews,U=B.totalPageviewsReportError,F=B.topCitiesReport,H=B.topContentReport,Y=B.topContentReportErrors,Q=B.topContentPageTitlesReport,X=B.topContentPageTitlesReportErrors,J=function(e,t){var n,i,r,a,o,c,l=(null==S||null===(n=S.filter((function(t){return t.name===e})))||void 0===n||null===(i=n[0])||void 0===i?void 0:i.displayName)||"",s=(null==S||null===(r=S.filter((function(t){return t.name===e})))||void 0===r||null===(a=r[0])||void 0===a?void 0:a.audienceSlug)||"",u=function(e){var t,n=R.some((function(t){return t.name===e})),i=null===(t=R.find((function(t){return t.name===e})))||void 0===t?void 0:t.audienceSlug,r=function(t){var r,a,o,c,l,s,u,d,g,f,m,p,b;if(n&&P){var v,h="new-visitors"===i?"new":"returning";b=null==Z||null===(v=Z.rows)||void 0===v?void 0:v.find((function(e){var n,i,r=e.dimensionValues;return(null==r||null===(n=r[0])||void 0===n?void 0:n.value)===h&&(null==r||null===(i=r[1])||void 0===i?void 0:i.value)===t}))}else{var j;b=null==G||null===(j=G.rows)||void 0===j?void 0:j.find((function(n){var i,r,a=n.dimensionValues;return(null==a||null===(i=a[0])||void 0===i?void 0:i.value)===e&&(null==a||null===(r=a[1])||void 0===r?void 0:r.value)===t}))}return[Number((null===(r=b)||void 0===r||null===(a=r.metricValues)||void 0===a||null===(o=a[0])||void 0===o?void 0:o.value)||0),Number((null===(c=b)||void 0===c||null===(l=c.metricValues)||void 0===l||null===(s=l[1])||void 0===s?void 0:s.value)||0),Number((null===(u=b)||void 0===u||null===(d=u.metricValues)||void 0===d||null===(g=d[2])||void 0===g?void 0:g.value)||0),Number((null===(f=b)||void 0===f||null===(m=f.metricValues)||void 0===m||null===(p=m[3])||void 0===p?void 0:p.value)||0)]};return{current:r("date_range_0"),previous:r("date_range_1")}}(e),d=u.current,g=u.previous,f=d[0],m=g[0],p=d[1],b=g[1],v=d[2],h=g[2],j=d[3],I=g[3],y=null==F?void 0:F[t],E=null==H?void 0:H[t],O=(null==Q||null===(o=Q[t])||void 0===o||null===(c=o.rows)||void 0===c?void 0:c.reduce((function(e,t){return e[t.dimensionValues[0].value]=t.dimensionValues[1].value,e}),{}))||{},M=R.some((function(t){return t.name===e})),k=G,N=e;return M&&P&&(k=Z,N="new-visitors"===s?"new":"returning"),{audienceName:l,audienceSlug:s,visitors:f,prevVisitors:m,visitsPerVisitors:p,prevVisitsPerVisitors:b,pagesPerVisit:v,prevPagesPerVisit:h,pageviews:j,prevPageviews:I,topCities:y,topContent:E,topContentTitles:O,isZeroData:function(e,t){var n,i,r,a=null==e||null===(n=e.rows)||void 0===n?void 0:n.find((function(e){var n,i;return(null===(n=e.dimensionValues)||void 0===n||null===(i=n[0])||void 0===i?void 0:i.value)===t}));return 0===((null==a||null===(i=a.metricValues)||void 0===i||null===(r=i[0])||void 0===r?void 0:r.value)||0)}(k,N),isPartialData:!M&&L[e]}},K=Object.values(Y).some(f.a)||Object.values(X).some(f.a),q=Object(u.useDispatch)(d.a).dismissItem,$=Object(u.useDispatch)(g.r).fetchSyncAvailableCustomDimensions,ee=Object(s.useCallback)((function(e){q("audience-tile-".concat(e))}),[q]);Object(s.useEffect)((function(){!A&&K&&$()}),[$,K,A]);var te=0;return e.createElement("div",{className:"googlesitekit-widget-audience-tiles__body"},i&&!c&&e.createElement(h.a,{Widget:_,errors:[].concat(r()(Object.values(a).flat(2)),[W,U])}),(!1===i||c)&&N.map((function(t,i){var r,o,s,u,d,g,f,p,b,v,h,O,D,A,S,w,C,z,R,x,P,L,B,G,W,Z,U,F,H,Y;if(T&&i!==n)return null;var Q=J(t,i),X=Q.audienceName,q=Q.audienceSlug,$=Q.visitors,ne=Q.prevVisitors,ie=Q.visitsPerVisitors,re=Q.prevVisitsPerVisitors,ae=Q.pagesPerVisit,oe=Q.prevPagesPerVisit,ce=Q.pageviews,le=Q.prevPageviews,se=Q.topCities,ue=Q.topContent,de=Q.topContentTitles,ge=Q.isZeroData,fe=Q.isPartialData,me=(null==se?void 0:se.rows)?Object(m.a)(se.rows):[];return!c&&(null==l?void 0:l[t])&&(null==M?void 0:M[t])&&(null==k?void 0:k[t])&&void 0!==ge&&void 0!==fe?a[t].length>0?e.createElement(I.a,{key:t,audienceSlug:q,errors:a[t]}):e.createElement(y.a,{key:t,audienceTileNumber:te++,audienceSlug:q,title:X,infoTooltip:e.createElement(E.a,{audienceName:X,audienceSlug:q}),visitors:{currentValue:$,previousValue:ne},visitsPerVisitor:{currentValue:ie,previousValue:re},pagesPerVisit:{currentValue:ae,previousValue:oe},pageviews:{currentValue:ce,previousValue:le},percentageOfTotalPageViews:0!==V?ce/V:0,topCities:{dimensionValues:[null==me||null===(r=me[0])||void 0===r||null===(o=r.dimensionValues)||void 0===o?void 0:o[0],null==me||null===(s=me[1])||void 0===s||null===(u=s.dimensionValues)||void 0===u?void 0:u[0],null==me||null===(d=me[2])||void 0===d||null===(g=d.dimensionValues)||void 0===g?void 0:g[0]],metricValues:[null==me||null===(f=me[0])||void 0===f||null===(p=f.metricValues)||void 0===p?void 0:p[0],null==me||null===(b=me[1])||void 0===b||null===(v=b.metricValues)||void 0===v?void 0:v[0],null==me||null===(h=me[2])||void 0===h||null===(O=h.metricValues)||void 0===O?void 0:O[0]],total:$},topContent:{dimensionValues:[null==ue||null===(D=ue.rows)||void 0===D||null===(A=D[0])||void 0===A||null===(S=A.dimensionValues)||void 0===S?void 0:S[0],null==ue||null===(w=ue.rows)||void 0===w||null===(C=w[1])||void 0===C||null===(z=C.dimensionValues)||void 0===z?void 0:z[0],null==ue||null===(R=ue.rows)||void 0===R||null===(x=R[2])||void 0===x||null===(P=x.dimensionValues)||void 0===P?void 0:P[0]],metricValues:[null==ue||null===(L=ue.rows)||void 0===L||null===(B=L[0])||void 0===B||null===(G=B.metricValues)||void 0===G?void 0:G[0],null==ue||null===(W=ue.rows)||void 0===W||null===(Z=W[1])||void 0===Z||null===(U=Z.metricValues)||void 0===U?void 0:U[0],null==ue||null===(F=ue.rows)||void 0===F||null===(H=F[2])||void 0===H||null===(Y=H.metricValues)||void 0===Y?void 0:Y[0]]},topContentTitles:de,hasInvalidCustomDimensionError:K,Widget:_,audienceResourceName:t,isZeroData:ge,isPartialData:fe,isTileHideable:N.length>1,onHideTile:function(){return ee(t)}}):e.createElement(_,{key:t,noPadding:!0},e.createElement(j.a,null))})),!T&&e.createElement(O.a,{Widget:_,loading:c,allTilesError:i,visibleAudienceCount:N.length}))}Body.propTypes={activeTileIndex:l.a.number.isRequired,allTilesError:l.a.bool.isRequired,individualTileErrors:l.a.object,loading:l.a.bool.isRequired,topCitiesReportsLoaded:l.a.object.isRequired,topContentReportsLoaded:l.a.object.isRequired,topContentPageTitlesReportsLoaded:l.a.object.isRequired,visibleAudiences:l.a.array.isRequired,Widget:l.a.elementType.isRequired}}).call(this,n(3))},485:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileError}));var i=n(0),r=n.n(i),a=n(34),o=n(486),c=n(127),l=n(18),s=n(9),u=Object(c.a)(o.a);function AudienceTileError(t){var n=t.audienceSlug,i=t.errors,r=Object(l.a)(),o=i.some((function(e){return Object(a.e)(e)}));return e.createElement(u,{errors:i,onInView:function(){var e=o?"insufficient_permissions_error":"data_loading_error";Object(s.I)("".concat(r,"_audiences-tile"),e,n)},onRetry:function(){Object(s.I)("".concat(r,"_audiences-tile"),"data_loading_error_retry",n)},onRequestAccess:function(){Object(s.I)("".concat(r,"_audiences-tile"),"insufficient_permissions_error_request_access",n)}})}AudienceTileError.propTypes={audienceSlug:r.a.string.isRequired,errors:r.a.array.isRequired}}).call(this,n(3))},486:function(e,t,n){"use strict";(function(e){var i=n(0),r=n.n(i),a=n(1),o=n(2),c=n(34),l=n(487),s=n(144),u=n(361),d=Object(a.forwardRef)((function(t,n){var i=t.errors,r=t.onRetry,a=t.onRequestAccess,d=i.some((function(e){return Object(c.e)(e)}));return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error",ref:n},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__container"},e.createElement(l.a,{className:"googlesitekit-audience-segmentation-tile-error__image"}),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__body"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__message"},e.createElement("h3",{className:"googlesitekit-audience-segmentation-tile-error__title"},d?Object(o.__)("Insufficient permissions","google-site-kit"):Object(o.__)("Data loading failed","google-site-kit"))),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__actions"},e.createElement(s.a,{moduleSlug:"analytics-4",error:i,GetHelpLink:d?u.a:void 0,hideGetHelpLink:!d,buttonVariant:"danger",onRetry:r,onRequestAccess:a})))))}));d.propTypes={errors:r.a.array.isRequired,onRetry:r.a.func.isRequired,onRequestAccess:r.a.func.isRequired},t.a=d}).call(this,n(3))},487:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M11.755 110.799a44.227 44.227 0 004.457 4.095c14.958 11.965 27.316 11.946 44.424 9.762 15.862-2.025 17.862-10.923 35.362-10.923 17.5 0 25.665 6.38 46 5s38.081-23.761 44.757-41.774c9.833-26.528-4.519-57.596-24.82-66.096-18.699-8.5-31.437.01-51.437-3.63C84.998 2.59 79.998-4.051 60.636 4c-20.53 8.701-20.455 23.533-32.699 38.667C18.974 53.747 4.956 56.312.734 76.959c-2.673 13.07 3.077 25.467 11.021 33.84z",fill:"#F3F5F7"}),o=i.createElement("path",{d:"M96.07 141.772c39.765 0 72-2.014 72-4.5 0-2.485-32.235-4.5-72-4.5-39.764 0-72 2.015-72 4.5 0 2.486 32.236 4.5 72 4.5z",fill:"#161B18",opacity:.1}),c=i.createElement("path",{d:"M96.07 141.772c39.765 0 72-2.014 72-4.5 0-2.485-32.235-4.5-72-4.5-39.764 0-72 2.015-72 4.5 0 2.486 32.236 4.5 72 4.5z",fill:"#CBD0D3"}),l=i.createElement("path",{d:"M72.657 62.826c2.14 7.94 9.59 13.553 17.488 15.847 7.898 2.295 16.295 1.846 24.51 1.435 3.883-.192 7.855-.363 11.576.764 3.72 1.127 7.202 3.821 8.191 7.58.568 2.16.147 4.49-.959 6.424",stroke:"#161B18",strokeWidth:1.7,strokeLinecap:"round",strokeLinejoin:"round"}),s=i.createElement("path",{d:"M57.306 76.348c-1.237 19.582-8.053 37.15-3.497 59.224h-5.188M63.171 135.572h-5.19l9.173-59.224",stroke:"#161B18",strokeWidth:1.705,strokeLinecap:"round",strokeLinejoin:"round"}),u=i.createElement("path",{d:"M40.754 70.174c-4.429 2-12.93 6.455-12.929 15.5",stroke:"#000",strokeWidth:1.7,strokeLinecap:"round"}),d=i.createElement("path",{d:"M76.804 102.64l-3.602.327-21.976 1.988-12.845 1.16-1.22-28.616-2.118-49.8 40.092-1.46.81 37.088.109 4.993.745 34.204.005.116z",fill:"#CBD0D3"}),g=i.createElement("path",{d:"M73.203 102.967l2.067-.188 1.537-.139-1.671-76.403-2.303.105 1.24 72.43-35.862 3.393.125 2.852.048 1.095 12.845-1.159 21.977-1.989-.003.003z",fill:"#999F9B"}),f=i.createElement("path",{d:"M27.827 85.674c0 5.264 5.99 9.06 10.262 7.203",stroke:"#000",strokeWidth:1.7,strokeLinecap:"round"}),m=i.createElement("path",{d:"M145.07 26.773l-28 108.499",stroke:"#7B807D",strokeWidth:4.393,strokeMiterlimit:10,strokeLinecap:"round"}),p=i.createElement("path",{d:"M143.121 18.374L101.519 60.22a4.387 4.387 0 00-1.124 4.247 4.395 4.395 0 003.116 3.096l57.038 15.105a4.394 4.394 0 005.365-5.402l-15.439-56.943a4.393 4.393 0 00-7.354-1.949z",fill:"#E77D5B"}),b=i.createElement("path",{d:"M138.137 42.556l2.44-9.216 4.756 1.26-2.44 9.215-3.501 11.163-3.78-1.001 2.525-11.421zm-3.301 22.078a3.372 3.372 0 01-2.088-1.553 3.37 3.37 0 01-.316-2.584c.24-.906.753-1.588 1.541-2.046a3.375 3.375 0 012.584-.316c.906.24 1.588.754 2.046 1.542.458.788.567 1.635.327 2.54a3.375 3.375 0 01-1.553 2.09c-.788.457-1.635.567-2.541.327z",fill:"#962C0A"}),v=i.createElement("path",{d:"M133.461 94.876a8.345 8.345 0 01-4.565 3.774c-4.063 1.39-9.013-.82-10.694-4.77",stroke:"#161B18",strokeWidth:1.7,strokeLinecap:"round",strokeLinejoin:"round"});t.a=function SvgAnalyticsAudienceSegmentationTileError(e){return i.createElement("svg",r({viewBox:"0 0 190 142",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,b,v)}},488:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTile}));var i=n(10),r=n.n(i),a=n(0),o=n.n(a),c=n(2),l=n(4),s=n(23),u=n(39),d=n(8),g=n(489),f=n(490),m=n(491),p=n(492),b=n(493),v=n(494),h=n(318),j=n(495),I=n(496),y=n(193),E=n(137),O=n(364),M=n(9),k=n(257),N=n(18),_=n(499);function AudienceTile(t){var n=t.audienceTileNumber,i=void 0===n?0:n,a=t.audienceSlug,o=t.title,D=t.infoTooltip,A=t.visitors,T=t.visitsPerVisitor,S=t.pagesPerVisit,w=t.pageviews,C=t.percentageOfTotalPageViews,z=t.topCities,R=t.topContent,x=t.topContentTitles,P=t.hasInvalidCustomDimensionError,L=t.Widget,B=t.audienceResourceName,G=t.isZeroData,W=t.isPartialData,Z=t.isTileHideable,V=t.onHideTile,U=Object(s.e)(),F=Object(N.a)(),H=Object(u.a)(),Y=Object(l.useInViewSelect)((function(e){var t=e(d.r).getPropertyID();return t&&e(d.r).isPropertyPartialData(t)})),Q=Object(l.useSelect)((function(e){return e(d.r).isSiteKitAudience(B)})),X=Object(l.useInViewSelect)((function(e){return!Q&&void 0!==Y&&(!Y&&B&&e(d.r).isAudiencePartialData(B))}),[Y,Q,B]),J=Object(l.useInViewSelect)((function(e){return void 0!==Y&&(!Y&&!X&&e(d.r).isCustomDimensionPartialData("googlesitekit_post_type"))}),[X]),K=Object(l.useInViewSelect)((function(e){return e(d.r).hasCustomDimensions("googlesitekit_post_type")}),[]),q=[s.b,s.c].includes(U);return W&&G?e.createElement(_.a,{Widget:L,audienceSlug:a,title:o,infoTooltip:D,isMobileBreakpoint:q,isTileHideable:Z,onHideTile:V}):e.createElement(L,{noPadding:!0},e.createElement("div",{className:r()("googlesitekit-audience-segmentation-tile",{"googlesitekit-audience-segmentation-tile--partial-data":X})},!q&&e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header-title"},o,D&&e.createElement(E.a,{title:D,tooltipClassName:"googlesitekit-info-tooltip__content--audience",onOpen:function(){return Object(M.I)("".concat(F,"_audiences-tile"),"view_tile_tooltip",a)}})),X&&e.createElement(k.a,{className:"googlesitekit-audience-segmentation-partial-data-badge",label:Object(c.__)("Partial data","google-site-kit"),tooltipTitle:Object(c.__)("Still collecting full data for this timeframe, partial data is displayed for this group","google-site-kit"),onTooltipOpen:function(){Object(M.I)("".concat(F,"_audiences-tile"),"view_tile_partial_data_tooltip",a)}})),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__metrics"},q&&X&&e.createElement(O.a,{content:Object(c.__)("Still collecting full data for this timeframe, partial data is displayed for this group","google-site-kit")}),e.createElement(h.a,{TileIcon:g.a,title:Object(c.__)("Visitors","google-site-kit"),metricValue:A.currentValue,Badge:function Badge(){return e.createElement(y.a,{previousValue:A.previousValue,currentValue:A.currentValue})}}),e.createElement(h.a,{TileIcon:f.a,title:Object(c.__)("Visits per visitor","google-site-kit"),metricValue:T.currentValue,Badge:function Badge(){return e.createElement(y.a,{previousValue:T.previousValue,currentValue:T.currentValue})}}),e.createElement(h.a,{TileIcon:m.a,title:Object(c.__)("Pages per visit","google-site-kit"),metricValue:S.currentValue,Badge:function Badge(){return e.createElement(y.a,{previousValue:S.previousValue,currentValue:S.currentValue})},metricValueFormat:{style:"decimal",maximumFractionDigits:2}}),e.createElement(h.a,{TileIcon:p.a,title:Object(c.sprintf)(/* translators: %s: is a percentage value such as 33.3%. */
Object(c.__)("%s of total pageviews","google-site-kit"),Object(M.B)(C,{style:"percent",maximumFractionDigits:1})),metricValue:w.currentValue,Badge:function Badge(){return e.createElement(y.a,{previousValue:w.previousValue,currentValue:w.currentValue})}}),e.createElement(j.a,{TileIcon:b.a,title:Object(c.__)("Cities with the most visitors","google-site-kit"),topCities:z}),(!H||K&&!P)&&e.createElement(I.a,{audienceTileNumber:i,audienceSlug:a,TileIcon:v.a,title:Object(c.__)("Top content by pageviews","google-site-kit"),topContentTitles:x,topContent:R,isTopContentPartialData:J}))))}AudienceTile.propTypes={audienceTileNumber:o.a.number,audienceSlug:o.a.string.isRequired,title:o.a.string.isRequired,infoTooltip:o.a.oneOfType([o.a.string,o.a.element]),visitors:o.a.object,visitsPerVisitor:o.a.object,pagesPerVisit:o.a.object,pageviews:o.a.object,percentageOfTotalPageViews:o.a.number,topCities:o.a.object,topContent:o.a.object,topContentTitles:o.a.object,hasInvalidCustomDimensionError:o.a.bool,Widget:o.a.elementType.isRequired,audienceResourceName:o.a.string.isRequired,isZeroData:o.a.bool,isPartialData:o.a.bool,isTileHideable:o.a.bool,onHideTile:o.a.func}}).call(this,n(3))},489:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M.833 16.667v-2.334c0-.472.118-.902.354-1.291a2.48 2.48 0 01.98-.917c.86-.43 1.735-.75 2.624-.958a11.126 11.126 0 012.709-.333c.916 0 1.82.11 2.708.333.889.208 1.764.528 2.625.958.403.209.722.514.958.917.25.389.375.82.375 1.292v2.333H.833zm15 0v-2.5c0-.611-.174-1.195-.52-1.75-.334-.57-.813-1.056-1.438-1.458.708.083 1.375.229 2 .437a9.852 9.852 0 011.75.73c.5.277.882.59 1.145.937.264.333.396.701.396 1.104v2.5h-3.333zM7.5 10a3.21 3.21 0 01-2.354-.979 3.21 3.21 0 01-.98-2.354c0-.917.327-1.702.98-2.354a3.21 3.21 0 012.354-.98 3.21 3.21 0 012.354.98 3.21 3.21 0 01.979 2.354 3.21 3.21 0 01-.98 2.354 3.21 3.21 0 01-2.353.98zm8.333-3.333a3.21 3.21 0 01-.98 2.354 3.21 3.21 0 01-2.353.98c-.153 0-.348-.015-.584-.042a6.732 6.732 0 01-.583-.125c.375-.445.66-.938.854-1.48a4.662 4.662 0 00.313-1.687c0-.583-.104-1.146-.313-1.688a4.784 4.784 0 00-.854-1.479c.194-.07.389-.11.583-.125a4.12 4.12 0 01.584-.042 3.21 3.21 0 012.354.98 3.21 3.21 0 01.979 2.354zM2.5 15h10v-.666a.735.735 0 00-.125-.417.737.737 0 00-.292-.292 10.446 10.446 0 00-2.27-.833 9.342 9.342 0 00-4.626 0c-.764.18-1.52.458-2.27.833a.894.894 0 00-.313.292.843.843 0 00-.104.417V15zm5-6.666c.458 0 .847-.16 1.166-.48.334-.333.5-.729.5-1.187 0-.458-.166-.847-.5-1.167-.32-.333-.708-.5-1.166-.5-.459 0-.854.167-1.188.5-.32.32-.479.708-.479 1.167 0 .458.16.854.48 1.187.333.32.728.48 1.187.48z",fill:"currentColor"});t.a=function SvgAudienceMetricIconVisitors(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},49:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var i={BOXES:"boxes",COMPOSITE:"composite"},r={QUARTER:"quarter",HALF:"half",FULL:"full"},a="core/widgets"},490:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M10 17.5a7.443 7.443 0 01-2.938-.583 8.045 8.045 0 01-2.375-1.605 8.045 8.045 0 01-1.604-2.374A7.443 7.443 0 012.5 10c0-1.042.194-2.014.583-2.917a7.7 7.7 0 011.604-2.375 7.548 7.548 0 012.375-1.604A7.221 7.221 0 0110 2.5c1.139 0 2.215.243 3.23.73a7.252 7.252 0 012.603 2.062V3.333H17.5v5h-5V6.667h2.292a6.194 6.194 0 00-2.104-1.834A5.625 5.625 0 0010 4.167c-1.625 0-3.007.57-4.146 1.708C4.73 7 4.167 8.375 4.167 10s.562 3.007 1.687 4.146c1.14 1.125 2.521 1.687 4.146 1.687 1.458 0 2.73-.472 3.813-1.416 1.097-.945 1.743-2.14 1.937-3.584h1.708c-.208 1.903-1.027 3.493-2.458 4.771-1.417 1.264-3.083 1.896-5 1.896zm2.333-4l-3.166-3.167v-4.5h1.666v3.834l2.667 2.666-1.167 1.167z",fill:"currentColor"});t.a=function SvgAudienceMetricIconVisitsPerVisitor(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},491:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M13.125 16.667H3.334c-.459 0-.855-.16-1.188-.48-.32-.333-.479-.729-.479-1.187V5c0-.458.16-.847.48-1.167.332-.333.728-.5 1.187-.5h13.333c.458 0 .847.167 1.167.5.333.32.5.709.5 1.167v10c0 .459-.167.854-.5 1.188-.32.32-.709.479-1.167.479H15.5l-3.916-3.917c-.292.195-.611.34-.959.438-.333.097-.68.146-1.041.146-1.042 0-1.93-.362-2.667-1.084-.722-.736-1.083-1.625-1.083-2.666 0-1.042.36-1.924 1.083-2.646a3.633 3.633 0 012.667-1.104c1.041 0 1.923.368 2.646 1.104.736.722 1.104 1.604 1.104 2.646 0 .375-.049.729-.146 1.062a3.393 3.393 0 01-.438.938L16.167 15h.5V5H3.334v10h8.125l1.666 1.667zm-3.541-5c.583 0 1.076-.202 1.479-.604.403-.403.604-.896.604-1.48 0-.583-.201-1.076-.604-1.479a2.012 2.012 0 00-1.48-.604c-.583 0-1.076.202-1.479.604a2.012 2.012 0 00-.604 1.48c0 .583.202 1.076.604 1.479.403.402.896.604 1.48.604zM3.334 15V5v10z",fill:"currentColor"});t.a=function SvgAudienceMetricIconPagesPerVisit(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},492:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M4.167 17.5c-.459 0-.854-.16-1.188-.48-.32-.332-.479-.728-.479-1.187V4.167c0-.459.16-.848.48-1.167.333-.333.728-.5 1.187-.5h11.666c.459 0 .848.167 1.167.5.333.32.5.708.5 1.167v11.666c0 .459-.167.854-.5 1.188-.32.32-.708.479-1.167.479H4.167zm0-1.667h11.666v-10H4.167v10zM10 14.167c-1.139 0-2.16-.306-3.063-.917A5.398 5.398 0 015 10.833a5.271 5.271 0 011.938-2.396C7.84 7.814 8.86 7.5 10 7.5c1.139 0 2.153.313 3.042.938A5.229 5.229 0 0115 10.832a5.353 5.353 0 01-1.958 2.417c-.89.611-1.903.917-3.042.917zm0-1.25c.778 0 1.486-.18 2.125-.542a4 4 0 001.5-1.542 3.854 3.854 0 00-1.5-1.52A4.12 4.12 0 0010 8.75a4.12 4.12 0 00-2.125.563 3.854 3.854 0 00-1.5 1.52 4 4 0 001.5 1.542 4.243 4.243 0 002.125.542zm0-.834a1.26 1.26 0 01-.896-.354 1.26 1.26 0 01-.354-.896c0-.347.118-.639.354-.875.25-.25.549-.375.896-.375s.639.125.875.375c.25.236.375.528.375.875 0 .348-.125.646-.375.896a1.189 1.189 0 01-.875.354z",fill:"currentColor"});t.a=function SvgAudienceMetricIconPageviews(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},493:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M10 18.333a.776.776 0 01-.5-.166 1.012 1.012 0 01-.312-.438 11.597 11.597 0 00-1-2.188c-.39-.68-.938-1.479-1.646-2.395-.708-.917-1.285-1.792-1.73-2.625-.43-.834-.645-1.84-.645-3.021 0-1.625.562-3 1.687-4.125C6.994 2.235 8.375 1.667 10 1.667s3 .569 4.125 1.708c1.14 1.125 1.709 2.5 1.709 4.125 0 1.264-.243 2.32-.73 3.166-.472.834-1.02 1.66-1.645 2.48-.75 1-1.32 1.833-1.709 2.5a11.765 11.765 0 00-.937 2.083.94.94 0 01-.334.458.814.814 0 01-.479.146zm0-2.979c.236-.472.5-.938.792-1.396.306-.458.75-1.07 1.333-1.833a19.693 19.693 0 001.459-2.146c.389-.667.583-1.493.583-2.48 0-1.152-.41-2.131-1.23-2.937-.805-.82-1.784-1.229-2.937-1.229-1.152 0-2.139.41-2.958 1.23-.806.805-1.208 1.784-1.208 2.937 0 .986.187 1.812.562 2.479.39.653.882 1.368 1.48 2.146.583.764 1.02 1.375 1.312 1.833.305.458.576.924.812 1.396zm0-5.77c.584 0 1.077-.202 1.48-.605.402-.403.604-.896.604-1.48 0-.582-.202-1.076-.604-1.478A2.012 2.012 0 0010 5.417c-.583 0-1.076.2-1.479.604A2.012 2.012 0 007.917 7.5c0 .583.201 1.076.604 1.479.403.403.896.604 1.48.604z",fill:"currentColor"});t.a=function SvgAudienceMetricIconCities(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},494:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M3.334 16.667c-.459 0-.855-.16-1.188-.48-.32-.333-.479-.729-.479-1.187V5c0-.458.16-.847.48-1.167.332-.333.728-.5 1.187-.5h13.333c.458 0 .847.167 1.167.5.333.32.5.709.5 1.167v10c0 .459-.167.854-.5 1.188-.32.32-.709.479-1.167.479H3.334zm0-1.667h8.75v-2.916h-8.75V15zm10.416 0h2.917V7.5H13.75V15zM3.334 10.417h8.75V7.5h-8.75v2.917z",fill:"currentColor"});t.a=function SvgAudienceMetricIconTopContent(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},495:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileCitiesMetric}));var i=n(0),r=n.n(i),a=n(9),o=n(363);function AudienceTileCitiesMetric(t){var n,i=t.TileIcon,r=t.title,c=t.topCities,l=(null==c||null===(n=c.dimensionValues)||void 0===n?void 0:n.filter(Boolean))||[],s=!!l.length;return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric googlesitekit-audience-segmentation-tile-metric--cities"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__icon"},e.createElement(i,null)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__container"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__title"},r),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__content"},!s&&e.createElement(o.a,null),s&&l.map((function(t,n){var i;return e.createElement("div",{key:null==t?void 0:t.value,className:"googlesitekit-audience-segmentation-tile-metric__cities-metric"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__cities-metric-name"},null==t?void 0:t.value),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__cities-metric-value"},Object(a.B)((null==c||null===(i=c.metricValues[n])||void 0===i?void 0:i.value)/(null==c?void 0:c.total),{style:"percent",maximumFractionDigits:1})))})))))}AudienceTileCitiesMetric.propTypes={TileIcon:r.a.elementType.isRequired,title:r.a.string.isRequired,topCities:r.a.object}}).call(this,n(3))},496:function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return AudienceTilePagesMetric}));var r=n(0),a=n.n(r),o=n(1),c=n(176),l=n(2),s=n(4),u=n(23),d=n(30),g=n(13),f=n(7),m=n(8),p=n(34),b=n(257),v=n(497),h=n(253),j=n(91),I=n(18),y=n(9);function AudienceTilePagesMetric(t){var n=t.audienceTileNumber,r=t.audienceSlug,a=t.TileIcon,E=t.title,O=t.topContent,M=t.topContentTitles,k=t.isTopContentPartialData,N=Object(u.e)(),_=Object(I.a)(),D=m.f.googlesitekit_post_type.parameterName,A=Object(s.useSelect)((function(e){return!e(m.r).hasCustomDimensions(D)})),T=Object(s.useSelect)((function(e){return e(f.a).hasScope(m.h)})),S=Object(c.a)(e.location.href,{notification:"audience_segmentation",widgetArea:j.AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION}),w=Object(c.a)(e.location.href,{widgetArea:j.AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION}),C=Object(s.useSelect)((function(e){return e(d.a).getValue(m.d,"isAutoCreatingCustomDimensionsForAudience")})),z=Object(s.useSelect)((function(e){return e(m.r).isCreatingCustomDimension(D)})),R=Object(s.useSelect)((function(e){return e(m.r).isFetchingSyncAvailableCustomDimensions()})),x=Object(s.useSelect)((function(e){return e(m.r).getCreateCustomDimensionError(D)})),P=Object(s.useSelect)((function(e){return e(m.r).getPropertyID()})),L=Object(s.useDispatch)(m.r).clearError,B=Object(s.useDispatch)(d.a).setValues,G=Object(s.useDispatch)(f.a),W=G.setPermissionScopeError,Z=G.clearPermissionScopeError,V=Object(s.useSelect)((function(e){return e(d.a).getValue(m.d,"isRetrying")})),U=Object(s.useSelect)((function(e){return e(d.a).getValue(m.d,"autoSubmit")})),F=Object(s.useSelect)((function(e){return e(g.c).getSetupErrorCode()})),H=Object(s.useDispatch)(g.c).setSetupErrorCode,Y=U&&"access_denied"===F,Q=Object(o.useCallback)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.isRetrying;B(m.d,{autoSubmit:!0,isRetrying:t}),T||W({code:p.a,message:Object(l.__)("Additional permissions are required to create new audiences in Analytics.","google-site-kit"),data:{status:403,scopes:[m.h],skipModal:!0,skipDefaultErrorNotifications:!0,redirectURL:S,errorRedirectURL:w}})}),[T,S,w,W,B]),X=Object(o.useCallback)((function(){B(m.d,{autoSubmit:!1,isRetrying:!1}),H(null),Z(),L("createCustomDimension",[P,m.f.googlesitekit_post_type])}),[L,Z,P,H,B]),J=[u.b,u.c].includes(N),K=C||z||R;return i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric googlesitekit-audience-segmentation-tile-metric--top-content"},i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__icon"},i.createElement(a,null)),i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__container"},i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__title"},E,!J&&k&&i.createElement(b.a,{className:"googlesitekit-audience-segmentation-partial-data-badge",label:Object(l.__)("Partial data","google-site-kit"),onTooltipOpen:function(){Object(y.I)("".concat(_,"_audiences-tile"),"view_top_content_partial_data_tooltip",r)},tooltipTitle:Object(l.__)("Still collecting full data for this timeframe, partial data is displayed for this metric","google-site-kit")})),i.createElement(v.a,{topContentTitles:M,topContent:O,isTopContentPartialData:k,hasCustomDimension:!A,onCreateCustomDimension:Q,isSaving:K}),0===n&&(x&&!K||V&&!C||Y)&&i.createElement(h.a,{apiErrors:[x],title:Object(l.__)("Failed to enable metric","google-site-kit"),description:Object(l.__)("Oops! Something went wrong. Retry enabling the metric.","google-site-kit"),onRetry:function(){return Q({isRetrying:!0})},onCancel:X,inProgress:K,hasOAuthError:Y,trackEventCategory:"".concat(_,"_audiences-top-content-cta")})))}AudienceTilePagesMetric.propTypes={audienceTileNumber:a.a.number,audienceSlug:a.a.string.isRequired,TileIcon:a.a.elementType.isRequired,title:a.a.string.isRequired,topContent:a.a.object,topContentTitles:a.a.object,isTopContentPartialData:a.a.bool}}).call(this,n(28),n(3))},497:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTilePagesMetricContent}));var i=n(0),r=n.n(i),a=n(2),o=n(4),c=n(23),l=n(7),s=n(8),u=n(363),d=n(21),g=n(364),f=n(9),m=n(127),p=n(18),b=n(39),v=n(498),h=Object(m.a)(v.a);function AudienceTilePagesMetricContent(t){var n,i=t.topContentTitles,r=t.topContent,m=t.isTopContentPartialData,v=t.hasCustomDimension,j=t.onCreateCustomDimension,I=t.isSaving,y=Object(p.a)(),E=Object(b.a)(),O=Object(c.e)(),M=[c.b,c.c].includes(O),k=(null==r||null===(n=r.dimensionValues)||void 0===n?void 0:n.filter(Boolean))||[],N=!!k.length,_=Object(o.useSelect)((function(e){return e(l.a).getDateRangeDates({offsetDays:s.g})}));function ContentLinkComponent(t){var n=t.content,r=i[null==n?void 0:n.value],a=null==n?void 0:n.value,c=Object(o.useSelect)((function(e){return E?null:e(s.r).getServiceReportURL("all-pages-and-screens",{filters:{unifiedPagePathScreen:a},dates:_})}));return E?e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__top-content-metric-name"},r):e.createElement(d.a,{href:c,title:r,external:!0,hideExternalIndicator:!0},r)}return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__content"},!v&&e.createElement(h,{onClick:function(){Object(f.I)("".concat(y,"_audiences-top-content-cta"),"create_custom_dimension").finally(j)},isSaving:I,onInView:function(){Object(f.I)("".concat(y,"_audiences-top-content-cta"),"view_cta")}}),v&&!N&&e.createElement(u.a,null),v&&N&&k.map((function(t,n){var i;return e.createElement("div",{key:null==t?void 0:t.value,className:"googlesitekit-audience-segmentation-tile-metric__page-metric-container"},e.createElement(ContentLinkComponent,{content:t}),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__page-metric-value"},Object(f.B)(null==r||null===(i=r.metricValues[n])||void 0===i?void 0:i.value)))})),M&&m&&e.createElement(g.a,{content:Object(a.__)("Still collecting full data for this timeframe, partial data is displayed for this metric","google-site-kit")}))}AudienceTilePagesMetricContent.propTypes={topContentTitles:r.a.object,topContent:r.a.object,isTopContentPartialData:r.a.bool,hasCustomDimension:r.a.bool,onCreateCustomDimension:r.a.func,isSaving:r.a.bool}}).call(this,n(3))},498:function(e,t,n){"use strict";(function(e){var i=n(0),r=n.n(i),a=n(1),o=n(2),c=n(11),l=Object(a.forwardRef)((function(t,n){var i=t.onClick,r=t.isSaving;return e.createElement("div",{ref:n,className:"googlesitekit-audience-segmentation-tile-metric__no-data"},Object(o.__)("No data to show","google-site-kit"),e.createElement("p",null,Object(o.__)("Update Analytics to track metric","google-site-kit")),e.createElement(c.SpinnerButton,{onClick:i,isSaving:r,disabled:r,danger:!0},Object(o.__)("Update","google-site-kit")))}));l.propTypes={onClick:r.a.func.isRequired,isSaving:r.a.bool},t.a=l}).call(this,n(3))},499:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileZeroData}));var i=n(0),r=n.n(i),a=n(18),o=n(127),c=n(9),l=n(500),s=Object(o.a)(l.a);function AudienceTileZeroData(t){var n=t.Widget,i=t.audienceSlug,r=t.title,o=t.infoTooltip,l=t.isMobileBreakpoint,u=t.isTileHideable,d=t.onHideTile,g=Object(a.a)();return e.createElement(s,{Widget:n,audienceSlug:i,title:r,infoTooltip:o,isMobileBreakpoint:l,isTileHideable:u,onHideTile:function(){Object(c.I)("".concat(g,"_audiences-tile"),"temporarily_hide",i).finally(d)},onInView:function(){Object(c.I)("".concat(g,"_audiences-tile"),"view_tile_collecting_data",i)}})}AudienceTileZeroData.propTypes={Widget:r.a.elementType.isRequired,audienceSlug:r.a.string.isRequired,title:r.a.string.isRequired,infoTooltip:r.a.oneOfType([r.a.string,r.a.element]),isMobileBreakpoint:r.a.bool,isTileHideable:r.a.bool,onHideTile:r.a.func}}).call(this,n(3))},500:function(e,t,n){"use strict";(function(e){var i=n(0),r=n.n(i),a=n(1),o=n(18),c=n(9),l=n(137),s=n(501),u=n(503),d=Object(a.forwardRef)((function(t,n){var i=t.Widget,r=t.audienceSlug,a=t.title,d=t.infoTooltip,g=t.isMobileBreakpoint,f=t.isTileHideable,m=t.onHideTile,p=Object(o.a)();return e.createElement(i,{ref:n,noPadding:!0},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__zero-data-container"},!g&&e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header-title"},a,d&&e.createElement(l.a,{title:d,tooltipClassName:"googlesitekit-info-tooltip__content--audience",onOpen:function(){return Object(c.I)("".concat(p,"_audiences-tile"),"view_tile_tooltip",r)}}))),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__zero-data-content"},e.createElement(s.a,null),f&&e.createElement(u.a,{onHideTile:m})))))}));d.propTypes={Widget:r.a.elementType.isRequired,audienceSlug:r.a.string.isRequired,title:r.a.string.isRequired,infoTooltip:r.a.oneOfType([r.a.string,r.a.element]),isMobileBreakpoint:r.a.bool,isTileHideable:r.a.bool,onHideTile:r.a.func},t.a=d}).call(this,n(3))},501:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileCollectingData}));var i=n(1),r=n(2),a=n(502);function AudienceTileCollectingData(){return e.createElement(i.Fragment,null,e.createElement(a.a,{className:"googlesitekit-audience-segmentation-tile__zero-data-image"}),e.createElement("p",{className:"googlesitekit-audience-segmentation-tile__zero-data-description"},Object(r.__)("Site Kit is collecting data for this group.","google-site-kit")))}}).call(this,n(3))},502:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M54.323 93.529c21.908 0 39.667-17.76 39.667-39.667 0-21.908-17.76-39.667-39.667-39.667s-39.667 17.76-39.667 39.667 17.76 39.667 39.667 39.667z",fill:"#EBEEF0"}),o=i.createElement("path",{d:"M37.717 38.469l52.162 18.445a12.955 12.955 0 0016.533-7.896v0a12.948 12.948 0 00-3.553-13.955 12.952 12.952 0 00-4.349-2.582L87.023 28.42l-.014.042c.3-1.877.323-3.787.07-5.67",stroke:"#161B18",strokeWidth:2.578,strokeLinecap:"round",strokeLinejoin:"round"}),c=i.createElement("path",{d:"M70.947 38.469L18.785 56.914A12.955 12.955 0 012.25 49.018v0a12.955 12.955 0 017.902-16.537L21.64 28.42l.016.042a19.431 19.431 0 01-.07-5.668",stroke:"#161B18",strokeWidth:2.578,strokeLinecap:"round",strokeLinejoin:"round"}),l=i.createElement("path",{d:"M27.61.51l.497 83.126a39.625 39.625 0 0053.598-1.071l1.19-82.11L27.61.51z",fill:"#70B2F5"}),s=i.createElement("path",{d:"M27.61.51l.497 83.126a39.625 39.625 0 0053.598-1.071l1.19-82.11L27.61.51z",fill:"#77AD8C"}),u=i.createElement("path",{d:"M82.648 17.112l.24-16.66h-5.853l-1.033 86.633a39.782 39.782 0 005.702-4.526l.944-65.447z",fill:"#77AD8C",opacity:.2}),d=i.createElement("path",{d:"M44.723 46.377c4.916 3.946 11.868 4.892 19.218.273",stroke:"#161B18",strokeWidth:1.785,strokeLinecap:"round",strokeLinejoin:"round"}),g=i.createElement("path",{d:"M80.38 24.992c0-9.563-11.446-17.056-26.059-17.056-14.613 0-26.06 7.49-26.06 17.056h52.12z",fill:"#7B807D"}),f=i.createElement("path",{d:"M39.588 39.736c8.143 0 14.744-6.6 14.744-14.744 0-8.143-6.601-14.744-14.744-14.744s-14.744 6.601-14.744 14.744 6.601 14.744 14.744 14.744z",fill:"#fff",stroke:"#464B48",strokeWidth:4.363,strokeLinejoin:"round"}),m=i.createElement("path",{d:"M69.076 39.736c8.143 0 14.745-6.6 14.745-14.744 0-8.143-6.602-14.744-14.745-14.744-8.143 0-14.744 6.601-14.744 14.744s6.601 14.744 14.744 14.744z",fill:"#fff",stroke:"#464B48",strokeWidth:4.363,strokeLinejoin:"round"}),p=i.createElement("path",{d:"M30.86 24.992a8.739 8.739 0 018.726-8.726M60.348 24.992a8.738 8.738 0 018.726-8.726",stroke:"#B8BDB9",strokeWidth:3.173,strokeLinejoin:"round"});t.a=function SvgAudienceSegmentationCollectingData(e){return i.createElement("svg",r({viewBox:"0 0 109 94",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p)}},503:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileCollectingDataHideable}));var i=n(0),r=n.n(i),a=n(1),o=n(2),c=n(21),l=n(504);function AudienceTileCollectingDataHideable(t){var n=t.onHideTile;return e.createElement(a.Fragment,null,e.createElement("p",{className:"googlesitekit-audience-segmentation-tile__zero-data-description"},Object(o.__)("You can hide this group until data is available.","google-site-kit")),e.createElement(c.a,{className:"googlesitekit-audience-segmentation-tile-hide-cta",onClick:n,leadingIcon:e.createElement(l.a,{width:22,height:22}),secondary:!0,linkButton:!0},Object(o.__)("Temporarily hide","google-site-kit")))}AudienceTileCollectingDataHideable.propTypes={onHideTile:r.a.func.isRequired}}).call(this,n(3))},504:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M12.907 10.523l-1.088-1.088c.113-.587-.056-1.137-.506-1.65-.45-.512-1.031-.712-1.743-.6L8.482 6.098c.213-.1.425-.175.638-.225.225-.05.462-.075.712-.075.938 0 1.731.331 2.381.994.663.65.994 1.443.994 2.381 0 .25-.025.487-.075.712-.05.213-.125.425-.225.638zm2.4 2.362l-1.088-1.05a8.11 8.11 0 001.257-1.18 6.69 6.69 0 00.956-1.482 7.287 7.287 0 00-2.7-3c-1.162-.75-2.462-1.125-3.9-1.125-.362 0-.719.025-1.069.075-.35.05-.693.125-1.031.225L6.57 4.185a7.851 7.851 0 011.575-.468 8.22 8.22 0 011.687-.169c1.887 0 3.569.525 5.044 1.575a8.87 8.87 0 013.206 4.05 8.766 8.766 0 01-1.144 2.063 8.16 8.16 0 01-1.631 1.65zm.375 4.613l-3.15-3.113a9.803 9.803 0 01-1.331.32 9.675 9.675 0 01-1.369.093c-1.887 0-3.569-.519-5.044-1.556a8.983 8.983 0 01-3.206-4.07 8.663 8.663 0 01.994-1.837c.4-.575.856-1.087 1.369-1.537l-2.063-2.1 1.05-1.05 13.8 13.8-1.05 1.05zM4.995 6.848A8.054 8.054 0 004 7.917c-.3.387-.556.806-.769 1.256a7.46 7.46 0 002.681 3.019c1.175.737 2.482 1.106 3.919 1.106.25 0 .494-.013.731-.038.238-.037.481-.075.731-.112l-.675-.713a5.889 5.889 0 01-.393.094 3.96 3.96 0 01-.394.019c-.937 0-1.737-.325-2.4-.975-.65-.662-.975-1.463-.975-2.4 0-.138.006-.269.019-.394.025-.125.056-.256.094-.394L4.995 6.848z",fill:"currentColor"});t.a=function SvgVisibility(e){return i.createElement("svg",r({viewBox:"0 0 19 19",fill:"none"},e),a)}},505:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MaybePlaceholderTile}));var i=n(0),r=n.n(i),a=n(185),o=n(506);function MaybePlaceholderTile(t){var n=t.Widget,i=t.loading,r=t.allTilesError,c=t.visibleAudienceCount;return!1!==r&&!i||1!==c?null:i?e.createElement(n,{noPadding:!0},e.createElement(a.a,null)):e.createElement(o.a,{Widget:n})}MaybePlaceholderTile.propTypes={Widget:r.a.elementType.isRequired,loading:r.a.bool.isRequired,allTilesError:r.a.bool,visibleAudienceCount:r.a.number.isRequired}}).call(this,n(3))},506:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PlaceholderTile}));var i=n(10),r=n.n(i),a=n(42),o=n(2),c=n(4),l=n(13),s=n(26),u=n(7),d=n(8),g=n(21),f=n(366),m=n(44);function PlaceholderTile(t){var n=t.Widget,i=Object(c.useSelect)((function(e){var t=e(u.a).getConfiguredAudiences();return e(d.r).getConfigurableAudiences().some((function(e){return"DEFAULT_AUDIENCE"!==e.audienceType&&!t.includes(e.name)}))})),p=Object(c.useSelect)((function(e){return e(l.c).getGoogleSupportURL({path:"/analytics/answer/12799087"})})),b=Object(c.useDispatch)(s.b).setValue,v=e.createElement(g.a,{href:p,secondary:!0,external:!0});return e.createElement(n,{className:"googlesitekit-audience-segmentation-tile-placeholder"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-placeholder__container"},e.createElement(f.a,{className:"googlesitekit-audience-segmentation-tile-placeholder__image"}),e.createElement("div",{className:r()("googlesitekit-audience-segmentation-tile-placeholder__body",{"googlesitekit-audience-segmentation-tile-placeholder__body--without-selectable-audiences":!i})},e.createElement("h3",{className:"googlesitekit-audience-segmentation-tile-placeholder__title"},i?Object(o.__)("Compare your group to other groups","google-site-kit"):Object(o.__)("Create more visitor groups","google-site-kit")),e.createElement("p",{className:"googlesitekit-audience-segmentation-tile-placeholder__description"},i?Object(a.a)(Object(o.__)("<SelectGroupLink>Select</SelectGroupLink> another group to compare with your current group or learn more about how to group site visitors in <AnalyticsLink>Analytics</AnalyticsLink>","google-site-kit"),{AnalyticsLink:v,SelectGroupLink:e.createElement(g.a,{onClick:function(){return b(m.i,!0)},secondary:!0})}):Object(a.a)(Object(o.__)("Learn more about how to group site visitors in <AnalyticsLink>Analytics</AnalyticsLink>","google-site-kit"),{AnalyticsLink:v})))))}}).call(this,n(3))},507:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Header}));var i=n(11),r=n(4),a=n(8),o=n(9),c=n(18),l=n(365),s=n(137);function Header(t){var n=t.activeTileIndex,u=t.setActiveTile,d=t.visibleAudiences,g=Object(c.a)(),f=Object(r.useInViewSelect)((function(e){return e(a.r).getOrSyncAvailableAudiences()}),[]);return e.createElement(i.TabBar,{key:d.length,className:"googlesitekit-widget-audience-tiles__tabs googlesitekit-tab-bar--start-aligned-high-contrast",activeIndex:n,handleActiveIndexUpdate:function(e){return u(d[e])}},d.map((function(t,n){var r,a,c,u,d=(null==f||null===(r=f.filter((function(e){return e.name===t})))||void 0===r||null===(a=r[0])||void 0===a?void 0:a.displayName)||"",m=(null==f||null===(c=f.filter((function(e){return e.name===t})))||void 0===c||null===(u=c[0])||void 0===u?void 0:u.audienceSlug)||"",p=e.createElement(l.a,{audienceName:d,audienceSlug:m});return e.createElement(i.Tab,{key:n,"aria-label":d},d,e.createElement(s.a,{title:p,tooltipClassName:"googlesitekit-info-tooltip__content--audience",onOpen:function(){Object(o.I)("".concat(g,"_audiences-tile"),"view_tile_tooltip",m)}}))})))}}).call(this,n(3))},508:function(e,t,n){"use strict";(function(e){var i=n(1),r=n(42),a=n(2),o=n(4),c=n(366),l=n(21),s=n(19),u=n(367),d=n(33),g=n(13),f=n(26),m=n(7),p=n(44),b=n(18),v=n(39),h=n(9),j=n(29),I=Object(i.forwardRef)((function(t,n){var i=Object(b.a)(),I=Object(v.a)(),y=Object(o.useSelect)((function(e){return e(m.a).didSetAudiences()})),E=Object(o.useSelect)((function(e){return e(s.a).getModuleIcon(j.g)})),O=Object(o.useSelect)((function(e){return e(g.c).getSiteKitAdminSettingsURL()})),M=Object(o.useDispatch)(f.b).setValue,k=Object(o.useDispatch)(d.a).navigateTo,N=y?"no-longer-available":"none-selected";function _(){Object(h.I)("".concat(i,"_audiences-no-audiences"),"select_groups",N).finally((function(){M(p.i,!0)}))}return e.createElement(u.a,{ref:n,className:"googlesitekit-no-audience-banner",Icon:E,SVGGraphic:c.a},e.createElement("p",null,y&&Object(r.a)(Object(a.__)("It looks like your visitor groups aren’t available anymore. <a>Select other groups</a>.","google-site-kit"),{a:e.createElement(l.a,{onClick:_,secondary:!0})}),!y&&Object(r.a)(Object(a.__)("You don’t have any visitor groups selected. <a>Select groups</a>.","google-site-kit"),{a:e.createElement(l.a,{onClick:_,secondary:!0})})),!I&&e.createElement("p",null,Object(r.a)(Object(a.__)("You can deactivate this widget in <a>Settings</a>.","google-site-kit"),{a:e.createElement(l.a,{onClick:function(){Object(h.I)("".concat(i,"_audiences-no-audiences"),"change_settings",N).finally((function(){k(O)}))},secondary:!0})})))}));t.a=I}).call(this,n(3))},509:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Panel}));var i=n(5),r=n.n(i),a=n(1),o=n(4),c=n(18),l=n(9),s=n(44),u=n(30),d=n(26),g=n(7),f=n(8),m=n(510),p=n(518),b=n(520),v=n(521),h=n(522),j=n(126),I=n(523);function Panel(){var t=Object(c.a)(),n=Object(o.useSelect)((function(e){return e(d.b).getValue(s.i)})),i=Object(o.useSelect)((function(e){return e(f.r).isFetchingSyncAvailableAudiences()})),y=Object(o.useInViewSelect)((function(e){var t=e(f.r).getConfigurableAudiences,n=(0,e(g.a).getConfiguredAudiences)()||[],i=t()||[];return i.length&&n.length?i.filter((function(e){var t=e.name;return n.includes(t)})).map((function(e){return e.name})):[]})),E=Object(o.useSelect)((function(e){return e(u.a).getValue(s.c,"autoSubmit")})),O=Object(o.useDispatch)(u.a).setValues,M=Object(o.useDispatch)(d.b).setValue,k=Object(a.useCallback)((function(){var e;O(s.h,(e={},r()(e,s.f,y),r()(e,s.g,!1),e)),Object(l.I)("".concat(t,"_audiences-sidebar"),"audiences_sidebar_view")}),[y,O,t]),N=Object(a.useCallback)((function(){n&&(M(s.i,!1),M(s.e,!1))}),[M,n]);return e.createElement(j.e,{className:"googlesitekit-audience-selection-panel",closePanel:N,isOpen:n||E,isLoading:i,onOpen:k},e.createElement(v.a,{closePanel:N}),e.createElement(m.a,{savedItemSlugs:y}),e.createElement(h.a,null),e.createElement(p.a,null),e.createElement(I.a,null),e.createElement(b.a,{closePanel:N,isOpen:n,savedItemSlugs:y}))}}).call(this,n(3))},51:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r})),n.d(t,"e",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var i="warning-notification-gtg",r="gtg-setup-cta",a={ERROR_HIGH:30,ERROR_LOW:60,WARNING:100,INFO:150,SETUP_CTA_HIGH:150,SETUP_CTA_LOW:200},o={HEADER:"notification-area-header",DASHBOARD_TOP:"notification-area-dashboard-top",OVERLAYS:"notification-area-overlays"},c={DEFAULT:"default",SETUP_CTAS:"setup-ctas"}},510:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceItems}));var i=n(5),r=n.n(i),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(14),u=n.n(s),d=n(0),g=n.n(d),f=n(811),m=n(2),p=n(1),b=n(4),v=n(44),h=n(26),j=n(7),I=n(8),y=n(9),E=n(511),O=n(126),M=n(512),k=n(513),N=n(39),_=n(516);function D(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function A(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?D(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):D(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function AudienceItems(t){var n=t.savedItemSlugs,i=void 0===n?[]:n,a=Object(p.useState)(!0),c=u()(a,2),s=c[0],d=c[1],g=Object(b.useDispatch)(j.a).setExpirableItemTimers,D=Object(b.useDispatch)(I.r).syncAvailableAudiences,T=Object(N.a)(),S=Object(b.useSelect)((function(e){return e(h.b).getValue(v.i)})),w=Object(b.useSelect)((function(e){return e(I.r).isFetchingSyncAvailableAudiences()}));Object(p.useEffect)((function(){if(s&&S){var e=function(){var e=l()(o.a.mark((function e(){return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,D();case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();d(!1),e()}}),[s,S,D]);var C=Object(b.useInViewSelect)((function(e){var t=e(I.r),n=t.getConfigurableAudiences,i=t.getReport,r=t.getAudiencesUserCountReportOptions,a=t.getConfigurableSiteKitAndOtherAudiences,o=t.hasAudiencePartialData,c=n();if(void 0!==c){if(!c.length)return[];var l=a(),s=u()(l,2),d=s[0],g=s[1],f=o(d),m=e(j.a).getDateRangeDates({offsetDays:I.g}),p=f&&i(A(A({},m),{},{metrics:[{name:"totalUsers"}],dimensions:[{name:"newVsReturning"}]})),b=!1===f||!0===f&&(null==g?void 0:g.length)>0?i(r(f?g:c)):{},v=(p||{}).rows,h=void 0===v?[]:v,y=(b||{}).rows,E=void 0===y?[]:y;return c.map((function(e){var t,n,i,r;return r="SITE_KIT_AUDIENCE"===e.audienceType&&f?O(h,"new-visitors"===e.audienceSlug?"new":"returning"):O(E,e.name),A(A({},e),{},{userCount:Number(null===(t=r)||void 0===t||null===(n=t.metricValues)||void 0===n||null===(i=n[0])||void 0===i?void 0:i.value)||0})}))}function O(e,t){return e.find((function(e){var n,i;return(null==e||null===(n=e.dimensionValues)||void 0===n||null===(i=n[0])||void 0===i?void 0:i.value)===t}))}})),z=function(e,t){var n=t.audienceType,i=t.description,a=t.displayName,o=t.name,c=t.userCount,l="";switch(n){case"DEFAULT_AUDIENCE":l=Object(m.__)("Created by default by Google Analytics","google-site-kit"),i="";break;case"SITE_KIT_AUDIENCE":l=Object(m.__)("Created by Site Kit","google-site-kit");break;case"USER_AUDIENCE":l=Object(m.__)("Already exists in your Analytics property","google-site-kit")}return A(A({},e),{},r()({},o,{title:a,subtitle:i,description:l,userCount:c,audienceType:n}))},R=null==C?void 0:C.filter((function(e){var t=e.name;return i.includes(t)})).reduce(z,{}),x=null==C?void 0:C.filter((function(e){var t=e.name;return!i.includes(t)})).reduce(z,{}),P=Object(b.useSelect)((function(e){if(void 0!==C){var t=e(j.a),n=t.hasFinishedResolution,i=t.hasExpirableItem;if(n("getExpirableItems"))return C.filter((function(e){var t=e.audienceType,n=e.name;return"DEFAULT_AUDIENCE"!==t&&!i("".concat(I.b).concat(n))})).map((function(e){var t=e.name;return"".concat(I.b).concat(t)}))}}));return Object(f.a)((function(){S&&void 0!==P&&P.length&&g(P.map((function(e){return{slug:e,expiresInSeconds:4*y.f}})))}),[S,g,P]),e.createElement(O.d,{availableItemsTitle:Object(m.__)("Additional groups","google-site-kit"),availableSavedItems:R,availableUnsavedItems:x,ItemComponent:w?M.a:E.a,savedItemSlugs:i,notice:e.createElement(p.Fragment,null,e.createElement(k.a,null),!T&&e.createElement(_.a,null))})}AudienceItems.propTypes={savedItemSlugs:g.a.array}}).call(this,n(3))},511:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceItem}));var i=n(5),r=n.n(i),a=n(14),o=n.n(a),c=n(0),l=n.n(c),s=n(1),u=n(2),d=n(4),g=n(44),f=n(30),m=n(7),p=n(8),b=n(9),v=n(316),h=n(126),j=n(257);function AudienceItem(t){var n=t.slug,i=t.title,a=t.description,c=t.subtitle,l=t.userCount,I=t.audienceType,y="".concat(p.b).concat(n),E=Object(d.useSelect)((function(e){return e(f.a).getValue(g.h,g.f)})),O=Object(d.useSelect)((function(e){return e(m.a).hasExpirableItem(y)})),M=Object(d.useSelect)((function(e){return e(m.a).isExpirableItemActive(y)})),k=Object(d.useSelect)((function(e){return e(p.r).getAudienceUserCountReportErrors()}))||[],N=o()(k,2),_=N[0],D=N[1],A=[];D&&A.push(D),_&&A.push(_);var T=Object(d.useDispatch)(f.a).setValues,S=Object(d.useSelect)((function(e){return e(m.a).isItemDismissed("audience-tile-".concat(n))})),w=Object(s.useCallback)((function(e){var t;T(g.h,(t={},r()(t,g.f,e.target.checked?E.concat([n]):E.filter((function(e){return e!==n}))),r()(t,g.g,!0),t))}),[E,T,n]),C="DEFAULT_AUDIENCE"!==I&&(!1===O||M),z=null==E?void 0:E.includes(n),R="audience-selection-checkbox-".concat(n);function ItemBadge(){return S?e.createElement(j.a,{label:Object(u.__)("Temporarily hidden","google-site-kit"),tooltipTitle:Object(u.__)("Site Kit is collecting data for this group. Once data is available the group will be added to your dashboard.","google-site-kit")}):C?e.createElement(v.a,null):null}return e.createElement(h.c,{id:R,slug:n,title:i,subtitle:c,description:a,isItemSelected:z,onCheckboxChange:w,suffix:A.length?"-":Object(b.B)(l),badge:(S||C)&&e.createElement(ItemBadge,null)})}AudienceItem.propTypes={slug:l.a.string.isRequired,title:l.a.string.isRequired,description:l.a.string.isRequired,subtitle:l.a.string,userCount:l.a.number.isRequired,audienceType:l.a.string.isRequired}}).call(this,n(3))},512:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceItemPreviewBlock}));var i=n(48);function AudienceItemPreviewBlock(){return e.createElement("div",{className:"googlesitekit-selection-panel__loading"},e.createElement("div",{className:"googlesitekit-selection-panel__loading-left"},e.createElement(i.a,{width:"90px",height:"20px",className:"googlesitekit-selection-panel__loading-item"}),e.createElement(i.a,{width:"293px",height:"15px",className:"googlesitekit-selection-panel__loading-item"})),e.createElement("div",{className:"googlesitekit-selection-panel__loading-right"},e.createElement(i.a,{width:"43px",height:"20px",className:"googlesitekit-selection-panel__loading-item"})))}}).call(this,n(3))},513:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AddGroupNotice}));var i=n(6),r=n.n(i),a=n(16),o=n.n(a),c=n(14),l=n.n(c),s=n(0),u=n.n(s),d=n(1),g=n(2),f=n(4),m=n(44),p=n(30),b=n(7),v=n(514),h=n(254),j=n(26),I=n(8);function AddGroupNotice(){var t=Object(d.useState)(!1),n=l()(t,2),i=n[0],a=n[1],c=Object(f.useInViewSelect)((function(e){return e(b.a).isItemDismissed(m.a)})),s=Object(f.useSelect)((function(e){return e(j.b).getValue(m.i)})),u=Object(f.useSelect)((function(e){return e(I.r).isFetchingSyncAvailableAudiences()})),y=Object(f.useSelect)((function(e){return e(p.a).getValue(m.h,m.f)})),E=Object(f.useDispatch)(b.a).dismissItem,O=Object(d.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,E(m.a);case 2:case"end":return e.stop()}}),e)}))),[E]);return Object(d.useEffect)((function(){Array.isArray(y)&&(y.length>1&&a(!0),s||1!==(null==y?void 0:y.length)||a(!1))}),[y,s,a]),c||i||u||!(null==y?void 0:y.length)?null:e.createElement(h.a,{className:"googlesitekit-audience-selection-panel__add-group-notice",content:Object(g.__)("By adding another group to your dashboard, you will be able to compare them and understand which content brings back users from each group","google-site-kit"),dismissLabel:Object(g.__)("Got it","google-site-kit"),Icon:v.a,onDismiss:O})}AddGroupNotice.propTypes={savedItemSlugs:u.a.array}}).call(this,n(3))},514:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5 22c-1.522 0-2.952-.284-4.29-.852a11.303 11.303 0 01-3.493-2.366 11.303 11.303 0 01-2.365-3.492A10.86 10.86 0 01.5 11c0-1.522.284-2.952.853-4.29a11.302 11.302 0 012.364-3.493A10.92 10.92 0 017.21.88 10.567 10.567 0 0111.5 0c1.522 0 2.952.293 4.29.88a10.92 10.92 0 013.492 2.337c.99.99 1.77 2.155 2.338 3.493.587 1.338.88 2.768.88 4.29 0 1.522-.293 2.952-.88 4.29a10.92 10.92 0 01-2.338 3.492c-.99.99-2.154 1.779-3.492 2.366A10.86 10.86 0 0111.5 22zm0-14.3c.312 0 .569-.1.77-.303.22-.22.33-.485.33-.797a.999.999 0 00-.33-.77.999.999 0 00-.77-.33c-.311 0-.577.11-.797.33a1.043 1.043 0 00-.303.77c0 .312.101.578.303.798.22.201.486.302.797.302zm-1.1 8.8V9.9h2.2v6.6h-2.2z",fill:"currentColor"});t.a=function SvgInfoCircle(e){return i.createElement("svg",r({viewBox:"0 0 23 22",fill:"none"},e),a)}},515:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fill:"currentColor",d:"M10 18.333c-.458 0-.854-.16-1.188-.479a1.66 1.66 0 01-.479-1.188h3.334c0 .459-.167.855-.5 1.188-.32.32-.709.48-1.167.48zm-3.333-2.5v-1.667h6.666v1.667H6.667zm.208-2.5a6.47 6.47 0 01-2.292-2.292c-.555-.958-.833-2-.833-3.125 0-1.736.604-3.208 1.813-4.416C6.784 2.278 8.262 1.667 10 1.667c1.736 0 3.208.61 4.417 1.833 1.222 1.208 1.833 2.68 1.833 4.417a6.008 6.008 0 01-.854 3.124 6.303 6.303 0 01-2.271 2.292h-6.25zm.5-1.667h5.25a4.528 4.528 0 001.438-1.645c.347-.653.52-1.355.52-2.105 0-1.277-.444-2.36-1.333-3.25-.889-.888-1.972-1.333-3.25-1.333s-2.361.445-3.25 1.333c-.889.89-1.333 1.973-1.333 3.25 0 .75.166 1.452.5 2.105a4.722 4.722 0 001.458 1.645z"});t.a=function SvgLightbulb(e){return i.createElement("svg",r({fill:"none"},e),a)}},516:function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return AudienceCreationNotice}));var r=n(6),a=n.n(r),o=n(16),c=n.n(o),l=n(14),s=n.n(l),u=n(2),d=n(1),g=n(176),f=n(4),m=n(18),p=n(9),b=n(44),v=n(30),h=n(13),j=n(7),I=n(26),y=n(8),E=n(34),O=n(21),M=n(114),k=n(267),N=n(517),_=n(35);function AudienceCreationNotice(){var t=Object(m.a)(),n=Object(d.useState)(!1),r=s()(n,2),o=r[0],l=r[1],D=Object(f.useInViewSelect)((function(e){var t=(0,e(y.r).getConfigurableAudiences)();if(void 0!==t)return t.length?t.filter((function(e){return"SITE_KIT_AUDIENCE"===e.audienceType})):[]})),A=Object(f.useDispatch)(j.a).dismissItem,T=Object(f.useDispatch)(I.b).setValue,S=Object(f.useInViewSelect)((function(e){return e(j.a).isItemDismissed(b.d)})),w=Object(f.useInViewSelect)((function(e){return e(j.a).isItemDismissed(b.b)})),C=Object(f.useInViewSelect)((function(e){return e(j.a).hasScope(y.h)})),z=Object(f.useSelect)((function(e){return e(I.b).getValue(b.i)})),R=Object(g.a)(e.location.href,{notification:"audience_segmentation"}),x=Object(f.useDispatch)(v.a).setValues,P=Object(f.useDispatch)(j.a).setPermissionScopeError,L=Object(f.useDispatch)(y.r),B=L.createAudience,G=L.syncAvailableAudiences,W=Object(f.useSelect)((function(e){return e(v.a).getValue(b.c,"autoSubmit")})),Z=Object(f.useSelect)((function(e){return e(v.a).getValue(b.c,"audienceToCreate")})),V=Object(d.useState)([]),U=s()(V,2),F=U[0],H=U[1],Y=Object(d.useCallback)(function(){var e=c()(a.a.mark((function e(t){var n,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(l(t),C){e.next=5;break}return x(b.c,{autoSubmit:!0,audienceToCreate:t}),P({code:E.a,message:Object(u.__)("Additional permissions are required to create a new audience in Analytics.","google-site-kit"),data:{status:403,scopes:[y.h],skipModal:!0,redirectURL:R}}),e.abrupt("return");case 5:return x(b.c,{autoSubmit:!1,audienceToCreate:void 0}),e.next=8,B(y.t[t]);case 8:return n=e.sent,i=n.error,H(i?[i]:[]),e.next=13,G();case 13:l(!1),i||T(b.e,!0);case 15:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[C,B,G,x,P,R,T]),Q=Object(f.useSelect)((function(e){return e(h.c).getSetupErrorCode()})),X=W&&"access_denied"===Q;Object(d.useEffect)((function(){function e(){return(e=c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!C||!W){e.next=4;break}return T(b.i,!0),e.next=4,Y(Z);case 4:case"end":return e.stop()}}),e)})))).apply(this,arguments)}!function(){e.apply(this,arguments)}()}),[Z,Y,C,W,T]);var J=!S&&(null==D?void 0:D.length)<2;if(Object(d.useEffect)((function(){z&&J&&Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"view_notice")}),[z,J,t]),Object(d.useEffect)((function(){!z||C||w||Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"view_oauth_notice")}),[C,w,z,t]),!J)return null;var K=Object.keys(y.t).filter((function(e){return!D.some((function(t){return t.audienceSlug===e}))}));return i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice"},i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-header"},i.createElement("p",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-title"},Object(u.__)("Create groups suggested by Site Kit","google-site-kit")),i.createElement(O.a,{className:"googlesitekit-audience-selection-panel__audience-creation-notice-close",onClick:function(){A(b.d)},linkButton:!0},i.createElement(M.a,{width:"15",height:"15"}))),i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-body"},K&&K.map((function(e){return i.createElement("div",{key:e,className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience"},i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience-details"},i.createElement("h3",null,y.t[e].displayName),i.createElement("p",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience-description"},y.t[e].description)),i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience-button"},i.createElement(k.b,{spinnerPosition:k.a.BEFORE,onClick:function(){Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"create_audience",e).finally((function(){Y(e)}))},isSaving:o===e},Object(u.__)("Create","google-site-kit"))))}))),!C&&!w&&i.createElement(_.a,{type:_.a.TYPES.WARNING,description:Object(u.__)("Creating these groups require more data tracking. You will be directed to update your Analytics property.","google-site-kit"),dismissButton:{onClick:function(){Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"dismiss_oauth_notice").finally((function(){A(b.b)}))}},hideIcon:!0}),(F.length>0||X)&&i.createElement(N.a,{apiErrors:F,hasOAuthError:X}))}}).call(this,n(28),n(3))},517:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceCreationErrorNotice}));var i=n(0),r=n.n(i),a=n(1),o=n(42),c=n(2),l=n(4),s=n(18),u=n(34),d=n(9),g=n(44),f=n(13),m=n(26),p=n(8),b=n(21),v=n(35);function AudienceCreationErrorNotice(t){var n,i,r=t.apiErrors,h=t.hasOAuthError,j=Object(s.a)(),I=Array.isArray(r)?r:[r],y=Object(l.useSelect)((function(e){return e(f.c).getErrorTroubleshootingLinkURL({code:"analytics-4_insufficient_permissions"})})),E=Object(l.useSelect)((function(e){return e(p.r).getServiceEntityAccessURL()})),O=Object(l.useSelect)((function(e){return e(f.c).getErrorTroubleshootingLinkURL({code:"access_denied"})})),M=Object(l.useSelect)((function(e){return e(m.b).getValue(g.i)})),k=I.length>0,N=I.some((function(e){return Object(u.e)(e)}));return Object(a.useEffect)((function(){if(M&&(k||h)){var e="setup_error";h?e="auth_error":N&&(e="insufficient_permissions_error"),Object(d.I)("".concat(j,"_audiences-sidebar-create-audiences"),e)}}),[k,N,h,M,j]),I.length||h?(h?i=Object(o.a)(Object(c.__)("Setup was interrupted because you didn’t grant the necessary permissions. Click on Create again to retry. If that doesn’t work, <HelpLink />","google-site-kit"),{HelpLink:e.createElement(b.a,{href:O,external:!0,hideExternalIndicator:!0},Object(c.__)("get help","google-site-kit"))}):N?(n=Object(c.__)("Insufficient permissions","google-site-kit"),i=Object(o.a)(Object(c.__)("Contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(b.a,{href:y,external:!0,hideExternalIndicator:!0},Object(c.__)("Get help","google-site-kit"))})):(n=Object(c.__)("Analytics update failed","google-site-kit"),i=Object(c.__)("Click on Create to try again.","google-site-kit")),e.createElement(v.a,{className:"googlesitekit-audience-creation-error-notice",type:v.a.TYPES.ERROR,title:n,description:i,ctaButton:N?{label:Object(c.__)("Request access","google-site-kit"),href:E,onClick:function(){Object(d.I)("".concat(j,"_audiences-sidebar-create-audiences"),"insufficient_permissions_error_request_access")}}:void 0,hideIcon:!0})):null}AudienceCreationErrorNotice.propTypes={apiErrors:r.a.oneOfType([r.a.arrayOf(r.a.object),r.a.object,r.a.array]),hasOAuthError:r.a.bool}}).call(this,n(3))},518:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var i=n(6),r=n.n(i),a=n(16),o=n.n(a),c=n(14),l=n.n(c),s=n(1),u=n(42),d=n(2),g=n(4),f=n(18),m=n(9),p=n(44),b=n(13),v=n(26),h=n(8),j=n(34),I=n(21),y=n(144),E=n(519),O=n(411),M=n(35);function ErrorNotice(){var t=Object(f.a)(),n=Object(g.useSelect)((function(e){return e(h.r).getErrorForAction("syncAvailableAudiences")})),i=Object(g.useInViewSelect)((function(e){return e(h.r).getAudienceUserCountReportErrors()}))||[],a=l()(i,2),c=a[0],k=a[1],N=Object(g.useSelect)((function(e){return e(b.c).getErrorTroubleshootingLinkURL({code:"analytics-4_insufficient_permissions"})})),_=Object(g.useSelect)((function(e){return e(v.b).getValue(p.i)})),D=Object(g.useDispatch)(h.r),A=D.clearError,T=D.syncAvailableAudiences,S=Object(s.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,A("syncAvailableAudiences");case 2:T();case 3:case"end":return e.stop()}}),e)}))),[A,T]),w=[];n&&w.push(n),k&&w.push(k),c&&w.push(c);var C=w.length>0,z=w.some((function(e){return Object(j.e)(e)}));if(Object(s.useEffect)((function(){_&&C&&Object(m.I)("".concat(t,"_audiences-sidebar"),z?"insufficient_permissions_error":"data_loading_error")}),[C,z,_,t]),!w.length)return null;var R=[k,c].some((function(e){return!!e}));return e.createElement(M.a,{className:"googlesitekit-audience-selection-panel__error-notice googlesitekit-notice--error googlesitekit-notice--small googlesitekit-notice--square",type:M.a.TYPES.ERROR,description:z?Object(u.a)(Object(d.__)("Insufficient permissions, contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(I.a,{href:N,external:!0,hideExternalIndicator:!0},Object(d.__)("Get help","google-site-kit"))}):Object(d.__)("Data loading failed","google-site-kit"),hideIcon:!0},z||R?e.createElement(y.a,{moduleSlug:"analytics-4",error:w,buttonVariant:"danger",RequestAccessButton:E.a,RetryButton:O.a,hideGetHelpLink:!0}):e.createElement(O.a,{handleRetry:S}))}}).call(this,n(3))},519:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RequestAccessButton}));var i=n(0),r=n.n(i),a=n(2),o=n(11),c=n(9),l=n(18);function RequestAccessButton(t){var n=t.requestAccessURL,i=Object(l.a)();return e.createElement(o.Button,{className:"googlesitekit-audience-selection-panel__error-notice-action",href:n,target:"_blank",onClick:function(){Object(c.I)("".concat(i,"_audiences-sidebar"),"insufficient_permissions_error_request_access")},tertiary:!0},Object(a.__)("Request access","google-site-kit"))}RequestAccessButton.propTypes={requestAccessURL:r.a.string.isRequired}}).call(this,n(3))},520:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Footer}));var i=n(6),r=n.n(i),a=n(27),o=n.n(a),c=n(16),l=n.n(c),s=n(14),u=n.n(s),d=n(5),g=n.n(d),f=n(0),m=n.n(f),p=n(1),b=n(2),v=n(4),h=n(18),j=n(9),I=n(44),y=n(30),E=n(7),O=n(8),M=n(126);function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function N(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){g()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Footer(t){var n,i=t.isOpen,a=t.closePanel,c=t.savedItemSlugs,s=Object(h.a)(),d=Object(v.useSelect)((function(e){return e(y.a).getValue(I.h,I.f)})),g=Object(v.useInViewSelect)((function(e){return e(E.a).getUserAudienceSettings()})),f=Object(v.useSelect)((function(e){return e(E.a).getErrorForAction("saveUserAudienceSettings",[N(N({},g),{},{configuredAudiences:d})])})),m=Object(v.useSelect)((function(e){return e(E.a).isSavingUserAudienceSettings()})),k=Object(v.useInViewSelect)((function(e){var t=e(E.a).getDismissedItems();return null==t?void 0:t.filter((function(e){return e.startsWith("audience-tile-")}))})),_=Object(v.useSelect)((function(e){return e(O.r).getOrSyncAvailableAudiences()})),D=Object(v.useDispatch)(E.a),A=D.saveUserAudienceSettings,T=D.removeDismissedItems,S=Object(v.useSelect)(E.a).getConfiguredAudiences,w=(null==d?void 0:d.length)||0;w<I.k?n=Object(b.sprintf)(/* translators: 1: Minimum number of groups that can be selected. 2: Number of selected groups. */
Object(b._n)("Select at least %1$d group (%2$d selected)","Select at least %1$d groups (%2$d selected)",I.k,"google-site-kit"),I.k,w):w>I.j&&(n=Object(b.sprintf)(/* translators: 1: Maximum number of groups that can be selected. 2: Number of selected groups. */
Object(b.__)("Select up to %1$d groups (%2$d selected)","google-site-kit"),I.j,w));var C=Object(p.useState)(null),z=u()(C,2),R=z[0],x=z[1],P=Object(p.useCallback)(function(){var e=l()(r.a.mark((function e(t){var n,i,a,c;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return x(null),e.next=3,A({configuredAudiences:t});case 3:if(n=e.sent,i=n.error){e.next=14;break}if(a=(null==k?void 0:k.filter((function(e){var n=e.replace("audience-tile-","");return!t.includes(n)})))||[],t.every((function(e){return null==k?void 0:k.includes("audience-tile-".concat(e))}))&&a.push("audience-tile-".concat(t[0])),!((null==a?void 0:a.length)>0)){e.next=14;break}return e.next=11,T.apply(void 0,o()(a));case 11:c=e.sent,(i=c.error)&&x(i);case 14:return e.abrupt("return",{error:i});case 15:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[k,T,A]),L=Object(p.useCallback)((function(){var e={USER_AUDIENCE:"user",SITE_KIT_AUDIENCE:"site-kit",DEFAULT_AUDIENCE:"default"},t=S(),n=Object.keys(e).map((function(n){var i=t.filter((function(e){var t=null==_?void 0:_.find((function(t){var n=t.name;return e===n}));return(null==t?void 0:t.audienceType)===n}));return"".concat(e[n],":").concat(i.length)})).join(",");Object(j.I)("".concat(s,"_audiences-sidebar"),"audiences_sidebar_save",n)}),[_,S,s]),B=Object(p.useCallback)((function(){Object(j.I)("".concat(s,"_audiences-sidebar"),"audiences_sidebar_cancel")}),[s]);return e.createElement(M.a,{savedItemSlugs:c,selectedItemSlugs:d,saveSettings:P,saveError:f||R,itemLimitError:n,minSelectedItemCount:I.k,maxSelectedItemCount:I.j,isBusy:m,isOpen:i,closePanel:a,onSaveSuccess:L,onCancel:B})}Footer.propTypes={isOpen:m.a.bool,closePanel:m.a.func.isRequired,savedItemSlugs:m.a.array}}).call(this,n(3))},521:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Header}));var i=n(0),r=n.n(i),a=n(1),o=n(42),c=n(2),l=n(4),s=n(33),u=n(13),d=n(7),g=n(39),f=n(21),m=n(126);function Header(t){var n=t.closePanel,i=Object(g.a)(),r=Object(l.useSelect)((function(e){return e(u.c).getSiteKitAdminSettingsURL()})),p=Object(l.useSelect)((function(e){return e(d.a).isSavingUserAudienceSettings()})),b=Object(l.useDispatch)(s.a).navigateTo,v=Object(a.useCallback)((function(){return b(r)}),[r,b]);return e.createElement(m.b,{title:Object(c.__)("Select visitor groups","google-site-kit"),onCloseClick:n},!i&&e.createElement("p",null,Object(o.a)(Object(c.__)("You can deactivate this widget in <link><strong>Settings</strong></link>","google-site-kit"),{link:e.createElement(f.a,{onClick:v,disabled:p,secondary:!0}),strong:e.createElement("strong",null)})))}Header.propTypes={closePanel:r.a.func.isRequired}}).call(this,n(3))},522:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LearnMoreLink}));var i=n(2),r=n(42),a=n(4),o=n(13),c=n(21);function LearnMoreLink(){var t=Object(a.useSelect)((function(e){return e(o.c).getGoogleSupportURL({path:"/analytics/answer/12799087"})}));return e.createElement("div",{className:"googlesitekit-audience-selection-panel__learn-more"},Object(r.a)(Object(i.__)("Learn more about grouping site visitors and audiences in <link><strong>Analytics</strong></link>","google-site-kit"),{link:e.createElement(c.a,{href:t,secondary:!0,external:!0}),strong:e.createElement("strong",null)}))}}).call(this,n(3))},523:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceCreationSuccessNotice}));var i=n(1),r=n(2),a=n(4),o=n(18),c=n(9),l=n(44),s=n(26),u=n(11),d=n(111);function AudienceCreationSuccessNotice(){var t=Object(o.a)(),n=Object(a.useDispatch)(s.b).setValue,g=Object(a.useSelect)((function(e){return e(s.b).getValue(l.e)})),f=Object(a.useSelect)((function(e){return e(s.b).getValue(l.i)}));return Object(i.useEffect)((function(){f&&g&&Object(c.I)("".concat(t,"_audiences-sidebar-create-audiences-success"),"view_notification")}),[f,g,t]),g?e.createElement("div",{className:"googlesitekit-audience-selection-panel__success-notice"},e.createElement("div",{className:"googlesitekit-audience-selection-panel__success-notice-icon"},e.createElement(d.a,{width:24,height:24})),e.createElement("p",{className:"googlesitekit-audience-selection-panel__success-notice-message"},Object(r.__)("Visitor group created successfully!","google-site-kit")),e.createElement("div",{className:"googlesitekit-audience-selection-panel__success-notice-actions"},e.createElement(u.Button,{onClick:function(){Object(c.I)("".concat(t,"_audiences-sidebar-create-audiences-success"),"dismiss_notification").finally((function(){n(l.e,!1)}))},tertiary:!0},Object(r.__)("Got it","google-site-kit")))):null}}).call(this,n(3))},524:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("defs",null,i.createElement("filter",{id:"audience-connect-analytics-cta-graphic_svg__c",x:109.551,y:18.171,width:144.59,height:185.064,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1731_24094"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1731_24094",result:"shape"})),i.createElement("filter",{id:"audience-connect-analytics-cta-graphic_svg__d",x:236.859,y:18.171,width:144.59,height:185.064,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1731_24094"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1731_24094",result:"shape"})),i.createElement("clipPath",{id:"audience-connect-analytics-cta-graphic_svg__a"},i.createElement("path",{fill:"#fff",d:"M0 0h480v150H0z"}))),o=i.createElement("path",{d:"M91.722 36.579a71.937 71.937 0 017.307-6.582c24.521-19.234 44.779-19.204 72.826-15.693 18.961 2.373 30.038 11.4 55.889 9.98 25.851-1.42 32.474-7.992 64.117-5.887 25.048 1.667 36.285 6.612 58.554 18.182 20.61 10.707 39.324 29.519 48.728 54.397 16.12 42.644-12.622 119.393-51.166 123.012-27.93 2.623-50.979-28.308-79.169-21.145-17.366 4.414-27.666 22.927-41.064 35.144-15.631 14.255-49.304 13.359-67.607 5.751-17.442-7.248-34.409-21.615-40.106-42.775-4.337-16.114-5.519-35.322-17.661-50.04-14.694-17.811-23.672-25.756-28.716-49.947-4.382-21.009 5.045-40.938 18.068-54.397z",fill:"#B8E6CA"}),c=i.createElement("path",{d:"M91.722 36.579a71.937 71.937 0 017.307-6.582c24.521-19.234 44.779-19.204 72.826-15.693 18.961 2.373 30.038 11.4 55.889 9.98 25.851-1.42 32.474-7.992 64.117-5.887 25.048 1.667 36.285 6.612 58.554 18.182 20.61 10.707 39.324 29.519 48.728 54.397 16.12 42.644-12.622 119.393-51.166 123.012-27.93 2.623-50.979-28.308-79.169-21.145-17.366 4.414-27.666 22.927-41.064 35.144-15.631 14.255-49.304 13.359-67.607 5.751-17.442-7.248-34.409-21.615-40.106-42.775-4.337-16.114-5.519-35.322-17.661-50.04-14.694-17.811-23.672-25.756-28.716-49.947-4.382-21.009 5.045-40.938 18.068-54.397z",fill:"#B8E6CA"}),l=i.createElement("g",{mask:"url(#audience-connect-analytics-cta-graphic_svg__b)"},i.createElement("g",{filter:"url(#audience-connect-analytics-cta-graphic_svg__c)"},i.createElement("rect",{x:125.551,y:30.171,width:112.591,height:153.065,rx:8.095,fill:"#fff"})),i.createElement("rect",{x:139.555,y:93.193,width:39.014,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:139.555,y:82.189,width:14.005,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:139.555,y:118.259,width:14.005,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("path",{d:"M202.578 91.693a6.502 6.502 0 016.502-6.503h10.004a6.502 6.502 0 010 13.005H209.08a6.502 6.502 0 01-6.502-6.502z",fill:"#B8E6CA"}),i.createElement("rect",{x:139.535,y:45.625,width:26.492,height:6.623,rx:3.311,fill:"#EBEEF0"}),i.createElement("path",{d:"M202.578 127.763a6.502 6.502 0 016.502-6.502h10.004a6.502 6.502 0 110 13.004H209.08a6.502 6.502 0 01-6.502-6.502z",fill:"#FFDED3"}),i.createElement("rect",{x:138.555,y:129.263,width:41.014,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("path",{d:"M238.141 65.862H126.286",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("g",{filter:"url(#audience-connect-analytics-cta-graphic_svg__d)"},i.createElement("rect",{x:252.859,y:30.171,width:112.591,height:153.065,rx:8.095,fill:"#fff"})),i.createElement("rect",{x:266.809,y:93.193,width:38.859,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:266.809,y:82.189,width:13.949,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:266.805,y:118.259,width:13.949,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("path",{d:"M329.582 91.693a6.502 6.502 0 016.502-6.503h9.912a6.502 6.502 0 110 13.005h-9.912a6.502 6.502 0 01-6.502-6.502z",fill:"#B8E6CA"}),i.createElement("rect",{x:266.844,y:45.625,width:26.492,height:6.623,rx:3.311,fill:"#EBEEF0"}),i.createElement("path",{d:"M358.094 65.862H252.862",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M329.582 127.763a6.502 6.502 0 016.502-6.502h9.912a6.502 6.502 0 110 13.004h-9.912a6.502 6.502 0 01-6.502-6.502z",fill:"#FFDED3"}),i.createElement("rect",{x:265.812,y:129.263,width:40.852,height:7.002,rx:3.501,fill:"#EBEEF0"}));t.a=function SvgAudienceConnectAnalyticsCtaGraphic(e){return i.createElement("svg",r({viewBox:"-3 1 333.666 149.252",fill:"none"},e),a,i.createElement("g",{clipPath:"url(#audience-connect-analytics-cta-graphic_svg__a)",transform:"translate(-73)"},o,i.createElement("mask",{id:"audience-connect-analytics-cta-graphic_svg__b",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:72,y:12,width:332,height:228},c),l))}},525:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M67.728 34.59a71.982 71.982 0 017.31-6.584c24.527-19.24 44.792-19.21 72.848-15.698 18.966 2.374 30.047 11.404 55.905 9.984C229.65 20.872 234.5 10.117 271 14.308 307.5 18.5 322.5-1.5 360.5 1.5s52 32 72 42 37 8.5 50 35 4 83.5-36 106-99.355 25.386-122.439 27.553c-27.938 2.624-50.995-28.317-79.194-21.151-17.371 4.415-27.674 22.934-41.076 35.155-15.636 14.258-49.319 13.362-67.627 5.752-17.448-7.25-34.42-21.622-40.118-42.788-4.338-16.119-5.521-35.333-17.667-50.056-14.698-17.816-23.679-25.763-28.725-49.961-4.382-21.016 5.047-40.95 18.074-54.414z",fill:"#B8E6CA"}),o=i.createElement("path",{d:"M67.728 34.59a71.982 71.982 0 017.31-6.584c24.527-19.24 44.792-19.21 72.848-15.698 18.966 2.374 30.047 11.404 55.905 9.984C229.65 20.872 234.5 10.117 271 14.308 307.5 18.5 322.5-1.5 360.5 1.5s52 32 72 42 37 8.5 50 35 4 83.5-36 106-99.355 25.386-122.439 27.553c-27.938 2.624-50.995-28.317-79.194-21.151-17.371 4.415-27.674 22.934-41.076 35.155-15.636 14.258-49.319 13.362-67.627 5.752-17.448-7.25-34.42-21.622-40.118-42.788-4.338-16.119-5.521-35.333-17.667-50.056-14.698-17.816-23.679-25.763-28.725-49.961-4.382-21.016 5.047-40.95 18.074-54.414z",fill:"#B8E6CA"}),c=i.createElement("g",{filter:"url(#audience-connect-analytics-cta-graphic-tablet_svg__filter0_d_2898_16714)",mask:"url(#audience-connect-analytics-cta-graphic-tablet_svg__a)"},i.createElement("rect",{x:93,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:107.008,y:92.222,width:39.025,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:107.008,y:81.214,width:14.009,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:107.008,y:117.295,width:14.009,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M170.051 90.72a6.504 6.504 0 016.504-6.504h10.007a6.504 6.504 0 010 13.009h-10.007a6.504 6.504 0 01-6.504-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:106.984,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M170.051 126.802a6.504 6.504 0 016.504-6.505h10.007a6.504 6.504 0 010 13.009h-10.007a6.504 6.504 0 01-6.504-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:106.008,y:128.303,width:41.027,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M205.625 64.882H93.736",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("rect",{x:220.348,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:234.301,y:92.222,width:38.871,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:234.301,y:81.214,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:234.301,y:117.295,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M297.094 90.72a6.504 6.504 0 016.504-6.504h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.504-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:234.332,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M325.613 64.882H220.349",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M297.094 126.802a6.504 6.504 0 016.504-6.505h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.504-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:233.305,y:128.303,width:40.864,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:347.695,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:361.648,y:92.222,width:38.871,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:361.648,y:81.214,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:361.648,y:117.295,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M424.441 90.72a6.504 6.504 0 016.505-6.504h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.505-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:361.68,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M452.961 64.882H347.697",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M424.441 126.802a6.505 6.505 0 016.505-6.505h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.505-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:360.652,y:128.303,width:40.864,height:7.005,rx:3.502,fill:"#EBEEF0"})),l=i.createElement("defs",null,i.createElement("clipPath",{id:"audience-connect-analytics-cta-graphic-tablet_svg__clip0_2898_16714"},i.createElement("path",{fill:"#fff",d:"M0 0h553v158H0z"})),i.createElement("filter",{id:"audience-connect-analytics-cta-graphic-tablet_svg__filter0_d_2898_16714",x:77,y:17.181,width:399.32,height:185.111,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2898_16714"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2898_16714",result:"shape"})));t.a=function SvgAudienceConnectAnalyticsCtaGraphicTablet(e){return i.createElement("svg",r({viewBox:"0 0 553 146",fill:"none"},e),i.createElement("g",{clipPath:"url(#audience-connect-analytics-cta-graphic-tablet_svg__clip0_2898_16714)"},a,i.createElement("mask",{id:"audience-connect-analytics-cta-graphic-tablet_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:48,y:1,width:441,height:237},o),c),l)}},533:function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var i=n(1),r=n(4),a=n(13),o=n(7),c=n(2),l=n(24),s={slug:"sharedKeyMetrics",contexts:[l.n,l.o,l.l,l.m],gaEventCategory:function(e){return"".concat(e,"_shared_key-metrics")},steps:[{target:".googlesitekit-km-change-metrics-cta",title:Object(c.__)("Personalize your key metrics","google-site-kit"),content:Object(c.__)("Another admin has set up these tailored metrics for your site. Click here to personalize them.","google-site-kit"),placement:"bottom-start"}]},u=function(e){var t=Object(r.useSelect)((function(e){return e(a.c).getKeyMetricsSetupCompletedBy()})),n=Object(r.useSelect)((function(e){return e(o.a).getID()})),c=Object(r.useDispatch)(o.a).triggerOnDemandTour,l=Number.isInteger(t)&&Number.isInteger(n)&&t>0&&n!==t;Object(i.useEffect)((function(){e&&l&&c(s)}),[e,l,c])}},59:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var i=n(0),r=n.n(i),a=n(1),o=n(2),c=n(115),l=n(4),s=n(34),u=n(35),d=n(9);function ErrorNotice(t){var n,i=t.className,r=t.error,g=t.hasButton,f=void 0!==g&&g,m=t.storeName,p=t.message,b=void 0===p?r.message:p,v=t.noPrefix,h=void 0!==v&&v,j=t.skipRetryMessage,I=t.hideIcon,y=void 0!==I&&I,E=Object(l.useDispatch)(),O=Object(l.useSelect)((function(e){return m?e(m).getSelectorDataForError(r):null})),M=Object(a.useCallback)((function(){E(O.storeName).invalidateResolution(O.name,O.args)}),[E,O]);if(!b||Object(s.f)(r))return null;var k=f&&Object(s.d)(r,O),N=b;f||j||(N=Object(o.sprintf)(/* translators: %s: Error message from Google API. */
Object(o.__)("%s (Please try again.)","google-site-kit"),N)),h||(N=Object(o.sprintf)(/* translators: $%s: Error message */
Object(o.__)("Error: %s","google-site-kit"),N));var _=null==r||null===(n=r.data)||void 0===n?void 0:n.reconnectURL;_&&Object(c.a)(_)&&(N=Object(o.sprintf)(/* translators: 1: Original error message 2: Reconnect URL */
Object(o.__)('%1$s To fix this, <a href="%2$s">redo the plugin setup</a>.',"google-site-kit"),N,_));return e.createElement(u.a,{className:i,type:u.a.TYPES.ERROR,description:e.createElement("span",{dangerouslySetInnerHTML:Object(d.F)(N,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}),ctaButton:k?{label:Object(o.__)("Retry","google-site-kit"),onClick:M}:void 0,hideIcon:y})}ErrorNotice.propTypes={className:r.a.string,error:r.a.shape({message:r.a.string}),hasButton:r.a.bool,storeName:r.a.string,message:r.a.string,noPrefix:r.a.bool,skipRetryMessage:r.a.bool,hideIcon:r.a.bool}}).call(this,n(3))},60:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var i=n(4),r=n(19),a=n(93);function o(t){var n=t.moduleName,o=t.FallbackComponent,c=t.IncompleteComponent;return function(t){function WhenActiveComponent(a){var l=Object(i.useSelect)((function(e){return e(r.a).getModule(n)}),[n]);if(!l)return null;var s=o||a.WidgetNull||null;if(!1===l.active)return s&&e.createElement(s,a);if(!1===l.connected){var u=c||s;return u&&e.createElement(u,a)}return e.createElement(t,a)}return WhenActiveComponent.displayName="When".concat(Object(a.c)(n),"Active"),(t.displayName||t.name)&&(WhenActiveComponent.displayName+="(".concat(t.displayName||t.name,")")),WhenActiveComponent}}}).call(this,n(3))},61:function(e,t,n){"use strict";(function(e){var i,r;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(i=e)||void 0===i||null===(r=i._googlesitekitBaseData)||void 0===r?void 0:r.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},62:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(43);function r(e){return function(){e[i.a]=e[i.a]||[],e[i.a].push(arguments)}}},637:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AddMetricCTATile}));var i=n(14),r=n.n(i),a=n(0),o=n.n(a),c=n(1),l=n(222),s=n(2),u=n(57),d=n(4),g=n(26),f=n(25),m=n(1025),p=n(9),b=n(18);function AddMetricCTATile(t){var n=t.Widget,i=Object(c.useRef)(),a=Object(d.useDispatch)(g.b).setValue,o=Object(b.a)(),v="".concat(o,"_kmw"),h=Object(c.useCallback)((function(e){("keydown"!==e.type||[u.b,u.e].includes(e.keyCode))&&(e.preventDefault(),a(f.k,!0),Object(p.I)(v,"add_metric_click"))}),[a,v]),j=Object(l.a)(i,{threshold:.25}),I=Object(c.useState)(!1),y=r()(I,2),E=y[0],O=y[1],M=!!(null==j?void 0:j.intersectionRatio);return Object(c.useEffect)((function(){M&&!E&&(Object(p.I)(v,"add_metric_view"),O(!0))}),[M,v,E]),e.createElement(n,{className:"googlesitekit-widget--addMetricCTATile",noPadding:!0},e.createElement("div",{ref:i,className:"googlesitekit-km-add-metric-cta-tile",onClick:h,onKeyDown:h,tabIndex:0,role:"button"},e.createElement("div",{className:"googlesitekit-km-add-metric-cta-tile__icon"},e.createElement(m.a,{width:16,height:16})),e.createElement("p",{className:"googlesitekit-km-add-metric-cta-tile__text"},Object(s.__)("Add a metric","google-site-kit"))))}AddMetricCTATile.propTypes={Widget:o.a.elementType.isRequired}}).call(this,n(3))},65:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(1),r=Object(i.createContext)(""),a=(r.Consumer,r.Provider);t.b=r},651:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConnectGA4CTAWidget}));var i=n(14),r=n.n(i),a=n(222),o=n(2),c=n(1),l=n(4),s=n(19),u=n(7),d=n(49),g=n(91),f=n(8),m=n(33),p=n(13),b=n(9),v=n(29),h=n(187),j=n(420),I=n(129),y=n(21),E=n(184),O=n(405),M=n(406);function ConnectGA4CTAWidget(t){var n=t.Widget,i=t.WidgetNull,k=Object(c.useRef)(),N=Object(l.useSelect)((function(e){var t=e(u.a).getKeyMetrics(),n=e(d.a).getWidgets(g.AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY);return t&&n?n.filter((function(e){var n=e.slug,i=e.modules;return t.includes(n)&&i.includes(v.g)})):[]})),_=Object(l.useSelect)((function(e){return e(s.a).isModuleActive(v.g)})),D=Object(l.useSelect)((function(e){var t=e(f.r).getAdminReauthURL();return!!t&&e(m.a).isNavigatingTo(t)})),A=Object(l.useSelect)((function(e){return e(s.a).isFetchingSetModuleActivation(v.g,!0)})),T=Object(l.useSelect)((function(e){return e(p.c).getModuleSettingsEditURL(v.g)})),S=Object(l.useSelect)((function(e){return e(m.a).isNavigatingTo(T)})),w=Object(l.useDispatch)(u.a).dismissItem,C=Object(h.a)(v.g),z=Object(j.a)(v.g),R=Object(c.useCallback)((function(){if(_)return z();C()}),[C,z,_]),x=Object(a.a)(k,{threshold:.25}),P=Object(c.useState)(!1),L=r()(P,2),B=L[0],G=L[1],W=!!(null==x?void 0:x.intersectionRatio),Z=Object(l.useDispatch)(u.a).triggerSurvey,V=Object(l.useSelect)((function(e){return e(p.c).isUsingProxy()}));Object(c.useEffect)((function(){W&&!B&&(V&&Z("view_kmw_setup_cta",{ttl:b.f}),G(!0))}),[W,B,V,Z]);var U=Object(c.useState)(!1),F=r()(U,2),H=F[0],Y=F[1],Q=Object(I.a)(Y,3e3);return Object(c.useEffect)((function(){A||D||S?Y(!0):Q(!1)}),[A,D,Q,S]),!1!==Object(l.useSelect)((function(e){return e(u.a).isItemDismissed(v.e)}))||N.length<4?e.createElement(i,null):e.createElement(n,{noPadding:!0},e.createElement(E.a,{ref:k,className:"googlesitekit-banner--setup-cta googlesitekit-km-connect-ga4-cta",title:Object(o.__)("Analytics is disconnected","google-site-kit"),description:Object(o.__)("Metrics cannot be displayed without Analytics","google-site-kit"),ctaButton:{label:Object(o.__)("Connect Analytics","google-site-kit"),onClick:R,disabled:H,inProgress:H},svg:{desktop:O.a,mobile:M.a,verticalPosition:"top"},footer:e.createElement(y.a,{onClick:function(){return w(v.e)}},Object(o.__)("Maybe later","google-site-kit"))}))}}).call(this,n(3))},68:function(e,t,n){"use strict";n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return v}));var i=n(5),r=n.n(i),a=n(36),o=n.n(a),c=n(125),l=n(12),s=n.n(l),u=n(103),d=n.n(u),g=n(9);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t){if(t&&Array.isArray(t)){var n=t.map((function(e){return"object"===o()(e)?Object(g.H)(e):e}));return"".concat(e,"::").concat(d()(JSON.stringify(n)))}return e}var b={receiveError:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return s()(e,"error is required."),s()(t,"baseName is required."),s()(n&&Array.isArray(n),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:n}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return s()(e,"baseName is required."),s()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function v(e){s()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return s()(n,"selectorName is required."),t.getError(e,n,i)},getErrorForAction:function(e,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return s()(n,"actionName is required."),t.getError(e,n,i)},getError:function(e,t,n){var i=e.errors;return s()(t,"baseName is required."),i[p(t,n)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var n=Object.keys(e.errors).find((function(n){return e.errors[n]===t}));return n?{baseName:n.substring(0,n.indexOf("::")),args:e.errorArgs[n]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(n,i){var r=t(e).getMetaDataForError(i);if(r){var a=r.baseName,o=r.args;if(!!t(e)[a])return{storeName:e,name:a,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:b,controls:{},reducer:function(e,t){var n=t.type,i=t.payload;switch(n){case"RECEIVE_ERROR":var a=i.baseName,o=i.args,c=i.error,l=p(a,o);return m(m({},e),{},{errors:m(m({},e.errors||{}),{},r()({},l,c)),errorArgs:m(m({},e.errorArgs||{}),{},r()({},l,o))});case"CLEAR_ERROR":var s=i.baseName,u=i.args,d=m({},e),g=p(s,u);return d.errors=m({},e.errors||{}),d.errorArgs=m({},e.errorArgs||{}),delete d.errors[g],delete d.errorArgs[g],d;case"CLEAR_ERRORS":var f=i.baseName,b=m({},e);if(f)for(var v in b.errors=m({},e.errors||{}),b.errorArgs=m({},e.errorArgs||{}),b.errors)(v===f||v.startsWith("".concat(f,"::")))&&(delete b.errors[v],delete b.errorArgs[v]);else b.errors={},b.errorArgs={};return b;default:return e}},resolvers:{},selectors:t}}},69:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(1),r=n(18),a=n(9);function o(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=n.viewAction,c=void 0===o?"view_notification":o,l=n.confirmAction,s=void 0===l?"confirm_notification":l,u=n.dismissAction,d=void 0===u?"dismiss_notification":u,g=n.clickLearnMoreAction,f=void 0===g?"click_learn_more_link":g,m=Object(r.a)(),p=null!=t?t:"".concat(m,"_").concat(e),b=Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,c].concat(t))}),[p,c]),v=Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,s].concat(t))}),[p,s]),h=Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,d].concat(t))}),[p,d]),j=Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,f].concat(t))}),[p,f]);return{view:b,confirm:v,dismiss:h,clickLearnMore:j}}},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return l})),n.d(t,"M",(function(){return s})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return g})),n.d(t,"J",(function(){return f})),n.d(t,"I",(function(){return m})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return j})),n.d(t,"l",(function(){return I})),n.d(t,"m",(function(){return y})),n.d(t,"n",(function(){return E})),n.d(t,"o",(function(){return O})),n.d(t,"q",(function(){return M})),n.d(t,"s",(function(){return k})),n.d(t,"r",(function(){return N})),n.d(t,"t",(function(){return _})),n.d(t,"w",(function(){return D})),n.d(t,"u",(function(){return A})),n.d(t,"v",(function(){return T})),n.d(t,"x",(function(){return S})),n.d(t,"y",(function(){return w})),n.d(t,"A",(function(){return C})),n.d(t,"B",(function(){return z})),n.d(t,"C",(function(){return R})),n.d(t,"D",(function(){return x})),n.d(t,"k",(function(){return P})),n.d(t,"F",(function(){return L})),n.d(t,"z",(function(){return B})),n.d(t,"G",(function(){return G})),n.d(t,"E",(function(){return W})),n.d(t,"i",(function(){return Z})),n.d(t,"p",(function(){return V})),n.d(t,"Q",(function(){return U})),n.d(t,"P",(function(){return F}));var i="core/user",r="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",l="googlesitekit_authenticate",s="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",g="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",m="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",j="kmAnalyticsNewVisitors",I="kmAnalyticsPopularAuthors",y="kmAnalyticsPopularContent",E="kmAnalyticsPopularProducts",O="kmAnalyticsReturningVisitors",M="kmAnalyticsTopCities",k="kmAnalyticsTopCitiesDrivingLeads",N="kmAnalyticsTopCitiesDrivingAddToCart",_="kmAnalyticsTopCitiesDrivingPurchases",D="kmAnalyticsTopDeviceDrivingPurchases",A="kmAnalyticsTopConvertingTrafficSource",T="kmAnalyticsTopCountries",S="kmAnalyticsTopPagesDrivingLeads",w="kmAnalyticsTopRecentTrendingPages",C="kmAnalyticsTopTrafficSource",z="kmAnalyticsTopTrafficSourceDrivingAddToCart",R="kmAnalyticsTopTrafficSourceDrivingLeads",x="kmAnalyticsTopTrafficSourceDrivingPurchases",P="kmAnalyticsPagesPerVisit",L="kmAnalyticsVisitLength",B="kmAnalyticsTopReturningVisitorPages",G="kmSearchConsolePopularKeywords",W="kmAnalyticsVisitsPerVisitor",Z="kmAnalyticsMostEngagingPages",V="kmAnalyticsTopCategories",U=[b,v,h,j,I,y,E,O,V,M,k,N,_,D,A,T,w,C,z,P,L,B,W,Z,V],F=[].concat(U,[G])},70:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r}));var i="modules/search-console",r=1},71:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M12 1c6.075 0 11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12 5.925 1 12 1zm0 14a1.5 1.5 0 100 3 1.5 1.5 0 000-3zm-1-2h2V6h-2v7z",fill:"currentColor"});t.a=function SvgWarningNotice(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"none"},e),a)}},73:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},74:function(e,t,n){"use strict";n.r(t),n.d(t,"CONTEXT_MAIN_DASHBOARD_KEY_METRICS",(function(){return i})),n.d(t,"CONTEXT_MAIN_DASHBOARD_TRAFFIC",(function(){return r})),n.d(t,"CONTEXT_MAIN_DASHBOARD_CONTENT",(function(){return a})),n.d(t,"CONTEXT_MAIN_DASHBOARD_SPEED",(function(){return o})),n.d(t,"CONTEXT_MAIN_DASHBOARD_MONETIZATION",(function(){return c})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_TRAFFIC",(function(){return l})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_CONTENT",(function(){return s})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_SPEED",(function(){return u})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_MONETIZATION",(function(){return d}));var i="mainDashboardKeyMetrics",r="mainDashboardTraffic",a="mainDashboardContent",o="mainDashboardSpeed",c="mainDashboardMonetization",l="entityDashboardTraffic",s="entityDashboardContent",u="entityDashboardSpeed",d="entityDashboardMonetization";t.default={CONTEXT_MAIN_DASHBOARD_KEY_METRICS:i,CONTEXT_MAIN_DASHBOARD_TRAFFIC:r,CONTEXT_MAIN_DASHBOARD_CONTENT:a,CONTEXT_MAIN_DASHBOARD_SPEED:o,CONTEXT_MAIN_DASHBOARD_MONETIZATION:c,CONTEXT_ENTITY_DASHBOARD_TRAFFIC:l,CONTEXT_ENTITY_DASHBOARD_CONTENT:s,CONTEXT_ENTITY_DASHBOARD_SPEED:u,CONTEXT_ENTITY_DASHBOARD_MONETIZATION:d}},75:function(e,t,n){"use strict";var i=n(14),r=n.n(i),a=n(327),o=n(0),c=n.n(o),l=n(1),s=n(157);function Portal(e){var t=e.children,n=e.slug,i=Object(l.useState)(document.createElement("div")),o=r()(i,1)[0];return Object(a.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(s.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},76:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var i=n(36),r=n.n(i),a=n(88),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===r()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},78:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var i=n(0),r=n.n(i);function IconWrapper(t){var n=t.children,i=t.marginLeft,r=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:i,marginRight:r}},n)}IconWrapper.propTypes={children:r.a.node.isRequired,marginLeft:r.a.number,marginRight:r.a.number}}).call(this,n(3))},79:function(e,t,n){"use strict";(function(e){var i=n(20),r=n.n(i),a=n(22),o=n.n(a),c=n(10),l=n.n(c),s=n(0),u=n.n(s),d=n(1),g=Object(d.forwardRef)((function(t,n){var i=t.label,a=t.className,c=t.hasLeftSpacing,s=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",r()({ref:n},u,{className:l()("googlesitekit-badge",a,{"googlesitekit-badge--has-left-spacing":s})}),i)}));g.displayName="Badge",g.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=g}).call(this,n(3))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"s",(function(){return a})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return l})),n.d(t,"g",(function(){return s})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"m",(function(){return m})),n.d(t,"n",(function(){return p})),n.d(t,"h",(function(){return b})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return j})),n.d(t,"u",(function(){return I})),n.d(t,"v",(function(){return y})),n.d(t,"f",(function(){return E})),n.d(t,"l",(function(){return O})),n.d(t,"e",(function(){return M})),n.d(t,"t",(function(){return k})),n.d(t,"c",(function(){return N})),n.d(t,"d",(function(){return _})),n.d(t,"b",(function(){return D}));var i="modules/analytics-4",r="account_create",a="property_create",o="webdatastream_create",c="analyticsSetup",l=10,s=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",g="enhanced-measurement-enabled",f="enhanced-measurement-should-dismiss-activation-banner",m="analyticsAccountCreate",p="analyticsCustomDimensionsCreate",b="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",j="dashboardAllTrafficWidgetDimensionValue",I="dashboardAllTrafficWidgetActiveRowIndex",y="dashboardAllTrafficWidgetLoaded",E={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},O={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},M=[O.CONTACT,O.GENERATE_LEAD,O.SUBMIT_LEAD_FORM],k={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},N="audiencePermissionsSetup",_="audienceTileCustomDimensionCreate",D="audience-selection-panel-expirable-new-badge-"},810:function(e,t,n){"use strict";n.d(t,"b",(function(){return g})),n.d(t,"c",(function(){return f.a})),n.d(t,"a",(function(){return m}));var i=n(49),r=n(4),a=n(1021),o=n(1023),c=n(12),l=n.n(c),s={selectors:{isWidgetContextActive:Object(r.createRegistrySelector)((function(e){return function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};l()(n,"contextSlug is required to check a widget context is active.");var a=r.modules;return e(i.a).getWidgetAreas(n).some((function(t){return e(i.a).isWidgetAreaActive(t.slug,{modules:a})}))}}))}},u=n(68),d=Object(r.combineStores)(r.commonStore,a.a,o.a,s,Object(u.b)(i.a)),g=function(e){e.registerStore(i.a,d)},f=n(852);function m(e){var t=e.dispatch,n=e.select,r={WIDGET_AREA_STYLES:i.b,WIDGET_WIDTHS:i.c,registerWidgetArea:function(e,n,a){t(i.a).registerWidgetArea(e,n),a&&r.assignWidgetArea(e,a)},registerWidget:function(e,n,a){t(i.a).registerWidget(e,n),a&&r.assignWidget(e,a)},assignWidgetArea:function(e,n){t(i.a).assignWidgetArea(e,n)},assignWidget:function(e,n){t(i.a).assignWidget(e,n)},isWidgetAreaRegistered:function(e){return n(i.a).isWidgetAreaRegistered(e)},isWidgetRegistered:function(e){return n(i.a).isWidgetRegistered(e)}};return r}},85:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var i=n(115);function r(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(i.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),r=e.replace(n.origin,"");if(r.length<t)return r;var a=r.length-Math.floor(t)+1;return"…"+r.substr(a)}},852:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return O}));var i=n(20),r=n.n(i),a=n(1),o=n(2),c=n(74),l=n(91),s=n(7),u=n(49),d=n(19),g=n(13),f=n(117),m=n(637),p=n(1026),b=n(1027),v=n(651),h=n(402),j=n(23),I=n(29),y=r()({},c),E=r()({},l);function O(t){var n=y.CONTEXT_MAIN_DASHBOARD_KEY_METRICS,i=y.CONTEXT_MAIN_DASHBOARD_TRAFFIC,r=y.CONTEXT_MAIN_DASHBOARD_CONTENT,c=y.CONTEXT_MAIN_DASHBOARD_SPEED,l=y.CONTEXT_MAIN_DASHBOARD_MONETIZATION,O=y.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,M=y.CONTEXT_ENTITY_DASHBOARD_CONTENT,k=y.CONTEXT_ENTITY_DASHBOARD_SPEED,N=y.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,_=E.AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY,D=E.AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY,A=E.AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION,T=E.AREA_MAIN_DASHBOARD_CONTENT_PRIMARY,S=E.AREA_MAIN_DASHBOARD_SPEED_PRIMARY,w=E.AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY,C=E.AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY,z=E.AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY,R=E.AREA_ENTITY_DASHBOARD_SPEED_PRIMARY,x=E.AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY;t.registerWidgetArea(_,{title:e.createElement(a.Fragment,null,Object(o.__)("Key metrics","google-site-kit"),e.createElement(p.a,null)),subtitle:b.a,style:u.b.BOXES,priority:1,CTA:f.a,filterActiveWidgets:function(e,t){return 1===t.length&&s.P.includes(t[0].slug)?[]:t}},n),t.registerWidgetArea(D,{title:Object(o.__)("Find out how your audience is growing","google-site-kit"),subtitle:Object(o.__)("Track your site’s traffic over time","google-site-kit"),style:u.b.BOXES,priority:1},i),t.registerWidgetArea(A,{subtitle:Object(o.__)("Understand how different visitor groups interact with your site","google-site-kit"),hasNewBadge:!0,style:u.b.BOXES,priority:2,CTA:h.f,Footer:h.a,filterActiveWidgets:function(e,t){var n=e(s.a).isAudienceSegmentationWidgetHidden();return void 0===n||n?[]:t}},i),t.registerWidgetArea(T,{title:Object(o.__)("See how your content is doing","google-site-kit"),subtitle:Object(o.__)("Keep track of your most popular pages and how people found them from Search","google-site-kit"),style:u.b.BOXES,priority:1},r),t.registerWidgetArea(S,{title:Object(o.__)("Find out how visitors experience your site","google-site-kit"),subtitle:Object(o.__)("Keep track of how fast your pages are and get specific recommendations on what to improve","google-site-kit"),style:u.b.BOXES,priority:1},c),t.registerWidgetArea(w,{title:Object(o.__)("Find out how much you’re earning from your content","google-site-kit"),subtitle:Object(o.__)("Track your AdSense revenue over time","google-site-kit"),style:u.b.BOXES,priority:1},l),t.registerWidgetArea(C,{title:Object(o.__)("Find out how your audience is growing","google-site-kit"),subtitle:Object(o.__)("Track traffic to this page over time","google-site-kit"),style:u.b.BOXES,priority:1},O),t.registerWidgetArea(z,{title:Object(o.__)("See how your content is doing","google-site-kit"),subtitle:Object(o.__)("Understand how people found this page from Search","google-site-kit"),style:u.b.BOXES,priority:1},M),t.registerWidgetArea(R,{title:Object(o.__)("Find out how visitors experience this page","google-site-kit"),subtitle:Object(o.__)("Keep track of how fast your page is and get specific recommendations on what to improve","google-site-kit"),style:u.b.BOXES,priority:1},k),t.registerWidgetArea(x,{title:Object(o.__)("Find out how much you’re earning from your content","google-site-kit"),subtitle:Object(o.__)("Track your AdSense revenue over time","google-site-kit"),style:u.b.BOXES,priority:1},N),t.registerWidget("keyMetricsSetupCTA",{Component:f.c,width:[t.WIDGET_WIDTHS.FULL],priority:1,wrapWidget:!1,isActive:function(e){return e(s.a).isAuthenticated()&&!1===e(g.c).isKeyMetricsSetupCompleted()}},[_]),t.registerWidget("keyMetricsConnectGA4All",{Component:v.a,width:[t.WIDGET_WIDTHS.FULL],priority:1,wrapWidget:!1,isActive:function(e){var t=e(s.a).getKeyMetrics();return!(e(d.a).isModuleConnected(I.g)||!Array.isArray(t))&&t.filter((function(e){return s.Q.includes(e)})).length>3}},[_]),t.registerWidget("keyMetricsAddMetricFirst",{Component:m.a,width:[t.WIDGET_WIDTHS.QUARTER],priority:3,wrapWidget:!1,isActive:function(e){var t=e(s.a).getKeyMetrics();return!(!Array.isArray(t)||t.length<2)&&t.length<4}},[_]),t.registerWidget("keyMetricsAddMetricSecond",{Component:m.a,width:[t.WIDGET_WIDTHS.QUARTER],priority:3,wrapWidget:!1,isActive:function(e){var t=e(s.a).getKeyMetrics();return!(!Array.isArray(t)||t.length<2)&&t.length<3}},[_]),t.registerWidget("keyMetricsAddMetricThird",{Component:m.a,width:[t.WIDGET_WIDTHS.QUARTER],priority:3,wrapWidget:!1,isActive:function(e){var t=e(s.a).getKeyMetrics();return!(!Array.isArray(t)||t.length<5)&&t.length<8}},[_]),t.registerWidget("keyMetricsAddMetricFourth",{Component:m.a,width:[t.WIDGET_WIDTHS.QUARTER],priority:3,wrapWidget:!1,hideOnBreakpoints:[j.b],isActive:function(e){var t=e(s.a).getKeyMetrics();return!(!Array.isArray(t)||t.length<5)&&t.length<7}},[_]),t.registerWidget("keyMetricsAddMetricFifth",{Component:m.a,width:[t.WIDGET_WIDTHS.QUARTER],priority:3,wrapWidget:!1,hideOnBreakpoints:[j.b],isActive:function(e){var t=e(s.a).getKeyMetrics();return!(!Array.isArray(t)||t.length<5)&&t.length<6}},[_])}}).call(this,n(3))},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i="search-console"},87:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return O})),n.d(t,"d",(function(){return M})),n.d(t,"e",(function(){return N})),n.d(t,"c",(function(){return _})),n.d(t,"b",(function(){return D}));var i=n(14),r=n.n(i),a=n(36),o=n.n(a),c=n(5),l=n.n(c),s=n(22),u=n.n(s),d=n(15),g=n(66),f=n.n(g),m=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=I(e,t),i=n.formatUnit,r=n.formatDecimal;try{return i()}catch(e){return r()}},h=function(e){var t=j(e),n=t.hours,i=t.minutes,r=t.seconds;return r=("0"+r).slice(-2),i=("0"+i).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(i,":").concat(r):"".concat(n,":").concat(i,":").concat(r)},j=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},I=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=j(e),i=n.hours,r=n.minutes,a=n.seconds;return{hours:i,minutes:r,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=b(b({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?N(a,b(b({},o),{},{unit:"second"})):Object(m.sprintf)(/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?N(a,b(b({},o),{},{unit:"second"})):"",r?N(r,b(b({},o),{},{unit:"minute"})):"",i?N(i,b(b({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(m.sprintf)(
// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(m.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(m.sprintf)(
// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(m.__)("%dm","google-site-kit"),r),o=Object(m.sprintf)(
// translators: %s: number of hours with "h" as the abbreviated unit.
Object(m.__)("%dh","google-site-kit"),i);return Object(m.sprintf)(/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",r?n:"",i?o:"").trim()}}},y=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},E=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(m.sprintf)(
// translators: %s: an abbreviated number in millions.
Object(m.__)("%sM","google-site-kit"),N(y(e),e%10==0?{}:t)):1e4<=e?Object(m.sprintf)(
// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),N(y(e))):1e3<=e?Object(m.sprintf)(
// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),N(y(e),e%10==0?{}:t)):N(e,{signDisplay:"never",maximumFractionDigits:1})};function O(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=b({},e)),t}function M(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=O(t),i=n.style,r=void 0===i?"metric":i;return"metric"===r?E(e):"duration"===r?v(e,n):"durationISO"===r?h(e):N(e,n)}var k=f()(console.warn),N=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,i=void 0===n?D():n,a=u()(t,["locale"]);try{return new Intl.NumberFormat(i,a).format(e)}catch(t){k("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(i),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},l=["signDisplay","compactDisplay"],s={},d=0,g=Object.entries(a);d<g.length;d++){var f=r()(g[d],2),m=f[0],p=f[1];c[m]&&p===c[m]||(l.includes(m)||(s[m]=p))}try{return new Intl.NumberFormat(i,s).format(e)}catch(t){return new Intl.NumberFormat(i).format(e)}},_=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,i=void 0===n?D():n,r=t.style,a=void 0===r?"long":r,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var l=new Intl.ListFormat(i,{style:a,type:c});return l.format(e)}
/* translators: used between list items, there is a space after the comma. */var s=Object(m.__)(", ","google-site-kit");return e.join(s)},D=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var i=n.match(/^(\w{2})?(_)?(\w{2})/);if(i&&i[0])return i[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},88:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return r}));var i=n(160),r=n.n(i)()(e)}).call(this,n(28))},89:function(e,t,n){"use strict";(function(e){var i=n(0),r=n.n(i),a=n(10),o=n.n(a);function ChangeArrow(t){var n=t.direction,i=t.invertColor,r=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":i}),width:r,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:r.a.string,invertColor:r.a.bool,width:r.a.number,height:r.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(3))},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return r.b})),n.d(t,"J",(function(){return r.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return b})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return O})),n.d(t,"c",(function(){return M})),n.d(t,"e",(function(){return k})),n.d(t,"b",(function(){return N})),n.d(t,"a",(function(){return _})),n.d(t,"f",(function(){return D})),n.d(t,"n",(function(){return A})),n.d(t,"w",(function(){return T})),n.d(t,"p",(function(){return S})),n.d(t,"G",(function(){return w})),n.d(t,"s",(function(){return C})),n.d(t,"v",(function(){return z})),n.d(t,"k",(function(){return R})),n.d(t,"o",(function(){return x.b})),n.d(t,"h",(function(){return x.a})),n.d(t,"t",(function(){return P.b})),n.d(t,"q",(function(){return P.a})),n.d(t,"A",(function(){return P.c})),n.d(t,"x",(function(){return L})),n.d(t,"u",(function(){return B})),n.d(t,"E",(function(){return Z})),n.d(t,"D",(function(){return V.a})),n.d(t,"g",(function(){return U})),n.d(t,"L",(function(){return F})),n.d(t,"l",(function(){return H}));var i=n(15),r=n(40),a=n(76),o=n(36),c=n.n(o),l=n(103),s=n.n(l),u=function(e){return s()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(i){var r=t[i];r&&"object"===c()(r)&&!Array.isArray(r)&&(r=e(r)),n[i]=r})),n}(e)))};n(104);var d=n(87);function g(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function m(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,i=[g,f,m];n<i.length;n++){t=(0,i[n])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(14),j=n.n(h),I=n(12),y=n.n(I),E=n(2),O="Invalid dateString parameter, it must be a string.",M='Invalid date range, it must be a string with the format "last-x-days".',k=60,N=60*k,_=24*N,D=7*_;function A(){var e=function(e){return Object(E.sprintf)(/* translators: %s: number of days */
Object(E._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function T(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(i.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(i.isDate)(n)&&!isNaN(n)}function S(e){y()(Object(i.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function w(e){y()(T(e),O);var t=e.split("-"),n=j()(t,3),i=n[0],r=n[1],a=n[2];return new Date(i,r-1,a)}function C(e,t){return S(R(e,t*_))}function z(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function R(e,t){y()(T(e)||Object(i.isDate)(e)&&!isNaN(e),O);var n=T(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var x=n(107),P=n(85);function L(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function B(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var G=n(27),W=n.n(G),Z=function(e){return Array.isArray(e)?W()(e).sort():e},V=n(92);function U(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var i=(t-e)/e;return Number.isNaN(i)||!Number.isFinite(i)?null:i}var F=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},H=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(i.unescape)(t)}},90:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var i=n(14),r=n.n(i),a=n(196),o=n(138),c={},l=void 0===e?null:e,s=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,i=e.initialWidth,u=void 0===i?0:i,d=e.initialHeight,g=void 0===d?0:d,f=Object(a.a)("undefined"==typeof document?[u,g]:s,t,n),m=r()(f,2),p=m[0],b=m[1],v=function(){return b(s)};return Object(o.a)(l,"resize",v),Object(o.a)(l,"orientationchange",v),p},d=function(e){return u(e)[0]}}).call(this,n(28))},91:function(e,t,n){"use strict";n.r(t),n.d(t,"AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY",(function(){return i})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY",(function(){return r})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION",(function(){return a})),n.d(t,"AREA_MAIN_DASHBOARD_CONTENT_PRIMARY",(function(){return o})),n.d(t,"AREA_MAIN_DASHBOARD_SPEED_PRIMARY",(function(){return c})),n.d(t,"AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY",(function(){return l})),n.d(t,"AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY",(function(){return s})),n.d(t,"AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY",(function(){return u})),n.d(t,"AREA_ENTITY_DASHBOARD_SPEED_PRIMARY",(function(){return d})),n.d(t,"AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY",(function(){return g}));var i="mainDashboardKeyMetricsPrimary",r="mainDashboardTrafficPrimary",a="mainDashboardTrafficAudienceSegmentation",o="mainDashboardContentPrimary",c="mainDashboardSpeedPrimary",l="mainDashboardMonetizationPrimary",s="entityDashboardTrafficPrimary",u="entityDashboardContentPrimary",d="entityDashboardSpeedPrimary",g="entityDashboardMonetizationPrimary";t.default={AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY:i,AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY:r,AREA_MAIN_DASHBOARD_CONTENT_PRIMARY:o,AREA_MAIN_DASHBOARD_SPEED_PRIMARY:c,AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY:l,AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY:s,AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY:u,AREA_ENTITY_DASHBOARD_SPEED_PRIMARY:d,AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY:g}},92:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(12),r=n.n(i),a=function(e,t){var n=t.dateRangeLength;r()(Array.isArray(e),"report must be an array to partition."),r()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var i=-1*n;return{currentRange:e.slice(i),compareRange:e.slice(2*i,i)}}},93:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return a}));var i=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},r=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function a(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},94:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var i=n(23),r=n(120);function a(t,n){var i=document.querySelector(t);if(!i)return 0;var r=i.getBoundingClientRect().top,a=o(n);return r+e.scrollY-a}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,a=document.querySelector(".googlesitekit-header");return n=!!a&&"sticky"===e.getComputedStyle(a).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===i.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==i.b?t.offsetHeight:0}(t),(n=Object(r.a)(n))<0?0:n}}).call(this,n(28))},97:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"none"},e),a)}},98:function(e,t,n){"use strict";var i=n(1);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=i.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return i.createElement("svg",r({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),a,o)}},99:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))}},[[1275,1,0]]]);