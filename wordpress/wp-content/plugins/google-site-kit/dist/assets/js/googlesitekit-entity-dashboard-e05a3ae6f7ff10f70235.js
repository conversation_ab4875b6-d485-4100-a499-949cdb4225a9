(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[13],{101:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return E})),n.d(t,"a",(function(){return TourTooltips}));var i=n(5),a=n.n(i),o=n(84),c=n(32),l=n(0),s=n.n(l),u=n(2),d=n(4),g=n(26),f=n(7),m=n(40),p=n(119),b=n(18);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var h={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},O={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},E={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},y="feature_tooltip_view",k="feature_tooltip_advance",_="feature_tooltip_return",j="feature_tooltip_dismiss",S="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,i=t.tourID,l=t.gaEventCategory,s=t.callback,u="".concat(i,"-step"),w="".concat(i,"-run"),N=Object(d.useDispatch)(g.b).setValue,T=Object(d.useDispatch)(f.a).dismissTour,C=Object(d.useRegistry)(),A=Object(b.a)(),D=Object(d.useSelect)((function(e){return e(g.b).getValue(u)||0})),R=Object(d.useSelect)((function(e){return e(g.b).getValue(w)&&!1===e(f.a).isTourDismissed(i)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),N(w,!0)}));var L=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,i=e.size,a=e.status,o=e.type,s=t+1,u="function"==typeof l?l(A):l;o===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(m.b)(u,y,s):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(m.b)(u,j,s):n===c.a.NEXT&&a===c.d.FINISHED&&o===c.b.TOUR_END&&i===s&&Object(m.b)(u,S,s),r===c.c.COMPLETE&&a!==c.d.FINISHED&&(n===c.a.PREV&&Object(m.b)(u,_,s),n===c.a.NEXT&&Object(m.b)(u,k,s))}(t);var n=t.action,r=t.index,a=t.status,o=t.step,d=t.type,g=n===c.a.CLOSE,f=!g&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),p=[c.d.FINISHED,c.d.SKIPPED].includes(a),b=g&&d===c.b.STEP_AFTER,v=p||b;if(c.b.STEP_BEFORE===d){var h,O,E=o.target;"string"==typeof o.target&&(E=e.document.querySelector(o.target)),null===(h=E)||void 0===h||null===(O=h.scrollIntoView)||void 0===O||O.call(h,{block:"center"})}f?function(e,t){N(u,e+(t===c.a.PREV?-1:1))}(r,n):v&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),T(i)),s&&s(t,C)},floaterProps:E,locale:O,run:R,stepIndex:D,steps:L,styles:h,tooltipComponent:p.a,continuous:!0,disableOverlayClose:!0,disableScrolling:!0,showProgress:!0})}TourTooltips.propTypes={steps:s.a.arrayOf(s.a.object).isRequired,tourID:s.a.string.isRequired,gaEventCategory:s.a.oneOfType([s.a.string,s.a.func]).isRequired,callback:s.a.func}}).call(this,n(28),n(3))},102:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(11),l=n(21);function CTA(t){var n=t.title,r=t.headerText,i=t.headerContent,a=t.description,s=t.ctaLink,u=t.ctaLabel,d=t.ctaLinkExternal,g=t.ctaType,f=t.error,m=t.onClick,p=t["aria-label"],b=t.children;return e.createElement("div",{className:o()("googlesitekit-cta",{"googlesitekit-cta--error":f})},(r||i)&&e.createElement("div",{className:"googlesitekit-cta__header"},r&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},r),i),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),a&&"string"==typeof a&&e.createElement("p",{className:"googlesitekit-cta__description"},a),a&&"string"!=typeof a&&e.createElement("div",{className:"googlesitekit-cta__description"},a),u&&"button"===g&&e.createElement(c.Button,{"aria-label":p,href:s,onClick:m},u),u&&"link"===g&&e.createElement(l.a,{href:s,onClick:m,"aria-label":p,external:d,hideExternalIndicator:d,arrow:!0},u),b))}CTA.propTypes={title:i.a.string.isRequired,headerText:i.a.string,description:i.a.oneOfType([i.a.string,i.a.node]),ctaLink:i.a.string,ctaLinkExternal:i.a.bool,ctaLabel:i.a.string,ctaType:i.a.string,"aria-label":i.a.string,error:i.a.bool,onClick:i.a.func,children:i.a.node,headerContent:i.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(3))},104:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return u}));var r,i=n(52),a=n.n(i),o=n(53),c=n.n(o),l=function(t){var n=e[t];if(!n)return!1;try{var r="__storage_test__";return n.setItem(r,r),n.removeItem(r),!0}catch(e){return e instanceof DOMException&&(22===e.code||1014===e.code||"QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name)&&0!==n.length}},s=function(){function NullStorage(){a()(this,NullStorage)}return c()(NullStorage,[{key:"key",value:function(){return null}},{key:"getItem",value:function(){return null}},{key:"setItem",value:function(){}},{key:"removeItem",value:function(){}},{key:"clear",value:function(){}},{key:"length",get:function(){return 0}}]),NullStorage}(),u=function(){return r||(r=l("sessionStorage")?e.sessionStorage:l("localStorage")?e.localStorage:new s),r}}).call(this,n(28))},105:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTAButton}));var r=n(0),i=n.n(r),a=n(11),o=n(73);function CTAButton(t){var n,r=t.label,i=t.disabled,c=t.inProgress,l=t.onClick,s=t.href,u=t.external,d=t.hideExternalIndicator;return u&&!d&&(n=e.createElement(o.a,{width:14,height:14})),e.createElement(a.SpinnerButton,{className:"googlesitekit-notice__cta",disabled:i,isSaving:c,onClick:l,href:s,target:u?"_blank":"_self",trailingIcon:n},r)}CTAButton.propTypes={label:i.a.string.isRequired,disabled:i.a.bool,inProgress:i.a.bool,onClick:i.a.func,href:i.a.string,external:i.a.bool,hideExternalIndicator:i.a.bool}}).call(this,n(3))},106:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DismissButton}));var r=n(0),i=n.n(r),a=n(2),o=n(11);function DismissButton(t){var n=t.label,r=void 0===n?Object(a.__)("Got it","google-site-kit"):n,i=t.onClick,c=t.disabled;return e.createElement(o.Button,{onClick:i,disabled:c,tertiary:!0},r)}DismissButton.propTypes={label:i.a.string,onClick:i.a.func.isRequired,disabled:i.a.bool}}).call(this,n(3))},107:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var r=n(245),i=n(89),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(r.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(3))},108:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(5),i=n.n(r),a=n(15),o=n(109),c=n(110);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=s(s({},u),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var l=Object(o.a)(i,n),d=Object(c.a)(i,n,l,r),g={},f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);g[r]||(g[r]=Object(a.once)(d)),g[r].apply(g,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:l,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:d,trackEventOnce:f}}}).call(this,n(28))},109:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(62),i=n(43),a=n(61);function o(t,n){var o,c=Object(r.a)(n),l=t.activeModules,s=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,g=void 0===d?[]:d,f=t.isAuthenticated,m=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(i.b,"]"))),!o){o=!0;var r=(null==g?void 0:g.length)?g.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:s,plugin_version:m||"",enabled_features:Array.from(a.a).join(","),active_modules:l.join(","),authenticated:f?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(i.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,n(28))},11:function(e,t){e.exports=googlesitekit.components},110:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(6),i=n.n(r),a=n(5),o=n.n(a),c=n(16),l=n.n(c),s=n(62);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n,r){var a=Object(s.a)(t);return function(){var t=l()(i.a.mark((function t(o,c,l,s){var u;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:l,value:s},t.abrupt("return",new Promise((function(e){var t,n,i=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),l=function(){clearTimeout(i),e()};a("event",c,d(d({},u),{},{event_callback:l})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&l()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}()}},111:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},112:function(e,t,n){"use strict";var r=n(131);n.d(t,"a",(function(){return r.a}));var i=n(132);n.d(t,"c",(function(){return i.a}));var a=n(133);n.d(t,"b",(function(){return a.a}))},114:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",i({viewBox:"0 0 14 14",fill:"none"},e),a)}},116:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(10),u=n.n(s);function VisuallyHidden(t){var n=t.className,r=t.children,a=o()(t,["className","children"]);return r?e.createElement("span",i()({},a,{className:u()("screen-reader-text",n)}),r):null}VisuallyHidden.propTypes={className:l.a.string,children:l.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(3))},119:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(20),i=n.n(r),a=n(162),o=n.n(a),c=n(10),l=n.n(c),s=n(0),u=n.n(s),d=n(2),g=n(11),f=n(164),m=n(114);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,s=t.primaryProps,u=t.size,p=t.step,b=t.tooltipProps,v=u>1?Object(f.a)(u):[],h=function(e){return l()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",i()({className:l()("googlesitekit-tour-tooltip",p.className)},b),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},p.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},p.content)),e.createElement(a.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},v.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:h(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(g.Button,i()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),p.cta,s.title&&e.createElement(g.Button,i()({className:"googlesitekit-tooltip-button",text:!0},s),s.title))),e.createElement(g.Button,{className:"googlesitekit-tooltip-close",icon:e.createElement(m.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(d.__)("Close","google-site-kit"),text:!0,hideTooltipTitle:!0})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(3))},1191:function(e,t,n){"use strict";(function(e){var r=n(10),i=n.n(r),a=n(1),o=n(42),c=n(2),l=n(4),s=n(240),u=n(74),d=n(453),g=n(711),f=n(716),m=n(241),p=n(24),b=n(13),v=n(7),h=n(21),O=n(116),E=n(17),y=n(390),k=n(199),_=n(49),j=n(630),S=n(697),w=n(39),N=n(631),T=n(719),C=n(632);t.a=function DashboardEntityApp(){var t=Object(w.a)(),n=Object(l.useSelect)((function(e){return t?e(v.a).getViewableModules():null})),r=Object(l.useSelect)((function(e){return e(b.c).getCurrentEntityURL()})),A=Object(l.useSelect)((function(e){return e(b.c).getPermaLinkParam()})),D=Object(l.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-dashboard")})),R={modules:n||void 0},L=Object(l.useSelect)((function(e){return e(_.a).isWidgetContextActive(u.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,R)})),x=Object(l.useSelect)((function(e){return e(_.a).isWidgetContextActive(u.CONTEXT_ENTITY_DASHBOARD_CONTENT,R)})),I=Object(l.useSelect)((function(e){return e(_.a).isWidgetContextActive(u.CONTEXT_ENTITY_DASHBOARD_SPEED,R)})),P=Object(l.useSelect)((function(e){return e(_.a).isWidgetContextActive(u.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,R)})),M=Object(l.useSelect)((function(e){return e(b.c).getDocumentationLinkURL("url-not-part-of-this-site")}));Object(T.a)();var B=null;return P?B=p.c:I?B=p.d:x?B=p.a:L&&(B=p.e),null===r?e.createElement("div",{className:"googlesitekit-widget-context googlesitekit-module-page googlesitekit-entity-dashboard"},e.createElement(j.a,null),e.createElement(C.a,null),e.createElement(E.e,null,e.createElement(E.k,null,e.createElement(E.a,{size:12},e.createElement(a.Fragment,null,e.createElement(h.a,{href:D,back:!0,small:!0},Object(c.__)("Back to the Site Kit dashboard","google-site-kit")),e.createElement(y.a,{title:Object(c.__)("Detailed Page Stats","google-site-kit"),className:"googlesitekit-heading-2 googlesitekit-entity-dashboard__heading",fullWidth:!0}),e.createElement(k.a,{className:"googlesitekit-entity-dashboard__entity-header"},e.createElement(E.e,null,e.createElement(E.k,null,e.createElement(E.a,{size:12},e.createElement("p",null,Object(o.a)(Object(c.sprintf)(/* translators: %s: current entity URL. */
Object(c.__)("It looks like the URL %s is not part of this site or is not based on standard WordPress content types, therefore there is no data available to display. Visit our <link1>support forums</link1> or <link2><VisuallyHidden>Site Kit</VisuallyHidden> website</link2> for support or further information.","google-site-kit"),"<strong>".concat(A,"</strong>")),{strong:e.createElement("strong",null),link1:e.createElement(h.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0}),link2:e.createElement(h.a,{href:M,external:!0}),VisuallyHidden:e.createElement(O.a,null)})))))))))),e.createElement(N.a,null)):e.createElement(a.Fragment,null,e.createElement(j.a,null),e.createElement(C.a,null),e.createElement(s.a,{showNavigation:!0},e.createElement(g.a,null),e.createElement(f.a,null),!t&&e.createElement(S.a,null),e.createElement(m.a,null)),e.createElement(d.a,{id:p.e,slug:u.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,className:i()({"googlesitekit-widget-context--last":B===p.e})}),e.createElement(d.a,{id:p.a,slug:u.CONTEXT_ENTITY_DASHBOARD_CONTENT,className:i()({"googlesitekit-widget-context--last":B===p.a})}),e.createElement(d.a,{id:p.d,slug:u.CONTEXT_ENTITY_DASHBOARD_SPEED,className:i()({"googlesitekit-widget-context--last":B===p.d})}),e.createElement(d.a,{id:p.c,slug:u.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,className:i()({"googlesitekit-widget-context--last":B===p.c})}),e.createElement(N.a,null))}}).call(this,n(3))},120:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(15),i=function(e){return Object(r.isFinite)(e)?e:0}},121:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(397),i=function(e,t,n){Object(r.a)((function(n){return e.includes(n.keyCode)&&t.current.contains(n.target)}),n)}},123:function(e,t,n){"use strict";n.d(t,"a",(function(){return p})),n.d(t,"c",(function(){return v})),n.d(t,"b",(function(){return h}));var r=n(22),i=n.n(r),a=n(5),o=n.n(a),c=n(6),l=n.n(c),s=n(12),u=n.n(s),d=n(4),g=n.n(d),f=n(37),m=n(9),p=function(e){var t;u()(e,"storeName is required to create a snapshot store.");var n={},r={deleteSnapshot:l.a.mark((function e(){var t;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"DELETE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),restoreSnapshot:l.a.mark((function e(){var t,n,r,i,a,o,c=arguments;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.clearAfterRestore,r=void 0===n||n,e.next=4,{payload:{},type:"RESTORE_SNAPSHOT"};case 4:if(i=e.sent,a=i.cacheHit,o=i.value,!a){e.next=13;break}return e.next=10,{payload:{snapshot:o},type:"SET_STATE_FROM_SNAPSHOT"};case 10:if(!r){e.next=13;break}return e.next=13,{payload:{},type:"DELETE_SNAPSHOT"};case 13:return e.abrupt("return",a);case 14:case"end":return e.stop()}}),e)})),createSnapshot:l.a.mark((function e(){var t;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CREATE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},a=(t={},o()(t,"DELETE_SNAPSHOT",(function(){return Object(f.c)("datastore::cache::".concat(e))})),o()(t,"CREATE_SNAPSHOT",Object(d.createRegistryControl)((function(t){return function(){return Object(f.f)("datastore::cache::".concat(e),t.stores[e].store.getState())}}))),o()(t,"RESTORE_SNAPSHOT",(function(){return Object(f.d)("datastore::cache::".concat(e),m.b)})),t);return{initialState:n,actions:r,controls:a,reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1?arguments[1]:void 0,r=t.type,a=t.payload;switch(r){case"SET_STATE_FROM_SNAPSHOT":var o=a.snapshot,c=(o.error,i()(o,["error"]));return c;default:return e}}}},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Object.values(e.stores).filter((function(e){return Object.keys(e.getActions()).includes("restoreSnapshot")}))},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(b(e).map((function(e){return e.getActions().createSnapshot()})))},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(b(e).map((function(e){return e.getActions().restoreSnapshot()})))}},124:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"d",(function(){return a})),n.d(t,"c",(function(){return o}));function r(e){var t=e.format,n=void 0===t?"small":t,r=e.hasErrorOrWarning,i=e.hasSmallImageSVG,o=e.hasWinImageSVG,c={smSize:4,mdSize:8,lgSize:12},l=a(n);return Object.keys(c).forEach((function(e){var t=c[e];r&&(t-=1),i&&(t-=1),o&&0<t-l[e]&&(t-=l[e]),c[e]=t})),c}var i=function(e){switch(e){case"small":return{};case"larger":return{smOrder:2,mdOrder:2,lgOrder:1};default:return{smOrder:2,mdOrder:1}}},a=function(e){switch(e){case"smaller":return{smSize:4,mdSize:2,lgSize:2};case"larger":return{smSize:4,mdSize:8,lgSize:7};default:return{smSize:4,mdSize:2,lgSize:4}}},o=function(e){switch(e){case"larger":return{smOrder:1,mdOrder:1,lgOrder:2};default:return{smOrder:1,mdOrder:2}}}},128:function(e,t,n){"use strict";n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return d}));var r,i=n(5),a=n.n(i),o=n(49),c=n(183),l=n(173),s=(r={},a()(r,o.c.QUARTER,3),a()(r,o.c.HALF,6),a()(r,o.c.FULL,12),r),u="googlesitekit-hidden",d=[c.a,l.a]},1293:function(e,t,n){"use strict";n.r(t),function(e){var t=n(346),r=n(157),i=n(1191),a=n(232),o=n(24);Object(t.a)((function(){var t=document.getElementById("js-googlesitekit-entity-dashboard");if(t){var n=t.dataset.viewOnly;Object(r.render)(e.createElement(a.a,{viewContext:n?o.m:o.l},e.createElement(i.a,null)),t)}}))}.call(this,n(3))},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r="core/site",i="primary",a="secondary"},131:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(20),i=n.n(r),a=n(5),o=n.n(a),c=n(22),l=n.n(c),s=n(0),u=n.n(s),d=n(10),g=n.n(d);function Cell(t){var n,r=t.className,a=t.alignTop,c=t.alignMiddle,s=t.alignBottom,u=t.alignRight,d=t.alignLeft,f=t.smAlignRight,m=t.mdAlignRight,p=t.lgAlignRight,b=t.smSize,v=t.smStart,h=t.smOrder,O=t.mdSize,E=t.mdStart,y=t.mdOrder,k=t.lgSize,_=t.lgStart,j=t.lgOrder,S=t.size,w=t.children,N=l()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},N,{className:g()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":s,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":m,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(S),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--start-".concat(_,"-desktop"),12>=_&&_>0),o()(n,"mdc-layout-grid__cell--order-".concat(j,"-desktop"),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--start-".concat(E,"-tablet"),8>=E&&E>0),o()(n,"mdc-layout-grid__cell--order-".concat(y,"-tablet"),8>=y&&y>0),o()(n,"mdc-layout-grid__cell--span-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),w)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(3))},132:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(10),u=n.n(s),d=n(1),g=Object(d.forwardRef)((function(t,n){var r=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",i()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),a)}));g.displayName="Row",g.propTypes={className:l.a.string,children:l.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(3))},133:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(10),u=n.n(s),d=n(1),g=Object(d.forwardRef)((function(t,n){var r=t.alignLeft,a=t.fill,c=t.className,l=t.children,s=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":s,"mdc-layout-grid--fill":a})},d,{ref:n}),l)}));g.displayName="Grid",g.propTypes={alignLeft:l.a.bool,fill:l.a.bool,className:l.a.string,collapsed:l.a.bool,children:l.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(3))},134:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},135:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},136:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},139:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Icon}));var r,i=n(5),a=n.n(i),o=n(0),c=n.n(o),l=n(111),s=n(71),u=n(97),d=n(38),g=(r={},a()(r,d.a.NEW,u.a),a()(r,d.a.SUCCESS,l.a),a()(r,d.a.INFO,s.a),a()(r,d.a.WARNING,s.a),a()(r,d.a.ERROR,s.a),r);function Icon(t){var n=t.type,r=g[n]||s.a;return e.createElement(r,{width:24,height:24})}Icon.propTypes={type:c.a.oneOf(Object.values(d.a))}}).call(this,n(3))},140:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Title}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Title(t){var n=t.className,r=t.children;return e.createElement("p",{className:i()("googlesitekit-notice__title",n)},r)}Title.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Description(t){var n=t.className,r=t.children;return e.createElement("p",{className:i()("googlesitekit-notice__description",n)},r)}Description.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},142:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(212),l=n(42),s=n(2),u=n(11),d=n(98);function ModalDialog(t){var n=t.className,r=void 0===n?"":n,i=t.dialogActive,a=void 0!==i&&i,g=t.handleCancel,f=void 0===g?null:g,m=t.onOpen,p=void 0===m?null:m,b=t.onClose,v=void 0===b?null:b,h=t.title,O=void 0===h?null:h,E=t.provides,y=t.handleConfirm,k=t.subtitle,_=t.confirmButton,j=void 0===_?null:_,S=t.dependentModules,w=t.danger,N=void 0!==w&&w,T=t.inProgress,C=void 0!==T&&T,A=t.small,D=void 0!==A&&A,R=t.medium,L=void 0!==R&&R,x=t.buttonLink,I=void 0===x?null:x,P=Object(c.a)(ModalDialog),M="googlesitekit-dialog-description-".concat(P),B=!(!E||!E.length);return e.createElement(u.Dialog,{open:a,onOpen:p,onClose:v,"aria-describedby":B?M:void 0,tabIndex:"-1",className:o()(r,{"googlesitekit-dialog-sm":D,"googlesitekit-dialog-md":L})},e.createElement(u.DialogTitle,null,N&&e.createElement(d.a,{width:28,height:28}),O),k?e.createElement("p",{className:"mdc-dialog__lead"},k):[],e.createElement(u.DialogContent,null,B&&e.createElement("section",{id:M,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},E.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),S&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(l.a)(Object(s.sprintf)(/* translators: %s is replaced with the dependent modules. */
Object(s.__)("<strong>Note:</strong> %s","google-site-kit"),S),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",onClick:f,disabled:C,tertiary:!0},Object(s.__)("Cancel","google-site-kit")),I?e.createElement(u.Button,{href:I,onClick:y,target:"_blank",danger:N},j):e.createElement(u.SpinnerButton,{onClick:y,danger:N,disabled:C,isSaving:C},j||Object(s.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:i.a.string,dialogActive:i.a.bool,handleDialog:i.a.func,handleConfirm:i.a.func.isRequired,onOpen:i.a.func,onClose:i.a.func,title:i.a.string,confirmButton:i.a.string,danger:i.a.bool,small:i.a.bool,medium:i.a.bool,buttonLink:i.a.string},t.a=ModalDialog}).call(this,n(3))},143:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(42),l=n(2),s=n(21),u=n(39);function SourceLink(t){var n=t.name,r=t.href,i=t.className,a=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",i)},Object(c.a)(Object(l.sprintf)(/* translators: %s: source link */
Object(l.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(s.a,{key:"link",href:r,external:a})}))}SourceLink.propTypes={name:i.a.string,href:i.a.string,className:i.a.string,external:i.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(3))},144:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportErrorActions}));var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(1),l=n(42),s=n(2),u=n(4),d=n(11),g=n(13),f=n(19),m=n(34),p=n(39),b=n(21);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportErrorActions(t){var n=t.moduleSlug,r=t.error,i=t.GetHelpLink,a=t.hideGetHelpLink,o=t.buttonVariant,v=t.onRetry,O=t.onRequestAccess,E=t.getHelpClassName,y=t.RequestAccessButton,k=t.RetryButton,_=Object(p.a)(),j=Object(u.useSelect)((function(e){return e(f.a).getModuleStoreName(n)})),S=Object(u.useSelect)((function(e){var t;return"function"==typeof(null===(t=e(j))||void 0===t?void 0:t.getServiceEntityAccessURL)?e(j).getServiceEntityAccessURL():null})),w=Array.isArray(r)?r:[r],N=Object(u.useSelect)((function(e){return w.map((function(t){var n,r=null===(n=e(j))||void 0===n?void 0:n.getSelectorDataForError(t);return h(h({},t),{},{selectorData:r})}))})),T=null==N?void 0:N.filter((function(e){return Object(m.d)(e,e.selectorData)&&"getReport"===e.selectorData.name})),C=!!T.length,A=Object(u.useSelect)((function(e){var t=h({},C?T[0]:w[0]);return Object(m.e)(t)&&(t.code="".concat(n,"_insufficient_permissions")),e(g.c).getErrorTroubleshootingLinkURL(t)})),D=Object(u.useDispatch)(),R=w.some((function(e){return Object(m.e)(e)})),L=Object(c.useCallback)((function(){T.forEach((function(e){var t=e.selectorData;D(t.storeName).invalidateResolution(t.name,t.args)})),null==v||v()}),[D,T,v]),x=S&&R&&!_;return e.createElement("div",{className:"googlesitekit-report-error-actions"},x&&("function"==typeof y?e.createElement(y,{requestAccessURL:S}):e.createElement(d.Button,{onClick:O,href:S,target:"_blank",danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Request access","google-site-kit"))),C&&e.createElement(c.Fragment,null,"function"==typeof k?e.createElement(k,{handleRetry:L}):e.createElement(d.Button,{onClick:L,danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Retry","google-site-kit")),!a&&e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(l.a)(Object(s.__)("Retry didn’t work? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(b.a,{href:A,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))}))),!C&&!a&&e.createElement("div",{className:E},"function"==typeof i?e.createElement(i,{linkURL:A}):e.createElement(b.a,{href:A,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))))}ReportErrorActions.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired,GetHelpLink:o.a.elementType,hideGetHelpLink:o.a.bool,buttonVariant:o.a.string,onRetry:o.a.func,onRequestAccess:o.a.func,getHelpClassName:o.a.string,RequestAccessButton:o.a.elementType,RetryButton:o.a.elementType}}).call(this,n(3))},150:function(e,t,n){"use strict";var r=n(1),i=Object(r.createContext)(!1);t.a=i},155:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notification}));var r=n(14),i=n.n(r),a=n(0),o=n.n(a),c=n(1),l=n(4),s=n(304),u=n(171),d=n(69),g=n(41);function Notification(t){var n=t.id,r=t.className,a=t.gaTrackingEventArgs,o=t.children,f=t.onView,m=Object(c.useRef)(),p=Object(u.a)(n),b=Object(d.a)(n,null==a?void 0:a.category,{viewAction:null==a?void 0:a.viewAction}),v=Object(c.useState)(!1),h=i()(v,2),O=h[0],E=h[1],y=Object(l.useSelect)((function(e){return e(g.a).getNotificationSeenDates(n)})),k=Object(l.useDispatch)(g.a).dismissNotification;return Object(c.useEffect)((function(){!O&&p&&(b.view(null==a?void 0:a.label,null==a?void 0:a.value),null==f||f(),E(!0)),(null==y?void 0:y.length)>=3&&k(n,{skipHidingFromQueue:!0})}),[p,b,O,a,f,y,k,n]),e.createElement("section",{id:n,ref:m,className:r},o,!p&&e.createElement(s.a,{id:n,observeRef:m,threshold:.5}))}Notification.propTypes={id:o.a.string,className:o.a.string,gaTrackingEventArgs:o.a.shape({category:o.a.string,viewAction:o.a.string,label:o.a.string,value:o.a.string}),children:o.a.node,onView:o.a.func}}).call(this,n(3))},156:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(1),i=n(4),a=n(49);function o(e,t,n){var o=Object(i.useDispatch)(a.a),c=o.setWidgetState,l=o.unsetWidgetState;Object(r.useEffect)((function(){return c(e,t,n),function(){l(e,t,n)}}),[e,t,n,c,l])}},159:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("path",{d:"M2.253 12.252l7.399 5.658A13.055 13.055 0 009 22c0 1.43.229 2.805.652 4.09l-7.4 5.658A22.02 22.02 0 010 22c0-3.506.81-6.814 2.253-9.748z",fill:"#FBBC05"}),r.createElement("path",{d:"M9.652 17.91l-7.4-5.658A21.935 21.935 0 0122 0c5.6 0 10.6 2.1 14.5 5.5l-6.4 6.4C27.9 10.1 25.1 9 22 9c-5.77 0-10.64 3.725-12.348 8.91z",fill:"#EA4335"}),r.createElement("path",{d:"M2.25 31.742l7.396-5.67A12.975 12.975 0 0022 35c6.1 0 10.7-3.1 11.8-8.5H22V18h20.5c.3 1.3.5 2.7.5 4 0 14-10 22-21 22A21.935 21.935 0 012.25 31.742z",fill:"#34A853"}),r.createElement("path",{d:"M36.34 38.52l-7.025-5.437c2.297-1.45 3.895-3.685 4.485-6.583H22V18h20.5c.3 1.3.5 2.7.5 4 0 7.17-2.623 12.767-6.66 16.52z",fill:"#4285F4"}));t.a=function SvgLogoG(e){return r.createElement("svg",i({viewBox:"0 0 43 44"},e),a)}},164:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},165:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerNotification}));var r=n(20),i=n.n(r),a=n(5),o=n.n(a),c=n(6),l=n.n(c),s=n(16),u=n.n(s),d=n(14),g=n.n(d),f=n(0),m=n.n(f),p=n(10),b=n.n(p),v=n(210),h=n(222),O=n(84),E=n(1),y=n(115),k=n(4),_=n(17),j=n(94),S=n(37),w=n(23),N=n(214),T=n(215),C=n(216),A=n(217),D=n(218),R=n(80),L=n(124),x=n(120),I=n(33),P=n(219),M=n(90);function BannerNotification(t){var n,r=t.badgeLabel,a=t.children,c=t.className,s=void 0===c?"":c,d=t.ctaLabel,f=t.ctaLink,m=t.ctaTarget,p=t.description,B=t.dismiss,H=t.dismissExpires,z=void 0===H?0:H,W=t.format,F=void 0===W?"":W,V=t.id,U=t.isDismissible,G=void 0===U||U,q=t.learnMoreDescription,K=t.learnMoreLabel,X=t.learnMoreURL,Y=t.learnMoreTarget,$=void 0===Y?R.a.EXTERNAL:Y,Z=t.logo,J=t.module,Q=t.moduleName,ee=t.onCTAClick,te=t.onView,ne=t.onDismiss,re=t.onLearnMoreClick,ie=t.showOnce,ae=void 0!==ie&&ie,oe=t.SmallImageSVG,ce=t.title,le=t.type,se=t.WinImageSVG,ue=t.showSmallWinImage,de=void 0===ue||ue,ge=t.smallWinImageSVGWidth,fe=void 0===ge?75:ge,me=t.smallWinImageSVGHeight,pe=void 0===me?75:me,be=t.mediumWinImageSVGWidth,ve=void 0===be?105:be,he=t.mediumWinImageSVGHeight,Oe=void 0===he?105:he,Ee=t.rounded,ye=void 0!==Ee&&Ee,ke=t.footer,_e=t.secondaryPane,je=t.ctaComponent,Se=Object(E.useState)(!1),we=g()(Se,2),Ne=we[0],Te=we[1],Ce=Object(E.useState)(!1),Ae=g()(Ce,2),De=Ae[0],Re=Ae[1],Le="notification::dismissed::".concat(V),xe=function(){return Object(S.f)(Le,new Date,{ttl:null})},Ie=Object(M.a)(),Pe=Object(w.e)(),Me=Object(v.a)(),Be=Object(E.useState)(!1),He=g()(Be,2),ze=He[0],We=He[1],Fe=Object(E.useRef)(),Ve=Object(h.a)(Fe,{rootMargin:"".concat(-Object(x.a)(Object(j.c)(Pe)),"px 0px 0px 0px"),threshold:0});Object(E.useEffect)((function(){!ze&&(null==Ve?void 0:Ve.isIntersecting)&&("function"==typeof te&&te(),We(!0))}),[V,te,ze,Ve]);var Ue=Ie>=600;Object(O.a)(u()(l.a.mark((function e(){var t,n;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(z>0)){e.next=3;break}return e.next=3,Ze();case 3:if(!G){e.next=9;break}return e.next=6,Object(S.d)(Le);case 6:t=e.sent,n=t.cacheHit,Re(n);case 9:if(!ae){e.next=12;break}return e.next=12,xe();case 12:case"end":return e.stop()}}),e)}))));var Ge=function(){var e=u()(l.a.mark((function e(t){return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),t.preventDefault(),!ne){e.next=5;break}return e.next=5,ne(t);case 5:Ke();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),qe=Object(y.a)(f)&&"_blank"!==m,Ke=function(){return qe||Te(!0),new Promise((function(e){setTimeout(u()(l.a.mark((function t(){var n;return l.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,xe();case 2:Me()&&Re(!0),n=new Event("notificationDismissed"),document.dispatchEvent(n),e();case 6:case"end":return t.stop()}}),t)}))),350)}))},Xe=Object(k.useSelect)((function(e){return!!f&&e(I.a).isNavigatingTo(f)})),Ye=Object(k.useDispatch)(I.a).navigateTo,$e=function(){var e=u()(l.a.mark((function e(t){var n,r,i;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),qe&&!t.defaultPrevented&&t.preventDefault(),n=!0,!ee){e.next=12;break}return e.next=6,ee(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:r=e.t0,i=r.dismissOnCTAClick,n=void 0===i||i;case 12:if(!G||!n){e.next=15;break}return e.next=15,Ke();case 15:qe&&Ye(f);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ze=function(){var e=u()(l.a.mark((function e(){var t,n,r;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(S.d)(Le);case 2:if(t=e.sent,!(n=t.value)){e.next=10;break}if((r=new Date(n)).setSeconds(r.getSeconds()+parseInt(z,10)),!(r<new Date)){e.next=10;break}return e.next=10,Object(S.c)(Le);case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();if(!Xe&&G&&(void 0===De||De))return null;var Je=!Xe&&Ne?"is-closed":"is-open",Qe=Object(L.d)(F),et=Object(L.c)(F),tt=Object(L.a)(F),nt=Object(L.b)({format:F,hasErrorOrWarning:"win-error"===le||"win-warning"===le,hasSmallImageSVG:!!oe,hasWinImageSVG:!!se});return e.createElement(N.a,{id:V,className:b()(s,(n={},o()(n,"googlesitekit-publisher-win--".concat(F),F),o()(n,"googlesitekit-publisher-win--".concat(le),le),o()(n,"googlesitekit-publisher-win--".concat(Je),Je),o()(n,"googlesitekit-publisher-win--rounded",ye),n)),secondaryPane:_e,ref:Fe},Z&&e.createElement(D.a,{module:J,moduleName:Q}),oe&&e.createElement(_.a,{size:1,className:"googlesitekit-publisher-win__small-media"},e.createElement(oe,null)),e.createElement(_.a,i()({},nt,tt,{className:"googlesitekit-publisher-win__content"}),e.createElement(T.a,{title:ce,badgeLabel:r,smallWinImageSVGHeight:pe,smallWinImageSVGWidth:fe,winImageFormat:F,WinImageSVG:!Ue&&de?se:void 0}),e.createElement(P.a,{description:p,learnMoreURL:X,learnMoreLabel:K,learnMoreTarget:$,learnMoreDescription:q,onLearnMoreClick:re}),a,e.createElement(C.a,{ctaLink:f,ctaLabel:d,ctaComponent:je,ctaTarget:m,ctaCallback:$e,dismissLabel:G?B:void 0,dismissCallback:Ge}),ke&&e.createElement("div",{className:"googlesitekit-publisher-win__footer"},ke)),se&&(Ue||!de)&&e.createElement(_.a,i()({},Qe,et,{alignBottom:"larger"===F,className:"googlesitekit-publisher-win__image"}),e.createElement("div",{className:"googlesitekit-publisher-win__image-".concat(F)},e.createElement(se,{style:{maxWidth:ve,maxHeight:Oe}}))),e.createElement(A.a,{type:le}))}BannerNotification.propTypes={id:m.a.string.isRequired,className:m.a.string,title:m.a.string.isRequired,description:m.a.node,learnMoreURL:m.a.string,learnMoreDescription:m.a.string,learnMoreLabel:m.a.string,learnMoreTarget:m.a.oneOf(Object.values(R.a)),WinImageSVG:m.a.elementType,SmallImageSVG:m.a.elementType,format:m.a.string,ctaLink:m.a.string,ctaLabel:m.a.string,type:m.a.string,dismiss:m.a.string,isDismissible:m.a.bool,logo:m.a.bool,module:m.a.string,moduleName:m.a.string,dismissExpires:m.a.number,showOnce:m.a.bool,onCTAClick:m.a.func,onView:m.a.func,onDismiss:m.a.func,onLearnMoreClick:m.a.func,badgeLabel:m.a.string,rounded:m.a.bool,footer:m.a.node,secondaryPane:m.a.node,showSmallWinImage:m.a.bool,smallWinImageSVGWidth:m.a.number,smallWinImageSVGHeight:m.a.number,mediumWinImageSVGWidth:m.a.number,mediumWinImageSVGHeight:m.a.number}}).call(this,n(3))},168:function(e,t,n){"use strict";(function(e,r){var i=n(5),a=n.n(i),o=n(20),c=n.n(o),l=n(22),s=n.n(l),u=n(0),d=n.n(u),g=n(1),f=n(259),m=n(11),p=n(142);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var v=null;function RefocusableModalDialog(e){var t=e.dialogActive,n=void 0!==t&&t,i=e.refocusQuerySelector,a=void 0===i?null:i,o=s()(e,["dialogActive","refocusQuerySelector"]),l=Object(g.useCallback)((function(){setTimeout((function(){var e=a?document.querySelector(a):v;e&&document.body.contains(e)&&e.focus(),a||(v=null)}))}),[a]),u=Object(f.a)(n);return Object(g.useEffect)((function(){return!0===u&&!1===n&&l(),function(){l()}}),[u,n,l]),r.createElement(p.a,c()({dialogActive:n},o))}!function(){if(void 0!==e&&e.document&&!e._googlesitekitModalFocusTrackerInitialized){var t=function(e){var t=e.target.closest("button, a, input");t&&!t.classList.contains("mdc-dialog__cancel-button")&&(v=t)};e.document.addEventListener("mousedown",t),e.document.addEventListener("keydown",(function(e){"Enter"!==e.key&&" "!==e.key||t(e)})),e._googlesitekitModalFocusTrackerInitialized=!0}}(),RefocusableModalDialog.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({dialogActive:d.a.bool,refocusQuerySelector:d.a.string},m.Dialog.propTypes),t.a=RefocusableModalDialog}).call(this,n(28),n(3))},169:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M3.332 24.75h21.335c2.182 0 3.542-2.366 2.451-4.25L16.451 2.07C15.36.184 12.64.184 11.549 2.07L.882 20.5c-1.091 1.884.269 4.25 2.45 4.25zM14 14.833a1.42 1.42 0 01-1.417-1.416v-2.834c0-.779.638-1.416 1.417-1.416.78 0 1.417.637 1.417 1.416v2.834A1.42 1.42 0 0114 14.833zm1.417 5.667h-2.834v-2.833h2.834V20.5z",fill:"currentColor",fillRule:"nonzero"});t.a=function SvgError(e){return r.createElement("svg",i({viewBox:"0 0 28 25"},e),a)}},17:function(e,t,n){"use strict";var r=n(261);n.d(t,"i",(function(){return r.a}));var i=n(333);n.d(t,"f",(function(){return i.a}));var a=n(334);n.d(t,"h",(function(){return a.a}));var o=n(335);n.d(t,"j",(function(){return o.a}));var c=n(332);n.d(t,"g",(function(){return c.a}));var l=n(96),s=n.n(l);n.d(t,"b",(function(){return s.a})),n.d(t,"c",(function(){return l.DialogContent})),n.d(t,"d",(function(){return l.DialogFooter}));var u=n(112);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},170:function(e,t,n){"use strict";var r=n(150),i=(r.a.Consumer,r.a.Provider);t.a=i},171:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(4),i=n(26),a=function(e){return"notification/".concat(e,"/viewed")};function o(e){return Object(r.useSelect)((function(t){return!!t(i.b).getValue(a(e))}),[e])}o.getKey=a},173:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RecoverableModules}));var r=n(0),i=n.n(r),a=n(2),o=n(4),c=n(19),l=n(102);function RecoverableModules(t){var n=t.moduleSlugs,r=Object(o.useSelect)((function(e){var t=e(c.a).getModules();if(void 0!==t)return n.map((function(e){return t[e].name}))}));if(void 0===r)return null;var i=1===r.length?Object(a.sprintf)(/* translators: %s: Module name */
Object(a.__)("%s data was previously shared by an admin who no longer has access. Please contact another admin to restore it.","google-site-kit"),r[0]):Object(a.sprintf)(/* translators: %s: List of module names */
Object(a.__)("The data for the following modules was previously shared by an admin who no longer has access: %s. Please contact another admin to restore it.","google-site-kit"),r.join(Object(a._x)(", ","Recoverable modules","google-site-kit")));return e.createElement(l.a,{title:Object(a.__)("Data Unavailable","google-site-kit"),description:i})}RecoverableModules.propTypes={moduleSlugs:i.a.arrayOf(i.a.string).isRequired}}).call(this,n(3))},174:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(0),l=n.n(c),s=n(1),u=n(21),d=n(9),g=n(18);function HelpMenuLink(t){var n=t.children,r=t.href,a=t.gaEventLabel,c=Object(g.a)(),l=Object(s.useCallback)(o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!a){e.next=3;break}return e.next=3,Object(d.I)("".concat(c,"_headerbar_helpmenu"),"click_outgoing_link",a);case 3:case"end":return e.stop()}}),e)}))),[a,c]);return e.createElement("li",{className:"googlesitekit-help-menu-link mdc-list-item",role:"none"},e.createElement(u.a,{className:"mdc-list-item__text",href:r,role:"menuitem",onClick:l,external:!0,hideExternalIndicator:!0},n))}HelpMenuLink.propTypes={children:l.a.node.isRequired,href:l.a.string.isRequired,gaEventLabel:l.a.string},t.a=HelpMenuLink}).call(this,n(3))},177:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(86),a=n(29);function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},o=n.slug,c=void 0===o?"":o,l=n.name,s=void 0===l?"":l,u=n.owner,d=void 0===u?{}:u;if(!c||!s)return e;var g="",f="";return a.g===c?e.match(/account/i)?g=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?g=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(g=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):i.a===c&&(g=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),g||(g=Object(r.sprintf)(/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),s)),d&&d.login&&(f=Object(r.sprintf)(/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),d.login)),f||(f=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(g," ").concat(f)}},178:function(e,t,n){"use strict";var r=n(1),i=n(61),a=Object(r.createContext)(i.a);t.a=a},179:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportError}));var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(15),l=n(1),s=n(2),u=n(4),d=n(19),g=n(34),f=n(177),m=n(88),p=n(102),b=n(144),v=n(39),h=n(59);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function E(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportError(t){var n,r=t.moduleSlug,i=t.error,a=Object(v.a)(),o=Object(u.useSelect)((function(e){return e(d.a).getModule(r)})),O=Array.isArray(i)?i:[i],y=function(e){return Object(g.e)(e)?a?(n=Object(s.sprintf)(/* translators: %s: module name */
Object(s.__)("Access lost to %s","google-site-kit"),null==o?void 0:o.name),Object(s.sprintf)(/* translators: %s: module name */
Object(s.__)("The administrator sharing this module with you has lost access to the %s service, so you won’t be able to see stats from it on the Site Kit dashboard. You can contact them or another administrator to restore access.","google-site-kit"),null==o?void 0:o.name)):(n=Object(s.sprintf)(/* translators: %s: module name */
Object(s.__)("Insufficient permissions in %s","google-site-kit"),null==o?void 0:o.name),Object(f.a)(e.message,o)):Object(g.b)(e)},k=Object(c.uniqWith)(O.map((function(e){var t;return E(E({},e),{},{message:y(e),reconnectURL:null===(t=e.data)||void 0===t?void 0:t.reconnectURL})})),(function(e,t){return e.message===t.message&&e.reconnectURL===t.reconnectURL})),_=O.some((function(e){return Object(g.e)(e)}));_||1!==k.length?!_&&k.length>1&&(n=Object(s.sprintf)(/* translators: %s: module name */
Object(s.__)("Data errors in %s","google-site-kit"),null==o?void 0:o.name)):n=Object(s.sprintf)(/* translators: %s: module name */
Object(s.__)("Data error in %s","google-site-kit"),null==o?void 0:o.name);var j=e.createElement(l.Fragment,null,k.map((function(t){var n;return(null==t||null===(n=t.data)||void 0===n?void 0:n.reconnectURL)?e.createElement(h.a,{key:t.message,error:t,message:t.message}):e.createElement("p",{key:t.message},m.a.sanitize(t.message,{ALLOWED_TAGS:[]}))})));return e.createElement(p.a,{title:n,description:j,error:!0},e.createElement(b.a,{moduleSlug:r,error:i}))}ReportError.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired}}).call(this,n(3))},18:function(e,t,n){"use strict";var r=n(1),i=n(65);t.a=function(){return Object(r.useContext)(i.b)}},180:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"b",(function(){return a}));var r="editing-user-role-select-slug-key",i="dashboardSharingDialogOpen",a="resetSharingDialogOpen"},181:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var r=n(0),i=n.n(r),a=n(2),o=n(21),c=n(202);function GenericErrorHandlerActions(t){var n=t.message,r=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:r}),e.createElement(o.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(a.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:i.a.string,componentStack:i.a.string}}).call(this,n(3))},182:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(24),i=function(e){return r.f.includes(e)}},183:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportZero}));var r=n(0),i=n.n(r),a=n(2),o=n(4),c=n(19),l=n(102);function ReportZero(t){var n=t.moduleSlug,r=Object(o.useSelect)((function(e){return e(c.a).getModule(n)}));return e.createElement(l.a,{title:Object(a.sprintf)(/* translators: %s: Module name */
Object(a.__)("%s Gathering Data","google-site-kit"),null==r?void 0:r.name),description:Object(a.sprintf)(/* translators: %s: Module name */
Object(a.__)("%s data is not yet available, please check back later","google-site-kit"),null==r?void 0:r.name)})}ReportZero.propTypes={moduleSlug:i.a.string.isRequired}}).call(this,n(3))},188:function(e,t,n){"use strict";(function(e){function Title(t){var n=t.title;return e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n)}n.d(t,"a",(function(){return Title}))}).call(this,n(3))},189:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(0),i=n.n(r),a=n(1),o=n(9),c=n(59);function Description(t){var n=t.className,r=void 0===n?"googlesitekit-publisher-win__desc":n,i=t.text,l=t.learnMoreLink,s=t.errorText,u=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:r},e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.F)(i,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",l)),s&&e.createElement(c.a,{message:s}),u)}Description.propTypes={className:i.a.string,text:i.a.string,learnMoreLink:i.a.node,errorText:i.a.string,children:i.a.node}}).call(this,n(3))},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="core/modules",i="insufficient_module_dependencies"},190:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LoadingWrapper}));var r=n(5),i=n.n(r),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(48);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function LoadingWrapper(t){var n=t.loading,r=t.children,i=o()(t,["loading","children"]);return n?e.createElement(s.a,i):r}LoadingWrapper.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({loading:l.a.bool,children:l.a.node},s.a.propTypes)}).call(this,n(3))},195:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleIcon}));var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(4),u=n(19);function ModuleIcon(t){var n=t.slug,r=t.size,a=o()(t,["slug","size"]),c=Object(s.useSelect)((function(e){return e(u.a).getModuleIcon(n)}));return c?e.createElement(c,i()({width:r,height:r},a)):null}ModuleIcon.propTypes={slug:l.a.string.isRequired,size:l.a.number},ModuleIcon.defaultProps={size:33}}).call(this,n(3))},197:function(e,t,n){"use strict";n.d(t,"c",(function(){return v})),n.d(t,"a",(function(){return h})),n.d(t,"b",(function(){return O})),n.d(t,"d",(function(){return y}));var r=n(5),i=n.n(r),a=n(1);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var c=a.createElement("path",{d:"M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19 10 15.27z"});var l=function SvgInfoIcon(e){return a.createElement("svg",o({viewBox:"0 0 20 20",fill:"currentColor"},e),c)};function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var u=a.createElement("path",{d:"M0 4h2v7H0zm0-4h2v2H0z",fill:"currentColor",fillRule:"evenodd"});var d=function SvgSuggestionIcon(e){return a.createElement("svg",s({viewBox:"0 0 2 11"},e),u)};function g(){return(g=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var f=a.createElement("path",{d:"M0 0h2v7H0zm0 10h2v2H0z",fill:"currentColor",fillRule:"evenodd"});var m,p=function SvgWarningIcon(e){return a.createElement("svg",g({viewBox:"0 0 2 12"},e),f)},b=n(99),v="warning",h="info",O="suggestion",E=(m={},i()(m,h,l),i()(m,v,p),i()(m,O,d),m),y=function(e){return E[e]||b.a}},199:function(e,t,n){"use strict";(function(e){var r=n(52),i=n.n(r),a=n(53),o=n.n(a),c=n(81),l=n.n(c),s=n(82),u=n.n(s),d=n(55),g=n.n(d),f=n(0),m=n.n(f),p=n(10),b=n.n(p),v=n(1),h=n(343),O=n(344);function E(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return u()(this,n)}}var y=function(t){l()(Layout,t);var n=E(Layout);function Layout(){return i()(this,Layout),n.apply(this,arguments)}return o()(Layout,[{key:"render",value:function(){var t=this.props,n=t.header,r=t.footer,i=t.children,a=t.title,o=t.badge,c=t.headerCTALabel,l=t.headerCTALink,s=t.footerCTALabel,u=t.footerCTALink,d=t.footerContent,g=t.className,f=t.fill,m=t.relative,p=t.rounded,v=void 0!==p&&p,E=t.transparent,y=void 0!==E&&E;return e.createElement("div",{className:b()("googlesitekit-layout",g,{"googlesitekit-layout--fill":f,"googlesitekit-layout--relative":m,"googlesitekit-layout--rounded":v,"googlesitekit-layout--transparent":y})},n&&e.createElement(h.a,{title:a,badge:o,ctaLabel:c,ctaLink:l}),i,r&&e.createElement(O.a,{ctaLabel:s,ctaLink:u,footerContent:d}))}}]),Layout}(v.Component);y.propTypes={header:m.a.bool,footer:m.a.bool,children:m.a.node.isRequired,title:m.a.string,badge:m.a.node,headerCTALabel:m.a.string,headerCTALink:m.a.string,footerCTALabel:m.a.string,footerCTALink:m.a.string,footerContent:m.a.node,className:m.a.string,fill:m.a.bool,relative:m.a.bool,rounded:m.a.bool,transparent:m.a.bool},y.defaultProps={header:!1,footer:!1,title:"",badge:null,headerCTALabel:"",headerCTALink:"",footerCTALabel:"",footerCTALink:"",footerContent:null,className:"",fill:!1,relative:!1},t.a=y}).call(this,n(3))},2:function(e,t){e.exports=googlesitekit.i18n},202:function(e,t,n){"use strict";(function(e){var r=n(14),i=n.n(r),a=n(204),o=n.n(a),c=n(0),l=n.n(c),s=n(1),u=n(2),d=n(272),g=n(444),f=n(445),m=n(11);function ReportErrorButton(t){var n=t.message,r=t.componentStack,a=Object(s.useState)(!1),c=i()(a,2),l=c[0],p=c[1];return e.createElement(m.Button,{"aria-label":l?Object(u.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){o()("```\n".concat(n,"\n").concat(r,"\n```")),p(!0)},trailingIcon:e.createElement(d.a,{className:"mdc-button__icon",icon:l?g.a:f.a})},l?Object(u.__)("Copied to clipboard","google-site-kit"):Object(u.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:l.a.string,componentStack:l.a.string},t.a=ReportErrorButton}).call(this,n(3))},21:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(10),l=n.n(c),s=n(0),u=n.n(s),d=n(158),g=n(1),f=n(2),m=n(134),p=n(135),b=n(136),v=n(73),h=n(78),O=Object(g.forwardRef)((function(t,n){var r,a=t["aria-label"],c=t.secondary,s=void 0!==c&&c,u=t.arrow,g=void 0!==u&&u,O=t.back,E=void 0!==O&&O,y=t.caps,k=void 0!==y&&y,_=t.children,j=t.className,S=void 0===j?"":j,w=t.danger,N=void 0!==w&&w,T=t.disabled,C=void 0!==T&&T,A=t.external,D=void 0!==A&&A,R=t.hideExternalIndicator,L=void 0!==R&&R,x=t.href,I=void 0===x?"":x,P=t.inverse,M=void 0!==P&&P,B=t.noFlex,H=void 0!==B&&B,z=t.onClick,W=t.small,F=void 0!==W&&W,V=t.standalone,U=void 0!==V&&V,G=t.linkButton,q=void 0!==G&&G,K=t.to,X=t.leadingIcon,Y=t.trailingIcon,$=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),Z=I||K||!z?K?"ROUTER_LINK":D?"EXTERNAL_LINK":"LINK":C?"BUTTON_DISABLED":"BUTTON",J="BUTTON"===Z||"BUTTON_DISABLED"===Z?"button":"ROUTER_LINK"===Z?d.b:"a",Q=("EXTERNAL_LINK"===Z&&(r=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===Z&&(r=Object(f._x)("(disabled)","screen reader text","google-site-kit")),r?a?"".concat(a," ").concat(r):"string"==typeof _?"".concat(_," ").concat(r):void 0:a),ee=X,te=Y;return E&&(ee=e.createElement(b.a,{width:14,height:14})),D&&!L&&(te=e.createElement(v.a,{width:14,height:14})),g&&!M&&(te=e.createElement(m.a,{width:14,height:14})),g&&M&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(J,i()({"aria-label":Q,className:l()("googlesitekit-cta-link",S,{"googlesitekit-cta-link--secondary":s,"googlesitekit-cta-link--inverse":M,"googlesitekit-cta-link--small":F,"googlesitekit-cta-link--caps":k,"googlesitekit-cta-link--danger":N,"googlesitekit-cta-link--disabled":C,"googlesitekit-cta-link--standalone":U,"googlesitekit-cta-link--link-button":q,"googlesitekit-cta-link--no-flex":!!H}),disabled:C,href:"LINK"!==Z&&"EXTERNAL_LINK"!==Z||C?void 0:I,onClick:z,rel:"EXTERNAL_LINK"===Z?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===Z?"_blank":void 0,to:K},$),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},_),!!te&&e.createElement(h.a,{marginLeft:5},te))}));O.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=O}).call(this,n(3))},214:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(1),l=n(17),s=Object(c.forwardRef)((function(t,n){var r=t.id,i=t.className,a=t.children,s=t.secondaryPane;return e.createElement("section",{id:r,className:o()(i,"googlesitekit-publisher-win"),ref:n},e.createElement(l.e,null,e.createElement(l.k,null,a)),s&&e.createElement(c.Fragment,null,e.createElement("div",{className:"googlesitekit-publisher-win__secondary-pane-divider"}),e.createElement(l.e,{className:"googlesitekit-publisher-win__secondary-pane"},e.createElement(l.k,null,e.createElement(l.a,{className:"googlesitekit-publisher-win__secondary-pane",size:12},s)))))}));s.displayName="Banner",s.propTypes={id:i.a.string,className:i.a.string,secondaryPane:i.a.node},t.a=s}).call(this,n(3))},215:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerTitle}));var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(10),l=n.n(c),s=n(79);function BannerTitle(t){var n=t.title,r=t.badgeLabel,a=t.WinImageSVG,o=t.winImageFormat,c=void 0===o?"":o,u=t.smallWinImageSVGWidth,d=void 0===u?75:u,g=t.smallWinImageSVGHeight,f=void 0===g?75:g;return n?e.createElement("div",{className:"googlesitekit-publisher-win__title-image-wrapper"},e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n,r&&e.createElement(s.a,{label:r})),a&&e.createElement("div",{className:l()(i()({},"googlesitekit-publisher-win__image-".concat(c),c))},e.createElement(a,{width:d,height:f}))):null}BannerTitle.propTypes={title:o.a.string,badgeLabel:o.a.string,WinImageSVG:o.a.elementType,winImageFormat:o.a.string,smallWinImageSVGWidth:o.a.number,smallWinImageSVGHeight:o.a.number}}).call(this,n(3))},216:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerActions}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(14),l=n.n(c),s=n(0),u=n.n(s),d=n(210),g=n(1),f=n(4),m=n(11),p=n(33);function BannerActions(t){var n=t.ctaLink,r=t.ctaLabel,a=t.ctaComponent,c=t.ctaTarget,s=t.ctaCallback,u=t.dismissLabel,b=t.dismissCallback,v=t.dismissIsTertiary,h=Object(g.useState)(!1),O=l()(h,2),E=O[0],y=O[1],k=Object(d.a)(),_=Object(f.useSelect)((function(e){return!!n&&e(p.a).isNavigatingTo(n)})),j=function(){var e=o()(i.a.mark((function e(){var t,n,r,a=arguments;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(y(!0),t=a.length,n=new Array(t),r=0;r<t;r++)n[r]=a[r];return e.next=4,null==s?void 0:s.apply(void 0,n);case 4:k()&&y(!1);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return n||u||a?e.createElement("div",{className:"googlesitekit-publisher-win__actions"},a,r&&e.createElement(m.SpinnerButton,{className:"googlesitekit-notification__cta",href:n,target:c,onClick:j,disabled:E||_,isSaving:E||_},r),u&&e.createElement(m.Button,{tertiary:Boolean(n||a||v),onClick:b,disabled:E||_},u)):null}BannerActions.propTypes={ctaLink:u.a.string,ctaLabel:u.a.string,ctaComponent:u.a.element,ctaTarget:u.a.string,ctaCallback:u.a.func,dismissLabel:u.a.string,dismissCallback:u.a.func,dismissIsTertiary:u.a.bool}}).call(this,n(3))},217:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(0),i=n.n(r),a=n(98),o=n(169),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(a.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:i.a.string}}).call(this,n(3))},218:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerLogo}));var r=n(0),i=n.n(r),a=n(17),o=n(159),c=n(195);function BannerLogo(t){var n=t.module,r=t.moduleName;return e.createElement(a.a,{size:12},e.createElement("div",{className:"googlesitekit-publisher-win__logo"},n&&e.createElement(c.a,{slug:n,size:19}),!n&&e.createElement(o.a,{height:"34",width:"32"})),r&&e.createElement("div",{className:"googlesitekit-publisher-win__module-name"},r))}BannerLogo.propTypes={module:i.a.string,moduleName:i.a.string}}).call(this,n(3))},219:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerDescription}));var r=n(0),i=n.n(r),a=n(1),o=n(76),c=n(21),l=n(80);function BannerDescription(t){var n=t.description,r=t.learnMoreLabel,i=t.learnMoreURL,s=t.learnMoreTarget,u=t.learnMoreDescription,d=t.onLearnMoreClick;if(!n)return null;var g;return r&&(g=e.createElement(a.Fragment,null,e.createElement(c.a,{onClick:function(e){e.persist(),null==d||d()},href:i,external:s===l.a.EXTERNAL},r),u)),e.createElement("div",{className:"googlesitekit-publisher-win__desc"},Object(a.isValidElement)(n)?e.createElement(a.Fragment,null,n,g&&e.createElement("p",null,g)):e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.a)(n,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",g))}BannerDescription.propTypes={description:i.a.node,learnMoreURL:i.a.string,learnMoreDescription:i.a.string,learnMoreLabel:i.a.string,learnMoreTarget:i.a.oneOf(Object.values(l.a)),onLearnMoreClick:i.a.func}}).call(this,n(3))},223:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OptIn}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(0),l=n.n(c),s=n(10),u=n.n(s),d=n(1),g=n(42),f=n(2),m=n(4),p=n(11),b=n(7),v=n(40),h=n(21),O=n(18);function OptIn(t){var n=t.id,r=void 0===n?"googlesitekit-opt-in":n,a=t.name,c=void 0===a?"optIn":a,l=t.className,s=t.trackEventCategory,E=t.alignLeftCheckbox,y=void 0!==E&&E,k=Object(m.useSelect)((function(e){return e(b.a).isTrackingEnabled()})),_=Object(m.useSelect)((function(e){return e(b.a).isSavingTrackingEnabled()})),j=Object(m.useSelect)((function(e){return e(b.a).getErrorForAction("setTrackingEnabled",[!k])})),S=Object(m.useDispatch)(b.a).setTrackingEnabled,w=Object(O.a)(),N=Object(d.useCallback)(function(){var e=o()(i.a.mark((function e(t){var n,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S(!!t.target.checked);case 2:n=e.sent,r=n.response,n.error||(Object(v.a)(r.enabled),r.enabled&&Object(v.b)(s||w,"tracking_optin"));case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[S,s,w]);return e.createElement("div",{className:u()("googlesitekit-opt-in",l)},e.createElement(p.Checkbox,{id:r,name:c,value:"1",checked:k,disabled:_,onChange:N,loading:void 0===k,alignLeft:y},Object(g.a)(Object(f.__)("<span>Help us improve Site Kit by sharing anonymous usage data.</span> <span>All collected data is treated in accordance with the <a>Google Privacy Policy.</a></span>","google-site-kit"),{a:e.createElement(h.a,{key:"link",href:"https://policies.google.com/privacy",external:!0}),span:e.createElement("span",null)})),(null==j?void 0:j.message)&&e.createElement("div",{className:"googlesitekit-error-text"},null==j?void 0:j.message))}OptIn.propTypes={id:l.a.string,name:l.a.string,className:l.a.string,trackEventCategory:l.a.string,alignLeftCheckbox:l.a.bool}}).call(this,n(3))},226:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Item}));var r=n(0),i=n.n(r);function Item(t){var n=t.icon,r=t.label;return e.createElement("div",{className:"googlesitekit-user-menu__item"},e.createElement("div",{className:"googlesitekit-user-menu__item-icon"},n),e.createElement("span",{className:"googlesitekit-user-menu__item-label"},r))}Item.propTypes={icon:i.a.node,label:i.a.string}}).call(this,n(3))},23:function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return l}));var r=n(90),i="xlarge",a="desktop",o="tablet",c="small";function l(){var e=Object(r.a)();return e>1280?i:e>960?a:e>600?o:c}},232:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Root}));var r=n(14),i=n.n(r),a=n(0),o=n.n(a),c=n(455),l=n(554),s=n(1),u=n(4),d=n.n(u),g=n(233),f=n(234),m=n(61),p=n(235),b=n(237),v=n(238),h=n(65),O=n(170),E=n(182);function Root(t){var n=t.children,r=t.registry,a=t.viewContext,o=void 0===a?null:a,d=c.a,y=Object(s.useState)({key:"Root",value:!0}),k=i()(y,1)[0];return e.createElement(s.StrictMode,null,e.createElement(O.a,{value:k},e.createElement(u.RegistryProvider,{value:r},e.createElement(f.a,{value:m.a},e.createElement(h.a,{value:o},e.createElement(l.a,{theme:d()},e.createElement(g.a,null,e.createElement(b.a,null,n,o&&e.createElement(v.a,null)),Object(E.a)(o)&&e.createElement(p.a,null))))))))}Root.propTypes={children:o.a.node,registry:o.a.object,viewContext:o.a.string.isRequired},Root.defaultProps={registry:d.a}}).call(this,n(3))},233:function(e,t,n){"use strict";(function(e,r){var i=n(52),a=n.n(i),o=n(53),c=n.n(o),l=n(81),s=n.n(l),u=n(82),d=n.n(u),g=n(55),f=n.n(g),m=n(0),p=n.n(m),b=n(1),v=n(2),h=n(181),O=n(65),E=n(165),y=n(9);function k(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var i=f()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}var _=function(t){s()(ErrorHandler,t);var n=k(ErrorHandler);function ErrorHandler(e){var t;return a()(this,ErrorHandler),(t=n.call(this,e)).state={error:null,info:null,copied:!1},t}return c()(ErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t,info:n}),Object(y.I)("react_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,i=t.info;return n?r.createElement(E.a,{id:"googlesitekit-error",className:"googlesitekit-error-handler",title:Object(v.__)("Site Kit encountered an error","google-site-kit"),description:r.createElement(h.a,{message:n.message,componentStack:i.componentStack}),isDismissible:!1,format:"small",type:"win-error"},r.createElement("pre",{className:"googlesitekit-overflow-auto"},n.message,i.componentStack)):e}}]),ErrorHandler}(b.Component);_.contextType=O.b,_.propTypes={children:p.a.node.isRequired},t.a=_}).call(this,n(28),n(3))},234:function(e,t,n){"use strict";var r=n(178),i=(r.a.Consumer,r.a.Provider);t.a=i},235:function(e,t,n){"use strict";(function(e){var r=n(4),i=n(236),a=n(7);t.a=function PermissionsModal(){return Object(r.useSelect)((function(e){return e(a.a).isAuthenticated()}))?e.createElement(i.a,null):null}}).call(this,n(3))},236:function(e,t,n){"use strict";(function(e,r){var i=n(6),a=n.n(i),o=n(16),c=n.n(o),l=n(2),s=n(1),u=n(4),d=n(168),g=n(30),f=n(33),m=n(7),p=n(123),b=n(75);t.a=function AuthenticatedPermissionsModal(){var t,n,i,o,v=Object(u.useRegistry)(),h=Object(u.useSelect)((function(e){return e(m.a).getPermissionScopeError()})),O=Object(u.useSelect)((function(e){return e(m.a).getUnsatisfiedScopes()})),E=Object(u.useSelect)((function(t){var n,r,i;return t(m.a).getConnectURL({additionalScopes:null==h||null===(n=h.data)||void 0===n?void 0:n.scopes,redirectURL:(null==h||null===(r=h.data)||void 0===r?void 0:r.redirectURL)||e.location.href,errorRedirectURL:null==h||null===(i=h.data)||void 0===i?void 0:i.errorRedirectURL})})),y=Object(u.useDispatch)(m.a).clearPermissionScopeError,k=Object(u.useDispatch)(f.a).navigateTo,_=Object(u.useDispatch)(g.a).setValues,j=Object(s.useCallback)((function(){y()}),[y]),S=Object(s.useCallback)(c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return _(m.d,{permissionsError:h}),e.next=3,Object(p.c)(v);case 3:k(E);case 4:case"end":return e.stop()}}),e)}))),[v,E,k,h,_]);return Object(s.useEffect)((function(){(function(){var e=c()(a.a.mark((function e(){var t,n,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(null==h||null===(t=h.data)||void 0===t?void 0:t.skipModal)||!(null==h||null===(n=h.data)||void 0===n||null===(r=n.scopes)||void 0===r?void 0:r.length)){e.next=3;break}return e.next=3,S();case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[S,h]),h?(null==h||null===(t=h.data)||void 0===t||null===(n=t.scopes)||void 0===n?void 0:n.length)?(null==h||null===(i=h.data)||void 0===i?void 0:i.skipModal)||O&&(null==h||null===(o=h.data)||void 0===o?void 0:o.scopes.every((function(e){return O.includes(e)})))?null:r.createElement(b.a,null,r.createElement(d.a,{title:Object(l.__)("Additional Permissions Required","google-site-kit"),subtitle:h.message,confirmButton:Object(l.__)("Proceed","google-site-kit"),handleConfirm:S,handleCancel:j,onClose:j,dialogActive:!0,medium:!0})):(e.console.warn("permissionsError lacks scopes array to use for redirect, so not showing the PermissionsModal. permissionsError was:",h),null):null}}).call(this,n(28),n(3))},237:function(e,t,n){"use strict";var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(14),l=n.n(c),s=n(1),u=n(4),d=n(123);t.a=function RestoreSnapshots(e){var t=e.children,n=Object(u.useRegistry)(),r=Object(s.useState)(!1),a=l()(r,2),c=a[0],g=a[1];return Object(s.useEffect)((function(){c||o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(d.b)(n);case 2:g(!0);case 3:case"end":return e.stop()}}),e)})))()}),[n,c]),c?t:null}},238:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return FeatureTours}));var i=n(84),a=n(1),o=n(4),c=n(7),l=n(18),s=n(101);function FeatureTours(){var t=Object(l.a)(),n=Object(o.useDispatch)(c.a).triggerTourForView;Object(i.a)((function(){n(t)}));var u=Object(o.useSelect)((function(e){return e(c.a).getCurrentTour()}));return Object(a.useEffect)((function(){if(u){var t=document.getElementById("js-googlesitekit-main-dashboard");if(t){var n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}}),[u]),u?r.createElement(s.a,{tourID:u.slug,steps:u.steps,gaEventCategory:u.gaEventCategory,callback:u.callback}):null}}).call(this,n(28),n(3))},24:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return l})),n.d(t,"s",(function(){return s})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"u",(function(){return m})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return b})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return O})),n.d(t,"a",(function(){return E})),n.d(t,"d",(function(){return y})),n.d(t,"c",(function(){return k})),n.d(t,"f",(function(){return _})),n.d(t,"g",(function(){return j}));var r="mainDashboard",i="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",l="activation",s="splash",u="adminBar",d="adminBarViewOnly",g="settings",f="adBlockingRecovery",m="wpDashboard",p="wpDashboardViewOnly",b="moduleSetup",v="metricSelection",h="key-metrics",O="traffic",E="content",y="speed",k="monetization",_=[r,i,a,o,c,s,g,b,v],j=[a,o,d,p]},240:function(e,t,n){"use strict";(function(e){var r=n(14),i=n.n(r),a=n(0),o=n.n(a),c=n(10),l=n.n(c),s=n(597),u=n(2),d=n(1),g=n(4),f=n(264),m=n(281),p=n(7),b=n(17),v=n(285),h=n(292),O=n(294),E=n(39),y=n(54),k=n(21),_=n(300),j=n(256),S=n(51),w=n(13),N=n(314);function Header(t){var n,r=t.children,a=t.subHeader,o=t.showNavigation,c=!!Object(y.c)(),T=Object(E.a)();Object(N.a)();var C=Object(g.useSelect)((function(e){return e(w.c).getAdminURL("googlesitekit-dashboard")})),A=Object(g.useSelect)((function(e){return e(p.a).isAuthenticated()})),D=Object(s.a)({childList:!0}),R=i()(D,2),L=R[0],x=!!(null===(n=R[1].target)||void 0===n?void 0:n.childElementCount);return e.createElement(d.Fragment,null,e.createElement("header",{className:l()("googlesitekit-header",{"googlesitekit-header--has-subheader":x,"googlesitekit-header--has-navigation":o})},e.createElement(b.e,null,e.createElement(b.k,null,e.createElement(b.a,{smSize:1,mdSize:2,lgSize:4,className:"googlesitekit-header__logo",alignMiddle:!0},e.createElement(k.a,{"aria-label":Object(u.__)("Go to dashboard","google-site-kit"),className:"googlesitekit-header__logo-link",href:C},e.createElement(f.a,null))),e.createElement(b.a,{smSize:3,mdSize:6,lgSize:8,className:"googlesitekit-header__children",alignMiddle:!0},r,!A&&c&&T&&e.createElement(O.a,null),A&&!T&&e.createElement(m.a,null))))),e.createElement("div",{className:"googlesitekit-subheader",ref:L},a),o&&e.createElement(v.a,null),e.createElement(h.a,null),e.createElement(_.a,null),e.createElement(j.a,{areaSlug:S.c.HEADER}))}Header.displayName="Header",Header.propTypes={children:o.a.node,subHeader:o.a.element,showNavigation:o.a.bool},Header.defaultProps={children:null,subHeader:null},t.a=Header}).call(this,n(3))},241:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HelpMenu}));var r=n(14),i=n.n(r),a=n(0),o=n.n(a),c=n(213),l=n(1),s=n(57),u=n(2),d=n(4),g=n(11),f=n(315),m=n(121),p=n(9),b=n(174),v=n(19),h=n(18),O=n(13),E=n(67);function HelpMenu(t){var n=t.children,r=Object(l.useState)(!1),a=i()(r,2),o=a[0],y=a[1],k=Object(l.useRef)(),_=Object(h.a)();Object(c.a)(k,(function(){return y(!1)})),Object(m.a)([s.c,s.f],k,(function(){return y(!1)}));var j=Object(d.useSelect)((function(e){return e(v.a).isModuleActive(E.d)})),S=Object(l.useCallback)((function(){o||Object(p.I)("".concat(_,"_headerbar"),"open_helpmenu"),y(!o)}),[o,_]),w=Object(l.useCallback)((function(){y(!1)}),[]),N=Object(d.useSelect)((function(e){return e(O.c).getDocumentationLinkURL("fix-common-issues")}));return e.createElement("div",{ref:k,className:"googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},e.createElement(g.Button,{"aria-controls":"googlesitekit-help-menu","aria-expanded":o,"aria-label":Object(u.__)("Help","google-site-kit"),"aria-haspopup":"menu",className:"googlesitekit-header__dropdown googlesitekit-border-radius-round googlesitekit-button-icon googlesitekit-help-menu__button mdc-button--dropdown",icon:e.createElement(f.a,{width:"20",height:"20"}),onClick:S,tooltipEnterDelayInMS:500,text:!0}),e.createElement(g.Menu,{className:"googlesitekit-width-auto",menuOpen:o,id:"googlesitekit-help-menu",onSelected:w},n,e.createElement(b.a,{gaEventLabel:"fix_common_issues",href:N},Object(u.__)("Fix common issues","google-site-kit")),e.createElement(b.a,{gaEventLabel:"documentation",href:"https://sitekit.withgoogle.com/documentation/"},Object(u.__)("Read help docs","google-site-kit")),e.createElement(b.a,{gaEventLabel:"support_forum",href:"https://wordpress.org/support/plugin/google-site-kit/"},Object(u.__)("Get support","google-site-kit")),j&&e.createElement(b.a,{gaEventLabel:"adsense_help",href:"https://support.google.com/adsense/"},Object(u.__)("Get help with AdSense","google-site-kit"))))}HelpMenu.propTypes={children:o.a.node}}).call(this,n(3))},248:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return m})),n.d(t,"c",(function(){return b})),n.d(t,"b",(function(){return v}));var r=n(20),i=n.n(r),a=n(66),o=n.n(a),c=n(273),l=n(336),s=n(337),u=n(249),d=n(274),g=n(338),f=n(1),m=o()((function(e){return{widgetSlug:e,Widget:p(e)(c.a),WidgetRecoverableModules:p(e)(d.a),WidgetReportZero:p(e)(l.a),WidgetReportError:p(e)(s.a),WidgetNull:p(e)(u.a)}}));function p(t){return function(n){var r=Object(f.forwardRef)((function(r,a){return e.createElement(n,i()({},r,{ref:a,widgetSlug:t}))}));return r.displayName="WithWidgetSlug",(n.displayName||n.name)&&(r.displayName+="(".concat(n.displayName||n.name,")")),r}}var b=function(t){var n=m(t);return function(t){function DecoratedComponent(r){return e.createElement(t,i()({},r,n))}return DecoratedComponent.displayName="WithWidgetComponentProps",(t.displayName||t.name)&&(DecoratedComponent.displayName+="(".concat(t.displayName||t.name,")")),DecoratedComponent}},v=function(t){return function(n){function DecoratedComponent(r){return e.createElement(n,i()({},r,{WPDashboardReportError:p(t)(g.a)}))}return DecoratedComponent.displayName="WithWPDashboardWidgetComponentProps",(n.displayName||n.name)&&(DecoratedComponent.displayName+="(".concat(n.displayName||n.name,")")),DecoratedComponent}}}).call(this,n(3))},249:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetNull}));var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(156),l=n(99);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var u={};function WidgetNull(t){var n=t.widgetSlug;return Object(c.a)(n,l.a,u),e.createElement(l.a,null)}WidgetNull.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:o.a.string.isRequired},l.a.propTypes)}).call(this,n(3))},251:function(e,t,n){"use strict";n.d(t,"d",(function(){return g})),n.d(t,"b",(function(){return m})),n.d(t,"c",(function(){return p.a})),n.d(t,"g",(function(){return p.c})),n.d(t,"a",(function(){return c.a})),n.d(t,"f",(function(){return b})),n.d(t,"e",(function(){return s}));var r=n(14),i=n.n(r),a=n(27),o=n.n(a),c=n(128),l=n(99);function s(e){return!!e&&e.Component===l.a}function u(e,t){if(9!==t)return[e,t];for(var n=(e=o()(e)).length-1;0!==t&&n>=0;)3===e[n]?(t-=3,e[n]=4):6===e[n]&&(t-=6,e[n]=8),n--;return[e,t]}function d(e,t){return(Array.isArray(t.width)?t.width:[t.width]).map((function(t){return{counter:e+c.c[t],width:t}}))}function g(e,t){var n=[],r=[];if(!(null==e?void 0:e.length))return{columnWidths:n,rowIndexes:r};var a=0,o=0,l=function(e,t){return e.counter-t.counter},g=function(e,t){var n=e.counter;return t.counter-n},f=function(e){return e.counter<=12};if(e.forEach((function(m,p){if(s(t[m.slug]))return n.push(0),void r.push(o);var b=d(a,m),v=function(e,t,n){for(;++e<t.length;)if(!s(n[t[e].slug]))return t[e];return null}(p,e,t);null!==v&&0!==d(b.sort(l)[0].counter,v).filter(f).length||b.some(f)&&(b=(b=b.sort(g)).filter(f));var h=b[0].width;if(r.push(o),(a+=c.c[h])>12){if(a-=c.c[h],r[p]++,9===a){var O=u(n,a),E=i()(O,2);n=E[0],a=E[1]}a=c.c[h],o++}else 12===a&&(a=0,o++);n.push(c.c[h])})),9===a){var m=u(n,a),p=i()(m,2);n=p[0],a=p[1]}return{columnWidths:n,rowIndexes:r}}var f=n(15);function m(e,t,n){var r=n.columnWidths,i=n.rowIndexes,a=[],l=o()(r);if(!(null==e?void 0:e.length))return{gridColumnWidths:l,overrideComponents:a};var s=null,u=-1,d=[];if(function(e,t){for(var n={},r=0;r<e.length;r++){var i,a=e[r],o=null==t?void 0:t[a.slug],l=null==o?void 0:o.Component,s=null==o||null===(i=o.metadata)||void 0===i?void 0:i.moduleSlug,u=c.b.includes(l);if(!l||!s||!u)return!1;if(n[s]){if(n[s]!==l)return!1}else n[s]=l}return!(Object.keys(n).length>1)}(e,t)){var g=Array.from({length:e.length-1}).fill(0);return{overrideComponents:[t[e[0].slug]],gridColumnWidths:[12].concat(o()(g))}}return e.forEach((function(n,o){var c,g,m,p,b;if(a.push(null),s=t[n.slug],u=i[o],s)if(g=s,m=t[null===(c=e[o+1])||void 0===c?void 0:c.slug],p=u,b=i[o+1],p===b&&Object(f.isEqual)(g,m))d.push(r[o]),l[o]=0;else if(d.length>0){d.push(r[o]);var v=d.reduce((function(e,t){return e+t}),0);a[o]=s,l[o]=v,d=[]}})),{gridColumnWidths:l,overrideComponents:a}}var p=n(248);function b(e){return(Array.isArray(e)?e:[e]).filter((function(e){return"string"==typeof e&&e.length>0}))}},255:function(e,t,n){"use strict";(function(e){var r=n(14),i=n.n(r),a=n(1);t.a=function(t,n){var r=Object(a.useState)(null),o=i()(r,2),c=o[0],l=o[1];return Object(a.useEffect)((function(){if(t.current&&"function"==typeof e.IntersectionObserver){var r=new e.IntersectionObserver((function(e){l(e[e.length-1])}),n);return r.observe(t.current),function(){l(null),r.disconnect()}}return function(){}}),[t.current,n.threshold,n.root,n.rootMargin]),c}}).call(this,n(28))},256:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notifications}));var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(4),l=n(18),s=n(41),u=n(51),d=n(313);function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Notifications(t){var n,r=t.areaSlug,a=t.groupID,o=void 0===a?u.d.DEFAULT:a,f=Object(l.a)(),m=Object(c.useSelect)((function(e){return e(s.a).getQueuedNotifications(f,o)}));if(void 0===(null==m?void 0:m[0])||(null==m||null===(n=m[0])||void 0===n?void 0:n.areaSlug)!==r)return null;var p=m[0],b=p.id,v=p.Component,h=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},Object(d.a)(b));return e.createElement(v,h)}Notifications.propTypes={viewContext:o.a.string,areaSlug:o.a.string}}).call(this,n(3))},26:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="core/ui",i="activeContextID"},263:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Dismiss}));var r=n(6),i=n.n(r),a=n(5),o=n.n(a),c=n(16),l=n.n(c),s=n(0),u=n.n(s),d=n(2),g=n(4),f=n(69),m=n(41),p=n(11);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Dismiss(t){var n=t.id,r=t.primary,a=void 0===r||r,o=t.dismissLabel,c=void 0===o?Object(d.__)("OK, Got it!","google-site-kit"):o,s=t.dismissExpires,u=void 0===s?0:s,b=t.disabled,h=t.onDismiss,O=void 0===h?function(){}:h,E=t.gaTrackingEventArgs,y=t.dismissOptions,k=Object(f.a)(n,null==E?void 0:E.category),_=Object(g.useDispatch)(m.a).dismissNotification,j=function(){var e=l()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==O?void 0:O(t);case 2:k.dismiss(null==E?void 0:E.label,null==E?void 0:E.value),_(n,v(v({},y),{},{expiresInSeconds:u}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(p.Button,{tertiary:!a,onClick:j,disabled:b},c)}Dismiss.propTypes={id:u.a.string,primary:u.a.bool,dismissLabel:u.a.string,dismissExpires:u.a.number,disabled:u.a.bool,onDismiss:u.a.func,gaTrackingEventArgs:u.a.shape({label:u.a.string,value:u.a.string})}}).call(this,n(3))},264:function(e,t,n){"use strict";(function(e){var r=n(2),i=n(159),a=n(265),o=n(116);t.a=function Logo(){return e.createElement("div",{className:"googlesitekit-logo","aria-hidden":"true"},e.createElement(i.a,{className:"googlesitekit-logo__logo-g",height:"34",width:"32"}),e.createElement(a.a,{className:"googlesitekit-logo__logo-sitekit",height:"26",width:"99"}),e.createElement(o.a,null,Object(r.__)("Site Kit by Google Logo","google-site-kit")))}}).call(this,n(3))},265:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M62.09 1.664h3.038v.1L58.34 9.593l7.241 10.224v.1H62.7L56.755 11.4 53.95 14.64v5.278h-2.351V1.664h2.35v9.415h.1l8.04-9.415zM69.984 3.117c0 .454-.166.853-.487 1.175-.322.322-.71.488-1.176.488-.455 0-.854-.166-1.175-.488a1.599 1.599 0 01-.488-1.175c0-.466.166-.854.488-1.176.321-.322.71-.488 1.175-.488.455 0 .854.166 1.176.488.332.333.487.72.487 1.176zm-.476 4.313v12.498h-2.351V7.43h2.35zM77.016 20.128c-1.02 0-1.864-.31-2.54-.943-.676-.632-1.02-1.508-1.031-2.628V9.57h-2.196V7.43h2.196V3.603h2.35V7.43h3.061v2.14h-3.06v6.222c0 .831.166 1.397.488 1.696.321.3.687.444 1.097.444.189 0 .366-.022.555-.067.188-.044.344-.1.499-.166l.743 2.096c-.632.222-1.342.333-2.162.333zM2.673 18.952C1.375 18.009.488 16.678 0 14.97l2.883-1.176c.289 1.076.799 1.94 1.542 2.628.732.677 1.619 1.02 2.65 1.02.965 0 1.774-.244 2.45-.742.677-.5 1.01-1.187 1.01-2.052 0-.798-.3-1.453-.887-1.974-.588-.521-1.62-1.042-3.094-1.564l-1.22-.432C4.025 10.224 2.928 9.57 2.04 8.716 1.153 7.862.71 6.742.71 5.346c0-.966.266-1.853.787-2.673C2.018 1.852 2.75 1.209 3.693.72 4.624.244 5.678 0 6.864 0c1.708 0 3.072.41 4.081 1.242 1.02.832 1.697 1.752 2.04 2.795L10.236 5.2c-.2-.621-.576-1.164-1.142-1.63-.565-.477-1.286-.71-2.173-.71s-1.641.222-2.251.676c-.61.455-.91 1.032-.91 1.742 0 .676.278 1.22.82 1.663.544.432 1.398.854 2.563 1.253l1.22.41c1.674.577 2.96 1.342 3.88 2.274.921.931 1.376 2.184 1.376 3.748 0 1.275-.322 2.34-.976 3.193a6.01 6.01 0 01-2.495 1.919 8.014 8.014 0 01-3.116.621c-1.62 0-3.072-.466-4.358-1.408zM15.969 3.449a1.95 1.95 0 01-.588-1.43c0-.566.2-1.043.588-1.431A1.95 1.95 0 0117.399 0c.566 0 1.043.2 1.43.588.389.388.588.865.588 1.43 0 .566-.2 1.043-.587 1.43a1.95 1.95 0 01-1.43.589c-.566-.012-1.043-.2-1.431-.588zm-.067 2.595h2.994v13.883h-2.994V6.044zM25.405 19.85c-.543-.2-.986-.466-1.33-.788-.776-.776-1.176-1.84-1.176-3.182V8.683h-2.428v-2.64h2.428V2.13h2.994v3.926h3.372v2.639h-3.372v6.531c0 .743.145 1.276.433 1.575.277.366.743.543 1.42.543.31 0 .576-.044.82-.122.233-.077.488-.21.765-.399v2.917c-.599.277-1.32.41-2.173.41a5.01 5.01 0 01-1.753-.3zM33.623 19.407a6.63 6.63 0 01-2.529-2.628c-.61-1.12-.909-2.373-.909-3.77 0-1.332.3-2.551.887-3.693.588-1.132 1.409-2.04 2.462-2.706 1.053-.666 2.251-1.01 3.593-1.01 1.397 0 2.606.311 3.637.921a6.123 6.123 0 012.34 2.528c.532 1.076.799 2.274.799 3.627 0 .255-.023.576-.078.953H33.179c.111 1.287.566 2.285 1.375 2.983a4.162 4.162 0 002.817 1.043c.854 0 1.597-.189 2.218-.588a4.266 4.266 0 001.508-1.597l2.528 1.198c-.654 1.142-1.508 2.04-2.561 2.694-1.054.655-2.318.976-3.782.976-1.364.022-2.584-.288-3.66-.931zm7.23-8.051a3.332 3.332 0 00-.466-1.453c-.277-.477-.687-.887-1.242-1.208-.554-.322-1.23-.488-2.03-.488-.964 0-1.773.288-2.439.853-.665.566-1.12 1.342-1.375 2.296h7.552z",fill:"#5F6368"});t.a=function SvgLogoSitekit(e){return r.createElement("svg",i({viewBox:"0 0 80 21",fill:"none"},e),a)}},273:function(e,t,n){"use strict";(function(e){var r=n(10),i=n.n(r),a=n(0),o=n.n(a),c=n(1),l=Object(c.forwardRef)((function(t,n){var r=t.children,a=t.className,o=t.widgetSlug,c=t.noPadding,l=t.Header,s=t.Footer;return e.createElement("div",{className:i()("googlesitekit-widget","googlesitekit-widget--".concat(o),{"googlesitekit-widget--no-padding":c},{"googlesitekit-widget--with-header":l},a),ref:n},l&&e.createElement("div",{className:"googlesitekit-widget__header"},e.createElement(l,null)),e.createElement("div",{className:"googlesitekit-widget__body"},r),s&&e.createElement("div",{className:"googlesitekit-widget__footer"},e.createElement(s,null)))}));l.defaultProps={children:void 0,noPadding:!1},l.propTypes={children:o.a.node,widgetSlug:o.a.string.isRequired,noPadding:o.a.bool,Header:o.a.elementType,Footer:o.a.elementType},t.a=l}).call(this,n(3))},274:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetRecoverableModules}));var r=n(5),i=n.n(r),a=n(20),o=n.n(a),c=n(27),l=n.n(c),s=n(22),u=n.n(s),d=n(0),g=n.n(d),f=n(1),m=n(156),p=n(173);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function WidgetRecoverableModules(t){var n=t.widgetSlug,r=t.moduleSlugs,i=u()(t,["widgetSlug","moduleSlugs"]),a=Object(f.useMemo)((function(){return{moduleSlug:l()(r).sort().join(","),moduleSlugs:r}}),[r]);return Object(m.a)(n,p.a,a),e.createElement(p.a,o()({moduleSlugs:r},i))}WidgetRecoverableModules.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:g.a.string.isRequired},p.a.propTypes)}).call(this,n(3))},278:function(e,t,n){"use strict";(function(e){var r=n(56),i=n.n(r),a=n(279),o=e._googlesitekitAPIFetchData||{},c=o.nonce,l=o.nonceEndpoint,s=o.preloadedData,u=o.rootURL;i.a.nonceEndpoint=l,i.a.nonceMiddleware=i.a.createNonceMiddleware(c),i.a.rootURLMiddleware=i.a.createRootURLMiddleware(u),i.a.preloadingMiddleware=Object(a.a)(s),i.a.use(i.a.nonceMiddleware),i.a.use(i.a.mediaUploadMiddleware),i.a.use(i.a.rootURLMiddleware),i.a.use(i.a.preloadingMiddleware),t.default=i.a}).call(this,n(28))},279:function(e,t,n){"use strict";var r=n(269);t.a=function(e){var t=Object.keys(e).reduce((function(t,n){return t[Object(r.getStablePath)(n)]=e[n],t}),{}),n=!1;return function(e,i){if(n)return i(e);setTimeout((function(){n=!0}),3e3);var a=e.parse,o=void 0===a||a,c=e.path;if("string"==typeof e.path){var l,s=(null===(l=e.method)||void 0===l?void 0:l.toUpperCase())||"GET",u=Object(r.getStablePath)(c);if(o&&"GET"===s&&t[u]){var d=Promise.resolve(t[u].body);return delete t[u],d}if("OPTIONS"===s&&t[s]&&t[s][u]){var g=Promise.resolve(t[s][u]);return delete t[s][u],g}}return i(e)}}},281:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return UserMenu}));var i=n(6),a=n.n(i),o=n(16),c=n.n(o),l=n(14),s=n.n(l),u=n(213),d=n(1),g=n(2),f=n(57),m=n(4),p=n(11),b=n(142),v=n(9),h=n(37),O=n(75),E=n(282),y=n(226),k=n(283),_=n(284),j=n(30),S=n(13),w=n(7),N=n(33),T=n(8),C=n(121),A=n(18);function UserMenu(){var t=Object(m.useSelect)((function(e){return e(S.c).getProxyPermissionsURL()})),n=Object(m.useSelect)((function(e){return e(w.a).getEmail()})),i=Object(m.useSelect)((function(e){return e(w.a).getPicture()})),o=Object(m.useSelect)((function(e){return e(w.a).getFullName()})),l=Object(m.useSelect)((function(e){return e(S.c).getAdminURL("googlesitekit-splash",{googlesitekit_context:"revoked"})})),D=Object(m.useSelect)((function(e){return e(j.a).getValue(T.d,"isAutoCreatingCustomDimensionsForAudience")})),R=Object(d.useState)(!1),L=s()(R,2),x=L[0],I=L[1],P=Object(d.useState)(!1),M=s()(P,2),B=M[0],H=M[1],z=Object(d.useRef)(),W=Object(d.useRef)(),F=Object(A.a)(),V=Object(m.useDispatch)(N.a).navigateTo;Object(u.a)(z,(function(){return H(!1)})),Object(C.a)([f.c,f.f],z,(function(){var e;H(!1),null===(e=W.current)||void 0===e||e.focus()}));var U=function(){I(!1),H(!1)};Object(d.useEffect)((function(){var t=function(e){f.c===e.keyCode&&U()};return e.addEventListener("keyup",t),function(){e.removeEventListener("keyup",t)}}),[]);var G,q=Object(d.useCallback)((function(){B||Object(v.I)("".concat(F,"_headerbar"),"open_usermenu"),H(!B)}),[B,F]),K=Object(d.useCallback)((function(){I(!x),H(!1)}),[x]),X=Object(d.useCallback)(function(){var e=c()(a.a.mark((function e(n,r){var i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:i=r.detail.item,e.t0=null==i?void 0:i.id,e.next="manage-sites"===e.t0?4:"disconnect"===e.t0?9:11;break;case 4:if(!t){e.next=8;break}return e.next=7,Object(v.I)("".concat(F,"_headerbar_usermenu"),"manage_sites");case 7:V(t);case 8:return e.abrupt("break",12);case 9:return K(),e.abrupt("break",12);case 11:q();case 12:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),[t,q,K,V,F]),Y=Object(d.useCallback)(c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return I(!1),e.next=3,Object(h.b)();case 3:return e.next=5,Object(v.I)("".concat(F,"_headerbar_usermenu"),"disconnect_user");case 5:V(l);case 6:case"end":return e.stop()}}),e)}))),[l,V,F]);return n?(o&&n&&(G=Object(g.sprintf)(/* translators: Account info text. 1: User's (full) name 2: User's email address. */
Object(g.__)("Google Account for %1$s (Email: %2$s)","google-site-kit"),o,n)),o&&!n&&(G=Object(g.sprintf)(/* translators: Account info text. 1: User's (full) name. */
Object(g.__)("Google Account for %1$s","google-site-kit"),o)),!o&&n&&(G=Object(g.sprintf)(/* translators: Account info text. 1: User's email address. */
Object(g.__)("Google Account (Email: %1$s)","google-site-kit"),n)),r.createElement(d.Fragment,null,r.createElement("div",{ref:z,className:"googlesitekit-user-selector googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},r.createElement(p.Button,{disabled:D,ref:W,className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--tablet googlesitekit-border-radius-round--phone googlesitekit-border-radius-round googlesitekit-button-icon",onClick:q,icon:!!i&&r.createElement("i",{className:"mdc-button__icon mdc-button__account","aria-hidden":"true"},r.createElement("img",{className:"mdc-button__icon--image",src:i,alt:Object(g.__)("User Avatar","google-site-kit")})),"aria-haspopup":"menu","aria-expanded":B,"aria-controls":"user-menu","aria-label":D?void 0:Object(g.__)("Account","google-site-kit"),tooltipEnterDelayInMS:500,customizedTooltip:D?null:r.createElement("span",{"aria-label":G},r.createElement("strong",null,Object(g.__)("Google Account","google-site-kit")),r.createElement("br",null),r.createElement("br",null),o,o&&r.createElement("br",null),n),text:!0,tooltip:!0}),r.createElement(p.Menu,{className:"googlesitekit-user-menu",menuOpen:B,onSelected:X,id:"user-menu"},r.createElement("li",null,r.createElement(E.a,null)),!!t&&r.createElement("li",{id:"manage-sites",className:"mdc-list-item",role:"menuitem"},r.createElement(y.a,{icon:r.createElement(_.a,{width:"22"}),label:Object(g.__)("Manage Sites","google-site-kit")})),r.createElement("li",{id:"disconnect",className:"mdc-list-item",role:"menuitem"},r.createElement(y.a,{icon:r.createElement(k.a,{width:"22"}),label:Object(g.__)("Disconnect","google-site-kit")})))),r.createElement(O.a,null,r.createElement(b.a,{dialogActive:x,handleConfirm:Y,handleCancel:U,onClose:U,title:Object(g.__)("Disconnect","google-site-kit"),subtitle:Object(g.__)("Disconnecting Site Kit by Google will remove your access to all services. After disconnecting, you will need to re-authorize to restore service.","google-site-kit"),confirmButton:Object(g.__)("Disconnect","google-site-kit"),danger:!0,small:!0})))):null}}).call(this,n(28),n(3))},282:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Details}));var r=n(2),i=n(4),a=n(7);function Details(){var t=Object(i.useSelect)((function(e){return e(a.a).getPicture()})),n=Object(i.useSelect)((function(e){return e(a.a).getFullName()})),o=Object(i.useSelect)((function(e){return e(a.a).getEmail()}));return e.createElement("div",{className:"googlesitekit-user-menu__details","aria-label":Object(r.__)("Google account","google-site-kit")},!!t&&e.createElement("img",{className:"googlesitekit-user-menu__details-avatar",src:t,alt:""}),e.createElement("div",{className:"googlesitekit-user-menu__details-info"},e.createElement("p",{className:"googlesitekit-user-menu__details-info__name"},n),e.createElement("p",{className:"googlesitekit-user-menu__details-info__email","aria-label":Object(r.__)("Email","google-site-kit")},o)))}}).call(this,n(3))},283:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M6.83 2H20a2 2 0 012 2v12c0 .34-.09.66-.23.94L20 15.17V6h-9.17l-4-4zm13.66 19.31L17.17 18H4a2 2 0 01-2-2V4c0-.34.08-.66.23-.94L.69 1.51 2.1.1l19.8 19.8-1.41 1.41zM15.17 16l-10-10H4v10h11.17z",fill:"currentColor"});t.a=function SvgDisconnect(e){return r.createElement("svg",i({viewBox:"0 0 22 22",fill:"none"},e),a)}},284:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M20 0H2C.9 0 0 .9 0 2v12c0 1.1.9 2 2 2h5v2h8v-2h5c1.1 0 2-.9 2-2V2c0-1.1-.9-2-2-2zm0 14H2V2h18v12zm-2-9H7v2h11V5zm0 4H7v2h11V9zM6 5H4v2h2V5zm0 4H4v2h2V9z",fill:"currentColor"});t.a=function SvgManageSites(e){return r.createElement("svg",i({viewBox:"0 0 22 18",fill:"none"},e),a)}},285:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardNavigation}));var r=n(4),i=n(7),a=n(39),o=n(190),c=n(286);function DashboardNavigation(){var t=Object(a.a)(),n=Object(r.useSelect)((function(e){return t?e(i.a).getViewableModules():null})),l=Object(r.useSelect)((function(e){return e(i.a).getKeyMetrics()}));return e.createElement(o.a,{loading:void 0===n||void 0===l,width:"100%",smallHeight:"59px",height:"71px"},e.createElement(c.a,null))}}).call(this,n(3))},286:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return Navigation}));var i=n(27),a=n.n(i),o=n(14),c=n.n(o),l=n(10),s=n.n(l),u=n(15),d=n(84),g=n(163),f=n(1),m=n(2),p=n(4),b=n(287),v=n(288),h=n(289),O=n(290),E=n(291),y=n(24),k=n(7),_=n(49),j=n(26),S=n(74),w=n(54),N=n(23),T=n(94),C=n(9),A=n(18),D=n(39);function R(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return L(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return L(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function L(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Navigation(){var t,n=Object(w.c)(),i=Object(f.useRef)(),o=Object(N.e)(),l=null===(t=e.location.hash)||void 0===t?void 0:t.substring(1),L=Object(f.useState)(l),x=c()(L,2),I=x[0],P=x[1],M=Object(f.useState)(l||void 0),B=c()(M,2),H=B[0],z=B[1],W=Object(f.useState)(!1),F=c()(W,2),V=F[0],U=F[1],G=Object(A.a)(),q=Object(D.a)(),K=Object(p.useDispatch)(j.b).setValue,X=Object(p.useSelect)((function(e){return q?e(k.a).getViewableModules():null})),Y=Object(p.useSelect)((function(e){return e(k.a).isKeyMetricsWidgetHidden()})),$={modules:X||void 0},Z=Object(p.useSelect)((function(e){return n===w.b&&!0!==Y&&e(_.a).isWidgetContextActive(S.CONTEXT_MAIN_DASHBOARD_KEY_METRICS,$)})),J=Object(p.useSelect)((function(e){return e(_.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_TRAFFIC:S.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,$)})),Q=Object(p.useSelect)((function(e){return e(_.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_CONTENT:S.CONTEXT_ENTITY_DASHBOARD_CONTENT,$)})),ee=Object(p.useSelect)((function(e){return e(_.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_SPEED:S.CONTEXT_ENTITY_DASHBOARD_SPEED,$)})),te=Object(p.useSelect)((function(e){return e(_.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_MONETIZATION:S.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,$)})),ne=Object(f.useCallback)((function(){return Z?y.b:q?J?y.e:Q?y.a:ee?y.d:te?y.c:"":y.e}),[Z,J,Q,ee,te,q]),re=Object(f.useCallback)((function(t){var n,r=t.target.closest(".mdc-chip"),i=null==r||null===(n=r.dataset)||void 0===n?void 0:n.contextId;e.history.replaceState({},"","#".concat(i)),z(i),Object(C.I)("".concat(G,"_navigation"),"tab_select",i),e.scrollTo({top:i!==ne()?Object(T.a)("#".concat(i),o):0,behavior:"smooth"}),setTimeout((function(){K(j.a,i)}),50)}),[o,G,K,ne]);return Object(d.a)((function(){var t=ne();if(!l)return P(t),void setTimeout((function(){return e.history.replaceState({},"","#".concat(t))}));var n=l;(function(e){return!(!Z||e!==y.b)||(!(!J||e!==y.e)||(!(!Q||e!==y.a)||(!(!ee||e!==y.d)||!(!te||e!==y.c))))})(n)||(n=t),K(j.a,n),P(n),setTimeout((function(){var r=n!==t?Object(T.a)("#".concat(n),o):0;e.scrollY!==r?e.scrollTo({top:r,behavior:"smooth"}):K(j.a,void 0)}),50)})),Object(f.useEffect)((function(){var t=function(e){K(j.a,void 0),P(e),z(void 0)},n=Object(u.throttle)((function(n){var r,o,c,l,s=e.scrollY,u=null===(r=document.querySelector(".googlesitekit-entity-header"))||void 0===r||null===(o=r.getBoundingClientRect())||void 0===o?void 0:o.bottom,d=null==i||null===(c=i.current)||void 0===c?void 0:c.getBoundingClientRect(),g=d.bottom,f=d.top,m=[].concat(a()(Z?[y.b]:[]),a()(J?[y.e]:[]),a()(Q?[y.a]:[]),a()(ee?[y.d]:[]),a()(te?[y.c]:[])),p=ne();if(0===s)U(!1);else{var b,v=null===(b=document.querySelector(".googlesitekit-header"))||void 0===b?void 0:b.getBoundingClientRect().bottom;U(f===v)}var h,O=R(m);try{for(O.s();!(h=O.n()).done;){var E=h.value,k=document.getElementById(E);if(k){var _=k.getBoundingClientRect().top-20-(u||g||0);_<0&&(void 0===l||l<_)&&(l=_,p=E)}}}catch(e){O.e(e)}finally{O.f()}if(H)H===p&&t(p);else{var j=e.location.hash;p!==(null==j?void 0:j.substring(1))&&(n&&Object(C.I)("".concat(G,"_navigation"),"tab_scroll",p),e.history.replaceState({},"","#".concat(p)),t(p))}}),150);return e.addEventListener("scroll",n),function(){e.removeEventListener("scroll",n)}}),[H,Z,J,Q,ee,te,G,K,ne]),r.createElement("nav",{className:s()("mdc-chip-set","googlesitekit-navigation","googlesitekit-navigation--".concat(n),{"googlesitekit-navigation--is-sticky":V}),ref:i},Z&&r.createElement(g.Chip,{id:y.b,label:Object(m.__)("Key metrics","google-site-kit"),leadingIcon:r.createElement(b.a,{width:"18",height:"16"}),onClick:re,selected:I===y.b,"data-context-id":y.b}),J&&r.createElement(g.Chip,{id:y.e,label:Object(m.__)("Traffic","google-site-kit"),leadingIcon:r.createElement(v.a,{width:"18",height:"16"}),onClick:re,selected:I===y.e,"data-context-id":y.e}),Q&&r.createElement(g.Chip,{id:y.a,label:Object(m.__)("Content","google-site-kit"),leadingIcon:r.createElement(h.a,{width:"18",height:"18"}),onClick:re,selected:I===y.a,"data-context-id":y.a}),ee&&r.createElement(g.Chip,{id:y.d,label:Object(m.__)("Speed","google-site-kit"),leadingIcon:r.createElement(O.a,{width:"20",height:"16"}),onClick:re,selected:I===y.d,"data-context-id":y.d}),te&&r.createElement(g.Chip,{id:y.c,label:Object(m.__)("Monetization","google-site-kit"),leadingIcon:r.createElement(E.a,{width:"18",height:"16"}),onClick:re,selected:I===y.c,"data-context-id":y.c}))}}).call(this,n(28),n(3))},287:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("rect",{x:.5,width:5,height:5,rx:1,fill:"currentColor"}),o=r.createElement("rect",{x:7.5,width:5,height:5,rx:1,fill:"currentColor"}),c=r.createElement("rect",{x:.5,y:7,width:5,height:5,rx:1,fill:"currentColor"}),l=r.createElement("rect",{x:7.5,y:7,width:5,height:5,rx:1,fill:"currentColor"});t.a=function SvgNavKeyMetricsIcon(e){return r.createElement("svg",i({viewBox:"0 0 13 12",fill:"none"},e),a,o,c,l)}},288:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 0h3.971v16H7V0zM0 8h4v8H0V8zm18-3h-4v11h4V5z",fill:"currentColor"});t.a=function SvgNavTrafficIcon(e){return r.createElement("svg",i({viewBox:"0 0 18 16",fill:"none"},e),a)}},289:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 16V2c0-1.1-1-2-2.222-2H2.222C1 0 0 .9 0 2v14c0 1.1 1 2 2.222 2h13.556C17 18 18 17.1 18 16zM9 7h5V5H9v2zm7-5H2v14h14V2zM4 4h4v4H4V4zm10 7H9v2h5v-2zM4 10h4v4H4v-4z",fill:"currentColor"});t.a=function SvgNavContentIcon(e){return r.createElement("svg",i({viewBox:"0 0 18 18",fill:"none"},e),a)}},29:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"f",(function(){return c})),n.d(t,"k",(function(){return l})),n.d(t,"j",(function(){return s})),n.d(t,"h",(function(){return u})),n.d(t,"i",(function(){return d})),n.d(t,"e",(function(){return g})),n.d(t,"g",(function(){return f}));var r=1,i=2,a=3,o="enhanced-measurement-activation-banner-tooltip-state",c="enhanced-measurement-activation-banner-dismissed-item",l="_r.explorerCard..selmet",s="_r.explorerCard..seldim",u="_r..dataFilters",d="_r..nav",g="key-metrics-connect-ga4-cta-widget",f="analytics-4"},290:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M18.378 4.543l-1.232 1.854a8.024 8.024 0 01-.22 7.598H3.043A8.024 8.024 0 014.154 4.49 8.011 8.011 0 0113.57 2.82l1.853-1.233A10.01 10.01 0 003.117 2.758a10.026 10.026 0 00-1.797 12.24A2.004 2.004 0 003.043 16h13.873a2.003 2.003 0 001.742-1.002 10.03 10.03 0 00-.27-10.465l-.01.01z",fill:"currentColor"}),o=r.createElement("path",{d:"M8.572 11.399a2.003 2.003 0 002.835 0l5.669-8.51-8.504 5.673a2.005 2.005 0 000 2.837z",fill:"currentColor"});t.a=function SvgNavSpeedIcon(e){return r.createElement("svg",i({viewBox:"0 0 20 16",fill:"none"},e),a,o)}},291:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M16.1 0v2h2.967l-5.946 5.17-4.6-4L0 10.59 1.621 12l6.9-6 4.6 4L20.7 3.42V6H23V0h-6.9z",fill:"currentColor"});t.a=function SvgNavMonetizationIcon(e){return r.createElement("svg",i({viewBox:"0 0 23 12",fill:"none"},e),a)}},292:function(e,t,n){"use strict";(function(e,r){var i=n(14),a=n.n(i),o=n(15),c=n(2),l=n(1),s=n(4),u=n(11),d=n(13),g=n(293),f=n(33),m=n(21),p=n(85),b=n(9),v=n(54),h=n(18);t.a=function EntityHeader(){var t=Object(h.a)(),n=Object(v.c)(),i=Object(s.useSelect)((function(e){return e(d.c).getCurrentEntityTitle()})),O=Object(s.useSelect)((function(e){return e(d.c).getCurrentEntityURL()})),E=Object(l.useRef)(),y=Object(l.useState)(O),k=a()(y,2),_=k[0],j=k[1];Object(l.useEffect)((function(){var t=function(){if(E.current){var t=E.current.clientWidth-40,n=e.getComputedStyle(E.current.lastChild,null).getPropertyValue("font-size"),r=2*t/parseFloat(n);j(Object(p.d)(O,r))}},n=Object(o.throttle)(t,100);return t(),e.addEventListener("resize",n),function(){e.removeEventListener("resize",n)}}),[O,E,j]);var S=Object(s.useDispatch)(f.a).navigateTo,w=Object(s.useSelect)((function(e){return e(d.c).getAdminURL("googlesitekit-dashboard")})),N=Object(l.useCallback)((function(){Object(b.I)("".concat(t,"_navigation"),"return_to_dashboard"),S(w)}),[w,S,t]);return v.a!==n||null===O||null===i?null:r.createElement("div",{className:"googlesitekit-entity-header"},r.createElement("div",{className:"googlesitekit-entity-header__back"},r.createElement(u.Button,{icon:r.createElement(g.a,{width:24,height:24}),"aria-label":Object(c.__)("Back to dashboard","google-site-kit"),onClick:N,text:!0,tertiary:!0},Object(c.__)("Back to dashboard","google-site-kit"))),r.createElement("div",{ref:E,className:"googlesitekit-entity-header__details"},r.createElement("p",null,i),r.createElement(m.a,{href:O,"aria-label":O,secondary:!0,external:!0},_)))}}).call(this,n(28),n(3))},293:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M21 11H6.83l3.58-3.59L9 6l-6 6 6 6 1.41-1.41L6.83 13H21z",fill:"currentColor"});t.a=function SvgKeyboardBackspace(e){return r.createElement("svg",i({viewBox:"0 0 24 24"},e),a,o)}},294:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ViewOnlyMenu}));var r=n(14),i=n.n(r),a=n(213),o=n(10),c=n.n(o),l=n(1),s=n(2),u=n(57),d=n(11),g=n(18),f=n(121),m=n(9),p=n(295),b=n(296),v=n(297),h=n(299),O=n(4),E=n(7);function ViewOnlyMenu(){var t=Object(l.useState)(!1),n=i()(t,2),r=n[0],o=n[1],y=Object(l.useRef)(),k=Object(g.a)();Object(a.a)(y,(function(){return o(!1)})),Object(f.a)([u.c,u.f],y,(function(){return o(!1)}));var _=Object(l.useCallback)((function(){r||Object(m.I)("".concat(k,"_headerbar"),"open_viewonly"),o(!r)}),[r,k]),j=Object(O.useSelect)((function(e){return e(E.a).hasCapability(E.H)}));return e.createElement("div",{ref:y,className:c()("googlesitekit-view-only-menu","googlesitekit-dropdown-menu","googlesitekit-dropdown-menu__icon-menu","mdc-menu-surface--anchor",{"googlesitekit-view-only-menu--user-can-authenticate":j})},e.createElement(d.Button,{className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--phone googlesitekit-button-icon",onClick:_,icon:e.createElement("span",{className:"mdc-button__icon","aria-hidden":"true"},e.createElement(p.a,{className:"mdc-button__icon--image"})),"aria-haspopup":"menu","aria-expanded":r,"aria-controls":"view-only-menu","aria-label":Object(s.__)("View only","google-site-kit"),tooltipEnterDelayInMS:500,text:!0,tooltip:!0},Object(s.__)("View only","google-site-kit")),e.createElement(d.Menu,{menuOpen:r,onSelected:_,id:"view-only-menu",nonInteractive:!0},e.createElement(b.a,null),e.createElement(v.a,null),e.createElement("li",{className:"mdc-list-divider",role:"separator"}),e.createElement(h.a,null)))}}).call(this,n(3))},295:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M8 1.333c2.756 0 5.214 1.42 6.415 3.667-1.2 2.247-3.659 3.667-6.415 3.667-2.756 0-5.215-1.42-6.415-3.667C2.785 2.753 5.244 1.333 8 1.333zM8 0C4.364 0 1.258 2.073 0 5c1.258 2.927 4.364 5 8 5s6.742-2.073 8-5c-1.258-2.927-4.364-5-8-5zm0 3.333c1.004 0 1.818.747 1.818 1.667S9.004 6.667 8 6.667 6.182 5.92 6.182 5 6.996 3.333 8 3.333zM8 2C6.196 2 4.727 3.347 4.727 5S6.197 8 8 8c1.804 0 3.273-1.347 3.273-3S9.803 2 8 2z",fill:"currentColor"});t.a=function SvgView(e){return r.createElement("svg",i({viewBox:"0 0 16 10",fill:"none"},e),a)}},296:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(1),l=n(42),s=n(2),u=n(4),d=n(11),g=n(33),f=n(13),m=n(7),p=n(9),b=n(21),v=n(18),h=n(37);function Description(){var t=Object(v.a)(),n=Object(u.useSelect)((function(e){return e(m.a).hasCapability(m.H)})),r=Object(u.useSelect)((function(e){return e(f.c).getProxySetupURL()})),a=Object(u.useSelect)((function(e){return e(f.c).getDocumentationLinkURL("dashboard-sharing")})),O=Object(u.useDispatch)(g.a).navigateTo,E=Object(c.useCallback)(function(){var e=o()(i.a.mark((function e(n){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.preventDefault(),e.next=3,Promise.all([Object(h.f)("start_user_setup",!0),Object(p.I)("".concat(t,"_headerbar_viewonly"),"start_user_setup",r?"proxy":"custom-oauth")]);case 3:O(r);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[r,O,t]),y=Object(c.useCallback)((function(){Object(p.I)("".concat(t,"_headerbar_viewonly"),"click_learn_more_link")}),[t]),k=n?Object(l.a)(Object(s.__)("You can see stats from all shared Google services, but you can't make any changes. <strong>Sign in to connect more services and control sharing access.</strong>","google-site-kit"),{strong:e.createElement("strong",null)}):Object(l.a)(Object(s.__)("You can see stats from all shared Google services, but you can't make any changes. <a>Learn more</a>","google-site-kit"),{a:e.createElement(b.a,{href:a,onClick:y,"aria-label":Object(s.__)("Learn more about dashboard sharing","google-site-kit"),external:!0})});return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item googlesitekit-view-only-menu__description"},e.createElement("p",null,k),n&&e.createElement(d.Button,{onClick:E},Object(s._x)("Sign in with Google","Service name","google-site-kit")))}}).call(this,n(3))},297:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SharedServices}));var r=n(2),i=n(4),a=n(7),o=n(298);function SharedServices(){var t=Object(i.useSelect)((function(e){return e(a.a).getViewableModules()}));return void 0===t?null:e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("h4",null,Object(r.__)("Shared services","google-site-kit")),e.createElement("ul",null,t.map((function(t){return e.createElement(o.a,{key:t,module:t})}))))}}).call(this,n(3))},298:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Service}));var r=n(0),i=n.n(r),a=n(42),o=n(2),c=n(4),l=n(19),s=n(7);function Service(t){var n=t.module,r=Object(c.useSelect)((function(e){return e(s.a).hasCapability(s.H)})),i=Object(c.useSelect)((function(e){return e(l.a).getModule(n)||{}})),u=i.name,d=i.owner,g=Object(c.useSelect)((function(e){return e(l.a).getModuleIcon(n)}));return e.createElement("li",{className:"googlesitekit-view-only-menu__service"},e.createElement("span",{className:"googlesitekit-view-only-menu__service--icon"},e.createElement(g,{height:26})),e.createElement("span",{className:"googlesitekit-view-only-menu__service--name"},u),r&&(null==d?void 0:d.login)&&e.createElement("span",{className:"googlesitekit-view-only-menu__service--owner"},Object(a.a)(Object(o.sprintf)(/* translators: %s: module owner Google Account email address */
Object(o.__)("Shared by <strong>%s</strong>","google-site-kit"),d.login),{strong:e.createElement("strong",{title:d.login})})))}Service.propTypes={module:i.a.string.isRequired}}).call(this,n(3))},299:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Tracking}));var r=n(42),i=n(2),a=n(223),o=n(18);function Tracking(){var t=Object(o.a)();return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("p",null,Object(r.a)(Object(i.__)("Thanks for using Site Kit!<br />Help us make it even better","google-site-kit"),{br:e.createElement("br",null)})),e.createElement(a.a,{trackEventCategory:"".concat(t,"_headerbar_viewonly"),alignCheckboxLeft:!0}))}}).call(this,n(3))},30:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},300:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LegacyNotifications}));var r=n(1),i=n(4),a=n(19),o=n(7),c=n(301),l=n(308),s=n(39),u=n(54),d=n(67),g=n(310);function LegacyNotifications(){var t=Object(s.a)(),n=Object(u.c)(),f=Object(i.useSelect)((function(e){return e(o.a).isAuthenticated()})),m=Object(i.useSelect)((function(e){return e(a.a).isModuleActive(d.d)}));return e.createElement(r.Fragment,null,e.createElement(g.a,null),!t&&n===u.b&&e.createElement(r.Fragment,null,m&&e.createElement(l.a,null),f&&e.createElement(c.a,null)))}}).call(this,n(3))},301:function(e,t,n){"use strict";(function(e){var r=n(14),i=n.n(r),a=n(1),o=n(4),c=n(13),l=n(7),s=n(41),u=n(302),d=n(51);t.a=function CoreSiteBannerNotifications(){var t=Object(a.useState)(!1),n=i()(t,2),r=n[0],g=n[1],f=Object(a.useState)(!1),m=i()(f,2),p=m[0],b=m[1],v=Object(a.useRef)(Date.now()),h=Object(o.useSelect)((function(e){return e(c.c).isUsingProxy()&&!1===e(l.a).areSurveysOnCooldown()?e(l.a).getCurrentSurvey():null})),O=Object(a.useState)([]),E=i()(O,2),y=E[0],k=E[1],_=Object(o.useDispatch)(s.a).registerNotification,j=Object(o.useSelect)((function(e){return e(c.c).getNotifications()}));return Object(a.useEffect)((function(){var e=setTimeout((function(){p||g(!0)}),5e3);return function(){clearTimeout(e)}}),[p]),Object(a.useEffect)((function(){Math.floor((Date.now()-v.current)/1e3)<5&&h&&b(!0)}),[v,h,b]),Object(a.useEffect)((function(){r&&!p&&(null==j||j.forEach((function(t){y.includes(t.id)||(_(t.id,{Component:function Component(){return e.createElement(u.a,t)},priority:t.priority,areaSlug:d.c.DASHBOARD_TOP,isDismissible:t.isDismissible}),k((function(e){return e.push(t.id),e})))})))}),[p,j,_,y,r]),null}}).call(this,n(3))},302:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(303),o=n(305),c=n(189),l=n(306),s=n(21);function NotificationFromServer(t){var n=t.id,r=t.title,i=t.content,u=t.ctaLabel,d=t.ctaTarget,g=t.ctaURL,f=t.dismissible,m=t.dismissLabel,p=t.learnMoreLabel,b=t.learnMoreURL;return e.createElement(a.a,{className:"googlesitekit-notification-from-server",id:n},e.createElement(o.a,{title:r,description:e.createElement(c.a,{learnMoreLink:e.createElement(s.a,{href:b,external:!0},p),text:i}),actions:e.createElement(l.a,{id:n,ctaLabel:u,ctaURL:g,ctaTarget:d,dismissLabel:f&&m?m:void 0,dismissExpires:1,dismissOnCTAClick:!0})}))}NotificationFromServer.propTypes={id:i.a.string.isRequired,title:i.a.string.isRequired,content:i.a.node,ctaLabel:i.a.string,ctaTarget:i.a.string,ctaURL:i.a.string,dismissible:i.a.bool,dismissLabel:i.a.string,learnMoreLabel:i.a.string,learnMoreURL:i.a.string},t.a=NotificationFromServer}).call(this,n(3))},303:function(e,t,n){"use strict";var r=n(155);n.d(t,"a",(function(){return r.a}))},304:function(e,t,n){"use strict";n.d(t,"a",(function(){return ViewedStateObserver}));var r=n(0),i=n.n(r),a=n(1),o=n(4),c=n(26),l=n(41),s=n(255),u=n(171);function ViewedStateObserver(e){var t=e.id,n=e.observeRef,r=e.threshold,i=Object(s.a)(n,{threshold:r}),d=Object(o.useDispatch)(c.b).setValue,g=Object(o.useDispatch)(l.a).markNotificationSeen,f=!!(null==i?void 0:i.isIntersecting),m=Object(u.a)(t),p=Object(a.useRef)(),b=function(){p.current&&clearTimeout(p.current)};return Object(a.useEffect)((function(){return!m&&f?(b(),p.current=setTimeout((function(){(null==i?void 0:i.isIntersecting)&&(d(u.a.getKey(t),!0),g(t))}),3e3)):!f&&p.current&&b(),function(){b()}}),[m,f,d,g,t,i]),null}ViewedStateObserver.propTypes={id:i.a.string,observeRef:i.a.object,threshold:i.a.number}},305:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SimpleNotification}));var r=n(17),i=n(188);function SimpleNotification(t){var n=t.actions,a=t.description,o=t.title;return e.createElement(r.e,null,e.createElement(r.k,null,e.createElement(r.a,{size:11,className:"googlesitekit-publisher-win__content"},e.createElement(i.a,{title:o}),a,n)))}}).call(this,n(3))},306:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActionsCTALinkDismiss}));var r=n(0),i=n.n(r),a=n(2),o=n(4),c=n(33),l=n(263),s=n(307);function ActionsCTALinkDismiss(t){var n=t.id,r=t.className,i=void 0===r?"googlesitekit-publisher-win__actions":r,u=t.ctaLink,d=t.ctaLabel,g=t.ctaDisabled,f=void 0!==g&&g,m=t.onCTAClick,p=t.ctaDismissOptions,b=t.isSaving,v=void 0!==b&&b,h=t.onDismiss,O=void 0===h?function(){}:h,E=t.dismissLabel,y=void 0===E?Object(a.__)("OK, Got it!","google-site-kit"):E,k=t.dismissOnCTAClick,_=void 0===k||k,j=t.dismissExpires,S=void 0===j?0:j,w=t.dismissOptions,N=void 0===w?{}:w,T=t.gaTrackingEventArgs,C=void 0===T?{}:T,A=Object(o.useSelect)((function(e){return!!u&&e(c.a).isNavigatingTo(u)}));return e.createElement("div",{className:i},e.createElement(s.a,{id:n,ctaLink:u,ctaLabel:d,onCTAClick:m,dismissOnCTAClick:_,dismissExpires:S,dismissOptions:p,gaTrackingEventArgs:C,isSaving:v,isDisabled:f}),e.createElement(l.a,{id:n,primary:!1,dismissLabel:y,dismissExpires:S,disabled:A,onDismiss:O,dismissOptions:N,gaTrackingEventArgs:C}))}ActionsCTALinkDismiss.propTypes={id:i.a.string,className:i.a.string,ctaDisabled:i.a.bool,ctaLink:i.a.string,ctaLabel:i.a.string,onCTAClick:i.a.func,isSaving:i.a.bool,onDismiss:i.a.func,ctaDismissOptions:i.a.object,dismissLabel:i.a.string,dismissOnCTAClick:i.a.bool,dismissExpires:i.a.number,dismissOptions:i.a.object,gaTrackingEventArgs:i.a.object}}).call(this,n(3))},307:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALink}));var r=n(6),i=n.n(r),a=n(5),o=n.n(a),c=n(16),l=n.n(c),s=n(14),u=n.n(s),d=n(0),g=n.n(d),f=n(210),m=n(1),p=n(4),b=n(41),v=n(33),h=n(13),O=n(69),E=n(11);function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function CTALink(t){var n=t.id,r=t.ctaLink,a=t.ctaLabel,o=t.onCTAClick,c=t.isSaving,s=t.dismissOnCTAClick,d=void 0!==s&&s,g=t.dismissExpires,y=void 0===g?0:g,_=t.dismissOptions,j=void 0===_?{}:_,S=t.gaTrackingEventArgs,w=t.isDisabled,N=void 0!==w&&w,T=Object(m.useState)(!1),C=u()(T,2),A=C[0],D=C[1],R=Object(f.a)(),L=Object(O.a)(n,null==S?void 0:S.category),x=Object(p.useSelect)((function(e){return!!r&&e(v.a).isNavigatingTo(r)})),I=Object(p.useDispatch)(h.c),P=I.clearError,M=I.receiveError,B=Object(p.useDispatch)(b.a).dismissNotification,H=Object(p.useDispatch)(v.a).navigateTo,z=function(){var e=l()(i.a.mark((function e(t){var a,c,l;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return P("notificationAction",[n]),t.persist(),!t.defaultPrevented&&r&&t.preventDefault(),D(!0),e.next=6,null==o?void 0:o(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:if(a=e.t0,c=a.error,R()&&D(!1),!c){e.next=15;break}return M(c,"notificationAction",[n]),e.abrupt("return");case 15:return l=[L.confirm()],d&&l.push(B(n,k(k({},j),{},{expiresInSeconds:y}))),e.next=19,Promise.all(l);case 19:r&&H(r);case 20:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(E.SpinnerButton,{className:"googlesitekit-notification__cta",href:r,onClick:z,disabled:A||x||N,isSaving:A||x||c},a)}CTALink.propTypes={id:g.a.string,ctaLink:g.a.string,ctaLabel:g.a.string,onCTAClick:g.a.func,dismissOnCTAClick:g.a.bool,dismissExpires:g.a.number,dismissOptions:g.a.object,isDisabled:g.a.bool}}).call(this,n(3))},308:function(e,t,n){"use strict";(function(e){var r=n(1),i=n(2),a=n(4),o=n(19),c=n(31),l=n(67),s=n(309),u=n(165);t.a=function AdSenseAlerts(){var t=Object(a.useSelect)((function(e){return e(o.a).isModuleConnected(l.d)})),n=Object(a.useSelect)((function(e){return e(c.l).getAccountID()})),d=Object(a.useSelect)((function(e){return e(c.l).getNotifications()}));return t&&n&&void 0!==d?e.createElement(r.Fragment,null,d.map((function(t){var n=t.id,r=t.title,a=t.description,o=t.format,c=t.ctaURL,l=t.ctaLabel,d=t.ctaTarget,g=t.severity,f=t.isDismissable;return e.createElement(u.a,{className:"googlesitekit-adsense-alert",key:n,id:n,title:r||"",description:a,WinImageSVG:s.a,format:o||"small",ctaLink:c,ctaLabel:l,ctaTarget:d,type:g,dismiss:Object(i.__)("OK, Got it!","google-site-kit"),isDismissible:f||!0,module:"adsense",moduleName:Object(i._x)("AdSense","Service name","google-site-kit"),dismissExpires:0,showOnce:!1,logo:!0})}))):null}}).call(this,n(3))},309:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("circle",{cx:150,cy:150,r:100,fill:"#D2E3FC"}),o=r.createElement("g",{clipPath:"url(#notification-alert_svg__clip0)",strokeMiterlimit:10,strokeWidth:15},r.createElement("path",{d:"M9.05 157.71c13.32 18.56 38.17 25.89 61 24.15s44.18-11.1 65.14-20.18c9.91-4.3 20.07-8.62 30.85-9.31s22.46 2.9 28.7 11.71a24 24 0 013.78 17.65",stroke:"#4285F4"}),r.createElement("path",{d:"M9.05 157.71c13.32 18.56 38.17 25.89 61 24.15",stroke:"#1A73E8"})),c=r.createElement("path",{stroke:"#C5221F",strokeMiterlimit:10,strokeWidth:10,d:"M225.13 32.33l-67.86 256.23"}),l=r.createElement("path",{stroke:"#A50E0E",strokeMiterlimit:10,strokeWidth:10,d:"M225.13 32.33L193.97 150"}),s=r.createElement("path",{d:"M221.3 13l-94.69 95.2a10 10 0 004.53 16.72L261 159.3a10 10 0 0012.18-12.3L238 17.39A10 10 0 00221.3 13z",fill:"#D93025"}),u=r.createElement("path",{d:"M137.37 111.56l87.36-87.87a3 3 0 015 1.33l32.42 119.6a3 3 0 01-3.66 3.68l-119.76-31.72a3 3 0 01-1.36-5.02z",stroke:"#C5221F",strokeMiterlimit:10,strokeWidth:2}),d=r.createElement("path",{d:"M196.78 118.35a7.38 7.38 0 01-.78-5.84 7.25 7.25 0 013.56-4.64 7.46 7.46 0 015.83-.73 7.51 7.51 0 015.4 9.29 7.51 7.51 0 01-3.52 4.7 7.27 7.27 0 01-5.8.8 7.44 7.44 0 01-4.69-3.58zm12.05-46.1l5.56-21 10.82 2.87-5.55 21-8 25.41-8.6-2.28z",fill:"#FFF"}),g=r.createElement("path",{d:"M198.47 181.73a23.18 23.18 0 01-8.2 14.28c-9.2 7.58-24.24 6.67-32.46-2",stroke:"#4285F4",strokeMiterlimit:10,strokeWidth:15}),f=r.createElement("defs",null,r.createElement("clipPath",{id:"notification-alert_svg__clip0"},r.createElement("circle",{cx:150,cy:150,r:100})));t.a=function SvgNotificationAlert(e){return r.createElement("svg",i({viewBox:"0 0 300 300",fill:"none"},e),a,o,c,l,s,u,d,g,f)}},31:function(e,t,n){"use strict";n.d(t,"l",(function(){return r})),n.d(t,"i",(function(){return i})),n.d(t,"f",(function(){return a})),n.d(t,"e",(function(){return o})),n.d(t,"g",(function(){return c})),n.d(t,"d",(function(){return l})),n.d(t,"h",(function(){return s})),n.d(t,"a",(function(){return u})),n.d(t,"c",(function(){return d})),n.d(t,"b",(function(){return g})),n.d(t,"j",(function(){return f})),n.d(t,"k",(function(){return m}));var r="modules/adsense",i=1,a="READY",o="NEEDS_ATTENTION",c="REQUIRES_REVIEW",l="GETTING_READY",s="background-submit-suspended",u="adsenseAdBlockingFormSettings",d="googlesitekit-ad-blocking-recovery-setup-create-message-cta-clicked",g="ad-blocking-recovery-notification",f={TAG_PLACED:"tag-placed",SETUP_CONFIRMED:"setup-confirmed"},m={PLACE_TAGS:0,CREATE_MESSAGE:1,COMPLETE:2}},310:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InternalServerError}));var r=n(4),i=n(13),a=n(311),o=n(155),c=n(189);function InternalServerError(){var t=Object(r.useSelect)((function(e){return e(i.c).getInternalServerError()}));return t?e.createElement(o.a,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"},e.createElement(a.a,{title:t.title,description:e.createElement(c.a,{text:t.description})})):null}}).call(this,n(3))},311:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationError}));var r=n(17),i=n(312),a=n(188);function NotificationError(t){var n=t.title,o=t.description,c=t.actions;return e.createElement(r.e,null,e.createElement(r.k,null,e.createElement(r.a,{smSize:3,mdSize:7,lgSize:11,className:"googlesitekit-publisher-win__content"},e.createElement(a.a,{title:n}),o,c),e.createElement(i.a,{type:"win-error"})))}}).call(this,n(3))},312:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(0),i=n.n(r),a=n(98),o=n(169),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(a.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:i.a.string}}).call(this,n(3))},313:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return l}));var r=n(20),i=n.n(r),a=n(66),o=n.n(a),c=n(155),l=o()((function(e){return{id:e,Notification:s(e)(c.a)}}));function s(t){return function(n){function WithNotificationID(r){return e.createElement(n,i()({},r,{id:t}))}return WithNotificationID.displayName="WithNotificationID",(n.displayName||n.name)&&(WithNotificationID.displayName+="(".concat(n.displayName||n.name,")")),WithNotificationID}}}).call(this,n(3))},314:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(1),l=n(4),s=n(13),u=n(18),d=n(37),g=n(9),f=function(){var e=Object(u.a)(),t=Object(l.useSelect)((function(e){return e(s.c).isUsingProxy()})),n=Object(l.useSelect)((function(e){return e(s.c).getSetupErrorMessage()}));Object(c.useEffect)((function(){n||void 0===t||function(){var n=o()(i.a.mark((function n(){var r,a;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,Object(d.d)("start_user_setup");case 2:return r=n.sent,n.next=5,Object(d.d)("start_site_setup");case 5:if(a=n.sent,!r.cacheHit){n.next=10;break}return n.next=9,Object(d.c)("start_user_setup");case 9:Object(g.I)("".concat(e,"_setup"),"complete_user_setup",t?"proxy":"custom-oauth");case 10:if(!a.cacheHit){n.next=14;break}return n.next=13,Object(d.c)("start_site_setup");case 13:Object(g.I)("".concat(e,"_setup"),"complete_site_setup",t?"proxy":"custom-oauth");case 14:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}()()}),[e,t,n])}},315:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M9 16h2v-2H9v2zm1-16C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14C7.79 4 6 5.79 6 8h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z",fill:"currentColor"});t.a=function SvgHelp(e){return r.createElement("svg",i({viewBox:"0 0 20 20",fill:"none"},e),a)}},316:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(1),l=n(2),s=n(11),u=n(79),d=n(21);function NewBadge(t){var n=t.tooltipTitle,r=t.learnMoreLink,i=t.forceOpen,a=t.hasLeftSpacing,g=t.hasNoSpacing,f=t.onLearnMoreClick,m=void 0===f?function(){}:f,p=e.createElement(u.a,{className:o()("googlesitekit-new-badge",{"googlesitekit-new-badge--has-no-spacing":g}),label:Object(l.__)("New","google-site-kit"),hasLeftSpacing:a});return n?e.createElement(s.Tooltip,{tooltipClassName:"googlesitekit-new-badge__tooltip",title:e.createElement(c.Fragment,null,n,e.createElement("br",null),e.createElement(d.a,{href:r,onClick:m,external:!0,hideExternalIndicator:!0},Object(l.__)("Learn more","google-site-kit"))),placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,open:i,interactive:!0},p):p}NewBadge.propTypes={tooltipTitle:i.a.string,learnMoreLink:i.a.string,forceOpen:i.a.bool,onLearnMoreClick:i.a.func,hasLeftSpacing:i.a.bool,hasNoSpacing:i.a.bool},t.a=NewBadge}).call(this,n(3))},324:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 6.414L1.415 5l5.292 5.292-1.414 1.415z"}),r.createElement("path",{d:"M14.146.146l1.415 1.414L5.414 11.707 4 10.292z"}));t.a=function SvgConnected(e){return r.createElement("svg",i({viewBox:"0 0 16 12"},e),a)}},325:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 0h2v7H0zM0 10h2v2H0z"}));t.a=function SvgExclamation(e){return r.createElement("svg",i({viewBox:"0 0 2 12"},e),a)}},33:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},336:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportZero}));var r=n(5),i=n.n(r),a=n(20),o=n.n(a),c=n(22),l=n.n(c),s=n(0),u=n.n(s),d=n(1),g=n(156),f=n(183);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function WidgetReportZero(t){var n=t.widgetSlug,r=t.moduleSlug,i=l()(t,["widgetSlug","moduleSlug"]),a=Object(d.useMemo)((function(){return{moduleSlug:r}}),[r]);return Object(g.a)(n,f.a,a),e.createElement(f.a,o()({moduleSlug:r},i))}WidgetReportZero.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:u.a.string.isRequired},f.a.propTypes)}).call(this,n(3))},337:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportError}));var r=n(5),i=n.n(r),a=n(22),o=n.n(a),c=n(0),l=n.n(c),s=n(179);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function WidgetReportError(t){t.widgetSlug;var n=o()(t,["widgetSlug"]);return e.createElement(s.a,n)}WidgetReportError.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:l.a.string.isRequired},s.a.propTypes)}).call(this,n(3))},338:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPDashboardReportError}));var r=n(0),i=n.n(r),a=n(568),o=n(212),c=n(4),l=n(26),s=n(179);function WPDashboardReportError(t){var n=t.moduleSlug,r=t.error,i=Object(o.a)(WPDashboardReportError,"WPDashboardReportError"),u=Object(c.useDispatch)(l.b).setValue,d=r.message,g=Object(c.useSelect)((function(e){return e(l.b).getValue("WPDashboardReportError-".concat(n,"-").concat(d))}));return Object(a.a)((function(){u("WPDashboardReportError-".concat(n,"-").concat(d),i)}),(function(){u("WPDashboardReportError-".concat(n,"-").concat(d),void 0)})),g!==i?null:e.createElement(s.a,{moduleSlug:n,error:r})}WPDashboardReportError.propTypes={moduleSlug:i.a.string.isRequired,error:i.a.object.isRequired}}).call(this,n(3))},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return l})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(15);var r=n(2),i="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===i}function l(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function s(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||l(e)||c(e)||s(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},343:function(e,t,n){"use strict";(function(e){var r=n(52),i=n.n(r),a=n(53),o=n.n(a),c=n(81),l=n.n(c),s=n(82),u=n.n(s),d=n(55),g=n.n(d),f=n(0),m=n.n(f),p=n(1),b=n(17),v=n(21);function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return u()(this,n)}}var O=function(t){l()(LayoutHeader,t);var n=h(LayoutHeader);function LayoutHeader(){return i()(this,LayoutHeader),n.apply(this,arguments)}return o()(LayoutHeader,[{key:"render",value:function(){var t=this.props,n=t.title,r=t.badge,i=t.ctaLabel,a=t.ctaLink,o=a?{alignMiddle:!0,smSize:4,lgSize:6}:{alignMiddle:!0,smSize:4,mdSize:8,lgSize:12};return e.createElement("header",{className:"googlesitekit-layout__header"},e.createElement(b.e,null,e.createElement(b.k,null,n&&e.createElement(b.a,o,e.createElement("h3",{className:"googlesitekit-subheading-1 googlesitekit-layout__header-title"},n,r)),a&&e.createElement(b.a,{smSize:4,lgSize:6,alignMiddle:!0,mdAlignRight:!0},e.createElement(v.a,{href:a,external:!0},i)))))}}]),LayoutHeader}(p.Component);O.propTypes={title:m.a.string,badge:m.a.node,ctaLabel:m.a.string,ctaLink:m.a.string},O.defaultProps={title:"",badge:null,ctaLabel:"",ctaLink:""},t.a=O}).call(this,n(3))},344:function(e,t,n){"use strict";(function(e){var r=n(52),i=n.n(r),a=n(53),o=n.n(a),c=n(81),l=n.n(c),s=n(82),u=n.n(s),d=n(55),g=n.n(d),f=n(0),m=n.n(f),p=n(1),b=n(17),v=n(143);function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return u()(this,n)}}var O=function(t){l()(LayoutFooter,t);var n=h(LayoutFooter);function LayoutFooter(){return i()(this,LayoutFooter),n.apply(this,arguments)}return o()(LayoutFooter,[{key:"render",value:function(){var t=this.props,n=t.ctaLabel,r=t.ctaLink,i=t.footerContent;return e.createElement("footer",{className:"googlesitekit-layout__footer"},e.createElement(b.e,null,e.createElement(b.k,null,e.createElement(b.a,{size:12},r&&n&&e.createElement(v.a,{className:"googlesitekit-data-block__source",name:n,href:r,external:!0}),i))))}}]),LayoutFooter}(p.Component);O.propTypes={ctaLabel:m.a.string,ctaLink:m.a.string},t.a=O}).call(this,n(3))},35:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(10),l=n.n(c),s=n(1),u=n(139),d=n(140),g=n(141),f=n(105),m=n(106),p=n(38);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=Object(s.forwardRef)((function(t,n){var r=t.className,i=t.title,a=t.description,o=t.dismissButton,c=t.ctaButton,s=t.type,b=void 0===s?p.a.INFO:s,v=t.children,h=t.hideIcon;return e.createElement("div",{ref:n,className:l()("googlesitekit-notice","googlesitekit-notice--".concat(b),r)},!h&&e.createElement("div",{className:"googlesitekit-notice__icon"},e.createElement(u.a,{type:b})),e.createElement("div",{className:"googlesitekit-notice__content"},i&&e.createElement(d.a,null,i),a&&e.createElement(g.a,null,a)),((null==o?void 0:o.label)||(null==o?void 0:o.onClick)||(null==c?void 0:c.label)&&((null==c?void 0:c.onClick)||(null==c?void 0:c.href))||v)&&e.createElement("div",{className:"googlesitekit-notice__action"},v,((null==o?void 0:o.label)||(null==o?void 0:o.onClick))&&e.createElement(m.a,{label:o.label,onClick:o.onClick,disabled:o.disabled}),(null==c?void 0:c.label)&&((null==c?void 0:c.onClick)||(null==c?void 0:c.href))&&e.createElement(f.a,{label:c.label,onClick:c.onClick,inProgress:c.inProgress,disabled:c.disabled,href:c.href,external:c.external,hideExternalIndicator:c.hideExternalIndicator})))}));h.TYPES=p.a,h.propTypes={className:o.a.string,title:o.a.oneOfType([o.a.string,o.a.object]),description:o.a.node,type:o.a.oneOf(Object.values(p.a)),dismissButton:o.a.shape(m.a.propTypes),ctaButton:o.a.shape(v(v({},f.a.propTypes),{},{label:o.a.string})),children:o.a.node,hideIcon:o.a.bool},t.a=h}).call(this,n(3))},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return O})),n.d(t,"c",(function(){return E})),n.d(t,"e",(function(){return y})),n.d(t,"b",(function(){return k}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=(n(27),n(9));function l(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,d="googlesitekit_",g="".concat(d).concat("1.157.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),f=["sessionStorage","localStorage"],m=[].concat(f),p=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",r.setItem(a,a),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return v.apply(this,arguments)}function v(){return(v=o()(i.a.mark((function t(){var n,r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=l(m),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(a=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(a);case 11:if(!t.sent){t.next=13;break}u=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(i.a.mark((function e(t){var n,r,a,o,c,l,s;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(g).concat(t)))){e.next=10;break}if(a=JSON.parse(r),o=a.timestamp,c=a.ttl,l=a.value,s=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:l,isError:s});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),O=function(){var t=o()(i.a.mark((function t(n,r){var a,o,l,s,u,d,f,m,p=arguments;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=p.length>2&&void 0!==p[2]?p[2]:{},o=a.ttl,l=void 0===o?c.b:o,s=a.timestamp,u=void 0===s?Math.round(Date.now()/1e3):s,d=a.isError,f=void 0!==d&&d,t.next=3,b();case 3:if(!(m=t.sent)){t.next=14;break}return t.prev=5,m.setItem("".concat(g).concat(n),JSON.stringify({timestamp:u,ttl:l,value:r,isError:f})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),E=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(d)?n:"".concat(g).concat(n),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),y=function(){var t=o()(i.a.mark((function t(){var n,r,a,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(d)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),k=function(){var e=o()(i.a.mark((function e(){var t,n,r,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,y();case 6:t=e.sent,n=l(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return a=r.value,e.next=14,E(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},370:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeSingleRow}));var r=n(0),i=n.n(r),a=n(1);function SettingsNoticeSingleRow(t){var n=t.notice,r=t.LearnMore,i=t.CTA;return e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(i,null)))}SettingsNoticeSingleRow.propTypes={notice:i.a.node.isRequired,LearnMore:i.a.elementType,CTA:i.a.elementType}}).call(this,n(3))},38:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={NEW:"new",SUCCESS:"success",WARNING:"warning",INFO:"info",ERROR:"error"}},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(24),i=n(18);function a(){var e=Object(i.a)();return r.g.includes(e)}},390:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PageHeader}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a),c=n(17),l=n(324),s=n(325),u=n(78);function PageHeader(t){var n=t.title,r=t.icon,a=t.className,o=t.status,d=t.statusText,g=t.fullWidth,f=t.children,m=g?{size:12}:{smSize:4,mdSize:4,lgSize:6},p=""!==o||Boolean(f);return e.createElement("header",{className:"googlesitekit-page-header"},e.createElement(c.k,null,n&&e.createElement(c.a,m,r,e.createElement("h1",{className:i()("googlesitekit-page-header__title",a)},n)),p&&e.createElement(c.a,{smSize:4,mdSize:4,lgSize:6,alignBottom:!0,mdAlignRight:!0},e.createElement("div",{className:"googlesitekit-page-header__details"},o&&e.createElement("span",{className:i()("googlesitekit-page-header__status","googlesitekit-page-header__status--".concat(o))},d,e.createElement(u.a,null,"connected"===o?e.createElement(l.a,{width:10,height:8}):e.createElement(s.a,{width:2,height:12}))),f))))}PageHeader.propTypes={title:o.a.string,icon:o.a.node,className:o.a.string,status:o.a.string,statusText:o.a.string,fullWidth:o.a.bool},PageHeader.defaultProps={title:"",icon:null,className:"googlesitekit-heading-3",status:"",statusText:"",fullWidth:!1}}).call(this,n(3))},394:function(e,t,n){"use strict";var r=n(415);n.d(t,"c",(function(){return r.a}));var i=n(197);n.d(t,"b",(function(){return i.c})),n.d(t,"a",(function(){return i.a}))},4:function(e,t){e.exports=googlesitekit.data},40:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return E})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return O}));var r=n(108),i=e._googlesitekitTrackingData||{},a=i.activeModules,o=void 0===a?[]:a,c=i.isSiteKitScreen,l=i.trackingEnabled,s=i.trackingID,u=i.referenceSiteURL,d=i.userIDHash,g=i.isAuthenticated,f={activeModules:o,trackingEnabled:l,trackingID:s,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:g,pluginVersion:"1.157.0"},m=Object(r.a)(f),p=m.enableTracking,b=m.disableTracking,v=(m.isTrackingEnabled,m.initializeSnippet),h=m.trackEvent,O=m.trackEventOnce;function E(e){e?p():b()}c&&l&&v()}).call(this,n(28))},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r=n(24),i="core/notifications",a=[r.s,r.n,r.l,r.o,r.m]},415:function(e,t,n){"use strict";(function(e){var r=n(10),i=n.n(r),a=n(0),o=n.n(a),c=n(2),l=n(4),s=n(370),u=n(422),d=n(197),g=n(7),f=n(11),m=n(1),p=Object(m.forwardRef)((function(t,n){var r=t.className,a=t.children,o=t.type,m=t.dismiss,p=void 0===m?"":m,b=t.dismissCallback,v=t.dismissLabel,h=void 0===v?Object(c.__)("OK, Got it!","google-site-kit"):v,O=t.Icon,E=void 0===O?Object(d.d)(o):O,y=t.OuterCTA,k=Object(l.useDispatch)(g.a).dismissItem,_=Object(l.useSelect)((function(e){return p?e(g.a).isItemDismissed(p):void 0}));if(p&&_)return null;var j=a?u.a:s.a;return e.createElement("div",{ref:n,className:i()(r,"googlesitekit-settings-notice","googlesitekit-settings-notice--".concat(o),{"googlesitekit-settings-notice--single-row":!a,"googlesitekit-settings-notice--multi-row":a})},e.createElement("div",{className:"googlesitekit-settings-notice__icon"},e.createElement(E,{width:"20",height:"20"})),e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(j,t)),p&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(f.Button,{onClick:function(){"string"==typeof p&&k(p),null==b||b()},tertiary:!0},h)),y&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(y,null)))}));p.propTypes={className:o.a.string,children:o.a.node,notice:o.a.node.isRequired,type:o.a.oneOf([d.b]),Icon:o.a.elementType,LearnMore:o.a.elementType,CTA:o.a.elementType,OuterCTA:o.a.elementType,dismiss:o.a.string,dismissLabel:o.a.string,dismissCallback:o.a.func},t.a=p}).call(this,n(3))},422:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeMultiRow}));var r=n(0),i=n.n(r),a=n(1);function SettingsNoticeMultiRow(t){var n=t.notice,r=t.LearnMore,i=t.CTA,o=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),e.createElement("div",{className:"googlesitekit-settings-notice__inner-row"},e.createElement("div",{className:"googlesitekit-settings-notice__children-container"},o),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(i,null))))}SettingsNoticeMultiRow.propTypes={children:i.a.node.isRequired,notice:i.a.node.isRequired,LearnMore:i.a.elementType,CTA:i.a.elementType}}).call(this,n(3))},43:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},437:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M16.666 7.5V5H15v2.5h-2.5v1.666H15v2.5h1.666v-2.5h2.5V7.5h-2.5zM7.5 10a3.332 3.332 0 100-6.667A3.332 3.332 0 107.5 10zm0-5c.916 0 1.666.75 1.666 1.666 0 .917-.75 1.667-1.666 1.667-.917 0-1.667-.75-1.667-1.667C5.833 5.75 6.583 5 7.5 5zm5.325 7.133c-1.4-.717-3.217-1.3-5.325-1.3-2.109 0-3.925.583-5.325 1.3A2.476 2.476 0 00.833 14.35v2.316h13.333V14.35c0-.934-.508-1.792-1.341-2.217zM12.5 15h-10v-.65c0-.317.166-.6.433-.734A10.09 10.09 0 017.5 12.5c1.975 0 3.575.608 4.566 1.116a.81.81 0 01.434.734V15z",fill:"currentColor"});t.a=function SvgShare(e){return r.createElement("svg",i({viewBox:"0 0 20 20",fill:"none"},e),a)}},453:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(4),l=n(706),s=n(7),u=n(49),d=n(17),g=n(39);function WidgetContextRenderer(t){var n=t.id,r=t.slug,i=t.className,a=t.Header,f=t.Footer,m=Object(g.a)(),p=Object(c.useSelect)((function(e){return m?e(s.a).getViewableModules():null})),b=Object(c.useSelect)((function(e){return r?e(u.a).getWidgetAreas(r):null})),v=Object(c.useSelect)((function(e){return!!r&&e(u.a).isWidgetContextActive(r,{modules:p||void 0})}));return void 0===p?null:e.createElement("div",{id:n,className:o()("googlesitekit-widget-context",{"googlesitekit-hidden":!v},i)},a&&v&&e.createElement(d.e,null,e.createElement(d.k,null,e.createElement(d.a,{size:12},e.createElement(a,null)))),b&&b.map((function(t){return e.createElement(l.a,{key:t.slug,slug:t.slug,contextID:n})})),f&&v&&e.createElement(d.e,null,e.createElement(d.k,null,e.createElement(d.a,{size:12},e.createElement(f,null)))))}WidgetContextRenderer.propTypes={id:i.a.string,slug:i.a.string,className:i.a.string,Header:i.a.elementType,Footer:i.a.elementType},t.a=WidgetContextRenderer}).call(this,n(3))},46:function(e,t){e.exports=googlesitekit.api},48:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(10),l=n.n(c),s=n(23);function PreviewBlock(t){var n,r,a=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,g=t.smallWidth,f=t.smallHeight,m=t.tabletWidth,p=t.tabletHeight,b=t.desktopWidth,v=t.desktopHeight,h=Object(s.e)(),O={width:(n={},i()(n,s.b,g),i()(n,s.c,m),i()(n,s.a,b),i()(n,s.d,b),n),height:(r={},i()(r,s.b,f),i()(r,s.c,p),i()(r,s.a,v),i()(r,s.d,b),r)};return e.createElement("div",{className:l()("googlesitekit-preview-block",a,{"googlesitekit-preview-block--padding":d}),style:{width:O.width[h]||o,height:O.height[h]||c}},e.createElement("div",{className:l()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(3))},49:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var r={BOXES:"boxes",COMPOSITE:"composite"},i={QUARTER:"quarter",HALF:"half",FULL:"full"},a="core/widgets"},51:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r="warning-notification-gtg",i="gtg-setup-cta",a={ERROR_HIGH:30,ERROR_LOW:60,WARNING:100,INFO:150,SETUP_CTA_HIGH:150,SETUP_CTA_LOW:200},o={HEADER:"notification-area-header",DASHBOARD_TOP:"notification-area-dashboard-top",OVERLAYS:"notification-area-overlays"},c={DEFAULT:"default",SETUP_CTAS:"setup-ctas"}},54:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(24),i=n(18),a=r.n,o=r.l;function c(){var e=Object(i.a)();return e===r.n||e===r.o?a:e===r.l||e===r.m?o:null}},587:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 6.414L1.415 5l5.292 5.292-1.414 1.415z"}),r.createElement("path",{d:"M14.146.146l1.415 1.414L5.414 11.707 4 10.292z"}));t.a=function SvgCheck(e){return r.createElement("svg",i({viewBox:"0 0 16 12"},e),a)}},59:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var r=n(0),i=n.n(r),a=n(1),o=n(2),c=n(115),l=n(4),s=n(34),u=n(35),d=n(9);function ErrorNotice(t){var n,r=t.className,i=t.error,g=t.hasButton,f=void 0!==g&&g,m=t.storeName,p=t.message,b=void 0===p?i.message:p,v=t.noPrefix,h=void 0!==v&&v,O=t.skipRetryMessage,E=t.hideIcon,y=void 0!==E&&E,k=Object(l.useDispatch)(),_=Object(l.useSelect)((function(e){return m?e(m).getSelectorDataForError(i):null})),j=Object(a.useCallback)((function(){k(_.storeName).invalidateResolution(_.name,_.args)}),[k,_]);if(!b||Object(s.f)(i))return null;var S=f&&Object(s.d)(i,_),w=b;f||O||(w=Object(o.sprintf)(/* translators: %s: Error message from Google API. */
Object(o.__)("%s (Please try again.)","google-site-kit"),w)),h||(w=Object(o.sprintf)(/* translators: $%s: Error message */
Object(o.__)("Error: %s","google-site-kit"),w));var N=null==i||null===(n=i.data)||void 0===n?void 0:n.reconnectURL;N&&Object(c.a)(N)&&(w=Object(o.sprintf)(/* translators: 1: Original error message 2: Reconnect URL */
Object(o.__)('%1$s To fix this, <a href="%2$s">redo the plugin setup</a>.',"google-site-kit"),w,N));return e.createElement(u.a,{className:r,type:u.a.TYPES.ERROR,description:e.createElement("span",{dangerouslySetInnerHTML:Object(d.F)(w,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}),ctaButton:S?{label:Object(o.__)("Retry","google-site-kit"),onClick:j}:void 0,hideIcon:y})}ErrorNotice.propTypes={className:i.a.string,error:i.a.shape({message:i.a.string}),hasButton:i.a.bool,storeName:i.a.string,message:i.a.string,noPrefix:i.a.bool,skipRetryMessage:i.a.bool,hideIcon:i.a.bool}}).call(this,n(3))},61:function(e,t,n){"use strict";(function(e){var r,i;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(r=e)||void 0===r||null===(i=r._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},612:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetNewBadge}));var r=n(0),i=n.n(r),a=n(1),o=n(4),c=n(7),l=n(316),s=n(9),u=n(49);function WidgetNewBadge(t){var n=t.slug,r=Object(o.useSelect)((function(e){return e(u.a).getWidgetArea(n)})).hasNewBadge,i="widget-area-expirable-new-badge-".concat(n),d=Object(o.useSelect)((function(e){return e(c.a).hasExpirableItem(i)})),g=Object(o.useSelect)((function(e){return e(c.a).isExpirableItemActive(i)})),f=r&&(!1===d||g),m=Object(o.useDispatch)(c.a).setExpirableItemTimers;return Object(a.useEffect)((function(){void 0!==d&&void 0!==g&&r&&!d&&m([{slug:i,expiresInSeconds:4*s.f}])}),[r,i,d,g,m]),!!f&&e.createElement(l.a,null)}WidgetNewBadge.propTypes={slug:i.a.string.isRequired}}).call(this,n(3))},62:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(43);function i(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},630:function(e,t,n){"use strict";n.d(t,"a",(function(){return ScrollEffect}));var r=n(718);function ScrollEffect(){return Object(r.a)(),null}},631:function(e,t,n){"use strict";(function(e){var r=n(14),i=n.n(r),a=n(10),o=n.n(a),c=n(1),l=n(2),s=n(4),u=n(394),d=n(26),g=n(370),f=n(11);t.a=function OfflineNotification(){var t=Object(c.useState)(!1),n=i()(t,2),r=n[0],a=n[1],m=Object(s.useSelect)((function(e){return e(d.b).getIsOnline()}));return Object(c.useEffect)((function(){m&&r&&a(!1)}),[m,r]),e.createElement("div",{"aria-live":"polite"},!m&&!r&&e.createElement("div",{className:o()("googlesitekit-margin-top-0","googlesitekit-margin-bottom-0","googlesitekit-settings-notice-offline-notice","googlesitekit-settings-notice","googlesitekit-settings-notice--single-row","googlesitekit-settings-notice--".concat(u.b))},e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(g.a,{notice:Object(l.__)("You are currently offline. Some features may not be available.","google-site-kit")})),e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(f.Button,{onClick:function(){a(!0)}},Object(l.__)("OK, Got it!","google-site-kit")))))}}).call(this,n(3))},632:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleDashboardEffects}));var r,i=n(27),a=n.n(i),o=n(5),c=n.n(o),l=n(4),s=n(19),u=n(54),d=(r={},c()(r,u.b,"DashboardMainEffectComponent"),c()(r,u.a,"DashboardEntityEffectComponent"),r);function ModuleDashboardEffects(){var t=Object(u.c)(),n=Object(l.useSelect)((function(e){return e(s.a).getModules()}));if(!n)return null;var r=d[t];return Object.values(n).reduce((function(t,n){var i=n[r];return n.active&&i?[].concat(a()(t),[e.createElement(i,{key:n.slug})]):t}),[])}}).call(this,n(3))},65:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(1),i=Object(r.createContext)(""),a=(i.Consumer,i.Provider);t.b=i},67:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"d",(function(){return o}));var r="adsense-connect-cta",i="adsense-connect-cta-tooltip-state",a="adsense-ga4-top-earnings-notice",o="adsense"},69:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(1),i=n(18),a=n(9);function o(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=n.viewAction,c=void 0===o?"view_notification":o,l=n.confirmAction,s=void 0===l?"confirm_notification":l,u=n.dismissAction,d=void 0===u?"dismiss_notification":u,g=n.clickLearnMoreAction,f=void 0===g?"click_learn_more_link":g,m=Object(i.a)(),p=null!=t?t:"".concat(m,"_").concat(e),b=Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,c].concat(t))}),[p,c]),v=Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,s].concat(t))}),[p,s]),h=Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,d].concat(t))}),[p,d]),O=Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,f].concat(t))}),[p,f]);return{view:b,confirm:v,dismiss:h,clickLearnMore:O}}},697:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardSharingSettingsButton}));var r=n(2),i=n(1),a=n(4),o=n(11),c=n(437),l=n(18),s=n(9),u=n(30),d=n(26),g=n(13),f=n(8),m=n(180),p=n(698);function DashboardSharingSettingsButton(){var t=Object(l.a)(),n=Object(a.useDispatch)(d.b).setValue,b=Object(a.useSelect)((function(e){return e(g.c).hasMultipleAdmins()})),v=Object(a.useSelect)((function(e){return e(u.a).getValue(f.d,"isAutoCreatingCustomDimensionsForAudience")})),h=Object(i.useCallback)((function(){Object(s.I)("".concat(t,"_headerbar"),"open_sharing",b?"advanced":"simple"),n(m.c,!0)}),[n,t,b]);return e.createElement(i.Fragment,null,e.createElement(o.Button,{"aria-label":Object(r.__)("Open sharing settings","google-site-kit"),className:"googlesitekit-sharing-settings__button googlesitekit-header__dropdown googlesitekit-border-radius-round googlesitekit-button-icon",onClick:h,icon:e.createElement(c.a,{width:20,height:20}),tooltipEnterDelayInMS:500,disabled:v}),e.createElement(p.a,null))}}).call(this,n(3))},698:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardSharingDialog}));var r=n(14),i=n.n(r),a=n(812),o=n(397),c=n(10),l=n.n(c),s=n(2),u=n(1),d=n(42),g=n(57),f=n(272),m=n(1228),p=n(4),b=n(11),v=n(26),h=n(13),O=n(19),E=n(180),y=n(23),k=n(75),_=n(17),j=n(437),S=n(21),w=n(699),N=n(704);function DashboardSharingDialog(){var t=Object(u.useState)(!1),n=i()(t,2),r=n[0],c=n[1],T=Object(y.e)(),C=Object(a.a)().y,A=Object(p.useDispatch)(v.b).setValue,D=Object(p.useDispatch)(O.a).rollbackSharingSettings,R=Object(p.useSelect)((function(e){return!!e(v.b).getValue(E.c)})),L=Object(p.useSelect)((function(e){return!!e(v.b).getValue(E.b)})),x=Object(p.useSelect)((function(e){return e(v.b).getValue(E.a)})),I=Object(p.useSelect)((function(e){return e(O.a).haveSharingSettingsChanged()})),P=Object(p.useSelect)((function(e){return e(h.c).getDocumentationLinkURL("dashboard-sharing")}));Object(u.useEffect)((function(){if(r){var e=document.querySelector(".googlesitekit-reset-sharing-permissions-button");e&&e.focus(),c(!1)}}),[r]);var M={};T===y.b&&(M.top="".concat(C<46?46-C:0,"px"),M.height="calc(100% - 46px + ".concat(C<46?C:46,"px)")),Object(u.useEffect)((function(){!R&&I&&D()}),[R,I,D]);var B=Object(u.useCallback)((function(){A(E.c,!0)}),[A]),H=Object(u.useCallback)((function(){A(E.c,!1),A(E.a,void 0)}),[A]),z=Object(u.useCallback)((function(){H(),A(E.b,!0)}),[H,A]),W=Object(u.useCallback)((function(){A(E.b,!1),B(),c(!0)}),[B,A]),F=Object(u.useCallback)((function(){if(L)return W(),null;H()}),[W,H,L]);return Object(u.useEffect)((function(){if(L){var e=function(e){e.target.classList.contains("mdc-dialog__scrim")&&W()};return document.addEventListener("click",e),function(){document.removeEventListener("click",e)}}}),[L,W]),Object(o.a)((function(e){return L&&g.c===e.keyCode}),W),e.createElement(k.a,null,e.createElement(_.b,{open:R||L,onClose:F,className:"googlesitekit-dialog googlesitekit-sharing-settings-dialog",style:M,escapeKeyAction:void 0!==x||L?"":"close",scrimClickAction:void 0!==x||L?"":"close"},e.createElement("div",{className:"googlesitekit-dialog__back-wrapper","aria-hidden":T!==y.b},e.createElement(b.Button,{"aria-label":Object(s.__)("Back","google-site-kit"),className:"googlesitekit-dialog__back",onClick:F},e.createElement(f.a,{icon:m.a}))),e.createElement(_.c,{className:"googlesitekit-dialog__content"},e.createElement("div",{className:"googlesitekit-dialog__header"},R&&e.createElement("div",{className:"googlesitekit-dialog__header-icon","aria-hidden":T===y.b},e.createElement("span",null,e.createElement(j.a,{width:20,height:20}))),e.createElement("div",{className:"googlesitekit-dialog__header-titles"},e.createElement("h2",{className:"googlesitekit-dialog__title"},R&&Object(s.__)("Dashboard sharing & permissions","google-site-kit"),L&&Object(s.__)("Reset dashboard sharing permissions","google-site-kit")),e.createElement("p",{className:l()("googlesitekit-dialog__subtitle",{"googlesitekit-dialog__subtitle--emphasis":L})},R&&Object(d.a)(Object(s.__)("Share a view-only version of your Site Kit dashboard with other WordPress roles. <a>Learn more</a>","google-site-kit"),{a:e.createElement(S.a,{"aria-label":Object(s.__)("Learn more about dashboard sharing","google-site-kit"),href:P,external:!0})}),L&&Object(s.__)("Warning: Resetting these permissions will remove view-only access for all users. Are you sure you want to reset all dashboard Sharing permissions?","google-site-kit")))),R&&e.createElement("div",{className:"googlesitekit-dialog__main"},e.createElement(w.a,null))),e.createElement(_.d,{className:"googlesitekit-dialog__footer"},e.createElement(N.a,{closeDialog:F,openResetDialog:z}))))}}).call(this,n(3))},699:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return DashboardSharingSettings}));var i=n(10),a=n.n(i),o=n(2),c=n(4),l=n(700),s=n(19),u=n(13),d=n(7);function DashboardSharingSettings(){var t=Object(c.useSelect)((function(e){return e(s.a).hasRecoverableModules()})),n=Object(c.useSelect)((function(e){return e(u.c).hasMultipleAdmins()})),i=t||n,g=Object(c.useSelect)((function(t){for(var n=t(d.a).getID(),r=t(s.a).getShareableModules(),i=[],a=[],o=[],c=0,l=Object.values(r);c<l.length;c++){var u;(null===(u=(e=l[c]).owner)||void 0===u?void 0:u.id)===n?i.push(e):t(d.a).hasCapability(d.J,e.slug)?a.push(e):o.push(e)}return[].concat(i,a,o)}));return void 0===g?null:r.createElement("div",{className:a()("googlesitekit-dashboard-sharing-settings",{"googlesitekit-dashboard-sharing-settings--has-multiple-admins":i})},r.createElement("header",{className:"googlesitekit-dashboard-sharing-settings__header googlesitekit-dashboard-sharing-settings__row"},r.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--product"},Object(o.__)("Product","google-site-kit")),r.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--view"},Object(o.__)("Who can view","google-site-kit")),i&&r.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--manage"},Object(o.__)("Who can manage view access","google-site-kit"))),r.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__main"},g.map((function(e){var t=e.slug,n=e.name,i=e.owner,a=e.recoverable;return r.createElement(l.a,{key:t,moduleSlug:t,moduleName:n,ownerUsername:null==i?void 0:i.login,recoverable:a})}))))}}).call(this,n(947)(e),n(3))},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return l})),n.d(t,"M",(function(){return s})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return g})),n.d(t,"J",(function(){return f})),n.d(t,"I",(function(){return m})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return O})),n.d(t,"l",(function(){return E})),n.d(t,"m",(function(){return y})),n.d(t,"n",(function(){return k})),n.d(t,"o",(function(){return _})),n.d(t,"q",(function(){return j})),n.d(t,"s",(function(){return S})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return N})),n.d(t,"w",(function(){return T})),n.d(t,"u",(function(){return C})),n.d(t,"v",(function(){return A})),n.d(t,"x",(function(){return D})),n.d(t,"y",(function(){return R})),n.d(t,"A",(function(){return L})),n.d(t,"B",(function(){return x})),n.d(t,"C",(function(){return I})),n.d(t,"D",(function(){return P})),n.d(t,"k",(function(){return M})),n.d(t,"F",(function(){return B})),n.d(t,"z",(function(){return H})),n.d(t,"G",(function(){return z})),n.d(t,"E",(function(){return W})),n.d(t,"i",(function(){return F})),n.d(t,"p",(function(){return V})),n.d(t,"Q",(function(){return U})),n.d(t,"P",(function(){return G}));var r="core/user",i="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",l="googlesitekit_authenticate",s="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",g="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",m="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",O="kmAnalyticsNewVisitors",E="kmAnalyticsPopularAuthors",y="kmAnalyticsPopularContent",k="kmAnalyticsPopularProducts",_="kmAnalyticsReturningVisitors",j="kmAnalyticsTopCities",S="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",N="kmAnalyticsTopCitiesDrivingPurchases",T="kmAnalyticsTopDeviceDrivingPurchases",C="kmAnalyticsTopConvertingTrafficSource",A="kmAnalyticsTopCountries",D="kmAnalyticsTopPagesDrivingLeads",R="kmAnalyticsTopRecentTrendingPages",L="kmAnalyticsTopTrafficSource",x="kmAnalyticsTopTrafficSourceDrivingAddToCart",I="kmAnalyticsTopTrafficSourceDrivingLeads",P="kmAnalyticsTopTrafficSourceDrivingPurchases",M="kmAnalyticsPagesPerVisit",B="kmAnalyticsVisitLength",H="kmAnalyticsTopReturningVisitorPages",z="kmSearchConsolePopularKeywords",W="kmAnalyticsVisitsPerVisitor",F="kmAnalyticsMostEngagingPages",V="kmAnalyticsTopCategories",U=[b,v,h,O,E,y,k,_,V,j,S,w,N,T,C,A,R,L,x,M,B,H,W,F,V],G=[].concat(U,[z])},700:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Module}));var r=n(14),i=n.n(r),a=n(0),o=n.n(a),c=n(10),l=n.n(c),s=n(1),u=n(4),d=n(195),g=n(18),f=n(19),m=n(13),p=n(26),b=n(180),v=n(9),h=n(7),O=n(701),E=n(703);function Module(t){var n=t.moduleSlug,r=t.moduleName,a=t.ownerUsername,o=t.recoverable,c=Object(g.a)(),y=Object(s.useRef)(),k=Object(s.useState)(void 0),_=i()(k,2),j=_[0],S=_[1],w=Object(u.useSelect)((function(e){return e(f.a).hasRecoverableModules()})),N=Object(u.useSelect)((function(e){return e(m.c).hasMultipleAdmins()})),T=w||N,C=Object(u.useSelect)((function(e){var t;return null!==(t=e(f.a).getSharingManagement(n))&&void 0!==t?t:"owner"})),A=Object(u.useSelect)((function(e){return e(h.a).hasCapability(h.I,n)})),D=Object(u.useSelect)((function(e){return e(h.a).hasCapability(h.J,n)})),R=Object(u.useSelect)((function(e){return e(f.a).getSharedOwnershipModules()})),L=Object(u.useSelect)((function(e){return e(p.b).getValue(b.a)})),x=Object(u.useSelect)((function(e){return e(f.a).isDoingSubmitSharingChanges()})),I=Object(u.useSelect)((function(e){return e(m.c).getDocumentationLinkURL("dashboard-sharing-module-recovery")})),P=Object(u.useDispatch)(f.a).setSharingManagement,M=R&&Object.keys(R).includes(n);Object(s.useEffect)((function(){S(M?"all_admins":C)}),[C,M]);var B=Object(u.useSelect)((function(e){return e(f.a).haveModuleSharingSettingsChanged(n,"management")}));Object(s.useEffect)((function(){B&&Object(v.I)("".concat(c,"_sharing"),"change_management_".concat(C),n)}),[B,C,n,c]);var H=Object(s.useCallback)((function(e){var t=e.target.value;S(t),P(n,t)}),[P,S,n]),z=n===L,W=!z&&void 0!==L||x;return e.createElement("div",{className:l()("googlesitekit-dashboard-sharing-settings__module","googlesitekit-dashboard-sharing-settings__row",{"googlesitekit-dashboard-sharing-settings__row--editing":z,"googlesitekit-dashboard-sharing-settings__row--disabled":W}),ref:y},e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--product"},e.createElement(d.a,{slug:n,size:48}),e.createElement("span",{className:"googlesitekit-dashboard-sharing-settings__module-name"},r)),e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--view"},e.createElement(O.a,{moduleSlug:n,isLocked:W,hasSharingCapability:D,recoverable:o,recoverableModuleSupportLink:I,ref:y})),T&&e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--manage"},e.createElement(E.a,{sharedOwnershipModule:M,hasOwnedModule:A,ownerUsername:a,hasSharingCapability:D,manageViewAccess:j,onChange:H})))}Module.propTypes={moduleSlug:o.a.string.isRequired,moduleName:o.a.string.isRequired,ownerUsername:o.a.string,recoverable:o.a.bool}}).call(this,n(3))},701:function(e,t,n){"use strict";(function(e){var r=n(2),i=n(1),a=n(42),o=n(0),c=n.n(o),l=n(702),s=n(35),u=n(21),d=Object(i.forwardRef)((function(t,n){var i=t.moduleSlug,o=t.isLocked,c=t.hasSharingCapability,d=t.recoverable,g=t.recoverableModuleSupportLink;return c?e.createElement(l.a,{moduleSlug:i,isLocked:o,ref:n}):d?e.createElement(s.a,{className:"googlesitekit-notice--small",type:s.a.TYPES.WARNING,description:Object(a.a)(Object(r.__)("Managing user required to manage view access. <a>Learn more</a>","google-site-kit"),{a:e.createElement(u.a,{href:g,external:!0,hideExternalIndicator:!0})}),hideIcon:!0}):e.createElement("p",{className:"googlesitekit-dashboard-sharing-settings__note"},Object(r.__)("Contact managing user to manage view access","google-site-kit"))}));d.propTypes={moduleSlug:c.a.string.isRequired,isLocked:c.a.bool.isRequired,hasSharingCapability:c.a.bool,recoverable:c.a.bool,recoverableModuleSupportLink:c.a.string},d.displayName="ModuleViewAccess",t.a=d}).call(this,n(3))},702:function(e,t,n){"use strict";(function(e){var r=n(27),i=n.n(r),a=n(0),o=n.n(a),c=n(10),l=n.n(c),s=n(2),u=n(57),d=n(1),g=n(4),f=n(11),m=n(21),p=n(437),b=n(587),v=n(18),h=n(121),O=n(9),E=n(19),y=n(26),k=n(180),_=Object(s.__)("All","google-site-kit"),j=Object(d.forwardRef)((function(t,n){var r=t.moduleSlug,a=t.isLocked,o=void 0!==a&&a,c=Object(v.a)(),j=Object(d.useRef)(),S=Object(g.useDispatch)(E.a).setSharedRoles,w=Object(g.useDispatch)(y.b).setValue,N=Object(g.useSelect)((function(e){return e(E.a).getShareableRoles()})),T=Object(g.useSelect)((function(e){return e(E.a).getSharedRoles(r)})),C=Object(g.useSelect)((function(e){return e(y.b).getValue(k.a)}))===r;Object(h.a)([u.c],n,(function(){C&&w(k.a,void 0)}));var A=Object(g.useSelect)((function(e){return e(E.a).haveModuleSharingSettingsChanged(r,"sharedRoles")})),D=Object(d.useCallback)((function(){C?(w(k.a,void 0),A&&Object(O.I)("".concat(c,"_sharing"),"change_shared_roles",r)):w(k.a,r)}),[C,A,r,w,c]);Object(d.useEffect)((function(){j.current&&(C?j.current.firstChild.focus():j.current.focus())}),[C]);var R=Object(d.useCallback)((function(e){var t,n=e.type,a=e.target,o=e.keyCode;if("keydown"!==n||o===u.b){var c,l=a.closest(".mdc-chip"),s=null==l||null===(t=l.dataset)||void 0===t?void 0:t.chipId;if(s)c="all"===s?(null==T?void 0:T.length)===(null==N?void 0:N.length)?[]:N.map((function(e){return e.id})):null===T?[s]:T.includes(s)?T.filter((function(e){return e!==s})):[].concat(i()(T),[s]),S(r,c)}}),[r,S,T,N]);return N?e.createElement("div",{className:l()("googlesitekit-user-role-select",{"googlesitekit-user-role-select--open":C})},!C&&e.createElement(f.Button,{"aria-label":Object(s.__)("Edit roles","google-site-kit"),className:"googlesitekit-user-role-select__button",onClick:D,icon:e.createElement(p.a,{width:23,height:23}),tabIndex:o?-1:void 0,ref:j}),!C&&(null==T?void 0:T.length)>0&&e.createElement("span",{className:"googlesitekit-user-role-select__current-roles"},(null==N?void 0:N.reduce((function(e,t){return T.includes(t.id)&&e.push(t.displayName),e}),[])).join(", ")),!C&&(!T||0===(null==T?void 0:T.length))&&e.createElement("span",{className:"googlesitekit-user-role-select__add-roles"},e.createElement(m.a,{onClick:D,tabIndex:o?-1:void 0},Object(s.__)("Add roles","google-site-kit"))),C&&e.createElement(d.Fragment,null,e.createElement("div",{className:"googlesitekit-user-role-select__chipset",ref:j},e.createElement(f.Chip,{id:"all",label:_,onClick:R,onKeyDown:R,selected:(null==T?void 0:T.length)===(null==N?void 0:N.length),className:"googlesitekit-user-role-select__chip--all"}),N.map((function(t,n){var r=t.id,i=t.displayName;return e.createElement(f.Chip,{key:n,id:r,label:i,onClick:R,onKeyDown:R,selected:null==T?void 0:T.includes(r)})}))),e.createElement(f.Button,{"aria-label":Object(s.__)("Done editing roles","google-site-kit"),title:Object(s.__)("Done","google-site-kit"),className:"googlesitekit-user-role-select__button",onClick:D,icon:e.createElement(b.a,{width:18,height:18}),tabIndex:o?-1:void 0}))):null}));j.propTypes={moduleSlug:o.a.string.isRequired,isLocked:o.a.bool},t.a=j}).call(this,n(3))},703:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleManageAccess}));var r=n(2),i=n(272),a=n(596),o=n(42),c=n(0),l=n.n(c),s=n(11),u=[{value:"owner",label:Object(r.__)("Only me","google-site-kit")},{value:"all_admins",label:Object(r.__)("Any admin signed in with Google","google-site-kit")}];function ModuleManageAccess(t){var n=t.sharedOwnershipModule,c=t.hasOwnedModule,l=t.ownerUsername,d=t.hasSharingCapability,g=t.manageViewAccess,f=t.onChange;return n?e.createElement("p",{className:"googlesitekit-dashboard-sharing-settings__note"},e.createElement("span",null,Object(r.__)("Any admin signed in with Google","google-site-kit")),e.createElement(s.Tooltip,{title:Object(r.__)("This service requires general access to Google APIs rather than access to a specific user-owned property/entity, so view access is manageable by any admin signed in with Google.","google-site-kit")},e.createElement("span",{className:"googlesitekit-dashboard-sharing-settings__tooltip-icon"},e.createElement(i.a,{icon:a.a,size:18})))):c?e.createElement(s.Select,{className:"googlesitekit-dashboard-sharing-settings__select",value:g,options:u,onChange:f,onClick:f,outlined:!0}):l?e.createElement("p",{className:"googlesitekit-dashboard-sharing-settings__note"},Object(o.a)(Object(r.sprintf)(/* translators: %s: user who manages the module. */
Object(r.__)("<span>Managed by</span> <strong>%s</strong>","google-site-kit"),l),{span:e.createElement("span",null),strong:e.createElement("strong",null)}),e.createElement(s.Tooltip,{title:d?Object(r.sprintf)(/* translators: %s: name of the user who manages the module. */
Object(r.__)("%s has connected this and given managing permissions to all admins. You can change who can view this on the dashboard.","google-site-kit"),l):Object(r.sprintf)(/* translators: %s: name of the user who manages the module. */
Object(r.__)("Contact %s to change who can manage view access for this module","google-site-kit"),l)},e.createElement("span",{className:"googlesitekit-dashboard-sharing-settings__tooltip-icon"},e.createElement(i.a,{icon:a.a,size:18})))):null}ModuleManageAccess.propTypes={sharedOwnershipModule:l.a.bool,hasOwnedModule:l.a.bool,ownerUsername:l.a.string,hasSharingCapability:l.a.bool,manageViewAccess:l.a.string,onChange:l.a.func.isRequired}}).call(this,n(3))},704:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Footer}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(14),l=n.n(c),s=n(0),u=n.n(s),d=n(2),g=n(1),f=n(11),m=n(4),p=n(19),b=n(26),v=n(180),h=n(18),O=n(9),E=n(21),y=n(705),k=n(59);function Footer(t){var n=t.closeDialog,r=t.openResetDialog,a=Object(h.a)(),c=Object(g.useState)(null),s=l()(c,2),u=s[0],_=s[1],j=Object(g.useState)(!1),S=l()(j,2),w=S[0],N=S[1],T=Object(m.useSelect)((function(e){return e(p.a).canSubmitSharingChanges()})),C=Object(m.useSelect)((function(e){return e(p.a).isDoingSubmitSharingChanges()})),A=Object(m.useSelect)((function(e){return e(p.a).haveSharingSettingsExpanded("management")})),D=Object(m.useSelect)((function(e){return e(p.a).haveSharingSettingsExpanded("sharedRoles")})),R=Object(m.useSelect)((function(e){return e(p.a).haveSharingSettingsUpdated()})),L=Object(m.useSelect)((function(e){return!!e(b.b).getValue(v.c)})),x=Object(m.useSelect)((function(e){return!!e(b.b).getValue(v.b)})),I=Object(m.useDispatch)(p.a),P=I.resetSharingSettings,M=I.saveSharingSettings,B=Object(m.useDispatch)(b.b).setValue,H=Object(g.useCallback)(o()(i.a.mark((function e(){var t,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return _(null),e.next=3,M();case 3:if(t=e.sent,!(r=t.error)){e.next=8;break}return _(r.message),e.abrupt("return");case 8:Object(O.I)("".concat(a,"_sharing"),"settings_confirm"),B(v.a,void 0),n();case 11:case"end":return e.stop()}}),e)}))),[a,M,B,n]),z=Object(g.useCallback)(o()(i.a.mark((function e(){var t,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return _(null),N(!0),e.next=4,P();case 4:if(t=e.sent,!(r=t.error)){e.next=9;break}return _(r.message),e.abrupt("return");case 9:N(!1),n();case 11:case"end":return e.stop()}}),e)}))),[n,P]),W=Object(g.useCallback)((function(){Object(O.I)("".concat(a,"_sharing"),"settings_cancel"),n()}),[n,a]),F=u||A||D;return e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__footer"},F&&e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__footer-notice"},u&&e.createElement(k.a,{message:u}),!u&&e.createElement(y.a,null)),e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__footer-actions"},R&&L&&!F&&e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__footer-actions-left"},e.createElement(E.a,{onClick:r,className:"googlesitekit-reset-sharing-permissions-button",danger:!0},Object(d.__)("Reset sharing permissions","google-site-kit"))),e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__footer-actions-right"},e.createElement(f.Button,{onClick:W,tertiary:!0},Object(d.__)("Cancel","google-site-kit")),L&&e.createElement(f.SpinnerButton,{onClick:H,disabled:C||!T,isSaving:C},Object(d.__)("Apply","google-site-kit")),x&&e.createElement(f.SpinnerButton,{onClick:z,disabled:w,isSaving:w,danger:!0},Object(d.__)("Reset","google-site-kit")))))}Footer.propTypes={closeDialog:u.a.func.isRequired,openResetDialog:u.a.func.isRequired}}).call(this,n(3))},705:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notice}));var r=n(2),i=n(42),a=n(4),o=n(19);function Notice(){var t=Object(a.useSelect)((function(e){return e(o.a).canSubmitSharingChanges()})),n=Object(a.useSelect)((function(e){return e(o.a).haveSharingSettingsExpanded("management")})),c=Object(a.useSelect)((function(e){return e(o.a).haveSharingSettingsExpanded("sharedRoles")}));return e.createElement("p",{className:"googlesitekit-dashboard-sharing-settings__notice"},n&&t&&Object(i.a)(Object(r.__)("By clicking <strong>Apply</strong>, you will give other authenticated admins of your site permission to manage view-only access to Site Kit dashboard data from the chosen Google service","google-site-kit"),{span:e.createElement("span",null),strong:e.createElement("strong",null)}),!n&&t&&c&&Object(i.a)(Object(r.__)("By clicking <strong>Apply</strong>, you’re granting the selected roles view-only access to data from the Google services you’ve connected via your account","google-site-kit"),{span:e.createElement("span",null),strong:e.createElement("strong",null)}))}}).call(this,n(3))},706:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetAreaRenderer}));var r=n(14),i=n.n(r),a=n(5),o=n.n(a),c=n(0),l=n.n(c),s=n(10),u=n.n(s),d=n(1),g=n(4),f=n(251),m=n(94),p=n(49),b=n(26),v=n(17),h=n(23),O=n(170),E=n(707),y=n(708),k=n(709),_=n(39),j=n(7),S=n(255),w=n(710),N=n(90);function T(e){var t,n=(t={},o()(t,h.d,48),o()(t,h.a,48),o()(t,h.c,32),o()(t,h.b,32),t)[e],r=Math.abs(Object(m.b)(e)+n);return"".concat(-r,"px ").concat(-n,"px ").concat(-n,"px ").concat(-n,"px")}function WidgetAreaRenderer(t){var n,r=t.slug,a=t.contextID,c=Object(_.a)(),l=Object(g.useSelect)((function(e){return c?e(j.a).getViewableModules():null})),s=Object(N.a)(),m=Object(h.e)(),C=Object(d.useRef)(),A=Object(S.a)(C,{rootMargin:T(m),threshold:0}),D=Object(g.useSelect)((function(e){return e(p.a).getWidgetArea(r)})),R=D.Icon,L=D.title,x=D.style,I=D.subtitle,P=D.CTA,M=D.Footer,B=Object(g.useSelect)((function(e){return e(p.a).getWidgets(r,{modules:l||void 0})})),H=Object(g.useSelect)((function(e){return e(p.a).getWidgetStates()})),z=Object(g.useSelect)((function(e){return e(p.a).isWidgetAreaActive(r,{modules:l||void 0})})),W=Object(g.useSelect)((function(e){return e(b.b).getValue(b.a)})),F=Object(d.useState)({key:"WidgetAreaRenderer-".concat(r),value:W?W===a:!!(null==A?void 0:A.intersectionRatio)}),V=i()(F,2),U=V[0],G=V[1];Object(d.useEffect)((function(){G({key:"WidgetAreaRenderer-".concat(r),value:W?W===a:!!(null==A?void 0:A.intersectionRatio)})}),[A,r,W,a]);var q=P&&s<=782;if(void 0===l)return null;var K=Object(f.d)(B,H),X=K.columnWidths,Y=K.rowIndexes,$=Object(f.b)(B,H,{columnWidths:X,rowIndexes:Y}),Z=$.gridColumnWidths,J=$.overrideComponents,Q=B.map((function(t,n){return e.createElement(y.a,{key:"".concat(t.slug,"-wrapper"),gridColumnWidth:Z[n]},e.createElement(k.a,{slug:t.slug},e.createElement(E.a,{OverrideComponent:J[n]?function(){var t=J[n],r=t.Component,i=t.metadata;return e.createElement(r,i)}:void 0,slug:t.slug})))}));return e.createElement(O.a,{value:U},!!z&&e.createElement(v.e,{className:u()("googlesitekit-widget-area","googlesitekit-widget-area--".concat(r),"googlesitekit-widget-area--".concat(x)),ref:C},e.createElement(v.k,null,e.createElement(v.a,{className:"googlesitekit-widget-area-header",size:12},e.createElement(w.a,{slug:r,Icon:R,title:L,subtitle:I,CTA:P}))),e.createElement("div",{className:"googlesitekit-widget-area-widgets"},e.createElement(v.k,null,x===p.b.BOXES&&Q,x===p.b.COMPOSITE&&e.createElement(v.a,{size:12},e.createElement(v.e,null,e.createElement(v.k,null,Q))))),e.createElement(v.k,null,q&&e.createElement(v.a,{className:"googlesitekit-widget-area-footer",lgSize:12,mdSize:4,smSize:2},e.createElement("div",{className:"googlesitekit-widget-area-footer__cta"},e.createElement(P,null))),M&&e.createElement(v.a,{className:"googlesitekit-widget-area-footer",lgSize:12,mdSize:q?4:8,smSize:q?2:4},e.createElement(M,null)))),!z&&e.createElement(v.e,{className:u()(f.a,"googlesitekit-widget-area",(n={},o()(n,"googlesitekit-widget-area--".concat(r),!!r),o()(n,"googlesitekit-widget-area--".concat(x),!!x),n)),ref:C},Q))}WidgetAreaRenderer.propTypes={slug:l.a.string.isRequired,contextID:l.a.string}}).call(this,n(3))},707:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(15),o=n(1),c=n(4),l=n(49),s=n(19),u=n(273),d=n(274),g=n(251),f=n(128),m=n(39),p=n(23);function WidgetRenderer(t){var n,r=t.slug,i=t.OverrideComponent,b=Object(c.useSelect)((function(e){return e(l.a).getWidget(r)})),v=Object(p.e)(),h=Object(g.c)(r),O=h.Widget,E=h.WidgetNull,y=Object(c.useSelect)((function(e){return e(s.a).getRecoverableModules()})),k=Object(m.a)(),_=Object(o.useMemo)((function(){return b&&y&&Object(a.intersection)(b.modules,Object.keys(y))}),[y,b]),j=Object(c.useSelect)((function(e){return e(l.a).isWidgetPreloaded(r)}));if(!b||void 0===_||(null==b||null===(n=b.hideOnBreakpoints)||void 0===n?void 0:n.includes(v)))return e.createElement(E,null);var S=b.Component,w=b.wrapWidget,N=e.createElement(S,h);return k&&(null==_?void 0:_.length)&&(N=e.createElement(d.a,{widgetSlug:r,moduleSlugs:_})),i?N=e.createElement(o.Fragment,null,e.createElement(u.a,{widgetSlug:"overridden"},e.createElement(i,null)),e.createElement("div",{className:f.a},N)):w&&(N=e.createElement(O,null,N)),j?e.createElement("div",{className:f.a},N):N}WidgetRenderer.propTypes={slug:i.a.string.isRequired,OverrideComponent:i.a.elementType},t.a=WidgetRenderer}).call(this,n(3))},708:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(128),o=n(17);function WidgetCellWrapper(t){var n=t.gridColumnWidth,r=t.children;return 0===n?e.createElement("div",{className:a.a},r):n<6?e.createElement(o.a,{lgSize:n,mdSize:4,smSize:2},r):n<8?e.createElement(o.a,{lgSize:n,mdSize:8},r):e.createElement(o.a,{size:n},r)}WidgetCellWrapper.propTypes={gridColumnWidth:i.a.number.isRequired,children:i.a.element.isRequired},t.a=WidgetCellWrapper}).call(this,n(3))},709:function(e,t,n){"use strict";(function(e,r){var i=n(52),a=n.n(i),o=n(53),c=n.n(o),l=n(244),s=n.n(l),u=n(81),d=n.n(u),g=n(82),f=n.n(g),m=n(55),p=n.n(m),b=n(204),v=n.n(b),h=n(0),O=n.n(h),E=n(1),y=n(2),k=n(102),_=n(181),j=n(65),S=n(9);function w(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=p()(e);if(t){var i=p()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return f()(this,n)}}var N=function(t){d()(WidgetErrorHandler,t);var n=w(WidgetErrorHandler);function WidgetErrorHandler(e){var t;return a()(this,WidgetErrorHandler),(t=n.call(this,e)).state={error:null,info:null},t.onErrorClick=t.onErrorClick.bind(s()(t)),t}return c()(WidgetErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Widget error:",t,n),this.setState({error:t,info:n}),Object(S.I)("widget_error","handle_".concat(this.context||"unknown","_error"),"".concat(this.props.slug,"_").concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500-this.props.slug.length-1))}},{key:"onErrorClick",value:function(){var e=this.state,t=e.error,n=e.info;v()("`".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack,"`"))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,i=t.info;return n?r.createElement("div",{className:"googlesitekit-widget-error-handler"},r.createElement(k.a,{description:r.createElement(E.Fragment,null,r.createElement("p",null,Object(y.__)("An error prevented this Widget from being displayed properly. Report the exact contents of the error on the support forum to find out what caused it.","google-site-kit")),r.createElement(_.a,{message:n.message,componentStack:i.componentStack})),onErrorClick:this.onErrorClick,onClick:this.onErrorClick,title:Object(y.__)("Error in Widget","google-site-kit"),error:!0})):e}}]),WidgetErrorHandler}(E.Component);N.contextType=j.b,N.propTypes={children:O.a.node.isRequired},t.a=N}).call(this,n(28),n(3))},71:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 1c6.075 0 11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12 5.925 1 12 1zm0 14a1.5 1.5 0 100 3 1.5 1.5 0 000-3zm-1-2h2V6h-2v7z",fill:"currentColor"});t.a=function SvgWarningNotice(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},710:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetAreaHeader}));var r=n(0),i=n.n(r),a=n(1),o=n(612),c=n(90);function WidgetAreaHeader(t){var n=t.slug,r=t.Icon,i=void 0!==r&&r,l=t.title,s=void 0===l?"":l,u=t.subtitle,d=void 0===u?"":u,g=t.CTA,f=Object(c.a)(),m=g&&f>=783,p="function"==typeof d?d:void 0;return e.createElement(a.Fragment,null,i&&e.createElement(i,{width:33,height:33}),s&&e.createElement("h3",{className:"googlesitekit-widget-area-header__title googlesitekit-heading-3"},s,e.createElement(o.a,{slug:n})),(d||g)&&e.createElement("div",{className:"googlesitekit-widget-area-header__details"},d&&e.createElement("h4",{className:"googlesitekit-widget-area-header__subtitle"},p&&e.createElement(p,null),!p&&d,!s&&e.createElement(o.a,{slug:n})),m&&e.createElement("div",{className:"googlesitekit-widget-area-header__cta"},e.createElement(g,null))))}WidgetAreaHeader.propTypes={slug:i.a.string.isRequired,Icon:i.a.bool,title:i.a.oneOfType([i.a.string,i.a.element]),subtitle:i.a.oneOfType([i.a.string,i.a.elementType]),CTA:i.a.elementType}}).call(this,n(3))},711:function(e,t,n){"use strict";(function(e){var r=n(14),i=n.n(r),a=n(84),o=n(555),c=n(212),l=n(1),s=n(2),u=n(4),d=n(11),g=n(116),f=n(712),m=n(713),p=n(714),b=n(13),v=n(33),h=n(9),O=n(54),E=n(18);t.a=function EntitySearchInput(){var t=Object(c.a)(EntitySearchInput,"EntitySearchInput"),n=Object(l.useState)(!1),r=i()(n,2),y=r[0],k=r[1],_=Object(l.useState)(!1),j=i()(_,2),S=j[0],w=j[1],N=Object(l.useState)(!1),T=i()(N,2),C=T[0],A=T[1],D=Object(E.a)(),R=Object(O.c)(),L=Object(l.useRef)(),x=Object(l.useCallback)((function(){Object(h.I)("".concat(D,"_headerbar"),"open_urlsearch"),k(!0)}),[D]),I=Object(l.useCallback)((function(){Object(h.I)("".concat(D,"_headerbar"),"close_urlsearch"),k(!1)}),[D]),P=Object(l.useState)({}),M=i()(P,2),B=M[0],H=M[1],z=Object(u.useSelect)((function(e){return(null==B?void 0:B.url)?e(b.c).getAdminURL("googlesitekit-dashboard",{permaLink:B.url}):null})),W=Object(u.useDispatch)(v.a).navigateTo;return Object(l.useEffect)((function(){z&&Object(h.I)("".concat(D,"_headerbar_urlsearch"),"open_urldetails").finally((function(){W(z)}))}),[z,W,D]),Object(a.a)((function(){R===O.a&&k(!0)})),Object(o.a)((function(){var e;y||(null==L||null===(e=L.current)||void 0===e||e.focus())}),[y]),y?e.createElement("div",{className:"googlesitekit-entity-search googlesitekit-entity-search--is-open"},e.createElement(g.a,null,e.createElement("label",{htmlFor:t},Object(s.__)("Page/URL Search","google-site-kit"))),e.createElement(p.a,{id:t,match:B,setIsActive:A,setMatch:H,placeholder:Object(s.__)("Enter title or URL…","google-site-kit"),isLoading:S,setIsLoading:w,showDropdown:C,onClose:I,autoFocus:!0}),S&&C&&e.createElement(d.ProgressBar,{className:"googlesitekit-entity-search__loading",compress:!0}),e.createElement("div",{className:"googlesitekit-entity-search__actions"},e.createElement(d.Button,{onClick:I,trailingIcon:e.createElement(m.a,{width:"30",height:"20"}),className:"googlesitekit-entity-search__close",title:Object(s.__)("Close","google-site-kit"),tooltipEnterDelayInMS:500,text:!0,tooltip:!0}))):e.createElement("div",{className:"googlesitekit-entity-search"},e.createElement(d.Button,{className:"googlesitekit-border-radius-round--phone googlesitekit-button-icon--phone",onClick:x,ref:L,title:Object(s.__)("Search","google-site-kit"),trailingIcon:e.createElement(f.a,{width:"20",height:"20"}),tooltipEnterDelayInMS:500,text:!0,tooltip:!0},Object(s.__)("URL Search","google-site-kit")))}}).call(this,n(3))},712:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 9.5c0 1.407-.45 2.714-1.218 3.783L20.49 19 19 20.49l-5.717-5.708A6.463 6.463 0 019.5 16 6.5 6.5 0 1116 9.5zm-11 0C5 11.99 7.01 14 9.5 14S14 11.99 14 9.5 11.99 5 9.5 5 5 7.01 5 9.5z",fill:"currentColor"});t.a=function SvgMagnifyingGlass(e){return r.createElement("svg",i({viewBox:"0 0 20 20",fill:"none"},e),a)}},713:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M15.833 5.342l-1.175-1.175L10 8.825 5.342 4.167 4.167 5.342 8.825 10l-4.658 4.658 1.175 1.175L10 11.175l4.658 4.658 1.175-1.175L11.175 10l4.658-4.658z",fill:"currentColor"});t.a=function SvgCloseDark(e){return r.createElement("svg",i({viewBox:"0 0 20 20",fill:"none"},e),a)}},714:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PostSearcherAutoSuggest}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(14),l=n.n(c),s=n(0),u=n.n(s),d=n(376),g=n(1),f=n(2),m=n(57),p=n(46),b=n(4),v=n(715),h=n(13),O=function(){};function PostSearcherAutoSuggest(t){var n=t.id,r=t.match,a=t.setMatch,c=t.isLoading,s=t.showDropdown,u=void 0===s||s,E=t.setIsLoading,y=void 0===E?O:E,k=t.setIsActive,_=void 0===k?O:k,j=t.autoFocus,S=t.setCanSubmit,w=void 0===S?O:S,N=t.onClose,T=void 0===N?O:N,C=t.placeholder,A=void 0===C?"":C,D=Object(g.useRef)(),R=Object(g.useState)(""),L=l()(R,2),x=L[0],I=L[1],P=null==r?void 0:r.title,M=Object(v.a)(x,x===P?0:200),B=Object(g.useState)([]),H=l()(B,2),z=H[0],W=H[1],F=Object(f.__)("No results found","google-site-kit"),V=Object(b.useSelect)((function(e){return e(h.c).getCurrentEntityTitle()})),U=Object(g.useRef)(null),G=Object(g.useCallback)((function(){_(!0)}),[_]),q=Object(g.useCallback)((function(e){var t,n,r;(null===(t=e.relatedTarget)||void 0===t?void 0:t.classList.contains("autocomplete__option--result"))||(_(!1),I(null!==(n=null!==(r=U.current)&&void 0!==r?r:V)&&void 0!==n?n:""))}),[V,_]),K=Object(g.useCallback)((function(e){if(Array.isArray(z)&&e!==F){var t=z.find((function(t){return t.title.toLowerCase()===e.toLowerCase()}));t?(U.current=t.title,w(!0),a(t),I(t.title)):U.current=null}else U.current=null,w(!1)}),[z,w,a,F,I]),X=Object(g.useCallback)((function(e){w(!1),I(e.target.value)}),[w]);Object(g.useEffect)((function(){if(""!==M&&M!==V&&(null==M?void 0:M.toLowerCase())!==(null==P?void 0:P.toLowerCase())){var e="undefined"==typeof AbortController?void 0:new AbortController;return(t=o()(i.a.mark((function t(){var n,r;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return y(!0),n=Object(p.get)("core","search","entity-search",{query:encodeURIComponent(M)},{useCache:!1,signal:null==e?void 0:e.signal}),D.current=n,t.prev=3,t.next=6,n;case 6:r=t.sent,W(r),t.next=13;break;case 10:t.prev=10,t.t0=t.catch(3),W(null);case 13:return t.prev=13,n===D.current&&y(!1),t.finish(13);case 16:case"end":return t.stop()}}),t,null,[[3,10,13,16]])}))),function(){return t.apply(this,arguments)})(),function(){return null==e?void 0:e.abort()}}var t}),[M,y,V,P]),Object(g.useEffect)((function(){x||W([])}),[x]),Object(g.useEffect)((function(){V&&I(V)}),[V]);var Y=Object(g.useRef)(),$=Object(g.useCallback)((function(e){var t=Y.current;switch(e.keyCode){case m.d:(null==t?void 0:t.value)&&(e.preventDefault(),t.selectionStart=0,t.selectionEnd=0);break;case m.a:(null==t?void 0:t.value)&&(e.preventDefault(),t.selectionStart=t.value.length,t.selectionEnd=t.value.length)}switch(e.keyCode){case m.c:return T();case m.b:return K(x)}}),[T,K,x]);return e.createElement(d.a,{className:"autocomplete__wrapper",onSelect:K},e.createElement(d.b,{ref:Y,id:n,className:"autocomplete__input autocomplete__input--default",type:"text",onBlur:q,onChange:X,onFocus:G,placeholder:A,onKeyDown:$,value:x,autoFocus:j}),!c&&u&&M!==V&&""!==M&&0===(null==z?void 0:z.length)&&e.createElement(d.e,{portal:!1},e.createElement(d.c,{className:"autocomplete__menu autocomplete__menu--inline"},e.createElement(d.d,{value:F,className:"autocomplete__option autocomplete__option--no-results"}))),u&&""!==M&&M!==V&&(null==z?void 0:z.length)>0&&e.createElement(d.e,{portal:!1},e.createElement(d.c,{className:"autocomplete__menu autocomplete__menu--inline"},z.map((function(t){var n=t.id,r=t.title;return e.createElement(d.d,{key:n,value:r,className:"autocomplete__option autocomplete__option--result"})})))))}PostSearcherAutoSuggest.propTypes={id:u.a.string,match:u.a.object,setCanSubmit:u.a.func,setMatch:u.a.func,isLoading:u.a.bool,setIsLoading:u.a.func,onKeyDown:u.a.func,autoFocus:u.a.bool,placeholder:u.a.string}}).call(this,n(3))},715:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(14),i=n.n(r),a=n(1);function o(e,t){var n=Object(a.useState)(e),r=i()(n,2),o=r[0],c=r[1];return Object(a.useEffect)((function(){var n=setTimeout((function(){c(e)}),t);return function(){clearTimeout(n)}}),[e,t]),o}},716:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DateRangeSelector}));var r=n(14),i=n.n(r),a=n(213),o=n(10),c=n.n(o),l=n(1),s=n(57),u=n(2),d=n(4),g=n(11),f=n(717),m=n(7),p=n(121),b=n(9),v=n(26),h=n(18);function DateRangeSelector(){var t,n=Object(b.n)(),r=Object(d.useSelect)((function(e){return e(m.a).getDateRange()})),o=Object(d.useDispatch)(m.a).setDateRange,O=Object(d.useDispatch)(v.b).resetInViewHook,E=Object(l.useState)(!1),y=i()(E,2),k=y[0],_=y[1],j=Object(l.useRef)(),S=Object(h.a)();Object(a.a)(j,(function(){return _(!1)})),Object(p.a)([s.c,s.f],j,(function(){return _(!1)}));var w=Object(l.useCallback)((function(){_(!k)}),[k]),N=Object(l.useCallback)((function(e){var t=Object.values(n)[e].slug;r!==t&&Object(b.I)("".concat(S,"_headerbar"),"change_daterange",t),O(),o(t),_(!1)}),[n,r,O,o,S]),T=null===(t=n[r])||void 0===t?void 0:t.label,C=Object.values(n).map((function(e){return e.label}));return e.createElement("div",{ref:j,className:"googlesitekit-date-range-selector googlesitekit-dropdown-menu mdc-menu-surface--anchor"},e.createElement(g.Button,{className:c()("mdc-button--dropdown","googlesitekit-header__dropdown","googlesitekit-header__date-range-selector-menu","googlesitekit-border-radius-round--phone","googlesitekit-button-icon--phone"),onClick:w,icon:e.createElement(f.a,{width:"20",height:"20"}),"aria-haspopup":"menu","aria-expanded":k,"aria-controls":"date-range-selector-menu",title:Object(u.__)("Date range","google-site-kit"),tooltipEnterDelayInMS:500,text:!0,tooltip:!0},T),e.createElement(g.Menu,{menuOpen:k,menuItems:C,onSelected:N,id:"date-range-selector-menu",className:"googlesitekit-width-auto"}))}}).call(this,n(3))},717:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M6 9H4v2h2V9zm4 0H8v2h2V9zm4 0h-2v2h2V9zm2-7h-1V0h-2v2H5V0H3v2H2C.89 2 .01 2.9.01 4L0 18a2 2 0 002 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 16H2V7h14v11z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgDateRange(e){return r.createElement("svg",i({viewBox:"0 0 18 20"},e),a)}},718:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return c}));var r=n(14),i=n.n(r),a=n(812),o=n(1),c=function(){var t=Object(a.a)().y,n=Object(o.useState)(!1),r=i()(n,2),c=r[0],l=r[1],s="googlesitekit-plugin--has-scrolled";return Object(o.useEffect)((function(){c?e.document.body.classList.add(s):e.document.body.classList.remove(s)}),[c]),t>0&&!c?l(!0):0===t&&c&&l(!1),c}}).call(this,n(28))},719:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return f}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(568),l=n(456),s=n(278),u=n(1),d=n(4),g=n(26);function f(){var t=Object(d.useDispatch)(g.b).setIsOnline,n=Object(d.useSelect)((function(e){return e(g.b).getIsOnline()})),r=Object(u.useCallback)(o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(navigator.onLine){e.next=3;break}return t(!1),e.abrupt("return");case 3:return e.prev=3,e.next=6,Object(s.default)({path:"/google-site-kit/v1/"});case 6:e.next=13;break;case 8:if(e.prev=8,e.t0=e.catch(3),"fetch_error"!==(null===e.t0||void 0===e.t0?void 0:e.t0.code)){e.next=13;break}return t(!1),e.abrupt("return");case 13:t(!0);case 14:case"end":return e.stop()}}),e,null,[[3,8]])}))),[t]);Object(c.a)((function(){e.addEventListener("online",r),e.addEventListener("offline",r)}),(function(){e.removeEventListener("online",r),e.removeEventListener("offline",r)})),Object(l.a)(r,n?12e4:15e3)}}).call(this,n(28))},73:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},74:function(e,t,n){"use strict";n.r(t),n.d(t,"CONTEXT_MAIN_DASHBOARD_KEY_METRICS",(function(){return r})),n.d(t,"CONTEXT_MAIN_DASHBOARD_TRAFFIC",(function(){return i})),n.d(t,"CONTEXT_MAIN_DASHBOARD_CONTENT",(function(){return a})),n.d(t,"CONTEXT_MAIN_DASHBOARD_SPEED",(function(){return o})),n.d(t,"CONTEXT_MAIN_DASHBOARD_MONETIZATION",(function(){return c})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_TRAFFIC",(function(){return l})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_CONTENT",(function(){return s})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_SPEED",(function(){return u})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_MONETIZATION",(function(){return d}));var r="mainDashboardKeyMetrics",i="mainDashboardTraffic",a="mainDashboardContent",o="mainDashboardSpeed",c="mainDashboardMonetization",l="entityDashboardTraffic",s="entityDashboardContent",u="entityDashboardSpeed",d="entityDashboardMonetization";t.default={CONTEXT_MAIN_DASHBOARD_KEY_METRICS:r,CONTEXT_MAIN_DASHBOARD_TRAFFIC:i,CONTEXT_MAIN_DASHBOARD_CONTENT:a,CONTEXT_MAIN_DASHBOARD_SPEED:o,CONTEXT_MAIN_DASHBOARD_MONETIZATION:c,CONTEXT_ENTITY_DASHBOARD_TRAFFIC:l,CONTEXT_ENTITY_DASHBOARD_CONTENT:s,CONTEXT_ENTITY_DASHBOARD_SPEED:u,CONTEXT_ENTITY_DASHBOARD_MONETIZATION:d}},75:function(e,t,n){"use strict";var r=n(14),i=n.n(r),a=n(327),o=n(0),c=n.n(o),l=n(1),s=n(157);function Portal(e){var t=e.children,n=e.slug,r=Object(l.useState)(document.createElement("div")),o=i()(r,1)[0];return Object(a.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(s.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},76:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(36),i=n.n(r),a=n(88),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===i()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},78:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(0),i=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:i}},n)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,n(3))},79:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(10),l=n.n(c),s=n(0),u=n.n(s),d=n(1),g=Object(d.forwardRef)((function(t,n){var r=t.label,a=t.className,c=t.hasLeftSpacing,s=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",i()({ref:n},u,{className:l()("googlesitekit-badge",a,{"googlesitekit-badge--has-left-spacing":s})}),r)}));g.displayName="Badge",g.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=g}).call(this,n(3))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"s",(function(){return a})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return l})),n.d(t,"g",(function(){return s})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"m",(function(){return m})),n.d(t,"n",(function(){return p})),n.d(t,"h",(function(){return b})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return O})),n.d(t,"u",(function(){return E})),n.d(t,"v",(function(){return y})),n.d(t,"f",(function(){return k})),n.d(t,"l",(function(){return _})),n.d(t,"e",(function(){return j})),n.d(t,"t",(function(){return S})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return N})),n.d(t,"b",(function(){return T}));var r="modules/analytics-4",i="account_create",a="property_create",o="webdatastream_create",c="analyticsSetup",l=10,s=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",g="enhanced-measurement-enabled",f="enhanced-measurement-should-dismiss-activation-banner",m="analyticsAccountCreate",p="analyticsCustomDimensionsCreate",b="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",O="dashboardAllTrafficWidgetDimensionValue",E="dashboardAllTrafficWidgetActiveRowIndex",y="dashboardAllTrafficWidgetLoaded",k={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},_={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},j=[_.CONTACT,_.GENERATE_LEAD,_.SUBMIT_LEAD_FORM],S={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},w="audiencePermissionsSetup",N="audienceTileCustomDimensionCreate",T="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={EXTERNAL:"external",INTERNAL:"internal"}},85:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(115);function i(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),i=e.replace(n.origin,"");if(i.length<t)return i;var a=i.length-Math.floor(t)+1;return"…"+i.substr(a)}},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="search-console"},87:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return _})),n.d(t,"d",(function(){return j})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return N})),n.d(t,"b",(function(){return T}));var r=n(14),i=n.n(r),a=n(36),o=n.n(a),c=n(5),l=n.n(c),s=n(22),u=n.n(s),d=n(15),g=n(66),f=n.n(g),m=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=E(e,t),r=n.formatUnit,i=n.formatDecimal;try{return r()}catch(e){return i()}},h=function(e){var t=O(e),n=t.hours,r=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(i):"".concat(n,":").concat(r,":").concat(i)},O=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},E=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=O(e),r=n.hours,i=n.minutes,a=n.seconds;return{hours:r,minutes:i,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=b(b({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(a,b(b({},o),{},{unit:"second"})):Object(m.sprintf)(/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?w(a,b(b({},o),{},{unit:"second"})):"",i?w(i,b(b({},o),{},{unit:"minute"})):"",r?w(r,b(b({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(m.sprintf)(
// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(m.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(m.sprintf)(
// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(m.__)("%dm","google-site-kit"),i),o=Object(m.sprintf)(
// translators: %s: number of hours with "h" as the abbreviated unit.
Object(m.__)("%dh","google-site-kit"),r);return Object(m.sprintf)(/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",i?n:"",r?o:"").trim()}}},y=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},k=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(m.sprintf)(
// translators: %s: an abbreviated number in millions.
Object(m.__)("%sM","google-site-kit"),w(y(e),e%10==0?{}:t)):1e4<=e?Object(m.sprintf)(
// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),w(y(e))):1e3<=e?Object(m.sprintf)(
// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),w(y(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function _(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=b({},e)),t}function j(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=_(t),r=n.style,i=void 0===r?"metric":r;return"metric"===i?k(e):"duration"===i?v(e,n):"durationISO"===i?h(e):w(e,n)}var S=f()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,a=u()(t,["locale"]);try{return new Intl.NumberFormat(r,a).format(e)}catch(t){S("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},l=["signDisplay","compactDisplay"],s={},d=0,g=Object.entries(a);d<g.length;d++){var f=i()(g[d],2),m=f[0],p=f[1];c[m]&&p===c[m]||(l.includes(m)||(s[m]=p))}try{return new Intl.NumberFormat(r,s).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},N=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,i=t.style,a=void 0===i?"long":i,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var l=new Intl.ListFormat(r,{style:a,type:c});return l.format(e)}
/* translators: used between list items, there is a space after the comma. */var s=Object(m.__)(", ","google-site-kit");return e.join(s)},T=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},88:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(160),i=n.n(r)()(e)}).call(this,n(28))},89:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a);function ChangeArrow(t){var n=t.direction,r=t.invertColor,i=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:i,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(3))},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return i.b})),n.d(t,"J",(function(){return i.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return b})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return _})),n.d(t,"c",(function(){return j})),n.d(t,"e",(function(){return S})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return N})),n.d(t,"f",(function(){return T})),n.d(t,"n",(function(){return C})),n.d(t,"w",(function(){return A})),n.d(t,"p",(function(){return D})),n.d(t,"G",(function(){return R})),n.d(t,"s",(function(){return L})),n.d(t,"v",(function(){return x})),n.d(t,"k",(function(){return I})),n.d(t,"o",(function(){return P.b})),n.d(t,"h",(function(){return P.a})),n.d(t,"t",(function(){return M.b})),n.d(t,"q",(function(){return M.a})),n.d(t,"A",(function(){return M.c})),n.d(t,"x",(function(){return B})),n.d(t,"u",(function(){return H})),n.d(t,"E",(function(){return F})),n.d(t,"D",(function(){return V.a})),n.d(t,"g",(function(){return U})),n.d(t,"L",(function(){return G})),n.d(t,"l",(function(){return q}));var r=n(15),i=n(40),a=n(76),o=n(36),c=n.n(o),l=n(103),s=n.n(l),u=function(e){return s()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var i=t[r];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),n[r]=i})),n}(e)))};n(104);var d=n(87);function g(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function m(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,r=[g,f,m];n<r.length;n++){t=(0,r[n])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(14),O=n.n(h),E=n(12),y=n.n(E),k=n(2),_="Invalid dateString parameter, it must be a string.",j='Invalid date range, it must be a string with the format "last-x-days".',S=60,w=60*S,N=24*w,T=7*N;function C(){var e=function(e){return Object(k.sprintf)(/* translators: %s: number of days */
Object(k._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function A(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function D(e){y()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function R(e){y()(A(e),_);var t=e.split("-"),n=O()(t,3),r=n[0],i=n[1],a=n[2];return new Date(r,i-1,a)}function L(e,t){return D(I(e,t*N))}function x(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function I(e,t){y()(A(e)||Object(r.isDate)(e)&&!isNaN(e),_);var n=A(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var P=n(107),M=n(85);function B(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function H(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var z=n(27),W=n.n(z),F=function(e){return Array.isArray(e)?W()(e).sort():e},V=n(92);function U(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var G=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},q=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(14),i=n.n(r),a=n(196),o=n(138),c={},l=void 0===e?null:e,s=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,d=e.initialHeight,g=void 0===d?0:d,f=Object(a.a)("undefined"==typeof document?[u,g]:s,t,n),m=i()(f,2),p=m[0],b=m[1],v=function(){return b(s)};return Object(o.a)(l,"resize",v),Object(o.a)(l,"orientationchange",v),p},d=function(e){return u(e)[0]}}).call(this,n(28))},92:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(12),i=n.n(r),a=function(e,t){var n=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},94:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(23),i=n(120);function a(t,n){var r=document.querySelector(t);if(!r)return 0;var i=r.getBoundingClientRect().top,a=o(n);return i+e.scrollY-a}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,a=document.querySelector(".googlesitekit-header");return n=!!a&&"sticky"===e.getComputedStyle(a).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===r.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==r.b?t.offsetHeight:0}(t),(n=Object(i.a)(n))<0?0:n}}).call(this,n(28))},97:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},98:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return r.createElement("svg",i({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),a,o)}},99:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))}},[[1293,1,0]]]);