(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[22],{101:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return O})),n.d(t,"a",(function(){return TourTooltips}));var i=n(5),a=n.n(i),o=n(84),c=n(32),s=n(0),l=n.n(s),u=n(2),g=n(4),d=n(26),f=n(7),p=n(40),m=n(119),b=n(18);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var h={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},y={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},O={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},j="feature_tooltip_view",I="feature_tooltip_advance",k="feature_tooltip_return",E="feature_tooltip_dismiss",S="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,i=t.tourID,s=t.gaEventCategory,l=t.callback,u="".concat(i,"-step"),_="".concat(i,"-run"),M=Object(g.useDispatch)(d.b).setValue,D=Object(g.useDispatch)(f.a).dismissTour,N=Object(g.useRegistry)(),w=Object(b.a)(),A=Object(g.useSelect)((function(e){return e(d.b).getValue(u)||0})),T=Object(g.useSelect)((function(e){return e(d.b).getValue(_)&&!1===e(f.a).isTourDismissed(i)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),M(_,!0)}));var C=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,i=e.size,a=e.status,o=e.type,l=t+1,u="function"==typeof s?s(w):s;o===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(p.b)(u,j,l):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(p.b)(u,E,l):n===c.a.NEXT&&a===c.d.FINISHED&&o===c.b.TOUR_END&&i===l&&Object(p.b)(u,S,l),r===c.c.COMPLETE&&a!==c.d.FINISHED&&(n===c.a.PREV&&Object(p.b)(u,k,l),n===c.a.NEXT&&Object(p.b)(u,I,l))}(t);var n=t.action,r=t.index,a=t.status,o=t.step,g=t.type,d=n===c.a.CLOSE,f=!d&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(g),m=[c.d.FINISHED,c.d.SKIPPED].includes(a),b=d&&g===c.b.STEP_AFTER,v=m||b;if(c.b.STEP_BEFORE===g){var h,y,O=o.target;"string"==typeof o.target&&(O=e.document.querySelector(o.target)),null===(h=O)||void 0===h||null===(y=h.scrollIntoView)||void 0===y||y.call(h,{block:"center"})}f?function(e,t){M(u,e+(t===c.a.PREV?-1:1))}(r,n):v&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),D(i)),l&&l(t,N)},floaterProps:O,locale:y,run:T,stepIndex:A,steps:C,styles:h,tooltipComponent:m.a,continuous:!0,disableOverlayClose:!0,disableScrolling:!0,showProgress:!0})}TourTooltips.propTypes={steps:l.a.arrayOf(l.a.object).isRequired,tourID:l.a.string.isRequired,gaEventCategory:l.a.oneOfType([l.a.string,l.a.func]).isRequired,callback:l.a.func}}).call(this,n(28),n(3))},102:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(11),s=n(21);function CTA(t){var n=t.title,r=t.headerText,i=t.headerContent,a=t.description,l=t.ctaLink,u=t.ctaLabel,g=t.ctaLinkExternal,d=t.ctaType,f=t.error,p=t.onClick,m=t["aria-label"],b=t.children;return e.createElement("div",{className:o()("googlesitekit-cta",{"googlesitekit-cta--error":f})},(r||i)&&e.createElement("div",{className:"googlesitekit-cta__header"},r&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},r),i),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),a&&"string"==typeof a&&e.createElement("p",{className:"googlesitekit-cta__description"},a),a&&"string"!=typeof a&&e.createElement("div",{className:"googlesitekit-cta__description"},a),u&&"button"===d&&e.createElement(c.Button,{"aria-label":m,href:l,onClick:p},u),u&&"link"===d&&e.createElement(s.a,{href:l,onClick:p,"aria-label":m,external:g,hideExternalIndicator:g,arrow:!0},u),b))}CTA.propTypes={title:i.a.string.isRequired,headerText:i.a.string,description:i.a.oneOfType([i.a.string,i.a.node]),ctaLink:i.a.string,ctaLinkExternal:i.a.bool,ctaLabel:i.a.string,ctaType:i.a.string,"aria-label":i.a.string,error:i.a.bool,onClick:i.a.func,children:i.a.node,headerContent:i.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(3))},104:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return u}));var r,i=n(52),a=n.n(i),o=n(53),c=n.n(o),s=function(t){var n=e[t];if(!n)return!1;try{var r="__storage_test__";return n.setItem(r,r),n.removeItem(r),!0}catch(e){return e instanceof DOMException&&(22===e.code||1014===e.code||"QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name)&&0!==n.length}},l=function(){function NullStorage(){a()(this,NullStorage)}return c()(NullStorage,[{key:"key",value:function(){return null}},{key:"getItem",value:function(){return null}},{key:"setItem",value:function(){}},{key:"removeItem",value:function(){}},{key:"clear",value:function(){}},{key:"length",get:function(){return 0}}]),NullStorage}(),u=function(){return r||(r=s("sessionStorage")?e.sessionStorage:s("localStorage")?e.localStorage:new l),r}}).call(this,n(28))},105:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTAButton}));var r=n(0),i=n.n(r),a=n(11),o=n(73);function CTAButton(t){var n,r=t.label,i=t.disabled,c=t.inProgress,s=t.onClick,l=t.href,u=t.external,g=t.hideExternalIndicator;return u&&!g&&(n=e.createElement(o.a,{width:14,height:14})),e.createElement(a.SpinnerButton,{className:"googlesitekit-notice__cta",disabled:i,isSaving:c,onClick:s,href:l,target:u?"_blank":"_self",trailingIcon:n},r)}CTAButton.propTypes={label:i.a.string.isRequired,disabled:i.a.bool,inProgress:i.a.bool,onClick:i.a.func,href:i.a.string,external:i.a.bool,hideExternalIndicator:i.a.bool}}).call(this,n(3))},106:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DismissButton}));var r=n(0),i=n.n(r),a=n(2),o=n(11);function DismissButton(t){var n=t.label,r=void 0===n?Object(a.__)("Got it","google-site-kit"):n,i=t.onClick,c=t.disabled;return e.createElement(o.Button,{onClick:i,disabled:c,tertiary:!0},r)}DismissButton.propTypes={label:i.a.string,onClick:i.a.func.isRequired,disabled:i.a.bool}}).call(this,n(3))},107:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var r=n(245),i=n(89),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(r.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(3))},108:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(5),i=n.n(r),a=n(15),o=n(109),c=n(110);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function g(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=l(l({},u),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(i,n),g=Object(c.a)(i,n,s,r),d={},f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);d[r]||(d[r]=Object(a.once)(g)),d[r].apply(d,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:g,trackEventOnce:f}}}).call(this,n(28))},109:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(62),i=n(43),a=n(61);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,l=t.referenceSiteURL,u=t.userIDHash,g=t.userRoles,d=void 0===g?[]:g,f=t.isAuthenticated,p=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(i.b,"]"))),!o){o=!0;var r=(null==d?void 0:d.length)?d.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:l,plugin_version:p||"",enabled_features:Array.from(a.a).join(","),active_modules:s.join(","),authenticated:f?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var g=n.createElement("script");return g.setAttribute(i.b,""),g.async=!0,g.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),n.head.appendChild(g),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,n(28))},1092:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardPopularKeywordsWidget}));var r=n(5),i=n.n(r),a=n(2),o=n(4),c=n(70),s=n(13),l=n(7),u=n(526),g=n(143),d=n(412),f=n(528),p=n(527),m=n(21),b=n(9),v=n(450),h=n(39);function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function DashboardPopularKeywordsWidget(t){var n=t.Widget,r=t.WidgetReportError,i=Object(h.a)(),y=Object(o.useInViewSelect)((function(e){return e(c.b).isGatheringData()})),j=Object(o.useSelect)((function(e){return e(l.a).getDateRangeDates({offsetDays:c.a})})),I=O(O({},j),{},{dimensions:"query",limit:10}),k=Object(o.useSelect)((function(e){return e(s.c).getCurrentEntityURL()}));k&&(I.url=k);var E=Object(o.useInViewSelect)((function(e){return e(c.b).getReport(I)}),[I]),S=Object(o.useSelect)((function(e){return e(c.b).getErrorForSelector("getReport",[I])})),_=Object(o.useSelect)((function(e){return!e(c.b).hasFinishedResolution("getReport",[I])})),M=Object(o.useSelect)((function(e){return i?null:e(c.b).getServiceReportURL(O(O({},Object(d.b)(j)),{},{page:k?"!".concat(k):void 0}))}));function Footer(){return e.createElement(g.a,{className:"googlesitekit-data-block__source",name:Object(a._x)("Search Console","Service name","google-site-kit"),href:M,external:!0})}if(S)return e.createElement(n,{Footer:Footer},e.createElement(r,{moduleSlug:"search-console",error:S}));if(_||void 0===y)return e.createElement(n,{Footer:Footer,noPadding:!0},e.createElement(u.a,{padding:!0}));var D=[{title:k?Object(a.__)("Top search queries for your page","google-site-kit"):Object(a.__)("Top search queries for your site","google-site-kit"),description:Object(a.__)("Most searched for keywords related to your content","google-site-kit"),primary:!0,field:"keys.0",Component:function Component(t){var n=t.fieldValue,r=Object(o.useSelect)((function(e){if(i)return null;var t=e(l.a).getDateRangeDates({offsetDays:c.a}),r=e(s.c).getCurrentEntityURL();return e(c.b).getServiceReportURL(O(O({},Object(d.b)(t)),{},{query:"!".concat(n),page:r?"!".concat(r):void 0}))}));return i?e.createElement("span",null,n):e.createElement(m.a,{href:r,external:!0,hideExternalIndicator:!0},n)}},{title:Object(a.__)("Clicks","google-site-kit"),description:Object(a.__)("Number of times users clicked on your content in search results","google-site-kit"),Component:function Component(t){var n=t.row;return e.createElement("span",null,Object(b.B)(n.clicks,{style:"decimal"}))}},{title:Object(a.__)("Impressions","google-site-kit"),description:Object(a.__)("Counted each time your content appears in search results","google-site-kit"),Component:function Component(t){var n=t.row;return e.createElement("span",null,Object(b.B)(n.impressions,{style:"decimal"}))}}];return e.createElement(n,{Footer:Footer,noPadding:!0},e.createElement(f.a,null,e.createElement(p.a,{rows:E,columns:D,zeroState:v.d,gatheringData:y})))}}).call(this,n(3))},1093:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(14),o=n.n(a),c=n(0),s=n.n(c),l=n(1),u=n(2),g=n(115),d=n(4),f=n(70),p=n(86),m=n(13),b=n(7),v=n(48),h=n(802),y=n(1094),O=n(1095),j=n(19),I=n(39),k=n(8),E=n(29),S=n(1099);function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SearchFunnelWidgetGA4(t){var n=t.Widget,r=t.WidgetReportError,i=Object(l.useState)(0),a=o()(i,2),c=a[0],s=a[1],u=Object(I.a)(),p=Object(d.useSelect)((function(e){return e(j.a).isModuleAvailable(E.g)})),_=Object(d.useSelect)((function(e){return!!p&&(!u||e(b.a).canViewSharedModule(E.g))})),D=Object(d.useSelect)((function(e){return e(j.a).isModuleConnected(E.g)})),N=Object(d.useSelect)((function(e){return e(b.a).getDateRangeNumberOfDays()})),w=Object(d.useSelect)((function(e){return e(m.c).getCurrentEntityURL()})),A=Object(d.useSelect)((function(e){return e(b.a).getDateRangeDates({compare:!0,offsetDays:f.a})})),T=A.endDate,C=A.compareStartDate,R=Object(d.useSelect)((function(e){return e(b.a).getDateRangeDates({compare:!0,offsetDays:k.g})})),P=Object(d.useSelect)((function(e){if(!u)return!1;var t=e(j.a).getRecoverableModules();return void 0!==t?Object.keys(t).includes(E.g):void 0})),x=Object(d.useInViewSelect)((function(e){return D&&_&&!P?e(k.r).getKeyEvents():[]}),[D,_,P]),L={startDate:C,endDate:T,dimensions:"date"},G=M(M({},R),{},{metrics:[{name:"keyEvents"},{name:"engagementRate"}],dimensionFilters:{sessionDefaultChannelGrouping:["Organic Search"]}}),z=M(M(M({},R),G),{},{dimensions:[{name:"date"}],orderby:[{dimension:{dimensionName:"date"}}]}),Z=M(M({},R),{},{metrics:[{name:"totalUsers"}],dimensions:[{name:"date"}],dimensionFilters:{sessionDefaultChannelGrouping:["Organic Search"]},orderby:[{dimension:{dimensionName:"date"}}]});Object(g.a)(w)&&(L.url=w,G.url=w,z.url=w,Z.url=w);var W=Object(d.useInViewSelect)((function(e){return e(f.b).getReport(L)}),[L]),B=Object(d.useSelect)((function(e){return e(f.b).getErrorForSelector("getReport",[L])})),U=Object(d.useSelect)((function(e){return!e(f.b).hasFinishedResolution("getReport",[L])})),V=Object(d.useInViewSelect)((function(e){return D&&_&&!P?e(k.r).getReport(G):null}),[D,_,P,G]),F=Object(d.useInViewSelect)((function(e){return D&&_&&!P?e(k.r).getReport(z):null}),[D,_,P,z]),H=Object(d.useInViewSelect)((function(e){return D&&_&&!P?e(k.r).getReport(Z):null}),[D,_,P,Z]),Y=Object(d.useSelect)((function(e){if(!D||!_||P)return!1;var t=e(k.r).hasFinishedResolution;return!(t("getReport",[G])&&t("getReport",[z])&&t("getReport",[Z])&&t("getKeyEvents",[]))})),Q=Object(d.useSelect)((function(e){if(!D||P)return null;var t=e(k.r).getErrorForSelector;return t("getReport",[G])||t("getReport",[z])||t("getReport",[Z])||t("getKeyEvents",[])})),J=Object(d.useInViewSelect)((function(e){return!(!D||!_||P)&&e(k.r).isGatheringData()}),[D,_,P]),X=Object(d.useInViewSelect)((function(e){return e(f.b).isGatheringData()}));function WidgetFooter(){return e.createElement(y.a,{metrics:SearchFunnelWidgetGA4.metrics,selectedStats:c})}return B?e.createElement(n,{Header:h.a,Footer:WidgetFooter},e.createElement(r,{moduleSlug:"search-console",error:B})):U||Y||void 0===J||void 0===X?e.createElement(n,{Header:h.a,Footer:WidgetFooter,noPadding:!0},e.createElement(v.a,{width:"100%",height:"190px",padding:!0}),e.createElement(v.a,{width:"100%",height:"270px",padding:!0})):e.createElement(n,{Header:h.a,Footer:WidgetFooter,noPadding:!0},e.createElement(O.a,{ga4Data:V,ga4KeyEventsData:x,ga4VisitorsData:H,searchConsoleData:W,handleStatsSelection:s,selectedStats:c,dateRangeLength:N,error:Q,WidgetReportError:r,showRecoverableAnalytics:P}),e.createElement(S.a,{canViewSharedAnalytics4:_,dateRangeLength:N,ga4StatsData:F,ga4VisitorsOverviewAndStatsData:H,isGA4GatheringData:J,isSearchConsoleGatheringData:X,metrics:SearchFunnelWidgetGA4.metrics,searchConsoleData:W,selectedStats:c,showRecoverableAnalytics:P}))}SearchFunnelWidgetGA4.metrics=[{id:"impressions",color:"#6380b8",label:Object(u.__)("Impressions","google-site-kit"),metric:"impressions",service:p.a},{id:"clicks",color:"#4bbbbb",label:Object(u.__)("Clicks","google-site-kit"),metric:"clicks",service:p.a},{id:"users",color:"#3c7251",label:Object(u.__)("Users","google-site-kit"),service:E.g},{id:"keyEvents",color:"#8e68cb",label:Object(u.__)("Key Events","google-site-kit"),service:E.g},{id:"engagement-rate",color:"#8e68cb",label:Object(u.__)("Engagement Rate","google-site-kit"),service:E.g}],SearchFunnelWidgetGA4.propTypes={Widget:s.a.elementType.isRequired,WidgetReportZero:s.a.elementType.isRequired,WidgetReportError:s.a.elementType.isRequired},t.a=SearchFunnelWidgetGA4}).call(this,n(3))},1094:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(115),s=n(2),l=n(70),u=n(86),g=n(7),d=n(13),f=n(412),p=n(9),m=n(8),b=n(143),v=n(4),h=n(39);function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function SourceLinkAnalytics4(){var t=Object(h.a)(),n=Object(v.useSelect)((function(e){if(t)return null;var n=e(m.r).getServiceReportURL,r=e(d.c).getCurrentEntityURL(),i={dates:e(g.a).getDateRangeDates({compare:!0,offsetDays:m.g}),filters:{sessionSource:"google"},otherArgs:{collectionId:"life-cycle"}};return Object(c.a)(r)&&(i.filters.unifiedPagePathScreen=Object(p.t)(r)),n("lifecycle-traffic-acquisition-v2",i)}));return e.createElement(b.a,{href:n,name:Object(s._x)("Analytics","Service name","google-site-kit"),external:!0})}function SourceLinkSearch(t){var n=t.metric,r=Object(h.a)(),a=Object(v.useSelect)((function(e){if(r)return null;var t=e(l.b),a=t.getServiceReportURL,o=t.getPropertyID,c=t.isDomainProperty,s=Object(p.K)(e(d.c).getReferenceSiteURL()),u=e(d.c).getCurrentEntityURL(),m=e(g.a).getDateRangeDates({offsetDays:l.a}),b=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({resource_id:o(),metrics:n},Object(f.b)(m));return u?b.page="!".concat(u):c()&&s&&(b.page="*".concat(s)),a(b)}),[n]);return e.createElement(b.a,{href:a,name:Object(s._x)("Search Console","Service name","google-site-kit"),external:!0})}function Footer(t){var n=t.metrics,r=t.selectedStats;if(!(null==n?void 0:n[r]))return null;var i=n[r],a=i.service,o=i.metric;return a===u.a?e.createElement(SourceLinkSearch,{metric:o}):e.createElement(SourceLinkAnalytics4,null)}Footer.propTypes={metrics:o.a.arrayOf(o.a.object).isRequired,selectedStats:o.a.number.isRequired},t.a=Footer}).call(this,n(3))},1095:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Overview}));var r=n(0),i=n.n(r),a=n(1),o=n(4),c=n(17),s=n(9),l=n(19),u=n(7),g=n(13),d=n(54),f=n(29),p=n(39),m=n(18),b=n(1096),v=n(1098);function Overview(t){var n,r,i,h,y=t.ga4Data,O=t.ga4KeyEventsData,j=t.ga4VisitorsData,I=t.searchConsoleData,k=t.selectedStats,E=t.handleStatsSelection,S=t.dateRangeLength,_=t.error,M=t.WidgetReportError,D=t.showRecoverableAnalytics,N=Object(d.c)(),w=Object(p.a)(),A=Object(m.a)(),T=Object(o.useSelect)((function(e){return e(l.a).isModuleAvailable(f.g)})),C=Object(o.useSelect)((function(e){return!!T&&(!w||e(u.a).canViewSharedModule(f.g))})),R=Object(o.useSelect)((function(e){return e(l.a).isModuleConnected(f.g)})),P=Object(o.useSelect)((function(e){return e(u.a).isAuthenticated()})),x=Object(o.useSelect)((function(e){return e(g.c).getGoogleSupportURL({path:"/analytics/answer/12195621"})})),L=C&&R&&!_&&!D,G=Object(a.useCallback)((function(){Object(s.I)("".concat(A,"_ga4-new-badge"),"click_learn_more_link")}),[A]),z=P&&L&&N===d.b&&(!(null==O?void 0:O.length)||1===(null==O?void 0:O.length)&&"purchase"===O[0].eventName&&"0"===(null==y||null===(n=y.totals)||void 0===n||null===(r=n[0])||void 0===r||null===(i=r.metricValues)||void 0===i||null===(h=i[0])||void 0===h?void 0:h.value));return e.createElement(c.e,null,e.createElement(c.k,null,e.createElement(v.a,{ga4Data:y,ga4VisitorsData:j,searchConsoleData:I,selectedStats:k,handleStatsSelection:E,dateRangeLength:S,showGA4:L,dashboardType:N,showKeyEventsCTA:z,engagementRateLearnMoreURL:x,onGA4NewBadgeLearnMoreClick:G}),e.createElement(b.a,{canViewSharedAnalytics4:C,error:_,showGA4:L,showKeyEventsCTA:z,showRecoverableAnalytics:D,WidgetReportError:M})))}Overview.propTypes={ga4Data:i.a.object,ga4KeyEventsData:i.a.arrayOf(i.a.object),ga4VisitorsData:i.a.object,searchConsoleData:i.a.arrayOf(i.a.object),selectedStats:i.a.number.isRequired,handleStatsSelection:i.a.func.isRequired,error:i.a.object,WidgetReportError:i.a.elementType.isRequired}}).call(this,n(3))},1096:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OptionalCells}));var r=n(20),i=n.n(r),a=n(0),o=n.n(a),c=n(1),s=n(2),l=n(4),u=n(17),g=n(19),d=n(29),f=n(450),p=n(1097),m=n(173),b=n(23),v=n(617);function OptionalCells(t){var n=t.canViewSharedAnalytics4,r=t.error,a=t.showGA4,o=t.showKeyEventsCTA,h=t.showRecoverableGA4,y=t.WidgetReportError,O=Object(b.e)(),j=Object(l.useSelect)((function(e){return e(g.a).isModuleConnected(d.g)})),I=Object(l.useSelect)((function(e){return e(g.a).isModuleActive(d.g)})),k=I&&j,E=Object(v.a)(o),S=E.quarterCellProps,_=E.halfCellProps;return e.createElement(c.Fragment,null,n&&(!j||!I)&&e.createElement(u.a,_,b.b!==O&&e.createElement(f.a,{title:Object(s.__)("Key Events completed","google-site-kit")})),!h&&n&&k&&r&&e.createElement(u.a,_,e.createElement(y,{moduleSlug:"analytics-4",error:r})),a&&e.createElement(u.a,i()({},S,{smSize:4}),o&&e.createElement(p.a,null)),n&&k&&h&&e.createElement(u.a,_,e.createElement(m.a,{moduleSlugs:[d.g]})))}OptionalCells.propTypes={canViewSharedAnalytics4:o.a.bool.isRequired,error:o.a.object,showGA4:o.a.bool.isRequired,showKeyEventsCTA:o.a.bool.isRequired,showRecoverableGA4:o.a.bool,WidgetReportError:o.a.elementType.isRequired}}).call(this,n(3))},1097:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CreateKeyEventCTA}));var r=n(84),i=n(2),a=n(1),o=n(4),c=n(11),s=n(438),l=n(786),u=n(9),g=n(18),d=n(13);function CreateKeyEventCTA(){var t=Object(g.a)(),n="".concat(t,"_search-traffic-widget"),f=Object(o.useSelect)((function(e){return e(d.c).getGoogleSupportURL({path:"/analytics/answer/12844695"})})),p=Object(a.useCallback)((function(){Object(u.I)(n,"click_ga4_keyEvents_cta")}),[n]);return Object(r.a)((function(){Object(u.I)(n,"view_ga4_keyEvents_cta")})),e.createElement("div",{className:"googlesitekit-analytics-cta googlesitekit-analytics-cta--setup-key-events"},e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graphs"},e.createElement(s.a,{title:Object(i.__)("Key Events completed","google-site-kit"),GraphSVG:l.a})),e.createElement("div",{className:"googlesitekit-analytics-cta__details"},e.createElement("p",{className:"googlesitekit-analytics-cta--description"},Object(i.__)("Set up key events to track how well your site fulfills your business objectives","google-site-kit")),e.createElement(c.Button,{href:f,target:"_blank",onClick:p},Object(i.__)("Set up key events","google-site-kit"))))}}).call(this,n(3))},1098:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DataBlocks}));var r=n(20),i=n.n(r),a=n(5),o=n.n(a),c=n(27),s=n.n(c),l=n(0),u=n.n(l),g=n(15),d=n(2),f=n(4),p=n(17),m=n(412),b=n(242),v=n(530),h=n(316),y=n(19),O=n(8),j=n(29),I=n(70),k=n(54),E=n(617);function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function DataBlocks(t){var n=t.ga4Data,r=t.ga4VisitorsData,a=t.searchConsoleData,o=t.selectedStats,c=t.handleStatsSelection,l=t.dateRangeLength,u=t.showGA4,S=t.dashboardType,M=t.showKeyEventsCTA,D=t.engagementRateLearnMoreURL,N=t.onGA4NewBadgeLearnMoreClick,w=Object(f.useSelect)((function(e){return e(y.a).isModuleActive(j.g)})),A=Object(f.useSelect)((function(e){return e(y.a).isModuleConnected(j.g)})),T=Object(f.useInViewSelect)((function(e){return!!A&&e(O.r).isGatheringData()}),[A]),C=Object(f.useInViewSelect)((function(e){return e(I.b).isGatheringData()})),R=Object(m.a)(a,l),P=R.totalClicks,x=R.totalImpressions,L=R.totalClicksChange,G=R.totalImpressionsChange,z=null,Z=null,W=null,B=null,U=null,V=null;if(w&&Object(g.isPlainObject)(n)&&Object(g.isPlainObject)(r)){var F,H,Y,Q,J,X,q,K;z=Object(E.b)(n,0,100).change,Z=null==n||null===(F=n.totals)||void 0===F||null===(H=F[0])||void 0===H||null===(Y=H.metricValues)||void 0===Y||null===(Q=Y[0])||void 0===Q?void 0:Q.value;var $=Object(E.b)(n,1);W=$.datapoint,B=$.change,U=null==r||null===(J=r.totals)||void 0===J||null===(X=J[0])||void 0===X||null===(q=X.metricValues)||void 0===q||null===(K=q[0])||void 0===K?void 0:K.value,V=Object(E.b)(r,0,100).change}var ee=Object(E.a)(M),te=ee.quarterCellProps,ne=ee.halfCellProps,re=ee.oneThirdCellProps,ie=ee.threeQuartersCellProps,ae=ee.fullCellProps,oe=[{id:"impressions",stat:0,title:Object(d.__)("Total Impressions","google-site-kit"),datapoint:x,change:G,isGatheringData:C},{id:"clicks",stat:1,title:Object(d.__)("Total Clicks","google-site-kit"),datapoint:P,change:L,isGatheringData:C}].concat(s()(u?[{id:"visitors",stat:2,title:Object(d.__)("Unique Visitors from Search","google-site-kit"),datapoint:U,change:V,isGatheringData:T}]:[]),s()(u&&S===k.b&&!M?[{id:"keyEvents",stat:3,title:Object(d.__)("Key Events","google-site-kit"),datapoint:Z,change:z,isGatheringData:T}]:[]),s()(u&&S===k.a?[{id:"engagement-rate",stat:4,title:Object(d.__)("Engagement Rate","google-site-kit"),datapoint:W,datapointUnit:"%",change:B,isGatheringData:T,badge:e.createElement(h.a,{tooltipTitle:Object(d.__)("Sessions which lasted 10 seconds or longer, had 1 or more key events, or 2 or more page views.","google-site-kit"),learnMoreLink:D,onLearnMoreClick:N})}]:[])),ce={2:ne,3:ie,4:ae},se={2:_(_({},ne),{},{smSize:2}),3:re,4:te};return e.createElement(p.a,ce[oe.length],e.createElement(v.a,{className:"mdc-layout-grid__inner"},oe.map((function(t,n){return e.createElement(p.a,i()({key:t.id},se[oe.length]),e.createElement(b.a,{stat:t.stat,className:"googlesitekit-data-block--".concat(t.id," googlesitekit-data-block--button-").concat(n+1),title:t.title,datapoint:t.datapoint,datapointUnit:t.datapointUnit?t.datapointUnit:void 0,change:t.change,changeDataUnit:"%",context:"button",selected:o===t.stat,handleStatSelection:c,gatheringData:t.isGatheringData}))}))))}DataBlocks.propTypes={ga4Data:u.a.object,ga4VisitorsData:u.a.object,searchConsoleData:u.a.arrayOf(u.a.object),selectedStats:u.a.number.isRequired,handleStatsSelection:u.a.func.isRequired,dateRangeLength:u.a.number.isRequired,showGA4:u.a.bool,dashboardType:u.a.string,showKeyEventsCTA:u.a.bool,isGA4GatheringData:u.a.bool,isSearchConsoleGatheringData:u.a.bool,engagementRateLearnMoreURL:u.a.string,onGA4NewBadgeLearnMoreClick:u.a.func.isRequired}}).call(this,n(3))},1099:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Chart}));var r=n(0),i=n.n(r),a=n(15),o=n(1),c=n(2),s=n(4),l=n(9),u=n(1100),g=n(450),d=n(19),f=n(17),p=n(23),m=n(29);function Chart(t){var n=t.canViewSharedAnalytics4,r=t.dateRangeLength,i=t.ga4StatsData,b=t.ga4VisitorsOverviewAndStatsData,v=t.isGA4GatheringData,h=t.isSearchConsoleGatheringData,y=t.metrics,O=t.searchConsoleData,j=t.selectedStats,I=Object(p.e)(),k=Object(s.useSelect)((function(e){return e(d.a).isModuleActive(m.g)})),E=Object(s.useSelect)((function(e){return e(d.a).isModuleConnected(m.g)}));return e.createElement(o.Fragment,null,(0===j||1===j)&&e.createElement(u.a,{data:O,dateRangeLength:r,selectedStats:j,metrics:y,gatheringData:h}),n&&(!k||!E)&&p.b===I&&e.createElement(f.e,null,e.createElement(f.k,null,e.createElement(f.a,null,e.createElement(g.a,{title:Object(c.__)("Key Events completed","google-site-kit")})))),2===j&&e.createElement(g.b,{data:b,dateRangeLength:r,selectedStats:0,metrics:y,dataLabels:[Object(c.__)("Unique Visitors","google-site-kit")],tooltipDataFormats:[function(e){return parseFloat(e).toLocaleString()}],statsColor:y[j].color,gatheringData:v,moduleSlug:"analytics-4"}),n&&(3===j||4===j)&&e.createElement(g.b,{data:i,dateRangeLength:r,selectedStats:j-3,metrics:y,dataLabels:[Object(c.__)("Key Events","google-site-kit"),Object(c.__)("Engagement Rate %","google-site-kit")],tooltipDataFormats:[function(e){return parseFloat(e).toLocaleString()},function(e){return Object(l.B)(e/100,{style:"percent",signDisplay:"never",maximumFractionDigits:2})}],chartDataFormats:[a.identity,function(e){return 100*e}],statsColor:y[j].color,gatheringData:v,moduleSlug:"analytics-4"}))}Chart.propTypes={canViewSharedAnalytics4:i.a.bool,dateRangeLength:i.a.number.isRequired,ga4StatsData:i.a.object,ga4VisitorsOverviewAndStatsData:i.a.object,isGA4GatheringData:i.a.bool,isSearchConsoleGatheringData:i.a.bool,metrics:i.a.array.isRequired,searchConsoleData:i.a.array,selectedStats:i.a.number.isRequired}}).call(this,n(3))},11:function(e,t){e.exports=googlesitekit.components},110:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var r=n(6),i=n.n(r),a=n(5),o=n.n(a),c=n(16),s=n.n(c),l=n(62);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e,t,n,r){var a=Object(l.a)(t);return function(){var t=s()(i.a.mark((function t(o,c,s,l){var u;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:s,value:l},t.abrupt("return",new Promise((function(e){var t,n,i=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(i),e()};a("event",c,g(g({},u),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}()}},1100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SearchConsoleStats}));var r=n(5),i=n.n(r),a=n(14),o=n.n(a),c=n(545),s=n.n(c),l=n(0),u=n.n(l),g=n(2),d=n(412),f=n(17),p=n(92),m=n(392);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SearchConsoleStats(t){var n=t.data,r=t.metrics,i=t.selectedStats,a=t.dateRangeLength,c=t.gatheringData,l=Object(p.a)(n,{dateRangeLength:a}),u=l.compareRange,g=l.currentRange,b=Object(d.c)(g,u,r[i].label,r[i].metric,a),h=b.slice(1).map((function(e){return o()(e,1)[0]})),y=s()(h).slice(1),O=v(v({},SearchConsoleStats.chartOptions),{},{hAxis:{format:"MMM d",gridlines:{color:"#fff"},textStyle:{color:"#616161",fontSize:12},ticks:y},series:{0:{color:r[i].color,targetAxisIndex:0},1:{color:r[i].color,targetAxisIndex:0,lineDashStyle:[3,3],lineWidth:1}}}),j=!b.slice(1).some((function(e){return e[2]>0||e[3]>0}));return O.vAxis.viewWindow.max=j?1:void 0,e.createElement(f.e,{className:"googlesitekit-search-console-site-stats"},e.createElement(f.k,null,e.createElement(f.a,{size:12},e.createElement(m.a,{chartType:"LineChart",data:b,loadingHeight:"270px",loadingWidth:"100%",options:O,gatheringData:c}))))}SearchConsoleStats.propTypes={data:u.a.arrayOf(u.a.object).isRequired,dateRangeLength:u.a.number.isRequired,metrics:u.a.arrayOf(u.a.object).isRequired,selectedStats:u.a.number.isRequired},SearchConsoleStats.chartOptions={chart:{title:Object(g.__)("Search Traffic Summary","google-site-kit")},curveType:"function",height:270,width:"100%",chartArea:{height:"80%",left:60,right:25},legend:{position:"top",textStyle:{color:"#616161",fontSize:12}},vAxis:{direction:1,gridlines:{color:"#eee"},minorGridlines:{color:"#eee"},textStyle:{color:"#616161",fontSize:12},titleTextStyle:{color:"#616161",fontSize:12,italic:!1},viewWindow:{min:0}},tooltip:{isHtml:!0,trigger:"both"},focusTarget:"category",crosshair:{color:"gray",opacity:.1,orientation:"vertical",trigger:"both"}}}).call(this,n(3))},1101:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PopularKeywordsWidget}));var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(2),s=n(4),l=n(7),u=n(70),g=n(412),d=n(9),f=n(21),p=n(39),m=n(117),b=n(450);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function PopularKeywordsWidget(t){var n=t.Widget,r=Object(p.a)(),i=Object(s.useSelect)((function(e){return e(l.a).getDateRangeDates({offsetDays:u.a})})),a=h(h({},i),{},{dimensions:"query",limit:100}),o=Object(s.useInViewSelect)((function(e){return e(u.b).getReport(a)}),[a]),v=Object(s.useSelect)((function(e){return e(u.b).getErrorForSelector("getReport",[a])}),[a]),y=Object(s.useSelect)((function(e){return!e(u.b).hasFinishedResolution("getReport",[a])})),O=Object(g.b)(i),j=[{field:"keys.0",Component:function Component(t){var n=t.fieldValue,i=Object(s.useSelect)((function(e){return r?null:e(u.b).getServiceReportURL(h(h({},O),{},{query:"!".concat(n)}))}));return r?e.createElement(m.f,{content:n}):e.createElement(f.a,{href:i,external:!0,hideExternalIndicator:!0},n)}},{field:"ctr",Component:function Component(t){var n=t.fieldValue;return e.createElement("strong",null,Object(c.sprintf)(/* translators: %s: clickthrough rate value */
Object(c.__)("%s CTR","google-site-kit"),Object(d.B)(n,"%")))}}],I=(o||[]).sort((function(e,t){var n=e.ctr,r=void 0===n?0:n,i=t.ctr;return(void 0===i?0:i)-r}));return e.createElement(m.e,{Widget:n,widgetSlug:l.G,loading:y,rows:I,columns:j,ZeroState:b.d,limit:3,error:v,moduleSlug:"search-console"})}PopularKeywordsWidget.propTypes={Widget:o.a.elementType.isRequired}}).call(this,n(3))},1102:function(e,t,n){"use strict";(function(e){var r,i=n(6),a=n.n(i),o=n(5),c=n.n(o),s=n(12),l=n.n(s),u=n(15),g=n(46),d=n(4),f=n(7),p=n(13),m=n(70),b=n(86),v=n(9),h=n(50),y=n(186),O=n(412),j=n(679);function I(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?I(Object(n),!0).forEach((function(t){c()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):I(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var E=Object(h.a)({baseName:"getReport",storeName:m.b,controlCallback:function(e){var t=e.options;return Object(g.get)("modules",b.a,"searchanalytics",t)},reducerCallback:function(e,t,n){var r=n.options;return k(k({},e),{},{reports:k(k({},e.reports),{},c()({},Object(v.H)(r),t))})},argsToParams:function(e){return{options:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.options;l()(Object(u.isPlainObject)(t),"Options for Search Console report must be an object."),l()(Object(y.a)(t),"Either date range or start/end dates must be provided for Search Console report.");var n=t.dimensions;n&&l()(Object(y.d)(n),"Dimensions for Search Console report must be either a string or an array of strings")}}),S=Object(j.a)(b.a,{storeName:m.b,dataAvailable:null===(r=e._googlesitekitModulesData)||void 0===r?void 0:r["data_available_search-console"],selectDataAvailability:Object(d.createRegistrySelector)((function(e){return function(){var t=e(m.b).getSampleReportArgs(),n=e(m.b).getReport(t);if(e(m.b).hasFinishedResolution("getReport",[t]))return e(m.b).getErrorForSelector("getReport",[t])||!Array.isArray(n)?null:!!n.length}}))}),_={getReport:a.a.mark((function e(){var t,n,r=arguments;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>0&&void 0!==r[0]?r[0]:{},e.next=3,d.commonActions.getRegistry();case 3:if(n=e.sent,!n.select(m.b).getReport(t)){e.next=7;break}return e.abrupt("return");case 7:return e.next=9,E.actions.fetchGetReport(t);case 9:case"end":return e.stop()}}),e)}))},M={getReport:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.reports;return n[Object(v.H)(t)]},hasZeroData:Object(d.createRegistrySelector)((function(e){return function(){var t=e(m.b).isGatheringData();if(void 0!==t){if(!0===t)return!0;var n=e(m.b).getSampleReportArgs(),r=e(m.b).getReport(n);if(e(m.b).hasFinishedResolution("getReport",[n]))return!!Array.isArray(r)&&Object(O.e)(r)}}})),getSampleReportArgs:Object(d.createRegistrySelector)((function(e){return function(){var t=e(p.c).getCurrentEntityURL(),n=e(f.a).getDateRangeDates({compare:!0,offsetDays:m.a}),r={startDate:n.compareStartDate,endDate:n.endDate,dimensions:"date"};return t&&(r.url=t),r}}))},D=Object(d.combineStores)(E,S,{initialState:{reports:{}},resolvers:_,selectors:M});D.initialState,D.actions,D.controls,D.reducer,D.resolvers,D.selectors;t.a=D}).call(this,n(28))},111:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},112:function(e,t,n){"use strict";var r=n(131);n.d(t,"a",(function(){return r.a}));var i=n(132);n.d(t,"c",(function(){return i.a}));var a=n(133);n.d(t,"b",(function(){return a.a}))},114:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",i({viewBox:"0 0 14 14",fill:"none"},e),a)}},116:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),s=n.n(c),l=n(10),u=n.n(l);function VisuallyHidden(t){var n=t.className,r=t.children,a=o()(t,["className","children"]);return r?e.createElement("span",i()({},a,{className:u()("screen-reader-text",n)}),r):null}VisuallyHidden.propTypes={className:s.a.string,children:s.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(3))},117:function(e,t,n){"use strict";var r=n(459);n.d(t,"a",(function(){return r.a}));var i=n(460);n.d(t,"b",(function(){return i.a}));var a=n(461);n.d(t,"c",(function(){return a.a}));var o=n(462);n.d(t,"d",(function(){return o.a}));var c=n(463);n.d(t,"e",(function(){return c.a}));var s=n(464);n.d(t,"f",(function(){return s.a}));var l=n(277);n.d(t,"g",(function(){return l.a}));var u=n(208);n.d(t,"h",(function(){return u.a}));n(377)},119:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(20),i=n.n(r),a=n(162),o=n.n(a),c=n(10),s=n.n(c),l=n(0),u=n.n(l),g=n(2),d=n(11),f=n(164),p=n(114);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,l=t.primaryProps,u=t.size,m=t.step,b=t.tooltipProps,v=u>1?Object(f.a)(u):[],h=function(e){return s()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",i()({className:s()("googlesitekit-tour-tooltip",m.className)},b),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},m.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},m.content)),e.createElement(a.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},v.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:h(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(d.Button,i()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),m.cta,l.title&&e.createElement(d.Button,i()({className:"googlesitekit-tooltip-button",text:!0},l),l.title))),e.createElement(d.Button,{className:"googlesitekit-tooltip-close",icon:e.createElement(p.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(g.__)("Close","google-site-kit"),text:!0,hideTooltipTitle:!0})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(3))},122:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return u}));var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(10),s=n.n(c),l=n(2),u={DEFAULT:"default",OVERLAY:"overlay",SMALL:"small",SMALL_OVERLAY:"small-overlay",LARGE:"large"};function GatheringDataNotice(t){var n=t.style;return e.createElement("div",{className:s()("googlesitekit-gathering-data-notice",i()({},"googlesitekit-gathering-data-notice--has-style-".concat(n),!!n))},e.createElement("span",null,Object(l.__)("Gathering data…","google-site-kit")))}GatheringDataNotice.propTypes={style:o.a.oneOf(Object.values(u))},t.b=GatheringDataNotice}).call(this,n(3))},126:function(e,t,n){"use strict";var r=n(340),i=n(328);n.d(t,"b",(function(){return i.a}));var a=n(329);n.d(t,"c",(function(){return a.a}));var o=n(330);n.d(t,"d",(function(){return o.a}));var c=n(331);n.d(t,"a",(function(){return c.a})),t.e=r.a},129:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(225),i=n(15),a=n(1);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object(r.b)((function(){return i.debounce.apply(void 0,t)}),t);return Object(a.useEffect)((function(){return function(){return o.cancel()}}),[o]),o}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r="core/site",i="primary",a="secondary"},130:function(e,t,n){"use strict";n.d(t,"d",(function(){return c})),n.d(t,"b",(function(){return s})),n.d(t,"a",(function(){return l})),n.d(t,"c",(function(){return u})),n.d(t,"e",(function(){return g}));var r=n(36),i=n.n(r),a=n(15),o=n(186);function c(e){var t=function(e){return"string"==typeof e&&/^[a-zA-Z0-9_]+$/.test(e)};return"string"==typeof e?e.split(",").every(t):Object(o.c)(e,(function(e){var n=e.hasOwnProperty("name")&&t(e.name);if(!e.hasOwnProperty("expression"))return n;var r="string"==typeof e.expression;return n&&r}),t)}function s(e){return Object(o.c)(e,(function(e){return e.hasOwnProperty("name")&&"string"==typeof e.name}))}function l(e){var t=["string"];return Object.keys(e).every((function(n){if(t.includes(i()(e[n])))return!0;if(Array.isArray(e[n]))return e[n].every((function(e){return t.includes(i()(e))}));if(Object(a.isPlainObject)(e[n])){var r=Object.keys(e[n]);return!!r.includes("filterType")&&!("emptyFilter"!==e[n].filterType&&!r.includes("value"))}return!1}))}function u(e){var t=["string"],n=["numericFilter","betweenFilter"];return Object.values(e).every((function(e){if(t.includes(i()(e)))return!0;if(Array.isArray(e))return e.every((function(e){return t.includes(i()(e))}));if(!Object(a.isPlainObject)(e))return!1;var r=e.filterType,o=e.value,c=e.fromValue,s=e.toValue;if(r&&!n.includes(r))return!1;var l=Object.keys(e);return r&&"numericFilter"!==r?"betweenFilter"===r&&(l.includes("fromValue")&&l.includes("toValue")&&[c,s].every((function(e){return!Object(a.isPlainObject)(e)||"int64Value"in e}))):l.includes("operation")&&l.includes("value")&&(!Object(a.isPlainObject)(o)||"int64Value"in o)}))}function g(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(a.isPlainObject)(e)&&((!e.hasOwnProperty("desc")||"boolean"==typeof e.desc)&&(e.metric?!e.dimension&&"string"==typeof(null===(t=e.metric)||void 0===t?void 0:t.metricName):!!e.dimension&&"string"==typeof(null===(n=e.dimension)||void 0===n?void 0:n.dimensionName)));var t,n}))}},1302:function(e,t,n){"use strict";n.r(t);var r=n(4),i=n.n(r),a=n(198),o=n.n(a),c=n(421),s=n.n(c),l=n(922),u=(n(758),n(926)),g=n(1092),d=n(1093),f=n(91),p=n(1);function m(){return(m=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var b=p.createElement("path",{fill:"#525252",d:"M97.15 2.73c.46-.55 1.37-1.64 1.82-2.2 27-.1 53.98-.04 80.97-.02.44.5 1.32 1.5 1.75 2 .1.15.4.43.5.57.18.16.5.48.68.65.25.23.77.67 1.03.9.33.3.98.92 1.3 1.24.26.25.76.75 1 1 .25.23.7.7.96.94.25.25.74.75.98 1 .25.25.76.75 1 1 .27.26.78.76 1.03 1.02.25.25.76.75 1.02 1 .23.25.72.73.96.97l1 1c.24.24.73.73.98 1 .3.3.93.94 1.24 1.26.23.26.67.78.9 1.04.17.2.5.55.67.73.18.2.56.6.75.78.43.45 1.3 1.3 1.72 1.76.14.15.43.46.57.6.07.13.25.37.33.48.27.56.8 1.67 1.08 2.22.2 5.67-.07 11.34.06 17-7.7-.1-15.4-.1-23.08.02.26-.16.8-.47 1.07-.63-.7-6.6-.7-13.24-.58-19.87-26.28-.02-52.56-.02-78.83 0 .17 6.83-.08 13.64-.44 20.46l1.27.04c-7.8-.1-15.63-.12-23.44-.02.12-5.66-.14-11.32.03-16.98.3-.57.86-1.72 1.15-2.3.1-.1.26-.34.35-.46.15-.16.45-.47.6-.63.44-.45 1.33-1.3 1.77-1.75.2-.18.6-.56.78-.75.22-.22.65-.64.86-.85.24-.23.7-.7.94-.94.25-.24.74-.73.98-1 .25-.22.74-.72 1-.96.23-.26.72-.75.96-1 .24-.23.7-.7.94-.95.2-.23.63-.67.84-.9.32-.32.96-1 1.28-1.33l1.28-1.2c.2-.2.62-.6.83-.8.23-.22.7-.68.9-.9.26-.24.75-.73 1-.97s.72-.73.97-.97c.23-.24.7-.7.94-.94l.8-.8c.1-.14.35-.4.46-.54zm37.7 166.57c2.8-14.95 12.87-28.3 26.16-35.56.2 12.76.08 25.53-.04 38.3.03 2.54-.08 5.12.42 7.63 6.28 4 13.18 6.96 19.6 10.74 6.25-3.25 12.47-6.6 18.7-9.9 2.18-.66 1.4-3.2 1.62-4.93-.06-13.9-.1-27.82 0-41.73 10.12 4.16 17.33 12.82 23 21.85 1.95 6.62 4.35 13.27 4.08 20.3.38 16.85-9.32 33.53-24.3 41.32-1.08.74-2.8 1.17-2.66 2.8-.96 9.1-.42 18.3.17 27.4h-41c.3-9.72.8-19.43-.05-29.14-7.56-3.6-14.02-9.35-18.68-16.3-6.28-9.55-8.7-21.48-7.03-32.76z"}),v=p.createElement("path",{fill:"#4d4d4d",d:"M96.68 3.26c.12-.13.36-.4.47-.53-.1.13-.35.4-.47.53zm85.02-.76c.12.14.4.42.52.56-.13-.14-.4-.42-.53-.56zM95.9 4.06c.2-.2.6-.6.78-.8-.2.2-.6.6-.78.8zm86.32-1c.17.16.5.48.67.65-.2-.15-.5-.47-.7-.63zM94.96 5c.23-.24.7-.7.94-.94-.24.24-.7.7-.94.93zm87.94-1.3c.25.23.77.67 1.03.9-.26-.23-.78-.67-1.04-.9zM93.98 5.96c.24-.24.73-.73.98-.97-.25.22-.74.7-.98.95zm89.95-1.36c.32.3.97.92 1.3 1.24-.33-.32-.98-.94-1.3-1.25zM93 6.93c.25-.24.74-.73.98-.97l-.97.97zm92.22-1.1l1 1-1-1zm-93.12 2c.22-.22.68-.7.9-.9-.22.2-.68.68-.9.9zm94.13-1l.95.95c-.24-.24-.7-.7-.95-.94zm-94.97 1.8c.2-.2.62-.6.83-.8-.23.2-.64.6-.85.8zm95.92-.85zm-97.2 2.05l1.28-1.2-1.28 1.2zm98.18-1.06c.25.26.76.76 1 1.02-.24-.28-.75-.78-1-1.04zm-99.46 2.4c.32-.33.96-1 1.28-1.34-.32.34-.96 1-1.28 1.34zM189.17 9.8c.26.24.77.74 1.02 1-.27-.26-.78-.76-1.04-1zm-101.3 2.25c.2-.22.62-.66.83-.88-.2.22-.63.66-.84.88zM190.2 10.8c.25.25.76.75 1.02 1-.26-.25-.77-.75-1.03-1zM86.92 13c.24-.23.7-.7.94-.95-.23.24-.7.72-.94.96zm104.3-1.2c.24.25.73.73.97.97-.26-.24-.75-.72-1-.96zM85.95 14c.24-.26.73-.75.97-1-.24.25-.73.74-.97 1zm106.25-1.23zm-107.24 2.2c.25-.24.74-.74 1-.98-.26.22-.75.72-1 .96zm108.24-1.2c.24.25.73.74.98 1-.25-.26-.74-.75-1-1zm-109.22 2.2c.25-.26.74-.75.98-1-.24.25-.73.74-.98 1zm110.2-1.2c.3.3.93.95 1.24 1.27-.3-.32-.93-.96-1.24-1.28zM83.04 16.9c.24-.23.7-.7.94-.94l-.94.94zm112.38-.86c.23.26.67.78.9 1.04-.23-.26-.67-.78-.9-1.04zm-113.24 1.7c.22-.2.65-.63.86-.84-.2.2-.64.63-.86.85zm114.14-.66c.17.2.5.55.67.73-.2-.16-.5-.52-.7-.7zM81.4 18.5c.2-.18.6-.56.78-.75-.2.2-.6.57-.8.76zm115.6-.7c.18.2.56.6.75.78-.2-.2-.57-.58-.76-.77zM79.02 20.88c.15-.16.45-.47.6-.63-.15.16-.45.47-.6.63zm120.45-.55zm-120.8 1c.1-.1.26-.33.35-.45-.1.12-.26.35-.34.46zm121.37-.4c.08.13.26.37.34.48-.08-.1-.26-.33-.34-.45z",opacity:.47}),h=p.createElement("path",{fill:"#d8d9da",d:"M40.3 40.66c12.4-.04 24.8.04 37.2-.04 7.8-.1 15.62-.1 23.43.02 25.83.02 51.66.02 77.5 0 7.68-.1 15.38-.12 23.08-.02 13.27.05 26.5.06 39.76 0C253.9 53.07 266.42 65.67 279 78.2v165.98c-1.32 1.53-2.82 3.45-5.07 3.3-8.86.12-17.72 0-26.57.04-.42-18.84-.05-37.68-.18-56.52-.1-4.3.55-8.73-.8-12.9 1.9-4.46.78-9.4 1.04-14.1-.2-15.05.4-30.14-.3-45.2-.66-11.58-.07-23.2-.3-34.8-.15-1.27.04-2.84-1-3.78-2.52-2.72-6.54-1.6-9.8-1.8-64.34.05-128.67.04-193 0-3.28.2-7.3-.97-9.8 1.8-1.05.92-.9 2.46-1.05 3.7-.24 11.63.4 23.27-.3 34.88-.26 3.07-.36 6.15-.32 9.23.15 39.83-.14 79.66.14 119.5-8.9-.26-17.78.14-26.65-.05-2.24.14-3.72-1.78-5.06-3.27V78.3c13.35-12.6 26.9-25.07 40.3-37.64zM52.6 86.8c6.7-1.8 13.87 5.82 11.3 12.38-1.67 5.15-8.2 8.87-13.24 6.06-3.68-1.78-6.6-5.98-5.97-10.17.94-3.9 3.8-7.48 7.9-8.26zm19.16 9.23c-.2-6.04 6.67-11.2 12.37-8.95 4.2 1 6.32 5.1 7.3 8.96-.83 3.42-2 7.26-5.5 8.8-5.96 4.1-14.94-1.8-14.17-8.8zm-26.43 38.3c23.17-.1 46.34.07 69.5-.05.3 26.85.12 53.72.08 80.58-23.2 0-46.4.14-69.6-.05.13-26.8.05-53.63.04-80.45zm.8 96.92c31.16-.04 62.33-.07 93.5.02 0 5.4-.08 10.82.12 16.23-2.8-.12-5.63-.16-8.44-.1-3.52.13-7.07.16-10.6-.02-1.46-.03-2.9-.03-4.37 0-23.74.25-47.48-.03-71.23.13 1.76-5.24.62-10.83 1.03-16.24z"}),y=p.createElement("path",{fill:"#4189f8",d:"M33.22 80.23c2.5-2.78 6.52-1.6 9.8-1.8 64.33.03 128.66.04 193-.02 3.26.24 7.28-.9 9.8 1.83 1.04.94.85 2.5 1 3.78.23 11.6-.36 23.23.3 34.8-71.75.1-143.5.1-215.26 0 .7-11.6.07-23.24.3-34.87.16-1.24 0-2.78 1.06-3.7m19.38 6.6c-4.1.77-6.95 4.35-7.9 8.25-.65 4.2 2.28 8.4 5.96 10.17 5.03 2.8 11.57-.9 13.24-6.06 2.57-6.58-4.6-14.2-11.3-12.4m19.16 9.23c-.77 7 8.2 12.9 14.16 8.82 3.5-1.55 4.68-5.4 5.5-8.8-.97-3.86-3.1-7.97-7.3-8.97-5.7-2.25-12.55 2.9-12.36 8.95z"}),O=p.createElement("path",{fill:"#fff",d:"M31.86 118.8c71.75.1 143.5.1 215.27 0 .68 15.06.08 30.15.3 45.2-.27 4.7.86 9.64-1.05 14.1-7.74-7.1-13.8-15.87-22.07-22.42-5.66-9.03-12.87-17.7-23-21.85-.1 13.9-.05 27.82 0 41.73-2.63-1.17-4.42-3.44-6.27-5.52-4.96-5.4-10.38-10.37-15.13-15.95-6.08-7-13.2-13.03-18.9-20.37-13.28 7.26-23.35 20.6-26.15 35.56-1.67 11.27.75 23.2 7.03 32.76 4.66 6.94 11.12 12.7 18.68 16.3.85 9.7.35 19.4.04 29.13h-20.85c-.2-5.4-.13-10.83-.12-16.24-31.17-.1-62.34-.06-93.5-.02-.4 5.4.73 11-1.04 16.26-4.48 0-8.95-.02-13.4.02-.3-39.83 0-79.66-.16-119.5-.04-3.07.06-6.15.3-9.22m13.48 15.54c0 26.82.1 53.65-.05 80.47 23.2.2 46.42.08 69.63.07.05-26.86.22-53.73-.07-80.58-23.16.1-46.33-.04-69.5.05z"}),j=p.createElement("path",{fill:"#bdbdbd",d:"M161 133.74c5.7 7.34 12.82 13.36 18.9 20.35 4.74 5.56 10.16 10.53 15.12 15.93 1.85 2.08 3.64 4.35 6.27 5.52-.24 1.72.54 4.27-1.64 4.93-6.23 3.3-12.45 6.64-18.7 9.9-6.42-3.77-13.32-6.74-19.6-10.73-.5-2.5-.4-5.1-.42-7.64.12-12.75.24-25.52.06-38.3zm63.3 21.94c8.27 6.55 14.34 15.32 22.08 22.42 1.35 4.17.7 8.6.8 12.9.13 18.84-.24 37.68.18 56.52-15.26-.04-30.5-.02-45.77 0-.6-9.13-1.15-18.32-.2-27.43-.13-1.64 1.6-2.07 2.66-2.8 15-7.8 24.7-24.47 24.3-41.33.28-7.02-2.12-13.67-4.06-20.3z"});var I=function SvgSearchConsole(e){return p.createElement("svg",m({viewBox:"0 0 279 248"},e),b,v,h,y,O,j)},k=n(70),E=n(86),S=n(1101),_=n(7),M=n(6),D=n.n(M),N=n(16),w=n.n(N),A=n(12),T=n.n(A),C=n(46),R=n(63),P=n(412),x=n(207),L={selectors:{areSettingsEditDependenciesLoaded:Object(r.createRegistrySelector)((function(e){return function(){return e(k.b).hasFinishedResolution("getMatchedProperties")}}))}};function G(){return(G=w()(D.a.mark((function e(t){var n,r,i,a;return D.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.select,r=t.dispatch,!n(k.b).haveSettingsChanged()){e.next=8;break}return e.next=4,r(k.b).saveSettings();case 4:if(i=e.sent,!(a=i.error)){e.next=8;break}return e.abrupt("return",{error:a});case 8:return e.next=10,Object(C.invalidateCache)("modules",E.a);case 10:return e.abrupt("return",{});case 11:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var z=o.a.createModuleStore(E.a,{ownedSettingsSlugs:["propertyID"],storeName:k.b,settingSlugs:["propertyID","ownerID"],requiresSetup:!1,submitChanges:function(e){return G.apply(this,arguments)},validateCanSubmitChanges:function(e){var t=Object(R.e)(e)(k.b),n=t.getPropertyID,r=t.haveSettingsChanged;T()(Object(P.d)(n()),"a valid propertyID is required to submit changes"),T()(r(),x.b)}}),Z=n(1102),W=n(5),B=n.n(W),U=n(22),V=n.n(U),F=n(176),H=n(13),Y=n(9);function Q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Q(Object(n),!0).forEach((function(t){B()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var X={getServiceURL:Object(r.createRegistrySelector)((function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.path,i=n.query,a="https://search.google.com/search-console";if(r){var o="/".concat(r.replace(/^\//,""));a="".concat(a).concat(o)}i&&(a=Object(F.a)(a,i));var c=e(_.a).getAccountChooserURL(a);if(void 0!==c)return c}})),getServiceReportURL:Object(r.createRegistrySelector)((function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e(k.b).getPropertyID(),i=X.isDomainProperty(t),a=e(H.c).getReferenceSiteURL(),o=n.page,c=void 0===o?i?"*".concat(Object(Y.K)(a)):void 0:o,s=V()(n,["page"]),l="/performance/search-analytics",u=J(J({page:c},s),{},{resource_id:r});return X.getServiceURL(t,{path:l,query:u})}})),getServiceEntityAccessURL:Object(r.createRegistrySelector)((function(e){return function(t){var n={resource_id:e(k.b).getPropertyID()};return X.getServiceURL(t,{query:n})}})),isDomainProperty:Object(r.createRegistrySelector)((function(e){return function(){var t=e(k.b).getPropertyID();return t&&t.startsWith("sc-domain:")}}))},q={selectors:X},K=n(50);function $(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$(Object(n),!0).forEach((function(t){B()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var te,ne=Object(K.a)({baseName:"getMatchedProperties",controlCallback:function(){return Object(C.get)("modules",E.a,"matched-sites",{},{useCache:!0})},reducerCallback:function(e,t){return ee(ee({},e),{},{properties:t})}}),re={properties:void 0},ie={getMatchedProperties:D.a.mark((function e(){var t;return D.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(t=e.sent,void 0!==t.select(k.b).getMatchedProperties()){e.next=7;break}return e.next=7,ne.actions.fetchGetMatchedProperties();case 7:case"end":return e.stop()}}),e)}))},ae=Object(r.combineStores)(ne,{initialState:re,actions:{},controls:{},reducer:function(e,t){t.type;return e},resolvers:ie,selectors:{getMatchedProperties:function(e){return e.properties}}}),oe=(ae.initialState,ae.actions,ae.controls,ae.reducer,ae.resolvers,ae.selectors,ae),ce=Object(r.combineStores)(z,Z.a,q,L,oe);ce.initialState,ce.actions,ce.controls,ce.reducer,ce.resolvers,ce.selectors;i.a.registerStore(k.b,ce),o.a.registerModule(E.a,{storeName:k.b,SettingsEditComponent:u.a,SettingsViewComponent:l.a,Icon:I}),(te=s.a).registerWidget("searchConsolePopularKeywords",{Component:g.a,width:[te.WIDGET_WIDTHS.HALF,te.WIDGET_WIDTHS.FULL],priority:1,wrapWidget:!1,modules:[E.a]},[f.AREA_MAIN_DASHBOARD_CONTENT_PRIMARY,f.AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY]),te.registerWidget("searchFunnelGA4",{Component:d.a,width:[te.WIDGET_WIDTHS.FULL],priority:3,wrapWidget:!1,modules:[E.a]},[f.AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY,f.AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY]),te.registerWidget(_.G,{Component:S.a,width:te.WIDGET_WIDTHS.QUARTER,priority:2,wrapWidget:!1,modules:[E.a],isActive:function(e){return e(_.a).isKeyMetricActive(_.G)}},[f.AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY])},131:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(20),i=n.n(r),a=n(5),o=n.n(a),c=n(22),s=n.n(c),l=n(0),u=n.n(l),g=n(10),d=n.n(g);function Cell(t){var n,r=t.className,a=t.alignTop,c=t.alignMiddle,l=t.alignBottom,u=t.alignRight,g=t.alignLeft,f=t.smAlignRight,p=t.mdAlignRight,m=t.lgAlignRight,b=t.smSize,v=t.smStart,h=t.smOrder,y=t.mdSize,O=t.mdStart,j=t.mdOrder,I=t.lgSize,k=t.lgStart,E=t.lgOrder,S=t.size,_=t.children,M=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},M,{className:d()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":l,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":g,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":p,"mdc-layout-grid__cell--align-right-desktop":m},o()(n,"mdc-layout-grid__cell--span-".concat(S),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(I,"-desktop"),12>=I&&I>0),o()(n,"mdc-layout-grid__cell--start-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--order-".concat(E,"-desktop"),12>=E&&E>0),o()(n,"mdc-layout-grid__cell--span-".concat(y,"-tablet"),8>=y&&y>0),o()(n,"mdc-layout-grid__cell--start-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--order-".concat(j,"-tablet"),8>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),_)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(3))},132:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),s=n.n(c),l=n(10),u=n.n(l),g=n(1),d=Object(g.forwardRef)((function(t,n){var r=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",i()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),a)}));d.displayName="Row",d.propTypes={className:s.a.string,children:s.a.node},d.defaultProps={className:""},t.a=d}).call(this,n(3))},133:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),s=n.n(c),l=n(10),u=n.n(l),g=n(1),d=Object(g.forwardRef)((function(t,n){var r=t.alignLeft,a=t.fill,c=t.className,s=t.children,l=t.collapsed,g=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":l,"mdc-layout-grid--fill":a})},g,{ref:n}),s)}));d.displayName="Grid",d.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},d.defaultProps={className:""},t.a=d}).call(this,n(3))},134:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},135:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},136:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},137:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InfoTooltip}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a),c=n(11),s=n(339);function InfoTooltip(t){var n=t.onOpen,r=t.title,a=t.tooltipClassName;return r?e.createElement(c.Tooltip,{className:"googlesitekit-info-tooltip",tooltipClassName:i()("googlesitekit-info-tooltip__content",a),title:r,placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,onOpen:n,interactive:!0},e.createElement("span",null,e.createElement(s.a,{width:"16",height:"16"}))):null}InfoTooltip.propTypes={onOpen:o.a.func,title:o.a.oneOfType([o.a.string,o.a.element]),tooltipClassName:o.a.string}}).call(this,n(3))},139:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Icon}));var r,i=n(5),a=n.n(i),o=n(0),c=n.n(o),s=n(111),l=n(71),u=n(97),g=n(38),d=(r={},a()(r,g.a.NEW,u.a),a()(r,g.a.SUCCESS,s.a),a()(r,g.a.INFO,l.a),a()(r,g.a.WARNING,l.a),a()(r,g.a.ERROR,l.a),r);function Icon(t){var n=t.type,r=d[n]||l.a;return e.createElement(r,{width:24,height:24})}Icon.propTypes={type:c.a.oneOf(Object.values(g.a))}}).call(this,n(3))},140:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Title}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Title(t){var n=t.className,r=t.children;return e.createElement("p",{className:i()("googlesitekit-notice__title",n)},r)}Title.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Description(t){var n=t.className,r=t.children;return e.createElement("p",{className:i()("googlesitekit-notice__description",n)},r)}Description.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},143:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(42),s=n(2),l=n(21),u=n(39);function SourceLink(t){var n=t.name,r=t.href,i=t.className,a=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",i)},Object(c.a)(Object(s.sprintf)(/* translators: %s: source link */
Object(s.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(l.a,{key:"link",href:r,external:a})}))}SourceLink.propTypes={name:i.a.string,href:i.a.string,className:i.a.string,external:i.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(3))},144:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportErrorActions}));var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(1),s=n(42),l=n(2),u=n(4),g=n(11),d=n(13),f=n(19),p=n(34),m=n(39),b=n(21);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportErrorActions(t){var n=t.moduleSlug,r=t.error,i=t.GetHelpLink,a=t.hideGetHelpLink,o=t.buttonVariant,v=t.onRetry,y=t.onRequestAccess,O=t.getHelpClassName,j=t.RequestAccessButton,I=t.RetryButton,k=Object(m.a)(),E=Object(u.useSelect)((function(e){return e(f.a).getModuleStoreName(n)})),S=Object(u.useSelect)((function(e){var t;return"function"==typeof(null===(t=e(E))||void 0===t?void 0:t.getServiceEntityAccessURL)?e(E).getServiceEntityAccessURL():null})),_=Array.isArray(r)?r:[r],M=Object(u.useSelect)((function(e){return _.map((function(t){var n,r=null===(n=e(E))||void 0===n?void 0:n.getSelectorDataForError(t);return h(h({},t),{},{selectorData:r})}))})),D=null==M?void 0:M.filter((function(e){return Object(p.d)(e,e.selectorData)&&"getReport"===e.selectorData.name})),N=!!D.length,w=Object(u.useSelect)((function(e){var t=h({},N?D[0]:_[0]);return Object(p.e)(t)&&(t.code="".concat(n,"_insufficient_permissions")),e(d.c).getErrorTroubleshootingLinkURL(t)})),A=Object(u.useDispatch)(),T=_.some((function(e){return Object(p.e)(e)})),C=Object(c.useCallback)((function(){D.forEach((function(e){var t=e.selectorData;A(t.storeName).invalidateResolution(t.name,t.args)})),null==v||v()}),[A,D,v]),R=S&&T&&!k;return e.createElement("div",{className:"googlesitekit-report-error-actions"},R&&("function"==typeof j?e.createElement(j,{requestAccessURL:S}):e.createElement(g.Button,{onClick:y,href:S,target:"_blank",danger:"danger"===o,tertiary:"tertiary"===o},Object(l.__)("Request access","google-site-kit"))),N&&e.createElement(c.Fragment,null,"function"==typeof I?e.createElement(I,{handleRetry:C}):e.createElement(g.Button,{onClick:C,danger:"danger"===o,tertiary:"tertiary"===o},Object(l.__)("Retry","google-site-kit")),!a&&e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(s.a)(Object(l.__)("Retry didn’t work? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(b.a,{href:w,external:!0,hideExternalIndicator:!0},Object(l.__)("Get help","google-site-kit"))}))),!N&&!a&&e.createElement("div",{className:O},"function"==typeof i?e.createElement(i,{linkURL:w}):e.createElement(b.a,{href:w,external:!0,hideExternalIndicator:!0},Object(l.__)("Get help","google-site-kit"))))}ReportErrorActions.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired,GetHelpLink:o.a.elementType,hideGetHelpLink:o.a.bool,buttonVariant:o.a.string,onRetry:o.a.func,onRequestAccess:o.a.func,getHelpClassName:o.a.string,RequestAccessButton:o.a.elementType,RetryButton:o.a.elementType}}).call(this,n(3))},145:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LearnMoreLink}));var r=n(0),i=n.n(r),a=n(2),o=n(21);function LearnMoreLink(t){var n=t.href,r=t.className,i=t.label,c=void 0===i?Object(a.__)("Learn more","google-site-kit"):i,s=t.onClick,l=void 0===s?function(){}:s;return n?e.createElement(o.a,{href:n,className:r,onClick:l,external:!0},c):null}LearnMoreLink.propTypes={href:i.a.string.isRequired,className:i.a.string,label:i.a.string,onClick:i.a.func}}).call(this,n(3))},146:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTAButton}));var r=n(0),i=n.n(r),a=n(11);function CTAButton(t){var n=t.label,r=t.disabled,i=t.inProgress,o=t.onClick,c=t.href;return n&&(o||c)?e.createElement(a.SpinnerButton,{className:"googlesitekit-banner__cta",disabled:r||i,isSaving:i,onClick:o,href:c},n):null}CTAButton.propTypes={label:i.a.string.isRequired,disabled:i.a.bool,inProgress:i.a.bool,onClick:i.a.func,href:i.a.string}}).call(this,n(3))},147:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DismissButton}));var r=n(0),i=n.n(r),a=n(2),o=n(11);function DismissButton(t){var n=t.className,r=t.label,i=void 0===r?Object(a.__)("Maybe later","google-site-kit"):r,c=t.tertiary,s=void 0===c||c,l=t.onClick,u=t.disabled;return l?e.createElement(o.Button,{className:n,onClick:l,disabled:u,tertiary:s},i):null}DismissButton.propTypes={className:i.a.string,label:i.a.string,tertiary:i.a.bool,onClick:i.a.func,disabled:i.a.bool}}).call(this,n(3))},153:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StoreErrorNotices}));var r=n(0),i=n.n(r),a=n(4),o=n(59),c=n(19),s=n(34),l=n(177);function StoreErrorNotices(t){var n=t.hasButton,r=void 0!==n&&n,i=t.moduleSlug,u=t.storeName,g=Object(a.useSelect)((function(e){return e(u).getErrors()})),d=Object(a.useSelect)((function(e){return e(c.a).getModule(i)})),f=[];return g.filter((function(e){return!(!(null==e?void 0:e.message)||f.includes(e.message))&&(f.push(e.message),!0)})).map((function(t,n){var i=t.message;return Object(s.e)(t)&&(i=Object(l.a)(i,d)),e.createElement(o.a,{key:n,error:t,hasButton:r,storeName:u,message:i})}))}StoreErrorNotices.propTypes={hasButton:i.a.bool,storeName:i.a.string.isRequired,moduleSlug:i.a.string}}).call(this,n(3))},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var r=n(5),i=n.n(r),a=n(2),o=n(7),c=n(13),s=n(8);function l(e,t,n){return e(s.r).hasConversionReportingEvents(this.requiredConversionEventName)||e(o.a).isKeyMetricActive(n)}var u,g=n(25);function d(e,t){return!t||!(!t||!e(s.r).getAdSenseLinked())}function f(e,t){return!t||e(s.r).hasCustomDimensions(this.requiredCustomDimensions)}var p=(u={},i()(u,o.f,{title:Object(a.__)("Top earning pages","google-site-kit"),description:Object(a.__)("Pages that generated the most AdSense revenue","google-site-kit"),infoTooltip:Object(a.__)("Pages that generated the most AdSense revenue","google-site-kit"),displayInSelectionPanel:d,displayInList:d,metadata:{group:g.b.SLUG}}),i()(u,o.y,{title:Object(a.__)("Top recent trending pages","google-site-kit"),description:Object(a.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),infoTooltip:Object(a.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_date"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:g.b.SLUG}}),i()(u,o.l,{title:Object(a.__)("Most popular authors by pageviews","google-site-kit"),description:Object(a.__)("Authors whose posts got the most visits","google-site-kit"),infoTooltip:Object(a.__)("Authors whose posts got the most visits","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_author"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:g.b.SLUG}}),i()(u,o.p,{title:Object(a.__)("Top categories by pageviews","google-site-kit"),description:Object(a.__)("Categories that your site visitors viewed the most","google-site-kit"),infoTooltip:Object(a.__)("Categories that your site visitors viewed the most","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_categories"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:g.b.SLUG}}),i()(u,o.m,{title:Object(a.__)("Most popular content by pageviews","google-site-kit"),description:Object(a.__)("Pages that brought in the most visitors","google-site-kit"),infoTooltip:Object(a.__)("Pages your visitors read the most","google-site-kit"),metadata:{group:g.b.SLUG}}),i()(u,o.n,{title:Object(a.__)("Most popular products by pageviews","google-site-kit"),description:Object(a.__)("Products that brought in the most visitors","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_type"],displayInSelectionPanel:function(e){return e(o.a).isKeyMetricActive(o.n)||e(c.c).getProductPostType()},displayInWidgetArea:f,metadata:{group:g.f.SLUG}}),i()(u,o.k,{title:Object(a.__)("Pages per visit","google-site-kit"),description:Object(a.__)("Number of pages visitors viewed per session on average","google-site-kit"),infoTooltip:Object(a.__)("Number of pages visitors viewed per session on average","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,o.F,{title:Object(a.__)("Visit length","google-site-kit"),description:Object(a.__)("Average duration of engaged visits","google-site-kit"),infoTooltip:Object(a.__)("Average duration of engaged visits","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,o.E,{title:Object(a.__)("Visits per visitor","google-site-kit"),description:Object(a.__)("Average number of sessions per site visitor","google-site-kit"),infoTooltip:Object(a.__)("Average number of sessions per site visitor","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,o.i,{title:Object(a.__)("Most engaging pages","google-site-kit"),description:Object(a.__)("Pages with the highest engagement rate","google-site-kit"),infoTooltip:Object(a.__)("Pages with the highest engagement rate","google-site-kit"),metadata:{group:g.b.SLUG}}),i()(u,o.h,{title:Object(a.__)("Least engaging pages","google-site-kit"),description:Object(a.__)("Pages with the highest percentage of visitors that left without engagement with your site","google-site-kit"),infoTooltip:Object(a.__)("Percentage of visitors that left without engagement with your site","google-site-kit"),metadata:{group:g.b.SLUG}}),i()(u,o.z,{title:Object(a.__)("Top pages by returning visitors","google-site-kit"),description:Object(a.__)("Pages that attracted the most returning visitors","google-site-kit"),infoTooltip:Object(a.__)("Pages that attracted the most returning visitors","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,o.j,{title:Object(a.__)("New visitors","google-site-kit"),description:Object(a.__)("How many new visitors you got and how the overall audience changed","google-site-kit"),infoTooltip:Object(a.__)("Portion of visitors who visited your site for the first time in this timeframe","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,o.o,{title:Object(a.__)("Returning visitors","google-site-kit"),description:Object(a.__)("Portion of people who visited your site more than once","google-site-kit"),infoTooltip:Object(a.__)("Portion of your site’s visitors that returned at least once in this timeframe","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,o.A,{title:Object(a.__)("Top traffic source","google-site-kit"),description:Object(a.__)("Channel which brought in the most visitors to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most visitors to your site","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,o.B,{title:Object(a.__)("Top traffic source driving add to cart","google-site-kit"),description:Object(a.__)("Channel which brought in the most add to cart events to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most add to cart events to your site","google-site-kit"),requiredConversionEventName:[s.l.ADD_TO_CART],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.f.SLUG}}),i()(u,o.C,{title:Object(a.__)("Top traffic source driving leads","google-site-kit"),description:Object(a.__)("Channel which brought in the most leads to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most leads to your site","google-site-kit"),requiredConversionEventName:[s.l.SUBMIT_LEAD_FORM,s.l.CONTACT,s.l.GENERATE_LEAD],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.e.SLUG}}),i()(u,o.D,{title:Object(a.__)("Top traffic source driving purchases","google-site-kit"),description:Object(a.__)("Channel which brought in the most purchases to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most purchases to your site","google-site-kit"),requiredConversionEventName:[s.l.PURCHASE],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.f.SLUG}}),i()(u,o.g,{title:Object(a.__)("Most engaged traffic source","google-site-kit"),description:Object(a.__)("Visitors coming via this channel spent the most time on your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most visitors who had a meaningful engagement with your site","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,o.u,{title:Object(a.__)("Top converting traffic source","google-site-kit"),description:Object(a.__)("Channel which brought in the most visits that resulted in key events","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in visitors who generated the most key events","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,o.q,{title:Object(a.__)("Top cities driving traffic","google-site-kit"),description:Object(a.__)("Which cities you get the most visitors from","google-site-kit"),infoTooltip:Object(a.__)("The cities where most of your visitors came from","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,o.s,{title:Object(a.__)("Top cities driving leads","google-site-kit"),description:Object(a.__)("Cities driving the most contact form submissions","google-site-kit"),infoTooltip:Object(a.__)("Cities driving the most contact form submissions","google-site-kit"),requiredConversionEventName:[s.l.SUBMIT_LEAD_FORM,s.l.CONTACT,s.l.GENERATE_LEAD],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.e.SLUG}}),i()(u,o.r,{title:Object(a.__)("Top cities driving add to cart","google-site-kit"),description:Object(a.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),infoTooltip:Object(a.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),requiredConversionEventName:[s.l.ADD_TO_CART],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.f.SLUG}}),i()(u,o.t,{title:Object(a.__)("Top cities driving purchases","google-site-kit"),description:Object(a.__)("Cities driving the most purchases","google-site-kit"),infoTooltip:Object(a.__)("Cities driving the most purchases","google-site-kit"),requiredConversionEventName:[s.l.PURCHASE],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.f.SLUG}}),i()(u,o.w,{title:Object(a.__)("Top device driving purchases","google-site-kit"),description:Object(a.__)("Top device driving the most purchases","google-site-kit"),infoTooltip:Object(a.__)("Top device driving the most purchases","google-site-kit"),requiredConversionEventName:[s.l.PURCHASE],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.f.SLUG}}),i()(u,o.v,{title:Object(a.__)("Top countries driving traffic","google-site-kit"),description:Object(a.__)("Which countries you get the most visitors from","google-site-kit"),infoTooltip:Object(a.__)("The countries where most of your visitors came from","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,o.G,{title:Object(a.__)("Top performing keywords","google-site-kit"),description:Object(a.__)("What people searched for before they came to your site","google-site-kit"),infoTooltip:Object(a.__)("The top search queries for your site by highest clickthrough rate","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,o.x,{title:Object(a.__)("Top pages driving leads","google-site-kit"),description:Object(a.__)("Pages on which forms are most frequently submitted","google-site-kit"),requiredConversionEventName:[s.l.SUBMIT_LEAD_FORM,s.l.CONTACT,s.l.GENERATE_LEAD],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.e.SLUG}}),u)},164:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},17:function(e,t,n){"use strict";var r=n(261);n.d(t,"i",(function(){return r.a}));var i=n(333);n.d(t,"f",(function(){return i.a}));var a=n(334);n.d(t,"h",(function(){return a.a}));var o=n(335);n.d(t,"j",(function(){return o.a}));var c=n(332);n.d(t,"g",(function(){return c.a}));var s=n(96),l=n.n(s);n.d(t,"b",(function(){return l.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var u=n(112);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},173:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RecoverableModules}));var r=n(0),i=n.n(r),a=n(2),o=n(4),c=n(19),s=n(102);function RecoverableModules(t){var n=t.moduleSlugs,r=Object(o.useSelect)((function(e){var t=e(c.a).getModules();if(void 0!==t)return n.map((function(e){return t[e].name}))}));if(void 0===r)return null;var i=1===r.length?Object(a.sprintf)(/* translators: %s: Module name */
Object(a.__)("%s data was previously shared by an admin who no longer has access. Please contact another admin to restore it.","google-site-kit"),r[0]):Object(a.sprintf)(/* translators: %s: List of module names */
Object(a.__)("The data for the following modules was previously shared by an admin who no longer has access: %s. Please contact another admin to restore it.","google-site-kit"),r.join(Object(a._x)(", ","Recoverable modules","google-site-kit")));return e.createElement(s.a,{title:Object(a.__)("Data Unavailable","google-site-kit"),description:i})}RecoverableModules.propTypes={moduleSlugs:i.a.arrayOf(i.a.string).isRequired}}).call(this,n(3))},177:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(86),a=n(29);function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},o=n.slug,c=void 0===o?"":o,s=n.name,l=void 0===s?"":s,u=n.owner,g=void 0===u?{}:u;if(!c||!l)return e;var d="",f="";return a.g===c?e.match(/account/i)?d=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?d=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(d=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):i.a===c&&(d=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),d||(d=Object(r.sprintf)(/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),l)),g&&g.login&&(f=Object(r.sprintf)(/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),g.login)),f||(f=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(d," ").concat(f)}},18:function(e,t,n){"use strict";var r=n(1),i=n(65);t.a=function(){return Object(r.useContext)(i.b)}},181:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var r=n(0),i=n.n(r),a=n(2),o=n(21),c=n(202);function GenericErrorHandlerActions(t){var n=t.message,r=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:r}),e.createElement(o.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(a.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:i.a.string,componentStack:i.a.string}}).call(this,n(3))},182:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(24),i=function(e){return r.f.includes(e)}},184:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(10),o=n.n(a),c=n(0),s=n.n(c),l=n(1),u=n(23),g=n(228),d=n(229),f=n(230),p=n(145),m=n(146),b=n(147),v=n(231),h=n(35),y=Object(l.forwardRef)((function(t,n){var r=t.className,a=t.title,c=t.description,s=t.additionalDescription,l=t.errorText,y=t.helpText,O=t.learnMoreLink,j=t.dismissButton,I=t.ctaButton,k=t.svg,E=t.footer,S=Object(u.e)(),_=S===u.b||S===u.c,M=null;_&&(null==k?void 0:k.mobile)?M=k.mobile:!_&&(null==k?void 0:k.desktop)&&(M=k.desktop);var D=(null==k?void 0:k.verticalPosition)?k.verticalPosition:"center";return e.createElement("div",{ref:n,className:o()("googlesitekit-banner",r)},e.createElement("div",{className:"googlesitekit-banner__content"},e.createElement(g.a,null,a),e.createElement(d.a,null,c," ",(null==O?void 0:O.href)&&e.createElement(p.a,O),s&&e.createElement("div",{className:"googlesitekit-banner__additional-description"},s)),y&&e.createElement(f.a,null,y),l&&e.createElement(h.a,{type:"error",description:l}),e.createElement("div",{className:"googlesitekit-notice__action"},I&&e.createElement(m.a,I),(null==j?void 0:j.onClick)&&e.createElement(b.a,j))),M&&e.createElement("div",{className:o()("googlesitekit-banner__svg-wrapper",i()({},"googlesitekit-banner__svg-wrapper--".concat(D),D)),style:{backgroundImage:"url(".concat(M,")")}}),E&&e.createElement(v.a,null,E))}));y.propTypes={title:s.a.string,description:s.a.oneOfType([s.a.string,s.a.node]),additionalDescription:s.a.oneOfType([s.a.string,s.a.node]),errorText:s.a.string,helpText:s.a.string,learnMoreLink:s.a.shape(p.a.propTypes),dismissButton:s.a.shape(b.a.propTypes),ctaButton:s.a.shape(m.a.propTypes),svg:s.a.shape({desktop:s.a.elementType,mobile:s.a.elementType,verticalPosition:s.a.oneOf(["top","center","bottom"])}),footer:s.a.node},t.a=y}).call(this,n(3))},186:function(e,t,n){"use strict";n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"d",(function(){return s}));var r=n(36),i=n.n(r);function a(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return"string"==typeof e?n(e):!("object"!==i()(e)||!t(e))||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e?n(e):"object"===i()(e)&&t(e)}))}function o(e){var t=e.startDate,n=e.endDate,r=t&&t.match(/^\d{4}-\d{2}-\d{2}$/),i=n&&n.match(/^\d{4}-\d{2}-\d{2}$/);return r&&i}function c(e){var t=function(e){var t=e.hasOwnProperty("fieldName")&&!!e.fieldName,n=e.hasOwnProperty("sortOrder")&&/(ASCENDING|DESCENDING)/i.test(e.sortOrder.toString());return t&&n};return Array.isArray(e)?e.every((function(e){return"object"===i()(e)&&t(e)})):"object"===i()(e)&&t(e)}function s(e){return"string"==typeof e||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e}))}},187:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(1),s=n(4),l=n(13),u=n(7),g=n(19),d=n(33),f=n(37),p=n(40),m=n(18);function b(e){var t=Object(m.a)(),n=Object(s.useSelect)((function(t){return t(g.a).getModule(e)})),r=Object(s.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),a=Object(s.useDispatch)(g.a).activateModule,b=Object(s.useDispatch)(d.a).navigateTo,v=Object(s.useDispatch)(l.c).setInternalServerError,h=Object(c.useCallback)(o()(i.a.mark((function n(){var r,o,c;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,a(e);case 2:if(r=n.sent,o=r.error,c=r.response,o){n.next=13;break}return n.next=8,Object(p.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(f.f)("module_setup",e,{ttl:300});case 10:b(c.moduleReauthURL),n.next=14;break;case 13:v({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[a,e,b,v,t]);return(null==n?void 0:n.name)&&r?h:null}},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="core/modules",i="insufficient_module_dependencies"},192:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(0),i=n.n(r),a=" ";function DisplaySetting(e){return e.value||a}DisplaySetting.propTypes={value:i.a.oneOfType([i.a.string,i.a.bool,i.a.number])},t.b=DisplaySetting},193:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeBadge}));var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(9);function ChangeBadge(t){var n=t.previousValue,r=t.currentValue,i=t.isAbsolute?r-n:Object(c.g)(n,r),a=i<0,s=0===i;return null===i?null:e.createElement("div",{className:o()("googlesitekit-change-badge",{"googlesitekit-change-badge--negative":a,"googlesitekit-change-badge--zero":s})},Object(c.B)(i,{style:"percent",signDisplay:"exceptZero",maximumFractionDigits:1}))}ChangeBadge.propTypes={isAbsolute:i.a.bool,previousValue:i.a.number.isRequired,currentValue:i.a.number.isRequired}}).call(this,n(3))},198:function(e,t){e.exports=googlesitekit.modules},2:function(e,t){e.exports=googlesitekit.i18n},202:function(e,t,n){"use strict";(function(e){var r=n(14),i=n.n(r),a=n(204),o=n.n(a),c=n(0),s=n.n(c),l=n(1),u=n(2),g=n(272),d=n(444),f=n(445),p=n(11);function ReportErrorButton(t){var n=t.message,r=t.componentStack,a=Object(l.useState)(!1),c=i()(a,2),s=c[0],m=c[1];return e.createElement(p.Button,{"aria-label":s?Object(u.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){o()("```\n".concat(n,"\n").concat(r,"\n```")),m(!0)},trailingIcon:e.createElement(g.a,{className:"mdc-button__icon",icon:s?d.a:f.a})},s?Object(u.__)("Copied to clipboard","google-site-kit"):Object(u.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:s.a.string,componentStack:s.a.string},t.a=ReportErrorButton}).call(this,n(3))},203:function(e,t,n){"use strict";var r=n(247);n.d(t,"b",(function(){return r.a}));var i=n(260);n.d(t,"a",(function(){return i.a}))},207:function(e,t,n){"use strict";n.d(t,"a",(function(){return I})),n.d(t,"b",(function(){return k})),n.d(t,"c",(function(){return E})),n.d(t,"g",(function(){return S})),n.d(t,"f",(function(){return _})),n.d(t,"d",(function(){return M})),n.d(t,"e",(function(){return D}));var r=n(16),i=n.n(r),a=n(6),o=n.n(a),c=n(5),s=n.n(c),l=n(12),u=n.n(l),g=n(15),d=n(46),f=n(4),p=n(63),m=n(93),b=n(50),v=n(68);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var O=v.a.clearError,j=v.a.receiveError,I="cannot submit changes while submitting changes",k="cannot submit changes if settings have not changed",E=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=r.ownedSettingsSlugs,a=void 0===i?void 0:i,c=r.storeName,l=void 0===c?void 0:c,v=r.settingSlugs,h=void 0===v?[]:v,I=r.initialSettings,k=void 0===I?void 0:I,E=r.validateHaveSettingsChanged,S=void 0===E?D():E;u()(e,"type is required."),u()(t,"identifier is required."),u()(n,"datapoint is required.");var _=l||"".concat(e,"/").concat(t),M={ownedSettingsSlugs:a,settings:k,savedSettings:void 0},N=Object(b.a)({baseName:"getSettings",controlCallback:function(){return Object(d.get)(e,t,n,{},{useCache:!1})},reducerCallback:function(e,t){return y(y({},e),{},{savedSettings:y({},t),settings:y(y({},t),e.settings||{})})}}),w=Object(b.a)({baseName:"saveSettings",controlCallback:function(r){var i=r.values;return Object(d.set)(e,t,n,i)},reducerCallback:function(e,t){return y(y({},e),{},{savedSettings:y({},t),settings:y({},t)})},argsToParams:function(e){return{values:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.values;u()(Object(g.isPlainObject)(t),"values is required.")}}),A={},T={setSettings:function(e){return u()(Object(g.isPlainObject)(e),"values is required."),{payload:{values:e},type:"SET_SETTINGS"}},rollbackSettings:function(){return{payload:{},type:"ROLLBACK_SETTINGS"}},rollbackSetting:function(e){return u()(e,"setting is required."),{payload:{setting:e},type:"ROLLBACK_SETTING"}},saveSettings:o.a.mark((function e(){var t,n,r,i,a;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,O("saveSettings",[]);case 5:return n=t.select(_).getSettings(),e.next=8,w.actions.fetchSaveSettings(n);case 8:if(r=e.sent,i=r.response,!(a=r.error)){e.next=14;break}return e.next=14,j(a,"saveSettings",[]);case 14:return e.abrupt("return",{response:i,error:a});case 15:case"end":return e.stop()}}),e)}))},C={},R=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:M,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"SET_SETTINGS":var i=r.values;return y(y({},e),{},{settings:y(y({},e.settings||{}),i)});case"ROLLBACK_SETTINGS":return y(y({},e),{},{settings:e.savedSettings});case"ROLLBACK_SETTING":var a=r.setting;return e.savedSettings[a]?y(y({},e),{},{settings:y(y({},e.settings||{}),{},s()({},a,e.savedSettings[a]))}):y({},e);default:return void 0!==A[n]?A[n](e,{type:n,payload:r}):e}},P={getSettings:o.a.mark((function e(){var t;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:if(t=e.sent,t.select(_).getSettings()){e.next=7;break}return e.next=7,N.actions.fetchGetSettings();case 7:case"end":return e.stop()}}),e)}))},x=Object(p.g)(S),L=x.safeSelector,G=x.dangerousSelector,z={haveSettingsChanged:L,__dangerousHaveSettingsChanged:G,getSettings:function(e){return e.settings},hasSettingChanged:function(e,t){u()(t,"setting is required.");var n=e.settings,r=e.savedSettings;return!(!n||!r)&&!Object(g.isEqual)(n[t],r[t])},isDoingSaveSettings:function(e){return Object.values(e.isFetchingSaveSettings).some(Boolean)},getOwnedSettingsSlugs:function(e){return e.ownedSettingsSlugs},haveOwnedSettingsChanged:Object(f.createRegistrySelector)((function(e){return function(){var t=e(_).getOwnedSettingsSlugs();return e(_).haveSettingsChanged(t)}}))};h.forEach((function(e){var t=Object(m.b)(e),n=Object(m.a)(e);T["set".concat(t)]=function(e){return u()(void 0!==e,"value is required for calls to set".concat(t,"().")),{payload:{value:e},type:"SET_".concat(n)}},A["SET_".concat(n)]=function(t,n){var r=n.payload.value;return y(y({},t),{},{settings:y(y({},t.settings||{}),{},s()({},e,r))})},z["get".concat(t)]=Object(f.createRegistrySelector)((function(t){return function(){return(t(_).getSettings()||{})[e]}}))}));var Z=Object(f.combineStores)(f.commonStore,N,w,{initialState:M,actions:T,controls:C,reducer:R,resolvers:P,selectors:z});return y(y({},Z),{},{STORE_NAME:_})};function S(e,t){return function(){var n=i()(o.a.mark((function n(r){var i,a,c,s;return o.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(i=r.select,a=r.dispatch,!i(t).haveSettingsChanged()){n.next=8;break}return n.next=4,a(t).saveSettings();case 4:if(c=n.sent,!(s=c.error)){n.next=8;break}return n.abrupt("return",{error:s});case 8:return n.next=10,Object(d.invalidateCache)("modules",e);case 10:return n.abrupt("return",{});case 11:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function _(e){return function(t){var n=t.select,r=t.dispatch;return n(e).haveSettingsChanged()?r(e).rollbackSettings():{}}}function M(e){return function(t){var n=Object(p.e)(t)(e),r=n.haveSettingsChanged,i=n.isDoingSubmitChanges;u()(!i(),I),u()(r(),k)}}function D(){return function(e,t,n){var r=t.settings,i=t.savedSettings;n&&u()(!Object(g.isEqual)(Object(g.pick)(r,n),Object(g.pick)(i,n)),k),u()(!Object(g.isEqual)(r,i),k)}}},208:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileWrapper}));var r=n(10),i=n.n(r),a=n(15),o=n(0),c=n.n(o),s=n(1),l=n(2),u=n(154),g=n(475),d=n(476),f=n(317),p=n(407),m=n(144),b=n(34),v=n(9),h=n(18);function MetricTileWrapper(t){var n,r,o,c=t.className,y=t.children,O=t.error,j=t.loading,I=t.moduleSlug,k=t.Widget,E=t.widgetSlug,S=t.title,_=void 0===S?null===(n=u.a[E])||void 0===n?void 0:n.title:S,M=t.infoTooltip,D=void 0===M?(null===(r=u.a[E])||void 0===r?void 0:r.infoTooltip)||(null===(o=u.a[E])||void 0===o?void 0:o.description):M,N=Object(h.a)(),w=!!O&&Object(a.castArray)(O).some(b.e),A=Object(s.useCallback)((function(){Object(v.I)("".concat(N,"_kmw"),"data_loading_error_retry")}),[N]);return Object(s.useEffect)((function(){O&&Object(v.I)("".concat(N,"_kmw"),"data_loading_error")}),[N,O]),O?e.createElement(f.a,{title:w?Object(l.__)("Insufficient permissions","google-site-kit"):Object(l.__)("Data loading failed","google-site-kit"),headerText:_,infoTooltip:D},e.createElement(m.a,{moduleSlug:I,error:O,onRetry:A,GetHelpLink:w?g.a:void 0,getHelpClassName:"googlesitekit-error-retry-text"})):e.createElement(k,{noPadding:!0},e.createElement("div",{className:i()("googlesitekit-km-widget-tile",c)},e.createElement(p.a,{title:_,infoTooltip:D,loading:j}),e.createElement("div",{className:"googlesitekit-km-widget-tile__body"},j&&e.createElement(d.a,null),!j&&y)))}MetricTileWrapper.propTypes={Widget:c.a.elementType.isRequired,loading:c.a.bool,title:c.a.string,infoTooltip:c.a.oneOfType([c.a.string,c.a.element]),moduleSlug:c.a.string.isRequired}}).call(this,n(3))},21:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(10),s=n.n(c),l=n(0),u=n.n(l),g=n(158),d=n(1),f=n(2),p=n(134),m=n(135),b=n(136),v=n(73),h=n(78),y=Object(d.forwardRef)((function(t,n){var r,a=t["aria-label"],c=t.secondary,l=void 0!==c&&c,u=t.arrow,d=void 0!==u&&u,y=t.back,O=void 0!==y&&y,j=t.caps,I=void 0!==j&&j,k=t.children,E=t.className,S=void 0===E?"":E,_=t.danger,M=void 0!==_&&_,D=t.disabled,N=void 0!==D&&D,w=t.external,A=void 0!==w&&w,T=t.hideExternalIndicator,C=void 0!==T&&T,R=t.href,P=void 0===R?"":R,x=t.inverse,L=void 0!==x&&x,G=t.noFlex,z=void 0!==G&&G,Z=t.onClick,W=t.small,B=void 0!==W&&W,U=t.standalone,V=void 0!==U&&U,F=t.linkButton,H=void 0!==F&&F,Y=t.to,Q=t.leadingIcon,J=t.trailingIcon,X=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),q=P||Y||!Z?Y?"ROUTER_LINK":A?"EXTERNAL_LINK":"LINK":N?"BUTTON_DISABLED":"BUTTON",K="BUTTON"===q||"BUTTON_DISABLED"===q?"button":"ROUTER_LINK"===q?g.b:"a",$=("EXTERNAL_LINK"===q&&(r=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===q&&(r=Object(f._x)("(disabled)","screen reader text","google-site-kit")),r?a?"".concat(a," ").concat(r):"string"==typeof k?"".concat(k," ").concat(r):void 0:a),ee=Q,te=J;return O&&(ee=e.createElement(b.a,{width:14,height:14})),A&&!C&&(te=e.createElement(v.a,{width:14,height:14})),d&&!L&&(te=e.createElement(p.a,{width:14,height:14})),d&&L&&(te=e.createElement(m.a,{width:14,height:14})),e.createElement(K,i()({"aria-label":$,className:s()("googlesitekit-cta-link",S,{"googlesitekit-cta-link--secondary":l,"googlesitekit-cta-link--inverse":L,"googlesitekit-cta-link--small":B,"googlesitekit-cta-link--caps":I,"googlesitekit-cta-link--danger":M,"googlesitekit-cta-link--disabled":N,"googlesitekit-cta-link--standalone":V,"googlesitekit-cta-link--link-button":H,"googlesitekit-cta-link--no-flex":!!z}),disabled:N,href:"LINK"!==q&&"EXTERNAL_LINK"!==q||N?void 0:P,onClick:Z,rel:"EXTERNAL_LINK"===q?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===q?"_blank":void 0,to:Y},X),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},k),!!te&&e.createElement(h.a,{marginLeft:5},te))}));y.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=y}).call(this,n(3))},211:function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"d",(function(){return a})),n.d(t,"e",(function(){return c})),n.d(t,"f",(function(){return s.a})),n.d(t,"b",(function(){return l.a})),n.d(t,"a",(function(){return u})),n.d(t,"g",(function(){return g}));var r=n(2);function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.keyColumnIndex,i=void 0===n?0:n,a=t.maxSlices,o=t.withOthers,c=void 0!==o&&o,s=t.tooltipCallback,l=e||{},u=l.rows,g=void 0===u?[]:u,d="function"==typeof s,f=["Source","Percent"];d&&f.push({type:"string",role:"tooltip",p:{html:!0}});var p=[f],m=g.filter((function(e){return"date_range_0"===e.dimensionValues[1].value})),b=m.reduce((function(e,t){return e+parseInt(t.metricValues[0].value,10)}),0),v=g.filter((function(e){return"date_range_1"===e.dimensionValues[1].value})),h=v.reduce((function(e,t){return e+parseInt(t.metricValues[0].value,10)}),0),y=c,O=m.length,j=b,I=h;a>0?(y=c&&m.length>a,O=Math.min(m.length,y?a-1:a)):(y=!1,O=m.length);for(var k=function(e){var t=m[e],n=t.metricValues[i].value,r=v.find((function(e){return e.dimensionValues[0].value===t.dimensionValues[0].value})),a=r?r.metricValues[i].value:0;j-=n,I-=a;var o=b>0?n/b:0,c=[t.dimensionValues[0].value,o];if(d){var l=g.find((function(e){var n=e.dimensionValues;return"date_range_1"===n[1].value&&n[0].value===t.dimensionValues[0].value}));c.push(s(t,l,c))}p.push(c)},E=0;E<O;E++)k(E);if(y&&j>0){var S=[Object(r.__)("Others","google-site-kit"),j/b];d&&S.push(s({metricValues:[{value:j}]},{metricValues:[{value:I}]},S)),p.push(S)}return p}var a=function(e){var t,n,r,i,a,o,c;if(void 0!==e){var s=((null==e?void 0:e.rows)||[]).filter((function(e){return"date_range_0"===e.dimensionValues[1].value}));return 1===(null==s?void 0:s.length)||(null==s||null===(t=s[0])||void 0===t||null===(n=t.metricValues)||void 0===n||null===(r=n[0])||void 0===r?void 0:r.value)===(null==e||null===(i=e.totals)||void 0===i||null===(a=i[0])||void 0===a||null===(o=a.metricValues)||void 0===o||null===(c=o[0])||void 0===c?void 0:c.value)}},o=n(15);function c(e){var t;if(void 0!==e)return!((null==e?void 0:e.rows)&&(null==e?void 0:e.totals)&&!(null==e||null===(t=e.totals)||void 0===t?void 0:t.every(o.isEmpty)))||!e.totals.some((function(e){return!!e.metricValues&&e.metricValues.some((function(e){return e.value>0}))}))}var s=n(280),l=(n(130),n(357),n(472));function u(e){return e.replace(/&amp;/gi,"&")}function g(e){return u(e).split("; ")}},220:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M2 5.309l1.474 2.14c.69 1.001 1.946 1.001 2.636 0L10 1.8",stroke:"currentColor",strokeWidth:1.6,strokeLinecap:"square"});t.a=function SvgCheck2(e){return r.createElement("svg",i({viewBox:"0 0 12 9",fill:"none"},e),a)}},228:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Title}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Title(t){var n=t.className,r=t.children;return e.createElement("p",{className:i()("googlesitekit-banner__title",n)},r)}Title.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},229:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Description(t){var n=t.className,r=t.children;return e.createElement("div",{className:i()("googlesitekit-banner__description",n)},r)}Description.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},23:function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var r=n(90),i="xlarge",a="desktop",o="tablet",c="small";function s(){var e=Object(r.a)();return e>1280?i:e>960?a:e>600?o:c}},230:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HelpText}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function HelpText(t){var n=t.className,r=t.children;return e.createElement("p",{className:i()("googlesitekit-banner__help-text",n)},r)}HelpText.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},231:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Footer}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Footer(t){var n=t.className,r=t.children;return e.createElement("div",{className:i()("googlesitekit-banner__footer",n)},r)}Footer.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},24:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return l})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return g})),n.d(t,"r",(function(){return d})),n.d(t,"k",(function(){return f})),n.d(t,"u",(function(){return p})),n.d(t,"v",(function(){return m})),n.d(t,"q",(function(){return b})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return y})),n.d(t,"a",(function(){return O})),n.d(t,"d",(function(){return j})),n.d(t,"c",(function(){return I})),n.d(t,"f",(function(){return k})),n.d(t,"g",(function(){return E}));var r="mainDashboard",i="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",l="splash",u="adminBar",g="adminBarViewOnly",d="settings",f="adBlockingRecovery",p="wpDashboard",m="wpDashboardViewOnly",b="moduleSetup",v="metricSelection",h="key-metrics",y="traffic",O="content",j="speed",I="monetization",k=[r,i,a,o,c,l,d,b,v],E=[a,o,g,m]},242:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(1),s=n(122),l=n(423);function DataBlock(t){var n=t.stat,r=void 0===n?null:n,i=t.className,a=void 0===i?"":i,u=t.title,g=void 0===u?"":u,d=t.datapoint,f=void 0===d?null:d,p=t.datapointUnit,m=void 0===p?"":p,b=t.change,v=void 0===b?null:b,h=t.changeDataUnit,y=void 0===h?"":h,O=t.context,j=void 0===O?"default":O,I=t.period,k=void 0===I?"":I,E=t.selected,S=void 0!==E&&E,_=t.source,M=t.sparkline,D=t.handleStatSelection,N=void 0===D?null:D,w=t.invertChangeColor,A=void 0!==w&&w,T=t.gatheringData,C=void 0!==T&&T,R=t.gatheringDataNoticeStyle,P=void 0===R?s.a.DEFAULT:R,x=t.badge,L=Object(c.useCallback)((function(){!C&&N&&N(r)}),[C,N,r]),G=Object(c.useCallback)((function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),L())}),[L]),z="button"===j,Z=z?"button":"";return e.createElement("div",{className:o()("googlesitekit-data-block",a,"googlesitekit-data-block--".concat(j),{"googlesitekit-data-block--selected":S,"googlesitekit-data-block--is-gathering-data":C}),tabIndex:z&&!C?"0":"-1",role:N&&Z,onClick:L,onKeyDown:G,"aria-disabled":C||void 0,"aria-label":N&&g,"aria-pressed":N&&S},e.createElement(l.a,{title:g,datapoint:f,datapointUnit:m,change:v,changeDataUnit:y,period:k,source:_,sparkline:M,invertChangeColor:A,gatheringData:C,badge:x}),C&&e.createElement(s.b,{style:P}))}DataBlock.propTypes={stat:i.a.number,className:i.a.string,title:i.a.string,datapoint:i.a.oneOfType([i.a.string,i.a.number]),datapointUnit:i.a.string,change:i.a.oneOfType([i.a.string,i.a.number]),changeDataUnit:i.a.oneOfType([i.a.string,i.a.bool]),context:i.a.string,period:i.a.string,selected:i.a.bool,source:i.a.object,sparkline:i.a.element,handleStatSelection:i.a.func,invertChangeColor:i.a.bool,gatheringData:i.a.bool,gatheringDataNoticeStyle:i.a.oneOf(Object.values(s.a)),badge:i.a.oneOfType([i.a.bool,i.a.node])},t.a=DataBlock}).call(this,n(3))},247:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var r=n(5),i=n.n(r),a=n(1),o=n(4),c=n(26);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){var t=Object(o.useDispatch)(c.b).setValue;return Object(a.useCallback)((function(){t("admin-menu-tooltip",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({isTooltipVisible:!0},e))}),[t,e])}},25:function(e,t,n){"use strict";n.d(t,"l",(function(){return i})),n.d(t,"k",(function(){return a})),n.d(t,"j",(function(){return o})),n.d(t,"i",(function(){return c})),n.d(t,"a",(function(){return s})),n.d(t,"o",(function(){return l})),n.d(t,"n",(function(){return u})),n.d(t,"m",(function(){return g})),n.d(t,"c",(function(){return d})),n.d(t,"g",(function(){return f})),n.d(t,"h",(function(){return p})),n.d(t,"d",(function(){return m})),n.d(t,"e",(function(){return b})),n.d(t,"f",(function(){return v})),n.d(t,"b",(function(){return h}));var r=n(2),i="key-metrics-setup-cta-widget",a="googlesitekit-key-metrics-selection-panel-opened",o="key-metrics-selection-form",c="key-metrics-selected",s="key-metrics-effective-selection",l="key-metrics-unstaged-selection",u=2,g=8,d={SLUG:"current-selection",LABEL:Object(r.__)("Current selection","google-site-kit")},f={SLUG:"suggested",LABEL:Object(r.__)("Suggested","google-site-kit")},p={SLUG:"visitors",LABEL:Object(r.__)("Visitors","google-site-kit")},m={SLUG:"driving-traffic",LABEL:Object(r.__)("Driving traffic","google-site-kit")},b={SLUG:"generating-leads",LABEL:Object(r.__)("Generating leads","google-site-kit")},v={SLUG:"selling-products",LABEL:Object(r.__)("Selling products","google-site-kit")},h={SLUG:"content-performance",LABEL:Object(r.__)("Content performance","google-site-kit")}},258:function(e,t,n){"use strict";n.d(t,"a",(function(){return SurveyViewTrigger}));var r=n(1),i=n(0),a=n.n(i),o=n(4),c=n(13),s=n(7);function SurveyViewTrigger(e){var t=e.triggerID,n=e.ttl,i=void 0===n?0:n,a=Object(o.useSelect)((function(e){return e(c.c).isUsingProxy()})),l=Object(o.useDispatch)(s.a).triggerSurvey;return Object(r.useEffect)((function(){a&&l(t,{ttl:i})}),[a,t,i,l]),null}SurveyViewTrigger.propTypes={triggerID:a.a.string.isRequired,ttl:a.a.number}},26:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="core/ui",i="activeContextID"},260:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminMenuTooltip}));var r=n(1),i=n(4),a=n(262),o=n(26),c=n(9),s=n(18),l=n(23);function AdminMenuTooltip(){var t=Object(s.a)(),n=Object(i.useDispatch)(o.b).setValue,u=Object(l.e)(),g=Object(i.useSelect)((function(e){return e(o.b).getValue("admin-menu-tooltip")||{isTooltipVisible:!1}})),d=g.isTooltipVisible,f=void 0!==d&&d,p=g.tooltipSlug,m=g.title,b=g.content,v=g.dismissLabel,h=Object(r.useCallback)((function(){p&&Object(c.I)("".concat(t,"_").concat(p),"tooltip_dismiss"),n("admin-menu-tooltip",void 0)}),[n,p,t]);if(!f)return null;var y=u===l.b||u===l.c;return e.createElement(a.a,{target:y?"body":'#adminmenu [href*="page=googlesitekit-settings"]',placement:y?"center":"auto",className:y?"googlesitekit-tour-tooltip__modal_step":"",disableOverlay:!y,slug:"ga4-activation-banner-admin-menu-tooltip",title:m,content:b,dismissLabel:v,onView:function(){Object(c.I)("".concat(t,"_").concat(p),"tooltip_view")},onDismiss:h})}}).call(this,n(3))},262:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return JoyrideTooltip}));var i=n(5),a=n.n(i),o=n(14),c=n.n(o),s=n(0),l=n(32),u=n(456),g=n(1),d=n(119),f=n(75),p=n(101),m=n(23);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function JoyrideTooltip(t){var n=t.title,i=t.content,a=t.dismissLabel,o=t.disableOverlay,s=void 0===o||o,b=t.target,h=t.cta,y=void 0!==h&&h,O=t.className,j=t.styles,I=void 0===j?{}:j,k=t.slug,E=void 0===k?"":k,S=t.placement,_=void 0===S?"auto":S,M=t.onDismiss,D=void 0===M?function(){}:M,N=t.onView,w=void 0===N?function(){}:N,A=t.onTourStart,T=void 0===A?function(){}:A,C=t.onTourEnd,R=void 0===C?function(){}:C,P=function(){return!!e.document.querySelector(b)},x=Object(g.useState)(P),L=c()(x,2),G=L[0],z=L[1],Z=Object(m.e)(),W=Z===m.b||Z===m.c,B=Object(g.useState)(!0),U=c()(B,2),V=U[0],F=U[1],H=Object(g.useRef)(W);if(Object(u.a)((function(){P()&&z(!0)}),G?null:250),Object(g.useEffect)((function(){if(G&&e.ResizeObserver){var t=e.document.querySelector(b),n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}),[b,G]),Object(g.useEffect)((function(){if(H.current!==W){F(!1);var e=setTimeout((function(){F(!0)}),50);return H.current=W,function(){return clearTimeout(e)}}}),[W]),!G)return null;var Y=[{title:n,target:b,content:i,disableBeacon:!0,isFixed:!0,placement:_,cta:y,className:O}],Q={close:a,last:a};return r.createElement(f.a,{slug:E},r.createElement(l.e,{callback:function(t){switch(t.type){case l.b.TOUR_START:T(),e.document.body.classList.add("googlesitekit-showing-tooltip");break;case l.b.TOUR_END:R(),e.document.body.classList.remove("googlesitekit-showing-tooltip");break;case l.b.STEP_AFTER:D();break;case l.b.TOOLTIP:w()}},disableOverlay:s,spotlightPadding:0,floaterProps:p.b,locale:Q,steps:Y,styles:v(v(v({},p.c),I),{},{options:v(v({},p.c.options),null==I?void 0:I.options),spotlight:v(v({},p.c.spotlight),null==I?void 0:I.spotlight)}),tooltipComponent:d.a,run:V,disableScrolling:!0}))}JoyrideTooltip.propTypes={title:s.PropTypes.node,content:s.PropTypes.string,disableOverlay:s.PropTypes.bool,dismissLabel:s.PropTypes.string,target:s.PropTypes.string.isRequired,onDismiss:s.PropTypes.func,onShow:s.PropTypes.func,className:s.PropTypes.string,styles:s.PropTypes.object,slug:s.PropTypes.string,placement:s.PropTypes.string,onView:s.PropTypes.func}}).call(this,n(28),n(3))},270:function(e,t,n){"use strict";n.d(t,"d",(function(){return s})),n.d(t,"e",(function(){return l})),n.d(t,"b",(function(){return u})),n.d(t,"a",(function(){return g})),n.d(t,"c",(function(){return d}));var r=n(27),i=n.n(r),a=n(15),o=n(23),c=n(9),s=function(e,t){if(!(null==t?void 0:t.length))return e;var n=[];return(null==e?void 0:e.length)&&(n=e[0].reduce((function(e,t,n){return(null==t?void 0:t.role)?[].concat(i()(e),[n]):e}),[])),e.map((function(e){return e.filter((function(e,r){return 0===r||t.includes(r-1)||n.includes(r-1)}))}))},l=function(e,t,n,r){var i={height:e||t,width:n||r};return i.width&&!i.height&&(i.height="100%"),i.height&&!i.width&&(i.width="100%"),i},u=function(e,t,n){var r=i()(e||[]);return t&&r.push({eventName:"ready",callback:t}),n&&r.push({eventName:"select",callback:n}),r},g=function(e,t,n,r,i,s){var l,u,g,d,f,p,m,b,v=Object(a.cloneDeep)(e);t&&"LineChart"===n&&((null==e||null===(l=e.vAxis)||void 0===l||null===(u=l.viewWindow)||void 0===u?void 0:u.min)||Object(a.set)(v,"vAxis.viewWindow.min",0),(null==e||null===(g=e.vAxis)||void 0===g||null===(d=g.viewWindow)||void 0===d?void 0:d.max)||Object(a.set)(v,"vAxis.viewWindow.max",100),(null==e||null===(f=e.hAxis)||void 0===f||null===(p=f.viewWindow)||void 0===p?void 0:p.min)||(Object(a.set)(v,"hAxis.viewWindow.min",Object(c.G)(r)),delete v.hAxis.ticks),(null==e||null===(m=e.hAxis)||void 0===m||null===(b=m.viewWindow)||void 0===b?void 0:b.max)||(Object(a.set)(v,"hAxis.viewWindow.max",Object(c.G)(i)),delete v.hAxis.ticks));if("LineChart"===n){var h,y,O;if((null==e||null===(h=e.hAxis)||void 0===h?void 0:h.maxTextLines)||Object(a.set)(v,"hAxis.maxTextLines",1),!(null==e||null===(y=e.hAxis)||void 0===y?void 0:y.minTextSpacing)){var j=s===o.b?50:100;Object(a.set)(v,"hAxis.minTextSpacing",j)}void 0===(null==e||null===(O=e.tooltip)||void 0===O?void 0:O.isHtml)&&(Object(a.set)(v,"tooltip.isHtml",!0),Object(a.set)(v,"tooltip.trigger","both"))}return Object(a.merge)(v,{hAxis:{textStyle:{fontSize:10,color:"#5f6561"}},vAxis:{textStyle:{color:"#5f6561",fontSize:10}},legend:{textStyle:{color:"#131418",fontSize:12}}}),v},d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object(c.r)(),n=Intl.NumberFormat(t,{style:"currency",currency:e}),r=n.formatToParts(1e6);return r.reduce((function(e,t){var n=t.value;switch(t.type){case"group":return e+",";case"decimal":return e+".";case"currency":return e+n;case"literal":return e+(/^\s*$/.test(n)?n:"");case"integer":var i=n.replace(/\d/g,"#");return e+(Object(a.findLast)(r,(function(e){return"integer"===e.type}))===t?i.replace(/#$/,"0"):i);case"fraction":return e+n.replace(/\d/g,"0");default:return e}}),"")}},277:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileText}));var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),s=n.n(c),l=n(193),u=n(9),g=n(208);function MetricTileText(t){var n=t.metricValue,r=t.metricValueFormat,a=t.subText,c=t.previousValue,s=t.currentValue,d=o()(t,["metricValue","metricValueFormat","subText","previousValue","currentValue"]),f=Object(u.m)(r);return e.createElement(g.a,i()({className:"googlesitekit-km-widget-tile--text"},d),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-container"},e.createElement("div",{className:"googlesitekit-km-widget-tile__metric"},n),e.createElement("p",{className:"googlesitekit-km-widget-tile__subtext"},a)),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-change-container"},e.createElement(l.a,{previousValue:c,currentValue:s,isAbsolute:"percent"===(null==f?void 0:f.style)})))}MetricTileText.propTypes={metricValue:s.a.oneOfType([s.a.string,s.a.number]),subtext:s.a.string,previousValue:s.a.number,currentValue:s.a.number}}).call(this,n(3))},280:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var r=n(5),i=n.n(r),a=n(22),o=n.n(a),c=n(66),s=n.n(c),l=n(15);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var d=s()((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.metrics,n=e.dimensions,r=o()(e,["metrics","dimensions"]);return g({metrics:f(t),dimensions:p(n)},r)})),f=function(e){return Object(l.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(l.isPlainObject)(e)}))},p=function(e){return Object(l.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(l.isPlainObject)(e)}))}},29:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"f",(function(){return c})),n.d(t,"k",(function(){return s})),n.d(t,"j",(function(){return l})),n.d(t,"h",(function(){return u})),n.d(t,"i",(function(){return g})),n.d(t,"e",(function(){return d})),n.d(t,"g",(function(){return f}));var r=1,i=2,a=3,o="enhanced-measurement-activation-banner-tooltip-state",c="enhanced-measurement-activation-banner-dismissed-item",s="_r.explorerCard..selmet",l="_r.explorerCard..seldim",u="_r..dataFilters",g="_r..nav",d="key-metrics-connect-ga4-cta-widget",f="analytics-4"},30:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},316:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(1),s=n(2),l=n(11),u=n(79),g=n(21);function NewBadge(t){var n=t.tooltipTitle,r=t.learnMoreLink,i=t.forceOpen,a=t.hasLeftSpacing,d=t.hasNoSpacing,f=t.onLearnMoreClick,p=void 0===f?function(){}:f,m=e.createElement(u.a,{className:o()("googlesitekit-new-badge",{"googlesitekit-new-badge--has-no-spacing":d}),label:Object(s.__)("New","google-site-kit"),hasLeftSpacing:a});return n?e.createElement(l.Tooltip,{tooltipClassName:"googlesitekit-new-badge__tooltip",title:e.createElement(c.Fragment,null,n,e.createElement("br",null),e.createElement(g.a,{href:r,onClick:p,external:!0,hideExternalIndicator:!0},Object(s.__)("Learn more","google-site-kit"))),placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,open:i,interactive:!0},m):m}NewBadge.propTypes={tooltipTitle:i.a.string,learnMoreLink:i.a.string,forceOpen:i.a.bool,onLearnMoreClick:i.a.func,hasLeftSpacing:i.a.bool,hasNoSpacing:i.a.bool},t.a=NewBadge}).call(this,n(3))},317:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileError}));var r=n(102),i=n(137);function MetricTileError(t){var n=t.children,a=t.headerText,o=t.infoTooltip,c=t.title;return e.createElement("div",{className:"googlesitekit-km-widget-tile--error"},e.createElement(r.a,{title:c,headerText:a,headerContent:o&&e.createElement(i.a,{title:o}),description:"",error:!0},n))}}).call(this,n(3))},323:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.149 7.96l-5.166 5.166a.344.344 0 00-.094.176l-.35 1.755a.344.344 0 00.404.404l1.755-.35a.344.344 0 00.175-.095l5.166-5.165-1.89-1.89zm2.301-1.814a1.031 1.031 0 00-1.458 0L6.497 12.64a1.031 1.031 0 00-.282.527l-.35 1.755a1.031 1.031 0 001.213 1.213l1.754-.35c.2-.04.383-.139.527-.283l6.495-6.494a1.031 1.031 0 000-1.459L14.45 6.146z"}),o=r.createElement("path",{d:"M12.149 7.96l.117-.116a.165.165 0 00-.234 0l.117.117zm-5.166 5.166l-.116-.116.116.116zm-.094.176l.162.033-.162-.033zm-.35 1.755l.161.032-.162-.032zm.404.404l.032.162-.032-.162zm1.755-.35l.032.161-.032-.162zm.175-.095l.117.117-.117-.117zm5.166-5.165l.116.116a.165.165 0 000-.233l-.116.117zm-1.047-3.705l.116.116-.116-.116zm1.458 0l-.116.116.116-.116zM6.497 12.64l.117.117-.117-.117zm-.282.527l-.162-.032.162.032zm-.35 1.755l.161.032-.162-.032zm1.213 1.213l-.033-.162.033.162zm1.754-.35l.033.161-.033-.162zm.527-.283l.117.117-.117-.117zm6.495-6.494l-.117-.117.117.117zm0-1.459l.117-.116-.117.116zm-3.822.295L6.867 13.01l.233.233 5.166-5.165-.234-.234zM6.867 13.01a.509.509 0 00-.14.26l.324.065a.18.18 0 01.05-.092l-.234-.233zm-.14.26l-.35 1.754.323.065.351-1.755-.323-.064zm-.35 1.754a.509.509 0 00.598.599l-.064-.324a.179.179 0 01-.21-.21l-.324-.065zm.598.599l1.755-.35-.065-.325-1.754.351.064.324zm1.755-.35a.508.508 0 00.26-.14l-.233-.233a.18.18 0 01-.092.048l.065.324zm.26-.14l5.165-5.166-.233-.233L8.757 14.9l.233.233zm3.042-7.055l1.89 1.89.233-.234-1.89-1.89-.233.234zm1.076-1.816a.866.866 0 011.226 0l.233-.233a1.196 1.196 0 00-1.692 0l.233.233zm-6.494 6.495l6.494-6.495-.233-.233-6.494 6.495.233.233zm-.237.443a.866.866 0 01.237-.443l-.233-.233c-.167.167-.281.38-.328.61l.324.066zm-.35 1.754l.35-1.754-.324-.065-.35 1.755.323.064zm1.018 1.02a.866.866 0 01-1.019-1.02l-.323-.065a1.196 1.196 0 001.407 1.408l-.065-.324zm1.755-.351l-1.755.35.065.324 1.755-.35-.065-.324zm.443-.237a.866.866 0 01-.443.237l.065.323c.231-.046.444-.16.611-.327l-.233-.233zm6.494-6.495l-6.494 6.495.233.233 6.495-6.494-.234-.234zm0-1.225a.866.866 0 010 1.225l.234.234a1.196 1.196 0 000-1.692l-.234.233zm-1.403-1.404l1.403 1.404.234-.233-1.404-1.404-.233.233z"});t.a=function SvgPencilAlt(e){return r.createElement("svg",i({viewBox:"0 0 22 22",fill:"currentColor"},e),a,o)}},328:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelHeader}));var r=n(0),i=n.n(r),a=n(21),o=n(114);function SelectionPanelHeader(t){var n=t.children,r=t.title,i=t.onCloseClick;return e.createElement("header",{className:"googlesitekit-selection-panel-header"},e.createElement("div",{className:"googlesitekit-selection-panel-header__row"},e.createElement("h3",null,r),e.createElement(a.a,{className:"googlesitekit-selection-panel-header__close",onClick:i,linkButton:!0},e.createElement(o.a,{width:"15",height:"15"}))),n)}SelectionPanelHeader.propTypes={children:i.a.node,title:i.a.string,onCloseClick:i.a.func}}).call(this,n(3))},329:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItem}));var r=n(0),i=n.n(r),a=n(2),o=n(342),c=n(79);function SelectionPanelItem(t){var n=t.children,r=t.id,i=t.slug,s=t.title,l=t.description,u=t.isItemSelected,g=t.isItemDisabled,d=t.onCheckboxChange,f=t.subtitle,p=t.suffix,m=t.badge,b=t.isNewlyDetected;return e.createElement("div",{className:"googlesitekit-selection-panel-item"},e.createElement(o.a,{badge:m,checked:u,disabled:g,id:r,onChange:d,title:s,value:i},f&&e.createElement("span",{className:"googlesitekit-selection-panel-item__subtitle"},f),l,n),b&&e.createElement(c.a,{label:Object(a.__)("New","google-site-kit")}),p&&e.createElement("span",{className:"googlesitekit-selection-panel-item__suffix"},p))}SelectionPanelItem.propTypes={children:i.a.node,id:i.a.string,slug:i.a.string,title:i.a.string,description:i.a.string,isItemSelected:i.a.bool,isItemDisabled:i.a.bool,onCheckboxChange:i.a.func,subtitle:i.a.string,suffix:i.a.node,badge:i.a.node,isNewlyDetected:i.a.bool}}).call(this,n(3))},33:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},330:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItems}));var r=n(20),i=n.n(r),a=n(0),o=n.n(a),c=n(1),s=n(2);function SelectionPanelItems(t){var n=t.currentSelectionTitle,r=void 0===n?Object(s.__)("Current selection","google-site-kit"):n,a=t.availableItemsTitle,o=void 0===a?Object(s.__)("Additional items","google-site-kit"):a,l=t.savedItemSlugs,u=void 0===l?[]:l,g=t.availableSavedItems,d=void 0===g?{}:g,f=t.availableUnsavedItems,p=void 0===f?{}:f,m=t.ItemComponent,b=t.notice,v=function(t){return Object.keys(t).map((function(n){return e.createElement(m,i()({key:n,slug:n,savedItemSlugs:u},t[n]))}))},h=Object.keys(p).length;return e.createElement("div",{className:"googlesitekit-selection-panel-items"},0!==u.length&&e.createElement(c.Fragment,null,e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},r),e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},v(d)),h>0&&e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},o)),h>0&&e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},v(p)),b)}SelectionPanelItems.propTypes={currentSelectionTitle:o.a.string,availableItemsTitle:o.a.string,savedItemSlugs:o.a.array,availableSavedItems:o.a.object,availableUnsavedItems:o.a.object,ItemComponent:o.a.elementType,notice:o.a.node}}).call(this,n(3))},331:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelFooter}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(14),s=n.n(c),l=n(15),u=n(0),g=n.n(u),d=n(1),f=n(42),p=n(2),m=n(4),b=n(11),v=n(59),h=n(9),y=n(8),O=n(48),j=n(35);function SelectionPanelFooter(t){var n=t.savedItemSlugs,r=void 0===n?[]:n,a=t.selectedItemSlugs,c=void 0===a?[]:a,u=t.saveSettings,g=void 0===u?function(){}:u,I=t.saveError,k=t.itemLimitError,E=t.minSelectedItemCount,S=void 0===E?0:E,_=t.maxSelectedItemCount,M=void 0===_?0:_,D=t.isBusy,N=t.onSaveSuccess,w=void 0===N?function(){}:N,A=t.onCancel,T=void 0===A?function(){}:A,C=t.isOpen,R=t.closePanel,P=void 0===R?function(){}:R,x=Object(d.useState)(null),L=s()(x,2),G=L[0],z=L[1],Z=Object(d.useState)(!1),W=s()(Z,2),B=W[0],U=W[1],V=Object(m.useSelect)((function(e){return e(y.r).isFetchingSyncAvailableAudiences()})),F=Object(d.useMemo)((function(){return!Object(l.isEqual)(Object(h.E)(c),Object(h.E)(r))}),[r,c]),H=(null==r?void 0:r.length)>0&&F?Object(p.__)("Apply changes","google-site-kit"):Object(p.__)("Save selection","google-site-kit"),Y=Object(d.useCallback)(o()(i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,g(c);case 2:t=e.sent,t.error||(w(),P(),z(H),U(!0));case 5:case"end":return e.stop()}}),e)}))),[g,c,w,P,H]),Q=Object(d.useCallback)((function(){P(),T()}),[P,T]),J=Object(d.useState)(null),X=s()(J,2),q=X[0],K=X[1];Object(d.useEffect)((function(){null!==q&&q!==C&&C&&(z(null),U(!1)),K(C)}),[C,q]);var $=(null==c?void 0:c.length)||0,ee=V?e.createElement(O.a,{width:"89px",height:"20px"}):e.createElement("p",{className:"googlesitekit-selection-panel-footer__item-count"},Object(f.a)(Object(p.sprintf)(/* translators: 1: Number of selected items. 2: Maximum number of items that can be selected. */
Object(p.__)("%1$d selected <MaxCount>(up to %2$d)</MaxCount>","google-site-kit"),$,M),{MaxCount:e.createElement("span",{className:"googlesitekit-selection-panel-footer__item-count--max-count"})}));return e.createElement("footer",{className:"googlesitekit-selection-panel-footer"},I&&e.createElement(v.a,{error:I}),e.createElement("div",{className:"googlesitekit-selection-panel-footer__content"},F&&k?e.createElement(j.a,{type:j.a.TYPES.ERROR,description:k}):ee,e.createElement("div",{className:"googlesitekit-selection-panel-footer__actions"},e.createElement(b.Button,{onClick:Q,disabled:D,tertiary:!0},Object(p.__)("Cancel","google-site-kit")),e.createElement(b.SpinnerButton,{onClick:Y,isSaving:D,disabled:$<S||$>M||D||!C&&B},G||H))))}SelectionPanelFooter.propTypes={savedItemSlugs:g.a.array,selectedItemSlugs:g.a.array,saveSettings:g.a.func,saveError:g.a.object,itemLimitError:g.a.string,minSelectedItemCount:g.a.number,maxSelectedItemCount:g.a.number,isBusy:g.a.bool,onSaveSuccess:g.a.func,onCancel:g.a.func,isOpen:g.a.bool,closePanel:g.a.func}}).call(this,n(3))},339:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M7.334 11.333h1.333v-4H7.334v4zM8.001 6a.658.658 0 00.667-.667.605.605 0 00-.2-.467.605.605 0 00-.467-.2.658.658 0 00-.667.667c0 .189.061.35.183.483A.69.69 0 008.001 6zm0 8.666a6.583 6.583 0 01-2.6-.516 6.85 6.85 0 01-2.117-1.434A6.85 6.85 0 011.851 10.6 6.582 6.582 0 011.334 8c0-.923.172-1.79.517-2.6a6.85 6.85 0 011.433-2.117c.6-.6 1.306-1.072 2.117-1.417A6.404 6.404 0 018 1.333c.922 0 1.789.178 2.6.533a6.618 6.618 0 012.116 1.417c.6.6 1.072 1.306 1.417 2.117.355.81.533 1.677.533 2.6 0 .922-.178 1.789-.533 2.6a6.619 6.619 0 01-1.417 2.116 6.85 6.85 0 01-2.116 1.434 6.583 6.583 0 01-2.6.516zm0-1.333c1.489 0 2.75-.517 3.783-1.55s1.55-2.294 1.55-3.783c0-1.49-.517-2.75-1.55-3.784-1.033-1.033-2.294-1.55-3.783-1.55-1.49 0-2.75.517-3.784 1.55C3.184 5.25 2.667 6.511 2.667 8c0 1.489.517 2.75 1.55 3.783 1.034 1.033 2.295 1.55 3.784 1.55z",fill:"currentColor"});t.a=function SvgInfoGreen(e){return r.createElement("svg",i({viewBox:"0 0 16 16",fill:"none"},e),a)}},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return g}));n(15);var r=n(2),i="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===i}function s(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function l(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||l(e))}function g(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},340:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanel}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a),c=n(341);function SelectionPanel(t){var n=t.children,r=t.isOpen,a=t.isLoading,o=t.onOpen,s=t.closePanel,l=t.className,u=null==l?void 0:l.split(/\s+/).map((function(e){return".".concat(e)})).join(""),g=u?"".concat(u," .googlesitekit-selection-panel-item .googlesitekit-selection-box input"):".googlesitekit-selection-panel-item .googlesitekit-selection-box input";return e.createElement(c.a,{className:i()("googlesitekit-selection-panel",l),isOpen:r,isLoading:a,onOpen:o,closeSheet:s,focusTrapOptions:{initialFocus:g}},n)}SelectionPanel.propTypes={children:o.a.node,isOpen:o.a.bool,isLoading:o.a.bool,onOpen:o.a.func,closePanel:o.a.func,className:o.a.string}}).call(this,n(3))},341:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SideSheet}));var r=n(5),i=n.n(r),a=n(10),o=n.n(a),c=n(427),s=n.n(c),l=n(0),u=n.n(l),g=n(213),d=n(397),f=n(1),p=n(57),m=n(75);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SideSheet(t){var n=t.className,r=t.children,i=t.isOpen,a=t.isLoading,c=t.onOpen,l=void 0===c?function(){}:c,u=t.closeSheet,b=void 0===u?function(){}:u,h=t.focusTrapOptions,y=void 0===h?{}:h,O=Object(f.useRef)();return Object(f.useEffect)((function(){i?(l(),document.body.classList.add("googlesitekit-side-sheet-scroll-lock")):document.body.classList.remove("googlesitekit-side-sheet-scroll-lock")}),[i,l]),Object(g.a)(O,b),Object(d.a)((function(e){return i&&p.c===e.keyCode}),b),e.createElement(m.a,null,e.createElement(s.a,{active:!!i&&!a,focusTrapOptions:v({fallbackFocus:"body"},y)},e.createElement("section",{ref:O,className:o()("googlesitekit-side-sheet",n,{"googlesitekit-side-sheet--open":i}),role:"dialog","aria-modal":"true","aria-hidden":!i,tabIndex:"0"},r)),i&&e.createElement("span",{className:"googlesitekit-side-sheet-overlay"}))}SideSheet.propTypes={className:u.a.string,children:u.a.node,isOpen:u.a.bool,isLoading:u.a.bool,onOpen:u.a.func,closeSheet:u.a.func,focusTrapOptions:u.a.object}}).call(this,n(3))},342:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionBox}));var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(11);function SelectionBox(t){var n=t.badge,r=t.checked,i=t.children,a=t.disabled,s=t.id,l=t.onChange,u=t.title,g=t.value;return e.createElement("div",{className:o()("googlesitekit-selection-box",{"googlesitekit-selection-box--disabled":a})},e.createElement(c.Checkbox,{checked:r,description:i,disabled:a,id:s,name:s,onChange:l,value:g,badge:n},u))}SelectionBox.propTypes={badge:i.a.node,checked:i.a.bool,children:i.a.node,disabled:i.a.bool,id:i.a.string,onChange:i.a.func,title:i.a.string,value:i.a.string}}).call(this,n(3))},35:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(10),s=n.n(c),l=n(1),u=n(139),g=n(140),d=n(141),f=n(105),p=n(106),m=n(38);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=Object(l.forwardRef)((function(t,n){var r=t.className,i=t.title,a=t.description,o=t.dismissButton,c=t.ctaButton,l=t.type,b=void 0===l?m.a.INFO:l,v=t.children,h=t.hideIcon;return e.createElement("div",{ref:n,className:s()("googlesitekit-notice","googlesitekit-notice--".concat(b),r)},!h&&e.createElement("div",{className:"googlesitekit-notice__icon"},e.createElement(u.a,{type:b})),e.createElement("div",{className:"googlesitekit-notice__content"},i&&e.createElement(g.a,null,i),a&&e.createElement(d.a,null,a)),((null==o?void 0:o.label)||(null==o?void 0:o.onClick)||(null==c?void 0:c.label)&&((null==c?void 0:c.onClick)||(null==c?void 0:c.href))||v)&&e.createElement("div",{className:"googlesitekit-notice__action"},v,((null==o?void 0:o.label)||(null==o?void 0:o.onClick))&&e.createElement(p.a,{label:o.label,onClick:o.onClick,disabled:o.disabled}),(null==c?void 0:c.label)&&((null==c?void 0:c.onClick)||(null==c?void 0:c.href))&&e.createElement(f.a,{label:c.label,onClick:c.onClick,inProgress:c.inProgress,disabled:c.disabled,href:c.href,external:c.external,hideExternalIndicator:c.hideExternalIndicator})))}));h.TYPES=m.a,h.propTypes={className:o.a.string,title:o.a.oneOfType([o.a.string,o.a.object]),description:o.a.node,type:o.a.oneOf(Object.values(m.a)),dismissButton:o.a.shape(p.a.propTypes),ctaButton:o.a.shape(v(v({},f.a.propTypes),{},{label:o.a.string})),children:o.a.node,hideIcon:o.a.bool},t.a=h}).call(this,n(3))},357:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(15),i=n(130);function a(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(r.isPlainObject)(e)&&(!(!e.hasOwnProperty("fieldNames")||!Array.isArray(e.fieldNames)||0===e.fieldNames.length)&&(!(!e.hasOwnProperty("limit")||"number"!=typeof e.limit)&&!(e.hasOwnProperty("orderby")&&!Object(i.e)(e.orderby))))}))}},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return y})),n.d(t,"c",(function(){return O})),n.d(t,"e",(function(){return j})),n.d(t,"b",(function(){return I}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,g="googlesitekit_",d="".concat(g).concat("1.157.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),f=["sessionStorage","localStorage"],p=[].concat(f),m=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",r.setItem(a,a),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return v.apply(this,arguments)}function v(){return(v=o()(i.a.mark((function t(){var n,r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=s(p),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(a=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,m(a);case 11:if(!t.sent){t.next=13;break}u=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(i.a.mark((function e(t){var n,r,a,o,c,s,l;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(d).concat(t)))){e.next=10;break}if(a=JSON.parse(r),o=a.timestamp,c=a.ttl,s=a.value,l=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:l});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),y=function(){var t=o()(i.a.mark((function t(n,r){var a,o,s,l,u,g,f,p,m=arguments;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=m.length>2&&void 0!==m[2]?m[2]:{},o=a.ttl,s=void 0===o?c.b:o,l=a.timestamp,u=void 0===l?Math.round(Date.now()/1e3):l,g=a.isError,f=void 0!==g&&g,t.next=3,b();case 3:if(!(p=t.sent)){t.next=14;break}return t.prev=5,p.setItem("".concat(d).concat(n),JSON.stringify({timestamp:u,ttl:s,value:r,isError:f})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),O=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(g)?n:"".concat(d).concat(n),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),j=function(){var t=o()(i.a.mark((function t(){var n,r,a,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(g)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),I=function(){var e=o()(i.a.mark((function e(){var t,n,r,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,j();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return a=r.value,e.next=14,O(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},377:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return ChipTabGroup}));var i,a=n(20),o=n.n(a),c=n(27),s=n.n(c),l=n(14),u=n.n(l),g=n(5),d=n.n(g),f=n(84),p=n(442),m=n(1),b=n(259),v=n(2),h=n(4),y=n(11),O=n(25),j=n(30),I=n(8),k=n(29),E=n(26),S=n(7),_=n(19),M=n(383),D=n(384),N=n(385),w=n(23),A=n(129),T=n(220),C=n(97),R=n(99),P=(i={},d()(i,O.c.SLUG,T.a),d()(i,O.g.SLUG,C.a),i);function ChipTabGroup(t){var n=t.allMetricItems,i=t.savedItemSlugs,a=Object(m.useRef)(),c=Object(m.useState)(O.c.SLUG),l=u()(c,2),g=l[0],T=l[1],C=Object(m.useState)(0),x=u()(C,2),L=x[0],G=x[1],z=Object(w.e)()===w.b,Z=Object(h.useSelect)((function(e){return e(j.a).getValue(O.j,O.i)})),W=Object(h.useSelect)((function(e){return e(j.a).getValue(O.j,O.a)||[]})),B=Object(h.useSelect)((function(e){return e(j.a).getValue(O.j,O.o)||[]})),U=Object(h.useSelect)((function(e){return e(S.a).isUserInputCompleted()})),V=Object(h.useSelect)((function(e){var t,n=e(S.a).getUserPickedMetrics();if(null==n?void 0:n.length){var r=e(I.r).getKeyMetricsConversionEventWidgets();return Object.keys(r).filter((function(e){return n.some((function(t){return r[e].includes(t)}))}))}var i=e(S.a).getUserInputSettings();return null==i||null===(t=i.includeConversionEvents)||void 0===t?void 0:t.values})),F=Object(h.useSelect)((function(e){return e(_.a).isModuleConnected(k.g)})),H=Object(h.useSelect)((function(e){return F?e(I.r).getDetectedEvents():[]})),Y=Object(h.useSelect)((function(e){return e(S.a).getAnswerBasedMetrics(null,[].concat(s()(V||[]),s()(H||[])))})),Q=[I.l.SUBMIT_LEAD_FORM,I.l.CONTACT,I.l.GENERATE_LEAD].filter((function(e){return(null==H?void 0:H.includes(e))||(null==V?void 0:V.includes(e))})),J=[I.l.ADD_TO_CART,I.l.PURCHASE].filter((function(e){return(null==H?void 0:H.includes(e))||(null==V?void 0:V.includes(e))})),X=Object(m.useMemo)((function(){return[O.h,O.d].concat(s()((null==Q?void 0:Q.length)?[O.e]:[]),s()((null==J?void 0:J.length)?[O.f]:[]),[O.b])}),[Q,J]),q=Object(m.useMemo)((function(){return U&&(null==Y?void 0:Y.length)?[O.c,O.g]:[O.c]}),[U,Y]),K=Object(m.useMemo)((function(){return[].concat(s()(q),s()(X))}),[q,X]),$=Object(h.useSelect)((function(e){if(!F)return[];var t=e(I.r).getNewBadgeEvents();if((null==H?void 0:H.length)&&(null==t?void 0:t.length)){var n=H.filter((function(e){return I.e.includes(e)})),r=t.filter((function(e){return I.e.includes(e)})),i=t.filter((function(e){return!I.e.includes(e)}));if((null==n?void 0:n.length)>1&&r.length>0)return i}return t})),ee=Object(h.useSelect)((function(e){return F?e(I.r).getKeyMetricsConversionEventWidgets():[]})),te=Object(m.useCallback)((function(){var e,t,n,r=null===(e=a.current)||void 0===e?void 0:e.querySelector(".mdc-tab-scroller__scroll-content");if(z){var i=null===(t=a.current)||void 0===t?void 0:t.querySelectorAll(".googlesitekit-chip-tab-group__tab-items .mdc-tab");if((null==i?void 0:i.length)&&r){var o=null===(n=a.current)||void 0===n?void 0:n.getBoundingClientRect(),c=[];i.forEach((function(e,t){var n=e.getBoundingClientRect();n.left>=o.left&&n.right<=o.right&&c.push(t)}));var s=i[c.length];if(s){var l=s.getBoundingClientRect();(l.left>=o.right||l.left-o.right<0&&-(l.left-o.right)<=20)&&("2px"===r.style.columnGap?r.style.columnGap="20px":r.style.columnGap="2px",te())}}}}),[z]),ne=d()({},O.c.SLUG,0),re={},ie={},ae=function(e){var t,r=n[e].group;if((r===g||g===O.c.SLUG&&W.includes(e))&&(re[e]=n[e]),g===O.g.SLUG&&Y.includes(e)&&Y.includes(e)&&(re[e]=n[e]),!ne[r]){var i=Object.keys(n).filter((function(e){return!(n[e].group!==r||!(null==Z?void 0:Z.includes(e)))})).length;ne[r]=i}(null==$?void 0:$.length)&&($.some((function(t){return ee[t].includes(e)}))&&(ie[r]=[].concat(s()(null!==(t=ie[r])&&void 0!==t?t:[]),[e])))};for(var oe in n)ae(oe);var ce=Object(h.useDispatch)(j.a).setValues,se=Object(m.useCallback)((function(){var e;ce(O.j,(e={},d()(e,O.i,Z),d()(e,O.a,[].concat(s()(W),s()(B))),d()(e,O.o,[]),e))}),[Z,W,B,ce]),le=Object(m.useCallback)((function(e,t){if(e)T(e);else{var n=K[t];G(t),T(n.SLUG)}B.length&&se()}),[K,B,T,se]),ue=Object(h.useSelect)((function(e){return e(E.b).getValue(O.k)})),ge=Object(b.a)(ue),de=Object.keys(ie);Object(m.useEffect)((function(){if(!ge&&ue)if(T(O.c.SLUG),G(0),de.length&&z){var e=K.find((function(e){return e.SLUG===de[0]}));G(K.indexOf(e)),T(e.SLUG)}else G(0),T(O.c.SLUG);ge&&!ue&&se(),!ge&&ue&&te()}),[ue,ge,B,K,z,de,se,te]);var fe=Object(A.a)(te,50);Object(f.a)((function(){e.addEventListener("resize",fe)})),Object(p.a)((function(){return e.removeEventListener("resize",fe)}));var pe=[[].concat(s()(q),s()(X.slice(0,2))),s()(X.slice(2))];return r.createElement("div",{className:"googlesitekit-chip-tab-group"},r.createElement("div",{className:"googlesitekit-chip-tab-group__tab-items",ref:a},!z&&pe.map((function(e){return r.createElement("div",{key:"row-".concat(e[0].SLUG),className:"googlesitekit-chip-tab-group__tab-items-row"},e.map((function(e){return r.createElement(M.a,{key:e.SLUG,slug:e.SLUG,label:e.LABEL,hasNewBadge:!!(null==ie?void 0:ie[e.SLUG]),isActive:e.SLUG===g,onClick:le,selectedCount:ne[e.SLUG]})})))})),z&&r.createElement(y.TabBar,{activeIndex:L,handleActiveIndexUpdate:function(e){return le(null,e)}},K.map((function(e,t){var n=P[e.SLUG]||R.a;return r.createElement(y.Tab,{key:t,"aria-label":e.LABEL},r.createElement(n,{width:12,height:12,className:"googlesitekit-chip-tab-group__chip-item-svg googlesitekit-chip-tab-group__tab-item-mobile-svg googlesitekit-chip-tab-group__chip-item-svg__".concat(e.SLUG)}),e.LABEL,ne[e.SLUG]>0&&r.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-count"},"(",ne[e.SLUG],")"),!!(null==ie?void 0:ie[e.SLUG])&&r.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-new-dot"}))})))),r.createElement("div",{className:"googlesitekit-chip-tab-group__tab-item"},Object.keys(re).map((function(e){var t,n=re[e].group,a=null==ie||null===(t=ie[n])||void 0===t?void 0:t.includes(e);return r.createElement(D.a,o()({key:e,slug:e,savedItemSlugs:i,isNewlyDetected:a},re[e]))})),!Object.keys(re).length&&r.createElement("div",{className:"googlesitekit-chip-tab-group__graphic"},r.createElement(N.a,{height:250}),r.createElement("p",null,Object(v.__)("No metrics were selected yet","google-site-kit")))))}}).call(this,n(28),n(3))},38:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={NEW:"new",SUCCESS:"success",WARNING:"warning",INFO:"info",ERROR:"error"}},383:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Chip}));var r,i=n(5),a=n.n(i),o=n(0),c=n.n(o),s=n(10),l=n.n(s),u=n(11),g=n(25),d=n(220),f=n(97),p=n(99),m=(r={},a()(r,g.c.SLUG,d.a),a()(r,g.g.SLUG,f.a),r);function Chip(t){var n=t.slug,r=t.label,i=t.isActive,a=t.onClick,o=t.hasNewBadge,c=void 0!==o&&o,s=t.selectedCount,g=void 0===s?0:s,d=m[n]||p.a;return e.createElement(u.Button,{className:l()("googlesitekit-chip-tab-group__chip-item",{"googlesitekit-chip-tab-group__chip-item--active":i}),icon:e.createElement(d,{width:12,height:12,className:"googlesitekit-chip-tab-group__chip-item-svg googlesitekit-chip-tab-group__chip-item-svg__".concat(n)}),trailingIcon:g>0?e.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-count"},"(",g,")"):null,onClick:function(){return a(n)}},r,c&&e.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-new-dot"}))}Chip.propTypes={slug:c.a.string.isRequired,label:c.a.string.isRequired,isActive:c.a.bool,hasNewBadge:c.a.bool,selectedCount:c.a.number,onClick:c.a.func.isRequired}}).call(this,n(3))},384:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricItem}));var r=n(5),i=n.n(r),a=n(27),o=n.n(a),c=n(0),s=n.n(c),l=n(1),u=n(2),g=n(4),d=n(30),f=n(49),p=n(19),m=n(25),b=n(126);function MetricItem(t){var n=t.slug,r=t.title,a=t.description,c=t.isNewlyDetected,s=t.savedItemSlugs,v=void 0===s?[]:s,h=Object(g.useSelect)((function(e){var t=e(p.a).getModule,r=e(f.a).getWidget(n);return null==r?void 0:r.modules.reduce((function(e,n){var r=t(n);return(null==r?void 0:r.connected)||!(null==r?void 0:r.name)?e:[].concat(o()(e),[r.name])}),[])})),y=Object(g.useSelect)((function(e){return e(d.a).getValue(m.j,m.i)})),O=Object(g.useSelect)((function(e){return e(d.a)})).getValue,j=Object(g.useDispatch)(d.a).setValues,I=Object(l.useCallback)((function(e){var t,r=O(m.j,m.i),a=e.target.checked?r.concat([n]):r.filter((function(e){return e!==n}));j(m.j,(t={},i()(t,m.i,a),i()(t,m.o,a),t))}),[O,j,n]),k=null==y?void 0:y.includes(n),E=!v.includes(n)&&h.length>0,S="key-metric-selection-checkbox-".concat(n);return e.createElement(b.c,{id:S,slug:n,title:r,description:a,isNewlyDetected:c,isItemSelected:k,isItemDisabled:E,onCheckboxChange:I},h.length>0&&e.createElement("div",{className:"googlesitekit-selection-panel-item-error"},Object(u.sprintf)(/* translators: %s: module names. */
Object(u._n)("%s is disconnected, no data to show","%s are disconnected, no data to show",h.length,"google-site-kit"),h.join(Object(u.__)(" and ","google-site-kit")))))}MetricItem.propTypes={slug:s.a.string.isRequired,title:s.a.string.isRequired,description:s.a.string.isRequired,isNewlyDetected:s.a.bool,savedItemSlugs:s.a.array}}).call(this,n(3))},385:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M59.238 58.571c-2.136 20.178 4.272 29.099 20.48 53.216 16.209 24.118-29.092 62.914 5.475 101.268 33.827 37.532 69.419.009 111.314-4.555 29.443-3.208 57.819 12.98 90.86 5.9 33.04-7.08 46.385-42.599 43.153-68.059-5.59-44.041-26.24-49.107-34.893-66.461-8.654-17.354 2.902-52.997-30.287-73.16-33.19-20.163-76.71 14.42-112.503 12.37-20.651-1.182-40.932-4.995-59.264.86-18.53 5.918-32.662 22.571-34.335 38.621z",fill:"#B8E6CA"}),o=r.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter0_d_2200_11981)"},r.createElement("rect",{x:242.455,y:45.266,width:130.621,height:89.651,rx:10.957,transform:"rotate(15 242.455 45.266)",fill:"#fff"})),c=r.createElement("rect",{x:253.726,y:64.785,width:24.903,height:7.969,rx:3.985,transform:"rotate(15 253.726 64.785)",fill:"#EBEEF0"}),s=r.createElement("rect",{x:249.342,y:81.144,width:49.806,height:19.923,rx:9.961,transform:"rotate(15 249.342 81.144)",fill:"#FFDED3"}),l=r.createElement("rect",{x:240.436,y:114.357,width:99.428,height:8.773,rx:3.985,transform:"rotate(15 240.436 114.357)",fill:"#EBEEF0"}),u=r.createElement("path",{d:"M256.195 90.198l4.644 8.044m0 0l1.412-4.986m-1.412 4.986l-5.023-1.27",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),g=r.createElement("rect",{x:268.706,y:93.551,width:19.923,height:5.977,rx:1.992,transform:"rotate(15 268.706 93.55)",fill:"#fff"}),d=r.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter1_d_2200_11981)"},r.createElement("rect",{x:13.887,y:79.094,width:130.621,height:89.68,rx:10.957,transform:"rotate(-15 13.887 79.094)",fill:"#fff"})),f=r.createElement("rect",{x:32.989,y:90.122,width:62.386,height:7.798,rx:3.899,transform:"rotate(-15 32.99 90.122)",fill:"#EBEEF0"}),p=r.createElement("rect",{x:37.691,y:106.902,width:49.806,height:19.923,rx:9.961,transform:"rotate(-15 37.691 106.902)",fill:"#FFDED3"}),m=r.createElement("rect",{x:46.612,y:140.967,width:99.428,height:7.798,rx:3.899,transform:"rotate(-15 46.612 140.967)",fill:"#EBEEF0"}),b=r.createElement("path",{d:"M48.152 111.318l8.044 4.645m0 0l-1.27-5.024m1.27 5.024l-4.986 1.411",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),v=r.createElement("rect",{x:60.663,y:107.966,width:19.923,height:5.977,rx:1.992,transform:"rotate(-15 60.663 107.966)",fill:"#fff"}),h=r.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter2_d_2200_11981)"},r.createElement("rect",{x:126.251,y:37.4,width:130.621,height:89.68,rx:10.957,fill:"#fff"})),y=r.createElement("rect",{x:143.013,y:53.134,width:98.333,height:7.867,rx:3.933,fill:"#EBEEF0"}),O=r.createElement("rect",{x:142.369,y:70.423,width:49.806,height:19.923,rx:9.961,fill:"#B8E6CA"}),j=r.createElement("rect",{x:143.013,y:105.84,width:33.04,height:7.867,rx:3.933,fill:"#EBEEF0"}),I=r.createElement("path",{d:"M151.336 84.036l6.568-6.567m0 0l-5.182-.073m5.182.073l.073 5.18",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),k=r.createElement("rect",{x:164.287,y:77.395,width:19.923,height:5.977,rx:1.992,fill:"#fff"}),E=r.createElement("path",{d:"M59.237 58.571C57.1 78.75 63.509 87.67 79.717 111.787c16.209 24.118-29.091 62.914 5.475 101.268 33.827 37.532 69.419.009 111.314-4.555 29.444-3.208 57.82 12.98 90.86 5.9s46.385-42.599 43.153-68.059c-5.59-44.041-26.24-49.107-34.893-66.461-8.654-17.354 2.902-52.997-30.287-73.16-33.19-20.163-76.71 14.42-112.503 12.37-20.651-1.182-40.932-4.995-59.264.86C75.042 25.867 60.91 42.52 59.237 58.57z",fill:"#B8E6CA"}),S=r.createElement("g",{mask:"url(#key-metrics-no-selected-items_svg__a)"},r.createElement("path",{d:"M227.674 108.973l11.312-8.418M218.925 98.852l2.868-12.68M205.623 102.87l-5.375-13.037",stroke:"#CBD0D3",strokeWidth:3.147,strokeMiterlimit:10}),r.createElement("path",{d:"M63.953 190.487c16.127 12.193 38.716 10.349 55.335 5.162 16.618-5.187 31.107-14.61 45.314-23.791 6.717-4.337 13.617-8.738 21.496-11.119 7.878-2.381 17.057-2.39 22.958 1.658 3.392 2.328 5.205 5.923 5.36 9.702",stroke:"#3C7251",strokeWidth:9.44,strokeLinejoin:"round"}),r.createElement("path",{d:"M215.831 109.67l-19.169 71.73",stroke:"#CBD0D3",strokeWidth:9.44,strokeMiterlimit:10,strokeLinecap:"round"}),r.createElement("path",{d:"M213.975 116.472l-19.169 71.731",stroke:"#161B18",strokeWidth:9.44,strokeMiterlimit:10})),_=r.createElement("defs",null,r.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter0_d_2200_11981",x:205.773,y:35.772,width:176.33,height:147.36,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.985}),r.createElement("feGaussianBlur",{stdDeviation:7.969}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})),r.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter1_d_2200_11981",x:.409,y:35.793,width:176.337,height:147.388,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.985}),r.createElement("feGaussianBlur",{stdDeviation:7.969}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})),r.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter2_d_2200_11981",x:110.313,y:25.447,width:162.497,height:121.556,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.985}),r.createElement("feGaussianBlur",{stdDeviation:7.969}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})));t.a=function SvgKeyMetricsNoSelectedItems(e){return r.createElement("svg",i({viewBox:"0 0 383 238",fill:"none"},e),a,o,c,s,l,u,g,d,f,p,m,b,v,h,y,O,j,I,k,r.createElement("mask",{id:"key-metrics-no-selected-items_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:58,y:0,width:273,height:230},E),S,_)}},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(24),i=n(18);function a(){var e=Object(i.a)();return r.g.includes(e)}},392:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return GoogleChart}));var i=n(5),a=n.n(i),o=n(27),c=n.n(o),s=n(20),l=n.n(s),u=n(14),g=n.n(u),d=n(22),f=n.n(d),p=(n(601),n(10)),m=n.n(p),b=n(12),v=n.n(b),h=n(0),y=n.n(h),O=n(451),j=n(84),I=n(212),k=n(1),E=n(48),S=n(7),_=n(122),M=n(4),D=n(531),N=n(532),w=n(26),A=n(18),T=n(182),C=n(270),R=n(9),P=n(23);function x(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function L(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?x(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):x(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function GoogleChart(t){var n=t.chartEvents,i=t.chartType,a=t.children,o=t.className,s=t.data,u=t.dateMarkers,d=t.getChartWrapper,p=t.height,b=t.loaded,h=t.loadingHeight,y=t.loadingWidth,x=t.onMouseOver,L=t.onMouseOut,G=t.onReady,z=t.onSelect,Z=t.selectedStats,W=t.width,B=t.options,U=t.gatheringData,V=f()(t,["chartEvents","chartType","children","className","data","dateMarkers","getChartWrapper","height","loaded","loadingHeight","loadingWidth","onMouseOver","onMouseOut","onReady","onSelect","selectedStats","width","options","gatheringData"]),F=Object(I.a)(GoogleChart),H=Object(P.e)(),Y=Object(M.useSelect)((function(e){return e(S.a).getDateRangeDates({offsetDays:0})})),Q=Y.startDate,J=Y.endDate,X=Object(A.a)(),q=Object(M.useSelect)((function(e){return e(w.b).getValue("googleChartsCollisionError")})),K=Object(k.useState)(!1),$=g()(K,2),ee=$[0],te=$[1],ne=Object(M.useDispatch)(w.b).setValue,re=Object(C.d)(s,Z),ie="PieChart"===i?"circular":"square",ae=Object(C.e)(h,p,y,W),oe=e.createElement("div",{className:"googlesitekit-chart-loading"},e.createElement(E.a,l()({className:"googlesitekit-chart-loading__wrapper",shape:ie},ae))),ce=Object(k.useRef)(),se=Object(k.useRef)();Object(j.a)((function(){var e,t,n,i;void 0===q&&(Object(T.a)(X)&&(null===(e=r)||void 0===e||null===(t=e.google)||void 0===t?void 0:t.charts)&&(r.google.charts=void 0),!Object(T.a)(X)&&(null===(n=r)||void 0===n||null===(i=n.google)||void 0===i?void 0:i.charts)?ne("googleChartsCollisionError",!0):ne("googleChartsCollisionError",!1))})),Object(k.useEffect)((function(){return function(){if(se.current&&ce.current){var e=se.current.visualization.events;e.removeAllListeners(ce.current.getChart()),e.removeAllListeners(ce.current)}}}),[]),Object(k.useLayoutEffect)((function(){var e,t;x&&(null===(e=se.current)||void 0===e||e.visualization.events.addListener(ce.current.getChart(),"onmouseover",(function(e){x(e,{chartWrapper:ce.current,google:se.current})})));L&&(null===(t=se.current)||void 0===t||t.visualization.events.addListener(ce.current.getChart(),"onmouseout",(function(e){L(e,{chartWrapper:ce.current,google:se.current})})))}),[x,L]);var le=u.filter((function(e){return!!((t=new Date(e.date))&&Q&&J)&&!(t.getTime()<Object(R.G)(Q).getTime()||t.getTime()>Object(R.G)(J).getTime());var t}));if(q)return null;if(!b)return e.createElement("div",{className:m()("googlesitekit-chart","googlesitekit-chart-loading__forced",o)},oe);var ue=Object(C.b)([].concat(c()(n||[]),[{eventName:"ready",callback:function(){var e;if(ce.current&&le.length){var t=ce.current.getChart(),n=null==t?void 0:t.getChartLayoutInterface(),r=null==n?void 0:n.getChartAreaBoundingBox(),i=ce.current.getDataTable();if(n&&r&&i){le.forEach((function(e,t){var i=new Date(e.date),a=document.getElementById("googlesitekit-chart__date-marker-line--".concat(F,"-").concat(t));v()(a,"#googlesitekit-chart__date-marker-line--".concat(F,"-").concat(t," is missing from the DOM, but required to render date markers."));var o=Math.floor(n.getXLocation(Object(R.G)(Object(R.p)(i))));if(Object.assign(a.style,{left:"".concat(o-1,"px"),top:"".concat(Math.floor(r.top),"px"),height:"".concat(Math.floor(r.height),"px"),opacity:1}),e.text){var c=document.getElementById("googlesitekit-chart__date-marker-tooltip--".concat(F,"-").concat(t));v()(c,"#googlesitekit-chart__date-marker-tooltip--".concat(F,"-").concat(t," is missing from the DOM, but required to render date marker tooltips.")),Object.assign(c.style,{left:"".concat(o-9,"px"),top:"".concat(Math.floor(r.top)-18,"px"),opacity:1})}}));var a=null===(e=document.querySelector("#googlesitekit-chart-".concat(F," svg:first-of-type > g:first-of-type > g > g > text")))||void 0===e?void 0:e.parentElement.parentElement.parentElement;!!a&&document.querySelectorAll("#googlesitekit-chart-".concat(F," svg:first-of-type > g")).length>=3&&(a.style.transform="translateY(-10px)")}}}}]),G,z),ge=Object(C.a)(B,U,i,Q,J,H);return e.createElement(D.a,null,e.createElement("div",{className:m()("googlesitekit-chart","googlesitekit-chart--".concat(i),o),id:"googlesitekit-chart-".concat(F),tabIndex:-1},e.createElement(O.a,l()({className:"googlesitekit-chart__inner",chartEvents:ue,chartLanguage:Object(R.r)(),chartType:i,chartVersion:"49",data:re,loader:oe,height:p,getChartWrapper:function(e,t){var n,r,i;(ee||te(!0),e!==ce.current)&&(null===(n=se.current)||void 0===n||n.visualization.events.removeAllListeners(null===(r=ce.current)||void 0===r?void 0:r.getChart()),null===(i=se.current)||void 0===i||i.visualization.events.removeAllListeners(ce.current));ce.current=e,se.current=t,d&&d(e,t)},width:W,options:ge},V)),U&&ee&&e.createElement(_.b,{style:_.a.OVERLAY}),!!le.length&&le.map((function(t,n){return e.createElement(N.a,{key:"googlesitekit-chart__date-marker--".concat(F,"-").concat(n),id:"".concat(F,"-").concat(n),text:t.text})})),a))}GoogleChart.propTypes={className:y.a.string,children:y.a.node,chartEvents:y.a.arrayOf(y.a.shape({eventName:y.a.string,callback:y.a.func})),chartType:y.a.oneOf(["LineChart","PieChart"]).isRequired,data:y.a.array,dateMarkers:y.a.arrayOf(y.a.shape({date:y.a.string.isRequired,text:y.a.string})),getChartWrapper:y.a.func,height:y.a.string,loaded:y.a.bool,loadingHeight:y.a.string,loadingWidth:y.a.string,onMouseOut:y.a.func,onMouseOver:y.a.func,onReady:y.a.func,onSelect:y.a.func,selectedStats:y.a.arrayOf(y.a.number),width:y.a.string,options:y.a.object,gatheringData:y.a.bool},GoogleChart.defaultProps=L(L({},O.a.defaultProps),{},{dateMarkers:[],gatheringData:!1,loaded:!0})}).call(this,n(3),n(28))},4:function(e,t){e.exports=googlesitekit.data},40:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return O})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return y}));var r=n(108),i=e._googlesitekitTrackingData||{},a=i.activeModules,o=void 0===a?[]:a,c=i.isSiteKitScreen,s=i.trackingEnabled,l=i.trackingID,u=i.referenceSiteURL,g=i.userIDHash,d=i.isAuthenticated,f={activeModules:o,trackingEnabled:s,trackingID:l,referenceSiteURL:u,userIDHash:g,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:d,pluginVersion:"1.157.0"},p=Object(r.a)(f),m=p.enableTracking,b=p.disableTracking,v=(p.isTrackingEnabled,p.initializeSnippet),h=p.trackEvent,y=p.trackEventOnce;function O(e){e?m():b()}c&&s&&v()}).call(this,n(28))},400:function(e,t,n){"use strict";t.a=function(e){if("string"==typeof e&&e.match(/[0-9]{8}/)){var t=e.slice(0,4),n=Number(e.slice(4,6))-1,r=e.slice(6,8);return new Date(t,n.toString(),r)}return!1}},405:function(e,t,n){"use strict";t.a="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTA4IiBoZWlnaHQ9IjI2NyIgdmlld0JveD0iMCAwIDUwOCAyNjciIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMF8xNDYxXzEwNTc2KSI+CjxwYXRoIGQ9Ik01MzMuOTg5IDIxNi4yNDNDNTA4LjI4MSAyNjkuOTc4IDQzOS41ODcgMjcwLjU1NiA0MDEuNDQyIDI2Mi4zNzFDMzUxLjY3OCAyNTEuNjkzIDMxOC42NDcgMjEyLjYzNCAyNzUuMiAyMTAuMTZDMjMxLjc1NCAyMDcuNjg1IDIwNy41MzIgMjMwLjI4OCAxNjAuNzE0IDIzNS45MzdDMTEzLjg5NSAyNDEuNTg1IDU2LjQ3NDcgMjM2LjQ1IDI2LjY5MjIgMTc4LjQyNUMtMy4wOTAyOCAxMjAuNDAxIDE3LjQ0OTMgNTguOTc2MyA3MC4zMzkgMjguMTkyM0MxMjMuMjI5IC0yLjU5MTc4IDE1NS4zMTcgMjguMzQ4MyAyMDcuNTMyIDI4LjE5MjNDMjU5Ljc0NyAyOC4wMzYzIDI4MC44NTQgLTE4LjU5MDkgMzI5LjQzMSAtMjIuMzcwMUMzNTMuNTQ0IC0yNC4yNDYgMzc3Ljg0MiAtMTkuMjg2IDQwMS45NjEgLTIuNTkxNzdDNDI1LjgxNiAxMy45MTk2IDQyOS4zMTIgNDQuMTM4OSA0ODguNjk5IDcyLjI1NTZDNTQ4LjA4NiAxMDAuMzcyIDU1OS42OTcgMTYyLjUwOCA1MzMuOTg5IDIxNi4yNDNaIiBmaWxsPSIjQjhFNkNBIi8+CjxnIGZpbHRlcj0idXJsKCNmaWx0ZXIwX2RfMTQ2MV8xMDU3NikiPgo8cmVjdCB4PSIyNjciIHk9IjU4Ljg3NiIgd2lkdGg9IjEzNi4xMjIiIGhlaWdodD0iNzQuMjQ4NCIgcng9IjkuMDc0OCIgZmlsbD0id2hpdGUiLz4KPC9nPgo8cmVjdCB4PSIyODAuMiIgeT0iNzIuMDc1MSIgd2lkdGg9IjIwLjYyNDYiIGhlaWdodD0iNi41OTk4NiIgcng9IjMuMjk5OTMiIGZpbGw9IiNFQkVFRjAiLz4KPHJlY3QgeD0iMjgwLjIiIHk9Ijg2LjEwMDMiIHdpZHRoPSI0MS4yNDkxIiBoZWlnaHQ9IjE2LjQ5OTYiIHJ4PSI4LjI0OTgyIiBmaWxsPSIjRkZERUQzIi8+CjxyZWN0IHg9IjI4MC4yIiB5PSIxMTQuOTc0IiB3aWR0aD0iMTA5LjcyMyIgaGVpZ2h0PSI2LjU5OTg2IiByeD0iMy4yOTk5MyIgZmlsbD0iI0VCRUVGMCIvPgo8cGF0aCBkPSJNMjg3LjYyMiA5MS44NzUyTDI5My4wNjIgOTcuMzE0N00yOTMuMDYyIDk3LjMxNDdMMjkzLjEyMiA5My4wMjM2TTI5My4wNjIgOTcuMzE0N0wyODguNzcxIDk3LjM3NTEiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMS4yMzc0NyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxyZWN0IHg9IjI5OC4zNSIgeT0iOTEuODc1MiIgd2lkdGg9IjE2LjQ5OTYiIGhlaWdodD0iNC45NDk4OSIgcng9IjEuNjQ5OTYiIGZpbGw9IndoaXRlIi8+CjxnIGZpbHRlcj0idXJsKCNmaWx0ZXIxX2RfMTQ2MV8xMDU3NikiPgo8cmVjdCB4PSIxMTciIHk9IjE0NSIgd2lkdGg9IjEzNi4xMjIiIGhlaWdodD0iNzQuMjQ4NCIgcng9IjkuMDc0OCIgZmlsbD0id2hpdGUiLz4KPC9nPgo8cmVjdCB4PSIxMzAiIHk9IjE1OCIgd2lkdGg9IjU0IiBoZWlnaHQ9IjciIHJ4PSIzLjI5OTkzIiBmaWxsPSIjRUJFRUYwIi8+CjxyZWN0IHg9IjEzMC4yIiB5PSIxNzIuMjI0IiB3aWR0aD0iNDEuMjQ5MSIgaGVpZ2h0PSIxNi40OTk2IiByeD0iOC4yNDk4MiIgZmlsbD0iI0ZGREVEMyIvPgo8cmVjdCB4PSIxMzAuMiIgeT0iMjAxLjA5OCIgd2lkdGg9IjEwOS43MjMiIGhlaWdodD0iNi41OTk4NiIgcng9IjMuMjk5OTMiIGZpbGw9IiNFQkVFRjAiLz4KPHBhdGggZD0iTTEzNy42MjIgMTc3Ljk5OUwxNDMuMDYyIDE4My40MzlNMTQzLjA2MiAxODMuNDM5TDE0My4xMjIgMTc5LjE0OE0xNDMuMDYyIDE4My40MzlMMTM4Ljc3MSAxODMuNDk5IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuMjM3NDciIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cmVjdCB4PSIxNDguMzUiIHk9IjE3Ny45OTkiIHdpZHRoPSIxNi40OTk2IiBoZWlnaHQ9IjQuOTQ5ODkiIHJ4PSIxLjY0OTk2IiBmaWxsPSJ3aGl0ZSIvPgo8ZyBmaWx0ZXI9InVybCgjZmlsdGVyMl9kXzE0NjFfMTA1NzYpIj4KPHJlY3QgeD0iMTE3IiB5PSI1OC44NzYiIHdpZHRoPSIxMzYuMTIyIiBoZWlnaHQ9Ijc0LjI0ODQiIHJ4PSI5LjA3NDgiIGZpbGw9IndoaXRlIi8+CjwvZz4KPHJlY3QgeD0iMTMwLjE5OSIgeT0iNzIuMDc1MSIgd2lkdGg9Ijg2LjYyMzEiIGhlaWdodD0iNi41OTk4NiIgcng9IjMuMjk5OTMiIGZpbGw9IiNFQkVFRjAiLz4KPHJlY3QgeD0iMTMwLjE5OSIgeT0iODYuMTAwMyIgd2lkdGg9IjQxLjI0OTEiIGhlaWdodD0iMTYuNDk5NiIgcng9IjguMjQ5ODIiIGZpbGw9IiNCOEU2Q0EiLz4KPHJlY3QgeD0iMTMwLjE5OSIgeT0iMTE0Ljk3NCIgd2lkdGg9IjEwOS43MjMiIGhlaWdodD0iNi41OTk4NiIgcng9IjMuMjk5OTMiIGZpbGw9IiNFQkVFRjAiLz4KPHBhdGggZD0iTTEzNy42MjUgOTcuMzc1MUwxNDMuMDY0IDkxLjkzNTdNMTQzLjA2NCA5MS45MzU3TDEzOC43NzMgOTEuODc1Mk0xNDMuMDY0IDkxLjkzNTdMMTQzLjEyNSA5Ni4yMjY4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuMjM3NDciIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cmVjdCB4PSIxNDguMzUiIHk9IjkxLjg3NTIiIHdpZHRoPSIxNi40OTk2IiBoZWlnaHQ9IjQuOTQ5ODkiIHJ4PSIxLjY0OTk2IiBmaWxsPSJ3aGl0ZSIvPgo8ZyBmaWx0ZXI9InVybCgjZmlsdGVyM19kXzE0NjFfMTA1NzYpIj4KPHJlY3QgeD0iMjY3IiB5PSIxNDUiIHdpZHRoPSIxMzYuMTIyIiBoZWlnaHQ9Ijc0LjI0ODQiIHJ4PSI5LjA3NDgiIGZpbGw9IndoaXRlIi8+CjwvZz4KPHJlY3QgeD0iMjgwLjE5OSIgeT0iMTU4LjE5OSIgd2lkdGg9IjUyLjc5ODkiIGhlaWdodD0iNi41OTk4NiIgcng9IjMuMjk5OTMiIGZpbGw9IiNFQkVFRjAiLz4KPHBhdGggZD0iTTI4MC4xOTkgMTgwLjQ3NEMyODAuMTk5IDE3NS45MTggMjgzLjg5MyAxNzIuMjI0IDI4OC40NDkgMTcyLjIyNEgzMTMuMTk5QzMxNy43NTUgMTcyLjIyNCAzMjEuNDQ4IDE3NS45MTggMzIxLjQ0OCAxODAuNDc0QzMyMS40NDggMTg1LjAzIDMxNy43NTUgMTg4LjcyNCAzMTMuMTk5IDE4OC43MjRIMjg4LjQ0OUMyODMuODkzIDE4OC43MjQgMjgwLjE5OSAxODUuMDMgMjgwLjE5OSAxODAuNDc0WiIgZmlsbD0iI0I4RTZDQSIvPgo8cmVjdCB4PSIyODAuMTk5IiB5PSIyMDEuMDk4IiB3aWR0aD0iMTA5LjcyMyIgaGVpZ2h0PSI2LjU5OTg2IiByeD0iMy4yOTk5MyIgZmlsbD0iI0VCRUVGMCIvPgo8cGF0aCBkPSJNMjg3LjYyNCAxODMuNDk5TDI5My4wNjMgMTc4LjA2TTI5My4wNjMgMTc4LjA2TDI4OC43NzIgMTc3Ljk5OU0yOTMuMDYzIDE3OC4wNkwyOTMuMTI0IDE4Mi4zNTEiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMS4yMzc0NyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxyZWN0IHg9IjI5OC4zNSIgeT0iMTc3Ljk5OSIgd2lkdGg9IjE2LjQ5OTYiIGhlaWdodD0iNC45NDk4OSIgcng9IjEuNjQ5OTYiIGZpbGw9IndoaXRlIi8+CjwvZz4KPGRlZnM+CjxmaWx0ZXIgaWQ9ImZpbHRlcjBfZF8xNDYxXzEwNTc2IiB4PSIyNTMuOCIgeT0iNDguOTc2MiIgd2lkdGg9IjE2Mi41MjIiIGhlaWdodD0iMTAwLjY0OCIgZmlsdGVyVW5pdHM9InVzZXJTcGFjZU9uVXNlIiBjb2xvci1pbnRlcnBvbGF0aW9uLWZpbHRlcnM9InNSR0IiPgo8ZmVGbG9vZCBmbG9vZC1vcGFjaXR5PSIwIiByZXN1bHQ9IkJhY2tncm91bmRJbWFnZUZpeCIvPgo8ZmVDb2xvck1hdHJpeCBpbj0iU291cmNlQWxwaGEiIHR5cGU9Im1hdHJpeCIgdmFsdWVzPSIwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAxMjcgMCIgcmVzdWx0PSJoYXJkQWxwaGEiLz4KPGZlT2Zmc2V0IGR5PSIzLjI5OTkzIi8+CjxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjYuNTk5ODYiLz4KPGZlQ29tcG9zaXRlIGluMj0iaGFyZEFscGhhIiBvcGVyYXRvcj0ib3V0Ii8+CjxmZUNvbG9yTWF0cml4IHR5cGU9Im1hdHJpeCIgdmFsdWVzPSIwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwLjE1IDAiLz4KPGZlQmxlbmQgbW9kZT0ibm9ybWFsIiBpbjI9IkJhY2tncm91bmRJbWFnZUZpeCIgcmVzdWx0PSJlZmZlY3QxX2Ryb3BTaGFkb3dfMTQ2MV8xMDU3NiIvPgo8ZmVCbGVuZCBtb2RlPSJub3JtYWwiIGluPSJTb3VyY2VHcmFwaGljIiBpbjI9ImVmZmVjdDFfZHJvcFNoYWRvd18xNDYxXzEwNTc2IiByZXN1bHQ9InNoYXBlIi8+CjwvZmlsdGVyPgo8ZmlsdGVyIGlkPSJmaWx0ZXIxX2RfMTQ2MV8xMDU3NiIgeD0iMTAzLjgiIHk9IjEzNS4xIiB3aWR0aD0iMTYyLjUyMiIgaGVpZ2h0PSIxMDAuNjQ4IiBmaWx0ZXJVbml0cz0idXNlclNwYWNlT25Vc2UiIGNvbG9yLWludGVycG9sYXRpb24tZmlsdGVycz0ic1JHQiI+CjxmZUZsb29kIGZsb29kLW9wYWNpdHk9IjAiIHJlc3VsdD0iQmFja2dyb3VuZEltYWdlRml4Ii8+CjxmZUNvbG9yTWF0cml4IGluPSJTb3VyY2VBbHBoYSIgdHlwZT0ibWF0cml4IiB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDEyNyAwIiByZXN1bHQ9ImhhcmRBbHBoYSIvPgo8ZmVPZmZzZXQgZHk9IjMuMjk5OTMiLz4KPGZlR2F1c3NpYW5CbHVyIHN0ZERldmlhdGlvbj0iNi41OTk4NiIvPgo8ZmVDb21wb3NpdGUgaW4yPSJoYXJkQWxwaGEiIG9wZXJhdG9yPSJvdXQiLz4KPGZlQ29sb3JNYXRyaXggdHlwZT0ibWF0cml4IiB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAuMTUgMCIvPgo8ZmVCbGVuZCBtb2RlPSJub3JtYWwiIGluMj0iQmFja2dyb3VuZEltYWdlRml4IiByZXN1bHQ9ImVmZmVjdDFfZHJvcFNoYWRvd18xNDYxXzEwNTc2Ii8+CjxmZUJsZW5kIG1vZGU9Im5vcm1hbCIgaW49IlNvdXJjZUdyYXBoaWMiIGluMj0iZWZmZWN0MV9kcm9wU2hhZG93XzE0NjFfMTA1NzYiIHJlc3VsdD0ic2hhcGUiLz4KPC9maWx0ZXI+CjxmaWx0ZXIgaWQ9ImZpbHRlcjJfZF8xNDYxXzEwNTc2IiB4PSIxMDMuOCIgeT0iNDguOTc2MiIgd2lkdGg9IjE2Mi41MjIiIGhlaWdodD0iMTAwLjY0OCIgZmlsdGVyVW5pdHM9InVzZXJTcGFjZU9uVXNlIiBjb2xvci1pbnRlcnBvbGF0aW9uLWZpbHRlcnM9InNSR0IiPgo8ZmVGbG9vZCBmbG9vZC1vcGFjaXR5PSIwIiByZXN1bHQ9IkJhY2tncm91bmRJbWFnZUZpeCIvPgo8ZmVDb2xvck1hdHJpeCBpbj0iU291cmNlQWxwaGEiIHR5cGU9Im1hdHJpeCIgdmFsdWVzPSIwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAxMjcgMCIgcmVzdWx0PSJoYXJkQWxwaGEiLz4KPGZlT2Zmc2V0IGR5PSIzLjI5OTkzIi8+CjxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjYuNTk5ODYiLz4KPGZlQ29tcG9zaXRlIGluMj0iaGFyZEFscGhhIiBvcGVyYXRvcj0ib3V0Ii8+CjxmZUNvbG9yTWF0cml4IHR5cGU9Im1hdHJpeCIgdmFsdWVzPSIwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwLjE1IDAiLz4KPGZlQmxlbmQgbW9kZT0ibm9ybWFsIiBpbjI9IkJhY2tncm91bmRJbWFnZUZpeCIgcmVzdWx0PSJlZmZlY3QxX2Ryb3BTaGFkb3dfMTQ2MV8xMDU3NiIvPgo8ZmVCbGVuZCBtb2RlPSJub3JtYWwiIGluPSJTb3VyY2VHcmFwaGljIiBpbjI9ImVmZmVjdDFfZHJvcFNoYWRvd18xNDYxXzEwNTc2IiByZXN1bHQ9InNoYXBlIi8+CjwvZmlsdGVyPgo8ZmlsdGVyIGlkPSJmaWx0ZXIzX2RfMTQ2MV8xMDU3NiIgeD0iMjUzLjgiIHk9IjEzNS4xIiB3aWR0aD0iMTYyLjUyMiIgaGVpZ2h0PSIxMDAuNjQ4IiBmaWx0ZXJVbml0cz0idXNlclNwYWNlT25Vc2UiIGNvbG9yLWludGVycG9sYXRpb24tZmlsdGVycz0ic1JHQiI+CjxmZUZsb29kIGZsb29kLW9wYWNpdHk9IjAiIHJlc3VsdD0iQmFja2dyb3VuZEltYWdlRml4Ii8+CjxmZUNvbG9yTWF0cml4IGluPSJTb3VyY2VBbHBoYSIgdHlwZT0ibWF0cml4IiB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDEyNyAwIiByZXN1bHQ9ImhhcmRBbHBoYSIvPgo8ZmVPZmZzZXQgZHk9IjMuMjk5OTMiLz4KPGZlR2F1c3NpYW5CbHVyIHN0ZERldmlhdGlvbj0iNi41OTk4NiIvPgo8ZmVDb21wb3NpdGUgaW4yPSJoYXJkQWxwaGEiIG9wZXJhdG9yPSJvdXQiLz4KPGZlQ29sb3JNYXRyaXggdHlwZT0ibWF0cml4IiB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAuMTUgMCIvPgo8ZmVCbGVuZCBtb2RlPSJub3JtYWwiIGluMj0iQmFja2dyb3VuZEltYWdlRml4IiByZXN1bHQ9ImVmZmVjdDFfZHJvcFNoYWRvd18xNDYxXzEwNTc2Ii8+CjxmZUJsZW5kIG1vZGU9Im5vcm1hbCIgaW49IlNvdXJjZUdyYXBoaWMiIGluMj0iZWZmZWN0MV9kcm9wU2hhZG93XzE0NjFfMTA1NzYiIHJlc3VsdD0ic2hhcGUiLz4KPC9maWx0ZXI+CjxjbGlwUGF0aCBpZD0iY2xpcDBfMTQ2MV8xMDU3NiI+CjxyZWN0IHdpZHRoPSI1MDgiIGhlaWdodD0iMjY3IiBmaWxsPSJ3aGl0ZSIvPgo8L2NsaXBQYXRoPgo8L2RlZnM+Cjwvc3ZnPgo="},406:function(e,t,n){"use strict";t.a="data:image/svg+xml;base64,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"},407:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileHeader}));var r=n(0),i=n.n(r),a=n(137),o=n(116);function MetricTileHeader(t){var n=t.title,r=t.infoTooltip,i=t.loading;return e.createElement("div",{className:"googlesitekit-km-widget-tile__title-container"},e.createElement("h3",{className:"googlesitekit-km-widget-tile__title"},n),i?e.createElement(o.a,null,e.createElement(a.a,{title:r})):e.createElement(a.a,{title:r}))}MetricTileHeader.propTypes={title:i.a.string,infoTooltip:i.a.oneOfType([i.a.string,i.a.element]),loading:i.a.bool}}).call(this,n(3))},412:function(e,t,n){"use strict";n.d(t,"e",(function(){return a})),n.d(t,"c",(function(){return l})),n.d(t,"b",(function(){return d})),n.d(t,"d",(function(){return f})),n.d(t,"a",(function(){return m}));var r=n(15),i=n(9);function a(e){if(void 0!==e)return!Array.isArray(e)||!e.length||!e.some((function(e){return e.clicks>0||e.ctr>0||e.impressions>0||e.position>0}))}var o=n(10),c=n.n(o),s=n(2),l=function(e,t,n,r,a){var o=[[{type:"date",label:Object(s.__)("Day","google-site-kit")},{type:"string",role:"tooltip",p:{html:!0}},{type:"number",label:n},{type:"number",label:Object(s.__)("Previous period","google-site-kit")}]],l=Object(i.r)(),u={weekday:"short",month:"short",day:"numeric"};return e.forEach((function(e,g){var d,f,p=e[r],m=e.keys[0],b=(null===(d=t[g])||void 0===d?void 0:d[r])||0,v=(null===(f=t[g])||void 0===f?void 0:f.keys[0])||Object(i.s)(m,a),h=Object(s.sprintf)(/* translators: 1: date for user stats, 2: previous date for user stats comparison */
Object(s._x)("%1$s vs %2$s","Date range for chart tooltip","google-site-kit"),Object(i.G)(m).toLocaleDateString(l,u),Object(i.G)(v).toLocaleDateString(l,u)),y=Object(i.g)(b,p),O=Object(i.h)(p,b),j=Object(i.o)(O),I=Object(s.sprintf)(/* translators: 1: selected stat label, 2: numeric value of selected stat, 3: up or down arrow , 4: different change in percentage, %%: percent symbol */
Object(s._x)("%1$s: <strong>%2$s</strong> <em>%3$s %4$s%%</em>","Stat information for chart tooltip","google-site-kit"),n,Math.abs(p).toFixed(2).replace(/(.00|0)$/,""),j,Object(i.B)(y));o.push([Object(i.G)(m),'<div class="'.concat(c()("googlesitekit-visualization-tooltip",{"googlesitekit-visualization-tooltip--up":O>0,"googlesitekit-visualization-tooltip--down":O<0}),'">\n\t\t\t\t<p>').concat(h,"</p>\n\t\t\t\t<p>").concat(I,"</p>\n\t\t\t</div>"),p,b])})),o},u=n(12),g=n.n(u),d=function(e){var t=e.startDate,n=e.endDate;return g()(Object(i.w)(t),"A valid startDate is required."),g()(Object(i.w)(n),"A valid endDate is required."),{start_date:t.replace(/-/g,""),end_date:n.replace(/-/g,"")}};function f(e){return"string"==typeof e&&e.length>0}function p(e){var t=[[{type:"string",label:"Day"},{type:"number",label:"Clicks"},{type:"number",label:"Impressions"},{type:"number",label:"CTR"},{type:"number",label:"Position"}]],n=0,a=0,o=0,c=0,s=e.length;return Object(r.each)(e,(function(e){var s=Object(i.G)(e.keys[0]);t.push([s.getMonth()+1+"/"+s.getUTCDate(),e.clicks,e.impressions,Object(r.round)(e.ctr,3),Object(r.round)(e.position,3)]),n+=e.clicks,a+=e.impressions,o+=e.ctr,c+=e.position})),{dataMap:t,totalClicks:n,totalImpressions:a,averageCTR:s>0?o/s:0,averagePosition:s>0?c/s:0}}var m=function(e,t){var n=Object(i.D)(e,{dateRangeLength:t}),r=n.compareRange,a=p(n.currentRange),o=p(r);return{dataMap:a.dataMap,totalClicks:a.totalClicks,totalImpressions:a.totalImpressions,averageCTR:a.averageCTR,averagePosition:a.averagePosition,totalClicksChange:Object(i.g)(o.totalClicks,a.totalClicks),totalImpressionsChange:Object(i.g)(o.totalImpressions,a.totalImpressions),averageCTRChange:Object(i.g)(o.averageCTR,a.averageCTR),averagePositionChange:Object(i.g)(o.averagePosition,a.averagePosition)}}},419:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(4),i=n(19),a=n(7),o=n(25),c=n(8),s=n(29),l=n(70),u=n(86);function g(){return Object(r.useSelect)((function(e){var t=e(a.a).isItemDismissed(o.l),n=e(a.a).isDismissingItem(o.l),r=d(e,u.a,l.b),i=d(e,s.g,c.r);return!1===t&&!1===n&&r&&i}),[])}function d(e,t,n){if(e(i.a).isModuleConnected(t)){var r=e(n),a=r.isGatheringData,o=r.isDataAvailableOnLoad;return a(),o()}}},420:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n(1),i=n(4),a=n(7),o=n(19),c=n(33);function s(e){var t=Object(i.useSelect)((function(e){return e(a.a).hasCapability(a.K)})),n=Object(i.useSelect)((function(t){return t(o.a).getModuleStoreName(e)})),s=Object(i.useSelect)((function(e){var t;return null===(t=e(n))||void 0===t?void 0:t.getAdminReauthURL()})),l=Object(i.useDispatch)(c.a).navigateTo,u=Object(r.useCallback)((function(){return l(s)}),[s,l]);return s&&t?u:null}},421:function(e,t){e.exports=googlesitekit.widgets},423:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Content}));var r=n(0),i=n.n(r),a=n(1),o=n(9),c=n(424),s=n(79),l=n(425),u=n(143);function Content(t){var n=t.title,r=void 0===n?"":n,i=t.datapoint,g=void 0===i?null:i,d=t.datapointUnit,f=void 0===d?"":d,p=t.change,m=void 0===p?null:p,b=t.changeDataUnit,v=void 0===b?"":b,h=t.period,y=void 0===h?"":h,O=t.source,j=t.sparkline,I=t.invertChangeColor,k=void 0!==I&&I,E=t.gatheringData,S=void 0!==E&&E,_=t.badge,M=void 0===g?g:Object(o.B)(g,f);return e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-data-block__title-datapoint-wrapper"},e.createElement("h3",{className:" googlesitekit-subheading-1 googlesitekit-data-block__title "},!0===_?e.createElement(s.a,{"aria-hidden":"true",className:"googlesitekit-badge--hidden",label:"X"}):_,e.createElement("span",{className:"googlesitekit-data-block__title-inner"},r)),!S&&e.createElement("div",{className:"googlesitekit-data-block__datapoint"},e.createElement("span",{className:"googlesitekit-data-block__datapoint--resize"},M))),!S&&j&&e.createElement(c.a,{sparkline:j,invertChangeColor:k}),!S&&e.createElement("div",{className:"googlesitekit-data-block__change-source-wrapper"},e.createElement(l.a,{change:m,changeDataUnit:v,period:y,invertChangeColor:k}),O&&e.createElement(u.a,{className:"googlesitekit-data-block__source",name:O.name,href:O.link,external:null==O?void 0:O.external})))}Content.propTypes={title:i.a.string,datapoint:i.a.oneOfType([i.a.string,i.a.number]),datapointUnit:i.a.string,change:i.a.oneOfType([i.a.string,i.a.number]),changeDataUnit:i.a.oneOfType([i.a.string,i.a.bool]),period:i.a.string,source:i.a.object,sparkline:i.a.element,invertChangeColor:i.a.bool,gatheringData:i.a.bool,badge:i.a.oneOfType([i.a.bool,i.a.node])}}).call(this,n(3))},424:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(1);function Sparkline(t){var n=t.sparkline,r=t.invertChangeColor,i=n;return i&&r&&(i=Object(a.cloneElement)(n,{invertChangeColor:r})),e.createElement("div",{className:"googlesitekit-data-block__sparkline"},i)}Sparkline.propTypes={sparkline:i.a.element,invertChangeColor:i.a.bool},t.a=Sparkline}).call(this,n(3))},425:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(2),s=n(9),l=n(89);function Change(t){var n=t.change,r=t.changeDataUnit,i=t.period,a=t.invertChangeColor,u=n;return r&&(u="%"===r?Object(s.B)(n,{style:"percent",signDisplay:"never",maximumFractionDigits:1}):Object(s.B)(n,r)),i&&(u=Object(c.sprintf)(i,u)),e.createElement("div",{className:o()("googlesitekit-data-block__change",{"googlesitekit-data-block__change--no-change":!n})},!!n&&e.createElement("span",{className:"googlesitekit-data-block__arrow"},e.createElement(l.a,{direction:0<parseFloat(n)?"up":"down",invertColor:a})),e.createElement("span",{className:"googlesitekit-data-block__value"},u))}Change.propTypes={change:i.a.oneOfType([i.a.string,i.a.number]),changeDataUnit:i.a.oneOfType([i.a.string,i.a.bool]),period:i.a.string,invertChangeColor:i.a.bool},t.a=Change}).call(this,n(3))},43:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},438:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PreviewGraph}));var r=n(0),i=n.n(r),a=n(586);function PreviewGraph(t){var n=t.title,r=t.GraphSVG,i=t.showIcons;return e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graph"},e.createElement("h3",{className:"googlesitekit-analytics-cta__preview-graph--title"},n),e.createElement("div",null,e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graph--icons"},e.createElement(a.a,{className:"googlesitekit-analytics-cta__preview-graph--up-arrow"}),e.createElement("span",{className:"googlesitekit-analytics-cta__preview-graph--bar"})))}PreviewGraph.propTypes={title:i.a.string.isRequired,GraphSVG:i.a.elementType.isRequired,showIcons:i.a.bool},PreviewGraph.defaultProps={showIcons:!0}}).call(this,n(3))},450:function(e,t,n){"use strict";n.d(t,"c",(function(){return r.a})),n.d(t,"d",(function(){return ZeroDataMessage})),n.d(t,"a",(function(){return l.a})),n.d(t,"b",(function(){return u.a}));var r=n(923),i=n(0),a=n.n(i),o=n(2),c=n(4),s=n(13);function ZeroDataMessage(e){var t=e.skipPrefix,n=Object(c.useSelect)((function(e){return e(s.c).getCurrentEntityURL()}));return t?n?Object(o.__)("Your page hasn’t appeared in Search yet","google-site-kit"):Object(o.__)("Your site hasn’t appeared in Search yet","google-site-kit"):n?Object(o.__)("No data to display: your page hasn’t appeared in Search yet","google-site-kit"):Object(o.__)("No data to display: your site hasn’t appeared in Search yet","google-site-kit")}ZeroDataMessage.propTypes={skipPrefix:a.a.bool};var l=n(924),u=n(925)},459:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeMetricsLink}));var r=n(1),i=n(2),a=n(4),o=n(26),c=n(7),s=n(25),l=n(21),u=n(323),g=n(474),d=n(9),f=n(18),p=n(533);function ChangeMetricsLink(){var t=Object(a.useSelect)((function(e){return e(c.a).getKeyMetrics()})),n=Object(f.a)(),m=Object(a.useDispatch)(o.b).setValue,b=Object(r.useCallback)((function(){m(s.k,!0),Object(d.I)("".concat(n,"_kmw"),"change_metrics")}),[m,n]),v=Array.isArray(t)&&(null==t?void 0:t.length)>0;return Object(p.a)(v),v?e.createElement(r.Fragment,null,e.createElement(l.a,{className:"googlesitekit-widget-area__cta-link googlesitekit-km-change-metrics-cta",onClick:b,leadingIcon:e.createElement(u.a,{width:22,height:22}),secondary:!0,linkButton:!0},Object(i.__)("Change metrics","google-site-kit")),e.createElement(g.a,null)):null}}).call(this,n(3))},46:function(e,t){e.exports=googlesitekit.api},460:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InsufficientPermissionsError}));var r=n(0),i=n.n(r),a=n(1),o=n(42),c=n(2),s=n(4),l=n(13),u=n(21),g=n(317),d=n(9),f=n(18);function InsufficientPermissionsError(t){var n=t.moduleSlug,r=t.onRetry,i=t.infoTooltip,p=t.headerText,m=Object(f.a)(),b=Object(s.useSelect)((function(e){return e(l.c).getErrorTroubleshootingLinkURL({code:"".concat(n,"_insufficient_permissions")})}));Object(a.useEffect)((function(){Object(d.J)("".concat(m,"_kmw"),"insufficient_permissions_error")}),[m]);var v=Object(a.useCallback)((function(){Object(d.I)("".concat(m,"_kmw"),"insufficient_permissions_error_retry"),null==r||r()}),[r,m]);return e.createElement(g.a,{title:Object(c.__)("Insufficient permissions","google-site-kit"),headerText:p,infoTooltip:i},e.createElement("div",{className:"googlesitekit-report-error-actions"},e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(o.a)(Object(c.__)("Permissions updated? <a>Retry</a>","google-site-kit"),{a:e.createElement(u.a,{onClick:v})})),e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(o.a)(Object(c.__)("You’ll need to contact your administrator. <a>Learn more</a>","google-site-kit"),{a:e.createElement(u.a,{href:b,external:!0,hideExternalIndicator:!0})}))))}InsufficientPermissionsError.propTypes={moduleSlug:i.a.string.isRequired,onRetry:i.a.func.isRequired,headerText:i.a.string,infoTooltip:i.a.string}}).call(this,n(3))},461:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(14),s=n.n(c),l=n(0),u=n.n(l),g=n(222),d=n(2),f=n(1),p=n(4),m=n(7),b=n(13),v=n(33),h=n(25),y=n(29),O=n(60),j=n(203),I=n(9),k=n(18),E=n(419),S=n(184),_=n(21),M=n(405),D=n(406);function KeyMetricsSetupCTAWidget(t){var n=t.Widget,r=t.WidgetNull,a=Object(f.useRef)(),c=Object(k.a)(),l="".concat(c,"_kmw-cta-notification"),u=Object(E.a)(),y=Object(p.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-user-input")})),O=Object(p.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-metric-selection")})),N=Object(g.a)(a,{threshold:.25}),w=Object(f.useState)(!1),A=s()(w,2),T=A[0],C=A[1],R=!!(null==N?void 0:N.intersectionRatio),P=Object(p.useDispatch)(m.a).triggerSurvey,x=Object(p.useSelect)((function(e){return e(b.c).isUsingProxy()}));Object(f.useEffect)((function(){R&&!T&&(Object(I.I)("".concat(c,"_kmw-cta-notification"),"view_notification"),x&&P("view_kmw_setup_cta",{ttl:I.f}),C(!0))}),[R,T,c,x,P]);var L={tooltipSlug:h.l,title:Object(d.__)("You can always set up goals in Settings later","google-site-kit"),content:Object(d.__)("The Key Metrics section will be added back to your dashboard once you set your goals in Settings","google-site-kit"),dismissLabel:Object(d.__)("Got it","google-site-kit")},G=Object(j.b)(L),z=Object(p.useDispatch)(m.a).dismissItem,Z=Object(f.useCallback)(o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(I.I)(l,"dismiss_notification");case 2:return G(),e.next=5,z(h.l);case 5:case"end":return e.stop()}}),e)}))),[l,G,z]),W=Object(p.useDispatch)(v.a).navigateTo,B=Object(f.useCallback)(o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(I.I)(l,"confirm_pick_own_metrics");case 2:W(O);case 3:case"end":return e.stop()}}),e)}))),[l,W,O]),U=Object(f.useCallback)((function(){Object(I.I)(l,"confirm_get_tailored_metrics")}),[l]);return u?e.createElement(n,{noPadding:!0},e.createElement(S.a,{ref:a,className:"googlesitekit-banner--setup-cta",title:Object(d.__)("Get personalized suggestions for user interaction metrics based on your goals","google-site-kit"),description:Object(d.__)("Answer 3 questions and we’ll suggest relevant metrics for your dashboard. These metrics will help you track how users interact with your site.","google-site-kit"),dismissButton:{label:Object(d.__)("Maybe later","google-site-kit"),onClick:Z},ctaButton:{label:Object(d.__)("Get tailored metrics","google-site-kit"),href:y,onClick:U},svg:{desktop:M.a,mobile:D.a,verticalPosition:"top"},footer:e.createElement("div",{className:"googlesitekit-widget-key-metrics-footer"},e.createElement("span",null,Object(d.__)("Interested in specific metrics?","google-site-kit")),e.createElement(_.a,{onClick:B},Object(d.__)("Select your own metrics","google-site-kit")))})):e.createElement(r,null)}KeyMetricsSetupCTAWidget.propTypes={Widget:u.a.elementType.isRequired,WidgetNull:u.a.elementType},t.a=Object(O.a)({moduleName:y.g})(KeyMetricsSetupCTAWidget)}).call(this,n(3))},462:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileNumeric}));var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),s=n.n(c),l=n(9),u=n(193),g=n(208);function MetricTileNumeric(t){var n=t.metricValue,r=t.metricValueFormat,a=t.subText,c=t.previousValue,s=t.currentValue,d=o()(t,["metricValue","metricValueFormat","subText","previousValue","currentValue"]),f=Object(l.m)(r);return e.createElement(g.a,i()({className:"googlesitekit-km-widget-tile--numeric"},d),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-container"},e.createElement("div",{className:"googlesitekit-km-widget-tile__metric"},Object(l.B)(n,f)),e.createElement("p",{className:"googlesitekit-km-widget-tile__subtext"},a)),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-change-container"},e.createElement(u.a,{previousValue:c,currentValue:s,isAbsolute:"percent"===(null==f?void 0:f.style)})))}MetricTileNumeric.propTypes={metricValue:s.a.oneOfType([s.a.string,s.a.number]),metricValueFormat:s.a.oneOfType([s.a.string,s.a.object]),subtext:s.a.string,previousValue:s.a.number,currentValue:s.a.number}}).call(this,n(3))},463:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileTable}));var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),s=n.n(c),l=n(15),u=n(10),g=n.n(u),d=n(208);function MetricTileTable(t){var n=t.rows,r=void 0===n?[]:n,a=t.columns,c=void 0===a?[]:a,s=t.limit,u=t.ZeroState,f=o()(t,["rows","columns","limit","ZeroState"]),p=null;return(null==r?void 0:r.length)>0?p=r.slice(0,s||r.length).map((function(t,n){return e.createElement("div",{key:n,className:"googlesitekit-table__body-row"},c.map((function(n,r){var i=n.Component,a=n.field,o=n.className,c=void 0!==a?Object(l.get)(t,a):void 0;return e.createElement("div",{key:r,className:g()("googlesitekit-table__body-item",o)},i&&e.createElement(i,{row:t,fieldValue:c}),!i&&c)})))})):u&&(p=e.createElement("div",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("div",{className:"googlesitekit-table__body-zero-data"},e.createElement(u,null)))),e.createElement(d.a,i()({className:"googlesitekit-km-widget-tile--table"},f),e.createElement("div",{className:"googlesitekit-km-widget-tile__table"},p))}MetricTileTable.propTypes={rows:s.a.array,columns:s.a.array,limit:s.a.number,ZeroState:s.a.elementType}}).call(this,n(3))},464:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileTablePlainText}));var r=n(0),i=n.n(r);function MetricTileTablePlainText(t){var n=t.content;return e.createElement("p",{className:"googlesitekit-km-widget-tile__table-plain-text"},n)}MetricTileTablePlainText.propTypes={content:i.a.string.isRequired}}).call(this,n(3))},472:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var r=n(27),i=n.n(r),a=n(10),o=n.n(a),c=n(15),s=n(2),l=n(87),u=n(9),g=n(14),d=n.n(g),f=n(12),p=n.n(f);function m(e,t){var n=t.dateRangeLength;p()(Array.isArray(e),"report must be an array to partition."),p()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=function(t){return e.filter((function(e){return d()(e.dimensionValues,2)[1].value===t}))},i=-1*n;return{currentRange:r("date_range_0").slice(i),compareRange:r("date_range_1").slice(2*i,i)}}var b=n(400);function v(e,t){var n=[];return e.forEach((function(e){if(e.metricValues){var r=e.metricValues[t].value,i=e.dimensionValues[0].value,a=Object(b.a)(i);n.push([a,r])}})),n}function h(e,t,n,r){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[Object(s.__)("Users","google-site-kit"),Object(s.__)("Sessions","google-site-kit"),Object(s.__)("Engagement Rate","google-site-kit"),Object(s.__)("Session Duration","google-site-kit")],g=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[function(e){return parseFloat(e).toLocaleString()},function(e){return parseFloat(e).toLocaleString()},function(e){return Object(u.B)(e/100,{style:"percent",signDisplay:"never",maximumFractionDigits:2})},function(e){return Object(u.B)(e,"s")}],d=arguments.length>6&&void 0!==arguments[6]?arguments[6]:[c.identity,c.identity,function(e){return 100*e},c.identity],f=i()((null==e?void 0:e.rows)||[]),p=f.length;if(2*n>p){for(var b=Object(u.G)(r),h=0;n>h;h++){var y=(b.getMonth()+1).toString(),O=b.getDate().toString(),j=b.getFullYear().toString()+(2>y.length?"0":"")+y+(2>O.length?"0":"")+O;if(h>p){var I=[{dimensionValues:[{value:j},{value:"date_range_0"}],metricValues:[{value:0},{value:0}]},{dimensionValues:[{value:j},{value:"date_range_1"}],metricValues:[{value:0},{value:0}]}];f.unshift.apply(f,I)}b.setDate(b.getDate()-1)}f.push({dimensionValues:[{value:"0"},{value:"date_range_0"}]},{dimensionValues:[{value:"0"},{value:"date_range_1"}]})}var k=a[t]===Object(s.__)("Session Duration","google-site-kit"),E=k?"timeofday":"number",S=[[{type:"date",label:Object(s.__)("Day","google-site-kit")},{type:"string",role:"tooltip",p:{html:!0}},{type:E,label:a[t]},{type:E,label:Object(s.__)("Previous period","google-site-kit")}]],_=m(f,{dateRangeLength:n}),M=_.compareRange,D=_.currentRange,N=v(D,t),w=v(M,t),A=Object(l.b)(),T={weekday:"short",month:"short",day:"numeric"};return N.forEach((function(e,n){if(e[0]&&e[1]&&w[n]){var r=d[t],i=r(e[1]),c=r(w[n][1]),l=parseFloat(c),f=Object(u.h)(i,l),p=Object(u.o)(f),m=Object(s.sprintf)(/* translators: 1: date for user stats, 2: previous date for user stats comparison */
Object(s._x)("%1$s vs %2$s","Date range for chart tooltip","google-site-kit"),e[0].toLocaleDateString(A,T),w[n][0].toLocaleDateString(A,T)),b=Object(s.sprintf)(/* translators: 1: selected stat label, 2: numeric value of selected stat, 3: up or down arrow , 4: different change in percentage */
Object(s._x)("%1$s: <strong>%2$s</strong> <em>%3$s %4$s</em>","Stat information for chart tooltip","google-site-kit"),a[t],g[t](i),p,Object(u.B)(Math.abs(f),"%"));S.push([e[0],'<div class="'.concat(o()("googlesitekit-visualization-tooltip",{"googlesitekit-visualization-tooltip--up":f>0,"googlesitekit-visualization-tooltip--down":f<0}),'">\n\t\t\t\t<p>').concat(m,"</p>\n\t\t\t\t<p>").concat(b,"</p>\n\t\t\t</div>"),k?Object(u.j)(i):i,k?Object(u.j)(c):c])}})),S}},474:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupCompletedSurveyTrigger}));var r=n(1),i=n(4),a=n(13),o=n(7),c=n(9),s=n(258);function SetupCompletedSurveyTrigger(){var t=Object(i.useSelect)((function(e){return e(a.c).isKeyMetricsSetupCompleted()})),n=Object(i.useSelect)((function(e){return e(a.c).getKeyMetricsSetupCompletedBy()})),l=Object(i.useSelect)((function(e){return e(o.a).getID()}));return t?e.createElement(r.Fragment,null,e.createElement(s.a,{triggerID:"view_kmw",ttl:c.f}),n===l&&e.createElement(s.a,{triggerID:"view_kmw_setup_completed",ttl:c.f})):null}}).call(this,n(3))},475:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GetHelpLink}));var r=n(0),i=n.n(r),a=n(42),o=n(2),c=n(21);function GetHelpLink(t){var n=t.linkURL;return Object(a.a)(/* translators: %s: get help text. */
Object(o.__)("Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(c.a,{href:n,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))})}GetHelpLink.propTypes={linkURL:i.a.string.isRequired}}).call(this,n(3))},476:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileLoader}));var r=n(48);function MetricTileLoader(){return e.createElement("div",{className:"googlesitekit-km-widget-tile__loading"},e.createElement(r.a,{className:"googlesitekit-km-widget-tile__loading-header",width:"100%",height:"14px"}),e.createElement(r.a,{className:"googlesitekit-km-widget-tile__loading-body",width:"100%",height:"53px"}))}}).call(this,n(3))},48:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(10),s=n.n(c),l=n(23);function PreviewBlock(t){var n,r,a=t.className,o=t.width,c=t.height,u=t.shape,g=t.padding,d=t.smallWidth,f=t.smallHeight,p=t.tabletWidth,m=t.tabletHeight,b=t.desktopWidth,v=t.desktopHeight,h=Object(l.e)(),y={width:(n={},i()(n,l.b,d),i()(n,l.c,p),i()(n,l.a,b),i()(n,l.d,b),n),height:(r={},i()(r,l.b,f),i()(r,l.c,m),i()(r,l.a,v),i()(r,l.d,b),r)};return e.createElement("div",{className:s()("googlesitekit-preview-block",a,{"googlesitekit-preview-block--padding":g}),style:{width:y.width[h]||o,height:y.height[h]||c}},e.createElement("div",{className:s()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(3))},49:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var r={BOXES:"boxes",COMPOSITE:"composite"},i={QUARTER:"quarter",HALF:"half",FULL:"full"},a="core/widgets"},50:function(e,t,n){"use strict";n.d(t,"a",(function(){return O}));var r=n(6),i=n.n(r),a=n(5),o=n.n(a),c=n(12),s=n.n(c),l=n(15),u=n(68),g=n(93),d=n(9);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m=function(e){return e},b=function(){return{}},v=function(){},h=u.a.clearError,y=u.a.receiveError,O=function(e){var t,n,r=i.a.mark(P),a=e.baseName,c=e.controlCallback,u=e.reducerCallback,f=void 0===u?m:u,O=e.argsToParams,j=void 0===O?b:O,I=e.validateParams,k=void 0===I?v:I;s()(a,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof f,"reducerCallback must be a function."),s()("function"==typeof j,"argsToParams must be a function."),s()("function"==typeof k,"validateParams must be a function.");try{k(j()),n=!1}catch(e){n=!0}var E=Object(g.b)(a),S=Object(g.a)(a),_="FETCH_".concat(S),M="START_".concat(_),D="FINISH_".concat(_),N="CATCH_".concat(_),w="RECEIVE_".concat(S),A="fetch".concat(E),T="receive".concat(E),C="isFetching".concat(E),R=o()({},C,{});function P(e,t){var n,o;return i.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,{payload:{params:e},type:M};case 2:return r.next=4,h(a,t);case 4:return r.prev=4,r.next=7,{payload:{params:e},type:_};case 7:return n=r.sent,r.next=10,x[T](n,e);case 10:return r.next=12,{payload:{params:e},type:D};case 12:r.next=21;break;case 14:return r.prev=14,r.t0=r.catch(4),o=r.t0,r.next=19,y(o,a,t);case 19:return r.next=21,{payload:{params:e},type:N};case 21:return r.abrupt("return",{response:n,error:o});case 22:case"end":return r.stop()}}),r,null,[[4,14]])}var x=(t={},o()(t,A,(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=j.apply(void 0,t);return k(r),P(r,t)})),o()(t,T,(function(e,t){return s()(void 0!==e,"response is required."),n?(s()(Object(l.isPlainObject)(t),"params is required."),k(t)):t={},{payload:{response:e,params:t},type:w}})),t),L=o()({},_,(function(e){var t=e.payload;return c(t.params)})),G=o()({},C,(function(e){if(void 0===e[C])return!1;var t;try{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t=j.apply(void 0,r),k(t)}catch(e){return!1}return!!e[C][Object(d.H)(t)]}));return{initialState:R,actions:x,controls:L,reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case M:var i=r.params;return p(p({},e),{},o()({},C,p(p({},e[C]),{},o()({},Object(d.H)(i),!0))));case w:var a=r.response,c=r.params;return f(e,a,c);case D:var s=r.params;return p(p({},e),{},o()({},C,p(p({},e[C]),{},o()({},Object(d.H)(s),!1))));case N:var l=r.params;return p(p({},e),{},o()({},C,p(p({},e[C]),{},o()({},Object(d.H)(l),!1))));default:return e}},resolvers:{},selectors:G}}},526:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a),c=n(48);function PreviewTable(t){for(var n=t.rows,r=t.rowHeight,i=t.padding,a=[],s=0;n>s;s++)a.push(e.createElement("div",{className:"googlesitekit-preview-table__row",key:"table-row-"+s},e.createElement(c.a,{width:"100%",height:r+"px"})));return e.createElement("div",{className:o()("googlesitekit-preview-table",{"googlesitekit-preview-table--padding":i})},a)}PreviewTable.propTypes={rows:i.a.number,rowHeight:i.a.number,padding:i.a.bool},PreviewTable.defaultProps={rows:11,rowHeight:35,padding:!1},t.a=PreviewTable}).call(this,n(3))},527:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportTable}));var r=n(14),i=n.n(r),a=n(10),o=n.n(a),c=n(12),s=n.n(c),l=n(0),u=n.n(l),g=n(15),d=n(1),f=n(11),p=n(122);function ReportTable(t){var n=t.rows,r=t.columns,a=t.className,c=t.limit,l=t.zeroState,u=t.gatheringData,m=void 0!==u&&u,b=t.tabbedLayout,v=void 0!==b&&b;s()(Array.isArray(n),"rows must be an array."),s()(Array.isArray(r),"columns must be an array."),r.forEach((function(e){var t=e.Component,n=e.field,r=void 0===n?null:n;s()(t||null!==r,"each column must define a Component and/or a field.")})),s()(Number.isInteger(c)||void 0===c,"limit must be an integer, if provided.");var h=r.some((function(e){return!!e.badge})),y=Object(d.useState)(0),O=i()(y,2),j=O[0],I=O[1],k=v&&r.slice(1),E=v?[r[0],k[j]]:r;return e.createElement("div",{className:a},v&&e.createElement(f.TabBar,{className:"googlesitekit-tab-bar--start-aligned-high-contrast",activeIndex:j,handleActiveIndexUpdate:I},k.map((function(t){var n=t.title,r=t.badge;return e.createElement(f.Tab,{key:n,"aria-label":n},n,r)}))),e.createElement("div",{className:o()("googlesitekit-table","googlesitekit-table--with-list",{"googlesitekit-table--gathering-data":m})},e.createElement("table",{className:o()("googlesitekit-table__wrapper","googlesitekit-table__wrapper--".concat(E.length,"-col"),{"googlesitekit-table__wrapper--tabbed-layout":v})},!v&&e.createElement("thead",{className:"googlesitekit-table__head"},h&&e.createElement("tr",{className:"googlesitekit-table__head-badges"},r.map((function(t,n){var r=t.badge,i=t.primary,a=t.className;return e.createElement("th",{className:o()("googlesitekit-table__head-item","googlesitekit-table__head-item--badge",{"googlesitekit-table__head-item--primary":i},a),key:"googlesitekit-table__head-row-badge-".concat(n)},r)}))),e.createElement("tr",{className:"googlesitekit-table__head-row"},r.map((function(t,n){var r=t.title,i=t.description,a=t.primary,c=t.className;return e.createElement("th",{className:o()("googlesitekit-table__head-item",{"googlesitekit-table__head-item--primary":a},c),"data-tooltip":i,key:"googlesitekit-table__head-row-".concat(n)},r)})))),e.createElement("tbody",{className:"googlesitekit-table__body"},m&&e.createElement("tr",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("td",{className:"googlesitekit-table__body-item",colSpan:E.length},e.createElement(p.b,null))),!m&&!(null==n?void 0:n.length)&&l&&e.createElement("tr",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("td",{className:"googlesitekit-table__body-item",colSpan:E.length},e.createElement(l,null))),!m&&n.slice(0,c).map((function(t,n){return e.createElement("tr",{className:"googlesitekit-table__body-row",key:"googlesitekit-table__body-row-".concat(n)},E.map((function(n,r){var i=n.Component,a=n.field,c=n.className,s=void 0!==a?Object(g.get)(t,a):void 0;return e.createElement("td",{key:"googlesitekit-table__body-item-".concat(r),className:o()("googlesitekit-table__body-item",c)},e.createElement("div",{className:"googlesitekit-table__body-item-content"},i&&e.createElement(i,{row:t,fieldValue:s}),!i&&s))})))}))))))}ReportTable.propTypes={rows:u.a.arrayOf(u.a.oneOfType([u.a.array,u.a.object])).isRequired,columns:u.a.arrayOf(u.a.shape({title:u.a.string,description:u.a.string,primary:u.a.bool,className:u.a.string,field:u.a.string,Component:u.a.componentType,badge:u.a.node})).isRequired,className:u.a.string,limit:u.a.number,zeroState:u.a.func,gatheringData:u.a.bool,tabbedLayout:u.a.bool}}).call(this,n(3))},528:function(e,t,n){"use strict";(function(e,r){var i=n(14),a=n.n(i),o=n(0),c=n.n(o),s=n(15),l=n(10),u=n.n(l),g=n(1);function TableOverflowContainer(t){var n=t.children,i=Object(g.useState)(!1),o=a()(i,2),c=o[0],l=o[1],d=Object(g.useRef)();Object(g.useEffect)((function(){f();var t=Object(s.debounce)(f,100);return e.addEventListener("resize",t),function(){return e.removeEventListener("resize",t)}}),[]);var f=function(){if(d.current){var e=d.current,t=e.scrollLeft,n=e.scrollWidth-e.offsetWidth;l(t<n-16&&0<n-16)}};return r.createElement("div",{onScroll:Object(s.debounce)(f,100),className:u()("googlesitekit-table-overflow",{"googlesitekit-table-overflow--gradient":c})},r.createElement("div",{ref:d,className:"googlesitekit-table-overflow__container"},n))}TableOverflowContainer.propTypes={children:c.a.element},t.a=TableOverflowContainer}).call(this,n(28),n(3))},530:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return DataBlockGroup}));var i=n(84),a=n(442),o=n(1),c=n(129);function DataBlockGroup(t){var n=t.className,s=t.children,l=Object(o.useRef)(),u=function(){var t,n=null==l||null===(t=l.current)||void 0===t?void 0:t.querySelectorAll(".googlesitekit-data-block");if(null==n?void 0:n.length){g(n,"");var r=1;if(n.forEach((function(e){var t,n=e.querySelector(".googlesitekit-data-block__datapoint");if(n){var i=null==n||null===(t=n.parentElement)||void 0===t?void 0:t.offsetWidth;if(n.scrollWidth>i){var a=i/n.scrollWidth;a<r&&(r=a)}}})),r<1){var i,a,o=parseInt(null===(i=e)||void 0===i||null===(a=i.getComputedStyle(n[0].querySelector(".googlesitekit-data-block__datapoint")))||void 0===a?void 0:a.fontSize,10),c=Math.floor(o*r),s=Math.max(c,14);g(n,"".concat(s,"px"))}}},g=function(e,t){e.forEach((function(e){var n=null==e?void 0:e.querySelector(".googlesitekit-data-block__datapoint");n&&(n.style.fontSize=t)}))},d=Object(c.a)(u,50);return Object(i.a)((function(){u(),e.addEventListener("resize",d)})),Object(a.a)((function(){return e.removeEventListener("resize",d)})),r.createElement("div",{ref:l,className:n},s)}}).call(this,n(28),n(3))},531:function(e,t,n){"use strict";(function(e,r){var i=n(52),a=n.n(i),o=n(53),c=n.n(o),s=n(244),l=n.n(s),u=n(81),g=n.n(u),d=n(82),f=n.n(d),p=n(55),m=n.n(p),b=n(204),v=n.n(b),h=n(0),y=n.n(h),O=n(1),j=n(2),I=n(102),k=n(181),E=n(65),S=n(9);function _(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=m()(e);if(t){var i=m()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return f()(this,n)}}var M=function(t){g()(GoogleChartErrorHandler,t);var n=_(GoogleChartErrorHandler);function GoogleChartErrorHandler(e){var t;return a()(this,GoogleChartErrorHandler),(t=n.call(this,e)).state={error:null,info:null},t.onErrorClick=t.onErrorClick.bind(l()(t)),t}return c()(GoogleChartErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Google Charts error:",t,n),this.setState({error:t,info:n}),Object(S.I)("google_chart_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"onErrorClick",value:function(){var e=this.state,t=e.error,n=e.info;v()("`".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack,"`"))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,i=t.info;return n?r.createElement("div",{className:"googlesitekit-googlechart-error-handler"},r.createElement(I.a,{description:r.createElement(O.Fragment,null,r.createElement("p",null,Object(j.__)("An error prevented this Google chart from being displayed properly. Report the exact contents of the error on the support forum to find out what caused it.","google-site-kit")),r.createElement(k.a,{message:n.message,componentStack:i.componentStack})),onErrorClick:this.onErrorClick,onClick:this.onErrorClick,title:Object(j.__)("Error in Google Chart","google-site-kit"),error:!0})):e}}]),GoogleChartErrorHandler}(O.Component);M.contextType=E.b,M.propTypes={children:y.a.node.isRequired},t.a=M}).call(this,n(28),n(3))},532:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DateMarker}));var r=n(1),i=n(272),a=n(596),o=n(11),c=n(18),s=n(129),l=n(9);function DateMarker(t){var n=t.id,u=t.text,g=Object(c.a)(),d="".concat(g,"_ga4-data-collection-line");Object(r.useEffect)((function(){Object(l.I)(d,"chart_line_view")}),[d]);var f=Object(r.useCallback)((function(){Object(l.I)(d,"chart_tooltip_view")}),[d]),p=Object(s.a)(f,5e3,{leading:!0,trailing:!1});return e.createElement(r.Fragment,null,e.createElement("div",{id:"googlesitekit-chart__date-marker-line--".concat(n),className:"googlesitekit-chart__date-marker-line"}),u&&e.createElement("div",{id:"googlesitekit-chart__date-marker-tooltip--".concat(n),className:"googlesitekit-chart__date-marker-tooltip"},e.createElement(o.Tooltip,{title:u,onOpen:p},e.createElement("span",null,e.createElement(i.a,{fill:"currentColor",icon:a.a,size:18})))))}}).call(this,n(3))},533:function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var r=n(1),i=n(4),a=n(13),o=n(7),c=n(2),s=n(24),l={slug:"sharedKeyMetrics",contexts:[s.n,s.o,s.l,s.m],gaEventCategory:function(e){return"".concat(e,"_shared_key-metrics")},steps:[{target:".googlesitekit-km-change-metrics-cta",title:Object(c.__)("Personalize your key metrics","google-site-kit"),content:Object(c.__)("Another admin has set up these tailored metrics for your site. Click here to personalize them.","google-site-kit"),placement:"bottom-start"}]},u=function(e){var t=Object(i.useSelect)((function(e){return e(a.c).getKeyMetricsSetupCompletedBy()})),n=Object(i.useSelect)((function(e){return e(o.a).getID()})),c=Object(i.useDispatch)(o.a).triggerOnDemandTour,s=Number.isInteger(t)&&Number.isInteger(n)&&t>0&&n!==t;Object(r.useEffect)((function(){e&&s&&c(l)}),[e,s,c])}},54:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(24),i=n(18),a=r.n,o=r.l;function c(){var e=Object(i.a)();return e===r.n||e===r.o?a:e===r.l||e===r.m?o:null}},542:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r);function WidgetHeaderTitle(t){var n=t.title;return e.createElement("h3",{className:"googlesitekit-subheading-1 googlesitekit-widget__header-title"},n)}WidgetHeaderTitle.propTypes={title:i.a.string.isRequired},t.a=WidgetHeaderTitle}).call(this,n(3))},585:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActivateAnalyticsCTA}));var r=n(14),i=n.n(r),a=n(0),o=n.n(a),c=n(1),s=n(2),l=n(11),u=n(4),g=n(19),d=n(8),f=n(29),p=n(33),m=n(187),b=n(420),v=n(129);function ActivateAnalyticsCTA(t){var n=t.children,r=Object(m.a)(f.g),a=Object(b.a)(f.g),o=Object(u.useSelect)((function(e){return e(g.a).isModuleActive(f.g)})),h=Object(u.useSelect)((function(e){return(0,e(g.a).isModuleAvailable)(f.g)&&!!e(d.r)})),y=Object(c.useState)(!1),O=i()(y,2),j=O[0],I=O[1],k=Object(u.useSelect)((function(e){if(!h)return!1;var t=e(d.r).getAdminReauthURL();return!!t&&e(p.a).isNavigatingTo(t)})),E=Object(u.useSelect)((function(e){return!!h&&e(g.a).isFetchingSetModuleActivation(f.g,!0)})),S=Object(v.a)(I,3e3);Object(c.useEffect)((function(){E||k?I(!0):S(!1)}),[E,k,S]);var _=o?a:r;return h&&_?e.createElement("div",{className:"googlesitekit-analytics-cta"},e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graphs"},n),e.createElement("div",{className:"googlesitekit-analytics-cta__details"},e.createElement("p",{className:"googlesitekit-analytics-cta--description"},Object(s.__)("See how many people visit your site from Search and track how you’re achieving your goals","google-site-kit")),e.createElement(l.SpinnerButton,{onClick:_,isSaving:j},o?Object(s.__)("Complete setup","google-site-kit"):Object(s.__)("Set up Google Analytics","google-site-kit")))):null}ActivateAnalyticsCTA.propTypes={children:o.a.node.isRequired}}).call(this,n(3))},586:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 3.01l.443.387 1.755-1.534v3.344h.628V1.863L4.578 3.4l.446-.39L2.512.811 0 3.009z",fill:"currentColor"});t.a=function SvgArrowUp(e){return r.createElement("svg",i({viewBox:"0 0 6 6",fill:"none"},e),a)}},59:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var r=n(0),i=n.n(r),a=n(1),o=n(2),c=n(115),s=n(4),l=n(34),u=n(35),g=n(9);function ErrorNotice(t){var n,r=t.className,i=t.error,d=t.hasButton,f=void 0!==d&&d,p=t.storeName,m=t.message,b=void 0===m?i.message:m,v=t.noPrefix,h=void 0!==v&&v,y=t.skipRetryMessage,O=t.hideIcon,j=void 0!==O&&O,I=Object(s.useDispatch)(),k=Object(s.useSelect)((function(e){return p?e(p).getSelectorDataForError(i):null})),E=Object(a.useCallback)((function(){I(k.storeName).invalidateResolution(k.name,k.args)}),[I,k]);if(!b||Object(l.f)(i))return null;var S=f&&Object(l.d)(i,k),_=b;f||y||(_=Object(o.sprintf)(/* translators: %s: Error message from Google API. */
Object(o.__)("%s (Please try again.)","google-site-kit"),_)),h||(_=Object(o.sprintf)(/* translators: $%s: Error message */
Object(o.__)("Error: %s","google-site-kit"),_));var M=null==i||null===(n=i.data)||void 0===n?void 0:n.reconnectURL;M&&Object(c.a)(M)&&(_=Object(o.sprintf)(/* translators: 1: Original error message 2: Reconnect URL */
Object(o.__)('%1$s To fix this, <a href="%2$s">redo the plugin setup</a>.',"google-site-kit"),_,M));return e.createElement(u.a,{className:r,type:u.a.TYPES.ERROR,description:e.createElement("span",{dangerouslySetInnerHTML:Object(g.F)(_,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}),ctaButton:S?{label:Object(o.__)("Retry","google-site-kit"),onClick:E}:void 0,hideIcon:j})}ErrorNotice.propTypes={className:i.a.string,error:i.a.shape({message:i.a.string}),hasButton:i.a.bool,storeName:i.a.string,message:i.a.string,noPrefix:i.a.bool,skipRetryMessage:i.a.bool,hideIcon:i.a.bool}}).call(this,n(3))},60:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(4),i=n(19),a=n(93);function o(t){var n=t.moduleName,o=t.FallbackComponent,c=t.IncompleteComponent;return function(t){function WhenActiveComponent(a){var s=Object(r.useSelect)((function(e){return e(i.a).getModule(n)}),[n]);if(!s)return null;var l=o||a.WidgetNull||null;if(!1===s.active)return l&&e.createElement(l,a);if(!1===s.connected){var u=c||l;return u&&e.createElement(u,a)}return e.createElement(t,a)}return WhenActiveComponent.displayName="When".concat(Object(a.c)(n),"Active"),(t.displayName||t.name)&&(WhenActiveComponent.displayName+="(".concat(t.displayName||t.name,")")),WhenActiveComponent}}}).call(this,n(3))},601:function(e,t,n){(function(e){Object.prototype.hasOwnProperty.call(e,"google")||(e.google={})}).call(this,n(28))},61:function(e,t,n){"use strict";(function(e){var r,i;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(r=e)||void 0===r||null===(i=r._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},617:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r=n(9);function i(e){return{quarterCellProps:{smSize:2,mdSize:e?4:2,lgSize:3},halfCellProps:{smSize:4,mdSize:4,lgSize:6},oneThirdCellProps:{smSize:2,mdSize:4,lgSize:4},threeQuartersCellProps:{smSize:4,mdSize:4,lgSize:9},fullCellProps:{smSize:4,mdSize:8,lgSize:12}}}function a(e,t){var n,i,a,o,c,s,l,u,g,d,f,p,m=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return{datapoint:(null==e||null===(n=e.totals)||void 0===n||null===(i=n[0])||void 0===i||null===(a=i.metricValues)||void 0===a||null===(o=a[t])||void 0===o?void 0:o.value)/m,change:Object(r.g)(null==e||null===(c=e.totals)||void 0===c||null===(s=c[1])||void 0===s||null===(l=s.metricValues)||void 0===l||null===(u=l[t])||void 0===u?void 0:u.value,null==e||null===(g=e.totals)||void 0===g||null===(d=g[0])||void 0===d||null===(f=d.metricValues)||void 0===f||null===(p=f[t])||void 0===p?void 0:p.value)}}},62:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(43);function i(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},63:function(e,t,n){"use strict";n.d(t,"a",(function(){return _})),n.d(t,"b",(function(){return M})),n.d(t,"c",(function(){return D})),n.d(t,"d",(function(){return w})),n.d(t,"e",(function(){return A})),n.d(t,"g",(function(){return C})),n.d(t,"f",(function(){return R}));var r,i=n(6),a=n.n(i),o=n(27),c=n.n(o),s=n(5),l=n.n(s),u=n(12),g=n.n(u),d=n(66),f=n.n(d),p=n(15),m=n(125);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.reduce((function(e,t){return v(v({},e),t)}),{}),i=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),a=N(i);return g()(0===a.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(a.join(", "),". Check your data stores for duplicates.")),r},y=h,O=h,j=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=[].concat(t);return"function"!=typeof i[0]&&(r=i.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i.reduce((function(e,n){return n(e,t)}),e)}},I=h,k=h,E=h,S=function(e){return e},_=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=E.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:r,controls:O.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:y.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:j.apply(void 0,[r].concat(c()(t.map((function(e){return e.reducer||S}))))),resolvers:I.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:k.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},M={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:a.a.mark((function e(t){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},D=(r={},l()(r,"GET_REGISTRY",Object(m.a)((function(e){return function(){return e}}))),l()(r,"AWAIT",(function(e){return e.payload.value})),r),N=function(e){for(var t=[],n={},r=0;r<e.length;r++){var i=e[r];n[i]=n[i]>=1?n[i]+1:1,n[i]>1&&t.push(i)}return t},w={actions:M,controls:D,reducer:S},A=function(e){return function(t){return T(e(t))}},T=f()((function(e){return Object(p.mapValues)(e,(function(e,t){return function(){var n=e.apply(void 0,arguments);return g()(void 0!==n,"".concat(t,"(...) is not resolved")),n}}))}));function C(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.negate,r=void 0!==n&&n,i=Object(m.b)((function(t){return function(n){var i=!r,a=!!r;try{for(var o=arguments.length,c=new Array(o>1?o-1:0),s=1;s<o;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,n].concat(c)),i}catch(e){return a}}})),a=Object(m.b)((function(t){return function(n){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];e.apply(void 0,[t,n].concat(i))}}));return{safeSelector:i,dangerousSelector:a}}function R(e,t){return g()("function"==typeof e,"a validator function is required."),g()("function"==typeof t,"an action creator function is required."),g()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},65:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(1),i=Object(r.createContext)(""),a=(i.Consumer,i.Provider);t.b=i},677:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return EntityOwnershipChangeNotice}));var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(2),s=n(4),l=n(19),u=n(7),g=n(9),d=n(35);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function EntityOwnershipChangeNotice(t){var n=t.slug,r=Array.isArray(n)?n:[n],a=Object(s.useSelect)((function(e){var t=e(l.a),n=t.getModuleStoreName,a=t.getSharedRoles;return r.filter((function(e){var t;return!!(null===(t=a(e))||void 0===t?void 0:t.length)})).reduce((function(e,t){var r=n(t);return r?p(p({},e),{},i()({},t,r)):e}),{})})),o=Object(s.useSelect)((function(e){return Object.keys(a).reduce((function(t,n){var r,i,o=a[n],c=null===(r=e(o))||void 0===r?void 0:r.getOwnerID(),s=e(u.a).getID(),l=null===(i=e(o))||void 0===i?void 0:i.haveOwnedSettingsChanged();return l&&c!==s&&(t[n]=l),t}),{})})),f=Object.values(o).some((function(e){return e})),m=Object(s.useSelect)((function(e){return Object.keys(o).reduce((function(t,n){var r=e(l.a).getModule(n);return r&&t.push(r.name),t}),[])}));return f?e.createElement(d.a,{className:"googlesitekit-notice--bottom-margin",type:d.a.TYPES.WARNING,description:Object(c.sprintf)(/* translators: %s: module name. */
Object(c.__)("By clicking confirm changes, you’re granting other users view-only access to data from %s via your Google account. You can always manage this later in the dashboard sharing settings.","google-site-kit"),Object(g.y)(m))}):null}EntityOwnershipChangeNotice.propTypes={slug:o.a.oneOfType([o.a.string,o.a.arrayOf(o.a.string)]).isRequired}}).call(this,n(3))},679:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var r=n(6),i=n.n(r),a=n(5),o=n.n(a),c=n(12),s=n.n(c),l=n(46),u=n(4),g=n(50);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var p=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.storeName,r=t.dataAvailable,a=void 0!==r&&r,c=t.selectDataAvailability;s()("string"==typeof e&&e,"module slug is required."),s()("string"==typeof n&&n,"store name is required."),s()("boolean"==typeof a,"dataAvailable must be a boolean."),s()("function"==typeof c,"selectDataAvailability must be a function.");var d=Object(g.a)({baseName:"saveDataAvailableState",controlCallback:function(){return Object(l.set)("modules",e,"data-available")}}),p={dataAvailableOnLoad:a,gatheringData:void 0},m={receiveIsGatheringData:function(e){return s()("boolean"==typeof e,"gatheringData must be a boolean."),{payload:{gatheringData:e},type:"RECEIVE_GATHERING_DATA"}},receiveIsDataAvailableOnLoad:function(e){return s()("boolean"==typeof e,"dataAvailableOnLoad must be a boolean."),{payload:{dataAvailableOnLoad:e},type:"RECEIVE_DATA_AVAILABLE_ON_LOAD"}}},b=o()({},"WAIT_FOR_DATA_AVAILABILITY_STATE",Object(u.createRegistryControl)((function(e){return function(){var t=function(){return void 0!==e.select(n).selectDataAvailability()};return!!t()||new Promise((function(n){var r=e.subscribe((function(){t()&&(r(),n(!0))}))}))}}))),v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"RECEIVE_GATHERING_DATA":var i=r.gatheringData;return f(f({},e),{},{gatheringData:i});case"RECEIVE_DATA_AVAILABLE_ON_LOAD":var a=r.dataAvailableOnLoad;return f(f({},e),{},{dataAvailableOnLoad:a});default:return e}},h={isGatheringData:i.a.mark((function e(){var t,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,u.commonActions.getRegistry();case 2:if(void 0===(t=e.sent).select(n).isGatheringData()){e.next=5;break}return e.abrupt("return");case 5:if(!t.select(n).isDataAvailableOnLoad()){e.next=10;break}return e.next=9,m.receiveIsGatheringData(!1);case 9:return e.abrupt("return");case 10:return e.next=12,{payload:{},type:"WAIT_FOR_DATA_AVAILABILITY_STATE"};case 12:return r=t.select(n).selectDataAvailability(),e.next=15,m.receiveIsGatheringData(!r);case 15:if(!r){e.next=18;break}return e.next=18,d.actions.fetchSaveDataAvailableState();case 18:case"end":return e.stop()}}),e)}))},y={selectDataAvailability:c,isDataAvailableOnLoad:function(e){return e.dataAvailableOnLoad},isGatheringData:function(e){return e.gatheringData}};return Object(u.combineStores)(d,{actions:m,controls:b,initialState:p,reducer:v,resolvers:h,selectors:y})}},68:function(e,t,n){"use strict";n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return v}));var r=n(5),i=n.n(r),a=n(36),o=n.n(a),c=n(125),s=n(12),l=n.n(s),u=n(103),g=n.n(u),d=n(9);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t){if(t&&Array.isArray(t)){var n=t.map((function(e){return"object"===o()(e)?Object(d.H)(e):e}));return"".concat(e,"::").concat(g()(JSON.stringify(n)))}return e}var b={receiveError:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(e,"error is required."),l()(t,"baseName is required."),l()(n&&Array.isArray(n),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:n}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return l()(e,"baseName is required."),l()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function v(e){l()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"selectorName is required."),t.getError(e,n,r)},getErrorForAction:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"actionName is required."),t.getError(e,n,r)},getError:function(e,t,n){var r=e.errors;return l()(t,"baseName is required."),r[m(t,n)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var n=Object.keys(e.errors).find((function(n){return e.errors[n]===t}));return n?{baseName:n.substring(0,n.indexOf("::")),args:e.errorArgs[n]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(n,r){var i=t(e).getMetaDataForError(r);if(i){var a=i.baseName,o=i.args;if(!!t(e)[a])return{storeName:e,name:a,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:b,controls:{},reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case"RECEIVE_ERROR":var a=r.baseName,o=r.args,c=r.error,s=m(a,o);return p(p({},e),{},{errors:p(p({},e.errors||{}),{},i()({},s,c)),errorArgs:p(p({},e.errorArgs||{}),{},i()({},s,o))});case"CLEAR_ERROR":var l=r.baseName,u=r.args,g=p({},e),d=m(l,u);return g.errors=p({},e.errors||{}),g.errorArgs=p({},e.errorArgs||{}),delete g.errors[d],delete g.errorArgs[d],g;case"CLEAR_ERRORS":var f=r.baseName,b=p({},e);if(f)for(var v in b.errors=p({},e.errors||{}),b.errorArgs=p({},e.errorArgs||{}),b.errors)(v===f||v.startsWith("".concat(f,"::")))&&(delete b.errors[v],delete b.errorArgs[v]);else b.errors={},b.errorArgs={};return b;default:return e}},resolvers:{},selectors:t}}},683:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M1 24.053l5-4.825 4 2.193 5.333-2.193 7.334 6.579 6-1.754 3-4.825 4.666 6.579 3.334-1.754L47.333 28 55 19.228l4.333 2.193 5.334-3.509 2 1.316h6L81.333 3 84 9.579l2.333-1.754L89 13.088l12-5.263",stroke:"#CCC",strokeWidth:2});t.a=function SvgCtaGraphVisitors(e){return r.createElement("svg",i({viewBox:"0 0 102 30",fill:"none"},e),a)}},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return l})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return g})),n.d(t,"L",(function(){return d})),n.d(t,"J",(function(){return f})),n.d(t,"I",(function(){return p})),n.d(t,"N",(function(){return m})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return y})),n.d(t,"l",(function(){return O})),n.d(t,"m",(function(){return j})),n.d(t,"n",(function(){return I})),n.d(t,"o",(function(){return k})),n.d(t,"q",(function(){return E})),n.d(t,"s",(function(){return S})),n.d(t,"r",(function(){return _})),n.d(t,"t",(function(){return M})),n.d(t,"w",(function(){return D})),n.d(t,"u",(function(){return N})),n.d(t,"v",(function(){return w})),n.d(t,"x",(function(){return A})),n.d(t,"y",(function(){return T})),n.d(t,"A",(function(){return C})),n.d(t,"B",(function(){return R})),n.d(t,"C",(function(){return P})),n.d(t,"D",(function(){return x})),n.d(t,"k",(function(){return L})),n.d(t,"F",(function(){return G})),n.d(t,"z",(function(){return z})),n.d(t,"G",(function(){return Z})),n.d(t,"E",(function(){return W})),n.d(t,"i",(function(){return B})),n.d(t,"p",(function(){return U})),n.d(t,"Q",(function(){return V})),n.d(t,"P",(function(){return F}));var r="core/user",i="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",l="googlesitekit_setup",u="googlesitekit_view_dashboard",g="googlesitekit_manage_options",d="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",p="googlesitekit_delegate_module_sharing_management",m="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",y="kmAnalyticsNewVisitors",O="kmAnalyticsPopularAuthors",j="kmAnalyticsPopularContent",I="kmAnalyticsPopularProducts",k="kmAnalyticsReturningVisitors",E="kmAnalyticsTopCities",S="kmAnalyticsTopCitiesDrivingLeads",_="kmAnalyticsTopCitiesDrivingAddToCart",M="kmAnalyticsTopCitiesDrivingPurchases",D="kmAnalyticsTopDeviceDrivingPurchases",N="kmAnalyticsTopConvertingTrafficSource",w="kmAnalyticsTopCountries",A="kmAnalyticsTopPagesDrivingLeads",T="kmAnalyticsTopRecentTrendingPages",C="kmAnalyticsTopTrafficSource",R="kmAnalyticsTopTrafficSourceDrivingAddToCart",P="kmAnalyticsTopTrafficSourceDrivingLeads",x="kmAnalyticsTopTrafficSourceDrivingPurchases",L="kmAnalyticsPagesPerVisit",G="kmAnalyticsVisitLength",z="kmAnalyticsTopReturningVisitorPages",Z="kmSearchConsolePopularKeywords",W="kmAnalyticsVisitsPerVisitor",B="kmAnalyticsMostEngagingPages",U="kmAnalyticsTopCategories",V=[b,v,h,y,O,j,I,k,U,E,S,_,M,D,N,w,T,C,R,L,G,z,W,B,U],F=[].concat(V,[Z])},70:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="modules/search-console",i=1},71:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 1c6.075 0 11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12 5.925 1 12 1zm0 14a1.5 1.5 0 100 3 1.5 1.5 0 000-3zm-1-2h2V6h-2v7z",fill:"currentColor"});t.a=function SvgWarningNotice(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},73:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},75:function(e,t,n){"use strict";var r=n(14),i=n.n(r),a=n(327),o=n(0),c=n.n(o),s=n(1),l=n(157);function Portal(e){var t=e.children,n=e.slug,r=Object(s.useState)(document.createElement("div")),o=i()(r,1)[0];return Object(a.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(l.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},758:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsForm}));var r=n(42),i=n(2),a=n(4),o=n(70),c=n(86),s=n(19),l=n(450),u=n(153),g=n(677),d=n(35);function SettingsForm(t){var n,f=t.hasModuleAccess,p=Object(a.useSelect)((function(e){return e(s.a).getModule(c.a)})),m=(null==p||null===(n=p.owner)||void 0===n?void 0:n.login)?"<strong>".concat(p.owner.login,"</strong>"):Object(i.__)("Another admin","google-site-kit");return e.createElement("div",{className:"googlesitekit-search-console-settings-fields"},e.createElement(u.a,{moduleSlug:"search-console",storeName:o.b}),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(l.c,{hasModuleAccess:f})),!1===f&&e.createElement(d.a,{type:d.a.TYPES.WARNING,description:Object(r.a)(Object(i.sprintf)(/* translators: 1: module owner's name, 2: module name */
Object(i.__)("%1$s configured %2$s and you don’t have access to this Search Console property. Contact them to share access or change the Search Console property.","google-site-kit"),m,null==p?void 0:p.name),{strong:e.createElement("strong",null)})}),f&&e.createElement(g.a,{slug:"search-console"}))}}).call(this,n(3))},76:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(36),i=n.n(r),a=n(88),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===i()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},78:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(0),i=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:i}},n)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,n(3))},786:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M101 35.5H1V0",stroke:"#CCC"}),o=r.createElement("path",{d:"M2 24.685l24.5-7.404L51 25 75.5 8.774 100 2",stroke:"#CCC",strokeWidth:2});t.a=function SvgCtaGraphGoals(e){return r.createElement("svg",i({viewBox:"0 0 101 36",fill:"none"},e),a,o)}},79:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(10),s=n.n(c),l=n(0),u=n.n(l),g=n(1),d=Object(g.forwardRef)((function(t,n){var r=t.label,a=t.className,c=t.hasLeftSpacing,l=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",i()({ref:n},u,{className:s()("googlesitekit-badge",a,{"googlesitekit-badge--has-left-spacing":l})}),r)}));d.displayName="Badge",d.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=d}).call(this,n(3))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"s",(function(){return a})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return g})),n.d(t,"i",(function(){return d})),n.d(t,"k",(function(){return f})),n.d(t,"m",(function(){return p})),n.d(t,"n",(function(){return m})),n.d(t,"h",(function(){return b})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return y})),n.d(t,"u",(function(){return O})),n.d(t,"v",(function(){return j})),n.d(t,"f",(function(){return I})),n.d(t,"l",(function(){return k})),n.d(t,"e",(function(){return E})),n.d(t,"t",(function(){return S})),n.d(t,"c",(function(){return _})),n.d(t,"d",(function(){return M})),n.d(t,"b",(function(){return D}));var r="modules/analytics-4",i="account_create",a="property_create",o="webdatastream_create",c="analyticsSetup",s=10,l=1,u="https://www.googleapis.com/auth/tagmanager.readonly",g="enhanced-measurement-form",d="enhanced-measurement-enabled",f="enhanced-measurement-should-dismiss-activation-banner",p="analyticsAccountCreate",m="analyticsCustomDimensionsCreate",b="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",y="dashboardAllTrafficWidgetDimensionValue",O="dashboardAllTrafficWidgetActiveRowIndex",j="dashboardAllTrafficWidgetLoaded",I={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},k={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},E=[k.CONTACT,k.GENERATE_LEAD,k.SUBMIT_LEAD_FORM],S={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},_="audiencePermissionsSetup",M="audienceTileCustomDimensionCreate",D="audience-selection-panel-expirable-new-badge-"},802:function(e,t,n){"use strict";(function(e){var r=n(2),i=n(7),a=n(542),o=n(4);t.a=function Header(){var t=Object(o.useSelect)((function(e){return e(i.a).getDateRangeNumberOfDays()}));return e.createElement(a.a,{title:Object(r.sprintf)(/* translators: %s: number of days */
Object(r._n)("Search traffic over the last %s day","Search traffic over the last %s days",t,"google-site-kit"),t)})}}).call(this,n(3))},85:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(115);function i(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),i=e.replace(n.origin,"");if(i.length<t)return i;var a=i.length-Math.floor(t)+1;return"…"+i.substr(a)}},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="search-console"},87:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return k})),n.d(t,"d",(function(){return E})),n.d(t,"e",(function(){return _})),n.d(t,"c",(function(){return M})),n.d(t,"b",(function(){return D}));var r=n(14),i=n.n(r),a=n(36),o=n.n(a),c=n(5),s=n.n(c),l=n(22),u=n.n(l),g=n(15),d=n(66),f=n.n(d),p=n(2);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=O(e,t),r=n.formatUnit,i=n.formatDecimal;try{return r()}catch(e){return i()}},h=function(e){var t=y(e),n=t.hours,r=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(i):"".concat(n,":").concat(r,":").concat(i)},y=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=y(e),r=n.hours,i=n.minutes,a=n.seconds;return{hours:r,minutes:i,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=b(b({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?_(a,b(b({},o),{},{unit:"second"})):Object(p.sprintf)(/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?_(a,b(b({},o),{},{unit:"second"})):"",i?_(i,b(b({},o),{},{unit:"minute"})):"",r?_(r,b(b({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(p.sprintf)(
// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(p.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(p.sprintf)(
// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(p.__)("%dm","google-site-kit"),i),o=Object(p.sprintf)(
// translators: %s: number of hours with "h" as the abbreviated unit.
Object(p.__)("%dh","google-site-kit"),r);return Object(p.sprintf)(/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",i?n:"",r?o:"").trim()}}},j=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},I=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(p.sprintf)(
// translators: %s: an abbreviated number in millions.
Object(p.__)("%sM","google-site-kit"),_(j(e),e%10==0?{}:t)):1e4<=e?Object(p.sprintf)(
// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),_(j(e))):1e3<=e?Object(p.sprintf)(
// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),_(j(e),e%10==0?{}:t)):_(e,{signDisplay:"never",maximumFractionDigits:1})};function k(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(g.isPlainObject)(e)&&(t=b({},e)),t}function E(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(g.isFinite)(e)?e:Number(e),Object(g.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=k(t),r=n.style,i=void 0===r?"metric":r;return"metric"===i?I(e):"duration"===i?v(e,n):"durationISO"===i?h(e):_(e,n)}var S=f()(console.warn),_=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?D():n,a=u()(t,["locale"]);try{return new Intl.NumberFormat(r,a).format(e)}catch(t){S("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],l={},g=0,d=Object.entries(a);g<d.length;g++){var f=i()(d[g],2),p=f[0],m=f[1];c[p]&&m===c[p]||(s.includes(p)||(l[p]=m))}try{return new Intl.NumberFormat(r,l).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},M=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?D():n,i=t.style,a=void 0===i?"long":i,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:a,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var l=Object(p.__)(", ","google-site-kit");return e.join(l)},D=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(g.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},88:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(160),i=n.n(r)()(e)}).call(this,n(28))},89:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a);function ChangeArrow(t){var n=t.direction,r=t.invertColor,i=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:i,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(3))},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return i.b})),n.d(t,"J",(function(){return i.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return g.a})),n.d(t,"B",(function(){return g.d})),n.d(t,"C",(function(){return g.e})),n.d(t,"y",(function(){return g.c})),n.d(t,"r",(function(){return g.b})),n.d(t,"z",(function(){return m})),n.d(t,"j",(function(){return b})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return k})),n.d(t,"c",(function(){return E})),n.d(t,"e",(function(){return S})),n.d(t,"b",(function(){return _})),n.d(t,"a",(function(){return M})),n.d(t,"f",(function(){return D})),n.d(t,"n",(function(){return N})),n.d(t,"w",(function(){return w})),n.d(t,"p",(function(){return A})),n.d(t,"G",(function(){return T})),n.d(t,"s",(function(){return C})),n.d(t,"v",(function(){return R})),n.d(t,"k",(function(){return P})),n.d(t,"o",(function(){return x.b})),n.d(t,"h",(function(){return x.a})),n.d(t,"t",(function(){return L.b})),n.d(t,"q",(function(){return L.a})),n.d(t,"A",(function(){return L.c})),n.d(t,"x",(function(){return G})),n.d(t,"u",(function(){return z})),n.d(t,"E",(function(){return B})),n.d(t,"D",(function(){return U.a})),n.d(t,"g",(function(){return V})),n.d(t,"L",(function(){return F})),n.d(t,"l",(function(){return H}));var r=n(15),i=n(40),a=n(76),o=n(36),c=n.n(o),s=n(103),l=n.n(s),u=function(e){return l()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var i=t[r];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),n[r]=i})),n}(e)))};n(104);var g=n(87);function d(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function p(e){return e.replace(/\n/gi,"<br>")}function m(e){for(var t=e,n=0,r=[d,f,p];n<r.length;n++){t=(0,r[n])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(14),y=n.n(h),O=n(12),j=n.n(O),I=n(2),k="Invalid dateString parameter, it must be a string.",E='Invalid date range, it must be a string with the format "last-x-days".',S=60,_=60*S,M=24*_,D=7*M;function N(){var e=function(e){return Object(I.sprintf)(/* translators: %s: number of days */
Object(I._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function w(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function A(e){j()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function T(e){j()(w(e),k);var t=e.split("-"),n=y()(t,3),r=n[0],i=n[1],a=n[2];return new Date(r,i-1,a)}function C(e,t){return A(P(e,t*M))}function R(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function P(e,t){j()(w(e)||Object(r.isDate)(e)&&!isNaN(e),k);var n=w(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var x=n(107),L=n(85);function G(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function z(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var Z=n(27),W=n.n(Z),B=function(e){return Array.isArray(e)?W()(e).sort():e},U=n(92);function V(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var F=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},H=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(14),i=n.n(r),a=n(196),o=n(138),c={},s=void 0===e?null:e,l=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,g=e.initialHeight,d=void 0===g?0:g,f=Object(a.a)("undefined"==typeof document?[u,d]:l,t,n),p=i()(f,2),m=p[0],b=p[1],v=function(){return b(l)};return Object(o.a)(s,"resize",v),Object(o.a)(s,"orientationchange",v),m},g=function(e){return u(e)[0]}}).call(this,n(28))},91:function(e,t,n){"use strict";n.r(t),n.d(t,"AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY",(function(){return r})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY",(function(){return i})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION",(function(){return a})),n.d(t,"AREA_MAIN_DASHBOARD_CONTENT_PRIMARY",(function(){return o})),n.d(t,"AREA_MAIN_DASHBOARD_SPEED_PRIMARY",(function(){return c})),n.d(t,"AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY",(function(){return s})),n.d(t,"AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY",(function(){return l})),n.d(t,"AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY",(function(){return u})),n.d(t,"AREA_ENTITY_DASHBOARD_SPEED_PRIMARY",(function(){return g})),n.d(t,"AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY",(function(){return d}));var r="mainDashboardKeyMetricsPrimary",i="mainDashboardTrafficPrimary",a="mainDashboardTrafficAudienceSegmentation",o="mainDashboardContentPrimary",c="mainDashboardSpeedPrimary",s="mainDashboardMonetizationPrimary",l="entityDashboardTrafficPrimary",u="entityDashboardContentPrimary",g="entityDashboardSpeedPrimary",d="entityDashboardMonetizationPrimary";t.default={AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY:r,AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY:i,AREA_MAIN_DASHBOARD_CONTENT_PRIMARY:o,AREA_MAIN_DASHBOARD_SPEED_PRIMARY:c,AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY:s,AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY:l,AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY:u,AREA_ENTITY_DASHBOARD_SPEED_PRIMARY:g,AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY:d}},92:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(12),i=n.n(r),a=function(e,t){var n=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},922:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsView}));var r=n(2),i=n(4),a=n(70),o=n(192);function SettingsView(){var t=Object(i.useSelect)((function(e){return e(a.b).getPropertyID()}));return e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Connected Property","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(o.b,{value:t})))}}).call(this,n(3))},923:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PropertySelect}));var r=n(1),i=n(2),a=n(11),o=n(4),c=n(70),s=n(40),l=n(18);function PropertySelect(t){var n=t.hasModuleAccess,u=Object(l.a)(),g=Object(o.useSelect)((function(e){return e(c.b).getPropertyID()})),d=Object(o.useSelect)((function(e){return e(c.b).getMatchedProperties()})),f=Object(o.useSelect)((function(e){return e(c.b).hasFinishedResolution("getMatchedProperties")})),p=Object(o.useDispatch)(c.b).setPropertyID,m=Object(r.useCallback)((function(e,t){var n=t.dataset.value;g!==n&&(p(n),Object(s.b)("".concat(u,"_search-console"),"change_property"))}),[g,p,u]);return f?!1===n?e.createElement(a.Select,{className:"googlesitekit-search-console__select-property",label:Object(i.__)("Property","google-site-kit"),value:g,enhanced:!0,outlined:!0,disabled:!0},e.createElement(a.Option,{value:g},g)):e.createElement(a.Select,{className:"googlesitekit-search-console__select-property",label:Object(i.__)("Property","google-site-kit"),value:g,onEnhancedChange:m,enhanced:!0,outlined:!0},(d||[]).map((function(t){var n=t.siteURL;return e.createElement(a.Option,{key:n,value:n},n.startsWith("sc-domain:")?Object(i.sprintf)(/* translators: %s: domain name */
Object(i.__)("%s (domain property)","google-site-kit"),n.replace(/^sc-domain:/,"")):n)}))):e.createElement(a.ProgressBar,{small:!0})}}).call(this,n(3))},924:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActivateAnalyticsCTA}));var r=n(0),i=n.n(r),a=n(2),o=n(683),c=n(786),s=n(585),l=n(438);function ActivateAnalyticsCTA(t){var n=t.title;return e.createElement(s.a,null,e.createElement(l.a,{title:Object(a.__)("Unique visitors from Search","google-site-kit"),GraphSVG:o.a}),e.createElement(l.a,{title:n,GraphSVG:c.a}))}ActivateAnalyticsCTA.propTypes={title:i.a.string.isRequired}}).call(this,n(3))},925:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AnalyticsStats}));var r=n(5),i=n.n(r),a=n(14),o=n.n(a),c=n(545),s=n.n(c),l=n(0),u=n.n(l),g=n(2),d=n(4),f=n(17),p=n(19),m=n(211),b=n(392),v=n(8),h=n(39),y=n(9),O=n(7);function j(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function I(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?j(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function AnalyticsStats(t){var n=t.data,r=t.selectedStats,i=t.dateRangeLength,a=t.dataLabels,c=t.tooltipDataFormats,l=t.chartDataFormats,u=t.statsColor,j=t.gatheringData,k=t.moduleSlug,E=Object(h.a)(),S=Object(d.useSelect)((function(e){return e(p.a).isModuleConnected(k)})),_=Object(d.useSelect)((function(e){return e(p.a).isModuleActive(k)})),M=Object(d.useSelect)((function(e){return e(O.a).getReferenceDate()})),D=Object(d.useSelect)((function(e){return E?null:e(v.r).getPropertyCreateTime()})),N=[];if(D&&(N=[{date:Object(y.p)(new Date(D)),text:Object(g.__)("Google Analytics property created","google-site-kit")}]),!_||!S)return null;var w=Object(m.b)(n,r,i,M,a,c,l),A=w.slice(1).map((function(e){return o()(e,1)[0]})),T=s()(A).slice(1),C=I(I({},AnalyticsStats.chartOptions),{},{hAxis:I(I({},AnalyticsStats.chartOptions.hAxis),{},{ticks:T}),vAxis:I({},AnalyticsStats.chartOptions.vAxis),series:{0:{color:u,targetAxisIndex:0},1:{color:u,targetAxisIndex:0,lineDashStyle:[3,3],lineWidth:1}}});if(!w.slice(1).some((function(e){return e[2]>0||e[3]>0}))){var R={0:1,1:100}[r];C.vAxis.viewWindow.max=R}else C.vAxis.viewWindow.max=void 0;return e.createElement(f.e,{className:"googlesitekit-analytics-site-stats"},e.createElement(f.k,null,e.createElement(f.a,{size:12},e.createElement(b.a,{chartType:"LineChart",data:w,dateMarkers:N,loadingHeight:"270px",loadingWidth:"100%",options:C,gatheringData:j}))))}AnalyticsStats.propTypes={data:u.a.oneOfType([u.a.arrayOf(u.a.object),u.a.object]).isRequired,dateRangeLength:u.a.number.isRequired,selectedStats:u.a.number.isRequired,dataLabels:u.a.arrayOf(u.a.string).isRequired,tooltipDataFormats:u.a.arrayOf(u.a.func).isRequired,statsColor:u.a.string.isRequired,gatheringData:u.a.bool,moduleSlug:u.a.string.isRequired},AnalyticsStats.chartOptions={chart:{title:""},curveType:"function",height:270,width:"100%",chartArea:{height:"80%",left:60,right:25},legend:{position:"top",textStyle:{color:"#616161",fontSize:12}},hAxis:{format:"MMM d",gridlines:{color:"#fff"},textStyle:{color:"#616161",fontSize:12}},vAxis:{gridlines:{color:"#eee"},minorGridlines:{color:"#eee"},textStyle:{color:"#616161",fontSize:12},titleTextStyle:{color:"#616161",fontSize:12,italic:!1},viewWindow:{min:0}},focusTarget:"category",crosshair:{color:"gray",opacity:.1,orientation:"vertical",trigger:"both"},tooltip:{isHtml:!0,trigger:"both"}}}).call(this,n(3))},926:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsEdit}));var r=n(4),i=n(11),a=n(70),o=n(86),c=n(19),s=n(758);function SettingsEdit(){Object(r.useSelect)((function(e){return e(a.b).getMatchedProperties()}));var t,n=Object(r.useSelect)((function(e){return e(a.b).isDoingSubmitChanges()})),l=Object(r.useSelect)((function(e){return e(a.b).hasFinishedResolution("getMatchedProperties")})),u=Object(r.useSelect)((function(e){return e(c.a).hasModuleOwnershipOrAccess(o.a)}));return t=n||!l||void 0===u?e.createElement(i.ProgressBar,null):e.createElement(s.a,{hasModuleAccess:u}),e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--search-console"},t)}}).call(this,n(3))},93:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return a}));var r=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},i=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function a(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},97:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},99:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))}},[[1302,1,0]]]);