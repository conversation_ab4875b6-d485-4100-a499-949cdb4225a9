(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[23],{104:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return l}));var r,i=n(52),a=n.n(i),o=n(53),c=n.n(o),s=function(t){var n=e[t];if(!n)return!1;try{var r="__storage_test__";return n.setItem(r,r),n.removeItem(r),!0}catch(e){return e instanceof DOMException&&(22===e.code||1014===e.code||"QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name)&&0!==n.length}},u=function(){function NullStorage(){a()(this,NullStorage)}return c()(NullStorage,[{key:"key",value:function(){return null}},{key:"getItem",value:function(){return null}},{key:"setItem",value:function(){}},{key:"removeItem",value:function(){}},{key:"clear",value:function(){}},{key:"length",get:function(){return 0}}]),NullStorage}(),l=function(){return r||(r=s("sessionStorage")?e.sessionStorage:s("localStorage")?e.localStorage:new u),r}}).call(this,n(28))},105:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTAButton}));var r=n(0),i=n.n(r),a=n(11),o=n(73);function CTAButton(t){var n,r=t.label,i=t.disabled,c=t.inProgress,s=t.onClick,u=t.href,l=t.external,g=t.hideExternalIndicator;return l&&!g&&(n=e.createElement(o.a,{width:14,height:14})),e.createElement(a.SpinnerButton,{className:"googlesitekit-notice__cta",disabled:i,isSaving:c,onClick:s,href:u,target:l?"_blank":"_self",trailingIcon:n},r)}CTAButton.propTypes={label:i.a.string.isRequired,disabled:i.a.bool,inProgress:i.a.bool,onClick:i.a.func,href:i.a.string,external:i.a.bool,hideExternalIndicator:i.a.bool}}).call(this,n(3))},106:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DismissButton}));var r=n(0),i=n.n(r),a=n(2),o=n(11);function DismissButton(t){var n=t.label,r=void 0===n?Object(a.__)("Got it","google-site-kit"):n,i=t.onClick,c=t.disabled;return e.createElement(o.Button,{onClick:i,disabled:c,tertiary:!0},r)}DismissButton.propTypes={label:i.a.string,onClick:i.a.func.isRequired,disabled:i.a.bool}}).call(this,n(3))},107:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var r=n(245),i=n(89),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(r.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(3))},108:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(5),i=n.n(r),a=n(15),o=n(109),c=n(110);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function g(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=u(u({},l),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(i,n),g=Object(c.a)(i,n,s,r),d={},f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);d[r]||(d[r]=Object(a.once)(g)),d[r].apply(d,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:g,trackEventOnce:f}}}).call(this,n(28))},109:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(62),i=n(43),a=n(61);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,u=t.referenceSiteURL,l=t.userIDHash,g=t.userRoles,d=void 0===g?[]:g,f=t.isAuthenticated,m=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(i.b,"]"))),!o){o=!0;var r=(null==d?void 0:d.length)?d.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:u,plugin_version:m||"",enabled_features:Array.from(a.a).join(","),active_modules:s.join(","),authenticated:f?"1":"0",user_properties:{user_roles:r,user_identifier:l}});var g=n.createElement("script");return g.setAttribute(i.b,""),g.async=!0,g.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),n.head.appendChild(g),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,n(28))},11:function(e,t){e.exports=googlesitekit.components},110:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var r=n(6),i=n.n(r),a=n(5),o=n.n(a),c=n(16),s=n.n(c),u=n(62);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e,t,n,r){var a=Object(u.a)(t);return function(){var t=s()(i.a.mark((function t(o,c,s,u){var l;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),l={send_to:"site_kit",event_category:o,event_label:s,value:u},t.abrupt("return",new Promise((function(e){var t,n,i=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(i),e()};a("event",c,g(g({},l),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}()}},1103:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupMain}));var r=n(2),i=n(4),a=n(13),o=n(787),c=n(1104),s=n(79),u=n(1105),l=n(610);function SetupMain(){var t=Object(i.useSelect)((function(e){return e(a.c).getHomeURL()}));return e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--with-panels googlesitekit-setup-module--sign-in-with-google"},e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement("div",{className:"googlesitekit-setup-module__logo"},e.createElement(o.a,{width:"40",height:"40"})),e.createElement("h2",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(r._x)("Sign in with Google","Service name","google-site-kit"),e.createElement(s.a,{className:"googlesitekit-badge--beta",label:Object(r.__)("Beta","google-site-kit")}))),e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement(u.a,{moduleSlug:"sign-in-with-google"}),void 0!==t&&Object(l.a)(t)&&e.createElement(c.a,null)))}}).call(this,n(3))},1104:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return SetupForm}));var i=n(6),a=n.n(i),o=n(16),c=n.n(o),s=n(14),u=n.n(s),l=n(84),g=n(1),d=n(42),f=n(2),m=n(4),p=n(153),b=n(13),v=n(118),j=n(326),M=n(759),I=n(11),h=n(21),y=n(73),N=n(48),O=n(391),k=Object(g.lazy)((function(){return n.e(37).then(n.bind(null,1311))}));function SetupForm(){var t=Object(m.useRegistry)(),n=Object(g.useState)(),i=u()(n,2),o=i[0],s=i[1],D=Object(m.useSelect)((function(e){return e(b.c).getDocumentationLinkURL(j.a)})),S=Object(m.useSelect)((function(e){return e(v.b).getServiceClientIDProvisioningURL()}));return Object(l.a)(c()(a.a.mark((function n(){var r,i;return a.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,t.resolveSelect(v.b).getSettings();case 2:""===t.select(v.b).getClientID()&&(null===(r=e._googlesitekitModulesData)||void 0===r||null===(i=r[j.a])||void 0===i?void 0:i.existingClientID)&&s(e._googlesitekitModulesData[j.a].existingClientID);case 4:case"end":return n.stop()}}),n)})))),r.createElement("div",{className:"googlesitekit-sign-in-with-google-setup__form"},r.createElement("div",{className:"googlesitekit-setup-module__panel-item"},r.createElement(p.a,{moduleSlug:v.b,storeName:v.b}),r.createElement("p",{className:"googlesitekit-setup-module__step-description"},Object(d.a)(Object(f.sprintf)(/* translators: %1$s: Sign in with Google service name */
Object(f.__)("To set up %1$s, Site Kit will help you create an “OAuth Client ID“ that will be used to enable %1$s on your website. You will be directed to a page that will allow you to generate an “OAuth Client ID“. <a>Learn more</a>","google-site-kit"),Object(f._x)("Sign in with Google","Service name","google-site-kit")),{a:r.createElement(h.a,{href:D,external:!0})})),r.createElement("p",{className:"googlesitekit-margin-bottom-0"},Object(f.__)("Add your client ID here to complete setup:","google-site-kit")),r.createElement("div",{className:"googlesitekit-setup-module__inputs"},r.createElement(M.a,{existingClientID:o})),r.createElement(I.Button,{className:"googlesitekit-sign-in-with-google-client-id-cta",href:S,target:"_blank",trailingIcon:r.createElement(y.a,{width:"15",height:"15"}),inverse:!0},Object(f.__)("Get your client ID","google-site-kit"))),r.createElement("div",{className:"googlesitekit-setup-module__panel-item googlesitekit-setup-module__panel-item--with-svg"},r.createElement(g.Suspense,{fallback:r.createElement(N.a,{width:"100%",height:"235px"})},r.createElement(O.a,{errorMessage:Object(f.__)("Failed to load graphic","google-site-kit")},r.createElement(k,null)))))}}).call(this,n(28),n(3))},1105:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HTTPSWarning}));var r=n(0),i=n.n(r),a=n(2),o=n(4),c=n(19),s=n(13),u=n(610),l=n(35);function HTTPSWarning(t){var n=t.moduleSlug,r=Object(o.useSelect)((function(e){return e(c.a).getModule(n)})),i=Object(o.useSelect)((function(e){return e(s.c).getHomeURL()}));if(!(null==r?void 0:r.name)||void 0!==i&&Object(u.a)(i))return null;var g=r.name;return e.createElement(l.a,{className:"googlesitekit-notice--small",type:l.a.TYPES.WARNING,description:Object(a.sprintf)(/* translators: %s: Module name. */
Object(a.__)("The site should use HTTPS to set up %s","google-site-kit"),g),hideIcon:!0})}HTTPSWarning.propTypes={moduleSlug:i.a.string.isRequired}}).call(this,n(3))},1106:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsEdit}));var r=n(4),i=n(11),a=n(118),o=n(1107);function SettingsEdit(){var t=Object(r.useSelect)((function(e){return e(a.b).isDoingSubmitChanges()}));return e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--sign-in-with-google"},t?e.createElement(i.ProgressBar,null):e.createElement(o.a,null))}}).call(this,n(3))},1107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsForm}));var r=n(539),i=n(118),a=n(153),o=n(17);function SettingsForm(){return e.createElement("div",{className:"googlesitekit-sign-in-with-google-settings-fields"},e.createElement(a.a,{moduleSlug:"sign-in-with-google",storeName:i.b}),e.createElement(o.e,null,e.createElement(o.k,null,e.createElement(o.a,{size:8},e.createElement(o.e,{className:"googlesitekit-sign-in-with-google-settings-fields__stretch-form"},e.createElement(o.k,null,e.createElement(o.a,{size:12},e.createElement(r.e,null))),e.createElement(o.k,null,e.createElement(o.a,{size:4},e.createElement(r.c,null)),e.createElement(o.a,{size:4},e.createElement(r.d,null)),e.createElement(o.a,{size:4},e.createElement(r.b,null))))),e.createElement(o.a,{size:4,className:"googlesitekit-sign-in-with-google-settings-fields__button-preview"},e.createElement(o.e,null,e.createElement(o.k,null,e.createElement(o.a,{size:12},e.createElement(r.g,null)))))),e.createElement(o.k,null,e.createElement(o.a,{size:12},e.createElement(r.f,null)),e.createElement(o.a,{size:12},e.createElement(r.a,null),e.createElement(r.h,null)))))}}).call(this,n(3))},1108:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsView}));var r=n(2),i=n(4),a=n(13),o=n(118),c=n(539),s=n(153),u=n(192);function SettingsView(){var t=Object(i.useSelect)((function(e){return e(o.b).getClientID()})),n=Object(i.useSelect)((function(e){return e(a.c).getAnyoneCanRegister()})),l=Object(i.useSelect)((function(e){var t,n=e(o.b).getShape();return null===(t=o.c.find((function(e){return e.value===n})))||void 0===t?void 0:t.label})),g=Object(i.useSelect)((function(e){var t,n=e(o.b).getText();return null===(t=o.d.find((function(e){return e.value===n})))||void 0===t?void 0:t.label})),d=Object(i.useSelect)((function(e){var t,n=e(o.b).getTheme();return null===(t=o.e.find((function(e){return e.value===n})))||void 0===t?void 0:t.label})),f=Object(i.useSelect)((function(e){return e(o.b).getOneTapEnabled()})),m=Object(i.useSelect)((function(e){return e(o.b).getOneTapOnAllPages()}));return t?e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--sign-in-with-google"},e.createElement(s.a,{moduleSlug:"sign-in-with-google",storeName:o.b}),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Client ID","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:t})))),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Button text","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:g}))),e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Button theme","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:d}))),e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Button shape","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:l})))),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("One Tap sign in","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},!f&&e.createElement(u.b,{value:Object(r.__)("Disabled","google-site-kit")}),!!f&&e.createElement(u.b,{value:m?Object(r.__)("Enabled (on all pages)","google-site-kit"):Object(r.__)("Enabled (login pages only)","google-site-kit")})))),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("User registration","google-site-kit")),void 0!==n&&e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:n?Object(r.__)("Enabled","google-site-kit"):Object(r.__)("Disabled","google-site-kit")})))),e.createElement(c.h,null)):null}}).call(this,n(3))},1109:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SignInWithGoogleSetupCTABanner}));var r=n(0),i=n.n(r),a=n(84),o=n(2),c=n(4),s=n(13),u=n(7),l=n(326),g=n(187),d=n(239),f=n(1110),m=n(1111);function SignInWithGoogleSetupCTABanner(t){var n=t.id,r=t.Notification,i=Object(c.useSelect)((function(e){return e(s.c).getDocumentationLinkURL(l.a)})),p=Object(c.useDispatch)(u.a).triggerSurvey;Object(a.a)((function(){p("view_siwg_setup_cta")}));var b=Object(g.a)(l.a);return e.createElement(r,null,e.createElement(d.a,{notificationID:n,title:Object(o.sprintf)(/* translators: %s: Sign in with Google service name */
Object(o.__)("Boost onboarding, security, and trust on your site using %s","google-site-kit"),Object(o._x)("Sign in with Google","Service name","google-site-kit")),description:Object(o.sprintf)(/* translators: %s: Sign in with Google service name */
Object(o.__)("Provide your site visitors with a simple, secure, and personalized experience by adding a %s button to your login page.","google-site-kit"),Object(o._x)("Sign in with Google","Service name","google-site-kit")),learnMoreLink:{href:i},ctaButton:{label:Object(o.sprintf)(/* translators: %s: Sign in with Google service name */
Object(o.__)("Set up %s","google-site-kit"),Object(o._x)("Sign in with Google","Service name","google-site-kit")),onClick:b},dismissButton:{label:Object(o.__)("Maybe later","google-site-kit")},svg:{desktop:f.a,mobile:m.a,verticalPosition:"bottom"}}))}SignInWithGoogleSetupCTABanner.propTypes={id:i.a.string,Notification:i.a.elementType}}).call(this,n(3))},111:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},1110:function(e,t,n){"use strict";t.a="data:image/svg+xml;base64,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"},1111:function(e,t,n){"use strict";t.a="data:image/svg+xml;base64,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"},1112:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupSuccessSubtleNotification}));var r=n(14),i=n.n(r),a=n(0),o=n.n(a),c=n(2),s=n(166),u=n(38),l=n(205),g=n(4),d=n(13),f=n(326);function SetupSuccessSubtleNotification(t){var n=t.id,r=t.Notification,a=Object(l.a)("notification"),o=i()(a,2)[1],m=Object(l.a)("slug"),p=i()(m,2)[1],b=Object(g.useSelect)((function(e){return e(d.c).getModuleSettingsURL(f.a)}));return e.createElement(r,null,e.createElement(s.a,{notificationID:n,type:u.a.SUCCESS,title:Object(c.sprintf)(/* translators: %s: Sign in with Google service name */
Object(c.__)("You successfully set up %s!","google-site-kit"),Object(c._x)("Sign in with Google","Service name","google-site-kit")),description:Object(c.sprintf)(/* translators: %s: Sign in with Google service name */
Object(c.__)("%s button was added to your site login page. You can customize the button appearance in settings.","google-site-kit"),Object(c._x)("Sign in with Google","Service name","google-site-kit")),dismissButton:{label:Object(c.__)("Maybe later","google-site-kit"),onClick:function(){o(void 0),p(void 0)}},ctaButton:{label:Object(c.__)("Customize settings","google-site-kit"),href:b}}))}SetupSuccessSubtleNotification.propTypes={id:o.a.string.isRequired,Notification:o.a.elementType.isRequired}}).call(this,n(3))},1113:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(12),o=n.n(a),c=n(4),s=n(118),u=n(326);function l(e){return Object(c.createRegistrySelector)((function(t){return function(){return(t(s.b).getModuleData()||[])[e]}}))}var g={moduleData:{isWooCommerceActive:void 0,isWooCommerceRegistrationEnabled:void 0}},d={receiveModuleData:function(e){return o()(e,"moduleData is required."),{payload:e,type:"RECEIVE_MODULE_DATA"}}},f=Object(c.createReducer)((function(e,t){switch(t.type){case"RECEIVE_MODULE_DATA":var n=t.payload,r=n.isWooCommerceActive,i=n.isWooCommerceRegistrationEnabled;e.moduleData={isWooCommerceActive:r,isWooCommerceRegistrationEnabled:i}}})),m={getModuleData:i.a.mark((function t(){var n,r;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=null===(n=e._googlesitekitModulesData)||void 0===n?void 0:n[u.a]){t.next=3;break}return t.abrupt("return");case 3:return t.next=5,d.receiveModuleData(r);case 5:case"end":return t.stop()}}),t)}))},p={getModuleData:function(e){return e.moduleData},getIsWooCommerceActive:l("isWooCommerceActive"),getIsWooCommerceRegistrationEnabled:l("isWooCommerceRegistrationEnabled")};t.a={initialState:g,actions:d,controls:{},reducer:f,resolvers:m,selectors:p}}).call(this,n(28))},112:function(e,t,n){"use strict";var r=n(131);n.d(t,"a",(function(){return r.a}));var i=n(132);n.d(t,"c",(function(){return i.a}));var a=n(133);n.d(t,"b",(function(){return a.a}))},118:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c}));var r="modules/sign-in-with-google",i="non_https_site",a=[{value:"outline",label:"Light"},{value:"filled_blue",label:"Neutral"},{value:"filled_black",label:"Dark"}],o=[{value:"continue_with",label:"Continue with Google"},{value:"signin",label:"Sign in"},{value:"signin_with",label:"Sign in with Google"},{value:"signup_with",label:"Sign up with Google"}],c=[{value:"pill",label:"Pill"},{value:"rectangular",label:"Rectangular"}]},129:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(225),i=n(15),a=n(1);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object(r.b)((function(){return i.debounce.apply(void 0,t)}),t);return Object(a.useEffect)((function(){return function(){return o.cancel()}}),[o]),o}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r="core/site",i="primary",a="secondary"},1304:function(e,t,n){"use strict";n.r(t);var r=n(4),i=n.n(r),a=n(198),o=n.n(a),c=n(388),s=n.n(c),u=n(6),l=n.n(u),g=n(16),d=n.n(g),f=n(2),m=n(443),p=n(13),b=n(19),v=n(118),j=n(326),M=n(787),I=n(1103),h=n(1106),y=n(1108),N=n(1109),O=n(51),k=n(24),D=n(1112),S=n(610),T=n(12),E=n.n(T),w=n(207),A=n(63),_=n(724);var z=o.a.createModuleStore(j.a,{storeName:v.b,validateCanSubmitChanges:function(e){var t=Object(A.e)(e)(v.b),n=t.getClientID,r=t.getShape,i=t.getText,a=t.getTheme,o=t.haveSettingsChanged,c=t.isDoingSubmitChanges;E()(!c(),w.a),E()(o(),w.b);var s=n(),u=r(),l=i(),g=a();E()(null==s?void 0:s.length,"clientID is required"),E()(Object(_.a)(s),"a valid clientID is required to submit changes"),E()(!!v.c.find((function(e){return e.value===u})),"shape must be one of: ".concat(v.c.map((function(e){return e.value})).join(", "))),E()(!!v.d.find((function(e){return e.value===l})),"text must be one of: ".concat(v.d.map((function(e){return e.value})).join(", "))),E()(!!v.e.find((function(e){return e.value===g})),"theme must be one of: ".concat(v.e.map((function(e){return e.value})).join(", ")))},ownedSettingsSlugs:[],settingSlugs:["clientID","shape","text","theme","oneTapEnabled","oneTapOnAllPages"]}),x=n(176),C=n(7),L={selectors:{getServiceURL:Object(r.createRegistrySelector)((function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.path,i=n.query,a="https://developers.google.com/identity/site-kit";if(i&&(a=Object(x.a)(a,i)),r){var o="/".concat(r.replace(/^\//,""));a="".concat(a,"#").concat(o)}var c=e(C.a).getAccountChooserURL(a);if(void 0!==c)return c}})),getServiceClientIDProvisioningURL:Object(r.createRegistrySelector)((function(e){return function(){var t=e(p.c).getSiteName(),n=e(p.c).getHomeURL(),r={appname:t,sitename:t,siteorigin:n?new URL(n).origin:n};return e(v.b).getServiceURL({query:r})}}))}},P=n(1113),R=Object(r.combineStores)(z,L,P.a);R.initialState,R.actions,R.controls,R.reducer,R.resolvers,R.selectors;var U,B,G,Q;o.a.registerModule(j.a,{storeName:v.b,SettingsEditComponent:h.a,SettingsViewComponent:y.a,SetupComponent:I.a,onCompleteSetup:(B=d()(l.a.mark((function e(t,n){var r,i;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.dispatch(v.b),i=r.submitChanges,e.next=3,i();case 3:e.sent.error||n();case 5:case"end":return e.stop()}}),e)}))),function(e,t){return B.apply(this,arguments)}),Icon:M.a,features:[Object(f.__)("Users will no longer be able to sign in to your WordPress site using their Google Accounts","google-site-kit"),Object(f.__)("Users will not be able to create an account on your site using their Google Account (if account creation is enabled)","google-site-kit"),Object(f.__)("Existing users who have only used Sign in with Google to sign in to your site will need to use WordPress’ “Reset my password” to set a password for their account","google-site-kit")],overrideSetupSuccessNotification:!0,checkRequirements:(U=d()(l.a.mark((function e(t){var n;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.resolveSelect(p.c).getSiteInfo();case 2:if(n=t.select(p.c).getHomeURL(),!Object(S.a)(n)){e.next=5;break}return e.abrupt("return");case 5:throw{code:v.a,message:Object(f.__)("The site should use HTTPS to set up Sign in with Google","google-site-kit"),data:null};case 6:case"end":return e.stop()}}),e)}))),function(e){return U.apply(this,arguments)})}),i.a.registerStore(v.b,R),(G=s.a).registerNotification("sign-in-with-google-setup-cta",{Component:N.a,priority:O.e.SETUP_CTA_LOW,areaSlug:O.c.DASHBOARD_TOP,groupID:O.d.SETUP_CTAS,viewContexts:[k.n],checkRequirements:(Q=d()(l.a.mark((function e(t){var n,r,i;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,e.next=3,Promise.all([r(b.a).getModules(),r(p.c).getSiteInfo()]);case 3:if(!n(b.a).isModuleConnected(j.a)){e.next=6;break}return e.abrupt("return",!1);case 6:if(i=n(p.c).getHomeURL(),Object(S.a)(i)){e.next=9;break}return e.abrupt("return",!1);case 9:return e.abrupt("return",!0);case 10:case"end":return e.stop()}}),e)}))),function(e){return Q.apply(this,arguments)}),isDismissible:!0}),G.registerNotification("setup-success-notification-siwg",{Component:D.a,areaSlug:O.c.DASHBOARD_TOP,viewContexts:[k.n],checkRequirements:function(){var e=Object(m.a)(location.href,"notification"),t=Object(m.a)(location.href,"slug");return"authentication_success"===e&&t===j.a}})},131:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(20),i=n.n(r),a=n(5),o=n.n(a),c=n(22),s=n.n(c),u=n(0),l=n.n(u),g=n(10),d=n.n(g);function Cell(t){var n,r=t.className,a=t.alignTop,c=t.alignMiddle,u=t.alignBottom,l=t.alignRight,g=t.alignLeft,f=t.smAlignRight,m=t.mdAlignRight,p=t.lgAlignRight,b=t.smSize,v=t.smStart,j=t.smOrder,M=t.mdSize,I=t.mdStart,h=t.mdOrder,y=t.lgSize,N=t.lgStart,O=t.lgOrder,k=t.size,D=t.children,S=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},S,{className:d()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":u,"mdc-layout-grid__cell--align-right":l,"mdc-layout-grid__cell--align-left":g,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":m,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(k),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--span-".concat(y,"-desktop"),12>=y&&y>0),o()(n,"mdc-layout-grid__cell--start-".concat(N,"-desktop"),12>=N&&N>0),o()(n,"mdc-layout-grid__cell--order-".concat(O,"-desktop"),12>=O&&O>0),o()(n,"mdc-layout-grid__cell--span-".concat(M,"-tablet"),8>=M&&M>0),o()(n,"mdc-layout-grid__cell--start-".concat(I,"-tablet"),8>=I&&I>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-tablet"),8>=h&&h>0),o()(n,"mdc-layout-grid__cell--span-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--order-".concat(j,"-phone"),4>=j&&j>0),n))}),D)}Cell.propTypes={smSize:l.a.number,smStart:l.a.number,smOrder:l.a.number,mdSize:l.a.number,mdStart:l.a.number,mdOrder:l.a.number,lgSize:l.a.number,lgStart:l.a.number,lgOrder:l.a.number,size:l.a.number,alignTop:l.a.bool,alignMiddle:l.a.bool,alignBottom:l.a.bool,alignRight:l.a.bool,alignLeft:l.a.bool,smAlignRight:l.a.bool,mdAlignRight:l.a.bool,lgAlignRight:l.a.bool,className:l.a.string,children:l.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(3))},132:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),s=n.n(c),u=n(10),l=n.n(u),g=n(1),d=Object(g.forwardRef)((function(t,n){var r=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",i()({ref:n,className:l()("mdc-layout-grid__inner",r)},c),a)}));d.displayName="Row",d.propTypes={className:s.a.string,children:s.a.node},d.defaultProps={className:""},t.a=d}).call(this,n(3))},133:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(0),s=n.n(c),u=n(10),l=n.n(u),g=n(1),d=Object(g.forwardRef)((function(t,n){var r=t.alignLeft,a=t.fill,c=t.className,s=t.children,u=t.collapsed,g=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:l()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":u,"mdc-layout-grid--fill":a})},g,{ref:n}),s)}));d.displayName="Grid",d.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},d.defaultProps={className:""},t.a=d}).call(this,n(3))},134:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},135:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},136:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},139:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Icon}));var r,i=n(5),a=n.n(i),o=n(0),c=n.n(o),s=n(111),u=n(71),l=n(97),g=n(38),d=(r={},a()(r,g.a.NEW,l.a),a()(r,g.a.SUCCESS,s.a),a()(r,g.a.INFO,u.a),a()(r,g.a.WARNING,u.a),a()(r,g.a.ERROR,u.a),r);function Icon(t){var n=t.type,r=d[n]||u.a;return e.createElement(r,{width:24,height:24})}Icon.propTypes={type:c.a.oneOf(Object.values(g.a))}}).call(this,n(3))},140:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Title}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Title(t){var n=t.className,r=t.children;return e.createElement("p",{className:i()("googlesitekit-notice__title",n)},r)}Title.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Description(t){var n=t.className,r=t.children;return e.createElement("p",{className:i()("googlesitekit-notice__description",n)},r)}Description.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},145:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LearnMoreLink}));var r=n(0),i=n.n(r),a=n(2),o=n(21);function LearnMoreLink(t){var n=t.href,r=t.className,i=t.label,c=void 0===i?Object(a.__)("Learn more","google-site-kit"):i,s=t.onClick,u=void 0===s?function(){}:s;return n?e.createElement(o.a,{href:n,className:r,onClick:u,external:!0},c):null}LearnMoreLink.propTypes={href:i.a.string.isRequired,className:i.a.string,label:i.a.string,onClick:i.a.func}}).call(this,n(3))},146:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTAButton}));var r=n(0),i=n.n(r),a=n(11);function CTAButton(t){var n=t.label,r=t.disabled,i=t.inProgress,o=t.onClick,c=t.href;return n&&(o||c)?e.createElement(a.SpinnerButton,{className:"googlesitekit-banner__cta",disabled:r||i,isSaving:i,onClick:o,href:c},n):null}CTAButton.propTypes={label:i.a.string.isRequired,disabled:i.a.bool,inProgress:i.a.bool,onClick:i.a.func,href:i.a.string}}).call(this,n(3))},147:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DismissButton}));var r=n(0),i=n.n(r),a=n(2),o=n(11);function DismissButton(t){var n=t.className,r=t.label,i=void 0===r?Object(a.__)("Maybe later","google-site-kit"):r,c=t.tertiary,s=void 0===c||c,u=t.onClick,l=t.disabled;return u?e.createElement(o.Button,{className:n,onClick:u,disabled:l,tertiary:s},i):null}DismissButton.propTypes={className:i.a.string,label:i.a.string,tertiary:i.a.bool,onClick:i.a.func,disabled:i.a.bool}}).call(this,n(3))},153:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StoreErrorNotices}));var r=n(0),i=n.n(r),a=n(4),o=n(59),c=n(19),s=n(34),u=n(177);function StoreErrorNotices(t){var n=t.hasButton,r=void 0!==n&&n,i=t.moduleSlug,l=t.storeName,g=Object(a.useSelect)((function(e){return e(l).getErrors()})),d=Object(a.useSelect)((function(e){return e(c.a).getModule(i)})),f=[];return g.filter((function(e){return!(!(null==e?void 0:e.message)||f.includes(e.message))&&(f.push(e.message),!0)})).map((function(t,n){var i=t.message;return Object(s.e)(t)&&(i=Object(u.a)(i,d)),e.createElement(o.a,{key:n,error:t,hasButton:r,storeName:l,message:i})}))}StoreErrorNotices.propTypes={hasButton:i.a.bool,storeName:i.a.string.isRequired,moduleSlug:i.a.string}}).call(this,n(3))},166:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NoticeNotification}));var r=n(20),i=n.n(r),a=n(6),o=n.n(a),c=n(5),s=n.n(c),u=n(16),l=n.n(u),g=n(22),d=n.n(g),f=n(35),m=n(69),p=n(4),b=n(41),v=n(17),j=n(0),M=n.n(j);function I(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?I(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):I(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function NoticeNotification(t){var n=t.notificationID,r=t.children,a=t.dismissButton,c=t.ctaButton,s=t.gaTrackingEventArgs,u=d()(t,["notificationID","children","dismissButton","ctaButton","gaTrackingEventArgs"]),g=Object(m.a)(n),j=Object(p.useDispatch)(b.a).dismissNotification,M=function(){var e=l()(o.a.mark((function e(t){var r;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==a||null===(r=a.onClick)||void 0===r?void 0:r.call(a,t);case 2:g.dismiss(null==s?void 0:s.label,null==s?void 0:s.value),j(n,h({},(null==a?void 0:a.dismissOptions)||{}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),I=function(){var e=l()(o.a.mark((function e(t){var n;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==c||null===(n=c.onClick)||void 0===n?void 0:n.call(c,t);case 2:g.confirm(null==s?void 0:s.label,null==s?void 0:s.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(v.e,null,e.createElement(v.k,null,e.createElement(v.a,{size:12,alignMiddle:!0},e.createElement(f.a,i()({dismissButton:h(h({},a),{},{onClick:M}),ctaButton:h(h({},c),{},{onClick:I})},u),r))))}NoticeNotification.propTypes={notificationID:M.a.string.isRequired,children:M.a.node,dismissButton:M.a.oneOfType([M.a.bool,M.a.object]),ctaButton:M.a.object,gaTrackingEventArgs:M.a.object}}).call(this,n(3))},17:function(e,t,n){"use strict";var r=n(261);n.d(t,"i",(function(){return r.a}));var i=n(333);n.d(t,"f",(function(){return i.a}));var a=n(334);n.d(t,"h",(function(){return a.a}));var o=n(335);n.d(t,"j",(function(){return o.a}));var c=n(332);n.d(t,"g",(function(){return c.a}));var s=n(96),u=n.n(s);n.d(t,"b",(function(){return u.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var l=n(112);n.d(t,"a",(function(){return l.a})),n.d(t,"e",(function(){return l.b})),n.d(t,"k",(function(){return l.c}))},177:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(86),a=n(29);function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},o=n.slug,c=void 0===o?"":o,s=n.name,u=void 0===s?"":s,l=n.owner,g=void 0===l?{}:l;if(!c||!u)return e;var d="",f="";return a.g===c?e.match(/account/i)?d=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?d=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(d=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):i.a===c&&(d=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),d||(d=Object(r.sprintf)(/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),u)),g&&g.login&&(f=Object(r.sprintf)(/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),g.login)),f||(f=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(d," ").concat(f)}},18:function(e,t,n){"use strict";var r=n(1),i=n(65);t.a=function(){return Object(r.useContext)(i.b)}},184:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(10),o=n.n(a),c=n(0),s=n.n(c),u=n(1),l=n(23),g=n(228),d=n(229),f=n(230),m=n(145),p=n(146),b=n(147),v=n(231),j=n(35),M=Object(u.forwardRef)((function(t,n){var r=t.className,a=t.title,c=t.description,s=t.additionalDescription,u=t.errorText,M=t.helpText,I=t.learnMoreLink,h=t.dismissButton,y=t.ctaButton,N=t.svg,O=t.footer,k=Object(l.e)(),D=k===l.b||k===l.c,S=null;D&&(null==N?void 0:N.mobile)?S=N.mobile:!D&&(null==N?void 0:N.desktop)&&(S=N.desktop);var T=(null==N?void 0:N.verticalPosition)?N.verticalPosition:"center";return e.createElement("div",{ref:n,className:o()("googlesitekit-banner",r)},e.createElement("div",{className:"googlesitekit-banner__content"},e.createElement(g.a,null,a),e.createElement(d.a,null,c," ",(null==I?void 0:I.href)&&e.createElement(m.a,I),s&&e.createElement("div",{className:"googlesitekit-banner__additional-description"},s)),M&&e.createElement(f.a,null,M),u&&e.createElement(j.a,{type:"error",description:u}),e.createElement("div",{className:"googlesitekit-notice__action"},y&&e.createElement(p.a,y),(null==h?void 0:h.onClick)&&e.createElement(b.a,h))),S&&e.createElement("div",{className:o()("googlesitekit-banner__svg-wrapper",i()({},"googlesitekit-banner__svg-wrapper--".concat(T),T)),style:{backgroundImage:"url(".concat(S,")")}}),O&&e.createElement(v.a,null,O))}));M.propTypes={title:s.a.string,description:s.a.oneOfType([s.a.string,s.a.node]),additionalDescription:s.a.oneOfType([s.a.string,s.a.node]),errorText:s.a.string,helpText:s.a.string,learnMoreLink:s.a.shape(m.a.propTypes),dismissButton:s.a.shape(b.a.propTypes),ctaButton:s.a.shape(p.a.propTypes),svg:s.a.shape({desktop:s.a.elementType,mobile:s.a.elementType,verticalPosition:s.a.oneOf(["top","center","bottom"])}),footer:s.a.node},t.a=M}).call(this,n(3))},187:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=n(1),s=n(4),u=n(13),l=n(7),g=n(19),d=n(33),f=n(37),m=n(40),p=n(18);function b(e){var t=Object(p.a)(),n=Object(s.useSelect)((function(t){return t(g.a).getModule(e)})),r=Object(s.useSelect)((function(e){return e(l.a).hasCapability(l.K)})),a=Object(s.useDispatch)(g.a).activateModule,b=Object(s.useDispatch)(d.a).navigateTo,v=Object(s.useDispatch)(u.c).setInternalServerError,j=Object(c.useCallback)(o()(i.a.mark((function n(){var r,o,c;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,a(e);case 2:if(r=n.sent,o=r.error,c=r.response,o){n.next=13;break}return n.next=8,Object(m.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(f.f)("module_setup",e,{ttl:300});case 10:b(c.moduleReauthURL),n.next=14;break;case 13:v({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[a,e,b,v,t]);return(null==n?void 0:n.name)&&r?j:null}},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="core/modules",i="insufficient_module_dependencies"},192:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(0),i=n.n(r),a=" ";function DisplaySetting(e){return e.value||a}DisplaySetting.propTypes={value:i.a.oneOfType([i.a.string,i.a.bool,i.a.number])},t.b=DisplaySetting},198:function(e,t){e.exports=googlesitekit.modules},2:function(e,t){e.exports=googlesitekit.i18n},205:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(14),o=n.n(a),c=n(1),s=n(443),u=n(176);t.a=function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=Object(c.useState)(Object(s.a)(r.location.href,t)||n),l=o()(a,2),g=l[0],d=l[1],f=function(e){d(e);var n=Object(u.a)(r.location.href,i()({},t,e));r.history.replaceState(null,"",n)};return[g,f]}}).call(this,n(28))},207:function(e,t,n){"use strict";n.d(t,"a",(function(){return y})),n.d(t,"b",(function(){return N})),n.d(t,"c",(function(){return O})),n.d(t,"g",(function(){return k})),n.d(t,"f",(function(){return D})),n.d(t,"d",(function(){return S})),n.d(t,"e",(function(){return T}));var r=n(16),i=n.n(r),a=n(6),o=n.n(a),c=n(5),s=n.n(c),u=n(12),l=n.n(u),g=n(15),d=n(46),f=n(4),m=n(63),p=n(93),b=n(50),v=n(68);function j(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?j(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var I=v.a.clearError,h=v.a.receiveError,y="cannot submit changes while submitting changes",N="cannot submit changes if settings have not changed",O=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=r.ownedSettingsSlugs,a=void 0===i?void 0:i,c=r.storeName,u=void 0===c?void 0:c,v=r.settingSlugs,j=void 0===v?[]:v,y=r.initialSettings,N=void 0===y?void 0:y,O=r.validateHaveSettingsChanged,k=void 0===O?T():O;l()(e,"type is required."),l()(t,"identifier is required."),l()(n,"datapoint is required.");var D=u||"".concat(e,"/").concat(t),S={ownedSettingsSlugs:a,settings:N,savedSettings:void 0},E=Object(b.a)({baseName:"getSettings",controlCallback:function(){return Object(d.get)(e,t,n,{},{useCache:!1})},reducerCallback:function(e,t){return M(M({},e),{},{savedSettings:M({},t),settings:M(M({},t),e.settings||{})})}}),w=Object(b.a)({baseName:"saveSettings",controlCallback:function(r){var i=r.values;return Object(d.set)(e,t,n,i)},reducerCallback:function(e,t){return M(M({},e),{},{savedSettings:M({},t),settings:M({},t)})},argsToParams:function(e){return{values:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.values;l()(Object(g.isPlainObject)(t),"values is required.")}}),A={},_={setSettings:function(e){return l()(Object(g.isPlainObject)(e),"values is required."),{payload:{values:e},type:"SET_SETTINGS"}},rollbackSettings:function(){return{payload:{},type:"ROLLBACK_SETTINGS"}},rollbackSetting:function(e){return l()(e,"setting is required."),{payload:{setting:e},type:"ROLLBACK_SETTING"}},saveSettings:o.a.mark((function e(){var t,n,r,i,a;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,I("saveSettings",[]);case 5:return n=t.select(D).getSettings(),e.next=8,w.actions.fetchSaveSettings(n);case 8:if(r=e.sent,i=r.response,!(a=r.error)){e.next=14;break}return e.next=14,h(a,"saveSettings",[]);case 14:return e.abrupt("return",{response:i,error:a});case 15:case"end":return e.stop()}}),e)}))},z={},x=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:S,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"SET_SETTINGS":var i=r.values;return M(M({},e),{},{settings:M(M({},e.settings||{}),i)});case"ROLLBACK_SETTINGS":return M(M({},e),{},{settings:e.savedSettings});case"ROLLBACK_SETTING":var a=r.setting;return e.savedSettings[a]?M(M({},e),{},{settings:M(M({},e.settings||{}),{},s()({},a,e.savedSettings[a]))}):M({},e);default:return void 0!==A[n]?A[n](e,{type:n,payload:r}):e}},C={getSettings:o.a.mark((function e(){var t;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:if(t=e.sent,t.select(D).getSettings()){e.next=7;break}return e.next=7,E.actions.fetchGetSettings();case 7:case"end":return e.stop()}}),e)}))},L=Object(m.g)(k),P=L.safeSelector,R=L.dangerousSelector,U={haveSettingsChanged:P,__dangerousHaveSettingsChanged:R,getSettings:function(e){return e.settings},hasSettingChanged:function(e,t){l()(t,"setting is required.");var n=e.settings,r=e.savedSettings;return!(!n||!r)&&!Object(g.isEqual)(n[t],r[t])},isDoingSaveSettings:function(e){return Object.values(e.isFetchingSaveSettings).some(Boolean)},getOwnedSettingsSlugs:function(e){return e.ownedSettingsSlugs},haveOwnedSettingsChanged:Object(f.createRegistrySelector)((function(e){return function(){var t=e(D).getOwnedSettingsSlugs();return e(D).haveSettingsChanged(t)}}))};j.forEach((function(e){var t=Object(p.b)(e),n=Object(p.a)(e);_["set".concat(t)]=function(e){return l()(void 0!==e,"value is required for calls to set".concat(t,"().")),{payload:{value:e},type:"SET_".concat(n)}},A["SET_".concat(n)]=function(t,n){var r=n.payload.value;return M(M({},t),{},{settings:M(M({},t.settings||{}),{},s()({},e,r))})},U["get".concat(t)]=Object(f.createRegistrySelector)((function(t){return function(){return(t(D).getSettings()||{})[e]}}))}));var B=Object(f.combineStores)(f.commonStore,E,w,{initialState:S,actions:_,controls:z,reducer:x,resolvers:C,selectors:U});return M(M({},B),{},{STORE_NAME:D})};function k(e,t){return function(){var n=i()(o.a.mark((function n(r){var i,a,c,s;return o.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(i=r.select,a=r.dispatch,!i(t).haveSettingsChanged()){n.next=8;break}return n.next=4,a(t).saveSettings();case 4:if(c=n.sent,!(s=c.error)){n.next=8;break}return n.abrupt("return",{error:s});case 8:return n.next=10,Object(d.invalidateCache)("modules",e);case 10:return n.abrupt("return",{});case 11:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function D(e){return function(t){var n=t.select,r=t.dispatch;return n(e).haveSettingsChanged()?r(e).rollbackSettings():{}}}function S(e){return function(t){var n=Object(m.e)(t)(e),r=n.haveSettingsChanged,i=n.isDoingSubmitChanges;l()(!i(),y),l()(r(),N)}}function T(){return function(e,t,n){var r=t.settings,i=t.savedSettings;n&&l()(!Object(g.isEqual)(Object(g.pick)(r,n),Object(g.pick)(i,n)),N),l()(!Object(g.isEqual)(r,i),N)}}},21:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(10),s=n.n(c),u=n(0),l=n.n(u),g=n(158),d=n(1),f=n(2),m=n(134),p=n(135),b=n(136),v=n(73),j=n(78),M=Object(d.forwardRef)((function(t,n){var r,a=t["aria-label"],c=t.secondary,u=void 0!==c&&c,l=t.arrow,d=void 0!==l&&l,M=t.back,I=void 0!==M&&M,h=t.caps,y=void 0!==h&&h,N=t.children,O=t.className,k=void 0===O?"":O,D=t.danger,S=void 0!==D&&D,T=t.disabled,E=void 0!==T&&T,w=t.external,A=void 0!==w&&w,_=t.hideExternalIndicator,z=void 0!==_&&_,x=t.href,C=void 0===x?"":x,L=t.inverse,P=void 0!==L&&L,R=t.noFlex,U=void 0!==R&&R,B=t.onClick,G=t.small,Q=void 0!==G&&G,Z=t.standalone,Y=void 0!==Z&&Z,W=t.linkButton,H=void 0!==W&&W,V=t.to,F=t.leadingIcon,X=t.trailingIcon,J=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),K=C||V||!B?V?"ROUTER_LINK":A?"EXTERNAL_LINK":"LINK":E?"BUTTON_DISABLED":"BUTTON",q="BUTTON"===K||"BUTTON_DISABLED"===K?"button":"ROUTER_LINK"===K?g.b:"a",$=("EXTERNAL_LINK"===K&&(r=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===K&&(r=Object(f._x)("(disabled)","screen reader text","google-site-kit")),r?a?"".concat(a," ").concat(r):"string"==typeof N?"".concat(N," ").concat(r):void 0:a),ee=F,te=X;return I&&(ee=e.createElement(b.a,{width:14,height:14})),A&&!z&&(te=e.createElement(v.a,{width:14,height:14})),d&&!P&&(te=e.createElement(m.a,{width:14,height:14})),d&&P&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(q,i()({"aria-label":$,className:s()("googlesitekit-cta-link",k,{"googlesitekit-cta-link--secondary":u,"googlesitekit-cta-link--inverse":P,"googlesitekit-cta-link--small":Q,"googlesitekit-cta-link--caps":y,"googlesitekit-cta-link--danger":S,"googlesitekit-cta-link--disabled":E,"googlesitekit-cta-link--standalone":Y,"googlesitekit-cta-link--link-button":H,"googlesitekit-cta-link--no-flex":!!U}),disabled:E,href:"LINK"!==K&&"EXTERNAL_LINK"!==K||E?void 0:C,onClick:B,rel:"EXTERNAL_LINK"===K?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===K?"_blank":void 0,to:V},J),!!ee&&e.createElement(j.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},N),!!te&&e.createElement(j.a,{marginLeft:5},te))}));M.propTypes={arrow:l.a.bool,back:l.a.bool,caps:l.a.bool,children:l.a.node,className:l.a.string,danger:l.a.bool,disabled:l.a.bool,external:l.a.bool,hideExternalIndicator:l.a.bool,href:l.a.string,inverse:l.a.bool,leadingIcon:l.a.node,linkButton:l.a.bool,noFlex:l.a.bool,onClick:l.a.func,small:l.a.bool,standalone:l.a.bool,to:l.a.string,trailingIcon:l.a.node},t.a=M}).call(this,n(3))},228:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Title}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Title(t){var n=t.className,r=t.children;return e.createElement("p",{className:i()("googlesitekit-banner__title",n)},r)}Title.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},229:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Description(t){var n=t.className,r=t.children;return e.createElement("div",{className:i()("googlesitekit-banner__description",n)},r)}Description.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},23:function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var r=n(90),i="xlarge",a="desktop",o="tablet",c="small";function s(){var e=Object(r.a)();return e>1280?i:e>960?a:e>600?o:c}},230:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HelpText}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function HelpText(t){var n=t.className,r=t.children;return e.createElement("p",{className:i()("googlesitekit-banner__help-text",n)},r)}HelpText.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},231:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Footer}));var r=n(10),i=n.n(r),a=n(0),o=n.n(a);function Footer(t){var n=t.className,r=t.children;return e.createElement("div",{className:i()("googlesitekit-banner__footer",n)},r)}Footer.propTypes={className:o.a.string,children:o.a.node}}).call(this,n(3))},239:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupCTA}));var r=n(20),i=n.n(r),a=n(6),o=n.n(a),c=n(5),s=n.n(c),u=n(16),l=n.n(u),g=n(22),d=n.n(g),f=n(10),m=n.n(f),p=n(0),b=n.n(p),v=n(1),j=n(4),M=n(41),I=n(69),h=n(184),y=n(145),N=n(146),O=n(147),k=n(17),D=n(11);function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SetupCTA(t){var n=t.notificationID,r=t.title,a=t.description,c=t.errorText,s=t.helpText,u=t.learnMoreLink,g=t.dismissButton,f=t.ctaButton,p=t.svg,b=t.footer,y=t.dismissOptions,N=t.gaTrackingEventArgs,O=t.waitingProgress,S=d()(t,["notificationID","title","description","errorText","helpText","learnMoreLink","dismissButton","ctaButton","svg","footer","dismissOptions","gaTrackingEventArgs","waitingProgress"]),E=Object(I.a)(n,null==N?void 0:N.category),w=Object(j.useDispatch)(M.a).dismissNotification,A=function(){var e=l()(o.a.mark((function e(t){var r;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==g||null===(r=g.onClick)||void 0===r?void 0:r.call(g,t);case 2:E.dismiss(null==N?void 0:N.label,null==N?void 0:N.value),w(n,T({},y));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),_=function(){var e=l()(o.a.mark((function e(t){var n;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return E.confirm(null==N?void 0:N.label,null==N?void 0:N.value),e.next=3,null==f||null===(n=f.onClick)||void 0===n?void 0:n.call(f,t);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),z=function(){var e=l()(o.a.mark((function e(t){var n;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return E.clickLearnMore(null==N?void 0:N.label,null==N?void 0:N.value),e.next=3,null==u||null===(n=u.onClick)||void 0===n?void 0:n.call(u,t);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(v.Fragment,null,!!O&&e.createElement(D.ProgressBar,i()({className:"googlesitekit-banner__progress-bar"},O)),e.createElement("div",{className:m()("googlesitekit-widget-context",{"googlesitekit-widget-context--with-progress-bar":!!O})},e.createElement(k.e,null,e.createElement(k.k,null,e.createElement(k.a,{size:12},e.createElement(h.a,i()({className:"googlesitekit-banner--setup-cta",title:r,description:a,errorText:c,helpText:s,learnMoreLink:u&&T(T({},u),{},{onClick:z}),dismissButton:g&&T(T({},g),{},{onClick:A}),ctaButton:f&&T(T({},f),{},{onClick:_}),svg:p,footer:b},S)))))))}SetupCTA.propTypes={notificationID:b.a.string,title:b.a.string,description:b.a.oneOfType([b.a.string,b.a.node]),errorText:b.a.string,helpText:b.a.string,learnMoreLink:b.a.shape(y.a.propTypes),dismissButton:b.a.shape(O.a.propTypes),ctaButton:b.a.shape(N.a.propTypes),svg:b.a.shape({desktop:b.a.elementType,mobile:b.a.elementType,verticalPosition:b.a.oneOf(["top","center","bottom"])}),footer:b.a.node,dismissOptions:b.a.object,gaTrackingEventArgs:b.a.shape({category:b.a.string,label:b.a.string,value:b.a.number}),waitingProgress:b.a.shape(D.ProgressBar.propTypes)}}).call(this,n(3))},24:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return u})),n.d(t,"i",(function(){return l})),n.d(t,"j",(function(){return g})),n.d(t,"r",(function(){return d})),n.d(t,"k",(function(){return f})),n.d(t,"u",(function(){return m})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return b})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return j})),n.d(t,"e",(function(){return M})),n.d(t,"a",(function(){return I})),n.d(t,"d",(function(){return h})),n.d(t,"c",(function(){return y})),n.d(t,"f",(function(){return N})),n.d(t,"g",(function(){return O}));var r="mainDashboard",i="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",u="splash",l="adminBar",g="adminBarViewOnly",d="settings",f="adBlockingRecovery",m="wpDashboard",p="wpDashboardViewOnly",b="moduleSetup",v="metricSelection",j="key-metrics",M="traffic",I="content",h="speed",y="monetization",N=[r,i,a,o,c,u,d,b,v],O=[a,o,g,p]},29:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"f",(function(){return c})),n.d(t,"k",(function(){return s})),n.d(t,"j",(function(){return u})),n.d(t,"h",(function(){return l})),n.d(t,"i",(function(){return g})),n.d(t,"e",(function(){return d})),n.d(t,"g",(function(){return f}));var r=1,i=2,a=3,o="enhanced-measurement-activation-banner-tooltip-state",c="enhanced-measurement-activation-banner-dismissed-item",s="_r.explorerCard..selmet",u="_r.explorerCard..seldim",l="_r..dataFilters",g="_r..nav",d="key-metrics-connect-ga4-cta-widget",f="analytics-4"},326:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="sign-in-with-google"},33:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return u})),n.d(t,"d",(function(){return l})),n.d(t,"b",(function(){return g}));n(15);var r=n(2),i="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===i}function s(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function u(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function l(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||u(e))}function g(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},35:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(10),s=n.n(c),u=n(1),l=n(139),g=n(140),d=n(141),f=n(105),m=n(106),p=n(38);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var j=Object(u.forwardRef)((function(t,n){var r=t.className,i=t.title,a=t.description,o=t.dismissButton,c=t.ctaButton,u=t.type,b=void 0===u?p.a.INFO:u,v=t.children,j=t.hideIcon;return e.createElement("div",{ref:n,className:s()("googlesitekit-notice","googlesitekit-notice--".concat(b),r)},!j&&e.createElement("div",{className:"googlesitekit-notice__icon"},e.createElement(l.a,{type:b})),e.createElement("div",{className:"googlesitekit-notice__content"},i&&e.createElement(g.a,null,i),a&&e.createElement(d.a,null,a)),((null==o?void 0:o.label)||(null==o?void 0:o.onClick)||(null==c?void 0:c.label)&&((null==c?void 0:c.onClick)||(null==c?void 0:c.href))||v)&&e.createElement("div",{className:"googlesitekit-notice__action"},v,((null==o?void 0:o.label)||(null==o?void 0:o.onClick))&&e.createElement(m.a,{label:o.label,onClick:o.onClick,disabled:o.disabled}),(null==c?void 0:c.label)&&((null==c?void 0:c.onClick)||(null==c?void 0:c.href))&&e.createElement(f.a,{label:c.label,onClick:c.onClick,inProgress:c.inProgress,disabled:c.disabled,href:c.href,external:c.external,hideExternalIndicator:c.hideExternalIndicator})))}));j.TYPES=p.a,j.propTypes={className:o.a.string,title:o.a.oneOfType([o.a.string,o.a.object]),description:o.a.node,type:o.a.oneOf(Object.values(p.a)),dismissButton:o.a.shape(m.a.propTypes),ctaButton:o.a.shape(v(v({},f.a.propTypes),{},{label:o.a.string})),children:o.a.node,hideIcon:o.a.bool},t.a=j}).call(this,n(3))},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g})),n.d(t,"d",(function(){return j})),n.d(t,"f",(function(){return M})),n.d(t,"c",(function(){return I})),n.d(t,"e",(function(){return h})),n.d(t,"b",(function(){return y}));var r=n(6),i=n.n(r),a=n(16),o=n.n(a),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return u(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var l,g="googlesitekit_",d="".concat(g).concat("1.157.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),f=["sessionStorage","localStorage"],m=[].concat(f),p=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",r.setItem(a,a),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return v.apply(this,arguments)}function v(){return(v=o()(i.a.mark((function t(){var n,r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===l){t.next=2;break}return t.abrupt("return",l);case 2:n=s(m),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(a=r.value,!l){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(a);case 11:if(!t.sent){t.next=13;break}l=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===l&&(l=null),t.abrupt("return",l);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var j=function(){var e=o()(i.a.mark((function e(t){var n,r,a,o,c,s,u;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(d).concat(t)))){e.next=10;break}if(a=JSON.parse(r),o=a.timestamp,c=a.ttl,s=a.value,u=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:u});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),M=function(){var t=o()(i.a.mark((function t(n,r){var a,o,s,u,l,g,f,m,p=arguments;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=p.length>2&&void 0!==p[2]?p[2]:{},o=a.ttl,s=void 0===o?c.b:o,u=a.timestamp,l=void 0===u?Math.round(Date.now()/1e3):u,g=a.isError,f=void 0!==g&&g,t.next=3,b();case 3:if(!(m=t.sent)){t.next=14;break}return t.prev=5,m.setItem("".concat(d).concat(n),JSON.stringify({timestamp:l,ttl:s,value:r,isError:f})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),I=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(g)?n:"".concat(d).concat(n),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),h=function(){var t=o()(i.a.mark((function t(){var n,r,a,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(g)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),y=function(){var e=o()(i.a.mark((function e(){var t,n,r,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,h();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return a=r.value,e.next=14,I(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},38:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={NEW:"new",SUCCESS:"success",WARNING:"warning",INFO:"info",ERROR:"error"}},388:function(e,t){e.exports=googlesitekit.notifications},391:function(e,t,n){"use strict";(function(e,r){var i=n(52),a=n.n(i),o=n(53),c=n.n(o),s=n(81),u=n.n(s),l=n(82),g=n.n(l),d=n(55),f=n.n(d),m=n(0),p=n.n(m),b=n(1),v=n(2),j=n(59);function M(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var i=f()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return g()(this,n)}}var I=function(t){u()(MediaErrorHandler,t);var n=M(MediaErrorHandler);function MediaErrorHandler(e){var t;return a()(this,MediaErrorHandler),(t=n.call(this,e)).state={error:null},t}return c()(MediaErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t})}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.errorMessage;return this.state.error?r.createElement(j.a,{message:n}):t}}]),MediaErrorHandler}(b.Component);I.defaultProps={errorMessage:Object(v.__)("Failed to load media","google-site-kit")},I.propTypes={children:p.a.node.isRequired,errorMessage:p.a.string.isRequired},t.a=I}).call(this,n(28),n(3))},4:function(e,t){e.exports=googlesitekit.data},40:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return I})),n.d(t,"b",(function(){return j})),n.d(t,"c",(function(){return M}));var r=n(108),i=e._googlesitekitTrackingData||{},a=i.activeModules,o=void 0===a?[]:a,c=i.isSiteKitScreen,s=i.trackingEnabled,u=i.trackingID,l=i.referenceSiteURL,g=i.userIDHash,d=i.isAuthenticated,f={activeModules:o,trackingEnabled:s,trackingID:u,referenceSiteURL:l,userIDHash:g,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:d,pluginVersion:"1.157.0"},m=Object(r.a)(f),p=m.enableTracking,b=m.disableTracking,v=(m.isTrackingEnabled,m.initializeSnippet),j=m.trackEvent,M=m.trackEventOnce;function I(e){e?p():b()}c&&s&&v()}).call(this,n(28))},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r=n(24),i="core/notifications",a=[r.s,r.n,r.l,r.o,r.m]},43:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},46:function(e,t){e.exports=googlesitekit.api},48:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(0),o=n.n(a),c=n(10),s=n.n(c),u=n(23);function PreviewBlock(t){var n,r,a=t.className,o=t.width,c=t.height,l=t.shape,g=t.padding,d=t.smallWidth,f=t.smallHeight,m=t.tabletWidth,p=t.tabletHeight,b=t.desktopWidth,v=t.desktopHeight,j=Object(u.e)(),M={width:(n={},i()(n,u.b,d),i()(n,u.c,m),i()(n,u.a,b),i()(n,u.d,b),n),height:(r={},i()(r,u.b,f),i()(r,u.c,p),i()(r,u.a,v),i()(r,u.d,b),r)};return e.createElement("div",{className:s()("googlesitekit-preview-block",a,{"googlesitekit-preview-block--padding":g}),style:{width:M.width[j]||o,height:M.height[j]||c}},e.createElement("div",{className:s()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===l})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(3))},50:function(e,t,n){"use strict";n.d(t,"a",(function(){return I}));var r=n(6),i=n.n(r),a=n(5),o=n.n(a),c=n(12),s=n.n(c),u=n(15),l=n(68),g=n(93),d=n(9);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var p=function(e){return e},b=function(){return{}},v=function(){},j=l.a.clearError,M=l.a.receiveError,I=function(e){var t,n,r=i.a.mark(C),a=e.baseName,c=e.controlCallback,l=e.reducerCallback,f=void 0===l?p:l,I=e.argsToParams,h=void 0===I?b:I,y=e.validateParams,N=void 0===y?v:y;s()(a,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof f,"reducerCallback must be a function."),s()("function"==typeof h,"argsToParams must be a function."),s()("function"==typeof N,"validateParams must be a function.");try{N(h()),n=!1}catch(e){n=!0}var O=Object(g.b)(a),k=Object(g.a)(a),D="FETCH_".concat(k),S="START_".concat(D),T="FINISH_".concat(D),E="CATCH_".concat(D),w="RECEIVE_".concat(k),A="fetch".concat(O),_="receive".concat(O),z="isFetching".concat(O),x=o()({},z,{});function C(e,t){var n,o;return i.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,{payload:{params:e},type:S};case 2:return r.next=4,j(a,t);case 4:return r.prev=4,r.next=7,{payload:{params:e},type:D};case 7:return n=r.sent,r.next=10,L[_](n,e);case 10:return r.next=12,{payload:{params:e},type:T};case 12:r.next=21;break;case 14:return r.prev=14,r.t0=r.catch(4),o=r.t0,r.next=19,M(o,a,t);case 19:return r.next=21,{payload:{params:e},type:E};case 21:return r.abrupt("return",{response:n,error:o});case 22:case"end":return r.stop()}}),r,null,[[4,14]])}var L=(t={},o()(t,A,(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=h.apply(void 0,t);return N(r),C(r,t)})),o()(t,_,(function(e,t){return s()(void 0!==e,"response is required."),n?(s()(Object(u.isPlainObject)(t),"params is required."),N(t)):t={},{payload:{response:e,params:t},type:w}})),t),P=o()({},D,(function(e){var t=e.payload;return c(t.params)})),R=o()({},z,(function(e){if(void 0===e[z])return!1;var t;try{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t=h.apply(void 0,r),N(t)}catch(e){return!1}return!!e[z][Object(d.H)(t)]}));return{initialState:x,actions:L,controls:P,reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case S:var i=r.params;return m(m({},e),{},o()({},z,m(m({},e[z]),{},o()({},Object(d.H)(i),!0))));case w:var a=r.response,c=r.params;return f(e,a,c);case T:var s=r.params;return m(m({},e),{},o()({},z,m(m({},e[z]),{},o()({},Object(d.H)(s),!1))));case E:var u=r.params;return m(m({},e),{},o()({},z,m(m({},e[z]),{},o()({},Object(d.H)(u),!1))));default:return e}},resolvers:{},selectors:R}}},51:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r="warning-notification-gtg",i="gtg-setup-cta",a={ERROR_HIGH:30,ERROR_LOW:60,WARNING:100,INFO:150,SETUP_CTA_HIGH:150,SETUP_CTA_LOW:200},o={HEADER:"notification-area-header",DASHBOARD_TOP:"notification-area-dashboard-top",OVERLAYS:"notification-area-overlays"},c={DEFAULT:"default",SETUP_CTAS:"setup-ctas"}},539:function(e,t,n){"use strict";var r=n(927);n.d(t,"c",(function(){return r.a}));var i=n(928);n.d(t,"d",(function(){return i.a}));var a=n(929);n.d(t,"b",(function(){return a.a}));var o=n(759);n.d(t,"e",(function(){return o.a}));var c=n(930);n.d(t,"a",(function(){return c.a}));n(760),n(761);var s=n(931);n.d(t,"h",(function(){return s.a}));var u=n(932);n.d(t,"f",(function(){return u.a}));var l=n(933);n.d(t,"g",(function(){return l.a}))},59:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var r=n(0),i=n.n(r),a=n(1),o=n(2),c=n(115),s=n(4),u=n(34),l=n(35),g=n(9);function ErrorNotice(t){var n,r=t.className,i=t.error,d=t.hasButton,f=void 0!==d&&d,m=t.storeName,p=t.message,b=void 0===p?i.message:p,v=t.noPrefix,j=void 0!==v&&v,M=t.skipRetryMessage,I=t.hideIcon,h=void 0!==I&&I,y=Object(s.useDispatch)(),N=Object(s.useSelect)((function(e){return m?e(m).getSelectorDataForError(i):null})),O=Object(a.useCallback)((function(){y(N.storeName).invalidateResolution(N.name,N.args)}),[y,N]);if(!b||Object(u.f)(i))return null;var k=f&&Object(u.d)(i,N),D=b;f||M||(D=Object(o.sprintf)(/* translators: %s: Error message from Google API. */
Object(o.__)("%s (Please try again.)","google-site-kit"),D)),j||(D=Object(o.sprintf)(/* translators: $%s: Error message */
Object(o.__)("Error: %s","google-site-kit"),D));var S=null==i||null===(n=i.data)||void 0===n?void 0:n.reconnectURL;S&&Object(c.a)(S)&&(D=Object(o.sprintf)(/* translators: 1: Original error message 2: Reconnect URL */
Object(o.__)('%1$s To fix this, <a href="%2$s">redo the plugin setup</a>.',"google-site-kit"),D,S));return e.createElement(l.a,{className:r,type:l.a.TYPES.ERROR,description:e.createElement("span",{dangerouslySetInnerHTML:Object(g.F)(D,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}),ctaButton:k?{label:Object(o.__)("Retry","google-site-kit"),onClick:O}:void 0,hideIcon:h})}ErrorNotice.propTypes={className:i.a.string,error:i.a.shape({message:i.a.string}),hasButton:i.a.bool,storeName:i.a.string,message:i.a.string,noPrefix:i.a.bool,skipRetryMessage:i.a.bool,hideIcon:i.a.bool}}).call(this,n(3))},61:function(e,t,n){"use strict";(function(e){var r,i;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(r=e)||void 0===r||null===(i=r._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},610:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return r}));var r=function(t){try{if("string"!=typeof t||!t)throw new TypeError("Invalid URL: ".concat(t));return"https:"===new URL(t).protocol}catch(t){return e.console.warn("Invalid URL:",t),!1}}}).call(this,n(28))},62:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(43);function i(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},63:function(e,t,n){"use strict";n.d(t,"a",(function(){return D})),n.d(t,"b",(function(){return S})),n.d(t,"c",(function(){return T})),n.d(t,"d",(function(){return w})),n.d(t,"e",(function(){return A})),n.d(t,"g",(function(){return z})),n.d(t,"f",(function(){return x}));var r,i=n(6),a=n.n(i),o=n(27),c=n.n(o),s=n(5),u=n.n(s),l=n(12),g=n.n(l),d=n(66),f=n.n(d),m=n(15),p=n(125);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){u()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var j=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.reduce((function(e,t){return v(v({},e),t)}),{}),i=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),a=E(i);return g()(0===a.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(a.join(", "),". Check your data stores for duplicates.")),r},M=j,I=j,h=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=[].concat(t);return"function"!=typeof i[0]&&(r=i.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i.reduce((function(e,n){return n(e,t)}),e)}},y=j,N=j,O=j,k=function(e){return e},D=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=O.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:r,controls:I.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:M.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:h.apply(void 0,[r].concat(c()(t.map((function(e){return e.reducer||k}))))),resolvers:y.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:N.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},S={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:a.a.mark((function e(t){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},T=(r={},u()(r,"GET_REGISTRY",Object(p.a)((function(e){return function(){return e}}))),u()(r,"AWAIT",(function(e){return e.payload.value})),r),E=function(e){for(var t=[],n={},r=0;r<e.length;r++){var i=e[r];n[i]=n[i]>=1?n[i]+1:1,n[i]>1&&t.push(i)}return t},w={actions:S,controls:T,reducer:k},A=function(e){return function(t){return _(e(t))}},_=f()((function(e){return Object(m.mapValues)(e,(function(e,t){return function(){var n=e.apply(void 0,arguments);return g()(void 0!==n,"".concat(t,"(...) is not resolved")),n}}))}));function z(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.negate,r=void 0!==n&&n,i=Object(p.b)((function(t){return function(n){var i=!r,a=!!r;try{for(var o=arguments.length,c=new Array(o>1?o-1:0),s=1;s<o;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,n].concat(c)),i}catch(e){return a}}})),a=Object(p.b)((function(t){return function(n){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];e.apply(void 0,[t,n].concat(i))}}));return{safeSelector:i,dangerousSelector:a}}function x(e,t){return g()("function"==typeof e,"a validator function is required."),g()("function"==typeof t,"an action creator function is required."),g()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},65:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(1),i=Object(r.createContext)(""),a=(i.Consumer,i.Provider);t.b=i},68:function(e,t,n){"use strict";n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return v}));var r=n(5),i=n.n(r),a=n(36),o=n.n(a),c=n(125),s=n(12),u=n.n(s),l=n(103),g=n.n(l),d=n(9);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t){if(t&&Array.isArray(t)){var n=t.map((function(e){return"object"===o()(e)?Object(d.H)(e):e}));return"".concat(e,"::").concat(g()(JSON.stringify(n)))}return e}var b={receiveError:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(e,"error is required."),u()(t,"baseName is required."),u()(n&&Array.isArray(n),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:n}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return u()(e,"baseName is required."),u()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function v(e){u()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(n,"selectorName is required."),t.getError(e,n,r)},getErrorForAction:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(n,"actionName is required."),t.getError(e,n,r)},getError:function(e,t,n){var r=e.errors;return u()(t,"baseName is required."),r[p(t,n)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var n=Object.keys(e.errors).find((function(n){return e.errors[n]===t}));return n?{baseName:n.substring(0,n.indexOf("::")),args:e.errorArgs[n]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(n,r){var i=t(e).getMetaDataForError(r);if(i){var a=i.baseName,o=i.args;if(!!t(e)[a])return{storeName:e,name:a,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:b,controls:{},reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case"RECEIVE_ERROR":var a=r.baseName,o=r.args,c=r.error,s=p(a,o);return m(m({},e),{},{errors:m(m({},e.errors||{}),{},i()({},s,c)),errorArgs:m(m({},e.errorArgs||{}),{},i()({},s,o))});case"CLEAR_ERROR":var u=r.baseName,l=r.args,g=m({},e),d=p(u,l);return g.errors=m({},e.errors||{}),g.errorArgs=m({},e.errorArgs||{}),delete g.errors[d],delete g.errorArgs[d],g;case"CLEAR_ERRORS":var f=r.baseName,b=m({},e);if(f)for(var v in b.errors=m({},e.errors||{}),b.errorArgs=m({},e.errorArgs||{}),b.errors)(v===f||v.startsWith("".concat(f,"::")))&&(delete b.errors[v],delete b.errorArgs[v]);else b.errors={},b.errorArgs={};return b;default:return e}},resolvers:{},selectors:t}}},69:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(1),i=n(18),a=n(9);function o(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=n.viewAction,c=void 0===o?"view_notification":o,s=n.confirmAction,u=void 0===s?"confirm_notification":s,l=n.dismissAction,g=void 0===l?"dismiss_notification":l,d=n.clickLearnMoreAction,f=void 0===d?"click_learn_more_link":d,m=Object(i.a)(),p=null!=t?t:"".concat(m,"_").concat(e),b=Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,c].concat(t))}),[p,c]),v=Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,u].concat(t))}),[p,u]),j=Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,g].concat(t))}),[p,g]),M=Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[p,f].concat(t))}),[p,f]);return{view:b,confirm:v,dismiss:j,clickLearnMore:M}}},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return u})),n.d(t,"O",(function(){return l})),n.d(t,"K",(function(){return g})),n.d(t,"L",(function(){return d})),n.d(t,"J",(function(){return f})),n.d(t,"I",(function(){return m})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return j})),n.d(t,"j",(function(){return M})),n.d(t,"l",(function(){return I})),n.d(t,"m",(function(){return h})),n.d(t,"n",(function(){return y})),n.d(t,"o",(function(){return N})),n.d(t,"q",(function(){return O})),n.d(t,"s",(function(){return k})),n.d(t,"r",(function(){return D})),n.d(t,"t",(function(){return S})),n.d(t,"w",(function(){return T})),n.d(t,"u",(function(){return E})),n.d(t,"v",(function(){return w})),n.d(t,"x",(function(){return A})),n.d(t,"y",(function(){return _})),n.d(t,"A",(function(){return z})),n.d(t,"B",(function(){return x})),n.d(t,"C",(function(){return C})),n.d(t,"D",(function(){return L})),n.d(t,"k",(function(){return P})),n.d(t,"F",(function(){return R})),n.d(t,"z",(function(){return U})),n.d(t,"G",(function(){return B})),n.d(t,"E",(function(){return G})),n.d(t,"i",(function(){return Q})),n.d(t,"p",(function(){return Z})),n.d(t,"Q",(function(){return Y})),n.d(t,"P",(function(){return W}));var r="core/user",i="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",u="googlesitekit_setup",l="googlesitekit_view_dashboard",g="googlesitekit_manage_options",d="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",m="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",j="kmAnalyticsLeastEngagingPages",M="kmAnalyticsNewVisitors",I="kmAnalyticsPopularAuthors",h="kmAnalyticsPopularContent",y="kmAnalyticsPopularProducts",N="kmAnalyticsReturningVisitors",O="kmAnalyticsTopCities",k="kmAnalyticsTopCitiesDrivingLeads",D="kmAnalyticsTopCitiesDrivingAddToCart",S="kmAnalyticsTopCitiesDrivingPurchases",T="kmAnalyticsTopDeviceDrivingPurchases",E="kmAnalyticsTopConvertingTrafficSource",w="kmAnalyticsTopCountries",A="kmAnalyticsTopPagesDrivingLeads",_="kmAnalyticsTopRecentTrendingPages",z="kmAnalyticsTopTrafficSource",x="kmAnalyticsTopTrafficSourceDrivingAddToCart",C="kmAnalyticsTopTrafficSourceDrivingLeads",L="kmAnalyticsTopTrafficSourceDrivingPurchases",P="kmAnalyticsPagesPerVisit",R="kmAnalyticsVisitLength",U="kmAnalyticsTopReturningVisitorPages",B="kmSearchConsolePopularKeywords",G="kmAnalyticsVisitsPerVisitor",Q="kmAnalyticsMostEngagingPages",Z="kmAnalyticsTopCategories",Y=[b,v,j,M,I,h,y,N,Z,O,k,D,S,T,E,w,_,z,x,P,R,U,G,Q,Z],W=[].concat(Y,[B])},71:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 1c6.075 0 11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12 5.925 1 12 1zm0 14a1.5 1.5 0 100 3 1.5 1.5 0 000-3zm-1-2h2V6h-2v7z",fill:"currentColor"});t.a=function SvgWarningNotice(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},724:function(e,t,n){"use strict";function r(e){return"string"==typeof e&&""!==e&&/^[A-Za-z0-9-_.]+$/.test(e)}n.d(t,"a",(function(){return r}))},73:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},759:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ClientIDTextField}));var r=n(14),i=n.n(r),a=n(10),o=n.n(a),c=n(0),s=n.n(c),u=n(1),l=n(2),g=n(4),d=n(11),f=n(118),m=n(724),p=n(129);function ClientIDTextField(t){var n=t.existingClientID,r=void 0===n?"":n,a=Object(g.useSelect)((function(e){return e(f.b).getClientID()})),c=Object(u.useState)(!1),s=i()(c,2),b=s[0],v=s[1],j=Object(u.useState)(!a||Object(m.a)(a)),M=i()(j,2),I=M[0],h=M[1],y=Object(p.a)(h,500),N=Object(g.useDispatch)(f.b).setClientID;Object(u.useEffect)((function(){a||!r||b||(N(r),v(!0))}),[a,N,r,b]);var O,k=Object(u.useCallback)((function(e){var t=e.currentTarget.value;t!==a&&N(t),y(Object(m.a)(t))}),[a,N,y]);return I||(O=Object(l.__)("A valid Client ID is required to use Sign in with Google","google-site-kit")),r&&a===r&&(O=Object(l.__)("Sign in with Google was already set up on this site. We recommend using your existing Client ID.","google-site-kit")),e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement(d.TextField,{label:Object(l.__)("Client ID","google-site-kit"),className:o()("googlesitekit-text-field-client-id",{"mdc-text-field--error":!I}),helperText:O,value:a,onChange:k,maxLength:120,outlined:!0}))}ClientIDTextField.propTypes={existingClientID:s.a.string}}).call(this,n(3))},76:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(36),i=n.n(r),a=n(88),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===i()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},760:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AnyoneCanRegisterDisabledNotice}));var r=n(42),i=n(1),a=n(2),o=n(4),c=n(7),s=n(13),u=n(23),l=n(21),g=n(35);function AnyoneCanRegisterDisabledNotice(){var t=Object(u.e)(),n=Object(o.useSelect)((function(e){return e(c.a).hasCapability(c.K)})),d=Object(o.useSelect)((function(e){return e(s.c).isMultisite()})),f=Object(o.useSelect)((function(e){return e(s.c).getAdminSettingsURL()})),m=Object(o.useDispatch)(c.a).dismissItem;return!0===Object(o.useSelect)((function(e){return e(c.a).isItemDismissed("sign-in-with-google-anyone-can-register-notice")}))?null:e.createElement("div",{className:"googlesitekit-registration-disabled-notice"},e.createElement(g.a,{type:g.a.TYPES.INFO,description:Object(r.a)(Object(a.sprintf)(/* translators: %1$s: Setting name, %2$s: Sign in with Google service name */
Object(a.__)("Enable the %1$s setting to allow your visitors to create an account using the %2$s button. <br/>Visit <a>WordPress settings</a> to manage this setting.","google-site-kit"),d?Object(a.__)("“Allow new registrations”","google-site-kit"):Object(a.__)("“Anyone can register”","google-site-kit"),Object(a._x)("Sign in with Google","Service name","google-site-kit")),{a:!n&&d?e.createElement("span",null):e.createElement(l.a,{key:"link",href:f}),br:t===u.d||t===u.a?e.createElement("br",null):e.createElement(i.Fragment,null)}),dismissButton:{label:Object(a.__)("Got it","google-site-kit"),onClick:function(){return m("sign-in-with-google-anyone-can-register-notice")}}}))}}).call(this,n(3))},761:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RegistrationDisabledNotice}));var r=n(2),i=n(4),a=n(13),o=n(35);function RegistrationDisabledNotice(){var t=Object(i.useSelect)((function(e){return e(a.c).isMultisite()})),n=Object(i.useSelect)((function(e){return e(a.c).getAdminSettingsURL()}));return e.createElement(o.a,{type:o.a.TYPES.WARNING,description:Object(r.sprintf)(/* translators: %1$s: Setting name, %2$s: Sign in with Google service name */
Object(r.__)("Using “One Tap sign in on all pages” will cause errors for users without an account. Enable “%1$s” in WordPress settings to allow anyone to use %2$s.","google-site-kit"),t?Object(r.__)("Allow new registrations","google-site-kit"):Object(r.__)("Anyone can register","google-site-kit"),Object(r._x)("Sign in with Google","Service name","google-site-kit")),ctaButton:{label:Object(r.__)("Manage settings","google-site-kit"),href:n,external:!0}})}}).call(this,n(3))},78:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(0),i=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:i}},n)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,n(3))},787:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#sign-in-with-google_svg__clip0_27_1152)"},r.createElement("path",{d:"M8.865 24.172L7.472 29.37l-5.088.108A19.91 19.91 0 010 20c0-3.317.807-6.444 2.236-9.198h.001l4.531.83 1.985 4.504A11.888 11.888 0 008.11 20c0 1.468.266 2.875.754 4.172z",fill:"#FBBB00"}),r.createElement("path",{d:"M39.65 16.264c.23 1.21.35 2.459.35 3.736 0 1.432-.15 2.828-.438 4.176a19.996 19.996 0 01-7.041 11.42h-.001l-5.707-.292-.808-5.041a11.92 11.92 0 005.129-6.087H20.439v-7.912H39.65z",fill:"#518EF8"}),r.createElement("path",{d:"M32.52 35.596h.001A19.916 19.916 0 0120.001 40c-7.617 0-14.24-4.257-17.617-10.522l6.481-5.305c1.69 4.507 6.037 7.716 11.135 7.716 2.191 0 4.244-.592 6.006-1.626l6.514 5.333z",fill:"#28B446"}),r.createElement("path",{d:"M32.766 4.604l-6.48 5.305A11.822 11.822 0 0020 8.11c-5.213 0-9.643 3.356-11.247 8.025l-6.516-5.334C5.564 4.385 12.27 0 20 0c4.853 0 9.302 1.729 12.766 4.604z",fill:"#F14336"})),o=r.createElement("defs",null,r.createElement("clipPath",{id:"sign-in-with-google_svg__clip0_27_1152"},r.createElement("path",{fill:"#fff",d:"M0 0h40v40H0z"})));t.a=function SvgSignInWithGoogle(e){return r.createElement("svg",i({viewBox:"0 0 40 40",fill:"none"},e),a,o)}},79:function(e,t,n){"use strict";(function(e){var r=n(20),i=n.n(r),a=n(22),o=n.n(a),c=n(10),s=n.n(c),u=n(0),l=n.n(u),g=n(1),d=Object(g.forwardRef)((function(t,n){var r=t.label,a=t.className,c=t.hasLeftSpacing,u=void 0!==c&&c,l=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",i()({ref:n},l,{className:s()("googlesitekit-badge",a,{"googlesitekit-badge--has-left-spacing":u})}),r)}));d.displayName="Badge",d.propTypes={label:l.a.string.isRequired,hasLeftSpacing:l.a.bool},t.a=d}).call(this,n(3))},85:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(115);function i(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),i=e.replace(n.origin,"");if(i.length<t)return i;var a=i.length-Math.floor(t)+1;return"…"+i.substr(a)}},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="search-console"},87:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return N})),n.d(t,"d",(function(){return O})),n.d(t,"e",(function(){return D})),n.d(t,"c",(function(){return S})),n.d(t,"b",(function(){return T}));var r=n(14),i=n.n(r),a=n(36),o=n.n(a),c=n(5),s=n.n(c),u=n(22),l=n.n(u),g=n(15),d=n(66),f=n.n(d),m=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=I(e,t),r=n.formatUnit,i=n.formatDecimal;try{return r()}catch(e){return i()}},j=function(e){var t=M(e),n=t.hours,r=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(i):"".concat(n,":").concat(r,":").concat(i)},M=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},I=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=M(e),r=n.hours,i=n.minutes,a=n.seconds;return{hours:r,minutes:i,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=b(b({unitDisplay:void 0===n?"short":n},l()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?D(a,b(b({},o),{},{unit:"second"})):Object(m.sprintf)(/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?D(a,b(b({},o),{},{unit:"second"})):"",i?D(i,b(b({},o),{},{unit:"minute"})):"",r?D(r,b(b({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(m.sprintf)(
// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(m.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(m.sprintf)(
// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(m.__)("%dm","google-site-kit"),i),o=Object(m.sprintf)(
// translators: %s: number of hours with "h" as the abbreviated unit.
Object(m.__)("%dh","google-site-kit"),r);return Object(m.sprintf)(/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",i?n:"",r?o:"").trim()}}},h=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},y=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(m.sprintf)(
// translators: %s: an abbreviated number in millions.
Object(m.__)("%sM","google-site-kit"),D(h(e),e%10==0?{}:t)):1e4<=e?Object(m.sprintf)(
// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),D(h(e))):1e3<=e?Object(m.sprintf)(
// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),D(h(e),e%10==0?{}:t)):D(e,{signDisplay:"never",maximumFractionDigits:1})};function N(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(g.isPlainObject)(e)&&(t=b({},e)),t}function O(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(g.isFinite)(e)?e:Number(e),Object(g.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=N(t),r=n.style,i=void 0===r?"metric":r;return"metric"===i?y(e):"duration"===i?v(e,n):"durationISO"===i?j(e):D(e,n)}var k=f()(console.warn),D=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,a=l()(t,["locale"]);try{return new Intl.NumberFormat(r,a).format(e)}catch(t){k("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],u={},g=0,d=Object.entries(a);g<d.length;g++){var f=i()(d[g],2),m=f[0],p=f[1];c[m]&&p===c[m]||(s.includes(m)||(u[m]=p))}try{return new Intl.NumberFormat(r,u).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,i=t.style,a=void 0===i?"long":i,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:a,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var u=Object(m.__)(", ","google-site-kit");return e.join(u)},T=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(g.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},88:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(160),i=n.n(r)()(e)}).call(this,n(28))},89:function(e,t,n){"use strict";(function(e){var r=n(0),i=n.n(r),a=n(10),o=n.n(a);function ChangeArrow(t){var n=t.direction,r=t.invertColor,i=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:i,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(3))},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return i.b})),n.d(t,"J",(function(){return i.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return l})),n.d(t,"m",(function(){return g.a})),n.d(t,"B",(function(){return g.d})),n.d(t,"C",(function(){return g.e})),n.d(t,"y",(function(){return g.c})),n.d(t,"r",(function(){return g.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return b})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return N})),n.d(t,"c",(function(){return O})),n.d(t,"e",(function(){return k})),n.d(t,"b",(function(){return D})),n.d(t,"a",(function(){return S})),n.d(t,"f",(function(){return T})),n.d(t,"n",(function(){return E})),n.d(t,"w",(function(){return w})),n.d(t,"p",(function(){return A})),n.d(t,"G",(function(){return _})),n.d(t,"s",(function(){return z})),n.d(t,"v",(function(){return x})),n.d(t,"k",(function(){return C})),n.d(t,"o",(function(){return L.b})),n.d(t,"h",(function(){return L.a})),n.d(t,"t",(function(){return P.b})),n.d(t,"q",(function(){return P.a})),n.d(t,"A",(function(){return P.c})),n.d(t,"x",(function(){return R})),n.d(t,"u",(function(){return U})),n.d(t,"E",(function(){return Q})),n.d(t,"D",(function(){return Z.a})),n.d(t,"g",(function(){return Y})),n.d(t,"L",(function(){return W})),n.d(t,"l",(function(){return H}));var r=n(15),i=n(40),a=n(76),o=n(36),c=n.n(o),s=n(103),u=n.n(s),l=function(e){return u()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var i=t[r];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),n[r]=i})),n}(e)))};n(104);var g=n(87);function d(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function m(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,r=[d,f,m];n<r.length;n++){t=(0,r[n])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},j=n(14),M=n.n(j),I=n(12),h=n.n(I),y=n(2),N="Invalid dateString parameter, it must be a string.",O='Invalid date range, it must be a string with the format "last-x-days".',k=60,D=60*k,S=24*D,T=7*S;function E(){var e=function(e){return Object(y.sprintf)(/* translators: %s: number of days */
Object(y._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function w(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function A(e){h()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function _(e){h()(w(e),N);var t=e.split("-"),n=M()(t,3),r=n[0],i=n[1],a=n[2];return new Date(r,i-1,a)}function z(e,t){return A(C(e,t*S))}function x(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function C(e,t){h()(w(e)||Object(r.isDate)(e)&&!isNaN(e),N);var n=w(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var L=n(107),P=n(85);function R(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function U(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var B=n(27),G=n.n(B),Q=function(e){return Array.isArray(e)?G()(e).sort():e},Z=n(92);function Y(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var W=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},H=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(14),i=n.n(r),a=n(196),o=n(138),c={},s=void 0===e?null:e,u=function(){return[e.innerWidth,e.innerHeight]},l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,l=void 0===r?0:r,g=e.initialHeight,d=void 0===g?0:g,f=Object(a.a)("undefined"==typeof document?[l,d]:u,t,n),m=i()(f,2),p=m[0],b=m[1],v=function(){return b(u)};return Object(o.a)(s,"resize",v),Object(o.a)(s,"orientationchange",v),p},g=function(e){return l(e)[0]}}).call(this,n(28))},92:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(12),i=n.n(r),a=function(e,t){var n=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},927:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ButtonTextSelect}));var r=n(1),i=n(2),a=n(4),o=n(118),c=n(11),s=n(18),u=n(9),l=n(129);function ButtonTextSelect(){var t=Object(s.a)(),n=Object(a.useSelect)((function(e){return e(o.b).getText()})),g=Object(a.useDispatch)(o.b).setText,d=Object(r.useCallback)((function(){Object(u.I)("".concat(t,"_sign-in-with-google-settings"),"change_button_text")}),[t]),f=Object(l.a)(d,500),m=Object(r.useCallback)((function(e,t){var r=t.dataset.value;r!==n&&(g(r),f())}),[n,g,f]);return e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement(c.Select,{className:"googlesitekit-sign-in-with-google__select-button-text",label:Object(i.__)("Button text","google-site-kit"),value:n,onEnhancedChange:m,enhanced:!0,outlined:!0},o.d.map((function(t){return e.createElement(c.Option,{key:t.value,value:t.value},t.label)}))))}}).call(this,n(3))},928:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ButtonThemeSelect}));var r=n(1),i=n(2),a=n(4),o=n(118),c=n(11),s=n(18),u=n(9),l=n(129);function ButtonThemeSelect(){var t=Object(s.a)(),n=Object(a.useSelect)((function(e){return e(o.b).getTheme()})),g=Object(a.useDispatch)(o.b).setTheme,d=Object(r.useCallback)((function(){Object(u.I)("".concat(t,"_sign-in-with-google-settings"),"change_button_theme")}),[t]),f=Object(l.a)(d,500),m=Object(r.useCallback)((function(e,t){var r=t.dataset.value;r!==n&&(g(r),f())}),[n,g,f]);return e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement(c.Select,{className:"googlesitekit-sign-in-with-google__select-button-theme",label:Object(i.__)("Button theme","google-site-kit"),value:n,onEnhancedChange:m,enhanced:!0,outlined:!0},o.e.map((function(t){return e.createElement(c.Option,{key:t.value,value:t.value},t.label)}))))}}).call(this,n(3))},929:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ButtonShapeSelect}));var r=n(1),i=n(2),a=n(4),o=n(118),c=n(11),s=n(18),u=n(9),l=n(129);function ButtonShapeSelect(){var t=Object(s.a)(),n=Object(a.useSelect)((function(e){return e(o.b).getShape()})),g=Object(a.useDispatch)(o.b).setShape,d=Object(r.useCallback)((function(){Object(u.I)("".concat(t,"_sign-in-with-google-settings"),"change_button_shape")}),[t]),f=Object(l.a)(d,500),m=Object(r.useCallback)((function(e,t){var r=t.dataset.value;r!==n&&(g(r),f())}),[n,g,f]);return e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement(c.Select,{className:"googlesitekit-sign-in-with-google__select-button-shape",label:Object(i.__)("Button shape","google-site-kit"),value:n,onEnhancedChange:m,enhanced:!0,outlined:!0},o.c.map((function(t){return e.createElement(c.Option,{key:t.value,value:t.value},t.label)}))))}}).call(this,n(3))},93:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return a}));var r=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},i=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function a(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},930:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AnyoneCanRegisterReadOnly}));var r=n(42),i=n(1),a=n(2),o=n(4),c=n(7),s=n(13),u=n(23),l=n(11),g=n(21);function AnyoneCanRegisterReadOnly(){var t=Object(u.e)(),n=Object(o.useSelect)((function(e){return e(s.c).getAnyoneCanRegister()})),d=Object(o.useSelect)((function(e){return e(c.a).hasCapability(c.K)})),f=Object(o.useSelect)((function(e){return e(s.c).isMultisite()})),m=Object(o.useSelect)((function(e){return e(s.c).getAdminSettingsURL()}));return e.createElement("div",{className:"googlesitekit-settings-module__fields-group googlesitekit-settings-module__fields-group--read-only"},e.createElement("span",null,Object(a.__)("User registration","google-site-kit")),n&&e.createElement(l.HelperText,{persistent:!0},Object(r.a)(Object(a.sprintf)(/* translators: %s: Sign in with Google service name */
Object(a.__)("Users can create new accounts on this site using %s. <br/>Visit <a>WordPress settings</a> to manage this membership setting.","google-site-kit"),Object(a._x)("Sign in with Google","Service name","google-site-kit")),{a:!d&&f?e.createElement("span",null):e.createElement(g.a,{key:"link",href:m}),br:t!==u.b?e.createElement("br",null):e.createElement(i.Fragment,null)})),!1===n&&e.createElement(l.HelperText,{persistent:!0},Object(a.sprintf)(/* translators: %s: Sign in with Google service name */
Object(a.__)("Only existing users can use %s to access their accounts.","google-site-kit"),Object(a._x)("Sign in with Google","Service name","google-site-kit"))))}}).call(this,n(3))},931:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNotice}));var r=n(1),i=n(4),a=n(13),o=n(761),c=n(760),s=n(118);function SettingsNotice(){var t=Object(i.useSelect)((function(e){return e(a.c).getAnyoneCanRegister()})),n=Object(i.useSelect)((function(e){return e(s.b).getOneTapEnabled()})),u=Object(i.useSelect)((function(e){return e(s.b).getOneTapOnAllPages()})),l=Object(i.useSelect)((function(e){return e(s.b).getIsWooCommerceActive()})),g=Object(i.useSelect)((function(e){return e(s.b).getIsWooCommerceRegistrationEnabled()})),d=n&&u&&!1===t&&!1===l;return l&&(d=n&&u&&!1===t&&!1===g),e.createElement(r.Fragment,null,!1===t&&!d&&e.createElement(c.a,null),d&&e.createElement(o.a,null))}}).call(this,n(3))},932:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OneTapToggles}));var r=n(1),i=n(42),a=n(2),o=n(11),c=n(4),s=n(118),u=n(21),l=n(18),g=n(9),d=n(13);function OneTapToggles(){var t=Object(l.a)(),n=Object(c.useSelect)((function(e){return e(s.b).getOneTapEnabled()})),f=Object(c.useSelect)((function(e){return e(s.b).getOneTapOnAllPages()})),m=Object(c.useDispatch)(s.b),p=m.setOneTapEnabled,b=m.setOneTapOnAllPages,v=Object(r.useCallback)((function(){Object(g.I)("".concat(t,"_sign-in-with-google-settings"),n?"disable_one_tap":"enable_one_tap")}),[t,n]),j=Object(r.useCallback)((function(){Object(g.I)("".concat(t,"_sign-in-with-google-settings"),f?"disable_one_tap_on_all_pages":"enable_one_tap_on_all_pages")}),[t,f]),M=Object(r.useCallback)((function(){p(!n),v()}),[n,p,v]),I=Object(r.useCallback)((function(){b(!f),j()}),[f,b,j]),h=Object(c.useSelect)((function(e){return e(d.c).getDocumentationLinkURL("sign-in-with-google-one-tap")}));return e.createElement(r.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-module__fields-group googlesitekit-settings-module__one-tap"},e.createElement(o.Switch,{label:Object(a.__)("Enable One Tap sign in","google-site-kit"),checked:n,onClick:M,hideLabel:!1}),e.createElement(o.HelperText,{persistent:!0},Object(i.a)(Object(a.__)("One Tap allows users to sign in or sign up with one click offering smooth user entry points without redirecting to a dedicated sign in and sign up page. <a>Learn more</a>","google-site-kit"),{a:e.createElement(u.a,{key:"link",href:h,external:!0})}))),e.createElement("div",{className:"googlesitekit-settings-module__fields-group googlesitekit-settings-module__one-tap-on-all-pages"},e.createElement(o.Switch,{label:Object(a.__)("One Tap enabled on all pages","google-site-kit"),checked:!!f,onClick:I,disabled:!n,hideLabel:!1}),e.createElement(o.HelperText,{persistent:!0},f?Object(a.__)("One Tap will be available on every page on this website.","google-site-kit"):Object(a.__)("One Tap will only appear on existing login pages.","google-site-kit"))))}}).call(this,n(3))},933:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return Preview}));var i=n(14),a=n.n(i),o=n(1),c=n(2),s=n(4),u=n(118),l=n(9);function Preview(){var t=Object(o.useState)(!1),n=a()(t,2),i=n[0],g=n[1],d=Object(o.useRef)(),f=Object(s.useSelect)((function(e){return e(u.b).getShape()})),m=Object(s.useSelect)((function(e){return e(u.b).getText()})),p=Object(s.useSelect)((function(e){return e(u.b).getTheme()}));return Object(o.useEffect)((function(){var t=document.createElement("script"),n=function(){g(!0),e.google.accounts.id.initialize({client_id:"notrealclientid"})};return t.src="https://accounts.google.com/gsi/client?hl=".concat(Object(l.r)()),t.addEventListener("load",n),document.body.appendChild(t),function(){g(!1),t.removeEventListener("load",n),document.body.removeChild(t)}}),[g]),Object(o.useEffect)((function(){i&&e.google.accounts.id.renderButton(d.current,{text:m,theme:p,shape:f})}),[i,d,m,p,f]),r.createElement("div",{className:"googlesitekit-sign-in-with-google__preview"},r.createElement("p",{className:"googlesitekit-sign-in-with-google__preview--label"},Object(c.__)("Preview","google-site-kit")),r.createElement("div",{ref:d}),r.createElement("div",{className:"googlesitekit-sign-in-with-google__preview--protector"}))}}).call(this,n(28),n(3))},97:function(e,t,n){"use strict";var r=n(1);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}}},[[1304,1,0]]]);