(globalThis.webpackChunkwebpackWcBlocksFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksFrontendJsonp||[]).push([[490],{3434:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7723),n=r(4656),o=(r(8502),r(790));const a=({name:e,count:t})=>(0,o.jsxs)(o.Fragment,{children:[e,null!==t&&Number.isFinite(t)&&(0,o.jsx)(n.Label,{label:t.toString(),screenReaderLabel:(0,s.sprintf)(/* translators: %s number of products. */ /* translators: %s number of products. */
(0,s._n)("%s product","%s products",t,"woocommerce"),t),wrapperElement:"span",wrapperProps:{className:"wc-filter-element-label-list-count"}})]})},3076:(e,t,r)=>{"use strict";r.d(t,{A:()=>n}),r(9300);var s=r(790);const n=({children:e})=>(0,s.jsx)("div",{className:"wc-block-filter-title-placeholder",children:e})},4559:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(7723),n=r(4921),o=r(4656),a=(r(7165),r(790));const l=({className:e,
/* translators: Reset button text for filters. */
label:t=(0,s.__)("Reset","woocommerce"),onClick:r,screenReaderLabel:l=(0,s.__)("Reset filter","woocommerce")})=>(0,a.jsx)("button",{className:(0,n.A)("wc-block-components-filter-reset-button",e),onClick:r,children:(0,a.jsx)(o.Label,{label:t,screenReaderLabel:l})})},18:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(7723),n=r(4921),o=r(4656),a=(r(874),r(790));const l=({className:e,isLoading:t,disabled:r,
/* translators: Submit button text for filters. */
label:l=(0,s.__)("Apply","woocommerce"),onClick:i,screenReaderLabel:c=(0,s.__)("Apply filter","woocommerce")})=>(0,a.jsx)("button",{type:"submit",className:(0,n.A)("wp-block-button__link","wc-block-filter-submit-button","wc-block-components-filter-submit-button",{"is-loading":t},e),disabled:r,onClick:i,children:(0,a.jsx)(o.Label,{label:l,screenReaderLabel:c})})},4467:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(4642),n=r(4921),o=(r(4357),r(790));const a=({className:e,style:t,suggestions:r,multiple:a=!0,saveTransform:l=e=>e.trim().replace(/\s/g,"-"),messages:i={},validateInput:c=e=>r.includes(e),label:u="",...d})=>(0,o.jsx)("div",{className:(0,n.A)("wc-blocks-components-form-token-field-wrapper",e,{"single-selection":!a}),style:t,children:(0,o.jsx)(s.A,{label:u,__experimentalExpandOnFocus:!0,__experimentalShowHowTo:!1,__experimentalValidateInput:c,saveTransform:l,maxLength:a?void 0:1,suggestions:r,messages:i,...d})})},5168:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(6087),n=r(4347),o=r(3993),a=r(9456),l=r(4556),i=r(9415),c=r(5479),u=r(2233);const d=({queryAttribute:e,queryPrices:t,queryStock:r,queryRating:d,queryState:m,isEditor:g=!1})=>{let p=(0,u._)();p=`${p}-collection-data`;const[b]=(0,i.dJ)(p),[y,f]=(0,i.xd)("calculate_attribute_counts",[],p),[h,_]=(0,i.xd)("calculate_price_range",null,p),[w,v]=(0,i.xd)("calculate_stock_status_counts",null,p),[x,A]=(0,i.xd)("calculate_rating_counts",null,p),S=(0,l.c)(e||{}),k=(0,l.c)(t),C=(0,l.c)(r),E=(0,l.c)(d);(0,s.useEffect)((()=>{"object"==typeof S&&Object.keys(S).length&&(y.find((e=>(0,o.objectHasProp)(S,"taxonomy")&&e.taxonomy===S.taxonomy))||f([...y,S]))}),[S,y,f]),(0,s.useEffect)((()=>{h!==k&&void 0!==k&&_(k)}),[k,_,h]),(0,s.useEffect)((()=>{w!==C&&void 0!==C&&v(C)}),[C,v,w]),(0,s.useEffect)((()=>{x!==E&&void 0!==E&&A(E)}),[E,A,x]);const[j,L]=(0,s.useState)(g),[N]=(0,n.d7)(j,200);j||L(!0);const T=(0,s.useMemo)((()=>(e=>{const t=e;return Array.isArray(e.calculate_attribute_counts)&&(t.calculate_attribute_counts=(0,a.di)(e.calculate_attribute_counts.map((({taxonomy:e,queryType:t})=>({taxonomy:e,query_type:t})))).asc(["taxonomy","query_type"])),t})(b)),[b]),{results:R,isLoading:F}=(0,c.G)({namespace:"/wc/store/v1",resourceName:"products/collection-data",query:{...m,page:void 0,per_page:void 0,orderby:void 0,order:void 0,...T},shouldSelect:N});return{data:R,isLoading:F}}},5479:(e,t,r)=>{"use strict";r.d(t,{G:()=>c});var s=r(7594),n=r(7143),o=r(6087),a=r(4556),l=r(3578),i=r(3993);const c=e=>{const{namespace:t,resourceName:r,resourceValues:c=[],query:u={},shouldSelect:d=!0}=e;if(!t||!r)throw new Error("The options object must have valid values for the namespace and the resource properties.");const m=(0,o.useRef)({results:[],isLoading:!0}),g=(0,a.c)(u),p=(0,a.c)(c),b=(0,l.a)(),y=(0,n.useSelect)((e=>{if(!d)return null;const n=e(s.COLLECTIONS_STORE_KEY),o=[t,r,g,p],a=n.getCollectionError(...o);if(a){if(!(0,i.isError)(a))throw new Error("TypeError: `error` object is not an instance of Error constructor");b(a)}return{results:n.getCollection(...o),isLoading:!n.hasFinishedResolution("getCollection",o)}}),[t,r,p,g,d,b]);return null!==y&&(m.current=y),m.current}},9415:(e,t,r)=>{"use strict";r.d(t,{dJ:()=>l,xd:()=>i});var s=r(7594),n=r(7143),o=r(6087),a=(r(923),r(2233));const l=e=>{const t=(0,a._)();e=e||t;const r=(0,n.useSelect)((t=>t(s.QUERY_STATE_STORE_KEY).getValueForQueryContext(e,void 0)),[e]),{setValueForQueryContext:l}=(0,n.useDispatch)(s.QUERY_STATE_STORE_KEY);return[r,(0,o.useCallback)((t=>{l(e,t)}),[e,l])]},i=(e,t,r)=>{const l=(0,a._)();r=r||l;const i=(0,n.useSelect)((n=>n(s.QUERY_STATE_STORE_KEY).getValueForQueryKey(r,e,t)),[r,e]),{setQueryValue:c}=(0,n.useDispatch)(s.QUERY_STATE_STORE_KEY);return[i,(0,o.useCallback)((t=>{c(r,e,t)}),[r,e,c])]}},2233:(e,t,r)=>{"use strict";r.d(t,{_:()=>o});var s=r(6087);const n=(0,s.createContext)("page"),o=()=>(0,s.useContext)(n);n.Provider},9464:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(6087);function n(e,t){const r=(0,s.useRef)();return(0,s.useEffect)((()=>{r.current===e||t&&!t(e,r.current)||(r.current=e)}),[e,t]),r.current}},4556:(e,t,r)=>{"use strict";r.d(t,{c:()=>a});var s=r(6087),n=r(923),o=r.n(n);function a(e){const t=(0,s.useRef)(e);return o()(e,t.current)||(t.current=e),t.current}},41:(e,t,r)=>{"use strict";r.d(t,{p:()=>c});var s=r(4921),n=r(3993),o=r(7356),a=r(9786);function l(e={}){const t={};return(0,a.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function i(e,t){return e&&t?`has-${(0,o.c)(t)}-${e}`:""}const c=e=>{const t=(e=>{const t=(0,n.isObject)(e)?e:{style:{}};let r=t.style;return(0,n.isString)(r)&&(r=JSON.parse(r)||{}),(0,n.isObject)(r)||(r={}),{...t,style:r}})(e),r=function(e){const{backgroundColor:t,textColor:r,gradient:o,style:a}=e,c=i("background-color",t),u=i("color",r),d=function(e){if(e)return`has-${e}-gradient-background`}(o),m=d||a?.color?.gradient;return{className:(0,s.A)(u,d,{[c]:!m&&!!c,"has-text-color":r||a?.color?.text,"has-background":t||a?.color?.background||o||a?.color?.gradient,"has-link-color":(0,n.isObject)(a?.elements?.link)?a?.elements?.link?.color:void 0}),style:l({color:a?.color||{}})}}(t),o=function(e){const t=e.style?.border||{};return{className:function(e){const{borderColor:t,style:r}=e,n=t?i("border-color",t):"";return(0,s.A)({"has-border-color":!!t||!!r?.border?.color,[n]:!!n})}(e),style:l({border:t})}}(t),a=function(e){return{className:void 0,style:l({spacing:e.style?.spacing||{}})}}(t),c=(e=>{const t=(0,n.isObject)(e.style.typography)?e.style.typography:{},r=(0,n.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:r,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}})(t);return{className:(0,s.A)(c.className,r.className,o.className,a.className),style:{...c.style,...r.style,...o.style,...a.style}}}},3578:(e,t,r)=>{"use strict";r.d(t,{a:()=>n});var s=r(6087);const n=()=>{const[,e]=(0,s.useState)();return(0,s.useCallback)((t=>{e((()=>{throw t}))}),[])}},7380:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>V});var s=r(4921),n=r(41),o=r(3993),a=r(7723),l=r(4556),i=r(9464),c=r(9415),u=r(5479),d=r(5168),m=r(6087),g=r(3434),p=r(4559),b=r(18),y=r(923),f=r.n(y),h=r(8537),_=r(5703),w=r(3832),v=r(4530),x=r(2174),A=r(1340),S=r(5009),k=r(4467),C=r(3076),E=r(3945),j=r(9180),L=r(790);const N=[{value:"preview-1",formattedValue:"preview-1",name:"Blue",label:(0,L.jsx)(g.A,{name:"Blue",count:3}),textLabel:"Blue (3)"},{value:"preview-2",formattedValue:"preview-2",name:"Green",label:(0,L.jsx)(g.A,{name:"Green",count:3}),textLabel:"Green (3)"},{value:"preview-3",formattedValue:"preview-3",name:"Red",label:(0,L.jsx)(g.A,{name:"Red",count:2}),textLabel:"Red (2)"}],T={count:0,has_archives:!0,id:0,label:"Preview",name:"preview",order:"menu_order",parent:0,taxonomy:"preview",type:""};r(2145);const R=JSON.parse('{"uK":{"Do":{"A":"or"},"F8":{"A":3},"Ox":{"A":"list"},"dc":{"A":"multiple"}}}'),F=e=>e.replace("pa_",""),q=(e,t=[])=>{const r={};t.forEach((e=>{const{attribute:t,slug:s,operator:n}=e,o=F(t),a=s.join(","),l=`${S.xB}${o}`,i="in"===n?"or":"and";r[`${S.nD}${o}`]=a,r[l]=i}));const s=(0,w.removeQueryArgs)(e,...Object.keys(r));return(0,w.addQueryArgs)(s,r)},O=e=>{if(e){const t=(0,S.Vf)(`filter_${e.name}`);return("string"==typeof t?t.split(","):[]).map((e=>encodeURIComponent(e).toLowerCase()))}return[]},I=e=>e.trim().replace(/\s/g,"-").replace(/_/g,"-").replace(/-+/g,"-").replace(/[^a-zA-Z0-9-]/g,"");var Q=r(4656);const B=({isLoading:e=!1,options:t,checked:r,onChange:s})=>e?(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)("span",{className:"is-loading"}),(0,L.jsx)("span",{className:"is-loading"})]}):(0,L.jsx)(Q.CheckboxList,{className:"wc-block-attribute-filter-list",options:t,checked:r,onChange:s,isLoading:e,isDisabled:e});var P=r(5179);const $=({attributes:e,isEditor:t=!1,getNotice:r=()=>null})=>{const n=(0,_.getSettingWithCoercion)("hasFilterableProducts",!1,o.isBoolean),y=(0,_.getSettingWithCoercion)("isRenderingPhpTemplate",!1,o.isBoolean),R=(0,_.getSettingWithCoercion)("pageUrl",window.location.href,o.isString),[Q,$]=(0,m.useState)(!1),V=e.isPreview&&!e.attributeId?T:(0,E.WK)(e.attributeId),D=(0,m.useMemo)((()=>O(V)),[V]),[K,W]=(0,m.useState)(D),[U,Y]=(0,m.useState)((0,A.I)()),[H,J]=(0,m.useState)(e.isPreview&&!e.attributeId?N:[]),[z]=(0,c.dJ)(),[G,M]=(0,c.xd)("attributes",[]),{results:Z,isLoading:X}=(0,u.G)({namespace:"/wc/store/v1",resourceName:"products/attributes/terms",resourceValues:[V?.id||0],shouldSelect:e.attributeId>0,query:{orderby:V?.orderby||"menu_order"}}),ee=(0,_.getSettingWithCoercion)("queryState",{},o.isObject),{data:te,isLoading:re}=(0,d.A)({queryAttribute:{taxonomy:V?.taxonomy||"",queryType:e.queryType},queryState:{...ee,...z},isEditor:t}),se=(0,m.useCallback)((e=>(0,o.objectHasProp)(te,"attribute_counts")&&Array.isArray(te.attribute_counts)?te.attribute_counts.find((({term:t})=>t===e)):null),[te]);(0,m.useEffect)((()=>{if(X||re)return;if(!Array.isArray(Z))return;const t=Z.map((t=>{const r=se(t.id);if(!(r||K.includes(t.slug)||(s=t.slug,z?.attributes&&z.attributes.some((({attribute:e,slug:t=[]})=>e===V?.taxonomy&&t.includes(s))))))return null;var s;const n=r?r.count:0;return{formattedValue:I(t.slug),value:t.slug,name:(0,h.decodeEntities)(t.name),label:(0,L.jsx)(g.A,{name:(0,h.decodeEntities)(t.name),count:e.showCounts?n:null}),textLabel:e.showCounts?`${(0,h.decodeEntities)(t.name)} (${n})`:(0,h.decodeEntities)(t.name)}})).filter((e=>!!e));J(t),Y((0,A.I)())}),[V?.taxonomy,Z,X,e.showCounts,re,se,K,z.attributes]);const ne=(0,m.useCallback)((e=>Array.isArray(Z)?Z.reduce(((t,r)=>(e.includes(r.slug)&&t.push(r),t)),[]):[]),[Z]),oe=(0,m.useCallback)(((e,t=!1)=>{if(e=e.map((e=>({...e,slug:e.slug.map((e=>decodeURIComponent(e)))}))),t){if(!V?.taxonomy)return;const t=Object.keys((0,w.getQueryArgs)(window.location.href)),r=F(V.taxonomy),s=t.reduce(((e,t)=>t.includes(S.xB+r)||t.includes(S.nD+r)?(0,w.removeQueryArgs)(e,t):e),window.location.href),n=q(s,e);(0,S.CH)(n)}else{const t=q(R,e);((e,t)=>{const r=Object.entries(t).reduce(((e,[t,r])=>t.includes("query_type")?e:{...e,[t]:r}),{});return Object.entries(r).reduce(((t,[r,s])=>e[r]===s&&t),!0)})((0,w.getQueryArgs)(window.location.href),(0,w.getQueryArgs)(t))||(0,S.CH)(t)}}),[R,V?.taxonomy]),ae=t=>{const r=(0,j.u)(G,M,V,ne(t),"or"===e.queryType?"in":"and");oe(r,0===t.length)},le=(0,m.useCallback)(((r,s=!1)=>{t||(W(r),!s&&e.showFilterButton||(0,j.u)(G,M,V,ne(r),"or"===e.queryType?"in":"and"))}),[t,W,G,M,V,ne,e.queryType,e.showFilterButton]),ie=(0,m.useMemo)((()=>(0,o.isAttributeQueryCollection)(G)?G.filter((({attribute:e})=>e===V?.taxonomy)).flatMap((({slug:e})=>e)):[]),[G,V?.taxonomy]),ce=(0,l.c)(ie),ue=(0,i.Z)(ce);(0,m.useEffect)((()=>{!ue||f()(ue,ce)||f()(K,ce)||le(ce)}),[K,ce,ue,le]);const de="single"!==e.selectType,me=(0,m.useCallback)((e=>{const t=K.includes(e);let r;de?(r=K.filter((t=>t!==e)),t||(r.push(e),r.sort())):r=t?[]:[e],le(r)}),[K,de,le]);(0,m.useEffect)((()=>{V&&!e.showFilterButton&&((({currentCheckedFilters:e,hasSetFilterDefaultsFromUrl:t})=>t&&0===e.length)({currentCheckedFilters:K,hasSetFilterDefaultsFromUrl:Q})?oe(G,!0):oe(G,!1))}),[Q,oe,G,V,K,e.showFilterButton]),(0,m.useEffect)((()=>{if(!Q&&!X)return D.length>0?($(!0),void le(D,!0)):void(y||$(!0))}),[V,Q,X,le,D,y]);const ge=(0,P.LY)();if(!n)return ge(!1),null;if(!V)return t?r("noAttributes"):(ge(!1),null);if(0===H.length&&!X&&t)return r("noProducts");const pe=`h${e.headingLevel}`,be=!e.isPreview&&X,ye=!e.isPreview&&re,fe=(be||ye)&&0===H.length;if(!fe&&0===H.length)return ge(!1),null;const he=de?!fe&&K.length<H.length:!fe&&0===K.length,_e=(0,L.jsx)(pe,{className:"wc-block-attribute-filter__title",children:e.heading}),we=fe?(0,L.jsx)(C.A,{children:_e}):_e;return ge(!0),(0,L.jsxs)(L.Fragment,{children:[!t&&e.heading&&we,(0,L.jsx)("div",{className:(0,s.A)("wc-block-attribute-filter",`style-${e.displayStyle}`),children:"dropdown"===e.displayStyle?(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(k.A,{label:V.label,className:(0,s.A)({"single-selection":!de,"is-loading":fe}),suggestions:H.filter((e=>!K.includes(e.value))).map((e=>e.formattedValue)),disabled:fe,placeholder:(0,a.sprintf)(/* translators: %s attribute name. */ /* translators: %s attribute name. */
(0,a.__)("Select %s","woocommerce"),V.label),onChange:e=>{!de&&e.length>1&&(e=[e[e.length-1]]);const t=[e=e.map((e=>{const t=H.find((t=>t.formattedValue===e));return t?t.value:e})),K].reduce(((e,t)=>e.filter((e=>!t.includes(e)))));if(1===t.length)return me(t[0]);const r=[K,e].reduce(((e,t)=>e.filter((e=>!t.includes(e)))));1===r.length&&me(r[0])},value:K,displayTransform:e=>{const t=H.find((t=>[t.value,t.formattedValue].includes(e)));return t?t.textLabel:e},saveTransform:I,messages:{added:(0,a.sprintf)(/* translators: %s is the attribute label. */ /* translators: %s is the attribute label. */
(0,a.__)("%s filter added.","woocommerce"),V.label),removed:(0,a.sprintf)(/* translators: %s is the attribute label. */ /* translators: %s is the attribute label. */
(0,a.__)("%s filter removed.","woocommerce"),V.label),remove:(0,a.sprintf)(/* translators: %s is the attribute label. */ /* translators: %s is the attribute label. */
(0,a.__)("Remove %s filter.","woocommerce"),V.label.toLocaleLowerCase()),__experimentalInvalid:(0,a.sprintf)(/* translators: %s is the attribute label. */ /* translators: %s is the attribute label. */
(0,a.__)("Invalid %s filter.","woocommerce"),V.label.toLocaleLowerCase())}},U),he&&(0,L.jsx)(v.A,{icon:x.A,size:30})]}):(0,L.jsx)(B,{options:H,checked:K,onChange:me,isLoading:fe,isDisabled:fe})}),(0,L.jsxs)("div",{className:"wc-block-attribute-filter__actions",children:[(K.length>0||t)&&!fe&&(0,L.jsx)(p.A,{onClick:()=>{W([]),Y((0,A.I)()),Q&&ae([])},screenReaderLabel:(0,a.__)("Reset attribute filter","woocommerce")}),e.showFilterButton&&(0,L.jsx)(b.A,{className:"wc-block-attribute-filter__button",isLoading:fe,disabled:(()=>{if(be||ye)return!0;const e=O(V);return e.length===K.length&&K.every((t=>e.includes(t)))})(),onClick:()=>ae(K),screenReaderLabel:(0,a.sprintf)(/* translators: %s is the attribute label */ /* translators: %s is the attribute label */
(0,a.__)("Apply attribute filter: %s","woocommerce"),V.label)})]})]})},V=e=>{const t=(0,n.p)(e),r=(a=e,{className:(0,o.isString)(a?.className)?a.className:"",attributeId:parseInt((0,o.isString)(a?.attributeId)?a.attributeId:"0",10),showCounts:"true"===a?.showCounts,queryType:(0,o.isString)(a?.queryType)&&a.queryType||R.uK.Do.A,heading:(0,o.isString)(a?.heading)?a.heading:"",headingLevel:(0,o.isString)(a?.headingLevel)&&parseInt(a.headingLevel,10)||R.uK.F8.A,displayStyle:(0,o.isString)(a?.displayStyle)&&a.displayStyle||R.uK.Ox.A,showFilterButton:"true"===a?.showFilterButton,selectType:(0,o.isString)(a?.selectType)&&a.selectType||R.uK.dc.A,isPreview:!1});var a;return(0,L.jsx)("div",{className:(0,s.A)((0,o.isString)(e.className)?e.className:"",t.className),style:t.style,children:(0,L.jsx)($,{isEditor:!1,attributes:r})})}},9180:(e,t,r)=>{"use strict";r.d(t,{$:()=>n,u:()=>o});var s=r(9456);const n=(e=[],t,r,n="")=>{const o=e.filter((e=>e.attribute===r.taxonomy)),a=o.length?o[0]:null;if(!(a&&a.slug&&Array.isArray(a.slug)&&a.slug.includes(n)))return;const l=a.slug.filter((e=>e!==n)),i=e.filter((e=>e.attribute!==r.taxonomy));l.length>0&&(a.slug=l.sort(),i.push(a)),t((0,s.di)(i).asc("attribute"))},o=(e=[],t,r,n=[],o="in")=>{if(!r||!r.taxonomy)return[];const a=e.filter((e=>e.attribute!==r.taxonomy));return 0===n.length?t(a):(a.push({attribute:r.taxonomy,operator:o,slug:n.map((({slug:e})=>e)).sort()}),t((0,s.di)(a).asc("attribute"))),a}},3945:(e,t,r)=>{"use strict";r.d(t,{OJ:()=>a,WK:()=>o});var s=r(5703);r(3993),r(7143);const n=(0,s.getSetting)("attributes",[]).reduce(((e,t)=>{const r=(s=t)&&s.attribute_name?{id:parseInt(s.attribute_id,10),name:s.attribute_name,taxonomy:"pa_"+s.attribute_name,label:s.attribute_label,orderby:s.attribute_orderby}:null;var s;return r&&r.id&&e.push(r),e}),[]),o=e=>{if(e)return n.find((t=>t.id===e))},a=e=>{if(e)return n.find((t=>t.taxonomy===e))}},5009:(e,t,r)=>{"use strict";r.d(t,{CH:()=>u,Q:()=>d,Vf:()=>c,nD:()=>i,xB:()=>l});var s=r(3832),n=r(5703),o=r(3993);const a=(0,n.getSettingWithCoercion)("isRenderingPhpTemplate",!1,o.isBoolean),l="query_type_",i="filter_";function c(e){return window?(0,s.getQueryArg)(window.location.href,e):null}function u(e){if(a){const t=new URL(e);t.pathname=t.pathname.replace(/\/page\/[0-9]+/i,""),t.searchParams.delete("paged"),t.searchParams.forEach(((e,r)=>{r.match(/^query(?:-[0-9]+)?-page$/)&&t.searchParams.delete(r)})),window.location.href=t.href}else window.history.replaceState({},"",e)}const d=e=>{const t=(0,s.getQueryArgs)(e);return(0,s.addQueryArgs)(e,t)}},1340:(e,t,r)=>{"use strict";function s(){return Math.floor(Math.random()*Date.now())}r.d(t,{I:()=>s})},8502:()=>{},9300:()=>{},7165:()=>{},874:()=>{},4357:()=>{},2145:()=>{}}]);