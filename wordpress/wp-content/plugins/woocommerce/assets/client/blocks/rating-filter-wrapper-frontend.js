(globalThis.webpackChunkwebpackWcBlocksFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksFrontendJsonp||[]).push([[21],{4559:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(7723),o=r(4921),n=r(4656),a=(r(7165),r(790));const l=({className:e,
/* translators: Reset button text for filters. */
label:t=(0,s.__)("Reset","woocommerce"),onClick:r,screenReaderLabel:l=(0,s.__)("Reset filter","woocommerce")})=>(0,a.jsx)("button",{className:(0,o.A)("wc-block-components-filter-reset-button",e),onClick:r,children:(0,a.jsx)(n.Label,{label:t,screenReaderLabel:l})})},18:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(7723),o=r(4921),n=r(4656),a=(r(874),r(790));const l=({className:e,isLoading:t,disabled:r,
/* translators: Submit button text for filters. */
label:l=(0,s.__)("Apply","woocommerce"),onClick:c,screenReaderLabel:i=(0,s.__)("Apply filter","woocommerce")})=>(0,a.jsx)("button",{type:"submit",className:(0,o.A)("wp-block-button__link","wc-block-filter-submit-button","wc-block-components-filter-submit-button",{"is-loading":t},e),disabled:r,onClick:c,children:(0,a.jsx)(n.Label,{label:l,screenReaderLabel:i})})},4467:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(4642),o=r(4921),n=(r(4357),r(790));const a=({className:e,style:t,suggestions:r,multiple:a=!0,saveTransform:l=e=>e.trim().replace(/\s/g,"-"),messages:c={},validateInput:i=e=>r.includes(e),label:u="",...d})=>(0,n.jsx)("div",{className:(0,o.A)("wc-blocks-components-form-token-field-wrapper",e,{"single-selection":!a}),style:t,children:(0,n.jsx)(s.A,{label:u,__experimentalExpandOnFocus:!0,__experimentalShowHowTo:!1,__experimentalValidateInput:i,saveTransform:l,maxLength:a?void 0:1,suggestions:r,messages:c,...d})})},5168:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(6087),o=r(4347),n=r(3993),a=r(9456),l=r(4556),c=r(9415),i=r(5479),u=r(2233);const d=({queryAttribute:e,queryPrices:t,queryStock:r,queryRating:d,queryState:g,isEditor:m=!1})=>{let f=(0,u._)();f=`${f}-collection-data`;const[p]=(0,c.dJ)(f),[y,b]=(0,c.xd)("calculate_attribute_counts",[],f),[h,_]=(0,c.xd)("calculate_price_range",null,f),[w,v]=(0,c.xd)("calculate_stock_status_counts",null,f),[x,S]=(0,c.xd)("calculate_rating_counts",null,f),k=(0,l.c)(e||{}),C=(0,l.c)(t),A=(0,l.c)(r),j=(0,l.c)(d);(0,s.useEffect)((()=>{"object"==typeof k&&Object.keys(k).length&&(y.find((e=>(0,n.objectHasProp)(k,"taxonomy")&&e.taxonomy===k.taxonomy))||b([...y,k]))}),[k,y,b]),(0,s.useEffect)((()=>{h!==C&&void 0!==C&&_(C)}),[C,_,h]),(0,s.useEffect)((()=>{w!==A&&void 0!==A&&v(A)}),[A,v,w]),(0,s.useEffect)((()=>{x!==j&&void 0!==j&&S(j)}),[j,S,x]);const[E,N]=(0,s.useState)(m),[R]=(0,o.d7)(E,200);E||N(!0);const T=(0,s.useMemo)((()=>(e=>{const t=e;return Array.isArray(e.calculate_attribute_counts)&&(t.calculate_attribute_counts=(0,a.di)(e.calculate_attribute_counts.map((({taxonomy:e,queryType:t})=>({taxonomy:e,query_type:t})))).asc(["taxonomy","query_type"])),t})(p)),[p]),{results:L,isLoading:P}=(0,i.G)({namespace:"/wc/store/v1",resourceName:"products/collection-data",query:{...g,page:void 0,per_page:void 0,orderby:void 0,order:void 0,...T},shouldSelect:R});return{data:L,isLoading:P}}},5479:(e,t,r)=>{"use strict";r.d(t,{G:()=>i});var s=r(7594),o=r(7143),n=r(6087),a=r(4556),l=r(3578),c=r(3993);const i=e=>{const{namespace:t,resourceName:r,resourceValues:i=[],query:u={},shouldSelect:d=!0}=e;if(!t||!r)throw new Error("The options object must have valid values for the namespace and the resource properties.");const g=(0,n.useRef)({results:[],isLoading:!0}),m=(0,a.c)(u),f=(0,a.c)(i),p=(0,l.a)(),y=(0,o.useSelect)((e=>{if(!d)return null;const o=e(s.COLLECTIONS_STORE_KEY),n=[t,r,m,f],a=o.getCollectionError(...n);if(a){if(!(0,c.isError)(a))throw new Error("TypeError: `error` object is not an instance of Error constructor");p(a)}return{results:o.getCollection(...n),isLoading:!o.hasFinishedResolution("getCollection",n)}}),[t,r,f,m,d,p]);return null!==y&&(g.current=y),g.current}},9415:(e,t,r)=>{"use strict";r.d(t,{dJ:()=>l,xd:()=>c});var s=r(7594),o=r(7143),n=r(6087),a=(r(923),r(2233));const l=e=>{const t=(0,a._)();e=e||t;const r=(0,o.useSelect)((t=>t(s.QUERY_STATE_STORE_KEY).getValueForQueryContext(e,void 0)),[e]),{setValueForQueryContext:l}=(0,o.useDispatch)(s.QUERY_STATE_STORE_KEY);return[r,(0,n.useCallback)((t=>{l(e,t)}),[e,l])]},c=(e,t,r)=>{const l=(0,a._)();r=r||l;const c=(0,o.useSelect)((o=>o(s.QUERY_STATE_STORE_KEY).getValueForQueryKey(r,e,t)),[r,e]),{setQueryValue:i}=(0,o.useDispatch)(s.QUERY_STATE_STORE_KEY);return[c,(0,n.useCallback)((t=>{i(r,e,t)}),[r,e,i])]}},2233:(e,t,r)=>{"use strict";r.d(t,{_:()=>n});var s=r(6087);const o=(0,s.createContext)("page"),n=()=>(0,s.useContext)(o);o.Provider},9464:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(6087);function o(e,t){const r=(0,s.useRef)();return(0,s.useEffect)((()=>{r.current===e||t&&!t(e,r.current)||(r.current=e)}),[e,t]),r.current}},4556:(e,t,r)=>{"use strict";r.d(t,{c:()=>a});var s=r(6087),o=r(923),n=r.n(o);function a(e){const t=(0,s.useRef)(e);return n()(e,t.current)||(t.current=e),t.current}},41:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(4921),o=r(3993),n=r(7356),a=r(9786);function l(e={}){const t={};return(0,a.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function c(e,t){return e&&t?`has-${(0,n.c)(t)}-${e}`:""}const i=e=>{const t=(e=>{const t=(0,o.isObject)(e)?e:{style:{}};let r=t.style;return(0,o.isString)(r)&&(r=JSON.parse(r)||{}),(0,o.isObject)(r)||(r={}),{...t,style:r}})(e),r=function(e){const{backgroundColor:t,textColor:r,gradient:n,style:a}=e,i=c("background-color",t),u=c("color",r),d=function(e){if(e)return`has-${e}-gradient-background`}(n),g=d||a?.color?.gradient;return{className:(0,s.A)(u,d,{[i]:!g&&!!i,"has-text-color":r||a?.color?.text,"has-background":t||a?.color?.background||n||a?.color?.gradient,"has-link-color":(0,o.isObject)(a?.elements?.link)?a?.elements?.link?.color:void 0}),style:l({color:a?.color||{}})}}(t),n=function(e){const t=e.style?.border||{};return{className:function(e){const{borderColor:t,style:r}=e,o=t?c("border-color",t):"";return(0,s.A)({"has-border-color":!!t||!!r?.border?.color,[o]:!!o})}(e),style:l({border:t})}}(t),a=function(e){return{className:void 0,style:l({spacing:e.style?.spacing||{}})}}(t),i=(e=>{const t=(0,o.isObject)(e.style.typography)?e.style.typography:{},r=(0,o.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:r,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}})(t);return{className:(0,s.A)(i.className,r.className,n.className,a.className),style:{...i.style,...r.style,...n.style,...a.style}}}},3578:(e,t,r)=>{"use strict";r.d(t,{a:()=>o});var s=r(6087);const o=()=>{const[,e]=(0,s.useState)();return(0,s.useCallback)((t=>{e((()=>{throw t}))}),[])}},2088:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>F});var s=r(4921),o=r(41),n=r(3993),a=r(7723),l=r(195),c=r(4530),i=r(2174),u=(r(5765),r(790));const d=({className:e,rating:t,ratedProductsCount:r})=>{const o=(0,s.A)("wc-block-components-product-rating",e),n={width:t/5*100+"%"},l=(0,a.sprintf)(/* translators: %f is referring to the average rating value */ /* translators: %f is referring to the average rating value */
(0,a.__)("Rated %f out of 5","woocommerce"),t),c={__html:(0,a.sprintf)(/* translators: %s is the rating value wrapped in HTML strong tags. */ /* translators: %s is the rating value wrapped in HTML strong tags. */
(0,a.__)("Rated %s out of 5","woocommerce"),(0,a.sprintf)('<strong class="rating">%f</strong>',t))};return(0,u.jsxs)("div",{className:o,children:[(0,u.jsx)("div",{className:"wc-block-components-product-rating__stars",role:"img","aria-label":l,children:(0,u.jsx)("span",{style:n,dangerouslySetInnerHTML:c})}),null!==r?(0,u.jsxs)("span",{className:"wc-block-components-product-rating-count",children:["(",r,")"]}):null]})};var g=r(4556),m=r(9464),f=r(9415),p=r(5168),y=r(5703),b=r(923),h=r.n(b),_=r(6087),w=r(4656),v=r(18),x=r(4559),S=r(4467),k=r(3832),C=r(1340),A=r(5009);const j=[{label:(0,u.jsx)(d,{rating:5,ratedProductsCount:null},5),value:"5"},{label:(0,u.jsx)(d,{rating:4,ratedProductsCount:null},4),value:"4"},{label:(0,u.jsx)(d,{rating:3,ratedProductsCount:null},3),value:"3"},{label:(0,u.jsx)(d,{rating:2,ratedProductsCount:null},2),value:"2"},{label:(0,u.jsx)(d,{rating:1,ratedProductsCount:null},1),value:"1"}];r(6121);const E=JSON.parse('{"uK":{"Ox":{"A":"list"},"dc":{"A":"multiple"}}}'),N=e=>e.trim().replace(/\s/g,"-").replace(/_/g,"-").replace(/-+/g,"-").replace(/[^a-zA-Z0-9-]/g,"");var R=r(5179);const T="rating_filter",L=e=>(0,a.sprintf)(/* translators: %s is referring to the average rating value */ /* translators: %s is referring to the average rating value */
(0,a.__)("Rated %s out of 5 filter added.","woocommerce"),e),P=e=>(0,a.sprintf)(/* translators: %s is referring to the average rating value */ /* translators: %s is referring to the average rating value */
(0,a.__)("Rated %s out of 5 filter added.","woocommerce"),e),O=({attributes:e,isEditor:t,noRatingsNotice:r=null})=>{const o=(0,R.LY)(),b=(0,y.getSettingWithCoercion)("isRenderingPhpTemplate",!1,n.isBoolean),[E,O]=(0,_.useState)(!1),[F]=(0,f.dJ)(),{data:Q,isLoading:q}=(0,p.A)({queryRating:!0,queryState:F,isEditor:t}),[B,Y]=(0,_.useState)(e.isPreview?j:[]),H=!e.isPreview&&q&&0===B.length,I=!e.isPreview&&q,K=(0,_.useMemo)((()=>((e="filter_rating")=>{const t=(0,A.Vf)(e);return t?(0,n.isString)(t)?t.split(","):t:[]})("rating_filter")),[]),[V,$]=(0,_.useState)(K),[z,D]=(0,f.xd)("rating",K),[J,W]=(0,_.useState)((0,C.I)()),[M,U]=(0,_.useState)(!1),Z="single"!==e.selectType,G=Z?!H&&V.length<B.length:!H&&0===V.length,X=(0,_.useCallback)((e=>{t||(e&&!b&&D(e),(e=>{if(!window)return;if(0===e.length){const e=(0,k.removeQueryArgs)(window.location.href,T);return void(e!==(0,A.Q)(window.location.href)&&(0,A.CH)(e))}const t=(0,k.addQueryArgs)(window.location.href,{[T]:e.join(",")});t!==(0,A.Q)(window.location.href)&&(0,A.CH)(t)})(e))}),[t,D,b]);(0,_.useEffect)((()=>{e.showFilterButton||X(V)}),[e.showFilterButton,V,X]);const ee=(0,_.useMemo)((()=>z),[z]),te=(0,g.c)(ee),re=(0,m.Z)(te);(0,_.useEffect)((()=>{h()(re,te)||h()(V,te)||$(te)}),[V,te,re]),(0,_.useEffect)((()=>{E||(D(K),O(!0))}),[D,E,O,K]),(0,_.useEffect)((()=>{if(q||e.isPreview)return;const r=!q&&(0,n.objectHasProp)(Q,"rating_counts")&&Array.isArray(Q.rating_counts)?[...Q.rating_counts].reverse():[];if(t&&0===r.length)return Y(j),void U(!0);const s=r.filter((e=>(0,n.isObject)(e)&&Object.keys(e).length>0)).map((t=>({label:(0,u.jsx)(d,{rating:t?.rating,ratedProductsCount:e.showCounts?t?.count:null},t?.rating),value:t?.rating?.toString()})));Y(s),W((0,C.I)())}),[e.showCounts,e.isPreview,Q,q,z,t]);const se=(0,_.useCallback)((e=>{const t=V.includes(e);if(!Z){const r=t?[]:[e];return(0,l.speak)(t?P(e):L(e)),void $(r)}if(t){const t=V.filter((t=>t!==e));return(0,l.speak)(P(e)),void $(t)}const r=[...V,e].sort(((e,t)=>Number(t)-Number(e)));(0,l.speak)(L(e)),$(r)}),[V,Z]);return(q||0!==B.length)&&(0,y.getSettingWithCoercion)("hasFilterableProducts",!1,n.isBoolean)?(o(!0),(0,u.jsxs)(u.Fragment,{children:[M&&r,(0,u.jsx)("div",{className:(0,s.A)("wc-block-rating-filter",`style-${e.displayStyle}`,{"is-loading":H}),children:"dropdown"===e.displayStyle?(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(S.A,{className:(0,s.A)({"single-selection":!Z,"is-loading":H}),style:{borderStyle:"none"},suggestions:B.filter((e=>!V.includes(e.value))).map((e=>e.value)),disabled:H,placeholder:(0,a.__)("Select Rating","woocommerce"),onChange:e=>{!Z&&e.length>1&&(e=[e[e.length-1]]);const t=[e=e.map((e=>{const t=B.find((t=>t.value===e));return t?t.value:e})),V].reduce(((e,t)=>e.filter((e=>!t.includes(e)))));if(1===t.length)return se(t[0]);const r=[V,e].reduce(((e,t)=>e.filter((e=>!t.includes(e)))));1===r.length&&se(r[0])},value:V,displayTransform:e=>{const t={value:e,label:(0,u.jsx)(d,{rating:Number(e),ratedProductsCount:0},Number(e))},r=B.find((t=>t.value===e))||t,{label:s,value:o}=r;return Object.assign({},s,{toLocaleLowerCase:()=>o,substring:(e,t)=>0===e&&1===t?s:""})},saveTransform:N,messages:{added:(0,a.__)("Rating filter added.","woocommerce"),removed:(0,a.__)("Rating filter removed.","woocommerce"),remove:(0,a.__)("Remove rating filter.","woocommerce"),__experimentalInvalid:(0,a.__)("Invalid rating filter.","woocommerce")}},J),G&&(0,u.jsx)(c.A,{icon:i.A,size:30})]}):(0,u.jsx)(w.CheckboxList,{className:"wc-block-rating-filter-list",options:B,checked:V,onChange:e=>{se(e.toString())},isLoading:H,isDisabled:I})}),(0,u.jsxs)("div",{className:"wc-block-rating-filter__actions",children:[(V.length>0||t)&&!H&&(0,u.jsx)(x.A,{onClick:()=>{$([]),D([]),X([])},screenReaderLabel:(0,a.__)("Reset rating filter","woocommerce")}),e.showFilterButton&&(0,u.jsx)(v.A,{className:"wc-block-rating-filter__button",isLoading:H,disabled:H||I,onClick:()=>X(V),screenReaderLabel:(0,a.__)("Apply rating filter","woocommerce")})]})]})):(o(!1),null)},F=e=>{const t=(0,o.p)(e),r=(a=e,{showFilterButton:"true"===a?.showFilterButton,showCounts:"true"===a?.showCounts,isPreview:!1,displayStyle:(0,n.isString)(a?.displayStyle)&&a.displayStyle||E.uK.Ox.A,selectType:(0,n.isString)(a?.selectType)&&a.selectType||E.uK.dc.A});var a;return(0,u.jsx)("div",{className:(0,s.A)((0,n.isString)(e.className)?e.className:"",t.className),style:t.style,children:(0,u.jsx)(O,{isEditor:!1,attributes:r})})}},5009:(e,t,r)=>{"use strict";r.d(t,{CH:()=>u,Q:()=>d,Vf:()=>i,nD:()=>c,xB:()=>l});var s=r(3832),o=r(5703),n=r(3993);const a=(0,o.getSettingWithCoercion)("isRenderingPhpTemplate",!1,n.isBoolean),l="query_type_",c="filter_";function i(e){return window?(0,s.getQueryArg)(window.location.href,e):null}function u(e){if(a){const t=new URL(e);t.pathname=t.pathname.replace(/\/page\/[0-9]+/i,""),t.searchParams.delete("paged"),t.searchParams.forEach(((e,r)=>{r.match(/^query(?:-[0-9]+)?-page$/)&&t.searchParams.delete(r)})),window.location.href=t.href}else window.history.replaceState({},"",e)}const d=e=>{const t=(0,s.getQueryArgs)(e);return(0,s.addQueryArgs)(e,t)}},1340:(e,t,r)=>{"use strict";function s(){return Math.floor(Math.random()*Date.now())}r.d(t,{I:()=>s})},7165:()=>{},874:()=>{},4357:()=>{},5765:()=>{},6121:()=>{}}]);