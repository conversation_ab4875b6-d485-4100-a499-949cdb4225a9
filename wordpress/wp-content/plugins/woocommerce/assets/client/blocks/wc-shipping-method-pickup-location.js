(()=>{"use strict";var e={824:e=>{e.exports=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var r,o,i;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(o=r;0!=o--;)if(!e(t[o],n[o]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(o of t.entries())if(!n.has(o[0]))return!1;for(o of t.entries())if(!e(o[1],n.get(o[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(o of t.entries())if(!n.has(o[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((r=t.length)!=n.length)return!1;for(o=r;0!=o--;)if(t[o]!==n[o])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(i=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(o=r;0!=o--;)if(!Object.prototype.hasOwnProperty.call(n,i[o]))return!1;for(o=r;0!=o--;){var a=i[o];if(!e(t[a],n[a]))return!1}return!0}return t!=t&&n!=n}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const r=window.wp.element;function o(){return o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o.apply(null,arguments)}const i=window.React;var a=n.n(i);function s(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var l=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,c=s((function(e){return l.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),u=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(e){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){return e.parentNode&&e.parentNode.removeChild(e)})),this.tags=[],this.ctr=0},e}(),d=Math.abs,p=String.fromCharCode,f=Object.assign;function h(e){return e.trim()}function g(e,t,n){return e.replace(t,n)}function m(e,t){return e.indexOf(t)}function v(e,t){return 0|e.charCodeAt(t)}function b(e,t,n){return e.slice(t,n)}function y(e){return e.length}function x(e){return e.length}function w(e,t){return t.push(e),e}var k=1,C=1,S=0,_=0,E=0,R="";function D(e,t,n,r,o,i,a){return{value:e,root:t,parent:n,type:r,props:o,children:i,line:k,column:C,length:a,return:""}}function j(e,t){return f(D("",null,null,"",null,null,0),e,{length:-e.length},t)}function O(){return E=_>0?v(R,--_):0,C--,10===E&&(C=1,k--),E}function A(){return E=_<S?v(R,_++):0,C++,10===E&&(C=1,k++),E}function T(){return v(R,_)}function M(){return _}function L(e,t){return b(R,e,t)}function N(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function I(e){return k=C=1,S=y(R=e),_=0,[]}function P(e){return R="",e}function z(e){return h(L(_-1,$(91===e?e+2:40===e?e+1:e)))}function B(e){for(;(E=T())&&E<33;)A();return N(e)>2||N(E)>3?"":" "}function F(e,t){for(;--t&&A()&&!(E<48||E>102||E>57&&E<65||E>70&&E<97););return L(e,M()+(t<6&&32==T()&&32==A()))}function $(e){for(;A();)switch(E){case e:return _;case 34:case 39:34!==e&&39!==e&&$(E);break;case 40:41===e&&$(e);break;case 92:A()}return _}function U(e,t){for(;A()&&e+E!==57&&(e+E!==84||47!==T()););return"/*"+L(t,_-1)+"*"+p(47===e?e:A())}function V(e){for(;!N(T());)A();return L(e,_)}var H="-ms-",q="-moz-",G="-webkit-",W="comm",X="rule",Y="decl",K="@keyframes";function J(e,t){for(var n="",r=x(e),o=0;o<r;o++)n+=t(e[o],o,e,t)||"";return n}function Z(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case Y:return e.return=e.return||e.value;case W:return"";case K:return e.return=e.value+"{"+J(e.children,r)+"}";case X:e.value=e.props.join(",")}return y(n=J(e.children,r))?e.return=e.value+"{"+n+"}":""}function Q(e){return P(ee("",null,null,null,[""],e=I(e),0,[0],e))}function ee(e,t,n,r,o,i,a,s,l){for(var c=0,u=0,d=a,f=0,h=0,b=0,x=1,k=1,C=1,S=0,_="",E=o,R=i,D=r,j=_;k;)switch(b=S,S=A()){case 40:if(108!=b&&58==v(j,d-1)){-1!=m(j+=g(z(S),"&","&\f"),"&\f")&&(C=-1);break}case 34:case 39:case 91:j+=z(S);break;case 9:case 10:case 13:case 32:j+=B(b);break;case 92:j+=F(M()-1,7);continue;case 47:switch(T()){case 42:case 47:w(ne(U(A(),M()),t,n),l);break;default:j+="/"}break;case 123*x:s[c++]=y(j)*C;case 125*x:case 59:case 0:switch(S){case 0:case 125:k=0;case 59+u:-1==C&&(j=g(j,/\f/g,"")),h>0&&y(j)-d&&w(h>32?re(j+";",r,n,d-1):re(g(j," ","")+";",r,n,d-2),l);break;case 59:j+=";";default:if(w(D=te(j,t,n,c,u,o,s,_,E=[],R=[],d),i),123===S)if(0===u)ee(j,t,D,D,E,i,d,s,R);else switch(99===f&&110===v(j,3)?100:f){case 100:case 108:case 109:case 115:ee(e,D,D,r&&w(te(e,D,D,0,0,o,s,_,o,E=[],d),R),o,R,d,s,r?E:R);break;default:ee(j,D,D,D,[""],R,0,s,R)}}c=u=h=0,x=C=1,_=j="",d=a;break;case 58:d=1+y(j),h=b;default:if(x<1)if(123==S)--x;else if(125==S&&0==x++&&125==O())continue;switch(j+=p(S),S*x){case 38:C=u>0?1:(j+="\f",-1);break;case 44:s[c++]=(y(j)-1)*C,C=1;break;case 64:45===T()&&(j+=z(A())),f=T(),u=d=y(_=j+=V(M())),S++;break;case 45:45===b&&2==y(j)&&(x=0)}}return i}function te(e,t,n,r,o,i,a,s,l,c,u){for(var p=o-1,f=0===o?i:[""],m=x(f),v=0,y=0,w=0;v<r;++v)for(var k=0,C=b(e,p+1,p=d(y=a[v])),S=e;k<m;++k)(S=h(y>0?f[k]+" "+C:g(C,/&\f/g,f[k])))&&(l[w++]=S);return D(e,t,n,0===o?X:s,l,c,u)}function ne(e,t,n){return D(e,t,n,W,p(E),b(e,2,-2),0)}function re(e,t,n,r){return D(e,t,n,Y,b(e,0,r),b(e,r+1,-1),r)}var oe=function(e,t,n){for(var r=0,o=0;r=o,o=T(),38===r&&12===o&&(t[n]=1),!N(o);)A();return L(e,_)},ie=new WeakMap,ae=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||ie.get(n))&&!r){ie.set(e,!0);for(var o=[],i=function(e,t){return P(function(e,t){var n=-1,r=44;do{switch(N(r)){case 0:38===r&&12===T()&&(t[n]=1),e[n]+=oe(_-1,t,n);break;case 2:e[n]+=z(r);break;case 4:if(44===r){e[++n]=58===T()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=p(r)}}while(r=A());return e}(I(e),t))}(t,o),a=n.props,s=0,l=0;s<i.length;s++)for(var c=0;c<a.length;c++,l++)e.props[l]=o[s]?i[s].replace(/&\f/g,a[c]):a[c]+" "+i[s]}}},se=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function le(e,t){switch(function(e,t){return 45^v(e,0)?(((t<<2^v(e,0))<<2^v(e,1))<<2^v(e,2))<<2^v(e,3):0}(e,t)){case 5103:return G+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return G+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return G+e+q+e+H+e+e;case 6828:case 4268:return G+e+H+e+e;case 6165:return G+e+H+"flex-"+e+e;case 5187:return G+e+g(e,/(\w+).+(:[^]+)/,G+"box-$1$2"+H+"flex-$1$2")+e;case 5443:return G+e+H+"flex-item-"+g(e,/flex-|-self/,"")+e;case 4675:return G+e+H+"flex-line-pack"+g(e,/align-content|flex-|-self/,"")+e;case 5548:return G+e+H+g(e,"shrink","negative")+e;case 5292:return G+e+H+g(e,"basis","preferred-size")+e;case 6060:return G+"box-"+g(e,"-grow","")+G+e+H+g(e,"grow","positive")+e;case 4554:return G+g(e,/([^-])(transform)/g,"$1"+G+"$2")+e;case 6187:return g(g(g(e,/(zoom-|grab)/,G+"$1"),/(image-set)/,G+"$1"),e,"")+e;case 5495:case 3959:return g(e,/(image-set\([^]*)/,G+"$1$`$1");case 4968:return g(g(e,/(.+:)(flex-)?(.*)/,G+"box-pack:$3"+H+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+G+e+e;case 4095:case 3583:case 4068:case 2532:return g(e,/(.+)-inline(.+)/,G+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(y(e)-1-t>6)switch(v(e,t+1)){case 109:if(45!==v(e,t+4))break;case 102:return g(e,/(.+:)(.+)-([^]+)/,"$1"+G+"$2-$3$1"+q+(108==v(e,t+3)?"$3":"$2-$3"))+e;case 115:return~m(e,"stretch")?le(g(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==v(e,t+1))break;case 6444:switch(v(e,y(e)-3-(~m(e,"!important")&&10))){case 107:return g(e,":",":"+G)+e;case 101:return g(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+G+(45===v(e,14)?"inline-":"")+"box$3$1"+G+"$2$3$1"+H+"$2box$3")+e}break;case 5936:switch(v(e,t+11)){case 114:return G+e+H+g(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return G+e+H+g(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return G+e+H+g(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return G+e+H+e+e}return e}var ce=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case Y:e.return=le(e.value,e.length);break;case K:return J([j(e,{value:g(e.value,"@","@"+G)})],r);case X:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e){return(e=/(::plac\w+|:read-\w+)/.exec(e))?e[0]:e}(t)){case":read-only":case":read-write":return J([j(e,{props:[g(t,/:(read-\w+)/,":-moz-$1")]})],r);case"::placeholder":return J([j(e,{props:[g(t,/:(plac\w+)/,":"+G+"input-$1")]}),j(e,{props:[g(t,/:(plac\w+)/,":-moz-$1")]}),j(e,{props:[g(t,/:(plac\w+)/,H+"input-$1")]})],r)}return""}))}}],ue=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var r,o,i=e.stylisPlugins||ce,a={},s=[];r=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)a[t[n]]=!0;s.push(e)}));var l,c,d,p,f=[Z,(p=function(e){l.insert(e)},function(e){e.root||(e=e.return)&&p(e)})],h=(c=[ae,se].concat(i,f),d=x(c),function(e,t,n,r){for(var o="",i=0;i<d;i++)o+=c[i](e,t,n,r)||"";return o});o=function(e,t,n,r){l=n,J(Q(e?e+"{"+t.styles+"}":t.styles),h),r&&(g.inserted[t.name]=!0)};var g={key:t,sheet:new u({key:t,container:r,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:a,registered:{},insert:o};return g.sheet.hydrate(s),g},de={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},pe=/[A-Z]|^ms/g,fe=/_EMO_([^_]+?)_([^]*?)_EMO_/g,he=function(e){return 45===e.charCodeAt(1)},ge=function(e){return null!=e&&"boolean"!=typeof e},me=s((function(e){return he(e)?e:e.replace(pe,"-$&").toLowerCase()})),ve=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(fe,(function(e,t,n){return ye={name:t,styles:n,next:ye},t}))}return 1===de[e]||he(e)||"number"!=typeof t||0===t?t:t+"px"};function be(e,t,n){if(null==n)return"";if(void 0!==n.__emotion_styles)return n;switch(typeof n){case"boolean":return"";case"object":if(1===n.anim)return ye={name:n.name,styles:n.styles,next:ye},n.name;if(void 0!==n.styles){var r=n.next;if(void 0!==r)for(;void 0!==r;)ye={name:r.name,styles:r.styles,next:ye},r=r.next;return n.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var o=0;o<n.length;o++)r+=be(e,t,n[o])+";";else for(var i in n){var a=n[i];if("object"!=typeof a)null!=t&&void 0!==t[a]?r+=i+"{"+t[a]+"}":ge(a)&&(r+=me(i)+":"+ve(i,a)+";");else if(!Array.isArray(a)||"string"!=typeof a[0]||null!=t&&void 0!==t[a[0]]){var s=be(e,t,a);switch(i){case"animation":case"animationName":r+=me(i)+":"+s+";";break;default:r+=i+"{"+s+"}"}}else for(var l=0;l<a.length;l++)ge(a[l])&&(r+=me(i)+":"+ve(i,a[l])+";")}return r}(e,t,n);case"function":if(void 0!==e){var o=ye,i=n(e);return ye=o,be(e,t,i)}}if(null==t)return n;var a=t[n];return void 0!==a?a:n}var ye,xe=/label:\s*([^\s;\n{]+)\s*(;|$)/g,we=!!i.useInsertionEffect&&i.useInsertionEffect,ke=we||function(e){return e()},Ce=(we||i.useLayoutEffect,i.createContext("undefined"!=typeof HTMLElement?ue({key:"css"}):null));Ce.Provider;var Se=i.createContext({}),_e=function(e,t,n){var r=e.key+"-"+t.name;!1===n&&void 0===e.registered[r]&&(e.registered[r]=t.styles)},Ee=c,Re=function(e){return"theme"!==e},De=function(e){return"string"==typeof e&&e.charCodeAt(0)>96?Ee:Re},je=function(e,t,n){var r;if(t){var o=t.shouldForwardProp;r=e.__emotion_forwardProp&&o?function(t){return e.__emotion_forwardProp(t)&&o(t)}:o}return"function"!=typeof r&&n&&(r=e.__emotion_forwardProp),r},Oe=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;return _e(t,n,r),ke((function(){return function(e,t,n){_e(e,t,n);var r=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var o=t;do{e.insert(t===o?"."+r:"",o,e.sheet,!0),o=o.next}while(void 0!==o)}}(t,n,r)})),null},Ae=function e(t,n){var r,a,s=t.__emotion_real===t,l=s&&t.__emotion_base||t;void 0!==n&&(r=n.label,a=n.target);var c=je(t,n,s),u=c||De(l),d=!u("as");return function(){var p=arguments,f=s&&void 0!==t.__emotion_styles?t.__emotion_styles.slice(0):[];if(void 0!==r&&f.push("label:"+r+";"),null==p[0]||void 0===p[0].raw)f.push.apply(f,p);else{f.push(p[0][0]);for(var h=p.length,g=1;g<h;g++)f.push(p[g],p[0][g])}var m,v=(m=function(e,t,n){var r,o,s,p,h=d&&e.as||l,g="",m=[],v=e;if(null==e.theme){for(var b in v={},e)v[b]=e[b];v.theme=i.useContext(Se)}"string"==typeof e.className?(r=t.registered,o=m,s=e.className,p="",s.split(" ").forEach((function(e){void 0!==r[e]?o.push(r[e]+";"):p+=e+" "})),g=p):null!=e.className&&(g=e.className+" ");var y=function(e,t,n){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,o="";ye=void 0;var i=e[0];null==i||void 0===i.raw?(r=!1,o+=be(n,t,i)):o+=i[0];for(var a=1;a<e.length;a++)o+=be(n,t,e[a]),r&&(o+=i[a]);xe.lastIndex=0;for(var s,l="";null!==(s=xe.exec(o));)l+="-"+s[1];var c=function(e){for(var t,n=0,r=0,o=e.length;o>=4;++r,o-=4)t=1540483477*(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&n)+(59797*(n>>>16)<<16);switch(o){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=1540483477*(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(o)+l;return{name:c,styles:o,next:ye}}(f.concat(m),t.registered,v);g+=t.key+"-"+y.name,void 0!==a&&(g+=" "+a);var x=d&&void 0===c?De(h):u,w={};for(var k in e)d&&"as"===k||x(k)&&(w[k]=e[k]);return w.className=g,w.ref=n,i.createElement(i.Fragment,null,i.createElement(Oe,{cache:t,serialized:y,isStringTag:"string"==typeof h}),i.createElement(h,w))},(0,i.forwardRef)((function(e,t){var n=(0,i.useContext)(Ce);return m(e,n,t)})));return v.displayName=void 0!==r?r:"Styled("+("string"==typeof l?l:l.displayName||l.name||"Component")+")",v.defaultProps=t.defaultProps,v.__emotion_real=v,v.__emotion_base=l,v.__emotion_styles=f,v.__emotion_forwardProp=c,Object.defineProperty(v,"toString",{value:function(){return"."+a}}),v.withComponent=function(t,r){return e(t,o({},n,r,{shouldForwardProp:je(v,r,!0)})).apply(void 0,f)},v}}.bind();["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"].forEach((function(e){Ae[e]=Ae(e)}));const Te=window.wp.i18n,Me=window.wc.wcSettings,Le=(0,Me.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),Ne=(Le.pluginUrl,Le.pluginUrl,Me.STORE_PAGES.shop,Me.STORE_PAGES.checkout?.id),Ie=(Me.STORE_PAGES.checkout,Me.STORE_PAGES.privacy,Me.STORE_PAGES.privacy,Me.STORE_PAGES.terms,Me.STORE_PAGES.terms,Me.STORE_PAGES.cart,Me.STORE_PAGES.cart,Me.STORE_PAGES.myaccount?.permalink?Me.STORE_PAGES.myaccount.permalink:(0,Me.getSetting)("wpLoginUrl","/wp-login.php"),(0,Me.getSetting)("localPickupEnabled",!1),(0,Me.getSetting)("shippingMethodsExist",!1),(0,Me.getSetting)("shippingEnabled",!0),(0,Me.getSetting)("countries",{})),Pe=(0,Me.getSetting)("countryData",{}),ze={...Object.fromEntries(Object.keys(Pe).filter((e=>!0===Pe[e].allowBilling)).map((e=>[e,Ie[e]||""]))),...Object.fromEntries(Object.keys(Pe).filter((e=>!0===Pe[e].allowShipping)).map((e=>[e,Ie[e]||""])))},Be=(Object.fromEntries(Object.keys(ze).map((e=>[e,Pe[e].states||{}]))),Object.fromEntries(Object.keys(ze).map((e=>[e,Pe[e].locale||{}]))),{address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]}),Fe=((0,Me.getSetting)("addressFieldsLocations",Be).address,(0,Me.getSetting)("addressFieldsLocations",Be).contact,(0,Me.getSetting)("addressFieldsLocations",Be).order,(0,Me.getSetting)("additionalOrderFields",{}),(0,Me.getSetting)("additionalContactFields",{}),(0,Me.getSetting)("additionalAddressFields",{}),window.wp.components),$e=window.ReactJSXRuntime,Ue=Ae.div`
	display: flex;
	flex-flow: column;
	margin-bottom: 24px;
	&:last-child {
		margin-bottom: 0;
	}
	@media ( min-width: 800px ) {
		flex-flow: row;
	}
	.components-base-control {
		label {
			text-transform: none !important;
		}
	}
`,Ve=Ae.div`
	flex: 0 1 auto;
	margin-bottom: 24px;
	@media ( min-width: 800px ) {
		flex: 0 0 250px;
		margin: 0 32px 0 0;
	}
	h2 {
		font-size: 16px;
		line-height: 24px;
	}
	p {
		font-size: 13px;
		line-height: 17.89px;
		margin: 12px 0;
	}
	> :last-child {
		margin-bottom: 0;
	}
`,He=Ae.div`
	flex: 1 1 auto;
	margin-bottom: 12px;
`,qe=({Description:e=()=>null,children:t,...n})=>(0,$e.jsxs)(Ue,{...n,children:[(0,$e.jsx)(Ve,{children:(0,$e.jsx)(e,{})}),(0,$e.jsx)(He,{children:t})]}),Ge=Ae(Fe.Card)`
	border-radius: 3px;
`,We=Ae(Fe.CardBody)`
	padding: 24px;

	// increasing the specificity of the styles to override the Gutenberg ones
	&.is-size-medium.is-size-medium {
		padding: 24px;
	}

	h4 {
		margin-top: 0;
		margin-bottom: 1em;
	}

	> * {
		margin-top: 0;
		margin-bottom: 1.5em;

		// fixing the spacing on the inputs and their help text, to ensure it is consistent
		&:last-child {
			margin-bottom: 0;

			> :last-child {
				margin-bottom: 0;
			}
		}
	}

	input,
	select {
		margin: 0;
	}

	// spacing adjustment on "Express checkouts > Show express checkouts on" list
	ul > li:last-child {
		margin-bottom: 0;

		.components-base-control__field {
			margin-bottom: 0;
		}
	}
`,Xe=({children:e,...t})=>(0,$e.jsx)(Ge,{children:(0,$e.jsx)(We,{...t,children:e})}),Ye=window.wp.url,Ke=window.wp.apiFetch;var Je=n.n(Ke);const Ze=window.wp.data;var Qe=n(824),et=n.n(Qe);const tt=window.wp.notices,nt=window.wc.wcTypes,rt={enabled:!1,title:(0,Te.__)("Pickup","woocommerce"),tax_status:"taxable",cost:""},ot={hasLegacyPickup:!1,storeCountry:"",storeState:""},it=()=>{const e=hydratedScreenSettings.pickupLocationSettings;return{enabled:"boolean"==typeof e?.enabled?e.enabled:rt.enabled,title:e?.title||rt.title,tax_status:e?.tax_status||rt.tax_status,cost:e?.cost||rt.cost}},at=()=>(hydratedScreenSettings.pickupLocations||[]).map(((e,t)=>({...e,id:(0,Ye.cleanForSlug)(e.name)+"-"+t}))),st=hydratedScreenSettings.readonlySettings||ot,lt=(0,Me.getSetting)("countries",[]),ct=(0,Me.getSetting)("countryStates",[]),ut=e=>{const t=(0,nt.isObject)(e)&&{...e,country:"string"==typeof e.country&&lt[e.country],state:"string"==typeof e.country&&"string"==typeof e.state&&ct[e.country]?.[e.state]?ct[e.country][e.state]:e.state};return Object.values(t).filter((e=>""!==e)).join(", ")},dt={options:Object.keys(lt).map((e=>{const t=ct[e]||{};if(0===Object.keys(t).length)return{options:[{value:e,label:lt[e]}]};const n=Object.keys(t).map((n=>({value:`${e}:${n}`,label:`${lt[e]} — ${t[n]}`})));return{label:lt[e],options:[...n]}}))},pt=(0,r.createContext)({settings:rt,readOnlySettings:ot,setSettingField:()=>()=>{},pickupLocations:[],setPickupLocations:()=>{},toggleLocation:()=>{},updateLocation:()=>{},isSaving:!1,save:()=>{},isDirty:!1}),ft=()=>(0,r.useContext)(pt),ht=({children:e})=>{const[t,n]=(0,r.useState)(!1),[o,i]=(0,r.useState)(!1),[a,s]=(0,r.useState)(at),[l,c]=(0,r.useState)(it),u=(0,r.useCallback)((e=>t=>{i(!0),c((n=>({...n,[e]:t})))}),[]),d=(0,r.useCallback)((e=>{i(!0),s(e)}),[]),p=(0,r.useCallback)((e=>{i(!0),s((t=>{const n=t.findIndex((({id:t})=>t===e)),r=[...t];return r[n].enabled=!t[n].enabled,r}))}),[]),f=(0,r.useCallback)((()=>{const e={pickup_location_settings:{enabled:l.enabled?"yes":"no",title:l.title,tax_status:["taxable","none"].includes(l.tax_status)?l.tax_status:"taxable",cost:l.cost},pickup_locations:a.map((e=>({name:e.name,address:e.address,details:e.details,enabled:e.enabled})))};n(!0),i(!1),Je()({path:"/wp/v2/settings",method:"POST",data:e}).then((t=>{n(!1),et()(t.pickup_location_settings,e.pickup_location_settings)&&et()(t.pickup_locations,e.pickup_locations)&&(0,Ze.dispatch)(tt.store).createSuccessNotice((0,Te.__)("Local Pickup settings have been saved.","woocommerce"))}))}),[l,a]),h={settings:l,setSettingField:u,readOnlySettings:st,pickupLocations:a,setPickupLocations:d,toggleLocation:p,updateLocation:(e,t)=>{s((n=>(i(!0),"new"===e?[...n,{...t,id:(0,Ye.cleanForSlug)(t.name)+"-"+n.length}]:n.map((n=>n.id===e?t:n)).filter(Boolean))))},isSaving:t,save:f,isDirty:o};return(0,$e.jsx)(pt.Provider,{value:h,children:e})},gt=()=>(0,$e.jsxs)($e.Fragment,{children:[(0,$e.jsx)("h2",{children:(0,Te._x)("General","Admin settings","woocommerce")}),(0,$e.jsx)("p",{children:(0,Te.__)("Enable or disable local pickup on your store, and define costs. Local pickup is only available from the block checkout.","woocommerce")}),(0,$e.jsx)(Fe.ExternalLink,{href:`${Me.ADMIN_URL}post.php?post=${Ne}&action=edit`,children:(0,Te.__)("View checkout page","woocommerce")})]}),mt=Ae(Fe.Notice)`
	margin-left: 0;
	margin-right: 0;
`,vt=()=>{const{settings:e,setSettingField:t,readOnlySettings:n}=ft(),[o,i]=(0,r.useState)(!!e.cost),a=(0,Me.getSetting)("shippingCostRequiresAddress",!1);return(0,$e.jsx)(qe,{Description:gt,children:(0,$e.jsxs)(Xe,{children:[n.hasLegacyPickup&&(0,$e.jsx)(mt,{status:"warning",isDismissible:!1,children:(0,r.createInterpolateElement)((0,Te.__)("By enabling Local Pickup with more valuable features for your store, it's recommended that you remove the legacy Local Pickup option from your <a>shipping zones</a>.","woocommerce"),{a:(0,$e.jsx)("a",{href:`${Me.ADMIN_URL}admin.php?page=wc-settings&tab=shipping`})})}),(0,$e.jsx)(Fe.CheckboxControl,{checked:e.enabled,name:"local_pickup_enabled",onChange:t("enabled"),label:(0,Te.__)("Enable local pickup","woocommerce"),help:(0,$e.jsxs)("span",{children:[(0,Te.__)("When enabled, local pickup will appear as an option on the block based checkout.","woocommerce"),a?(0,$e.jsxs)($e.Fragment,{children:[(0,$e.jsx)("br",{}),(0,Te.__)('If local pickup is enabled, the "Hide shipping costs until an address is entered" setting will be ignored.',"woocommerce")]}):null]})}),(0,$e.jsx)(Fe.TextControl,{label:(0,Te.__)("Title","woocommerce"),name:"local_pickup_title",help:(0,Te.__)("This is the shipping method title shown to customers.","woocommerce"),placeholder:(0,Te.__)("Pickup","woocommerce"),value:e.title,onChange:t("title"),disabled:!1,autoComplete:"off",required:!0,onInvalid:e=>{e.target.setCustomValidity((0,Te.__)("Local pickup title is required","woocommerce"))},onInput:e=>{e.target.setCustomValidity("")}}),(0,$e.jsx)(Fe.CheckboxControl,{checked:o,onChange:()=>{i(!o),t("cost")("")},label:(0,Te.__)("Add a price for customers who choose local pickup","woocommerce"),help:(0,Te.__)("By default, the local pickup shipping method is free.","woocommerce")}),o?(0,$e.jsxs)($e.Fragment,{children:[(0,$e.jsx)(Fe.TextControl,{label:(0,Te.__)("Cost","woocommerce"),name:"local_pickup_cost",help:(0,Te.__)("Optional cost to charge for local pickup.","woocommerce"),placeholder:(0,Te.__)("Free","woocommerce"),type:"number",pattern:"[0-9]+\\.?[0-9]*",min:0,value:e.cost,onChange:t("cost"),disabled:!1,autoComplete:"off"}),(0,$e.jsx)(Fe.SelectControl,{label:(0,Te.__)("Taxes","woocommerce"),name:"local_pickup_tax_status",help:(0,Te.__)("If a cost is defined, this controls if taxes are applied to that cost.","woocommerce"),options:[{label:(0,Te.__)("Taxable","woocommerce"),value:"taxable"},{label:(0,Te.__)("Not taxable","woocommerce"),value:"none"}],value:e.tax_status,onChange:t("tax_status"),disabled:!1})]}):null]})})},bt=(0,r.forwardRef)((function({icon:e,size:t=24,...n},o){return(0,r.cloneElement)(e,{width:t,height:t,...n,ref:o})})),yt=window.wp.primitives,xt=(0,i.createElement)(yt.SVG,{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(yt.Path,{d:"M8 7h2V5H8v2zm0 6h2v-2H8v2zm0 6h2v-2H8v2zm6-14v2h2V5h-2zm0 8h2v-2h-2v2zm0 6h2v-2h-2v2z"})),wt=window.ReactDOM,kt="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function Ct(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function St(e){return"nodeType"in e}function _t(e){var t,n;return e?Ct(e)?e:St(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function Et(e){const{Document:t}=_t(e);return e instanceof t}function Rt(e){return!Ct(e)&&e instanceof _t(e).HTMLElement}function Dt(e){return e instanceof _t(e).SVGElement}function jt(e){return e?Ct(e)?e.document:St(e)?Et(e)?e:Rt(e)||Dt(e)?e.ownerDocument:document:document:document}const Ot=kt?i.useLayoutEffect:i.useEffect;function At(e){const t=(0,i.useRef)(e);return Ot((()=>{t.current=e})),(0,i.useCallback)((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}function Tt(e,t){void 0===t&&(t=[e]);const n=(0,i.useRef)(e);return Ot((()=>{n.current!==e&&(n.current=e)}),t),n}function Mt(e,t){const n=(0,i.useRef)();return(0,i.useMemo)((()=>{const t=e(n.current);return n.current=t,t}),[...t])}function Lt(e){const t=At(e),n=(0,i.useRef)(null),r=(0,i.useCallback)((e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e}),[]);return[n,r]}function Nt(e){const t=(0,i.useRef)();return(0,i.useEffect)((()=>{t.current=e}),[e]),t.current}let It={};function Pt(e,t){return(0,i.useMemo)((()=>{if(t)return t;const n=null==It[e]?0:It[e]+1;return It[e]=n,e+"-"+n}),[e,t])}function zt(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[n,o]of r){const r=t[n];null!=r&&(t[n]=r+e*o)}return t}),{...t})}}const Bt=zt(1),Ft=zt(-1);function $t(e){if(!e)return!1;const{KeyboardEvent:t}=_t(e.target);return t&&e instanceof t}function Ut(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=_t(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}const Vt=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[Vt.Translate.toString(e),Vt.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),Ht="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function qt(e){return e.matches(Ht)?e:e.querySelector(Ht)}const Gt={display:"none"};function Wt(e){let{id:t,value:n}=e;return a().createElement("div",{id:t,style:Gt},n)}function Xt(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return a().createElement("div",{id:t,style:{position:"fixed",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}const Yt=(0,i.createContext)(null),Kt={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},Jt={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function Zt(e){let{announcements:t=Jt,container:n,hiddenTextDescribedById:r,screenReaderInstructions:o=Kt}=e;const{announce:s,announcement:l}=function(){const[e,t]=(0,i.useState)("");return{announce:(0,i.useCallback)((e=>{null!=e&&t(e)}),[]),announcement:e}}(),c=Pt("DndLiveRegion"),[u,d]=(0,i.useState)(!1);if((0,i.useEffect)((()=>{d(!0)}),[]),function(e){const t=(0,i.useContext)(Yt);(0,i.useEffect)((()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)}),[e,t])}((0,i.useMemo)((()=>({onDragStart(e){let{active:n}=e;s(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&s(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;s(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;s(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;s(t.onDragCancel({active:n,over:r}))}})),[s,t])),!u)return null;const p=a().createElement(a().Fragment,null,a().createElement(Wt,{id:r,value:o.draggable}),a().createElement(Xt,{id:c,announcement:l}));return n?(0,wt.createPortal)(p,n):p}var Qt;function en(){}function tn(e,t){return(0,i.useMemo)((()=>({sensor:e,options:null!=t?t:{}})),[e,t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(Qt||(Qt={}));const nn=Object.freeze({x:0,y:0});function rn(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function on(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function an(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}const sn=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=an(t,t.left,t.top),i=[];for(const e of r){const{id:t}=e,r=n.get(t);if(r){const n=(a=an(r),s=o,Math.sqrt(Math.pow(a.x-s.x,2)+Math.pow(a.y-s.y,2)));i.push({id:t,data:{droppableContainer:e,value:n}})}}var a,s;return i.sort(rn)};function ln(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),a=o-r,s=i-n;if(r<o&&n<i){const n=t.width*t.height,r=e.width*e.height,o=a*s;return Number((o/(n+r-o)).toFixed(4))}return 0}const cn=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const e of r){const{id:r}=e,i=n.get(r);if(i){const n=ln(i,t);n>0&&o.push({id:r,data:{droppableContainer:e,value:n}})}}return o.sort(on)};function un(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:nn}function dn(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...t})}}const pn=dn(1);const fn={ignoreTransform:!1};function hn(e,t){void 0===t&&(t=fn);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=_t(e).getComputedStyle(e);t&&(n=function(e,t,n){const r=function(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;const{scaleX:o,scaleY:i,x:a,y:s}=r,l=e.left-a-(1-o)*parseFloat(n),c=e.top-s-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),u=o?e.width/o:e.width,d=i?e.height/i:e.height;return{width:u,height:d,top:c,right:l+u,bottom:c+d,left:l}}(n,t,r))}const{top:r,left:o,width:i,height:a,bottom:s,right:l}=n;return{top:r,left:o,width:i,height:a,bottom:s,right:l}}function gn(e){return hn(e,{ignoreTransform:!0})}function mn(e,t){const n=[];return e?function r(o){if(null!=t&&n.length>=t)return n;if(!o)return n;if(Et(o)&&null!=o.scrollingElement&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!Rt(o)||Dt(o))return n;if(n.includes(o))return n;const i=_t(e).getComputedStyle(o);return o!==e&&function(e,t){void 0===t&&(t=_t(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some((e=>{const r=t[e];return"string"==typeof r&&n.test(r)}))}(o,i)&&n.push(o),function(e,t){return void 0===t&&(t=_t(e).getComputedStyle(e)),"fixed"===t.position}(o,i)?n:r(o.parentNode)}(e):n}function vn(e){const[t]=mn(e,1);return null!=t?t:null}function bn(e){return kt&&e?Ct(e)?e:St(e)?Et(e)||e===jt(e).scrollingElement?window:Rt(e)?e:null:null:null}function yn(e){return Ct(e)?e.scrollX:e.scrollLeft}function xn(e){return Ct(e)?e.scrollY:e.scrollTop}function wn(e){return{x:yn(e),y:xn(e)}}var kn;function Cn(e){return!(!kt||!e)&&e===document.scrollingElement}function Sn(e){const t={x:0,y:0},n=Cn(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};return{isTop:e.scrollTop<=t.y,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(kn||(kn={}));const En={x:.2,y:.2};function Rn(e,t,n,r,o){let{top:i,left:a,right:s,bottom:l}=n;void 0===r&&(r=10),void 0===o&&(o=En);const{isTop:c,isBottom:u,isLeft:d,isRight:p}=Sn(e),f={x:0,y:0},h={x:0,y:0},g=t.height*o.y,m=t.width*o.x;return!c&&i<=t.top+g?(f.y=kn.Backward,h.y=r*Math.abs((t.top+g-i)/g)):!u&&l>=t.bottom-g&&(f.y=kn.Forward,h.y=r*Math.abs((t.bottom-g-l)/g)),!p&&s>=t.right-m?(f.x=kn.Forward,h.x=r*Math.abs((t.right-m-s)/m)):!d&&a<=t.left+m&&(f.x=kn.Backward,h.x=r*Math.abs((t.left+m-a)/m)),{direction:f,speed:h}}function Dn(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function jn(e){return e.reduce(((e,t)=>Bt(e,wn(t))),nn)}const On=[["x",["left","right"],function(e){return e.reduce(((e,t)=>e+yn(t)),0)}],["y",["top","bottom"],function(e){return e.reduce(((e,t)=>e+xn(t)),0)}]];class An{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const n=mn(t),r=jn(n);this.rect={...e},this.width=e.width,this.height=e.height;for(const[e,t,o]of On)for(const i of t)Object.defineProperty(this,i,{get:()=>{const t=o(n),a=r[e]-t;return this.rect[i]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class Tn{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Mn(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}var Ln,Nn,In;function Pn(e){e.preventDefault()}function zn(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(Ln||(Ln={})),(In=Nn||(Nn={})).Space="Space",In.Down="ArrowDown",In.Right="ArrowRight",In.Left="ArrowLeft",In.Up="ArrowUp",In.Esc="Escape",In.Enter="Enter";const Bn={start:[Nn.Space,Nn.Enter],cancel:[Nn.Esc],end:[Nn.Space,Nn.Enter]},Fn=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case Nn.Right:return{...n,x:n.x+25};case Nn.Left:return{...n,x:n.x-25};case Nn.Down:return{...n,y:n.y+25};case Nn.Up:return{...n,y:n.y-25}}};class $n{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;const{event:{target:t}}=e;this.props=e,this.listeners=new Tn(jt(t)),this.windowListeners=new Tn(_t(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(Ln.Resize,this.handleCancel),this.windowListeners.add(Ln.VisibilityChange,this.handleCancel),setTimeout((()=>this.listeners.add(Ln.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=hn),!e)return;const{top:n,left:r,bottom:o,right:i}=t(e);vn(e)&&(o<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(nn)}handleKeyDown(e){if($t(e)){const{active:t,context:n,options:r}=this.props,{keyboardCodes:o=Bn,coordinateGetter:i=Fn,scrollBehavior:a="smooth"}=r,{code:s}=e;if(o.end.includes(s))return void this.handleEnd(e);if(o.cancel.includes(s))return void this.handleCancel(e);const{collisionRect:l}=n.current,c=l?{x:l.left,y:l.top}:nn;this.referenceCoordinates||(this.referenceCoordinates=c);const u=i(e,{active:t,context:n.current,currentCoordinates:c});if(u){const t=Ft(u,c),r={x:0,y:0},{scrollableAncestors:o}=n.current;for(const n of o){const o=e.code,{isTop:i,isRight:s,isLeft:l,isBottom:c,maxScroll:d,minScroll:p}=Sn(n),f=Dn(n),h={x:Math.min(o===Nn.Right?f.right-f.width/2:f.right,Math.max(o===Nn.Right?f.left:f.left+f.width/2,u.x)),y:Math.min(o===Nn.Down?f.bottom-f.height/2:f.bottom,Math.max(o===Nn.Down?f.top:f.top+f.height/2,u.y))},g=o===Nn.Right&&!s||o===Nn.Left&&!l,m=o===Nn.Down&&!c||o===Nn.Up&&!i;if(g&&h.x!==u.x){const e=n.scrollLeft+t.x,i=o===Nn.Right&&e<=d.x||o===Nn.Left&&e>=p.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});r.x=i?n.scrollLeft-e:o===Nn.Right?n.scrollLeft-d.x:n.scrollLeft-p.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(m&&h.y!==u.y){const e=n.scrollTop+t.y,i=o===Nn.Down&&e<=d.y||o===Nn.Up&&e>=p.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});r.y=i?n.scrollTop-e:o===Nn.Down?n.scrollTop-d.y:n.scrollTop-p.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,Bt(Ft(u,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function Un(e){return Boolean(e&&"distance"in e)}function Vn(e){return Boolean(e&&"delay"in e)}$n.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=Bn,onActivation:o}=t,{active:i}=n;const{code:a}=e.nativeEvent;if(r.start.includes(a)){const t=i.activatorNode.current;return!(t&&e.target!==t||(e.preventDefault(),null==o||o({event:e.nativeEvent}),0))}return!1}}];class Hn{constructor(e,t,n){var r;void 0===n&&(n=function(e){const{EventTarget:t}=_t(e);return e instanceof t?e:jt(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:o}=e,{target:i}=o;this.props=e,this.events=t,this.document=jt(i),this.documentListeners=new Tn(this.document),this.listeners=new Tn(n),this.windowListeners=new Tn(_t(i)),this.initialCoordinates=null!=(r=Ut(o))?r:nn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(Ln.Resize,this.handleCancel),this.windowListeners.add(Ln.DragStart,Pn),this.windowListeners.add(Ln.VisibilityChange,this.handleCancel),this.windowListeners.add(Ln.ContextMenu,Pn),this.documentListeners.add(Ln.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(Vn(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(Un(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(Ln.Click,zn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(Ln.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:o}=this,{onMove:i,options:{activationConstraint:a}}=o;if(!r)return;const s=null!=(t=Ut(e))?t:nn,l=Ft(r,s);if(!n&&a){if(Un(a)){if(null!=a.tolerance&&Mn(l,a.tolerance))return this.handleCancel();if(Mn(l,a.distance))return this.handleStart()}return Vn(a)&&Mn(l,a.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),i(s)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===Nn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const qn={move:{name:"pointermove"},end:{name:"pointerup"}};class Gn extends Hn{constructor(e){const{event:t}=e,n=jt(t.target);super(e,qn,n)}}Gn.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!(!n.isPrimary||0!==n.button||(null==r||r({event:n}),0))}}];const Wn={move:{name:"mousemove"},end:{name:"mouseup"}};var Xn;!function(e){e[e.RightClick=2]="RightClick"}(Xn||(Xn={}));class Yn extends Hn{constructor(e){super(e,Wn,jt(e.event.target))}}Yn.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==Xn.RightClick&&(null==r||r({event:n}),!0)}}];const Kn={move:{name:"touchmove"},end:{name:"touchend"}};class Jn extends Hn{constructor(e){super(e,Kn)}static setup(){return window.addEventListener(Kn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Kn.move.name,e)};function e(){}}}var Zn,Qn;Jn.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:o}=n;return!(o.length>1||(null==r||r({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(Zn||(Zn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(Qn||(Qn={}));const er={x:{[kn.Backward]:!1,[kn.Forward]:!1},y:{[kn.Backward]:!1,[kn.Forward]:!1}};var tr,nr;!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(tr||(tr={})),function(e){e.Optimized="optimized"}(nr||(nr={}));const rr=new Map;function or(e,t){return Mt((n=>e?n||("function"==typeof t?t(e):e):null),[t,e])}function ir(e){let{callback:t,disabled:n}=e;const r=At(t),o=(0,i.useMemo)((()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;const{ResizeObserver:e}=window;return new e(r)}),[n]);return(0,i.useEffect)((()=>()=>null==o?void 0:o.disconnect()),[o]),o}function ar(e){return new An(hn(e),e)}function sr(e,t,n){void 0===t&&(t=ar);const[r,o]=(0,i.useReducer)((function(r){if(!e)return null;var o;if(!1===e.isConnected)return null!=(o=null!=r?r:n)?o:null;const i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i}),null),a=function(e){let{callback:t,disabled:n}=e;const r=At(t),o=(0,i.useMemo)((()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;const{MutationObserver:e}=window;return new e(r)}),[r,n]);return(0,i.useEffect)((()=>()=>null==o?void 0:o.disconnect()),[o]),o}({callback(t){if(e)for(const n of t){const{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){o();break}}}}),s=ir({callback:o});return Ot((()=>{o(),e?(null==s||s.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==s||s.disconnect(),null==a||a.disconnect())}),[e]),r}const lr=[];function cr(e,t){void 0===t&&(t=[]);const n=(0,i.useRef)(null);return(0,i.useEffect)((()=>{n.current=null}),t),(0,i.useEffect)((()=>{const t=e!==nn;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)}),[e]),n.current?Ft(e,n.current):nn}function ur(e){return(0,i.useMemo)((()=>e?function(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null),[e])}const dr=[];const pr=[{sensor:Gn,options:{}},{sensor:$n,options:{}}],fr={current:{}},hr={draggable:{measure:gn},droppable:{measure:gn,strategy:tr.WhileDragging,frequency:nr.Optimized},dragOverlay:{measure:hn}};class gr extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}const mr={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new gr,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:en},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:hr,measureDroppableContainers:en,windowRect:null,measuringScheduled:!1},vr={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:en,draggableNodes:new Map,over:null,measureDroppableContainers:en},br=(0,i.createContext)(vr),yr=(0,i.createContext)(mr);function xr(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new gr}}}function wr(e,t){switch(t.type){case Qt.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case Qt.DragMove:return e.draggable.active?{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}}:e;case Qt.DragEnd:case Qt.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case Qt.RegisterDroppable:{const{element:n}=t,{id:r}=n,o=new gr(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case Qt.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;const a=new gr(e.droppable.containers);return a.set(n,{...i,disabled:o}),{...e,droppable:{...e.droppable,containers:a}}}case Qt.UnregisterDroppable:{const{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const i=new gr(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function kr(e){let{disabled:t}=e;const{active:n,activatorEvent:r,draggableNodes:o}=(0,i.useContext)(br),a=Nt(r),s=Nt(null==n?void 0:n.id);return(0,i.useEffect)((()=>{if(!t&&!r&&a&&null!=s){if(!$t(a))return;if(document.activeElement===a.target)return;const e=o.get(s);if(!e)return;const{activatorNode:t,node:n}=e;if(!t.current&&!n.current)return;requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e)continue;const t=qt(e);if(t){t.focus();break}}}))}}),[r,t,o,s,a]),null}const Cr=(0,i.createContext)({...nn,scaleX:1,scaleY:1});var Sr;!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Sr||(Sr={}));const _r=(0,i.memo)((function(e){var t,n,r,o;let{id:s,accessibility:l,autoScroll:c=!0,children:u,sensors:d=pr,collisionDetection:p=cn,measuring:f,modifiers:h,...g}=e;const m=(0,i.useReducer)(wr,void 0,xr),[v,b]=m,[y,x]=function(){const[e]=(0,i.useState)((()=>new Set)),t=(0,i.useCallback)((t=>(e.add(t),()=>e.delete(t))),[e]);return[(0,i.useCallback)((t=>{let{type:n,event:r}=t;e.forEach((e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)}))}),[e]),t]}(),[w,k]=(0,i.useState)(Sr.Uninitialized),C=w===Sr.Initialized,{draggable:{active:S,nodes:_,translate:E},droppable:{containers:R}}=v,D=S?_.get(S):null,j=(0,i.useRef)({initial:null,translated:null}),O=(0,i.useMemo)((()=>{var e;return null!=S?{id:S,data:null!=(e=null==D?void 0:D.data)?e:fr,rect:j}:null}),[S,D]),A=(0,i.useRef)(null),[T,M]=(0,i.useState)(null),[L,N]=(0,i.useState)(null),I=Tt(g,Object.values(g)),P=Pt("DndDescribedBy",s),z=(0,i.useMemo)((()=>R.getEnabled()),[R]),B=(F=f,(0,i.useMemo)((()=>({draggable:{...hr.draggable,...null==F?void 0:F.draggable},droppable:{...hr.droppable,...null==F?void 0:F.droppable},dragOverlay:{...hr.dragOverlay,...null==F?void 0:F.dragOverlay}})),[null==F?void 0:F.draggable,null==F?void 0:F.droppable,null==F?void 0:F.dragOverlay]));var F;const{droppableRects:$,measureDroppableContainers:U,measuringScheduled:V}=function(e,t){let{dragging:n,dependencies:r,config:o}=t;const[a,s]=(0,i.useState)(null),{frequency:l,measure:c,strategy:u}=o,d=(0,i.useRef)(e),p=function(){switch(u){case tr.Always:return!1;case tr.BeforeDragging:return n;default:return!n}}(),f=Tt(p),h=(0,i.useCallback)((function(e){void 0===e&&(e=[]),f.current||s((t=>null===t?e:t.concat(e.filter((e=>!t.includes(e))))))}),[f]),g=(0,i.useRef)(null),m=Mt((t=>{if(p&&!n)return rr;if(!t||t===rr||d.current!==e||null!=a){const t=new Map;for(let n of e){if(!n)continue;if(a&&a.length>0&&!a.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}const e=n.node.current,r=e?new An(c(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t}),[e,a,n,p,c]);return(0,i.useEffect)((()=>{d.current=e}),[e]),(0,i.useEffect)((()=>{p||h()}),[n,p]),(0,i.useEffect)((()=>{a&&a.length>0&&s(null)}),[JSON.stringify(a)]),(0,i.useEffect)((()=>{p||"number"!=typeof l||null!==g.current||(g.current=setTimeout((()=>{h(),g.current=null}),l))}),[l,p,h,...r]),{droppableRects:m,measureDroppableContainers:h,measuringScheduled:null!=a}}(z,{dragging:C,dependencies:[E.x,E.y],config:B.droppable}),H=function(e,t){const n=null!==t?e.get(t):void 0,r=n?n.node.current:null;return Mt((e=>{var n;return null===t?null:null!=(n=null!=r?r:e)?n:null}),[r,t])}(_,S),q=(0,i.useMemo)((()=>L?Ut(L):null),[L]),G=function(){const e=!1===(null==T?void 0:T.autoScrollEnabled),t="object"==typeof c?!1===c.enabled:!1===c,n=C&&!e&&!t;return"object"==typeof c?{...c,enabled:n}:{enabled:n}}(),W=function(e,t){return or(e,t)}(H,B.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:o=!0}=e;const a=(0,i.useRef)(!1),{x:s,y:l}="boolean"==typeof o?{x:o,y:o}:o;Ot((()=>{if(!s&&!l||!t)return void(a.current=!1);if(a.current||!r)return;const e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;const o=un(n(e),r);if(s||(o.x=0),l||(o.y=0),a.current=!0,Math.abs(o.x)>0||Math.abs(o.y)>0){const t=vn(e);t&&t.scrollBy({top:o.y,left:o.x})}}),[t,s,l,r,n])}({activeNode:S?_.get(S):null,config:G.layoutShiftCompensation,initialRect:W,measure:B.draggable.measure});const X=sr(H,B.draggable.measure,W),Y=sr(H?H.parentElement:null),K=(0,i.useRef)({activatorEvent:null,active:null,activeNode:H,collisionRect:null,collisions:null,droppableRects:$,draggableNodes:_,draggingNode:null,draggingNodeRect:null,droppableContainers:R,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),J=R.getNodeFor(null==(t=K.current.over)?void 0:t.id),Z=function(e){let{measure:t}=e;const[n,r]=(0,i.useState)(null),o=ir({callback:(0,i.useCallback)((e=>{for(const{target:n}of e)if(Rt(n)){r((e=>{const r=t(n);return e?{...e,width:r.width,height:r.height}:r}));break}}),[t])}),a=(0,i.useCallback)((e=>{const n=function(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return Rt(t)?t:e}(e);null==o||o.disconnect(),n&&(null==o||o.observe(n)),r(n?t(n):null)}),[t,o]),[s,l]=Lt(a);return(0,i.useMemo)((()=>({nodeRef:s,rect:n,setRef:l})),[n,s,l])}({measure:B.dragOverlay.measure}),Q=null!=(n=Z.nodeRef.current)?n:H,ee=C?null!=(r=Z.rect)?r:X:null,te=Boolean(Z.nodeRef.current&&Z.rect),ne=un(re=te?null:X,or(re));var re;const oe=ur(Q?_t(Q):null),ie=function(e){const t=(0,i.useRef)(e),n=Mt((n=>e?n&&n!==lr&&e&&t.current&&e.parentNode===t.current.parentNode?n:mn(e):lr),[e]);return(0,i.useEffect)((()=>{t.current=e}),[e]),n}(C?null!=J?J:H:null),ae=function(e,t){void 0===t&&(t=hn);const[n]=e,r=ur(n?_t(n):null),[o,a]=(0,i.useReducer)((function(){return e.length?e.map((e=>Cn(e)?r:new An(t(e),e))):dr}),dr),s=ir({callback:a});return e.length>0&&o===dr&&a(),Ot((()=>{e.length?e.forEach((e=>null==s?void 0:s.observe(e))):(null==s||s.disconnect(),a())}),[e]),o}(ie),se=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce(((e,t)=>t({transform:e,...r})),n):n}(h,{transform:{x:E.x-ne.x,y:E.y-ne.y,scaleX:1,scaleY:1},activatorEvent:L,active:O,activeNodeRect:X,containerNodeRect:Y,draggingNodeRect:ee,over:K.current.over,overlayNodeRect:Z.rect,scrollableAncestors:ie,scrollableAncestorRects:ae,windowRect:oe}),le=q?Bt(q,E):null,ce=function(e){const[t,n]=(0,i.useState)(null),r=(0,i.useRef)(e),o=(0,i.useCallback)((e=>{const t=bn(e.target);t&&n((e=>e?(e.set(t,wn(t)),new Map(e)):null))}),[]);return(0,i.useEffect)((()=>{const t=r.current;if(e!==t){i(t);const a=e.map((e=>{const t=bn(e);return t?(t.addEventListener("scroll",o,{passive:!0}),[t,wn(t)]):null})).filter((e=>null!=e));n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach((e=>{const t=bn(e);null==t||t.removeEventListener("scroll",o)}))}}),[o,e]),(0,i.useMemo)((()=>e.length?t?Array.from(t.values()).reduce(((e,t)=>Bt(e,t)),nn):jn(e):nn),[e,t])}(ie),ue=cr(ce),de=cr(ce,[X]),pe=Bt(se,ue),fe=ee?pn(ee,se):null,he=O&&fe?p({active:O,collisionRect:fe,droppableRects:$,droppableContainers:z,pointerCoordinates:le}):null,ge=function(e){if(!e||0===e.length)return null;const[t]=e;return t.id}(he),[me,ve]=(0,i.useState)(null),be=function(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}(te?se:Bt(se,de),null!=(o=null==me?void 0:me.rect)?o:null,X),ye=(0,i.useCallback)(((e,t)=>{let{sensor:n,options:r}=t;if(null==A.current)return;const o=_.get(A.current);if(!o)return;const i=e.nativeEvent,a=new n({active:A.current,activeNode:o,event:i,options:r,context:K,onStart(e){const t=A.current;if(null==t)return;const n=_.get(t);if(!n)return;const{onDragStart:r}=I.current,o={active:{id:t,data:n.data,rect:j}};(0,wt.unstable_batchedUpdates)((()=>{null==r||r(o),k(Sr.Initializing),b({type:Qt.DragStart,initialCoordinates:e,active:t}),y({type:"onDragStart",event:o})}))},onMove(e){b({type:Qt.DragMove,coordinates:e})},onEnd:s(Qt.DragEnd),onCancel:s(Qt.DragCancel)});function s(e){return async function(){const{active:t,collisions:n,over:r,scrollAdjustedTranslate:o}=K.current;let a=null;if(t&&o){const{cancelDrop:s}=I.current;a={activatorEvent:i,active:t,collisions:n,delta:o,over:r},e===Qt.DragEnd&&"function"==typeof s&&await Promise.resolve(s(a))&&(e=Qt.DragCancel)}A.current=null,(0,wt.unstable_batchedUpdates)((()=>{b({type:e}),k(Sr.Uninitialized),ve(null),M(null),N(null);const t=e===Qt.DragEnd?"onDragEnd":"onDragCancel";if(a){const e=I.current[t];null==e||e(a),y({type:t,event:a})}}))}}(0,wt.unstable_batchedUpdates)((()=>{M(a),N(e.nativeEvent)}))}),[_]),xe=(0,i.useCallback)(((e,t)=>(n,r)=>{const o=n.nativeEvent,i=_.get(r);if(null!==A.current||!i||o.dndKit||o.defaultPrevented)return;const a={active:i};!0===e(n,t.options,a)&&(o.dndKit={capturedBy:t.sensor},A.current=r,ye(n,t))}),[_,ye]),we=function(e,t){return(0,i.useMemo)((()=>e.reduce(((e,n)=>{const{sensor:r}=n;return[...e,...r.activators.map((e=>({eventName:e.eventName,handler:t(e.handler,n)})))]}),[])),[e,t])}(d,xe);!function(e){(0,i.useEffect)((()=>{if(!kt)return;const t=e.map((e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()}));return()=>{for(const e of t)null==e||e()}}),e.map((e=>{let{sensor:t}=e;return t})))}(d),Ot((()=>{X&&w===Sr.Initializing&&k(Sr.Initialized)}),[X,w]),(0,i.useEffect)((()=>{const{onDragMove:e}=I.current,{active:t,activatorEvent:n,collisions:r,over:o}=K.current;if(!t||!n)return;const i={active:t,activatorEvent:n,collisions:r,delta:{x:pe.x,y:pe.y},over:o};(0,wt.unstable_batchedUpdates)((()=>{null==e||e(i),y({type:"onDragMove",event:i})}))}),[pe.x,pe.y]),(0,i.useEffect)((()=>{const{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:o}=K.current;if(!e||null==A.current||!t||!o)return;const{onDragOver:i}=I.current,a=r.get(ge),s=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,l={active:e,activatorEvent:t,collisions:n,delta:{x:o.x,y:o.y},over:s};(0,wt.unstable_batchedUpdates)((()=>{ve(s),null==i||i(l),y({type:"onDragOver",event:l})}))}),[ge]),Ot((()=>{K.current={activatorEvent:L,active:O,activeNode:H,collisionRect:fe,collisions:he,droppableRects:$,draggableNodes:_,draggingNode:Q,draggingNodeRect:ee,droppableContainers:R,over:me,scrollableAncestors:ie,scrollAdjustedTranslate:pe},j.current={initial:ee,translated:fe}}),[O,H,he,fe,_,Q,ee,$,R,me,ie,pe]),function(e){let{acceleration:t,activator:n=Zn.Pointer,canScroll:r,draggingRect:o,enabled:a,interval:s=5,order:l=Qn.TreeOrder,pointerCoordinates:c,scrollableAncestors:u,scrollableAncestorRects:d,delta:p,threshold:f}=e;const h=function(e){let{delta:t,disabled:n}=e;const r=Nt(t);return Mt((e=>{if(n||!r||!e)return er;const o=Math.sign(t.x-r.x),i=Math.sign(t.y-r.y);return{x:{[kn.Backward]:e.x[kn.Backward]||-1===o,[kn.Forward]:e.x[kn.Forward]||1===o},y:{[kn.Backward]:e.y[kn.Backward]||-1===i,[kn.Forward]:e.y[kn.Forward]||1===i}}}),[n,t,r])}({delta:p,disabled:!a}),[g,m]=function(){const e=(0,i.useRef)(null);return[(0,i.useCallback)(((t,n)=>{e.current=setInterval(t,n)}),[]),(0,i.useCallback)((()=>{null!==e.current&&(clearInterval(e.current),e.current=null)}),[])]}(),v=(0,i.useRef)({x:0,y:0}),b=(0,i.useRef)({x:0,y:0}),y=(0,i.useMemo)((()=>{switch(n){case Zn.Pointer:return c?{top:c.y,bottom:c.y,left:c.x,right:c.x}:null;case Zn.DraggableRect:return o}}),[n,o,c]),x=(0,i.useRef)(null),w=(0,i.useCallback)((()=>{const e=x.current;if(!e)return;const t=v.current.x*b.current.x,n=v.current.y*b.current.y;e.scrollBy(t,n)}),[]),k=(0,i.useMemo)((()=>l===Qn.TreeOrder?[...u].reverse():u),[l,u]);(0,i.useEffect)((()=>{if(a&&u.length&&y){for(const e of k){if(!1===(null==r?void 0:r(e)))continue;const n=u.indexOf(e),o=d[n];if(!o)continue;const{direction:i,speed:a}=Rn(e,o,y,t,f);for(const e of["x","y"])h[e][i[e]]||(a[e]=0,i[e]=0);if(a.x>0||a.y>0)return m(),x.current=e,g(w,s),v.current=a,void(b.current=i)}v.current={x:0,y:0},b.current={x:0,y:0},m()}else m()}),[t,w,r,m,a,s,JSON.stringify(y),JSON.stringify(h),g,u,k,d,JSON.stringify(f)])}({...G,delta:E,draggingRect:fe,pointerCoordinates:le,scrollableAncestors:ie,scrollableAncestorRects:ae});const ke=(0,i.useMemo)((()=>({active:O,activeNode:H,activeNodeRect:X,activatorEvent:L,collisions:he,containerNodeRect:Y,dragOverlay:Z,draggableNodes:_,droppableContainers:R,droppableRects:$,over:me,measureDroppableContainers:U,scrollableAncestors:ie,scrollableAncestorRects:ae,measuringConfiguration:B,measuringScheduled:V,windowRect:oe})),[O,H,X,L,he,Y,Z,_,R,$,me,U,ie,ae,B,V,oe]),Ce=(0,i.useMemo)((()=>({activatorEvent:L,activators:we,active:O,activeNodeRect:X,ariaDescribedById:{draggable:P},dispatch:b,draggableNodes:_,over:me,measureDroppableContainers:U})),[L,we,O,X,b,P,_,me,U]);return a().createElement(Yt.Provider,{value:x},a().createElement(br.Provider,{value:Ce},a().createElement(yr.Provider,{value:ke},a().createElement(Cr.Provider,{value:be},u)),a().createElement(kr,{disabled:!1===(null==l?void 0:l.restoreFocus)})),a().createElement(Zt,{...l,hiddenTextDescribedById:P}))})),Er=(0,i.createContext)(null),Rr="button";const Dr={timeout:25},jr=e=>{let{transform:t}=e;return{...t,x:0}};function Or(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function Ar(e,t){return e.reduce(((e,n,r)=>{const o=t.get(n);return o&&(e[r]=o),e}),Array(e.length))}function Tr(e){return null!==e&&e>=0}const Mr=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const i=Or(t,r,n),a=t[o],s=i[o];return s&&a?{x:s.left-a.left,y:s.top-a.top,scaleX:s.width/a.width,scaleY:s.height/a.height}:null},Lr={scaleX:1,scaleY:1},Nr=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:o,rects:i,overIndex:a}=e;const s=null!=(t=i[n])?t:r;if(!s)return null;if(o===n){const e=i[a];return e?{x:0,y:n<a?e.top+e.height-(s.top+s.height):e.top-s.top,...Lr}:null}const l=function(e,t,n){const r=e[t],o=e[t-1],i=e[t+1];return r?n<t?o?r.top-(o.top+o.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):o?r.top-(o.top+o.height):0:0}(i,o,n);return o>n&&o<=a?{x:0,y:-s.height-l,...Lr}:o<n&&o>=a?{x:0,y:s.height+l,...Lr}:{x:0,y:0,...Lr}},Ir="Sortable",Pr=a().createContext({activeIndex:-1,containerId:Ir,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:Mr,disabled:{draggable:!1,droppable:!1}});function zr(e){let{children:t,id:n,items:r,strategy:o=Mr,disabled:s=!1}=e;const{active:l,dragOverlay:c,droppableRects:u,over:d,measureDroppableContainers:p}=(0,i.useContext)(yr),f=Pt(Ir,n),h=Boolean(null!==c.rect),g=(0,i.useMemo)((()=>r.map((e=>"object"==typeof e&&"id"in e?e.id:e))),[r]),m=null!=l,v=l?g.indexOf(l.id):-1,b=d?g.indexOf(d.id):-1,y=(0,i.useRef)(g),x=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(g,y.current),w=-1!==b&&-1===v||x,k=function(e){return"boolean"==typeof e?{draggable:e,droppable:e}:e}(s);Ot((()=>{x&&m&&p(g)}),[x,g,m,p]),(0,i.useEffect)((()=>{y.current=g}),[g]);const C=(0,i.useMemo)((()=>({activeIndex:v,containerId:f,disabled:k,disableTransforms:w,items:g,overIndex:b,useDragOverlay:h,sortedRects:Ar(g,u),strategy:o})),[v,f,k.draggable,k.droppable,w,g,b,u,h,o]);return a().createElement(Pr.Provider,{value:C},t)}const Br=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return Or(n,r,o).indexOf(t)},Fr=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:i,newIndex:a,previousItems:s,previousContainerId:l,transition:c}=e;return!(!c||!r||s!==i&&o===a||!n&&(a===o||t!==l))},$r={duration:200,easing:"ease"},Ur="transform",Vr=Vt.Transition.toString({property:Ur,duration:0,easing:"linear"}),Hr={roleDescription:"sortable"};function qr(e){let{animateLayoutChanges:t=Fr,attributes:n,disabled:r,data:o,getNewIndex:a=Br,id:s,strategy:l,resizeObserverConfig:c,transition:u=$r}=e;const{items:d,containerId:p,activeIndex:f,disabled:h,disableTransforms:g,sortedRects:m,overIndex:v,useDragOverlay:b,strategy:y}=(0,i.useContext)(Pr),x=function(e,t){var n,r;return"boolean"==typeof e?{draggable:e,droppable:!1}:{draggable:null!=(n=null==e?void 0:e.draggable)?n:t.draggable,droppable:null!=(r=null==e?void 0:e.droppable)?r:t.droppable}}(r,h),w=d.indexOf(s),k=(0,i.useMemo)((()=>({sortable:{containerId:p,index:w,items:d},...o})),[p,o,w,d]),C=(0,i.useMemo)((()=>d.slice(d.indexOf(s))),[d,s]),{rect:S,node:_,isOver:E,setNodeRef:R}=function(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:o}=e;const a=Pt("Droppable"),{active:s,dispatch:l,over:c,measureDroppableContainers:u}=(0,i.useContext)(br),d=(0,i.useRef)({disabled:n}),p=(0,i.useRef)(!1),f=(0,i.useRef)(null),h=(0,i.useRef)(null),{disabled:g,updateMeasurementsFor:m,timeout:v}={...Dr,...o},b=Tt(null!=m?m:r),y=ir({callback:(0,i.useCallback)((()=>{p.current?(null!=h.current&&clearTimeout(h.current),h.current=setTimeout((()=>{u(Array.isArray(b.current)?b.current:[b.current]),h.current=null}),v)):p.current=!0}),[v]),disabled:g||!s}),x=(0,i.useCallback)(((e,t)=>{y&&(t&&(y.unobserve(t),p.current=!1),e&&y.observe(e))}),[y]),[w,k]=Lt(x),C=Tt(t);return(0,i.useEffect)((()=>{y&&w.current&&(y.disconnect(),p.current=!1,y.observe(w.current))}),[w,y]),Ot((()=>(l({type:Qt.RegisterDroppable,element:{id:r,key:a,disabled:n,node:w,rect:f,data:C}}),()=>l({type:Qt.UnregisterDroppable,key:a,id:r}))),[r]),(0,i.useEffect)((()=>{n!==d.current.disabled&&(l({type:Qt.SetDroppableDisabled,id:r,key:a,disabled:n}),d.current.disabled=n)}),[r,a,n,l]),{active:s,rect:f,isOver:(null==c?void 0:c.id)===r,node:w,over:c,setNodeRef:k}}({id:s,data:k,disabled:x.droppable,resizeObserverConfig:{updateMeasurementsFor:C,...c}}),{active:D,activatorEvent:j,activeNodeRect:O,attributes:A,setNodeRef:T,listeners:M,isDragging:L,over:N,setActivatorNodeRef:I,transform:P}=function(e){let{id:t,data:n,disabled:r=!1,attributes:o}=e;const a=Pt("Droppable"),{activators:s,activatorEvent:l,active:c,activeNodeRect:u,ariaDescribedById:d,draggableNodes:p,over:f}=(0,i.useContext)(br),{role:h=Rr,roleDescription:g="draggable",tabIndex:m=0}=null!=o?o:{},v=(null==c?void 0:c.id)===t,b=(0,i.useContext)(v?Cr:Er),[y,x]=Lt(),[w,k]=Lt(),C=function(e,t){return(0,i.useMemo)((()=>e.reduce(((e,n)=>{let{eventName:r,handler:o}=n;return e[r]=e=>{o(e,t)},e}),{})),[e,t])}(s,t),S=Tt(n);return Ot((()=>(p.set(t,{id:t,key:a,node:y,activatorNode:w,data:S}),()=>{const e=p.get(t);e&&e.key===a&&p.delete(t)})),[p,t]),{active:c,activatorEvent:l,activeNodeRect:u,attributes:(0,i.useMemo)((()=>({role:h,tabIndex:m,"aria-disabled":r,"aria-pressed":!(!v||h!==Rr)||void 0,"aria-roledescription":g,"aria-describedby":d.draggable})),[r,h,m,v,g,d.draggable]),isDragging:v,listeners:r?void 0:C,node:y,over:f,setNodeRef:x,setActivatorNodeRef:k,transform:b}}({id:s,data:k,attributes:{...Hr,...n},disabled:x.draggable}),z=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i.useMemo)((()=>e=>{t.forEach((t=>t(e)))}),t)}(R,T),B=Boolean(D),F=B&&!g&&Tr(f)&&Tr(v),$=!b&&L,U=$&&F?P:null,V=F?null!=U?U:(null!=l?l:y)({rects:m,activeNodeRect:O,activeIndex:f,overIndex:v,index:w}):null,H=Tr(f)&&Tr(v)?a({id:s,items:d,activeIndex:f,overIndex:v}):w,q=null==D?void 0:D.id,G=(0,i.useRef)({activeId:q,items:d,newIndex:H,containerId:p}),W=d!==G.current.items,X=t({active:D,containerId:p,isDragging:L,isSorting:B,id:s,index:w,items:d,newIndex:G.current.newIndex,previousItems:G.current.items,previousContainerId:G.current.containerId,transition:u,wasDragging:null!=G.current.activeId}),Y=function(e){let{disabled:t,index:n,node:r,rect:o}=e;const[a,s]=(0,i.useState)(null),l=(0,i.useRef)(n);return Ot((()=>{if(!t&&n!==l.current&&r.current){const e=o.current;if(e){const t=hn(r.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==l.current&&(l.current=n)}),[t,n,r,o]),(0,i.useEffect)((()=>{a&&s(null)}),[a]),a}({disabled:!X,index:w,node:_,rect:S});return(0,i.useEffect)((()=>{B&&G.current.newIndex!==H&&(G.current.newIndex=H),p!==G.current.containerId&&(G.current.containerId=p),d!==G.current.items&&(G.current.items=d)}),[B,H,p,d]),(0,i.useEffect)((()=>{if(q===G.current.activeId)return;if(q&&!G.current.activeId)return void(G.current.activeId=q);const e=setTimeout((()=>{G.current.activeId=q}),50);return()=>clearTimeout(e)}),[q]),{active:D,activeIndex:f,attributes:A,data:k,rect:S,index:w,newIndex:H,items:d,isOver:E,isSorting:B,isDragging:L,listeners:M,node:_,overIndex:v,over:N,setNodeRef:z,setActivatorNodeRef:I,setDroppableNodeRef:R,setDraggableNodeRef:T,transform:null!=Y?Y:V,transition:Y||W&&G.current.newIndex===w?Vr:$&&!$t(j)||!u?void 0:B||X?Vt.Transition.toString({...u,property:Ur}):void 0}}Nn.Down,Nn.Right,Nn.Up,Nn.Left;const Gr=({children:e,id:t})=>{const{attributes:n,listeners:r,transform:o,transition:i,setNodeRef:a}=qr({id:t}),s={transform:Vt.Transform.toString(o),transition:i};return(0,$e.jsx)("tr",{ref:a,style:s,children:(0,$e.jsxs)($e.Fragment,{children:[(0,$e.jsx)("td",{style:{width:"1%"},children:(0,$e.jsx)("span",{className:"sortable-table__handle",...n,...r,children:(0,$e.jsx)(bt,{icon:xt,size:14})})}),e]})})},Wr=Ae.table`
	background: #fff;
	border: 0;
	border-radius: 3px;
	box-shadow: 0 0 0 1px rgb( 0 0 0 / 10% );
	border-spacing: 0;
	width: 100%;
	clear: both;
	margin: 0;
	font-size: 14px;

	.align-left {
		text-align: left;
		.components-flex {
			justify-content: flex-start;
			gap: 0;
		}
	}
	.align-right {
		text-align: right;
		.components-flex {
			justify-content: flex-end;
			flex-direction: row-reverse;
			gap: 0;
		}
	}
	.align-center {
		text-align: center;
		> * {
			margin: 0 auto;
		}
		.components-flex {
			display: block;
		}
	}

	.sortable-table__handle {
		cursor: move;
	}

	th {
		position: relative;
		color: #2c3338;
		text-align: left;
		vertical-align: middle;
		vertical-align: top;
		word-wrap: break-word;
	}

	tbody {
		td {
			vertical-align: top;
			margin-bottom: 9px;
		}
	}

	tfoot {
		td {
			text-align: left;
			vertical-align: middle;
		}
	}

	thead,
	tfoot,
	tbody {
		td,
		th {
			border-top: 1px solid rgb( 0 0 0 / 10% );
			border-bottom: 1px solid rgb( 0 0 0 / 10% );
			padding: 16px 0 16px 24px;
			line-height: 1.5;

			&:last-child {
				padding-right: 24px;
			}

			> svg,
			> .components-base-control {
				margin: 3px 0;
			}
		}
	}

	thead th {
		border-top: 0;
	}

	tfoot td {
		border-bottom: 0;
	}
`,Xr=({columns:e,data:t,setData:n,className:o,footerContent:a,placeholder:s})=>{const l=(0,r.useMemo)((()=>t.map((({id:e})=>e))),[t]),c=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i.useMemo)((()=>[...t].filter((e=>null!=e))),[...t])}(tn(Yn,{}),tn(Jn,{}),tn($n,{})),u=(e,t)=>{const n=e?.align||"left",r=e?.width||"auto";return{className:`${t}-${e.name} align-${n}`,style:{width:r}}};return(0,$e.jsx)(_r,{sensors:c,onDragEnd:function(e){const{active:r,over:o}=e;if(null!==r&&null!==o&&r?.id!==o?.id){const e=Or(t,l.indexOf(r.id),l.indexOf(o.id));n(e)}},collisionDetection:sn,modifiers:[jr],children:(0,$e.jsxs)(Wr,{className:`${o} sortable-table`,children:[(0,$e.jsx)("thead",{children:(0,$e.jsx)("tr",{children:e.map(((e,t)=>(0,$e.jsx)("th",{...u(e,"sortable-table__column"),colSpan:0===t?2:1,children:e.label},e.name)))})}),a&&(0,$e.jsx)("tfoot",{children:(0,$e.jsx)("tr",{children:(0,$e.jsx)("td",{colSpan:e.length+1,children:(0,$e.jsx)(a,{})})})}),(0,$e.jsx)("tbody",{children:(0,$e.jsx)(zr,{items:l,strategy:Nr,children:t.length?t.map((t=>t&&(0,$e.jsx)(Gr,{id:t.id,className:o,children:e.map((e=>(0,$e.jsx)("td",{...u(e,"sortable-table__column"),children:e.renderCallback?e.renderCallback(t):(0,$e.jsx)($e.Fragment,{children:(0,nt.objectHasProp)(t,e.name)&&t[e.name]})},`${t.id}-${e.name}`)))},t.id))):(0,$e.jsx)("tr",{children:(0,$e.jsx)("td",{colSpan:e.length+1,children:s})})})})]})})},Yr=Ae(Fe.Modal)`
	max-width: 600px;
	border-radius: 4px;
	@media ( min-width: 600px ) {
		min-width: 560px;
	}

	.components-modal__header {
		padding: 12px 24px;
		border-bottom: 1px solid #e0e0e0;
		position: relative;
		height: auto;
		width: auto;
		margin: 0 -24px 16px;

		@media ( max-width: 599px ) {
			button {
				display: none;
			}
		}
	}

	.components-modal__content {
		margin: 0;
		padding: 0 24px;

		@media ( max-width: 599px ) {
			display: flex;
			flex-direction: column;

			hr:last-of-type {
				margin-top: auto;
			}
		}

		.components-base-control {
			label {
				margin-top: 8px;
				text-transform: none !important;
			}
		}
	}
`,Kr=Ae.div`
	display: flex;
	justify-content: flex-end;
	border-top: 1px solid #e0e0e0;
	margin: 24px -24px 0;
	padding: 24px;

	> * {
		&:not( :first-of-type ) {
			margin-left: 8px;
		}
	}

	.button-link-delete {
		margin-right: auto;
		color: #d63638;
	}
`,Jr=({children:e,actions:t,title:n,onRequestClose:r,...o})=>(0,$e.jsxs)(Yr,{title:n,onRequestClose:r,...o,children:[e,(0,$e.jsx)(Kr,{children:t})]}),Zr=({formRef:e,values:t,setValues:n})=>{const{country:r,state:o}=t.address,i=e=>t=>{n((n=>({...n,[e]:t})))},a=e=>t=>{n((n=>({...n,address:{...n.address,[e]:t}})))},s=ct[r]&&Object.keys(ct[r]).length>0;return(0,$e.jsxs)("form",{ref:e,children:[(0,$e.jsx)(Fe.TextControl,{label:(0,Te.__)("Location name","woocommerce")+" *",name:"location_name",value:t.name,onChange:i("name"),autoComplete:"off",required:!0,"aria-required":!0,onInvalid:e=>{e.target.setCustomValidity((0,Te.__)("A Location title is required","woocommerce"))},onInput:e=>{e.target.setCustomValidity("")}}),(0,$e.jsx)(Fe.TextControl,{label:(0,Te.__)("Address","woocommerce"),name:"location_address",placeholder:(0,Te.__)("Address","woocommerce"),value:t.address.address_1,onChange:a("address_1"),autoComplete:"off"}),(0,$e.jsx)(Fe.TextControl,{label:(0,Te.__)("City","woocommerce"),name:"location_city",hideLabelFromVision:!0,placeholder:(0,Te.__)("City","woocommerce"),value:t.address.city,onChange:a("city"),autoComplete:"off"}),(0,$e.jsx)(Fe.TextControl,{label:(0,Te.__)("Postcode / ZIP","woocommerce"),name:"location_postcode",hideLabelFromVision:!0,placeholder:(0,Te.__)("Postcode / ZIP","woocommerce"),value:t.address.postcode,onChange:a("postcode"),autoComplete:"off"}),!s&&(0,$e.jsx)(Fe.TextControl,{placeholder:(0,Te.__)("State","woocommerce"),value:o,onChange:a("state")}),(0,$e.jsx)(Fe.SelectControl,{name:"location_country_state",label:(0,Te.__)("Country / State","woocommerce"),hideLabelFromVision:!0,placeholder:(0,Te.__)("Country / State","woocommerce"),value:!o&&s?`${r}:${Object.keys(ct[r])[0]}`:`${r}${o&&ct[r]?.[o]?":"+o:""}`,onChange:e=>{const[t,n=""]=e.split(":");a("country")(t),a("state")(n)},children:dt.options.map((e=>e.label?(0,$e.jsx)("optgroup",{label:e.label,children:e.options.map((e=>(0,$e.jsx)("option",{value:e.value,children:e.label},e.value)))},e.label):(0,$e.jsx)("option",{value:e.options[0].value,children:e.options[0].label},e.options[0].value)))}),(0,$e.jsx)(Fe.TextControl,{label:(0,Te.__)("Pickup details","woocommerce"),name:"pickup_details",value:t.details,onChange:i("details"),autoComplete:"off"})]})},Qr=({locationData:e,editingLocation:t,onClose:n,onSave:o,onDelete:i})=>{const a=(0,r.useRef)(null),[s,l]=(0,r.useState)(e);return e?(0,$e.jsx)(Jr,{onRequestClose:n,title:"new"===t?(0,Te.__)("Pickup location","woocommerce"):(0,Te.__)("Edit pickup location","woocommerce"),actions:(0,$e.jsxs)($e.Fragment,{children:["new"!==t&&(0,$e.jsx)(Fe.Button,{variant:"link",className:"button-link-delete",onClick:()=>{i(),n()},children:(0,Te.__)("Delete location","woocommerce")}),(0,$e.jsx)(Fe.Button,{variant:"secondary",onClick:n,children:(0,Te.__)("Cancel","woocommerce")}),(0,$e.jsx)(Fe.Button,{variant:"primary",onClick:()=>{const e=a?.current;e.reportValidity()&&(o(s),n())},children:(0,Te.__)("Done","woocommerce")})]}),children:(0,$e.jsx)(Zr,{formRef:a,values:s,setValues:l})}):null},eo=()=>(0,$e.jsxs)($e.Fragment,{children:[(0,$e.jsx)("h2",{children:(0,Te.__)("Pickup locations","woocommerce")}),(0,$e.jsx)("p",{children:(0,Te.__)("Define pickup locations for your customers to choose from during checkout.","woocommerce")}),(0,$e.jsx)(Fe.ExternalLink,{href:"https://woocommerce.com/document/woocommerce-blocks-local-pickup/",children:(0,Te.__)("Learn more","woocommerce")})]}),to=Ae.address`
	color: #757575;
	font-style: normal;
	display: inline;
	margin-left: 12px;
`,no=()=>{const{pickupLocations:e,setPickupLocations:t,toggleLocation:n,updateLocation:o,readOnlySettings:i}=ft(),[a,s]=(0,r.useState)(""),l=[{name:"name",label:(0,Te.__)("Pickup location","woocommerce"),width:"50%",renderCallback:e=>(0,$e.jsxs)($e.Fragment,{children:[e.name,(0,$e.jsx)(to,{children:ut(e.address)})]})},{name:"enabled",label:(0,Te.__)("Enabled","woocommerce"),align:"right",renderCallback:e=>(0,$e.jsx)(Fe.ToggleControl,{checked:!!(0,nt.isBoolean)(e.enabled)&&e.enabled,onChange:()=>n(e.id)})},{name:"edit",label:"",align:"center",width:"1%",renderCallback:e=>(0,$e.jsx)("button",{type:"button",className:"button-link-edit button-link",onClick:()=>{s(e.id)},children:(0,Te.__)("Edit","woocommerce")})}];return(0,$e.jsxs)(qe,{Description:eo,children:[(0,$e.jsx)(Xr,{className:"pickup-locations",columns:l,data:e,setData:e=>{t(e)},placeholder:(0,Te.__)("When you add a pickup location, it will appear here.","woocommerce"),footerContent:()=>(0,$e.jsx)(Fe.Button,{variant:"secondary",onClick:()=>{s("new")},children:(0,Te.__)("Add pickup location","woocommerce")})}),a&&(0,$e.jsx)(Qr,{locationData:"new"===a?{name:"",details:"",enabled:!0,address:{address_1:"",city:"",state:i.storeState,postcode:"",country:i.storeCountry}}:e.find((({id:e})=>e===a))||null,editingLocation:a,onSave:e=>{o(a,e)},onClose:()=>s(""),onDelete:()=>{o(a,null),s("")}})]})},ro=Ae(qe)`
	text-align: right;
	padding-top: 0;
	margin-top: 0;
`,oo=()=>{const{isSaving:e,save:t,isDirty:n}=ft();return(0,$e.jsx)(ro,{className:"submit",children:(0,$e.jsx)(Fe.Button,{variant:"primary",isBusy:e,disabled:e||!n,onClick:e=>{e.preventDefault();const n=e.target;n?.form?.reportValidity()&&t()},type:"submit",children:(0,Te.__)("Save changes","woocommerce")})})},io=Ae.form`
	margin: 48px auto 0;
	max-width: 1032px;
	display: flex;
	flex-flow: column;

	@media ( min-width: 960px ) {
		padding: 0 56px;
	}
`,ao=()=>(0,$e.jsx)(io,{id:"local-pickup-settings",children:(0,$e.jsxs)(ht,{children:[(0,$e.jsx)(vt,{}),(0,$e.jsx)(no,{}),(0,$e.jsx)(oo,{})]})}),so=document.getElementById("wc-shipping-method-pickup-location-settings-container");so&&(0,r.createRoot)(so).render((0,$e.jsx)(ao,{}))})();