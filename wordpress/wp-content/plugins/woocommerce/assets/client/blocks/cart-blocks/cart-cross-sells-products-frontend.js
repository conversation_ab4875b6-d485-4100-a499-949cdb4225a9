(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[3819],{9866:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Z});var r=s(5460),c=s(2796),o=s(6087),n=s(7723),a=s(4921),i=s(5703),l=s(371),d=s(1616),u=s(7052),p=s(8537),m=s(3993),g=s(4656),_=(s(4313),s(790));const h=(0,d.withProductDataContext)((e=>{const{className:t,align:s,isDescendentOfSingleProductTemplate:r}=e,o=(0,l.p)(e),{parentClassName:i}=(0,c.useInnerBlockLayoutContext)(),{product:d}=(0,c.useProductDataContext)();if(!(d.id&&d.on_sale||r))return null;const u="string"==typeof s?`wc-block-components-product-sale-badge--align-${s}`:"";return(0,_.jsx)("div",{className:(0,a.A)("wc-block-components-product-sale-badge",t,u,{[`${i}__product-onsale`]:i},o.className),style:o.style,children:(0,_.jsx)(g.Label,{label:(0,n.__)("Sale","woocommerce"),screenReaderLabel:(0,n.__)("Product on sale","woocommerce")})})}));s(1189);let w=function(e){return e.SINGLE="single",e.THUMBNAIL="thumbnail",e}({});const y=e=>(0,_.jsx)("img",{...e,src:i.PLACEHOLDER_IMG_SRC,alt:"",width:void 0,height:void 0}),x=({image:e,loaded:t,showFullSize:s,fallbackAlt:r,width:c,scale:o,height:n,aspectRatio:a})=>{const{thumbnail:i,src:l,srcset:d,sizes:u,alt:p}=e||{},m={alt:p||r,hidden:!t,src:i,...s&&{src:l,srcSet:d,sizes:u}},g={height:n,width:c,objectFit:o,aspectRatio:a};return(0,_.jsxs)(_.Fragment,{children:[m.src&&(0,_.jsx)("img",{style:g,"data-testid":"product-image",...m}),!e&&(0,_.jsx)(y,{style:g})]})},N=e=>{const{product:t}=e;return!(0,m.isEmpty)(t)&&(void 0===(s=e.showSaleBadge)||s);var s},b=e=>{const{aspectRatio:t,children:s,className:r,height:i,imageId:d,imageSizing:g=w.SINGLE,scale:b,showProductLink:k=!0,style:C,width:v,...j}=e,L=(0,l.p)(e),{parentClassName:P}=(0,c.useInnerBlockLayoutContext)(),{product:T,isLoading:f}=(0,c.useProductDataContext)(),{dispatchStoreEvent:A}=(0,u.y)();if(!T?.id)return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)("div",{className:(0,a.A)(r,"wc-block-components-product-image",{[`${P}__product-image`]:P},L.className),style:L.style,children:(0,_.jsx)(y,{})}),s]});const R=((e,t)=>e.images.length?t&&e.images.find((e=>e.id===t))||e.images[0]:null)(T,d);R&&(R.alt=R.alt||(0,p.decodeEntities)(T.name));const S=k?"a":o.Fragment,D=T?.name?
// translators: %s is the product name.
// translators: %s is the product name.
(0,n.sprintf)((0,n.__)("Link to %s","woocommerce"),T.name):"",$={href:k?T?.permalink:void 0,...k&&{"aria-label":D,onClick:()=>{A("product-view-link",{product:T})}}};return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsxs)("div",{className:(0,a.A)(r,"wc-block-components-product-image",{[`${P}__product-image`]:P},L.className),style:L.style,children:[N(e)&&(0,_.jsx)(h,{align:e.saleBadgeAlign||"right",...j}),(0,_.jsx)(S,{...k&&$,children:(0,_.jsx)(x,{fallbackAlt:(0,p.decodeEntities)(T.name),image:R,loaded:!f,showFullSize:g!==w.THUMBNAIL,width:v,height:i,scale:b,aspectRatio:(0,m.objectHasProp)(C,"dimensions")&&(0,m.objectHasProp)(C.dimensions,"aspectRatio")&&(0,m.isString)(C.dimensions.aspectRatio)?C.dimensions.aspectRatio:t})})]}),s]})};(0,d.withProductDataContext)(b);var k=s(4473);s(7578);const C=({children:e,headingLevel:t,elementType:s=`h${t}`,...r})=>(0,_.jsx)(s,{...r,children:e}),v=e=>{const{className:t,headingLevel:s=2,showProductLink:r=!0,linkTarget:o,align:n}=e,i=(0,l.p)(e),{parentClassName:d}=(0,c.useInnerBlockLayoutContext)(),{product:p}=(0,c.useProductDataContext)(),{dispatchStoreEvent:m}=(0,u.y)();return p.id?(0,_.jsx)(C,{headingLevel:s,className:(0,a.A)(t,i.className,"wc-block-components-product-title",{[`${d}__product-title`]:d,[`wc-block-components-product-title--align-${n}`]:n}),style:i.style,children:(0,_.jsx)(k.A,{disabled:!r,name:p.name,permalink:p.permalink,target:o,onClick:()=>{m("product-view-link",{product:p})}})}):(0,_.jsx)(C,{headingLevel:s,className:(0,a.A)(t,i.className,"wc-block-components-product-title",{[`${d}__product-title`]:d,[`wc-block-components-product-title--align-${n}`]:n}),style:i.style})},j=((0,d.withProductDataContext)(v),e=>({width:e/5*100+"%"})),L=({className:e,parentClassName:t})=>{const s=j(0);return(0,_.jsxs)("div",{className:(0,a.A)(`${e}__norating-container`,`${t}-product-rating__norating-container`),children:[(0,_.jsx)("div",{className:`${e}__norating`,role:"img",children:(0,_.jsx)("span",{style:s})}),(0,_.jsx)("span",{children:(0,n.__)("No Reviews","woocommerce")})]})},P=e=>{const{className:t,rating:s,reviews:r,parentClassName:c}=e,o=j(s),i=(0,n.sprintf)(/* translators: %f is referring to the average rating value */ /* translators: %f is referring to the average rating value */
(0,n.__)("Rated %f out of 5","woocommerce"),s),l={__html:(0,n.sprintf)(/* translators: %1$s is referring to the average rating value, %2$s is referring to the number of ratings */ /* translators: %1$s is referring to the average rating value, %2$s is referring to the number of ratings */
(0,n._n)("Rated %1$s out of 5 based on %2$s customer rating","Rated %1$s out of 5 based on %2$s customer ratings",r,"woocommerce"),(0,n.sprintf)('<strong class="rating">%f</strong>',s),(0,n.sprintf)('<span class="rating">%d</span>',r))};return(0,_.jsx)("div",{className:(0,a.A)(`${t}__stars`,`${c}__product-rating__stars`),role:"img","aria-label":i,children:(0,_.jsx)("span",{style:o,dangerouslySetInnerHTML:l})})},T=e=>{const{className:t,reviews:s}=e,r=(0,n.sprintf)(/* translators: %s is referring to the total of reviews for a product */ /* translators: %s is referring to the total of reviews for a product */
(0,n._n)("(%s customer review)","(%s customer reviews)",s,"woocommerce"),s);return(0,_.jsx)("span",{className:`${t}__reviews_count`,children:r})},f=e=>{const{className:t="wc-block-components-product-rating",showReviewCount:s,showMockedReviews:r,parentClassName:c="",rating:o,reviews:n,styleProps:i,textAlign:l}=e,d=(0,a.A)(i.className,t,{[`${c}__product-rating`]:c,[`has-text-align-${l}`]:l}),u=r&&(0,_.jsx)(L,{className:t,parentClassName:c}),p=n?(0,_.jsx)(P,{className:t,rating:o,reviews:n,parentClassName:c}):u,m=n&&s;return(0,_.jsx)("div",{className:d,style:i.style,children:(0,_.jsxs)("div",{className:`${t}__container`,children:[p,m?(0,_.jsx)(T,{className:t,reviews:n}):null]})})};s(7545);const A=e=>{const{textAlign:t="",shouldDisplayMockedReviewsWhenProductHasNoReviews:s}=e,r=(0,l.p)(e),{parentClassName:o}=(0,c.useInnerBlockLayoutContext)(),{product:n}=(0,c.useProductDataContext)(),a=(e=>{const t=parseFloat(e.average_rating);return Number.isFinite(t)&&t>0?t:0})(n),i=(e=>{const t=(0,m.isNumber)(e.review_count)?e.review_count:parseInt(e.review_count,10);return Number.isFinite(t)&&t>0?t:0})(n);return(0,_.jsx)(f,{className:"wc-block-components-product-rating-stars",showMockedReviews:s,styleProps:r,parentClassName:o,reviews:i,rating:a,textAlign:t})};(0,d.withProductDataContext)(A);var R=s(6711),S=s(910);const D=e=>{const{className:t,textAlign:s,isDescendentOfSingleProductTemplate:r}=e,o=(0,l.p)(e),{parentName:n,parentClassName:i}=(0,c.useInnerBlockLayoutContext)(),{product:d}=(0,c.useProductDataContext)(),u="woocommerce/all-products"===n,p=r&&!("woocommerce/add-to-cart-with-options-grouped-product-item"===n),m=(0,a.A)("wc-block-components-product-price",t,o.className,{[`${i}__product-price`]:i});if(!d.id&&!r){const e=(0,_.jsx)(R.A,{align:s,className:m});return u?(0,_.jsx)("div",{className:"wp-block-woocommerce-product-price",children:e}):e}const g=d.prices,h=p?(0,S.getCurrencyFromPriceResponse)():(0,S.getCurrencyFromPriceResponse)(g),w="5000",y=g.price!==g.regular_price,x=(0,a.A)({[`${i}__product-price__value`]:i,[`${i}__product-price__value--on-sale`]:y}),N=(0,_.jsx)(R.A,{align:s,className:m,style:o.style,regularPriceStyle:o.style,priceStyle:o.style,priceClassName:x,currency:h,price:p?w:g.price,minPrice:g?.price_range?.min_amount,maxPrice:g?.price_range?.max_amount,regularPrice:p?w:g.regular_price,regularPriceClassName:(0,a.A)({[`${i}__product-price__regular`]:i})});return u?(0,_.jsx)("div",{className:"wp-block-woocommerce-product-price",children:N}):N};var $=s(1057),I=s(8331),E=(s(7316),s(7143));const O="woocommerce/product-type-template-state",B="SWITCH_PRODUCT_TYPE",F="SET_PRODUCT_TYPES",W="REGISTER_LISTENER",H="UNREGISTER_LISTENER",M=(0,s(7254).c)(),z={productTypes:{list:M,current:M[0]?.slug},listeners:[]},G={switchProductType:e=>({type:B,current:e}),setProductTypes:e=>({type:F,productTypes:e}),registerListener:e=>({type:W,listener:e}),unregisterListener:e=>({type:H,listener:e})},Q=(0,E.createReduxStore)(O,{reducer:(e=z,t)=>{switch(t.type){case F:return{...e,productTypes:{...e.productTypes,list:t.productTypes||[]}};case B:return{...e,productTypes:{...e.productTypes,current:t.current}};case W:return{...e,listeners:[...e.listeners,t.listener||""]};case H:return{...e,listeners:e.listeners.filter((e=>e!==t.listener))};default:return e}},actions:G,selectors:{getProductTypes:e=>e.productTypes.list,getCurrentProductType:e=>e.productTypes.list.find((t=>t.slug===e.productTypes.current)),getRegisteredListeners:e=>e.listeners}});(0,E.select)(O)||(0,E.register)(Q);const U=({product:e,isDescendantOfAddToCartWithOptions:t,className:s,style:r})=>{const{id:c,permalink:o,add_to_cart:l,has_options:d,is_purchasable:m,is_in_stock:g}=e,{dispatchStoreEvent:h}=(0,u.y)(),{cartQuantity:w,addingToCart:y,addToCart:x}=(0,$.R)(c),N=Number.isFinite(w)&&w>0,b=!d&&m&&g,k=(0,p.decodeEntities)(l?.description||""),C=(({cartQuantity:e,productCartDetails:t,isDescendantOfAddToCartWithOptions:s})=>Number.isFinite(e)&&e>0?(0,n.sprintf)(/* translators: %s number of products in cart. */ /* translators: %s number of products in cart. */
(0,n._n)("%d in cart","%d in cart",e,"woocommerce"),e):s&&t?.single_text?t?.single_text:t?.text||(0,n.__)("Add to cart","woocommerce"))({cartQuantity:w,productCartDetails:l,isDescendantOfAddToCartWithOptions:t}),v=b?"button":"a",j={};return b?j.onClick=async()=>{await x(),h("cart-add-item",{product:e});const{cartRedirectAfterAdd:t}=(0,i.getSetting)("productsSettings");t&&(window.location.href=I.Vo)}:(j.href=o,j.rel="nofollow",j.onClick=()=>{h("product-view-link",{product:e})}),(0,_.jsx)(v,{...j,"aria-label":k,disabled:y,className:(0,a.A)(s,"wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button",{loading:y,added:N}),style:r,children:C})},J=({className:e,style:t})=>(0,_.jsx)("button",{className:(0,a.A)("wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button","wc-block-components-product-button__button--placeholder",e),style:t,disabled:!0,children:(0,n.__)("Add to cart","woocommerce")}),V=({className:e,style:t,blockClientId:s})=>{const{current:r,registerListener:c,unregisterListener:i}=function(){const{productTypes:e,current:t,registeredListeners:s}=(0,E.useSelect)((e=>{const{getProductTypes:t,getCurrentProductType:s,getRegisteredListeners:r}=e(Q);return{productTypes:t(),current:s(),registeredListeners:r()}}),[]),{switchProductType:r,registerListener:c,unregisterListener:o}=(0,E.useDispatch)(Q);return{productTypes:e,current:t,set:r,registeredListeners:s,registerListener:c,unregisterListener:o}}();(0,o.useEffect)((()=>{if(s)return c(s),()=>{i(s)}}),[s,c,i]);const l="external"===r?.slug?(0,n.__)("Buy product","woocommerce"):(0,n.__)("Add to cart","woocommerce");return(0,_.jsx)("button",{className:(0,a.A)("wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button",e),style:t,disabled:!0,children:l})},Y=e=>{const{className:t,textAlign:s,blockClientId:r}=e,o=(0,l.p)(e),{parentClassName:n}=(0,c.useInnerBlockLayoutContext)(),{isLoading:i,product:d}=(0,c.useProductDataContext)();return(0,_.jsx)("div",{className:(0,a.A)(t,"wp-block-button","wc-block-components-product-button",{[`${n}__product-add-to-cart`]:n,[`align-${s}`]:s}),children:i?(0,_.jsx)(J,{className:o.className,style:o.style}):(0,_.jsx)(_.Fragment,{children:d.id?(0,_.jsx)(U,{product:d,style:o.style,className:o.className,isDescendantOfAddToCartWithOptions:e["woocommerce/isDescendantOfAddToCartWithOptions"]}):(0,_.jsx)(V,{style:o.style,className:o.className,isLoading:i,blockClientId:r})})})},q=((0,d.withProductDataContext)(Y),({product:e})=>(0,_.jsx)("div",{className:"cross-sells-product",children:(0,_.jsx)(c.InnerBlockLayoutContextProvider,{parentName:"woocommerce/cart-cross-sells-block",parentClassName:"wp-block-cart-cross-sells-product",children:(0,_.jsxs)(c.ProductDataContextProvider,{isLoading:!1,product:e,children:[(0,_.jsxs)("div",{children:[(0,_.jsx)(b,{className:"",showSaleBadge:!0,productId:e.id,showProductLink:!0,saleBadgeAlign:"left",imageSizing:w.SINGLE,isDescendentOfQueryLoop:!1,scale:"cover",aspectRatio:"1:1"}),(0,_.jsx)(v,{align:"",headingLevel:3,showProductLink:!0}),(0,_.jsx)(A,{isDescendentOfQueryLoop:!1,isDescendentOfSingleProductBlock:!1,productId:e.id,postId:0,shouldDisplayMockedReviewsWhenProductHasNoReviews:!1}),(0,_.jsx)(D,{})]}),(0,_.jsx)(Y,{})]})})})),K=({products:e,columns:t})=>{const s=e.map(((e,s)=>s>=t?null:(0,_.jsx)(q,{isLoading:!1,product:e},e.id)));return(0,_.jsx)("div",{children:s})};var X=s(7292);const Z=({className:e,columns:t})=>{const{crossSellsProducts:s}=(0,r.V)();return void 0===t&&(t=X.attributes.columns.default),(0,_.jsx)(K,{className:e,columns:t,products:s})}},7316:()=>{},1189:()=>{},7545:()=>{},4313:()=>{},7578:()=>{}}]);