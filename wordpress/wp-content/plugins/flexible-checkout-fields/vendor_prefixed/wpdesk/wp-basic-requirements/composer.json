{"name": "wpdesk/wp-basic-requirements", "authors": [{"name": "Krzysiek", "email": "<EMAIL>"}], "require": {"php": ">=5.3"}, "require-dev": {"php": ">=5.5", "phpunit/phpunit": "^8", "mockery/mockery": "*", "10up/wp_mock": "*", "wimg/php-compatibility": "^8", "wpdesk/wp-code-sniffer": "^1.2"}, "autoload": {}, "autoload-dev": {"classmap": ["src", "tests"]}, "extra": {"text-domain": "wp-basic-requirements", "translations-folder": "lang", "po-files": {"pl_PL": "pl_PL.po", "en_AU": "en_AU.po", "en_CA": "en_CA.po", "en_GB": "en_GB.po", "de_DE": "de_DE.po"}}, "scripts": {"phpcs": "phpcs", "phpunit-unit": "phpunit --configuration phpunit-unit.xml --coverage-text --colors=never", "phpunit-unit-fast": "phpunit --configuration phpunit-unit.xml --no-coverage", "phpunit-integration": "phpunit --configuration phpunit-integration.xml --coverage-text --colors=never", "phpunit-integration-fast": "phpunit --configuration phpunit-integration.xml --no-coverage", "docs": "apigen generate"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}}