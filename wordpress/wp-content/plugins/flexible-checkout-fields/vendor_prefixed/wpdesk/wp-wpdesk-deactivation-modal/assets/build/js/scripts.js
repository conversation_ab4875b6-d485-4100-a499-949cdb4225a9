!function(){"use strict";var t,e={364:function(){function t(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var e=function(){function e(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),this.set_vars()&&this.set_events()}var n,i,o;return n=e,(i=[{key:"set_vars",value:function(){if(this.button_open=document.querySelector('[data-slug="<?php echo $plugin_slug; ?>"] a[href*="action=deactivate"]'),this.modal_wrapper=document.querySelector('[data-wpdesk-deactivation-modal="<?php echo $plugin_slug; ?>"]'),this.button_open&&this.modal_wrapper)return this.button_close=this.modal_wrapper.querySelector("[data-wpdesk-deactivation-modal-button-close]"),this.button_submit=this.modal_wrapper.querySelector("[data-wpdesk-deactivation-modal-button-submit]"),this.button_skip=this.modal_wrapper.querySelector("[data-wpdesk-deactivation-modal-button-skip]"),this.form_wrapper=this.modal_wrapper.querySelector("[data-wpdesk-deactivation-modal-form]"),this.events={open_modal:this.open_modal.bind(this)},this.settings={delay_click_deactivate:10},this.status={is_sent:!1},!0}},{key:"set_events",value:function(){this.button_open.addEventListener("click",this.events.open_modal),this.modal_wrapper.addEventListener("click",this.close_modal.bind(this,!1)),this.button_close.addEventListener("click",this.close_modal.bind(this,!1)),this.button_skip.addEventListener("click",this.close_modal.bind(this,!0)),this.form_wrapper.addEventListener("click",(function(t){t.stopPropagation()})),this.form_wrapper.addEventListener("submit",this.init_form_submission.bind(this))}},{key:"open_modal",value:function(t){t.preventDefault(),this.button_open.removeEventListener("click",this.events.open_modal),this.modal_wrapper.removeAttribute("hidden")}},{key:"close_modal",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;n&&n.preventDefault();var i=this.settings.delay_click_deactivate;this.modal_wrapper.setAttribute("hidden","hidden"),t&&setTimeout((function(){e.button_open.click()}),i)}},{key:"init_form_submission",value:function(t){t.preventDefault(),this.form_wrapper.removeEventListener("submit",this.events.submit_form),this.close_modal(!0),setTimeout(this.submit_form.bind(this),0)}},{key:"submit_form",value:function(){if(!this.status.is_sent){this.status.is_sent=!0;var t=this.form_wrapper.getAttribute("action"),e=new XMLHttpRequest;e.open("POST",t,!0),e.send(new FormData(this.form_wrapper))}}}])&&t(n.prototype,i),o&&t(n,o),e}();new function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),new e}},109:function(){}},n={};function i(t){var o=n[t];if(void 0!==o)return o.exports;var r=n[t]={exports:{}};return e[t](r,r.exports,i),r.exports}i.m=e,t=[],i.O=function(e,n,o,r){if(!n){var s=1/0;for(l=0;l<t.length;l++){n=t[l][0],o=t[l][1],r=t[l][2];for(var a=!0,u=0;u<n.length;u++)(!1&r||s>=r)&&Object.keys(i.O).every((function(t){return i.O[t](n[u])}))?n.splice(u--,1):(a=!1,r<s&&(s=r));if(a){t.splice(l--,1);var c=o();void 0!==c&&(e=c)}}return e}r=r||0;for(var l=t.length;l>0&&t[l-1][2]>r;l--)t[l]=t[l-1];t[l]=[n,o,r]},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},function(){var t={861:0,33:0};i.O.j=function(e){return 0===t[e]};var e=function(e,n){var o,r,s=n[0],a=n[1],u=n[2],c=0;if(s.some((function(e){return 0!==t[e]}))){for(o in a)i.o(a,o)&&(i.m[o]=a[o]);if(u)var l=u(i)}for(e&&e(n);c<s.length;c++)r=s[c],i.o(t,r)&&t[r]&&t[r][0](),t[s[c]]=0;return i.O(l)},n=self.webpackChunkwp_wpdesk_deactivation_modal=self.webpackChunkwp_wpdesk_deactivation_modal||[];n.forEach(e.bind(null,0)),n.push=e.bind(null,n.push.bind(n))}(),i.O(void 0,[33],(function(){return i(364)}));var o=i.O(void 0,[33],(function(){return i(109)}));o=i.O(o)}();