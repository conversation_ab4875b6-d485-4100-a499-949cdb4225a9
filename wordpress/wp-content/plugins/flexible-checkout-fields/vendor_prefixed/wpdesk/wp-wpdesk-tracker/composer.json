{"name": "wpdesk/wp-wpdesk-tracker", "license": "MIT", "authors": [{"name": "WP Desk", "homepage": "https://wpdesk.net"}, {"name": "Krzysiek", "email": "<EMAIL>"}], "require": {"php": ">=7.4 || ^8", "wpdesk/wp-builder": "^2.0", "wpdesk/wp-notice": "^3.1", "wpdesk/wp-view": "^2", "psr/log": "^1 || ^2"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9", "wpdesk/phpstan-rules": "^1", "wpdesk/wp-code-sniffer": "^1", "10up/wp_mock": "^1"}, "autoload": {"classmap": ["src/"], "exclude-from-classmap": ["scr/PSR/"], "psr-4": {"FcfVendor\\": "src/PSR/"}}, "autoload-dev": {"classmap": ["tests/"]}, "extra": {"text-domain": "wpdesk-tracker", "translations-folder": "lang", "po-files": {"pl_PL": "pl_PL.po", "es_ES": "es_ES.po", "en_AU": "en_AU.po", "en_CA": "en_CA.po", "en_GB": "en_GB.po", "de_DE": "de_DE.po"}}, "scripts": {"test": "echo composer is alive", "phpcs": "phpcs", "phpunit-unit": "phpunit --configuration phpunit-unit.xml --coverage-text --colors=never", "phpunit-unit-fast": "phpunit --configuration phpunit-unit.xml --no-coverage", "phpunit-integration": "phpunit --configuration phpunit-integration.xml --coverage-text --colors=never", "phpunit-integration-fast": "phpunit --configuration phpunit-integration.xml --no-coverage", "docs": "apigen generate"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true}}}