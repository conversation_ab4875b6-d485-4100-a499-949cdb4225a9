{"name": "wpdesk/ltv-dashboard-widget", "description": "Library for displaying ltv widget in WordPress dashboard.", "license": "MIT", "keywords": ["wordpress", "notice", "admin"], "minimum-stability": "stable", "config": {"platform": {"php": "7.4"}}, "require": {"php": ">=7.4"}, "require-dev": {"phpunit/phpunit": "^8", "wp-coding-standards/wpcs": "^0.14.1", "squizlabs/php_codesniffer": "^3.0.2", "mockery/mockery": "*", "10up/wp_mock": "*", "wimg/php-compatibility": "^8"}, "autoload": {"psr-4": {"FcfVendor\\WPDesk\\Dashboard\\": "src/"}}, "extra": {"text-domain": "wpdesk_ltv_dashboard_widget", "translations-folder": "lang", "po-files": {"pl_PL": "wpdesk-ltv-dashboard-widget-pl_PL.po"}}, "autoload-dev": {}, "scripts": {"phpcs": "phpcs", "phpunit-unit": "phpunit --configuration phpunit-unit.xml --coverage-text --colors=never", "phpunit-unit-fast": "phpunit --configuration phpunit-unit.xml --no-coverage", "phpunit-integration": "phpunit --configuration phpunit-integration.xml --coverage-text --colors=never", "phpunit-integration-fast": "phpunit --configuration phpunit-integration.xml --no-coverage"}}