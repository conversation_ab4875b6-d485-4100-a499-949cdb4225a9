import"./js/translations.d159963e.js";import{g as O<PERSON>}from"./js/helpers.f993c04f.js";import{u as x,c as Le,l as ro,e as me,a as co,w as To,$ as Bo}from"./js/index.a13799ab.js";import{_ as d,s as D}from"./js/default-i18n.20001971.js";import{v as $,o as f,c as S,H as b,l as w,x as _,t as g,a as m,J as Te,k as U,b as k,G as Qe,N as uo,K as Be,Y as ae,h as ce,F as Po,q as Eo}from"./js/runtime-dom.esm-bundler.baf35205.js";import{l as ue}from"./js/index.7c01c5f2.js";import{l as Ao,B as Re,b as Ue,d as Je}from"./js/index.d63536bd.js";import{l as xo}from"./js/index.d80c2c2c.js";import{L as R,K as Ho}from"./js/helpers.a0b389be.js";import{u as Mo}from"./js/JsonValues.3fcfec97.js";import{S as No}from"./js/AddPlus.8648d7b1.js";import{S as po}from"./js/Caret.7cc96622.js";import{S as Do}from"./js/External.49bc8f29.js";import{_ as W}from"./js/_plugin-vue_export-helper.eefbdd86.js";import{u as qo}from"./js/PostTypes.dafa8837.js";import{B as Ro}from"./js/HighlightToggle.f01975c9.js";import{C as Ge}from"./js/Tooltip.78b61f71.js";import{G as Uo,a as zo}from"./js/Row.c7b7fb90.js";import{C as jo}from"./js/ImageUploader.4aebc2ee.js";import{S as mo}from"./js/Info.a76f68ec.js";import{c as Fe}from"./js/cleanForSlug.cacc99d0.js";import{u as fe,o as Fo,a as Ke,f as be}from"./js/TableOfContentsStore.395f3f7c.js";import{D as Zo,S as Wo}from"./js/vuedraggable.umd.724fe865.js";import{S as Qo}from"./js/Eye.60f8b51e.js";import{S as Jo}from"./js/Link.ad58218a.js";import{C as Go}from"./js/Index.278f7f5c.js";import{C as Ko}from"./js/Index.9f340035.js";import{G as Yo}from"./js/constants.a8a14dc3.js";import{d as ot}from"./js/debounce.f2bce338.js";import"./js/Checkbox.d4a94670.js";import"./js/Checkmark.b35299d3.js";import"./js/Radio.2d9be588.js";import"./js/Img.633d5228.js";import"./js/Plus.d91e5f23.js";import"./js/toString.1e64e8a6.js";import"./js/toNumber.89dbadc6.js";const ie=window.wp,F=ie.element.createElement,Xo=ie.element.Fragment;var kt;const es=((kt=ie.blockEditor)==null?void 0:kt.InspectorControls)||ie.editor.InspectorControls,ts=ie.components.PanelBody,os=ie.components.Disabled,ss=ie.serverSideRender||ie.components.ServerSideRender,Ce="all-in-one-seo-pack",ns=F("svg",{width:24,height:25,viewBox:"0 0 24 25",xmlns:"http://www.w3.org/2000/svg"},F("path",{d:"M1.7002 5.31067H10.8705L17.8705 12.5L10.8705 19.6893H1.7002V5.31067ZM10.2856 12.4999C10.2856 13.3284 9.61396 14.0001 8.7854 14.0001C7.95684 14.0001 7.28516 13.3284 7.28516 12.4999C7.28516 11.6713 7.95684 10.9996 8.7854 10.9996C9.61396 10.9996 10.2856 11.6713 10.2856 12.4999ZM6.1933 12.5001C6.1933 13.195 5.62995 13.7584 4.93503 13.7584C4.2401 13.7584 3.67676 13.195 3.67676 12.5001C3.67676 11.8052 4.2401 11.2418 4.93503 11.2418C5.62995 11.2418 6.1933 11.8052 6.1933 12.5001ZM12.6342 13.7584C13.3292 13.7584 13.8925 13.195 13.8925 12.5001C13.8925 11.8052 13.3292 11.2418 12.6342 11.2418C11.9393 11.2418 11.376 11.8052 11.376 12.5001C11.376 13.195 11.9393 13.7584 12.6342 13.7584ZM15.3002 5.31067H12.5771L19.5771 12.5L12.5771 19.6893H15.3002L22.3002 12.5L15.3002 5.31067Z"}));var It,Lt;const as=((Lt=(It=window==null?void 0:window.aioseo)==null?void 0:It.currentPost)==null?void 0:Lt.postType)??"";let ho="";var Ct,Vt;const ze=(Vt=(Ct=window==null?void 0:window.aioseo)==null?void 0:Ct.postData)==null?void 0:Vt.postTypes.find(e=>e.name===as);ze&&0<ze.taxonomies.length&&(ho=ze.taxonomies[0]);const go="aioseo/breadcrumbs";var Ot,Tt,Bt,Pt,Et,At,xt,Ht,Mt,Nt,Dt,qt,Rt,Ut,zt,jt,Ft,Zt,Wt,Qt,Jt,Gt,Kt,Yt,Xt,eo,to;const is={title:d("AIOSEO - Breadcrumbs",Ce),description:d("Automatically output a breadcrumb trail to help your users and search engines navigate your site.",Ce),category:"aioseo",icon:ns,example:{},attributes:{primaryTerm:{type:"string",default:null},breadcrumbSettings:{type:"object",default:{default:((Bt=(Tt=(Ot=window==null?void 0:window.aioseo)==null?void 0:Ot.currentPost)==null?void 0:Tt.breadcrumb_settings)==null?void 0:Bt.default)??!0,separator:((At=(Et=(Pt=window==null?void 0:window.aioseo)==null?void 0:Pt.currentPost)==null?void 0:Et.breadcrumb_settings)==null?void 0:At.separator)??"›",showHomeCrumb:((Mt=(Ht=(xt=window==null?void 0:window.aioseo)==null?void 0:xt.currentPost)==null?void 0:Ht.breadcrumb_settings)==null?void 0:Mt.showHomeCrumb)??!0,showTaxonomyCrumbs:((qt=(Dt=(Nt=window==null?void 0:window.aioseo)==null?void 0:Nt.currentPost)==null?void 0:Dt.breadcrumb_settings)==null?void 0:qt.showTaxonomyCrumbs)??!0,showParentCrumbs:((zt=(Ut=(Rt=window==null?void 0:window.aioseo)==null?void 0:Rt.currentPost)==null?void 0:Ut.breadcrumb_settings)==null?void 0:zt.showParentCrumbs)??!0,template:((Zt=(Ft=(jt=window==null?void 0:window.aioseo)==null?void 0:jt.currentPost)==null?void 0:Ft.breadcrumb_settings)==null?void 0:Zt.template)??"default",parentTemplate:((Jt=(Qt=(Wt=window==null?void 0:window.aioseo)==null?void 0:Wt.currentPost)==null?void 0:Qt.breadcrumb_settings)==null?void 0:Jt.parentTemplate)??"default",taxonomy:((Yt=(Kt=(Gt=window==null?void 0:window.aioseo)==null?void 0:Gt.currentPost)==null?void 0:Kt.breadcrumb_settings)==null?void 0:Yt.taxonomy)||ho,primaryTerm:((to=(eo=(Xt=window==null?void 0:window.aioseo)==null?void 0:Xt.currentPost)==null?void 0:eo.breadcrumb_settings)==null?void 0:to.primaryTerm)??null}}},edit:function(e){var p,u,h;const{setAttributes:t,attributes:n,clientId:s}=e,o=x(),i=`aioseo-${s}-settings`;window.aioseoBus.$on("standalone-update-post",y=>{!(y!=null&&y.primary_term)&&!(y!=null&&y.breadcrumb_settings)||(y!=null&&y.primary_term&&t({primaryTerm:JSON.stringify(y.primary_term)}),y!=null&&y.breadcrumb_settings&&t({breadcrumbSettings:y.breadcrumb_settings}))});let l=(p=n.breadcrumbSettings)==null?void 0:p.primaryTerm;if((u=n.breadcrumbSettings)!=null&&u.taxonomy){const y=Oo((h=n.breadcrumbSettings)==null?void 0:h.taxonomy,!0);y[0]&&y[0]!==l&&(l=y[0])}const r=()=>{window.aioseoBus.$emit("do-post-settings-main-tab-change",{name:"advanced",context:"metabox"}),ie.data.dispatch("core/edit-post").openGeneralSidebar("aioseo-post-settings-sidebar/aioseo-post-settings-sidebar"),setTimeout(()=>{window.aioseoBus.$emit("do-post-settings-main-tab-change",{name:"advanced",context:"sidebar"})},100)},c=F(es,null,F(ts,{title:d("Breadcrumb Settings",Ce),initialOpen:!0},F("div",{},F("div",{id:i},F("p",{className:"aioseo-breadcrumbs-sidebar-text"},[d("You can customize your breadcrumb trail under ",Ce),F("a",{href:"#",onClick:()=>r()},d("Advanced > Breadcrumbs.",Ce))]))))),a=o.aioseo.user;return F(Xo,{},a.capabilities.aioseo_page_advanced_settings?c:null,F("div",{},F(window.aioseo.options.breadcrumbs.enable?os:"div",null,F(ss,{block:go,attributes:{primaryTerm:n.primaryTerm,breadcrumbSettings:{...n.breadcrumbSettings,primaryTerm:l}}}))))},save:function(){return null}},ls=Object.freeze(Object.defineProperty({__proto__:null,name:go,settings:is},Symbol.toStringTag,{value:"Module"}));var bo=function(e,t,n,s){var o;t[0]=0;for(var i=1;i<t.length;i++){var l=t[i++],r=t[i]?(t[0]|=l?1:2,n[t[i++]]):t[++i];l===3?s[0]=r:l===4?s[1]=Object.assign(s[1]||{},r):l===5?(s[1]=s[1]||{})[t[++i]]=r:l===6?s[1][t[++i]]+=r+"":l?(o=e.apply(r,bo(e,r,n,["",null])),s.push(o),r[0]?t[0]|=2:(t[i-2]=0,t[i]=o)):s.push(r)}return s},st=new Map;function rs(e){var t=st.get(this);return t||(t=new Map,st.set(this,t)),(t=bo(this,t.get(e)||(t.set(e,t=function(n){for(var s,o,i=1,l="",r="",c=[0],a=function(h){i===1&&(h||(l=l.replace(/^\s*\n\s*|\s*\n\s*$/g,"")))?c.push(0,h,l):i===3&&(h||l)?(c.push(3,h,l),i=2):i===2&&l==="..."&&h?c.push(4,h,0):i===2&&l&&!h?c.push(5,0,!0,l):i>=5&&((l||!h&&i===5)&&(c.push(i,0,l,o),i=6),h&&(c.push(i,h,0,o),i=6)),l=""},p=0;p<n.length;p++){p&&(i===1&&a(),a(p));for(var u=0;u<n[p].length;u++)s=n[p][u],i===1?s==="<"?(a(),c=[c],i=3):l+=s:i===4?l==="--"&&s===">"?(i=1,l=""):l=s+l[0]:r?s===r?r="":l+=s:s==='"'||s==="'"?r=s:s===">"?(a(),i=1):i&&(s==="="?(i=5,o=l,l=""):s==="/"&&(i<5||n[p][u+1]===">")?(a(),i===3&&(c=c[0]),i=c,(c=c[0]).push(2,0,i),i=0):s===" "||s==="	"||s===`
`||s==="\r"?(a(),i=2):l+=s),i===3&&l==="!--"&&(i=4,c=c[0])}return a(),c}(e)),t),arguments,[])).length>1?t:t[0]}const te=rs.bind(window.wp.element.createElement),Ze=e=>Object.prototype.toString.call(e).slice(8,-1).toLowerCase(),ke=(e,t)=>{function n(){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(!ke(e[r],t[r]))return!1;return!0}function s(){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&!ke(e[r],t[r]))return!1;return!0}function o(){return e.toString()===t.toString()}function i(){return e===t}const l=Ze(e);return l!==Ze(t)?!1:l==="array"?n():l==="object"?s():l==="function"?o():i()},ne=e=>{const t=c=>{for(const a in e)Object.prototype.hasOwnProperty.call(e,a)&&(c[a]=ne(e[a]))},n=()=>{const c={};return t(c),c},s=()=>e.map(function(c){return ne(c)}),o=()=>{const c=new Map;for(const[a,p]of e)c.set(a,ne(p));return c},i=()=>{const c=new Set;for(const a of e)c.add(ne(a));return c},l=()=>{const c=e.bind(void 0);return t(c),c},r=Ze(e);return r==="object"?n():r==="array"?s():r==="map"?o():r==="set"?i():r==="function"?l():e},fo=(e,t=!1)=>{const{body:n}=document.implementation.createHTMLDocument("");n.innerHTML=e;const s=n.getElementsByTagName("*");let o=s.length;for(;o--;){const i=s[o];if(i.tagName==="SCRIPT")i.parentNode.removeChild(i);else{let l=i.attributes.length;for(;l--;){const{name:r}=i.attributes[l];r.startsWith("on")&&i.removeAttribute(r)}}}return t?n.textContent.trim():n.innerHTML},ds=()=>"aioseo-"+new Date().getTime().toString(36),pe=(e,t)=>{const n=t.findIndex(s=>s.clientId===e);n!==-1&&(t[n].app.unmount(),t.splice(n,1))},he="all-in-one-seo-pack",cs={setup(){const{getJsonValues:e,setJsonValues:t}=Mo();return{getJsonValues:e,optionsStore:Le(),setJsonValues:t}},components:{SvgAddPlus:No,SvgClose:po,SvgExternal:Do},props:{type:{type:String,required:!0}},data(){return{excludeOptions:[],strings:{typeToSearch:d("Type to search...",he),noOptionsPosts:d("Begin typing a post ID, title or slug to search...",he),noOptionsTerms:d("Begin typing a term ID or name to search...",he),noResult:d("No results found for your search. Try again!",he),clear:d("Clear",he),id:d("ID",he),type:d("Type",he)}}},computed:{optionName:{get(){return this.type==="posts"?JSON.parse(this.$root.$data.excluded_posts):JSON.parse(this.$root.$data.excluded_terms)},set(e){if(e=JSON.stringify(e),this.type==="posts"){this.$root.$data.excluded_posts=e;return}this.$root.$data.excluded_terms=e}},noOptions(){return this.type==="posts"?this.strings.noOptionsPosts:this.strings.noOptionsTerms}},methods:{processGetObjects(e){return this.optionsStore.getObjects({query:e,type:this.type}).then(t=>{this.excludeOptions=t.body.objects})},getOptionTitle(e,t){e=e.replace(/<\/?[^>]+(>|$)/g,""),t=t.replace(/<\/?[^>]+(>|$)/g,"");const n=new RegExp(`(${t})`,"gi");return e.replace(n,'<span class="search-term">$1</span>')}}},us={class:"aioseo-exclude-posts"},ps={class:"option"},ms=["innerHTML"],hs={class:"option-details"},gs=["href"],bs={class:"multiselect__tag"},fs={class:"multiselect__tag-value"},ys=["onClick"];function ws(e,t,n,s,o,i){const l=$("svg-add-plus"),r=$("base-button"),c=$("svg-external"),a=$("svg-close"),p=$("base-select");return f(),S("div",us,[b(p,{options:o.excludeOptions,"ajax-search":i.processGetObjects,size:"medium",multiple:"",modelValue:s.getJsonValues(i.optionName),"onUpdate:modelValue":t[0]||(t[0]=u=>i.optionName=s.setJsonValues(u)),placeholder:o.strings.typeToSearch},{noOptions:w(()=>[_(g(i.noOptions),1)]),noResult:w(()=>[_(g(o.strings.noResult),1)]),caret:w(({toggle:u})=>[b(r,{class:"multiselect-toggle",style:{padding:"10px 13px",width:"40px",position:"absolute",height:"36px",right:"2px",top:"2px","text-align":"center",transition:"transform .2s ease"},type:"gray",onClick:u},{default:w(()=>[b(l,{style:{width:"14px",height:"14px",color:"black"}})]),_:2},1032,["onClick"])]),option:w(({option:u,search:h})=>[m("div",ps,[m("div",{class:"option-title",innerHTML:i.getOptionTitle(u.label,h)},null,8,ms),m("div",hs,[m("span",null,g(o.strings.id)+": #"+g(u.value),1),m("span",null,g(o.strings.type)+": "+g(u.type),1)])]),m("a",{class:"option-permalink",href:u.link,target:"_blank",onClick:Te(()=>{},["stop"])},[b(c)],8,gs)]),tag:w(({option:u,remove:h})=>[m("div",bs,[m("div",fs,g(u.label)+" - #"+g(u.value),1),m("div",{class:"multiselect__tag-remove",onClick:Te(y=>h(u),["stop"])},[b(a,{onClick:Te(y=>h(u),["stop"])},null,8,["onClick"])],8,ys)])]),_:1},8,["options","ajax-search","modelValue","placeholder"]),b(r,{type:"gray",size:"small",onClick:t[1]||(t[1]=u=>i.optionName=[])},{default:w(()=>[_(g(o.strings.clear),1)]),_:1})])}const $s=W(cs,[["render",ws]]),Pe="all-in-one-seo-pack",vs={setup(){const{getPostIconClass:e}=qo();return{getPostIconClass:e,rootStore:x()}},components:{BaseHighlightToggle:Ro,CoreTooltip:Ge,GridColumn:Uo,GridRow:zo},props:{type:{type:String,required:!0},excluded:{type:Array,default(){return[]}}},data(){return{strings:{label:d("Label:",Pe),name:d("Slug:",Pe),includeAllPostTypes:d("Include All Post Types",Pe),includeAllTaxonomies:d("Include All Taxonomies",Pe)}}},computed:{includeAllSetting(){const e=this.type+"_all";return this.$root.$data[e]},objects(){const e=this.type==="taxonomies"?"taxonomies":"postTypes";return this.rootStore.aioseo.postData[e].filter(t=>!this.excluded.includes(t.name))}},methods:{getState(){return JSON.parse(this.$root.$data[this.type])},setState(e){this.$root.$data[this.type]=JSON.stringify(e)},getValue(e){return this.getState().includes(e.name)},updateValue(e,t){const n=this.getState();if(e){n.push(t.name),this.setState(n);return}const s=n.findIndex(o=>o===t.name);s!==-1&&(n.splice(s,1),this.setState(n))},isActive(e){return this.getState().findIndex(n=>n===e.name)!==-1}}},Ss={class:"aioseo-included-objects-toggle"},_s={class:"included-objects-settings"},ks={class:"aioseo-description"},Is=m("br",null,null,-1);function Ls(e,t,n,s,o,i){const l=$("base-toggle"),r=$("core-tooltip"),c=$("base-highlight-toggle"),a=$("grid-column"),p=$("grid-row");return f(),S("div",Ss,[m("div",_s,[n.type==="post_types"?(f(),U(l,{key:0,size:"medium",modelValue:e.$root.$data.post_types_all,"onUpdate:modelValue":t[0]||(t[0]=u=>e.$root.$data.post_types_all=u)},{default:w(()=>[_(g(o.strings.includeAllPostTypes),1)]),_:1},8,["modelValue"])):k("",!0),n.type==="taxonomies"?(f(),U(l,{key:1,size:"medium",modelValue:e.$root.$data.taxonomies_all,"onUpdate:modelValue":t[1]||(t[1]=u=>e.$root.$data.taxonomies_all=u)},{default:w(()=>[_(g(o.strings.includeAllTaxonomies),1)]),_:1},8,["modelValue"])):k("",!0),0<i.objects.length&&!i.includeAllSetting?(f(),U(p,{key:2,class:"aioseo-included-list"},{default:w(()=>[(f(!0),S(Qe,null,uo(i.objects,(u,h)=>(f(),U(a,{md:"6",key:h},{default:w(()=>[b(c,{size:"medium",active:i.isActive(u),name:u.name,type:"checkbox",modelValue:i.getValue(u),"onUpdate:modelValue":y=>i.updateValue(y,u)},{default:w(()=>[b(r,null,{tooltip:w(()=>[m("div",ks,[_(g(o.strings.label)+" ",1),m("strong",null,g(u.label),1),Is,_(" "+g(o.strings.name)+" ",1),m("strong",null,g(u.name),1)])]),default:w(()=>[m("span",{class:Be(["icon dashicons",s.getPostIconClass(u.icon)])},null,2)]),_:2},1024),_(" "+g(u.label),1)]),_:2},1032,["active","name","modelValue","onUpdate:modelValue"])]),_:2},1024))),128))]),_:1})):k("",!0)])])}const Cs=W(vs,[["render",Ls]]),M="all-in-one-seo-pack",Vs={components:{HtmlSitemapExcludeObjects:$s,HtmlSitemapIncludedObjects:Cs},data(){return{sortDirections:[{label:d("Ascending",M),value:"asc"},{label:d("Descending",M),value:"desc"}],sortOrders:[{label:d("Publish Date",M),value:"publish_date"},{label:d("Last Updated",M),value:"last_updated"},{label:d("Alphabetical",M),value:"alphabetical"},{label:d("Post/Term ID",M),value:"id"}],strings:{useDefault:d("Use Default Settings",M),postTypes:d("Post Types",M),taxonomies:d("Taxonomies",M),includeAllPostTypes:d("Include All Post Types",M),includeAllTaxonomies:d("Include All Taxonomies",M),showLabel:d("Show Labels",M),publicationDate:d("Show Publication Date",M),archives:d("Compact Archives",M),sortOrder:d("Sort Order",M),sortDirection:d("Sort Direction",M),excludePostsPages:d("Exclude Posts / Pages",M),excludeTerms:d("Exclude Terms",M)}}},methods:{getSortOrder(e){return this.sortOrders.find(t=>t.value===e)},getSortDirection(e){return this.sortDirections.find(t=>t.value===e)}}},Os={class:"aioseo-sidebar-row"},Ts={class:"aioseo-sidebar-row"},Bs={key:0,class:"aioseo-sidebar-row"},Ps={key:1,class:"aioseo-sidebar-row"},Es={key:2,class:"aioseo-sidebar-row"},As={class:"aioseo-sidebar-title"},xs={key:3,class:"aioseo-sidebar-row"},Hs={class:"aioseo-sidebar-title"},Ms={key:4,class:"aioseo-sidebar-row"},Ns={class:"aioseo-sidebar-title"},Ds={class:"aioseo-sidebar-row"},qs={class:"aioseo-sidebar-title"},Rs={key:5,class:"aioseo-sidebar-row"},Us={class:"aioseo-sidebar-title"},zs={key:6,class:"aioseo-sidebar-row"},js={class:"aioseo-sidebar-title"};function Fs(e,t,n,s,o,i){const l=$("base-toggle"),r=$("html-sitemap-included-objects"),c=$("base-select"),a=$("html-sitemap-exclude-objects");return f(),S("div",null,[m("div",Os,[b(l,{modelValue:e.$root.$data.default,"onUpdate:modelValue":t[0]||(t[0]=p=>e.$root.$data.default=p)},{default:w(()=>[_(g(o.strings.useDefault),1)]),_:1},8,["modelValue"])]),e.$root.$data.default?k("",!0):(f(),S(Qe,{key:0},[m("div",Ts,[b(l,{modelValue:e.$root.$data.archives,"onUpdate:modelValue":t[1]||(t[1]=p=>e.$root.$data.archives=p)},{default:w(()=>[_(g(o.strings.archives),1)]),_:1},8,["modelValue"])]),e.$root.$data.archives?k("",!0):(f(),S("div",Bs,[b(l,{modelValue:e.$root.$data.show_label,"onUpdate:modelValue":t[2]||(t[2]=p=>e.$root.$data.show_label=p)},{default:w(()=>[_(g(o.strings.showLabel),1)]),_:1},8,["modelValue"])])),e.$root.$data.archives?k("",!0):(f(),S("div",Ps,[b(l,{modelValue:e.$root.$data.publication_date,"onUpdate:modelValue":t[3]||(t[3]=p=>e.$root.$data.publication_date=p)},{default:w(()=>[_(g(o.strings.publicationDate),1)]),_:1},8,["modelValue"])])),e.$root.$data.archives?k("",!0):(f(),S("div",Es,[m("p",As,g(o.strings.postTypes),1),b(r,{type:"post_types",excluded:["attachment"]})])),e.$root.$data.archives?k("",!0):(f(),S("div",xs,[m("p",Hs,g(o.strings.taxonomies),1),b(r,{type:"taxonomies"})])),e.$root.$data.archives?k("",!0):(f(),S("div",Ms,[m("p",Ns,g(o.strings.sortOrder),1),b(c,{size:"medium",options:o.sortOrders,modelValue:i.getSortOrder(e.$root.$data.order_by),"onUpdate:modelValue":t[4]||(t[4]=p=>e.$root.$data.order_by=p.value),"track-by":"value"},null,8,["options","modelValue"])])),m("div",Ds,[m("p",qs,g(o.strings.sortDirection),1),b(c,{size:"medium",options:o.sortDirections,modelValue:i.getSortDirection(e.$root.$data.order),"onUpdate:modelValue":t[5]||(t[5]=p=>e.$root.$data.order=p.value),"track-by":"value"},null,8,["options","modelValue"])]),e.$root.$data.archives?k("",!0):(f(),S("div",Rs,[m("p",Us,g(o.strings.excludePostsPages),1),b(a,{type:"posts"})])),e.$root.$data.archives?k("",!0):(f(),S("div",zs,[m("p",js,g(o.strings.excludeTerms),1),b(a,{type:"terms"})]))],64))])}const Zs=W(Vs,[["render",Fs]]),le=window.wp,G=le.element.createElement,Ws=le.element.Fragment,Qs=le.serverSideRender||le.components.ServerSideRender;var oo;const Js=((oo=le.blockEditor)==null?void 0:oo.InspectorControls)||le.editor.InspectorControls,Gs=le.components.PanelBody,Ks=le.components.Disabled,nt="all-in-one-seo-pack",Ys=G("svg",{width:25,height:25,viewBox:"0 0 25 25",xmlns:"http://www.w3.org/2000/svg"},G("path",{d:"M4.125 4.5H20.125V20.5H4.125V4.5ZM9.71875 6.89062H17.7188V8.49062H9.71875V6.89062ZM17.7188 10.0781H9.71875V11.6781H17.7188V10.0781ZM9.71875 13.2661H17.7188V14.8661H9.71875V13.2661ZM17.7188 16.5H9.71875V18.1H17.7188V16.5ZM6.51562 6.89062H8.11562V8.49062H6.51562V6.89062ZM8.11562 10.0781H6.51562V11.6781H8.11562V10.0781ZM6.51562 13.2661H8.11562V14.8661H6.51562V13.2661ZM8.11562 16.5H6.51562V18.1H8.11562V16.5Z",fillRule:"evenodd",clipRule:"evenodd"})),je={},at=[],yo="aioseo/html-sitemap",Xs={title:d("AIOSEO - HTML Sitemap",nt),category:"aioseo",icon:Ys,example:{},attributes:{default:{type:"boolean",default:!0},post_types:{type:"string",default:'["post", "page"]'},post_types_all:{type:"boolean",default:!0},taxonomies:{type:"string",default:'["category", "post_tag"]'},taxonomies_all:{type:"boolean",default:!0},show_label:{type:"boolean",default:!0},archives:{type:"boolean",default:!1},publication_date:{type:"boolean",default:!0},nofollow_links:{type:"boolean",default:!1},order_by:{type:"string",default:"publish_date"},order:{type:"string",default:"asc"},excluded_posts:{type:"string",default:"[]"},excluded_terms:{type:"string",default:"[]"},is_admin:{type:"boolean",default:window.location&&window.location.pathname.startsWith("/wp-admin/")}},edit:function(e){const{setAttributes:t,attributes:n,className:s,clientId:o,isSelected:i,toggleSelection:l}=e,r="aioseo-"+o,c={id:r,parent:document.querySelector(".block-editor"),subtree:!0,loop:!1,done:function(u){pe(o,at);let h=ae({name:"Blocks/HtmlSitemap",data:function(){return je[o]},watch:{$data:{handler:function(y){t(y)},deep:!0}},render:()=>ce(Zs)});h=ue(h),h=Ao(h),h=xo(h),ro(h),h.mount(u),at.push({clientId:o,app:h})}};i&&(je[o]={},Object.keys(n).forEach(u=>{je[o][u]=n[u]}),R(c)),le.data.useSelect(u=>u("core/edit-post").getActiveGeneralSidebarName())==="edit-post/block"&&(typeof l!="function"||l(!0));const p=G(Js,null,G(Gs,{title:d("Display Settings",nt),initialOpen:!0,onToggle:()=>{R(c)}},G("div",null,G("div",{id:r},null))));return G(Ws,{},p,G("div",{className:s},G(Ks,null,G(Qs,{block:yo,attributes:{...n}}))))},save:function(){return null}},en=Object.freeze(Object.defineProperty({__proto__:null,name:yo,settings:Xs},Symbol.toStringTag,{value:"Module"})),N="all-in-one-seo-pack",tn={setup(){return{postEditorStore:me(),rootStore:x()}},components:{BaseInput:Re,BaseSelect:Ue,BaseToggle:Je},data(){return{locationsList:[],strings:{selectLocation:this.rootStore.aioseo.localBusiness.postTypeSingleLabel,showLabels:d("Show labels",N),showIcons:d("Show icons",N),businessInfo:d("Business Info",N),showName:d("Name",N),address:d("Address",N),phoneNumber:d("Phone Number",N),faxNumber:d("Fax Number",N),emailAddress:d("Email Address",N),showVat:d("Show VAT ID",N),showTax:d("Show Tax ID",N),countryCode:d("Phone/Fax Country Code",N),labels:d("Labels",N),addressLabel:d("Address",N),vatIdLabel:d("Vat ID",N),taxIdLabel:d("Tax ID",N),phoneLabel:d("Phone",N),faxLabel:d("Fax",N),emailLabel:d("Email",N)}}},methods:{getLocationOptions(e){let t=this.locationsList.find(n=>n.value===e);return!t&&!this.isLocationPostType()&&(t=this.locationsList.find(n=>!!n),t&&(this.$root.$data.locationId=t.value)),t},isLocationPostType(){return this.postEditorStore.currentPost.postType===this.rootStore.aioseo.localBusiness.postTypeName}},created(){this.$root.$data.locations&&this.$root.$data.locations.forEach(e=>{this.locationsList.push({value:e.id,label:e.title.rendered})})}},on={key:0,class:"sidebar-row"},sn={class:"title"},nn={class:"sidebar-row"},an={class:"sidebar-row"},ln={class:"sidebar-row"},rn={class:"title"},dn={class:"sidebar-row"},cn={class:"sidebar-row"},un={class:"sidebar-row"},pn={class:"sidebar-row"},mn={key:1,class:"sidebar-row"},hn={class:"sidebar-row"},gn={class:"sidebar-row"},bn={class:"sidebar-row"},fn={key:2},yn={class:"sidebar-row"},wn={class:"title"},$n={class:"sidebar-row labels"},vn={key:0},Sn={key:1},_n={key:2},kn={key:3},In={key:4},Ln={key:5};function Cn(e,t,n,s,o,i){const l=$("base-select"),r=$("base-toggle"),c=$("base-input");return f(),S("div",null,[o.locationsList.length&&!i.isLocationPostType()?(f(),S("div",on,[m("p",sn,g(o.strings.selectLocation),1),b(l,{size:"medium",options:o.locationsList,modelValue:i.getLocationOptions(e.$root.$data.locationId),"onUpdate:modelValue":t[0]||(t[0]=a=>e.$root.$data.locationId=a.value),"track-by":"value"},null,8,["options","modelValue"])])):k("",!0),m("div",nn,[b(r,{modelValue:e.$root.$data.showLabels,"onUpdate:modelValue":t[1]||(t[1]=a=>e.$root.$data.showLabels=a)},{default:w(()=>[_(g(o.strings.showLabels),1)]),_:1},8,["modelValue"])]),m("div",an,[b(r,{modelValue:e.$root.$data.showIcons,"onUpdate:modelValue":t[2]||(t[2]=a=>e.$root.$data.showIcons=a)},{default:w(()=>[_(g(o.strings.showIcons),1)]),_:1},8,["modelValue"])]),m("div",ln,[m("p",rn,g(o.strings.businessInfo),1)]),m("div",dn,[b(r,{modelValue:e.$root.$data.showName,"onUpdate:modelValue":t[3]||(t[3]=a=>e.$root.$data.showName=a)},{default:w(()=>[_(g(o.strings.showName),1)]),_:1},8,["modelValue"])]),m("div",cn,[b(r,{modelValue:e.$root.$data.showAddress,"onUpdate:modelValue":t[4]||(t[4]=a=>e.$root.$data.showAddress=a)},{default:w(()=>[_(g(o.strings.address),1)]),_:1},8,["modelValue"])]),m("div",un,[b(r,{modelValue:e.$root.$data.showPhone,"onUpdate:modelValue":t[5]||(t[5]=a=>e.$root.$data.showPhone=a)},{default:w(()=>[_(g(o.strings.phoneNumber),1)]),_:1},8,["modelValue"])]),m("div",pn,[b(r,{modelValue:e.$root.$data.showFax,"onUpdate:modelValue":t[6]||(t[6]=a=>e.$root.$data.showFax=a)},{default:w(()=>[_(g(o.strings.faxNumber),1)]),_:1},8,["modelValue"])]),e.$root.$data.showPhone||e.$root.$data.showFax?(f(),S("div",mn,[b(r,{modelValue:e.$root.$data.showCountryCode,"onUpdate:modelValue":t[7]||(t[7]=a=>e.$root.$data.showCountryCode=a)},{default:w(()=>[_(g(o.strings.countryCode),1)]),_:1},8,["modelValue"])])):k("",!0),m("div",hn,[b(r,{modelValue:e.$root.$data.showEmail,"onUpdate:modelValue":t[8]||(t[8]=a=>e.$root.$data.showEmail=a)},{default:w(()=>[_(g(o.strings.emailAddress),1)]),_:1},8,["modelValue"])]),m("div",gn,[b(r,{modelValue:e.$root.$data.showVat,"onUpdate:modelValue":t[9]||(t[9]=a=>e.$root.$data.showVat=a)},{default:w(()=>[_(g(o.strings.showVat),1)]),_:1},8,["modelValue"])]),m("div",bn,[b(r,{modelValue:e.$root.$data.showTax,"onUpdate:modelValue":t[10]||(t[10]=a=>e.$root.$data.showTax=a)},{default:w(()=>[_(g(o.strings.showTax),1)]),_:1},8,["modelValue"])]),e.$root.$data.showLabels?(f(),S("div",fn,[m("div",yn,[m("p",wn,g(o.strings.labels),1)]),m("div",$n,[e.$root.$data.showAddress?(f(),S("div",vn,[m("label",null,g(o.strings.addressLabel),1),b(c,{size:"small",modelValue:e.$root.$data.addressLabel,"onUpdate:modelValue":t[11]||(t[11]=a=>e.$root.$data.addressLabel=a)},null,8,["modelValue"])])):k("",!0),e.$root.$data.showVat?(f(),S("div",Sn,[m("label",null,g(o.strings.vatIdLabel),1),b(c,{size:"small",modelValue:e.$root.$data.vatIdLabel,"onUpdate:modelValue":t[12]||(t[12]=a=>e.$root.$data.vatIdLabel=a)},null,8,["modelValue"])])):k("",!0),e.$root.$data.showTax?(f(),S("div",_n,[m("label",null,g(o.strings.taxIdLabel),1),b(c,{size:"small",modelValue:e.$root.$data.taxIdLabel,"onUpdate:modelValue":t[13]||(t[13]=a=>e.$root.$data.taxIdLabel=a)},null,8,["modelValue"])])):k("",!0),e.$root.$data.showPhone?(f(),S("div",kn,[m("label",null,g(o.strings.phoneLabel),1),b(c,{size:"small",modelValue:e.$root.$data.phoneLabel,"onUpdate:modelValue":t[14]||(t[14]=a=>e.$root.$data.phoneLabel=a)},null,8,["modelValue"])])):k("",!0),e.$root.$data.showFax?(f(),S("div",In,[m("label",null,g(o.strings.faxLabel),1),b(c,{size:"small",modelValue:e.$root.$data.faxLabel,"onUpdate:modelValue":t[15]||(t[15]=a=>e.$root.$data.faxLabel=a)},null,8,["modelValue"])])):k("",!0),e.$root.$data.showEmail?(f(),S("div",Ln,[m("label",null,g(o.strings.emailLabel),1),b(c,{size:"small",modelValue:e.$root.$data.emailLabel,"onUpdate:modelValue":t[16]||(t[16]=a=>e.$root.$data.emailLabel=a)},null,8,["modelValue"])])):k("",!0)])])):k("",!0)])}const Vn=W(tn,[["render",Cn],["__scopeId","data-v-f724afcb"]]),K=window.wp,P=K.element.createElement,Ve=K.element.Fragment;var so;const On=((so=K.blockEditor)==null?void 0:so.InspectorControls)||K.editor.InspectorControls,Tn=K.components.PanelBody,Bn=K.components.Disabled,Pn=K.serverSideRender||K.components.ServerSideRender,En=K.data.withSelect,z="all-in-one-seo-pack",An=P("svg",{width:20,height:19,viewBox:"0 0 20 19",xmlns:"http://www.w3.org/2000/svg"},P("path",{d:"M17.2001 7.2L19.0361 9.036L17.7641 10.308L10.0001 2.544L2.23611 10.308L0.964111 9.036L10.0001 0L14.8001 4.8V2.4H17.2001V7.2ZM10.0001 4.248L17.2001 11.436V18.6H2.80011V11.436L10.0001 4.248ZM12.4001 17.4V11.4H7.60011V17.4H12.4001Z"})),Ee={},it=[],lt=[],wo="aioseo/businessinfo",xn={title:d("AIOSEO Local - Business Info",z),category:"aioseo",icon:An,example:{},attributes:{locationId:{type:"number",default:null},showLabels:{type:"boolean",default:!0},addressLabel:{type:"string",default:d("Address:",z)},vatIdLabel:{type:"string",default:d("VAT ID:",z)},taxIdLabel:{type:"string",default:d("Tax ID:",z)},phoneLabel:{type:"string",default:d("Phone:",z)},faxLabel:{type:"string",default:d("Fax:",z)},emailLabel:{type:"string",default:d("Email:",z)},showIcons:{type:"boolean",default:!0},showName:{type:"boolean",default:!0},showAddress:{type:"boolean",default:!0},showPhone:{type:"boolean",default:!0},showFax:{type:"boolean",default:!0},showCountryCode:{type:"boolean",default:!0},showEmail:{type:"boolean",default:!0},showVat:{type:"boolean",default:!0},showTax:{type:"boolean",default:!0},dataObject:{type:"string",default:null},updated:{type:"string",default:Date.now()}},edit:En(function(e){const t=x();return{locations:e("core").getEntityRecords("postType",t.aioseo.localBusiness.postTypeName,{per_page:100})}})(function(e){var v;const n=(v=Le().options.localBusiness)==null?void 0:v.locations.general.multiple,{setAttributes:s,attributes:o,className:i,clientId:l,isSelected:r,toggleSelection:c}=e;let{locations:a}=e;const p="aioseo-"+l;if(n&&a===null)return P(Ve,{},P("div",{},d("Loading...",z)));if(a=a===null?[]:a,!n&&o.locationId)return P(Ve,{},P("div",{},d("Please enable multiple locations before using this block.",z)));const u=x();if(n&&a.length===0)return P(Ve,{},P("div",{},D(d("No %1$s found",z),u.aioseo.localBusiness.postTypePluralLabel)));const h=me();o.locationId=!o.locationId&&h.currentPost.postType===u.aioseo.localBusiness.postTypeName?h.currentPost.id:o.locationId;const y={id:p,parent:document.querySelector(".block-editor"),subtree:!0,loop:!1,done:function(I){pe(l,it);let L=ae({name:"Blocks/BusinessInfo",data:function(){return Ee[l]},watch:{$data:{handler:function(Q){s(Q)},deep:!0}},render:()=>ce(Vn)});L=ue(L),L.mount(I),it.push({clientId:l,app:L})}};r&&(Ee[l]={},Object.keys(o).forEach(I=>{Ee[l][I]=o[I]}),Ee[l].locations=a,R(y)),K.data.useSelect(I=>I("core/edit-post").getActiveGeneralSidebarName())==="edit-post/block"&&(typeof c!="function"||c(!0)),h.currentPost.postType===u.aioseo.localBusiness.postTypeName&&R({id:p+"-watcher",parent:document.querySelector(".block-editor"),subtree:!0,done:function(I){pe(l,lt);let L=ae({name:"Blocks/BusinessInfoWatcher",data:function(){return h.currentPost.local_seo.locations.business},watch:{$data:{handler:function(){s({updated:Date.now()})},deep:!0}},render:()=>ce("div")});L=ue(L),L.mount(I),lt.push({clientId:l,app:L})}});const H=P(On,null,P(Tn,{title:d("Display Settings",z),initialOpen:!0,onToggle:()=>{R(y)}},P("div",null,P("div",{id:p},null))));return n&&!o.locationId?P(Ve,{},H,P("div",{},D(d("Select a %1$s",z),u.aioseo.localBusiness.postTypeSingleLabel))):P(Ve,{},H,P("div",{className:i},P(Bn,null,P(Pn,{block:wo,attributes:{locationId:o.locationId?o.locationId:0,layout:o.layout,showLabels:o.showLabels,showIcons:o.showIcons,showName:o.showName,showAddress:o.showAddress,showPhone:o.showPhone,showFax:o.showFax,showCountryCode:o.showCountryCode,showEmail:o.showEmail,showVat:o.showVat,showTax:o.showTax,addressLabel:o.addressLabel,vatIdLabel:o.vatIdLabel,taxIdLabel:o.taxIdLabel,phoneLabel:o.phoneLabel,faxLabel:o.faxLabel,emailLabel:o.emailLabel,updated:o.updated,dataObject:h.currentPost.postType===u.aioseo.localBusiness.postTypeName?JSON.stringify(h.currentPost.local_seo.locations.business):null}})),P("div",{},P("div",{id:p+"-watcher"},null))))}),save:function(){return null}},Hn=Object.freeze(Object.defineProperty({__proto__:null,name:wo,settings:xn},Symbol.toStringTag,{value:"Module"})),J="all-in-one-seo-pack",Mn={setup(){return{postEditorStore:me(),rootStore:x()}},components:{BaseInput:Re,BaseSelect:Ue,BaseToggle:Je},data(){return{locationsList:[],strings:{selectLocation:this.rootStore.aioseo.localBusiness.postTypeSingleLabel,showTitle:d("Show Title",J),showIcons:d("Show Icons",J),Monday:d("Monday",J),Tuesday:d("Tuesday",J),Wednesday:d("Wednesday",J),Thursday:d("Thursday",J),Friday:d("Friday",J),Saturday:d("Saturday",J),Sunday:d("Sunday",J),label:d("Label",J)}}},methods:{getLocationOptions(e){let t=this.locationsList.find(n=>n.value===e);return!t&&!this.isLocationPostType()&&(t=this.locationsList.find(n=>!!n),t&&(this.$root.$data.locationId=t.value)),t},isLocationPostType(){return this.postEditorStore.currentPost.postType===this.rootStore.aioseo.localBusiness.postTypeName}},created(){this.$root.$data.locations&&this.$root.$data.locations.forEach(e=>{this.locationsList.push({value:e.id,label:e.title.rendered})})}},Nn={key:0,class:"sidebar-row"},Dn={class:"title"},qn={class:"sidebar-row"},Rn={class:"sidebar-row"},Un={class:"sidebar-row"},zn={class:"sidebar-row"},jn={class:"sidebar-row"},Fn={class:"sidebar-row"},Zn={class:"sidebar-row"},Wn={class:"sidebar-row"},Qn={class:"sidebar-row"},Jn={key:1,class:"sidebar-row labels"};function Gn(e,t,n,s,o,i){const l=$("base-select"),r=$("base-toggle"),c=$("base-input");return f(),S("div",null,[o.locationsList.length&&!i.isLocationPostType()?(f(),S("div",Nn,[m("p",Dn,g(o.strings.selectLocation),1),b(l,{size:"medium",options:o.locationsList,modelValue:i.getLocationOptions(e.$root.$data.locationId),"onUpdate:modelValue":t[0]||(t[0]=a=>e.$root.$data.locationId=a.value),"track-by":"value"},null,8,["options","modelValue"])])):k("",!0),m("div",qn,[b(r,{modelValue:e.$root.$data.showTitle,"onUpdate:modelValue":t[1]||(t[1]=a=>e.$root.$data.showTitle=a)},{default:w(()=>[_(g(o.strings.showTitle),1)]),_:1},8,["modelValue"])]),m("div",Rn,[b(r,{modelValue:e.$root.$data.showIcons,"onUpdate:modelValue":t[2]||(t[2]=a=>e.$root.$data.showIcons=a)},{default:w(()=>[_(g(o.strings.showIcons),1)]),_:1},8,["modelValue"])]),m("div",Un,[b(r,{modelValue:e.$root.$data.showMonday,"onUpdate:modelValue":t[3]||(t[3]=a=>e.$root.$data.showMonday=a)},{default:w(()=>[_(g(o.strings.Monday),1)]),_:1},8,["modelValue"])]),m("div",zn,[b(r,{modelValue:e.$root.$data.showTuesday,"onUpdate:modelValue":t[4]||(t[4]=a=>e.$root.$data.showTuesday=a)},{default:w(()=>[_(g(o.strings.Tuesday),1)]),_:1},8,["modelValue"])]),m("div",jn,[b(r,{modelValue:e.$root.$data.showWednesday,"onUpdate:modelValue":t[5]||(t[5]=a=>e.$root.$data.showWednesday=a)},{default:w(()=>[_(g(o.strings.Wednesday),1)]),_:1},8,["modelValue"])]),m("div",Fn,[b(r,{modelValue:e.$root.$data.showThursday,"onUpdate:modelValue":t[6]||(t[6]=a=>e.$root.$data.showThursday=a)},{default:w(()=>[_(g(o.strings.Thursday),1)]),_:1},8,["modelValue"])]),m("div",Zn,[b(r,{modelValue:e.$root.$data.showFriday,"onUpdate:modelValue":t[7]||(t[7]=a=>e.$root.$data.showFriday=a)},{default:w(()=>[_(g(o.strings.Friday),1)]),_:1},8,["modelValue"])]),m("div",Wn,[b(r,{modelValue:e.$root.$data.showSaturday,"onUpdate:modelValue":t[8]||(t[8]=a=>e.$root.$data.showSaturday=a)},{default:w(()=>[_(g(o.strings.Saturday),1)]),_:1},8,["modelValue"])]),m("div",Qn,[b(r,{modelValue:e.$root.$data.showSunday,"onUpdate:modelValue":t[9]||(t[9]=a=>e.$root.$data.showSunday=a)},{default:w(()=>[_(g(o.strings.Sunday),1)]),_:1},8,["modelValue"])]),e.$root.$data.showTitle?(f(),S("div",Jn,[m("label",null,g(o.strings.label),1),b(c,{size:"small",modelValue:e.$root.$data.label,"onUpdate:modelValue":t[10]||(t[10]=a=>e.$root.$data.label=a)},null,8,["modelValue"])])):k("",!0)])}const Kn=W(Mn,[["render",Gn],["__scopeId","data-v-7aaa8be8"]]),Y=window.wp,E=Y.element.createElement,Oe=Y.element.Fragment;var no;const Yn=((no=Y.blockEditor)==null?void 0:no.InspectorControls)||Y.editor.InspectorControls,Xn=Y.components.PanelBody,ea=Y.components.Disabled,ta=Y.serverSideRender||Y.components.ServerSideRender,oa=Y.data.withSelect,ge="all-in-one-seo-pack",sa=E("svg",{width:20,height:20,viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},E("path",{d:"M9.99999 0.400024C15.304 0.400024 19.6 4.69602 19.6 10C19.6 15.304 15.304 19.6 9.99999 19.6C4.69599 19.6 0.399994 15.304 0.399994 10C0.399994 4.69602 4.69599 0.400024 9.99999 0.400024ZM9.99999 17.2C13.972 17.2 17.2 13.972 17.2 10C17.2 6.02802 13.972 2.80002 9.99999 2.80002C6.02799 2.80002 2.79999 6.02802 2.79999 10C2.79999 13.972 6.02799 17.2 9.99999 17.2ZM9.14799 10.852C9.23199 10.912 9.31599 10.972 9.42399 11.032L9.39999 11.056L14.8 13.6L11.164 9.77202L9.99999 4.00002L8.83599 9.77202H8.84799C8.84799 9.79602 8.83599 9.83202 8.82399 9.88002C8.81199 9.92802 8.79999 9.96402 8.79999 10C8.79999 10.336 8.91999 10.624 9.14799 10.852Z"})),Ae={},rt=[],dt=[],$o="aioseo/openinghours",na={title:d("AIOSEO Local - Opening Hours",ge),category:"aioseo",icon:sa,example:{},attributes:{locationId:{type:"number",default:null},layout:{type:"string",default:"classic"},showTitle:{type:"boolean",default:!0},showIcons:{type:"boolean",default:!0},showMonday:{type:"boolean",default:!0},showTuesday:{type:"boolean",default:!0},showWednesday:{type:"boolean",default:!0},showThursday:{type:"boolean",default:!0},showFriday:{type:"boolean",default:!0},showSaturday:{type:"boolean",default:!0},showSunday:{type:"boolean",default:!0},label:{type:"string",default:d("Our Opening Hours:",ge)},dataObject:{type:"string",default:null},updated:{type:"string",default:Date.now()}},edit:oa(function(e){const t=x();return{locations:e("core").getEntityRecords("postType",t.aioseo.localBusiness.postTypeName,{per_page:100})}})(function(e){var v;const n=(v=Le().options.localBusiness)==null?void 0:v.locations.general.multiple,{setAttributes:s,attributes:o,className:i,clientId:l,isSelected:r,toggleSelection:c}=e;let{locations:a}=e;const p=`aioseo-${l}-settings`;if(n&&a===null)return E(Oe,{},E("div",{},d("Loading...",ge)));if(a=a===null?[]:a,!n&&o.locationId)return E(Oe,{},E("div",{},d("Please enable multiple locations before using this block.",ge)));const u=x();if(n&&a.length===0)return E(Oe,{},E("div",{},D(d("No %1$s found",ge),u.aioseo.localBusiness.postTypePluralLabel)));const h=me();o.locationId=!o.locationId&&h.currentPost.postType===u.aioseo.localBusiness.postTypeName?h.currentPost.id:o.locationId;const y={id:p,parent:document.querySelector(".block-editor"),subtree:!0,loop:!1,done:function(I){pe(l,rt);let L=ae({name:"Blocks/OpeningHours",data:function(){return Ae[l]},watch:{$data:{handler:function(Q){s(Q)},deep:!0}},render:()=>ce(Kn)});L=ue(L),L.mount(I),rt.push({clientId:l,app:L})}};r&&(Ae[l]={},Object.keys(o).forEach(I=>{Ae[l][I]=o[I]}),Ae[l].locations=a,R(y)),Y.data.useSelect(I=>I("core/edit-post").getActiveGeneralSidebarName())==="edit-post/block"&&(typeof c!="function"||c(!0)),h.currentPost.postType===u.aioseo.localBusiness.postTypeName&&R({id:p+"-watcher",parent:document.querySelector(".block-editor"),subtree:!0,done:function(I){pe(l,dt);let L=ae({name:"Blocks/OpeningHoursWatcher",data:function(){return h.currentPost.local_seo.openingHours},watch:{$data:{handler:function(){s({updated:Date.now()})},deep:!0}},render:()=>ce("div")});L=ue(L),L.mount(I),dt.push({clientId:l,app:L})}});const H=E(Yn,null,E(Xn,{title:d("Display Settings",ge),initialOpen:!0,onToggle:()=>{R(y)}},E("div",{},E("div",{id:p},null))));return n&&!o.locationId?E(Oe,{},H,E("div",{},D(d("Select a %1$s",ge),u.aioseo.localBusiness.postTypeSingleLabel))):E(Oe,{},H,E("div",{className:i},E(ea,null,E(ta,{block:$o,attributes:{locationId:o.locationId?o.locationId:0,layout:o.layout,showTitle:o.showTitle,showIcons:o.showIcons,showMonday:o.showMonday,showTuesday:o.showTuesday,showWednesday:o.showWednesday,showThursday:o.showThursday,showFriday:o.showFriday,showSaturday:o.showSaturday,showSunday:o.showSunday,label:o.label,updated:o.updated,dataObject:h.currentPost.postType===u.aioseo.localBusiness.postTypeName?JSON.stringify(h.currentPost.local_seo.openingHours):null}})),E("div",{},E("div",{id:p+"-watcher"},null))))}),save:function(){return null}},aa=Object.freeze(Object.defineProperty({__proto__:null,name:$o,settings:na},Symbol.toStringTag,{value:"Module"})),ia={setup(){return{postEditorStore:me(),rootStore:x()}},components:{BaseSelect:Ue},data(){return{locationCategories:[],strings:{selectLocation:this.rootStore.aioseo.localBusiness.taxonomySingleLabel}}},methods:{getCategoryOptions(e){let t=this.locationCategories.find(n=>n.value===e);return t||(t=this.locationCategories.find(n=>n.value===this.postEditorStore.currentPost.localBusinessCategory)||this.locationCategories.find(n=>!!n),t&&(this.$root.$data.categoryId=t.value)),t}},created(){this.$root.$data.categories&&this.$root.$data.categories.forEach(e=>{this.locationCategories.push({value:e.id,label:e.name})})}},la={class:"sidebar-row"},ra={class:"title"};function da(e,t,n,s,o,i){const l=$("base-select");return f(),S("div",null,[m("div",la,[m("p",ra,g(o.strings.selectLocation),1),b(l,{size:"medium",options:o.locationCategories,modelValue:i.getCategoryOptions(e.$root.$data.categoryId),"onUpdate:modelValue":t[0]||(t[0]=r=>e.$root.$data.categoryId=r.value),"track-by":"value"},null,8,["options","modelValue"])])])}const ca=W(ia,[["render",da]]),X=window.wp,A=X.element.createElement,we=X.element.Fragment;var ao;const ua=((ao=X.blockEditor)==null?void 0:ao.InspectorControls)||X.editor.InspectorControls,pa=X.components.PanelBody,ma=X.components.Disabled,ha=X.serverSideRender||X.components.ServerSideRender,ga=X.data.withSelect,$e="all-in-one-seo-pack",ba=A("svg",{width:20,height:19,viewBox:"0 0 20 19",xmlns:"http://www.w3.org/2000/svg"},A("path",{d:"M17.2001 7.2L19.0361 9.036L17.7641 10.308L10.0001 2.544L2.23611 10.308L0.964111 9.036L10.0001 0L14.8001 4.8V2.4H17.2001V7.2ZM10.0001 4.248L17.2001 11.436V18.6H2.80011V11.436L10.0001 4.248ZM12.4001 17.4V11.4H7.60011V17.4H12.4001Z"})),xe={},ct=[],vo="aioseo/locations",fa={title:d("AIOSEO Local - Locations",$e),category:"aioseo",icon:ba,example:{},attributes:{categoryId:{type:"number",default:null}},edit:ga(function(e){const t=x();return{categories:e("core").getEntityRecords("taxonomy",t.aioseo.localBusiness.taxonomyName)}})(function(e){var H;const n=(H=Le().options.localBusiness)==null?void 0:H.locations.general.multiple,{setAttributes:s,attributes:o,className:i,clientId:l,isSelected:r,toggleSelection:c}=e;let{categories:a}=e;const p="aioseo-"+l;if(n&&a===null)return A(we,{},A("div",{},d("Loading...",$e)));if(a=a===null?[]:a,!n)return A(we,{},A("div",{},d("Please enable multiple locations before using this block.",$e)));const u=x();if(a.length===0)return A(we,{},A("div",{},D(d("No %1$s found",$e),u.aioseo.localBusiness.taxonomyPluralLabel)));const h={id:p,parent:document.querySelector(".block-editor"),subtree:!0,loop:!1,done:function(v){pe(l,ct);let I=ae({name:"Blocks/Locations",data:function(){return xe[l]},watch:{$data:{handler:function(L){s(L)},deep:!0}},render:()=>ce(ca)});I=ue(I),I.mount(v),ct.push({clientId:l,app:I})}};r&&(xe[l]={},Object.keys(o).forEach(v=>{xe[l][v]=o[v]}),xe[l].categories=a,R(h)),X.data.useSelect(v=>v("core/edit-post").getActiveGeneralSidebarName())==="edit-post/block"&&(typeof c!="function"||c(!0));const C=A(ua,null,A(pa,{title:u.aioseo.localBusiness.postTypePluralLabel,initialOpen:!0,onToggle:()=>{R(h)}},A("div",{},A("div",{id:p},null))));return a!==null&&a.length===0?A(we,{},A("div",{},D(d("No %1$s found",$e),u.aioseo.localBusiness.taxonomyPluralLabel))):o.categoryId?A(we,{},C,A("div",{className:i},A(ma,null,A(ha,{block:vo,attributes:{categoryId:o.categoryId}})))):A(we,{},C,A("div",{},D(d("Select a %1$s",$e),u.aioseo.localBusiness.taxonomySingleLabel)))}),save:function(){return null}},ya=Object.freeze(Object.defineProperty({__proto__:null,name:vo,settings:fa},Symbol.toStringTag,{value:"Module"})),Ie=window.wp,Z=Ie.element.createElement,He=Ie.element.Fragment,wa=Ie.serverSideRender||Ie.components.ServerSideRender,$a=Ie.components.Disabled,va=Ie.data.withSelect,Me="all-in-one-seo-pack",Sa=Z("svg",{width:20,height:19,viewBox:"0 0 20 19",xmlns:"http://www.w3.org/2000/svg"},Z("path",{d:"M17.2001 7.2L19.0361 9.036L17.7641 10.308L10.0001 2.544L2.23611 10.308L0.964111 9.036L10.0001 0L14.8001 4.8V2.4H17.2001V7.2ZM10.0001 4.248L17.2001 11.436V18.6H2.80011V11.436L10.0001 4.248ZM12.4001 17.4V11.4H7.60011V17.4H12.4001Z"})),So="aioseo/locationcategories",_a={title:d("AIOSEO Local - Location Categories",Me),category:"aioseo",icon:Sa,example:{},edit:va(function(e){const t=x();return{categories:e("core").getEntityRecords("taxonomy",t.aioseo.localBusiness.taxonomyName)}})(function(e){var i;const n=(i=Le().options.localBusiness)==null?void 0:i.locations.general.multiple,{className:s}=e;let{categories:o}=e;if(n&&o===null)return Z(He,{},Z("div",{},d("Loading...",Me)));if(o=o===null?[]:o,!n)return Z(He,{},Z("div",{},d("Please enable multiple locations before using this block.",Me)));if(o.length===0){const l=x();return Z(He,{},Z("div",{},D(d("No %1$s found",Me),l.aioseo.localBusiness.taxonomyPluralLabel)))}return Z(He,{},Z("div",{className:s},Z($a,null,Z(wa,{block:So}))))}),save:function(){return null}},ka=Object.freeze(Object.defineProperty({__proto__:null,name:So,settings:_a},Symbol.toStringTag,{value:"Module"})),se="all-in-one-seo-pack",Ia={setup(){return{postEditorStore:me(),rootStore:x()}},components:{BaseInput:Re,BaseSelect:Ue,BaseToggle:Je,CoreImageUploader:jo},data(){return{locationsList:[],strings:{selectLocation:this.rootStore.aioseo.localBusiness.postTypeSingleLabel,showLabel:d("Show label",se),showIcon:d("Show icon",se),businessInfo:d("Business Info",se),mapDisplay:d("Map Display",se),width:d("Width",se),height:d("Height",se),customMarker:d("Custom Marker",se),minimumSize:D(d("%1$sThe custom marker should be: 100x100 px.%2$s If the image exceeds those dimensions it could (partially) cover the info popup.",se),"<strong>","</strong>"),label:d("Label",se)}}},methods:{getLocationOptions(e){let t=this.locationsList.find(n=>n.value===e);return!t&&!this.isLocationPostType()&&(t=this.locationsList.find(n=>!!n),t&&(this.$root.$data.locationId=t.value)),t},isLocationPostType(){return this.postEditorStore.currentPost.postType===this.rootStore.aioseo.localBusiness.postTypeName}},created(){this.$root.$data.locations&&this.$root.$data.locations.forEach(e=>{this.locationsList.push({value:e.id,label:e.title.rendered})})}},La={key:0,class:"sidebar-row"},Ca={class:"title"},Va={class:"sidebar-row"},Oa={class:"sidebar-row"},Ta={class:"sidebar-row"},Ba={class:"title"},Pa={class:"sidebar-row"},Ea={class:"title"},Aa={class:"sidebar-row dimensions"},xa={key:1,class:"sidebar-row labels"},Ha={class:"title"};function Ma(e,t,n,s,o,i){const l=$("base-select"),r=$("base-toggle"),c=$("core-image-uploader"),a=$("base-input");return f(),S("div",null,[o.locationsList.length&&!i.isLocationPostType()?(f(),S("div",La,[m("p",Ca,g(o.strings.selectLocation),1),b(l,{size:"medium",options:o.locationsList,modelValue:i.getLocationOptions(e.$root.$data.locationId),"onUpdate:modelValue":t[0]||(t[0]=p=>e.$root.$data.locationId=p.value),"track-by":"value"},null,8,["options","modelValue"])])):k("",!0),m("div",Va,[b(r,{modelValue:e.$root.$data.showLabel,"onUpdate:modelValue":t[1]||(t[1]=p=>e.$root.$data.showLabel=p)},{default:w(()=>[_(g(o.strings.showLabel),1)]),_:1},8,["modelValue"])]),m("div",Oa,[b(r,{modelValue:e.$root.$data.showIcon,"onUpdate:modelValue":t[2]||(t[2]=p=>e.$root.$data.showIcon=p)},{default:w(()=>[_(g(o.strings.showIcon),1)]),_:1},8,["modelValue"])]),m("div",Ta,[m("p",Ba,g(o.strings.customMarker),1),b(c,{class:"aioseo-image-uploader--no-icon","img-preview-max-width":"100px","img-preview-max-height":"100px","base-size":"small",description:o.strings.minimumSize,modelValue:e.$root.$data.customMarker,"onUpdate:modelValue":t[3]||(t[3]=p=>e.$root.$data.customMarker=p)},null,8,["description","modelValue"])]),m("div",Pa,[m("p",Ea,g(o.strings.mapDisplay),1)]),m("div",Aa,[m("div",null,[m("label",null,g(o.strings.width)+":",1),b(a,{size:"small",modelValue:e.$root.$data.width,"onUpdate:modelValue":t[4]||(t[4]=p=>e.$root.$data.width=p)},null,8,["modelValue"])]),m("div",null,[m("div",null,[m("label",null,g(o.strings.height)+":",1),b(a,{size:"small",modelValue:e.$root.$data.height,"onUpdate:modelValue":t[5]||(t[5]=p=>e.$root.$data.height=p)},null,8,["modelValue"])])])]),e.$root.$data.showLabel?(f(),S("div",xa,[m("p",Ha,g(o.strings.label),1),b(a,{size:"small",modelValue:e.$root.$data.label,"onUpdate:modelValue":t[6]||(t[6]=p=>e.$root.$data.label=p)},null,8,["modelValue"])])):k("",!0)])}const Na=W(Ia,[["render",Ma],["__scopeId","data-v-b101bbec"]]);function Da(e){let t;try{t=new URL(e,"http://example.com").search.substring(1)}catch{}if(t)return t}function qa(e){let t="";const n=Object.entries(e);let s;for(;s=n.shift();){let[o,i]=s;if(Array.isArray(i)||i&&i.constructor===Object){const r=Object.entries(i).reverse();for(const[c,a]of r)n.unshift([`${o}[${c}]`,a])}else i!==void 0&&(i===null&&(i=""),t+="&"+[o,i].map(encodeURIComponent).join("="))}return t.substr(1)}function Ra(e){try{return decodeURIComponent(e)}catch{return e}}function Ua(e,t,n){const s=t.length,o=s-1;for(let i=0;i<s;i++){let l=t[i];!l&&Array.isArray(e)&&(l=e.length.toString()),l=["__proto__","constructor","prototype"].includes(l)?l.toUpperCase():l;const r=!isNaN(Number(t[i+1]));e[l]=i===o?n:e[l]||(r?[]:{}),Array.isArray(e[l])&&!r&&(e[l]={...e[l]}),e=e[l]}}function za(e){return(Da(e)||"").replace(/\+/g,"%20").split("&").reduce((t,n)=>{const[s,o=""]=n.split("=").filter(Boolean).map(Ra);if(s){const i=s.replace(/\]/g,"").split("[");Ua(t,i,o)}return t},Object.create(null))}function ja(e="",t){if(!t||!Object.keys(t).length)return e;let n=e;const s=e.indexOf("?");return s!==-1&&(t=Object.assign(za(e),t),n=n.substr(0,s)),n+"?"+qa(t)}const ee=window.wp,B=ee.element.createElement,ve=ee.element.Fragment;var io;const Fa=((io=ee.blockEditor)==null?void 0:io.InspectorControls)||ee.editor.InspectorControls,Za=ee.components.PanelBody,Wa=ee.components.Disabled,Qa=ee.serverSideRender||ee.components.ServerSideRender,Ja=ee.data.withSelect,re="all-in-one-seo-pack",Ga=B("svg",{width:20,height:20,viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},B("path",{d:"M11 11.14L9.83 5.2C9.04 4.77 8.5 3.95 8.5 3C8.5 1.62 9.62 0.499999 11 0.499999C12.38 0.499999 13.5 1.62 13.5 3C13.5 3.95 12.96 4.77 12.17 5.2L11 11.14ZM11 1.5C10.17 1.5 9.5 2.17 9.5 3C9.5 3.83 10.17 4.5 11 4.5C11.83 4.5 12.5 3.83 12.5 3C12.5 2.17 11.83 1.5 11 1.5ZM12.72 6.3L11 13.68L9.27 6.38L5 4.97L2.98023e-08 6.97V15.97L5 13.97L11.12 16L16 13.97V4.97L12.72 6.3Z"})),Ne={},ut=[],pt=[],Ka="aioseo/locationmap",Ya={title:d("AIOSEO Local - Map",re),category:"aioseo",icon:Ga,example:{},attributes:{locationId:{type:"number",default:null},showLabel:{type:"boolean",default:!0},showIcon:{type:"boolean",default:!0},customMarker:{type:"string",default:null},width:{type:"string",default:"100%"},height:{type:"string",default:"450px"},label:{type:"string",default:d("Our location:",re)},dataObject:{type:"string",default:null},updated:{type:"string",default:Date.now()}},edit:Ja(function(e){const t=x();return{locations:e("core").getEntityRecords("postType",t.aioseo.localBusiness.postTypeName,{per_page:100})}})(function(e){var oe;const t=Le(),n=(oe=t.options.localBusiness)==null?void 0:oe.locations.general.multiple,{setAttributes:s,attributes:o,className:i,clientId:l,isSelected:r,toggleSelection:c}=e;let{locations:a}=e;const p="aioseo-location-map-"+l;if(n&&a===null)return B(ve,{},B("div",{},d("Loading...",re)));if(a=a===null?[]:a,!n&&o.locationId)return B(ve,{},B("div",{},d("Please enable multiple locations before using this block.",re)));const u=x();if(n&&a.length===0)return B(ve,{},B("div",{},D(d("No %1$s found",re),u.aioseo.localBusiness.postTypePluralLabel)));const h=me(),y=h.currentPost.postType===u.aioseo.localBusiness.postTypeName;o.locationId=!o.locationId&&y?h.currentPost.id:o.locationId;const C=a.find(V=>V.id===o.locationId),H=y?h.currentPost.local_seo.maps:C?C.maps:null,v={id:p,parent:document.querySelector(".block-editor"),subtree:!0,loop:!1,done:function(V){pe(l,ut);let q=ae({name:"Blocks/LocationMap",data:function(){return Ne[l]},watch:{$data:{handler:function(T){s(T)},deep:!0}},render:()=>ce(Na)});q=ue(q),q.mount(V),ut.push({clientId:l,app:q})}};r&&(Ne[l]={},Object.keys(o).forEach(function(V){Ne[l][V]=o[V]}),Ne[l].locations=a,R(v)),ee.data.useSelect(V=>V("core/edit-post").getActiveGeneralSidebarName())==="edit-post/block"&&(typeof c!="function"||c(!0)),y&&R({id:p+"-watcher",parent:document.querySelector(".block-editor"),subtree:!0,done:function(V){pe(l,pt);let q=ae({name:"Blocks/LocationMapWatcher",data:function(){return h.currentPost.local_seo.maps},watch:{$data:{handler:function(){s({updated:Date.now()})},deep:!0}},render:()=>ce("div")});q=ue(q),q.mount(V),pt.push({clientId:l,app:q})}});const L=B(Fa,null,B(Za,{title:d("Settings",re),initialOpen:!0,onToggle:()=>{R(v)}},B("div",null,B("div",{id:p},null))));if(n){if(!o.locationId)return B(ve,{},L,B("div",{},D(d("Select a %1$s",re),u.aioseo.localBusiness.postTypeSingleLabel)));if(!H){const V=ja("post.php",{post:o.locationId,action:"edit"});return B(ve,{},L,B("div",{dangerouslySetInnerHTML:{__html:D(d("Please configure the map for this location: %1$s",re),'<a href="'+V+'" target="_blank">'+C.title.rendered+"</a>")}}))}}const Q="#"+p+"-preview .aioseo-local-map";return R({selector:Q,parent:document.querySelector(".block-editor"),subtree:!0,done:function(){const V=H||t.options.localBusiness.maps;setTimeout(function(){document.dispatchEvent(new CustomEvent(u.aioseo.localBusiness.mapLoadEvent,{detail:{element:Q,mapOptions:V.mapOptions,customMarker:o.customMarker||V.customMarker||t.options.localBusiness.maps.customMarker,instance:o,placeId:t.options.localBusiness.maps.mapsEmbedApiEnabled?V.placeId:null,infoWindowContent:V.infoWindowContent?V.infoWindowContent:null}}))},2e3)}}),B(ve,{},L,B("div",{className:i,id:p+"-preview"},B(Wa,null,B(Qa,{block:"aioseo/locationmap",attributes:{locationId:o.locationId?o.locationId:0,showLabel:o.showLabel,showIcon:o.showIcon,customMarker:o.customMarker,width:o.width,height:o.height,label:o.label,updated:o.updated,dataObject:y?JSON.stringify(h.currentPost.local_seo.maps):null}})),B("div",{},B("div",{id:p+"-watcher"},null))))}),save:function(){return null}},Xa=Object.freeze(Object.defineProperty({__proto__:null,name:Ka,settings:Ya},Symbol.toStringTag,{value:"Module"})),ei="https://schemas.wp.org/wp/5.8/block.json",ti="aioseo/table-of-contents",oi="1.0.0",si="AIOSEO - Table of Contents",ni="Automatically output a table of contents (TOC) for long posts or pages (and custom post types).",ai="layout",ii=["toc","table of contents","document outline","summary","index"],li="all-in-one-seo-pack",ri={listStyle:{type:"string",default:"ul"},headings:{type:"array",items:{type:"object"},default:[]},reOrdered:{type:"boolean",default:!1}},di={multiple:!1,html:!1,align:["wide","full"]},_o={$schema:ei,name:ti,version:oi,title:si,description:ni,category:ai,keywords:ii,textdomain:li,attributes:ri,supports:di},ci={},ui={viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",class:"aioseo-eye-off"},pi=m("path",{d:"M0 0h24v24H0V0zm0 0h24v24H0V0zm0 0h24v24H0V0zm0 0h24v24H0V0z",fill:"none"},null,-1),mi=m("path",{d:"M12 6a9.77 9.77 0 0 1 8.82 5.5 9.647 9.647 0 0 1-2.41 3.12l1.41 1.41c1.39-1.23 2.49-2.77 3.18-4.53C21.27 7.11 17 4 12 4c-1.27 0-2.49.2-3.64.57l1.65 1.65C10.66 6.09 11.32 6 12 6zm-1.07 1.14L13 9.21c.57.25 1.03.71 1.28 1.28l2.07 2.07c.08-.34.14-.7.14-1.07C16.5 9.01 14.48 7 12 7c-.37 0-.72.05-1.07.14zM2.01 3.87l2.68 2.68A11.738 11.738 0 0 0 1 11.5C2.73 15.89 7 19 12 19c1.52 0 2.98-.29 4.32-.82l3.42 3.42 1.41-1.41L3.42 2.45 2.01 3.87zm7.5 7.5 2.61 2.61c-.04.01-.08.02-.12.02a2.5 2.5 0 0 1-2.5-2.5c0-.05.01-.08.01-.13zm-3.4-3.4 1.75 1.75a4.6 4.6 0 0 0-.36 1.78 4.507 4.507 0 0 0 6.27 4.14l.98.98c-.88.24-1.8.38-2.75.38a9.77 9.77 0 0 1-8.82-5.5c.7-1.43 1.72-2.61 2.93-3.53z",fill:"currentColor"},null,-1),hi=[pi,mi];function gi(e,t){return f(),S("svg",ui,hi)}const bi=W(ci,[["render",gi]]),mt="all-in-one-seo-pack",fi={name:"List",setup(){return{tableOfContentsStore:fe()}},components:{BaseInput:Re,CoreTooltip:Ge,Draggable:Zo,SvgClose:po,SvgDrag:Wo,SvgEye:Qo,SvgEyeOff:bi,SvgInfo:mo,SvgTocLink:Jo},props:{headings:{required:!0,type:Array},allowReorder:{required:!1,type:Boolean,default(){return!1}}},data(){return{showAnchorField:-1,strings:{tooltipHeader:d("Edit HTML Anchor:",mt),tooltipDescription:D(d("The HTML anchor allows %1$s to link directly to your header from this table of contents block. Feel free to edit if you want, but an anchor is required. For headings without an anchor, %1$s will automatically generate them.",mt),"AIOSEO")}}},computed:{dragOptions(){return{tag:this.tableOfContentsStore.listStyle,animation:300,group:"description",disabled:!this.allowReorder,ghostClass:"aioseo-drag-ghost",dragClass:"aioseo-dragging"}}},methods:{setEditedContent:function(e,t){if(e===t.content){t.editedContent="";return}t.editedContent=fo(e,!0)},setReorder(){this.tableOfContentsStore.reOrdered=!0;const e=Fo(ne(this.tableOfContentsStore.headings));this.tableOfContentsStore.setHeadings(e),window.aioseoBus.$emit("updateHeadings"+this.tableOfContentsStore.blockClientId,e)},setAnchor:function(e,t){if(t.anchor=Fe(e),!e){const o=x();t.anchor=o.aioseo.data.blocks.toc.hashPrefix+Fe(t.content)}const n=t.blockClientId;window.wp.data.select("core/block-editor").getBlock(n)&&window.wp.data.dispatch("core/block-editor").updateBlockAttributes(n,{anchor:t.anchor})},setHiddenStatus(e){e.hidden=!e.hidden,e.editedLevel===9?e.editedLevel=0:e.editedLevel=9,this.tableOfContentsStore.setHeadings(Ke([...this.tableOfContentsStore.headings]))},handleAnchorInput(e){const t=e.target.closest(".aioseo-toc-list-item"),n=t==null?void 0:t.querySelector(".row-input--anchor input");n&&(t.classList.contains("anchor-edit")?(t.classList.add("anchor-is-animating"),t.classList.remove("anchor-edit"),t.classList.remove("done"),n.addEventListener("animationend",function s(){t.classList.remove("anchor-is-animating"),t.removeEventListener("animationend",s,!1)})):(n.focus({preventScroll:!0}),t.classList.add("anchor-edit","anchor-is-animating"),n.addEventListener("animationend",function s(){t.classList.remove("anchor-is-animating"),t.classList.add("done"),n.removeEventListener("animationend",s,!1)})))}}},yi={class:"aioseo-toc-list-item__inner"},wi={key:0,class:"aioseo-drag-handle has-icon"},$i={key:0,class:"append-icon"},vi={class:"append-icon"},Si={class:"aioseo-tooltip__header"},_i=["onClick"];function ki(e,t,n,s,o,i){const l=$("svg-drag"),r=$("svg-toc-link"),c=$("base-input"),a=$("svg-info"),p=$("core-tooltip"),u=$("svg-close"),h=$("svg-eye-off"),y=$("svg-eye"),C=$("List",!0),H=$("draggable");return f(),U(H,Po({class:["aioseo-toc-list",[{orderable:n.allowReorder}]]},i.dragOptions,{handle:".aioseo-drag-handle",list:n.headings,"onUpdate:modelValue":t[0]||(t[0]=v=>e.$emit("update:modelValue",v)),onChange:t[1]||(t[1]=v=>i.setReorder(v)),"item-key":e.$.uid.toString()}),{item:w(({element:v})=>[m("li",{class:Be(["aioseo-toc-list-item",{"heading-hidden":v.hidden}])},[m("div",yi,[n.allowReorder?(f(),S("button",wi,[b(l)])):k("",!0),b(c,{class:"row-input row-input--content",modelValue:v.editedContent||v.content,"onUpdate:modelValue":I=>i.setEditedContent(I,v),placeholder:v.content},{"append-icon":w(()=>[n.allowReorder?k("",!0):(f(),S("div",$i,[b(r,{onClick:i.handleAnchorInput},null,8,["onClick"])]))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"]),n.allowReorder?k("",!0):(f(),U(c,{key:1,class:"row-input row-input--anchor",spellcheck:!1,modelValue:v.anchor,"onUpdate:modelValue":I=>i.setAnchor(I,v)},{"append-icon":w(()=>[m("div",vi,[b(p,null,{tooltip:w(()=>[m("p",Si,g(o.strings.tooltipHeader),1),m("p",null,g(o.strings.tooltipDescription),1)]),default:w(()=>[b(a)]),_:1}),b(u,{onClick:i.handleAnchorInput},null,8,["onClick"])])]),_:2},1032,["modelValue","onUpdate:modelValue"])),n.allowReorder?k("",!0):(f(),S("button",{key:2,class:Be([{active:v.hidden},"aioseo-hide-heading-toggle","has-icon"]),onClick:I=>i.setHiddenStatus(v)},[v.hidden?(f(),U(h,{key:0})):(f(),U(y,{key:1}))],10,_i)),v.headings?(f(),U(C,{key:3,class:"aioseo-toc-list-nested",headings:v.headings,allowReorder:n.allowReorder},null,8,["headings","allowReorder"])):k("",!0)])],2)]),_:1},16,["class","list","item-key"])}const Ii=W(fi,[["render",ki]]),Li={name:"ListRendered",setup(){return{tableOfContentsStore:fe()}},props:{headings:{required:!0,type:Array}}},Ci=["href"];function Vi(e,t,n,s,o,i){const l=$("ListRendered",!0);return f(),U(Eo(s.tableOfContentsStore.listStyle),{class:"aioseo-toc-list--rendered"},{default:w(()=>[(f(!0),S(Qe,null,uo(n.headings,(r,c)=>(f(),S("li",{class:Be(["aioseo-toc-list-item--rendered",[{hidden:r.hidden}]]),key:c},[m("a",{href:`#${r.anchor}`},g(r.editedContent||r.content),9,Ci),r.headings?(f(),U(l,{key:0,class:"aioseo-toc-list-nested--rendered",headings:r.headings},null,8,["headings"])):k("",!0)],2))),128))]),_:1})}const Oi=W(Li,[["render",Vi]]),ht="all-in-one-seo-pack",Ti={emits:["closeModal"],setup(){return{rootStore:x(),links:co}},components:{CoreModal:Go,Cta:Ko},props:{show:Boolean},data(){return{strings:{header:D(d("Reordering Headings is a %1$s Feature",ht),"PRO"),description:d("Reordering the headings in the Table of Contents block is a feature that can only be used by Pro users. Upgrade to Pro to unlock this advanced functionality.",ht)}}}};function Bi(e,t,n,s,o,i){const l=$("cta"),r=$("core-modal");return f(),U(r,{show:n.show,classes:["aioseo-toc-modal-lite"],onClose:t[0]||(t[0]=c=>e.$emit("closeModal"))},{body:w(()=>[b(l,{type:1,floating:!1,"cta-link":s.links.utmUrl("toc-block"),"button-text":"Unlock Reordering","learn-more-link":s.links.getUpsellUrl("toc-block",null,s.rootStore.isPro?"pricing":"liteUpgrade")},{"header-text":w(()=>[_(g(o.strings.header),1)]),description:w(()=>[_(g(o.strings.description),1)]),_:1},8,["cta-link","learn-more-link"])]),_:1},8,["show"])}const Pi=W(Ti,[["render",Bi]]),De={editedContent:"",editedLevel:0,editedOrder:0,hidden:!1,headings:[]},Se="all-in-one-seo-pack",Ei={setup(){return{tableOfContentsStore:fe()}},components:{CoreTooltip:Ge,Info:mo,List:Ii,ListRendered:Oi,Reorder:Pi},data(){return{showModal:!1,strings:{header:D(d("%1$s Table of Contents",Se),"AIOSEO"),instructions:d("Add a heading block below to begin generating the Table of Contents.",Se),tooltipMainDescription:D(d("%1$s can automatically output a table of contents based on your heading tags below. Search engines sometimes use table of contents in search results or rich snippets which can help you increase your rankings.",Se),"AIOSEO"),reorder:d("Reorder",Se),save:d("Save",Se),done:d("Done",Se)}}},watch:{"tableOfContentsStore.headings":{handler(e,t){ke(e,t)||window.aioseoBus.$emit("setAttributes"+this.tableOfContentsStore.blockClientId,e)},deep:!0},"tableOfContentsStore.reOrdered":{handler(e,t){e!==t&&window.aioseoBus.$emit("setAttributes"+this.tableOfContentsStore.blockClientId,e)}}},methods:{save(e){const t=e.target.closest(".wp-block");t==null||t.classList.remove("is-selected")},setDefaultHeadingProps(e){return Object.keys(De).forEach(t=>{e[t]=De[t]}),e},updateHeadings(e){const t=be(ne(this.tableOfContentsStore.headings));let n=e.map(s=>this.setDefaultHeadingProps(s));t.forEach(s=>{const o=n.findIndex(i=>i.blockClientId===s.blockClientId);o!==-1&&(Object.keys(De).forEach(i=>{n[o][i]=s[i]}),n[o].editedContent===n[o].content&&(n[o].editedContent=""),n[o].hidden?n[o].editedLevel=9:n[o].editedLevel===9&&(n[o].editedLevel=0))}),this.tableOfContentsStore.reOrdered?0<n.length-t.length&&(n.sort((s,o)=>s.id-o.id),n.forEach((s,o)=>{s.editedOrder||(s.editedOrder=o+1)}),n.sort((s,o)=>s.editedOrder-o.editedOrder)):n.sort((s,o)=>s.id-o.id),n=Ke(n),this.tableOfContentsStore.setHeadings(n)}},mounted(){this.$nextTick(()=>{window.aioseoBus.$on("updateHeadings"+this.tableOfContentsStore.blockClientId,e=>{this.updateHeadings(e)})})}},Ai={class:"aioseo-toc-container"},xi={class:"aioseo-toc-header"},Hi={class:"aioseo-toc-header-title"},Mi={key:0,class:"aioseo-toc-header-instructions"},Ni={key:1,class:"aioseo-toc-header-buttons"},Di={key:0,class:"aioseo-toc-content"};function qi(e,t,n,s,o,i){const l=$("Info"),r=$("core-tooltip"),c=$("List"),a=$("reorder"),p=$("ListRendered");return f(),S("div",Ai,[m("div",{class:Be(["aioseo-toc-menu",{"aioseo-toc-placeholder":s.tableOfContentsStore.headings.length===0}])},[m("header",xi,[m("div",Hi,[_(g(o.strings.header)+" ",1),b(r,{placement:"bottom"},{tooltip:w(()=>[m("p",null,g(o.strings.tooltipMainDescription),1)]),default:w(()=>[b(l)]),_:1})]),s.tableOfContentsStore.headings.length===0?(f(),S("div",Mi,g(o.strings.instructions),1)):k("",!0),s.tableOfContentsStore.headings.length!==0?(f(),S("div",Ni,[m("a",{class:"aioseo-button-link",href:"#",onClick:t[0]||(t[0]=Te(u=>o.showModal=!0,["prevent"]))},g(o.strings.reorder),1),m("a",{class:"aioseo-button-link",href:"#",onClick:t[1]||(t[1]=Te((...u)=>i.save&&i.save(...u),["prevent"]))},g(o.strings.done),1)])):k("",!0)]),s.tableOfContentsStore.headings.length!==0?(f(),S("div",Di,[b(c,{headings:s.tableOfContentsStore.headings},null,8,["headings"])])):k("",!0),b(a,{show:o.showModal,headings:s.tableOfContentsStore.headings,onCloseModal:t[2]||(t[2]=u=>o.showModal=!1)},null,8,["show","headings"])],2),s.tableOfContentsStore.headings.length!==0?(f(),U(p,{key:0,headings:s.tableOfContentsStore.headings},null,8,["headings"])):k("",!0)])}const Ri=W(Ei,[["render",qi]]),{InspectorControls:gt}=window.wp.blockEditor,{PanelBody:bt,SelectControl:Ui}=window.wp.components,ft="all-in-one-seo-pack",zi=e=>{const{setAttributes:t,attributes:{listStyle:n}}=e,s=fe();return te`
	<${gt}>
		<${bt} title=${d("Table of Contents Settings",ft)}>
			<${Ui}
				label=${d("List Style",ft)}
				options=${[{label:"Bullets",value:"ul"},{label:"Numbers",value:"ol"}]}
				value=${n}
				onChange=${o=>{s.listStyle=o,t({listStyle:o})}}
			/>
		</${bt}>
	</${gt}>
`},{select:ko,subscribe:ji,useSelect:Fi}=window.wp.data,{isTyping:Zi}=window.wp.data.select("core/block-editor")||{isTyping:()=>null},qe=[];let j=[],We=!1;const Wi=ko("core/edit-post").getEditorMode();ji(()=>{if(ko("core/edit-post").getEditorMode()!==Wi){const e=Object.keys(qe)[0];if(We=!0,e){const t=document.querySelector(`[data-block="${e}"]`);t&&t.remove()}}});function Qi(e){const{setAttributes:t,attributes:n,clientId:s,className:o,isSelected:i}=e,l=`aioseo-${s}`;if((We||!qe.includes(s)&&(i||document.querySelector(`[data-block="${s}"]`)))&&(We=!1,qe.includes(s)||qe.push(s),R({id:l,parent:document.querySelector(".block-editor"),subtree:!0,done:function(r){var p;const c=ae({...Ri,name:"Blocks/TableOfContents"});ro(c),c.mount(r);const a=fe();if(a.blockClientId=s,a.headings=n.headings,a.listStyle=n.listStyle,a.reOrdered=n.reOrdered,(p=a.headings)!=null&&p.length&&0<(j==null?void 0:j.length)){const u=be(ne(a.headings));u.forEach(h=>{const y=j==null?void 0:j.find(C=>C.content===h.content&&C.anchor===h.anchor&&C.id===h.id&&C.level===h.level);y&&(h.blockClientId=y.blockClientId)}),a.headings=Ke(u),t(a)}window.aioseoBus.$on("setAttributes"+s,()=>{t(a.$state)})}})),j=Fi(r=>{var L,Q,oe,V,q;const c=r("core/block-editor");if(!c)return null;const{getBlockAttributes:a,getBlockIndex:p,getBlockName:u,getClientIdsWithDescendants:h}=c,y=p(s),C=[];h().forEach(T=>{var tt;if(u(T)!=="core/heading"&&u(T)!=="aioseo/faq")return;const O=a(T),Ye=p(T),Xe=typeof(O==null?void 0:O.anchor)=="string"&&O.anchor!=="",et=O.level||O.tagName.replace("h","");if(et==="div")return;let ye=O.question||((tt=O.content)==null?void 0:tt.text)||O.content||"";if(!(typeof ye!="string"||ye===""||y>Ye)){if(ye=fo(ye.replace(/(<br *\/?>)+/g," "),!0),!Xe&&!Zi()){const Vo=x();O.anchor=Vo.aioseo.data.blocks.toc.hashPrefix+Fe(ye)}C.push({id:Ye,blockClientId:T,content:ye,level:Number(et),anchor:Xe?O.anchor:""})}}),C.forEach((T,O)=>{T.order=O});const v=fe(),I=(L=be(ne(v.headings)))==null?void 0:L.map(T=>(Object.keys(De).forEach(O=>{delete T[O]}),T));return!ke([...C].sort((T,O)=>T.order-O.order),[...I].sort((T,O)=>T.order-O.order))||((oe=be([...(Q=a(s))==null?void 0:Q.headings]))==null?void 0:oe.length)!==[...C].length||((V=a(s))==null?void 0:V.reOrdered)===!0&&!ke(be([...v.headings]).sort((T,O)=>T.editedOrder-O.editedOrder),be([...(q=a(s))==null?void 0:q.headings]).sort((T,O)=>T.editedOrder-O.editedOrder))?C:null},[s]),j!=null&&j.length){const r=fe(),c=be(ne(r.headings));if(!ke([...j].sort((a,p)=>a.order-p.order),[...c].sort((a,p)=>a.order-p.order))){const a=j.sort((p,u)=>p.editedOrder-u.editedOrder);window.aioseoBus.$emit("updateHeadings"+s,a)}}return te`
		<div className="${o}">
			<div id="aioseo-${s}"></div>
			${zi(e)}
		</div>
	`}const{RawHTML:yt}=window.wp.element;function Ji({attributes:{headings:e,listStyle:t}}){if(!e)return"";const n=Io(e,t);return te`<${yt}>${n}</${yt}>`}const Io=(e,t)=>{let n=`<${t}>`;return e.forEach(s=>{var l;if(s.hidden)return;let o="<li>";const i=s.editedContent||s.content;o+=`<a href="#${s.anchor}">${To.encode(i)}</a>`,(l=s.headings)!=null&&l.length&&(o+=Io(s.headings,t)),o+="</li>",n+=o}),n+=`</${t}>`,n},Gi=te`
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M0 0h24v24H0V0zm0 0h24v24H0V0z" fill="none"/><path d="M3 9h14V7H3v2zm0 4h14v-2H3v2zm0 4h14v-2H3v2zm16 0h2v-2h-2v2zm0-10v2h2V7h-2zm0 6h2v-2h-2v2z"/></svg>
`,{name:Ki,title:Yi,description:Xi,keywords:el,category:tl,supports:ol,attributes:sl}=_o,nl={title:Yi,description:Xi,keywords:el,category:tl,attributes:sl,icon:Gi,supports:ol,edit:Qi,save:Ji},al=Object.freeze(Object.defineProperty({__proto__:null,metadata:_o,name:Ki,settings:nl},Symbol.toStringTag,{value:"Module"})),il="https://schemas.wp.org/wp/5.8/block.json",ll="aioseo/faq",rl="AIOSEO - FAQ",dl="Easily add an SEO-friendly Frequently Asked Question (FAQ) to your content.",cl="aioseo",ul=["accordion","answer","faq","frequently asked questions","knowledge base","question","schema","seo"],pl="all-in-one-seo-pack",ml={hidden:{type:"boolean",default:!1},question:{type:"string",selector:".aioseo-faq-block-question"},schemaBlockId:{type:"string",default:""},tagName:{type:"string",default:"h3"}},hl={html:!1,anchor:!0,align:["wide","full"],color:{link:!0,__experimentalDefaultControls:{background:!0,text:!0}},typography:{fontSize:!0,lineHeight:!0},spacing:{margin:!0,padding:!0,blockGap:!0}},Lo={$schema:il,name:ll,title:rl,description:dl,category:cl,keywords:ul,textdomain:pl,attributes:ml,supports:hl},{RichText:gl,InnerBlocks:bl}=window.wp.blockEditor,{PanelBody:wt,SelectControl:fl,ToggleControl:yl}=window.wp.components,$t=window.wp.blockEditor.InspectorControls||window.wp.editor.InspectorControls,de="all-in-one-seo-pack",wl=[["core/paragraph",{placeholder:d("Write an answer...",de)}]],$l=["core/paragraph","core/heading","core/list","core/image","core/media-text"],vl=(e,t,n)=>{const{hidden:s,question:o,tagName:i,schemaBlockId:l}=t,r=co.getUpsellLink("faq-block",Yo.learnMore,"sidebar",!0);return te`
		<div data-schema-only="${s}" className="${e}" data-schema-block-id="${l}">
			<${$t}>
				<${wt} title=${d("FAQ Options",de)}>
					<${fl}
						label=${d("Title Wrapper",de)}
						options=${[{label:"DIV",value:"div"},{label:"H1",value:"h1"},{label:"H2",value:"h2"},{label:"H3",value:"h3"},{label:"H4",value:"h4"}]}
						value=${i}
						onChange=${c=>n({tagName:c})}
					/>

					<p className="aioseo-visibility-label">${d("Visibility",de)}</p>
					<${yl}
						className="aioseo-faq-visibility"
						label=${d("Display Block",de)}
						help=${d("You can choose to hide this block on the front-end of your site so that visitors won't see it but search engines will still see it.",de)}
						checked=${!s}
						onChange=${()=>n({hidden:!s})}
						disabled
					/>
					<div className="aioseo-alert aioseo-alert inline-upsell blue medium">
						<div dangerouslySetInnerHTML=${{__html:d("FAQ schema is a Pro feature.",de)+" "+r}}></div>
					</div>
				</${wt}>
			</${$t}>
			<${gl}
				tagName=${i}
				placeholder=${d("Write a question...",de)}
				className='aioseo-faq-block-question'
				value=${o}
				onChange=${c=>n({question:c})}
			/>
			<div className="aioseo-faq-block-answer">
				<${bl}
					template=${wl}
					allowedBlocks=${$l}
				/>
			</div>
		</div>
	`},{useSelect:vt}=window.wp.data,{useEffect:Sl}=window.wp.element,{serialize:_l}=window.wp.blocks,_e=[],kl=e=>{const{body:t}=document.implementation.createHTMLDocument("");t.innerHTML=e;function n(s){var i;const o=s.childNodes;for(const l of o){if((i=l.nodeName)!=null&&i.toLowerCase().includes("comment")&&l.parentNode.removeChild(l),l.attributes)for(const{name:r,value:c}of l.attributes){const a=c.replace(/\s+/g,"").toLowerCase();(!["src","href"].includes(r)||r.startsWith("on")||a.includes("javascript:")||a.includes("data:"))&&l.removeAttribute(r)}n(l)}}return n(t),t.innerHTML.trim().replace(/\s+/g," ")};function Il(e){const{attributes:t,className:n,clientId:s,setAttributes:o,isSelected:i}=e,{schemaBlockId:l}=t;!_e.includes(s)&&!i&&_e.push(s);const r=p=>{window.requestAnimationFrame(()=>{o(p)}),window.aioseoBus.$emit("schemaBlockUpdated")};Sl(()=>{(!l||1<(document.querySelectorAll(`[data-schema-block-id='${l}']`)||[]).length)&&r({schemaBlockId:ds()})},[]);const c=vt(p=>{const u=p("core/block-editor");if(!u)return;const{getAdjacentBlockClientId:h,getBlockAttributes:y,getBlockName:C,getClientIdsWithDescendants:H,getGlobalBlockCount:v}=u;if(_e.includes(s)||2>v("aioseo/faq"))return null;const I=h(s,-1);if(C(I)==="aioseo/faq")return y(I);const L=h(s,1);if(C(L)==="aioseo/faq")return y(L);const oe=H().filter(T=>C(T)==="aioseo/faq"),V=oe.indexOf(s),q=oe[V-1]||oe[V+1];return C(q)==="aioseo/faq"?y(q):null},[s,_e]);i&&!_e.includes(s)&&!t.question&&c&&(_e.push(s),window.requestAnimationFrame(()=>{o({backgroundColor:c.backgroundColor,textColor:c.textColor,tagName:c.tagName,hidden:c.hidden,fontSize:c.fontSize,style:c.style})}));const a=vt(p=>{const u=p("core/block-editor");if(!u)return;const{getBlocks:h}=u;return(C=>{const H=h(C),v=_l(H);return kl(v)})(s)},[s]);return r({answer:a}),vl(n,t,r)}const{RichText:Ll,InnerBlocks:Cl}=window.wp.blockEditor;function Vl({attributes:e,className:t}){const{question:n,hidden:s,tagName:o}=e;return te`
		<div data-schema-only="${s}" className=${t}>
			<${Ll.Content}
				tagName=${o}
				className="aioseo-faq-block-question"
				value=${n}
			/>
			<div className="aioseo-faq-block-answer">
				<${Cl.Content} />
			</div>
		</div>
	`}const Ol=te`
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M14 15q.425 0 .738-.312.312-.313.312-.738t-.312-.738Q14.425 12.9 14 12.9t-.737.312q-.313.313-.313.738t.313.738Q13.575 15 14 15Zm-.75-3.2h1.5q0-.725.15-1.063.15-.337.7-.887.75-.75 1-1.213.25-.462.25-1.087 0-1.125-.788-1.837Q15.275 5 14 5q-1.025 0-1.787.575-.763.575-1.063 1.525l1.35.55q.225-.625.613-.938Q13.5 6.4 14 6.4q.6 0 .975.337.375.338.375.913 0 .35-.2.662-.2.313-.7.788-.825.725-1.012 1.137-.188.413-.188 1.563ZM8 18q-.825 0-1.412-.587Q6 16.825 6 16V4q0-.825.588-1.413Q7.175 2 8 2h12q.825 0 1.413.587Q22 3.175 22 4v12q0 .825-.587 1.413Q20.825 18 20 18Zm-4 4q-.825 0-1.412-.587Q2 20.825 2 20V6h2v14h14v2Z"/></svg>
`,St="all-in-one-seo-pack",{name:Tl,title:Bl,description:Pl,category:El,supports:Al,attributes:xl}=Lo,Hl={title:Bl,description:Pl,category:El,attributes:xl,icon:Ol,example:{attributes:{question:d("Who should use AIOSEO?",St)},innerBlocks:[{name:"core/paragraph",attributes:{content:d("All in One SEO is perfect for business owners, bloggers, designers, developers, photographers, and basically everyone else. If you want to optimize your WordPress SEO, then you need to use All in One SEO Pack.",St)}}]},supports:Al,edit:Il,save:Vl},Ml=Object.freeze(Object.defineProperty({__proto__:null,metadata:Lo,name:Tl,settings:Hl},Symbol.toStringTag,{value:"Module"})),Nl="https://schemas.wp.org/wp/5.8/block.json",Dl="aioseo/key-points",ql="AIOSEO - Key Points",Rl="Easily add an Too Long, Didn't Read (TLDR) to your content.",Ul="text",zl=["list","tldr","key points"],jl="all-in-one-seo-pack",Fl={html:!1},Co={$schema:Nl,name:Dl,title:ql,description:Rl,category:Ul,keywords:zl,textdomain:jl,supports:Fl},{InnerBlocks:Zl}=window.wp.blockEditor,{useEffect:Wl}=window.wp.element,{select:Ql,dispatch:_t,subscribe:Jl}=window.wp.data;function Gl(e){const{className:t,clientId:n}=e;return Wl(()=>{const s=Jl(()=>{const i=Ql("core/block-editor").getBlocks(n).find(l=>l.name==="core/list");if(i){_t("core/block-editor").selectBlock(i.clientId);const l=i.attributes.values;if(l){const r=l.indexOf("<li>")+4,c={clientId:i.clientId,start:r,end:r};_t("core/block-editor").selectBlock(i.clientId,c)}s()}});return()=>s()},[n]),te`
    <div className="${t}">
      <div id="aioseo-${n}">
				<${Zl}
					template=${[["core/list"]]}
					templateLock="all"
				/>
      </div>
    </div>
  `}const{InnerBlocks:Kl}=window.wp.blockEditor;function Yl({className:e}){return te`
    <div className=${e}>
      <div className="aioseo-key-points-block-content">
        <${Kl.Content} />
      </div>
    </div>
  `}const Xl=te`
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25"><path fillRule="evenodd" clipRule="evenodd" d="M4.125 4.5H20.125V20.5H4.125V4.5ZM9.71875 6.89062H17.7188V8.49062H9.71875V6.89062ZM17.7188 10.0781H9.71875V11.6781H17.7188V10.0781ZM9.71875 13.2661H17.7188V14.8661H9.71875V13.2661ZM17.7188 16.5H9.71875V18.1H17.7188V16.5ZM6.51562 6.89062H8.11562V8.49062H6.51562V6.89062ZM8.11562 10.0781H6.51562V11.6781H8.11562V10.0781ZM6.51562 13.2661H8.11562V14.8661H6.51562V13.2661ZM8.11562 16.5H6.51562V18.1H8.11562V16.5Z"/></svg>
`,er="all-in-one-seo-pack",{name:tr,title:or,description:sr,category:nr,supports:ar,attributes:ir}=Co,lr={title:or,description:sr,category:nr,attributes:ir,icon:Xl,example:{innerBlocks:[{name:"core/paragraph",attributes:{content:d("All in One SEO is perfect for business owners, bloggers, designers, developers, photographers, and basically everyone else. If you want to optimize your WordPress SEO, then you need to use All in One SEO Pack.",er)}}]},supports:ar,edit:Gl,save:Yl},rr=Object.freeze(Object.defineProperty({__proto__:null,metadata:Co,name:tr,settings:lr},Symbol.toStringTag,{value:"Module"}));var lo;if(((lo=window.aioseo.currentPost)==null?void 0:lo.context)==="post"){let e=[];window.wp.data.subscribe(()=>{t()});const t=ot(()=>{const s=window.wp.data.select("core/block-editor").getBlocks();(s.length<e.length||s.length===1)&&n(),e=s},200);window.aioseoBus.$on("schemaBlockUpdated",()=>{n()});const n=ot(()=>{var l;let s=window.wp.data.select("core/block-editor").getBlocks();s=Ho(s),s=s.filter(r=>{var c;return(c=r==null?void 0:r.attributes)==null?void 0:c.schemaBlockId}),s=s.map(r=>(r.attributes.type=r.name,r));const o=me(),i=((l=o.currentPost.schema)==null?void 0:l.blockGraphs)||[];i.forEach((r,c)=>{const a=s.findIndex(h=>{var y;return((y=h==null?void 0:h.attributes)==null?void 0:y.schemaBlockId)===(r==null?void 0:r.schemaBlockId)});if(a===-1){i.splice(c,1);return}const p={...s[a].attributes};["backgroundColor","textColor","fontSize","style"].forEach(h=>{delete p[h]}),i[c]=p}),s.forEach(r=>{i.findIndex(a=>{var p;return(a==null?void 0:a.schemaBlockId)===((p=r==null?void 0:r.attributes)==null?void 0:p.schemaBlockId)})===-1&&(r!=null&&r.attributes)&&i.push(r.attributes)}),i.sort((r,c)=>{const a=s.findIndex(u=>{var h;return((h=u==null?void 0:u.attributes)==null?void 0:h.schemaBlockId)===(r==null?void 0:r.schemaBlockId)}),p=s.findIndex(u=>{var h;return((h=u==null?void 0:u.attributes)==null?void 0:h.schemaBlockId)===(c==null?void 0:c.schemaBlockId)});return a-p}),o.currentPost.schema.blockGraphs=i},200)}if(Bo()){const e=window.wp.element.createElement,t=e("svg",{width:25,height:25,viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e("path",{fillRule:"evenodd",clipRule:"evenodd",fill:"#434960",d:"M12.434 24c6.627 0 12-5.373 12-12s-5.373-12-12-12c-6.628 0-12 5.373-12 12s5.372 12 12 12zM10.523 4.392a.635.635 0 00-.74-.266 8.07 8.07 0 00-.92.391.659.659 0 00-.337.723l.206 1.041a.746.746 0 01-.264.713 6.293 6.293 0 00-.903.924.717.717 0 01-.698.272l-1.02-.206a.638.638 0 00-.708.346 8.493 8.493 0 00-.*********** 0 00.263.754l.867.588a.745.745 0 01.307.694 6.54 6.54 0 00.003 1.305.744.744 0 01-.305.695l-.865.591a.661.661 0 00-.26.755c.108.321.236.635.383.94a.638.638 0 00.708.343l1.02-.21a.716.716 0 01.7.27c.267.334.57.643.905.92a.746.746 0 01.266.712L8.55 18.67a.659.659 0 00.34.722 8.045 8.045 0 00.92.388c.458.16 1.097-.413 1.57-.836a1.12 1.12 0 00.379-.82v-1.748c0-.018 0-.036.002-.055-1.397-.34-2.435-1.622-2.435-3.152v-1.85c0-.14.111-.254.25-.254h.864v-1.82c0-.251.2-.455.446-.455a.45.45 0 01.446.455v1.82h2.34v-1.82c0-.251.2-.455.446-.455a.45.45 0 01.446.455v1.82h.865c.138 0 .25.114.25.254v1.85c0 1.578-1.105 2.893-2.569 3.182v1.76c0 .322.149.623.389.832.481.42 1.132.988 1.586.827a8.08 8.08 0 00.921-.391.659.659 0 00.336-.723l-.205-1.042a.746.746 0 01.264-.712c.328-.273.63-.582.902-.924a.717.717 0 01.698-.272l1.02.206a.638.638 0 00.708-.346 8.575 8.575 0 00.38-.94.661.661 0 00-.263-.754l-.867-.588a.745.745 0 01-.307-.694 6.481 6.481 0 00-.003-1.305.745.745 0 01.305-.695l.865-.591a.662.662 0 00.26-.755 8.435 8.435 0 00-.383-.94.638.638 0 00-.708-.343l-1.02.21a.716.716 0 01-.7-.27 6.297 6.297 0 00-.904-.92.747.747 0 01-.267-.712l.202-1.042a.659.659 0 00-.339-.722 8.164 8.164 0 00-.921-.388.635.635 0 00-.74.269l-.575.884a.718.718 0 01-.681.314 6.105 6.105 0 00-1.278.003.718.718 0 01-.682-.311l-.58-.883z"})),{getCategories:n,setCategories:s,registerBlockCollection:o,registerBlockType:i}=window.wp.blocks,l=[...n()];typeof o=="function"?o("aioseo",{title:"AIOSEO",icon:t}):(l.unshift({slug:"aioseo",title:"AIOSEO",icon:t}),s(l));const r=a=>{if(!a)return;const{name:p,settings:u}=a;if(u.icon&&!u.icon.foreground){const h={foreground:"#141B38",src:u.icon};u.icon=h}typeof o=="function"&&u.category==="aioseo"&&(u.category="widgets"),typeof o!="function"&&u.category!=="aioseo"&&(u.category="aioseo"),i(p,u)};(()=>{var a,p;[ls,en,((p=(a=window==null?void 0:window.aioseo)==null?void 0:a.screen)==null?void 0:p.base)==="post"?al:null,Ml,rr].forEach(r),window.aioseo.localBusiness&&[Hn,aa,ya,ka,Xa].forEach(r)})()}
