import{a0 as V,c as it,k as pe,j as Le,a as Ie,u as qe,m as xe,s as Ze}from"./index.a13799ab.js";import{g as K,v as R,o as h,c as I,H as g,l as r,x as B,t as u,a as i,u as e,J as x,k as S,b as M,O as nt,P as lt,f as Q,G as de,N as Ce,K as te,Q as ze,Z as Ne,n as we,z as He,w as _e,m as Ee,D as ct,T as je,y as Xe,q as Ye}from"./runtime-dom.esm-bundler.baf35205.js";import{u as dt}from"./vue-router.fd2bb90e.js";import{G as H}from"./constants.a8a14dc3.js";import{B as ut}from"./Editor.cb52ad98.js";import{C as he,b as j,S as pt,a as Qe}from"./Caret.7cc96622.js";import{C as ne}from"./Index.278f7f5c.js";import{C as ie}from"./SettingsRow.67d94513.js";import{j as ht,N as mt}from"./helpers.a0b389be.js";import"./translations.d159963e.js";import{_ as N}from"./_plugin-vue_export-helper.eefbdd86.js";import{_ as a,s as J}from"./default-i18n.20001971.js";import{n as X}from"./numbers.9fc174f3.js";import{S as gt}from"./LogoGear.383c5312.js";import{S as ft}from"./External.e551aa30.js";import{t as kt,a as Te}from"./index.d63536bd.js";import{C as $e}from"./Card.b4864217.js";import{C as Pe}from"./Tabs.4788ba43.js";import{C as Be}from"./Tooltip.78b61f71.js";import{G as yt,a as wt}from"./Row.c7b7fb90.js";import{G as ue}from"./Graph.63f9e093.js";import{_ as vt}from"./KeywordsGraph.e89c34fd.js";import{S as ve,a as At,c as bt}from"./Statistic.29914812.js";import{u as me}from"./WpTable.1371581e.js";import{C as ge}from"./Table.5c46c27d.js";import{C as Je}from"./Index.9f340035.js";import{u as We}from"./PostTypes.dafa8837.js";import{S as et}from"./External.49bc8f29.js";import{S as _t}from"./SeoStatisticsOverview.78e5e8a2.js";import{r as Ke}from"./params.af7ed354.js";import{S as Se}from"./Star.af6f3901.js";import{C as Ct}from"./Blur.601d8ea8.js";import{R as Bt}from"./RequiredPlans.545990bc.js";import{u as It}from"./Cta.bce043d1.js";import{u as Et}from"./License.bff5145f.js";import"./isEqual.34cebf90.js";import"./_baseIsEqual.e5574158.js";import"./_getTag.69d3a807.js";import"./_baseClone.475f8e3d.js";import"./_arrayEach.6af5abac.js";import"./Slide.1db617da.js";import"./ProBadge.2e262a91.js";import"./Information.8cb16b63.js";import"./vue3-apexcharts.b03ec956.js";import"./datetime.f197aeae.js";import"./ScrollTo.81bea8a7.js";import"./Download.7a5bbc25.js";import"./addons.90aa6c58.js";import"./upperFirst.9d3c89a3.js";import"./toString.1e64e8a6.js";import"./license.61cecabd.js";const St=""+window.__aioseoDynamicImportPreload__("images/import-from-csv.9622cc10.jpg"),tt=l=>(nt("data-v-babcc56b"),l=l(),lt(),l),xt={class:"keyword-rank-tracker-import-from-csv"},Qt={class:"aioseo-search-statistics-keyword-rank-tracker-modal__body"},Tt=["innerHTML"],Pt=tt(()=>i("br",null,null,-1)),Kt=tt(()=>i("br",null,null,-1)),Dt=["src"],Rt={class:"keyword-rank-tracker-import-from-csv__file-input"},Zt={class:"aioseo-search-statistics-keyword-rank-tracker-modal__footer"},Mt=`Keyword\r
luggage\r
travel\r
headphones`,Gt={__name:"ImportFromCsv",props:{modalOpen:Boolean},emits:["update:modalOpen","on-import-keywords"],setup(l,{emit:o}){const t="aioseo-pro",s=o,m=K({readCsvFile:null}),c=K(!1),n=K(null),A=K(null),d={headerTitle:a("Import Keywords",t),youCanImportKeywords:J(a("You can import keywords using a CSV file. The following column is required: %1$s.",t),"<b>Keyword</b>"),downloadSampleFile:a("Download Sample CSV File",t),fileUploadPlaceholder:a("Import from CSV file...",t),chooseAfile:a("Choose a File",t),import:a("Import",t),unableToReadCsvFile:a("Unable to read CSV file. Please check if the file is valid and try again.",t)},p=()=>{A.value=null,Object.keys(m.value).forEach(w=>{m.value[w]=null})},y=()=>{p(),n.value.$el.querySelector("input").focus(),n.value.$el.querySelector("input").click()},f=()=>{const w=new FileReader;return w.readAsText(A.value),new Promise((v,C)=>{w.onerror=()=>{w.abort(),C(new DOMException)},w.onload=()=>{const _=w.result.split(/[\r\n]/).filter(Boolean);(_.shift()||"").toLowerCase()!=="keyword"&&C(new DOMException),1>_.length&&C(new DOMException),v(_)}})},k=()=>{const w=new Blob([Mt],{type:"text/csv"}),v=URL.createObjectURL(w),C=document.createElement("a");C.href=v,C.download="aioseo-keywords-sample.csv",C.click(),C.remove()},b=async()=>{c.value=!0,f().then(w=>{p(),s("update:modalOpen",!1),s("on-import-keywords",w)}).catch(w=>{console.error(w),m.value.readCsvFile=d.unableToReadCsvFile}).finally(()=>{c.value=!1})};return(w,v)=>{const C=R("base-input"),_=R("base-button");return h(),I("div",xt,[g(e(ne),{show:l.modalOpen,onClose:v[6]||(v[6]=E=>{p(),w.$emit("update:modalOpen",!1)}),classes:["aioseo-search-statistics-keyword-rank-tracker-modal"]},{headerTitle:r(()=>[B(u(d.headerTitle),1)]),body:r(()=>[g(C,{accept:".csv",type:"file",value:A.value,"onUpdate:modelValue":v[0]||(v[0]=E=>A.value=n.value.$el.querySelector("input").files[0]),ref_key:"inputFile",ref:n},null,8,["value"]),i("div",Qt,[g(e(he),{type:"yellow"},{default:r(()=>[i("span",{innerHTML:d.youCanImportKeywords},null,8,Tt),Pt,Kt,i("a",{href:"#",onClick:v[1]||(v[1]=x(E=>k(),["prevent","exact"]))},u(d.downloadSampleFile),1)]),_:1}),i("img",{class:"keyword-rank-tracker-import-from-csv__image",src:e(ht)(e(St)),alt:""},null,8,Dt),i("div",Rt,[g(C,{modelValue:A.value?A.value.name:"",size:"medium",onFocus:v[2]||(v[2]=E=>y()),placeholder:d.fileUploadPlaceholder},null,8,["modelValue","placeholder"]),g(_,{type:"black",size:"medium",onClick:v[3]||(v[3]=x(E=>y(),["exact"]))},{default:r(()=>[B(u(d.chooseAfile),1)]),_:1})]),m.value.readCsvFile?(h(),S(e(he),{key:0,type:"red"},{default:r(()=>[B(u(m.value.readCsvFile),1)]),_:1})):M("",!0)])]),footer:r(()=>[i("div",Zt,[g(_,{type:"gray",size:"medium",onClick:v[4]||(v[4]=x(E=>{p(),w.$emit("update:modalOpen",!1)},["exact"]))},{default:r(()=>[B(u(e(H).cancel),1)]),_:1}),g(_,{class:"btn-import",type:"blue",size:"medium",loading:c.value,disabled:!A.value,onClick:v[5]||(v[5]=x(E=>b(),["exact"]))},{default:r(()=>[B(u(d.import),1)]),_:1},8,["loading","disabled"])])]),_:1},8,["show"])])}}},Ft=N(Gt,[["__scopeId","data-v-babcc56b"]]),Ot={class:"keyword-rank-tracker-import-from-gsc__body"},Ut={key:0,class:"keyword-rank-tracker-import-from-gsc__loader"},Vt={class:"keyword-rank-tracker-import-from-gsc__table"},Lt={class:"keyword-rank-tracker-import-from-gsc__table__header"},qt={class:"keyword-rank-tracker-import-from-gsc__table__row keyword-rank-tracker-import-from-gsc__table__row--header"},zt={class:"keyword-rank-tracker-import-from-gsc__table__column keyword-rank-tracker-import-from-gsc__table__column--keyword"},Nt=["checked"],Ht={class:"keyword-rank-tracker-import-from-gsc__table__body"},jt=["for"],Xt=["value","id"],Yt={class:"keyword-rank-tracker-import-from-gsc__table__column"},$t={class:"keyword-rank-tracker-import-from-gsc__table__column"},Jt={key:2},Wt={key:3},e1={class:"keyword-rank-tracker-import-from-gsc__footer"},t1=["innerHTML"],o1={__name:"ImportFromGsc",props:{modalOpen:Boolean},emits:["update:modalOpen","on-import-keywords"],setup(l,{emit:o}){const t="aioseo-pro",s=V(),m=o,c=K([]),n=Q(()=>c.value.length+s.keywords.count),A=Q(()=>J('<b class="keywords-qty keywords-qty--%1$s">%2$s / %3$s</b> %4$s',n.value>s.keywordsLimit?"red":"green",n.value,s.keywordsLimit,p.selectedKeywords)),d=Q(()=>s.gscKeywords.filter(k=>!s.keywords.all.rows.find(b=>b.name===k.keyword))),p={headerTitle:a("Import Keywords",t),import:a("Import",t),belowAre:a("Below are some of your best performing keywords from your Google Search Console account that you can start tracking.",t),selectedKeywords:a("Selected Keywords",t),noKeywordsFound:a("No keywords found.",t),alreadyTrackingAllFound:a("You are already tracking all the keywords found.",t),keyword:a("Keyword",t),clicks:a("Clicks",t),impressions:a("Impressions",t)},y=()=>{c.value=[]},f=async()=>{m("update:modalOpen",!1),m("on-import-keywords",c.value),await we(),y()};return(k,b)=>{const w=R("base-button");return h(),S(e(ne),{show:l.modalOpen,onClose:b[4]||(b[4]=v=>{y(),k.$emit("update:modalOpen",!1)}),classes:["keyword-rank-tracker-import-from-gsc"]},{headerTitle:r(()=>[B(u(p.headerTitle),1)]),body:r(()=>[i("div",Ot,[e(s).isFetchingGscKeywords?(h(),I("div",Ut,[g(e(j),{dark:""})])):d.value.length?(h(),I(de,{key:1},[i("div",null,u(p.belowAre),1),i("div",Vt,[i("div",Lt,[i("div",qt,[i("div",zt,[i("input",{type:"checkbox",onInput:b[0]||(b[0]=v=>v.target.checked?c.value=d.value.map(C=>C.keyword):y()),checked:c.value.length===d.value.length},null,40,Nt),B(" "+u(p.keyword),1)]),i("div",null,u(p.clicks),1),i("div",null,u(p.impressions),1)])]),i("div",Ht,[(h(!0),I(de,null,Ce(d.value,(v,C)=>(h(),I("div",{class:te(["keyword-rank-tracker-import-from-gsc__table__row",{"keyword-rank-tracker-import-from-gsc__table__row--active":c.value.includes(v.keyword)}]),key:`keyword-${C}`},[i("label",{class:"keyword-rank-tracker-import-from-gsc__table__column keyword-rank-tracker-import-from-gsc__table__column--keyword",for:`keyword-rank-tracker-import-from-gsc-keyword-${C}`},[ze(i("input",{type:"checkbox",name:"keyword","onUpdate:modelValue":b[1]||(b[1]=_=>c.value=_),value:v.keyword,id:`keyword-rank-tracker-import-from-gsc-keyword-${C}`},null,8,Xt),[[Ne,c.value]]),i("b",null,u(v.keyword),1)],8,jt),i("div",Yt,u(e(X).compactNumber(v.clicks)),1),i("div",$t,u(e(X).compactNumber(v.impressions)),1)],2))),128))])])],64)):e(s).gscKeywords.length?(h(),I("span",Jt,u(p.alreadyTrackingAllFound),1)):(h(),I("span",Wt,u(p.noKeywordsFound),1))])]),footer:r(()=>[i("div",e1,[i("div",null,[d.value.length?(h(),I("span",{key:0,innerHTML:A.value},null,8,t1)):M("",!0)]),i("div",null,[g(w,{class:"btn-cancel",type:"gray",size:"medium",onClick:b[2]||(b[2]=x(v=>{y(),k.$emit("update:modalOpen",!1)},["exact"]))},{default:r(()=>[B(u(e(H).cancel),1)]),_:1}),g(w,{class:"btn-import",type:"blue",size:"medium",disabled:!c.value.length,onClick:b[3]||(b[3]=x(v=>f(),["exact"]))},{default:r(()=>[B(u(p.import),1)]),_:1},8,["disabled"])])])]),_:1},8,["show"])}}},s1=N(o1,[["__scopeId","data-v-a66cdea9"]]),a1={class:"keyword-rank-tracker-import-from-site__body"},r1={class:"keyword-rank-tracker-import-from-site__table"},i1={class:"keyword-rank-tracker-import-from-site__table__header"},n1={class:"keyword-rank-tracker-import-from-site__table__row keyword-rank-tracker-import-from-site__table__row--header"},l1={class:"keyword-rank-tracker-import-from-site__table__body"},c1=["for"],d1=["value","id"],u1={class:"keyword-rank-tracker-import-from-site__table__column keyword-rank-tracker-import-from-site__table__column--post-title"},p1=["href"],h1=["innerHTML"],m1={class:"keyword-rank-tracker-import-from-site__table__column keyword-rank-tracker-import-from-site__table__column--score"},g1={key:1},f1={class:"keyword-rank-tracker-import-from-site__footer"},k1=["innerHTML"],y1={__name:"ImportFromSite",props:{modalOpen:Boolean},emits:["update:modalOpen","on-import-keywords"],setup(l,{emit:o}){const t="aioseo-pro",s=V(),m=o,c=K([]),n=Q(()=>c.value.length+s.keywords.count),A=Q(()=>J('<b class="keywords-qty keywords-qty--%1$s">%2$s / %3$s</b> %4$s',n.value>s.keywordsLimit?"red":"green",n.value,s.keywordsLimit,p.selectedKeywords)),d=Q(()=>s.siteFocusKeywords.filter(k=>!s.keywords.all.rows.find(b=>b.name===k.label))),p={keyword:a("Keyword",t),postTitle:a("Post Title",t),truSeoScore:a("TruSEO Score",t),headerTitle:a("Import Keywords",t),import:a("Import",t),belowAre:a("Below are highly optimized keywords from your website that you can select to start tracking.",t),selectedKeywords:a("Selected Keywords",t),noKeywordsFound:a("No focus keywords found. Any focus keywords that you add to posts will appear here.",t)},y=()=>{c.value=[]},f=async()=>{m("update:modalOpen",!1),m("on-import-keywords",c.value),await we(),y()};return(k,b)=>{const w=R("base-button");return h(),S(e(ne),{show:l.modalOpen,onClose:b[3]||(b[3]=v=>{y(),k.$emit("update:modalOpen",!1)}),classes:["keyword-rank-tracker-import-from-site"]},{headerTitle:r(()=>[B(u(p.headerTitle),1)]),body:r(()=>[i("div",a1,[d.value.length?(h(),I(de,{key:0},[i("div",null,u(p.belowAre),1),i("div",r1,[i("div",i1,[i("div",n1,[i("div",null,u(p.keyword),1),i("div",null,u(p.postTitle),1),i("div",null,u(p.truSeoScore),1)])]),i("div",l1,[(h(!0),I(de,null,Ce(d.value,(v,C)=>(h(),I("div",{class:te(["keyword-rank-tracker-import-from-site__table__row",{"keyword-rank-tracker-import-from-site__table__row--active":c.value.includes(v.label)}]),key:`keyword-${C}`},[i("label",{class:"keyword-rank-tracker-import-from-site__table__column keyword-rank-tracker-import-from-site__table__column--keyword",for:`keyword-rank-tracker-import-from-site-keyword-${C}`},[ze(i("input",{type:"checkbox",name:"keyword","onUpdate:modelValue":b[0]||(b[0]=_=>c.value=_),value:v.label,id:`keyword-rank-tracker-import-from-site-keyword-${C}`},null,8,d1),[[Ne,c.value]]),i("b",null,u(v.label),1)],8,c1),i("div",u1,[i("a",{href:v.postEditLink,target:"_blank"},[i("span",{class:"text-truncate",innerHTML:v.postTitle},null,8,h1),g(e(ft),{width:"12"})],8,p1)]),i("div",m1,[i("span",{class:te(["ribbon",`clr-${v.postScores.truSeo.color}`])},[g(e(gt),{width:"18"}),B(" "+u(v.postScores.truSeo.text),1)],2)])],2))),128))])])],64)):(h(),I("span",g1,u(p.noKeywordsFound),1))])]),footer:r(()=>[i("div",f1,[i("div",null,[d.value.length?(h(),I("span",{key:0,innerHTML:A.value},null,8,k1)):M("",!0)]),i("div",null,[g(w,{class:"btn-cancel",type:"gray",size:"medium",onClick:b[1]||(b[1]=x(v=>{y(),k.$emit("update:modalOpen",!1)},["exact"]))},{default:r(()=>[B(u(e(H).cancel),1)]),_:1}),g(w,{class:"btn-import",type:"blue",size:"medium",disabled:!c.value.length,onClick:b[2]||(b[2]=x(v=>f(),["exact"]))},{default:r(()=>[B(u(p.import),1)]),_:1},8,["disabled"])])])]),_:1},8,["show"])}}},w1=N(y1,[["__scopeId","data-v-867d6022"]]),v1={class:"keyword-rank-tracker-select-group"},A1={class:"multiselect__tag"},b1=["innerHTML"],_1=["onClick"],C1=["innerHTML"],B1={class:"keyword-rank-tracker-select-group__hint"},I1={__name:"SelectGroup",emits:["on-select-change"],setup(l,{emit:o}){const t="aioseo-pro",s=V(),m=o,c=K(s.keywords.selected.length===1?s.keywords.selected[0].groups:[]),n=Q({get:()=>c.value.map(p=>({...p,label:s.favoriteGroup.label===p.label?'<span style="color: #f18200">&starf;</span>':p.label})),set:p=>{c.value=p,m("on-select-change",p)}}),A=Q(()=>s.groups.all.rows.map(p=>({...p,label:s.favoriteGroup.label===p.label?'<span style="color: #f18200">&starf;</span>':p.label}))),d={selectGroup:a("Select a Group",t),createNewGroup:a("create new group",t)};return He(()=>{m("on-select-change",c.value)}),(p,y)=>{const f=R("base-select");return h(),I("div",v1,[g(e(ie),{name:d.selectGroup,"left-size":"12","right-size":"12","no-vertical-margin":"","no-border":"",style:{padding:"0"}},{content:r(()=>[g(f,{modelValue:n.value,"onUpdate:modelValue":y[0]||(y[0]=k=>n.value=k),multiple:"",size:"medium",options:A.value},{tag:r(({option:k,remove:b})=>[i("div",A1,[i("div",{class:"multiselect__tag-value",innerHTML:k.label},null,8,b1),i("div",{class:"multiselect__tag-remove",onClick:x(w=>b(k),["stop"])},[g(e(pt),{onClick:x(w=>b(k),["stop"])},null,8,["onClick"])],8,_1)])]),option:r(({option:k})=>[i("div",{innerHTML:k.label},null,8,C1)]),_:1},8,["modelValue","options"]),i("div",B1,[B(u(e(H).or.toLowerCase())+" ",1),i("a",{href:"#",onClick:y[1]||(y[1]=x(k=>e(s).toggleModal({modal:"modalOpenCreateGroup",open:!0}),["prevent","exact"]))},u(d.createNewGroup.toLowerCase())+". ",1)])]),_:1},8,["name"])])}}},ot=N(I1,[["__scopeId","data-v-c300e572"]]),E1={},S1={class:"aioseo-csv",fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"1.67 3.33 16.67 13.33"},x1=i("path",{d:"M4.792 12.5h2.5v-1.25H5.209v-2.5h2.083V7.5h-2.5a.8.8 0 0 0-.594.24.8.8 0 0 0-.24.593v3.334q0 .354.24.594t.594.239m3.25 0h2.5q.354 0 .594-.24t.24-.593v-1.25q0-.354-.24-.657a.73.73 0 0 0-.594-.302h-1.25V8.75h2.083V7.5h-2.5a.8.8 0 0 0-.593.24.8.8 0 0 0-.24.593v1.25q0 .354.24.636a.75.75 0 0 0 .593.281h1.25v.75H8.042zm5.5 0h1.25l1.458-5H15l-.833 2.875-.833-2.875h-1.25zM3.334 16.667q-.688 0-1.177-.49A1.6 1.6 0 0 1 1.667 15V5q0-.687.49-1.177.489-.49 1.177-.49h13.333q.687 0 1.177.49T18.334 5v10q0 .687-.49 1.177t-1.177.49zm0-1.667h13.333V5H3.334z",fill:"currentColor"},null,-1),Q1=[x1];function T1(l,o){return h(),I("svg",S1,Q1)}const P1=N(E1,[["render",T1]]),K1={},D1={class:"aioseo-key",fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20.18 20"},R1=i("path",{d:"M5.833 11.741q-.717 0-1.23-.512A1.68 1.68 0 0 1 4.093 10q0-.717.512-1.23a1.68 1.68 0 0 1 1.23-.511q.716 0 1.228.512.513.511.513 1.229 0 .717-.513 1.23a1.68 1.68 0 0 1-1.229.511m0 3.413q-2.143 0-3.649-1.505Q.68 12.143.68 10t1.505-3.649 3.65-1.505q1.449 0 2.622.717a5.3 5.3 0 0 1 1.84 1.862h7.308L20.184 10l-3.855 3.855-1.671-1.26-1.662 1.244-1.796-1.264h-.904a5.26 5.26 0 0 1-1.837 1.86q-1.176.72-2.626.72m0-1.82q1.187 0 2.077-.724a3.35 3.35 0 0 0 1.167-1.851h2.689l1.184.834 1.708-1.271 1.474 1.136 1.483-1.438-.774-.779H9.072a3.3 3.3 0 0 0-1.16-1.844 3.2 3.2 0 0 0-2.079-.73 3.2 3.2 0 0 0-2.354.979A3.2 3.2 0 0 0 2.499 10q0 1.375.98 2.354a3.2 3.2 0 0 0 2.354.98",fill:"currentColor"},null,-1),Z1=[R1];function M1(l,o){return h(),I("svg",D1,Z1)}const G1=N(K1,[["render",M1]]),F1={class:"keyword-rank-tracker-add-keywords"},O1={class:"aioseo-search-statistics-keyword-rank-tracker-modal__body keyword-rank-tracker-add-keywords__body"},U1={class:"keyword-rank-tracker-add-keywords__columns"},V1={class:"keyword-rank-tracker-add-keywords__column"},L1=i("div",{class:"keyword-rank-tracker-add-keywords__column__marker"},"1",-1),q1={class:"keyword-rank-tracker-add-keywords__editor"},z1=["innerHTML"],N1={class:"keyword-rank-tracker-add-keywords__column keyword-rank-tracker-add-keywords__column--border"},H1=i("div",{class:"keyword-rank-tracker-add-keywords__column__marker"},"2",-1),j1={class:"aioseo-search-statistics-keyword-rank-tracker-modal__footer"},X1={__name:"AddKeywords",props:{modalOpen:Boolean},emits:["update:modalOpen"],setup(l,{emit:o}){const t="aioseo-pro",s=V(),m=it(),c=o,n=l,A=K(""),d=K([]),p=K([]),y=K(!1),f=K(!1),k=K(!1),b=K(!1),w=K({keywordsEntered:null}),v=Q(()=>d.value.length+s.keywords.count),C=Q(()=>J('%1$s: <b class="keywords-qty keywords-qty--%2$s">%3$s / %4$s</b>',a("Keywords Entered",t),v.value>s.keywordsLimit?"red":"green",v.value,s.keywordsLimit)),_=Q(()=>!d.value.length||v.value>s.keywordsLimit),E={addToGroup:a("Add to Group",t),keywordsEntered:a("Keywords Entered",t),chooseYourKeywords:a("Enter Your Keywords, One per Line",t),headerTitle:a("Add Keywords",t),importKeywordsFrom:a("Import Keywords From",t),btnImportFromSiteLabel:a("Focus Keywords",t),btnImportFromCsvLabel:a("CSV",t),keywordsAboveLimit:a("You're trying to add keywords above your plan limit.",t),googleSearchConsole:a("Google Search Console",t)},Z=async()=>{y.value=!0;try{const z=p.value.map(D=>({id:D.value}));await s.insertKeywords({keywords:d.value,groups:z}),await s.fetchKeywords().then(()=>{W(),s.maybeFetchStatistics({context:"keywords"})}),c("update:modalOpen",!1),z.length&&s.fetchGroups().then(()=>{s.maybeFetchStatistics({context:"groups"})})}catch(z){console.error(z)}finally{y.value=!1}},le=async z=>{await we(),d.value=z,A.value=z.join(`
`)},W=()=>{d.value=[],p.value=[],A.value=""};return _e(()=>n.modalOpen,z=>{if(z&&(W(),!s.gscKeywords.length))try{s.fetchGscKeywords()}catch(D){console.error(D)}}),_e(()=>v.value,z=>{w.value.keywordsEntered=z>s.keywordsLimit?E.keywordsAboveLimit:null}),_e(()=>s.keywords.related.selected,z=>{z.length&&n.modalOpen&&le(z)}),(z,D)=>{const se=R("base-button");return h(),I("div",F1,[g(e(ne),{show:l.modalOpen,onClose:D[7]||(D[7]=U=>z.$emit("update:modalOpen",!1)),classes:["aioseo-search-statistics-keyword-rank-tracker-modal"]},{headerTitle:r(()=>[B(u(E.headerTitle),1)]),body:r(()=>[i("div",O1,[i("div",U1,[i("div",V1,[L1,g(e(ie),{name:E.chooseYourKeywords,"left-size":"12","right-size":"12","no-vertical-margin":"","no-border":""},{content:r(()=>[i("div",q1,[g(e(ut),{modelValue:A.value,"onUpdate:modelValue":D[0]||(D[0]=U=>d.value=U.split(`
`).map(L=>e(mt)(L).trim()).filter(L=>L)),"line-numbers":!0,"minimum-line-numbers":8,monospace:"","force-updates":""},null,8,["modelValue"])]),i("span",{class:"small",innerHTML:C.value},null,8,z1),w.value.keywordsEntered?(h(),S(e(he),{key:0,type:"yellow"},{default:r(()=>[B(u(w.value.keywordsEntered),1)]),_:1})):M("",!0),e(s).errors.crud?(h(),S(e(he),{key:1,type:"red"},{default:r(()=>[B(u(e(s).errors.crud),1)]),_:1})):M("",!0)]),_:1},8,["name"]),g(e(ie),{"data-or":e(H).or,class:"aioseo-settings-row--or","left-size":"12","right-size":"12"},null,8,["data-or"]),g(e(ie),{class:"aioseo-settings-row__buttons",name:E.importKeywordsFrom,"left-size":"12","right-size":"12","no-vertical-margin":"","no-border":""},{content:r(()=>{var U;return[g(se,{type:"black",size:"medium",onClick:D[1]||(D[1]=x(L=>f.value=!0,["exact"]))},{default:r(()=>[g(e(kt),{width:"20",height:"18"}),B(" "+u(E.googleSearchConsole),1)]),_:1}),(U=e(m).options)!=null&&U.advanced.truSeo?(h(),S(se,{key:0,type:"black",size:"medium",onClick:D[2]||(D[2]=x(L=>b.value=!0,["exact"]))},{default:r(()=>[g(e(G1),{width:"20",height:"18"}),B(" "+u(E.btnImportFromSiteLabel),1)]),_:1})):M("",!0),g(se,{type:"black",size:"medium",onClick:D[3]||(D[3]=x(L=>k.value=!0,["exact"]))},{default:r(()=>[g(e(P1),{width:"20",height:"18"}),B(" "+u(E.btnImportFromCsvLabel),1)]),_:1})]}),_:1},8,["name"])]),i("div",N1,[H1,g(e(ie),{name:E.addToGroup,"left-size":"12","right-size":"12","no-vertical-margin":"","no-border":""},{content:r(()=>[g(e(ot),{onOnSelectChange:D[4]||(D[4]=U=>{p.value=U})})]),_:1},8,["name"])])])])]),footer:r(()=>[i("div",j1,[g(se,{type:"gray",size:"medium",onClick:D[5]||(D[5]=x(U=>z.$emit("update:modalOpen",!1),["exact"]))},{default:r(()=>[B(u(e(H).cancel),1)]),_:1}),g(se,{type:"blue",size:"medium",loading:y.value,disabled:_.value,onClick:D[6]||(D[6]=x(U=>Z(),["exact"]))},{default:r(()=>[B(u(E.headerTitle),1)]),_:1},8,["loading","disabled"])])]),_:1},8,["show"]),g(e(s1),{"modal-open":f.value,"onUpdate:modalOpen":D[8]||(D[8]=U=>f.value=U),onOnImportKeywords:D[9]||(D[9]=U=>le(U))},null,8,["modal-open"]),g(e(Ft),{"modal-open":k.value,"onUpdate:modalOpen":D[10]||(D[10]=U=>k.value=U),onOnImportKeywords:D[11]||(D[11]=U=>le(U))},null,8,["modal-open"]),g(e(w1),{"modal-open":b.value,"onUpdate:modalOpen":D[12]||(D[12]=U=>b.value=U),onOnImportKeywords:D[13]||(D[13]=U=>le(U))},null,8,["modal-open"])])}}},Y1={class:"keyword-rank-tracker-assign-groups"},$1={class:"aioseo-search-statistics-keyword-rank-tracker-modal__body"},J1={class:"aioseo-search-statistics-keyword-rank-tracker-modal__footer"},W1={__name:"AssignGroups",props:{modalOpen:Boolean},emits:["update:modalOpen"],setup(l,{emit:o}){const t="aioseo-pro",s=V(),m=o,c=K([]),n=K(!1),A=Q(()=>d.value==="create"?!c.value.length:!1),d=Q(()=>s.keywords.selected.every(k=>k.groups.length)?"update":"create"),p=Q(()=>d.value==="create"?y.addToGroup:y.editGroup),y={addToGroup:a("Assign to Group(s)",t),editGroup:a("Edit Group(s)",t),saveChanges:a("Save Changes",t)},f=async()=>{n.value=!0;try{await s.updateRelationships({keywords:s.keywords.selected,groups:c.value}),await s.fetchGroups().then(()=>{s.maybeFetchStatistics({context:"groups"}),s.abstractFetchKeywords({updateKeywords:!0})}),m("update:modalOpen",!1)}catch(k){console.error(k)}finally{n.value=!1}};return(k,b)=>{const w=R("base-button");return h(),I("div",Y1,[g(e(ne),{show:l.modalOpen,onClose:b[3]||(b[3]=v=>k.$emit("update:modalOpen",!1)),classes:["aioseo-search-statistics-keyword-rank-tracker-modal"],"allow-overflow":""},{headerTitle:r(()=>[B(u(p.value),1)]),body:r(()=>[i("div",$1,[g(e(ot),{onOnSelectChange:b[0]||(b[0]=v=>{c.value=v})})])]),footer:r(()=>[i("div",J1,[g(w,{type:"gray",size:"medium",onClick:b[1]||(b[1]=x(v=>k.$emit("update:modalOpen",!1),["exact"]))},{default:r(()=>[B(u(e(H).cancel),1)]),_:1}),g(w,{type:"blue",size:"medium",loading:n.value,disabled:A.value,onClick:b[2]||(b[2]=x(v=>f(),["exact"]))},{default:r(()=>[B(u(y.saveChanges),1)]),_:1},8,["loading","disabled"])])]),_:1},8,["show"])])}}},eo={class:"keyword-rank-tracker-create-group"},to={class:"aioseo-search-statistics-keyword-rank-tracker-modal__body"},oo={class:"aioseo-search-statistics-keyword-rank-tracker-modal__footer"},so={__name:"CreateGroup",props:{modalOpen:Boolean},emits:["update:modalOpen"],setup(l,{emit:o}){const t="aioseo-pro",s=V(),m=o,c=K(""),n=K([]),A=K(!1),d=Q(()=>!c.value.trim()),p=Q(()=>s.keywords.all.rows),y={headerTitle:a("Create Group",t),groupName:a("Group Name",t),selectKeywords:a("Add Keywords to Group",t)},f=async()=>{A.value=!0;try{const b=n.value.map(w=>({id:w.value}));await s.insertGroups({groups:[c.value],keywords:b}),await s.fetchGroups().then(()=>{b.length&&s.maybeFetchStatistics({context:"groups"})}),k(),m("update:modalOpen",!1)}catch(b){console.error(b)}finally{A.value=!1}},k=()=>{c.value="",n.value=[]};return(b,w)=>{const v=R("base-input"),C=R("base-select"),_=R("base-button");return h(),I("div",eo,[g(e(ne),{show:l.modalOpen,onClose:w[4]||(w[4]=E=>{k(),b.$emit("update:modalOpen",!1)}),classes:["aioseo-search-statistics-keyword-rank-tracker-modal"],"allow-overflow":""},{headerTitle:r(()=>[B(u(y.headerTitle),1)]),body:r(()=>[i("div",to,[e(s).errors.crud?(h(),S(e(he),{key:0,type:"red"},{default:r(()=>[B(u(e(s).errors.crud),1)]),_:1})):M("",!0),g(e(ie),{name:y.groupName,"left-size":"12","right-size":"12","no-border":""},{content:r(()=>[g(v,{modelValue:c.value,"onUpdate:modelValue":w[0]||(w[0]=E=>c.value=E),size:"medium",placeholder:y.groupName,maxlength:e(s).options.input.group.maxlength},null,8,["modelValue","placeholder","maxlength"])]),_:1},8,["name"]),g(e(ie),{name:y.selectKeywords,"left-size":"12","right-size":"12","no-vertical-margin":"","no-border":"",style:{padding:"0"}},{content:r(()=>[g(C,{modelValue:n.value,"onUpdate:modelValue":w[1]||(w[1]=E=>n.value=E),multiple:"",size:"medium",options:p.value},null,8,["modelValue","options"])]),_:1},8,["name"])])]),footer:r(()=>[i("div",oo,[g(_,{type:"gray",size:"medium",onClick:w[2]||(w[2]=x(E=>{k(),b.$emit("update:modalOpen",!1)},["exact"]))},{default:r(()=>[B(u(e(H).cancel),1)]),_:1}),g(_,{type:"blue",size:"medium",loading:A.value,disabled:d.value,onClick:w[3]||(w[3]=x(E=>f(),["exact"]))},{default:r(()=>[B(u(y.headerTitle),1)]),_:1},8,["loading","disabled"])])]),_:1},8,["show"])])}}},ao={class:"keyword-rank-tracker-delete-groups"},ro={class:"aioseo-search-statistics-keyword-rank-tracker-modal__body"},io={class:"aioseo-search-statistics-keyword-rank-tracker-modal__footer"},no={__name:"DeleteGroups",props:{modalOpen:Boolean},emits:["update:modalOpen"],setup(l,{emit:o}){const t="aioseo-pro",s=V(),m=o,c=K(!1),n={headerTitle:a("Delete Group(s)",t),areYouSure:a("Are you sure you want to delete the following group(s)?",t)},A=async()=>{c.value=!0;try{await s.deleteGroups(s.groups.selected.map(d=>d.id)),await s.fetchGroups(),await s.fetchKeywords(),m("update:modalOpen",!1)}catch(d){console.error(d)}finally{c.value=!1}};return(d,p)=>{const y=R("base-button");return h(),I("div",ao,[g(e(ne),{show:l.modalOpen,onClose:p[2]||(p[2]=f=>d.$emit("update:modalOpen",!1)),classes:["aioseo-search-statistics-keyword-rank-tracker-modal"]},{headerTitle:r(()=>[B(u(n.headerTitle),1)]),body:r(()=>[i("div",ro,[i("span",null,u(n.areYouSure),1),i("b",null,u(e(s).groups.selected.map(f=>f.name).join(", ")),1)])]),footer:r(()=>[i("div",io,[g(y,{type:"gray",size:"medium",onClick:p[0]||(p[0]=x(f=>d.$emit("update:modalOpen",!1),["exact"]))},{default:r(()=>[B(u(e(H).cancel),1)]),_:1}),g(y,{type:"red",size:"medium",loading:c.value,onClick:p[1]||(p[1]=x(f=>A(),["exact"]))},{default:r(()=>[B(u(e(H).delete),1)]),_:1},8,["loading"])])]),_:1},8,["show"])])}}},lo={class:"keyword-rank-tracker-delete-keywords"},co={class:"aioseo-search-statistics-keyword-rank-tracker-modal__body"},uo={class:"aioseo-search-statistics-keyword-rank-tracker-modal__footer"},po={__name:"DeleteKeywords",props:{modalOpen:Boolean},emits:["update:modalOpen"],setup(l,{emit:o}){const t="aioseo-pro",s=V(),m=o,c=K(!1),n={headerTitle:a("Delete Keyword(s)",t),areYouSure:a("Are you sure you want to stop tracking the following keyword(s)?",t)},A=async()=>{c.value=!0;try{await s.deleteKeywords(s.keywords.selected.map(d=>d.id)),await s.abstractFetchKeywords({updateKeywords:!0}),await s.fetchGroups().then(async()=>{s.maybeFetchStatistics({context:"groups"})}),m("update:modalOpen",!1)}catch(d){console.error(d)}finally{c.value=!1}};return(d,p)=>{const y=R("base-button");return h(),I("div",lo,[g(e(ne),{show:l.modalOpen,onClose:p[2]||(p[2]=f=>d.$emit("update:modalOpen",!1)),classes:["aioseo-search-statistics-keyword-rank-tracker-modal"]},{headerTitle:r(()=>[B(u(n.headerTitle),1)]),body:r(()=>[i("div",co,[i("span",null,u(n.areYouSure),1),i("b",null,u(e(s).keywords.selected.map(f=>f.name).join(", ")),1)])]),footer:r(()=>[i("div",uo,[g(y,{type:"gray",size:"medium",onClick:p[0]||(p[0]=x(f=>d.$emit("update:modalOpen",!1),["exact"]))},{default:r(()=>[B(u(e(H).cancel),1)]),_:1}),g(y,{type:"red",size:"medium",loading:c.value,onClick:p[1]||(p[1]=x(f=>A(),["exact"]))},{default:r(()=>[B(u(e(H).delete),1)]),_:1},8,["loading"])])]),_:1},8,["show"])])}}},ye="all-in-one-seo-pack",ho={setup(){return{searchStatisticsStore:pe()}},components:{Graph:ue},computed:{series(){var o,t;if(!((t=(o=this.searchStatisticsStore.data)==null?void 0:o.keywords)!=null&&t.distribution))return[];const l=this.searchStatisticsStore.data.keywords.distribution;return[{name:a("Keywords",ye),data:[{x:a("Top 3 Position",ye),y:l.top3,fillColor:"#005AE0"},{x:a("4-10 Position",ye),y:l.top10,fillColor:"#00AA63"},{x:a("11-50 Position",ye),y:l.top50,fillColor:"#F18200"},{x:a("50-100 Position",ye),y:l.top100,fillColor:"#DF2A4A"}]}]}}},mo={class:"aioseo-search-statistics-keywords-distribution-graph"};function go(l,o,t,s,m,c){const n=R("graph");return h(),I("div",mo,[g(n,{series:c.series,loading:s.searchStatisticsStore.loading.keywords,preset:"keywordsDistribution"},null,8,["series","loading"])])}const fo=N(ho,[["render",go]]),ko={class:"post-title"},yo={key:0,class:"row-actions"},wo={class:"edit"},vo=["href"],Ao=["href"],Me="search-statistics-keywords-inner-table",bo={__name:"KeywordInner",props:{paginatedRows:Object},setup(l){const o="all-in-one-seo-pack",t=pe(),s=l,m=K(5),c=K(null),{editPost:n,viewPost:A}=We(),{offset:d,processChangeItemsPerPage:p,processPagination:y,wpTableKey:f,wpTableLoading:k}=me({fetchData(){return C({keywords:[s.paginatedRows.keyword],limit:m.value,offset:d.value}).catch(_=>{console.error(_)})},tableId:Me,tableRef:c.value,resultsPerPage:m}),b=Q(()=>s.paginatedRows.totals||{page:1,pages:0,total:0}),w=Q(()=>b.value.page),v=Q(()=>[{slug:"post_title",label:a("Title",o),width:"100%"},{slug:"clicks",label:a("Clicks",o),width:"120px"},{slug:"ctr",label:a("Avg. CTR",o),width:"120px"},{slug:"impressions",label:a("Impressions",o),width:"120px"},{slug:"position",label:a("Position",o),width:"120px"}]),C=_=>t.getPagesByKeywords(_).catch(()=>t.getPagesByKeywords(_)).catch(()=>t.getPagesByKeywords(_)).then(E=>{s.paginatedRows.pages=Object.values((E==null?void 0:E.rows)||{}),s.paginatedRows.totals=(E==null?void 0:E.totals)||{}});return He(()=>{const _=s.paginatedRows.keyword;k.value=!0,C({keywords:[_],limit:m.value,offset:d.value}).catch(E=>{console.error(E)}).finally(()=>{k.value=!1})}),(_,E)=>(h(),S(e(ge),{ref_key:"table",ref:c,id:Me,"additional-filters":[],columns:v.value,filters:[],"initial-items-per-page":m.value,"initial-page-number":w.value,"initial-search-term":"",key:e(f),loading:e(k),rows:l.paginatedRows.pages||[],"show-bulk-actions":!1,"show-header":!1,"show-table-footer":!0,totals:b.value,"show-items-per-page":!0,onPaginate:e(y),"show-search":!1,"show-pagination":!0,onProcessChangeItemsPerPage:e(p)},{post_title:r(({row:Z})=>[i("div",ko,[i("b",null,u(Z.objectTitle),1)]),Z!=null&&Z.objectId?(h(),I("div",yo,[i("span",wo,[i("a",{href:Z.context.permalink,target:"_blank"},u(e(A)(Z.context.postType.singular)),9,vo),B(" | "),i("a",{href:Z.context.editLink,target:"_blank"},u(e(n)(Z.context.postType.singular)),9,Ao)])])):M("",!0)]),clicks:r(({row:Z})=>[B(u(e(X).compactNumber(Z.clicks)),1)]),ctr:r(({row:Z})=>[B(u(parseFloat(Z.ctr))+"% ",1)]),impressions:r(({row:Z})=>[B(u(e(X).compactNumber(Z.impressions)),1)]),position:r(({row:Z})=>[Z.difference.comparison?(h(),S(e(ve),{key:0,type:"position",total:Z.position,difference:Z.difference.position,"tooltip-offset":"-150px,0"},null,8,["total","difference"])):M("",!0)]),_:1},8,["columns","initial-items-per-page","initial-page-number","loading","rows","totals","onPaginate","onProcessChangeItemsPerPage"]))}},_o=N(bo,[["__scopeId","data-v-2f00e628"]]),ee="all-in-one-seo-pack",Co={setup(l){const o="aioseo-search-statistics-keywords-table",t=Q(()=>l.postDetail?"searchStatisticsPostDetailKeywords":"searchStatisticsKeywordRankings"),s=Z=>`https://www.google.com/search?q=${encodeURIComponent(Z)}`,m=K(!1),c=pe(),n=Z=>(m.value=!0,l.page!==""&&(Z={...Z,page:l.page}),l.postDetail?c.updatePostDetailKeywords(Z).finally(()=>{m.value=!1}):c.updateKeywords(Z).finally(()=>{m.value=!1})),A=K(!1),{orderBy:d,orderDir:p,processFilter:y}=At({processFilterTable:Z=>w(Z),showUpsell:A}),{filter:f,processAdditionalFilters:k,processChangeItemsPerPage:b,processFilterTable:w,processPagination:v,processSearch:C,processSort:_,searchTerm:E}=me({changeItemsPerPageSlug:t.value,fetchData:n,orderBy:d,orderDir:p,tableId:o});return{changeItemsPerPageSlug:t,filter:f,keywordRankTrackerStore:V(),licenseStore:Le(),links:Ie,orderBy:d,orderDir:p,processAdditionalFilters:k,processChangeItemsPerPage:b,processFilter:y,processPagination:v,processSearch:C,processSort:_,rootStore:qe(),searchStatisticsStore:c,searchTerm:E,settingsStore:xe(),showUpsell:A,tableId:o,viewInGoogleLink:s}},components:{CoreTooltip:Be,CoreWpTable:ge,Cta:Je,KeywordInner:_o,Statistic:ve,SvgCaret:Qe,SvgExternal:et},props:{keywords:Object,loading:{type:Boolean,default(){return!1}},showHeader:{type:Boolean,default(){return!0}},showTableFooter:Boolean,showItemsPerPage:Boolean,columns:{type:Array,default(){return["keyword","clicks","ctr","impressions","position","diffPosition","buttons"]}},appendColumns:{type:Object,default(){return{}}},postDetail:{type:Boolean,default(){return!1}},refreshOnLoad:{type:Boolean,default(){return!0}},page:{type:String,default(){return""}},initialFilter:String},data(){return{numbers:X,activeRow:-1,interval:null,sortableColumns:[],strings:{addKeyword:a("Add to KRT",ee),removeFromKrt:a("Remove from KRT",ee),viewInGoogle:a("View in Google",ee),position:a("Position",ee),ctaButtonText:a("Unlock Keyword Tracking",ee),ctaHeader:J(a("Keyword Tracking is a %1$s Feature",ee),"PRO")}}},computed:{getFilters(){return this.searchStatisticsStore.shouldShowSampleReports?[]:this.keywords.filters},allColumns(){var t,s;const l=bt(this.columns),o=((s=(t=this.keywords)==null?void 0:t.filters)==null?void 0:s.find(m=>m.active))||{};return this.appendColumns[o.slug||"all"]&&l.push(this.appendColumns[o.slug||"all"]),l.map(m=>(m.endsWith("Sortable")&&(m=m.replace("Sortable",""),this.sortableColumns.push(m)),m))},tableColumns(){return[{slug:"keyword",label:a("Keyword",ee)},{slug:"clicks",label:a("Clicks",ee),width:"80px"},{slug:"ctr",label:a("Avg. CTR",ee),width:"100px"},{slug:"impressions",label:a("Impressions",ee),width:"120px"},{slug:"position",label:a("Position",ee),width:"85px"},{slug:"diffDecay",label:a("Diff",ee),width:"95px"},{slug:"diffPosition",label:a("Diff",ee),width:"80px"},{slug:"buttons",label:"",width:this.hasSlot("buttons")?"240px":"40px"}].filter(l=>this.allColumns.includes(l.slug)).map(l=>(l.sortable=this.isSortable&&this.sortableColumns.includes(l.slug),l.sortable&&(l.sortDir=l.slug===this.orderBy?this.orderDir:"asc",l.sorted=l.slug===this.orderBy),l)).filter(l=>!this.searchStatisticsStore.shouldShowSampleReports||l.slug!=="buttons")},isSortable(){return this.filter==="all"&&this.rootStore.isPro&&!this.licenseStore.isUnlicensed}},methods:{isTrackingKeyword(l){return this.keywordRankTrackerStore.keywords.all.rows.find(o=>o.name===l.keyword)},sanitizeString:Ze,isRowActive(l){return l===this.activeRow},toggleRow(l){if(this.activeRow===l){this.activeRow=-1;return}this.activeRow=l},hasSlot(l="default"){return!!this.$slots[l]},shouldLimitText(l){return 120<Ze(l).length},maybeTrackKeyword(l){this.keywordRankTrackerStore.parentActiveTab="rank-tracker",this.keywordRankTrackerStore.toggleModal({modal:"modalOpenAddKeywords",open:!0,relatedKeywords:[l.keyword]})},maybeUntrackKeyword(l){this.keywordRankTrackerStore.parentActiveTab="rank-tracker",this.keywordRankTrackerStore.toggleModal({modal:"modalOpenDeleteKeywords",open:!0,keywords:[this.keywordRankTrackerStore.keywords.all.rows.find(o=>o.name===l.keyword)]})}},async mounted(){var l,o;this.initialFilter&&this.processFilter({slug:this.initialFilter}),this.orderBy=((l=this.defaultSorting)==null?void 0:l.orderBy)||this.orderBy,this.orderDir=((o=this.defaultSorting)==null?void 0:o.orderDir)||this.orderDir}},Bo={class:"aioseo-search-statistics-keywords-table"},Io={class:"post-title"},Eo=["onClick"],So=["onClick"],xo={class:"row-actions"},Qo={class:"edit"},To=["href"],Po=["onClick"],Ko={key:0,class:"delete"},Do=["onClick"],Ro={class:""};function Zo(l,o,t,s,m,c){const n=R("core-tooltip"),A=R("svg-external"),d=R("statistic"),p=R("svg-caret"),y=R("base-button"),f=R("keyword-inner"),k=R("cta"),b=R("core-wp-table");return h(),I("div",Bo,[g(b,{ref:"table",class:"keywords-table",id:s.tableId,columns:c.tableColumns,rows:Object.values(t.keywords.rows),totals:t.keywords.totals,filters:c.getFilters,"additional-filters":t.keywords.additionalFilters,loading:t.loading,"initial-page-number":t.keywords.totals.page||1,"initial-search-term":s.searchTerm,"initial-items-per-page":s.settingsStore.settings.tablePagination[s.changeItemsPerPageSlug],"show-header":t.showHeader,"show-bulk-actions":!1,"show-table-footer":t.showTableFooter,"show-items-per-page":t.showItemsPerPage&&!s.searchStatisticsStore.shouldShowSampleReports,"show-pagination":"","blur-rows":s.showUpsell,onFilterTable:s.processFilter,onProcessAdditionalFilters:s.processAdditionalFilters,onPaginate:s.processPagination,onProcessChangeItemsPerPage:s.processChangeItemsPerPage,onSearch:s.processSearch,onSortColumn:s.processSort},{keyword:r(({row:w,index:v,editRow:C})=>[i("div",Io,[c.shouldLimitText(w.keyword)?(h(),S(n,{key:0},{tooltip:r(()=>[B(u(c.sanitizeString(w.keyword)),1)]),default:r(()=>[i("a",{class:"limit-line",href:"#",onClick:x(_=>{C(v),c.toggleRow(v)},["prevent"])},u(c.sanitizeString(w.keyword)),9,Eo)]),_:2},1024)):(h(),I("a",{key:1,href:"#",onClick:x(_=>{C(v),c.toggleRow(v)},["prevent"])},u(c.sanitizeString(w.keyword)),9,So))]),i("div",xo,[i("span",Qo,[i("a",{href:s.viewInGoogleLink(w.keyword),target:"_blank"},[B(u(m.strings.viewInGoogle)+" ",1),g(A)],8,To),B(" | "),c.isTrackingKeyword(w)?M("",!0):(h(),I("a",{key:0,href:"#",onClick:x(_=>c.maybeTrackKeyword(w),["prevent","exact"])},u(m.strings.addKeyword),9,Po))]),c.isTrackingKeyword(w)?(h(),I("span",Ko,[i("a",{href:"#",onClick:x(_=>c.maybeUntrackKeyword(w),["prevent","exact"])},u(m.strings.removeFromKrt),9,Do)])):M("",!0)])]),clicks:r(({row:w})=>[B(u(w.clicks),1)]),ctr:r(({row:w})=>[B(u(parseFloat(w.ctr))+"% ",1)]),impressions:r(({row:w})=>[B(u(m.numbers.compactNumber(w.impressions)),1)]),position:r(({row:w})=>[B(u(Math.round(w.position).toFixed(0)),1)]),diffPosition:r(({row:w})=>[w.difference.comparison?(h(),S(d,{key:0,type:"position",difference:w.difference.position,showCurrent:!1,"tooltip-offset":"-100px,0"},null,8,["difference"])):M("",!0)]),diffDecay:r(({row:w})=>[w.difference.comparison?(h(),S(d,{key:0,type:"decay",difference:w.difference.decay,showCurrent:!1,"tooltip-offset":"-100px,0"},null,8,["difference"])):M("",!0)]),buttons:r(({row:w,index:v,column:C,editRow:_})=>[i("div",Ro,[Ee(l.$slots,"buttons",{row:w,column:C,index:v}),g(y,{type:"gray",class:te(["toggle-row-button",{active:c.isRowActive(v)}]),onClick:E=>{_(v),c.toggleRow(v)}},{default:r(()=>[g(p)]),_:2},1032,["class","onClick"])])]),"edit-row":r(({index:w})=>[g(f,{"paginated-rows":t.keywords.rows[w]},null,8,["paginated-rows"])]),cta:r(()=>[s.showUpsell?(h(),S(k,{key:0,"cta-link":s.links.getPricingUrl("search-statistics","search-statistics-upsell"),"button-text":m.strings.ctaButtonText,"learn-more-link":s.links.getUpsellUrl("search-statistics","search-statistics-upsell",s.rootStore.isPro?"pricing":"liteUpgrade"),"hide-bonus":!s.licenseStore.isUnlicensed},{"header-text":r(()=>[B(u(m.strings.ctaHeader),1)]),_:1},8,["cta-link","button-text","learn-more-link","hide-bonus"])):M("",!0)]),tablenav:r(()=>[Ee(l.$slots,"tablenav")]),_:3},8,["id","columns","rows","totals","filters","additional-filters","loading","initial-page-number","initial-search-term","initial-items-per-page","show-header","show-table-footer","show-items-per-page","blur-rows","onFilterTable","onProcessAdditionalFilters","onPaginate","onProcessChangeItemsPerPage","onSearch","onSortColumn"])])}const Mo=N(Co,[["render",Zo]]),be="all-in-one-seo-pack",Go={setup(){return{searchStatisticsStore:pe()}},components:{CoreSettingsRow:ie,CoreTooltip:Be,GridColumn:yt,GridRow:wt,KeywordsDistributionGraph:fo,KeywordsGraph:vt,KeywordsTable:Mo,SeoStatisticsOverview:_t,SvgCircleQuestionMark:Te},data(){return{initialTableFilter:"",strings:{keywordPositionsCard:a("Keyword Positions",be),keywordPositionsTooltip:a("This graph is a visual representation of how well <strong>keywords are ranking in search results over time</strong> based on their position and average CTR. This can help you understand the performance of keywords and identify any trends or fluctuations.",be),keywordPerformanceCard:a("Keyword Performance",be),keywordPerformanceTooltip:a("This table displays the performance of keywords that your site ranks for over time, including metrics such as impressions, click-through rate, and average position in search results. It allows for easy analysis of how keywords are performing and identification of any underperforming keywords that may need to be optimized or replaced.",be)},defaultKeywords:{rows:[],totals:{page:0,pages:0,total:0}}}},beforeMount(){var t,s,m;const l={TopLosingKeywords:"topLosing",TopWinningKeywords:"topWinning"},o=new URLSearchParams(((t=window.location)==null?void 0:t.search)||"")||{};if(o.has("table-filter")||(m=(s=this.$route)==null?void 0:s.query)!=null&&m["table-filter"]){const c=o.get("table-filter")||this.$route.query["table-filter"]||"all";this.initialTableFilter=l[c],this.$route.query["table-filter"]=void 0,Ke("table-filter")}},mounted(){this.searchStatisticsStore.isConnected&&this.searchStatisticsStore.loadInitialData()}},Fo={class:"aioseo-search-statistics-keywords"},Oo=["innerHTML"],Uo=["innerHTML"];function Vo(l,o,t,s,m,c){const n=R("svg-circle-question-mark"),A=R("core-tooltip"),d=R("seo-statistics-overview"),p=R("keywords-graph"),y=R("grid-column"),f=R("keywords-distribution-graph"),k=R("grid-row"),b=R("core-settings-row"),w=R("keywords-table");return h(),I("div",Fo,[g(b,{name:m.strings.chooseYourKeywords,"left-size":"12","right-size":"12",class:"aioseo-settings-row--positions"},{name:r(()=>[B(u(m.strings.keywordPositionsCard)+" ",1),g(A,null,{tooltip:r(()=>[i("span",{innerHTML:m.strings.keywordPositionsTooltip},null,8,Oo)]),default:r(()=>[g(n)]),_:1})]),content:r(()=>[g(d,{statistics:["keywords","impressions","position"],"show-graph":!1,view:"side-by-side"}),g(k,null,{default:r(()=>[g(y,{md:"6"},{default:r(()=>[g(p,{"legend-style":"simple"})]),_:1}),g(y,{md:"6"},{default:r(()=>[g(f)]),_:1})]),_:1})]),_:1},8,["name"]),g(b,{name:m.strings.keywordPerformanceCard,"left-size":"12","right-size":"12","no-vertical-margin":"","no-border":"",class:"aioseo-settings-row--performance"},{name:r(()=>[B(u(m.strings.keywordPerformanceCard)+" ",1),g(A,null,{tooltip:r(()=>[i("span",{innerHTML:m.strings.keywordPerformanceTooltip},null,8,Uo)]),default:r(()=>[g(n)]),_:1})]),content:r(()=>{var v,C;return[g(w,{keywords:((C=(v=s.searchStatisticsStore.data)==null?void 0:v.keywords)==null?void 0:C.paginated)||m.defaultKeywords,loading:s.searchStatisticsStore.loading.keywords,columns:["keywordSortable","clicksSortable","ctrSortable","impressionsSortable","positionSortable","buttons"],"append-columns":{all:"diffPosition",topLosing:"diffDecay",topWinning:"diffDecay"},initialFilter:m.initialTableFilter,"show-items-per-page":"","show-table-footer":""},null,8,["keywords","loading","initialFilter"])]}),_:1},8,["name"])])}const Lo=N(Go,[["render",Vo]]),qo={},zo={width:"434",height:"311",fill:"none",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"},No=ct('<circle cx="211.536" cy="127.22" r="125.363" fill="url(#a)"></circle><rect x="37" y="1.25" width="217.943" height="25.497" rx="12.749" fill="#fff"></rect><rect x="37" y="1.25" width="217.943" height="25.497" rx="12.749" stroke="#E0E0E0" stroke-width=".607"></rect><path d="M48.316 16.813c.795 0 1.537-.259 2.14-.69l2.271 2.27a.543.543 0 0 0 .388.159.53.53 0 0 0 .537-.546.527.527 0 0 0-.153-.383l-2.257-2.261A3.67 3.67 0 0 0 52 13.129a3.694 3.694 0 0 0-3.684-3.683 3.691 3.691 0 0 0-3.683 3.683 3.691 3.691 0 0 0 3.684 3.684Zm0-.795a2.904 2.904 0 0 1-2.889-2.889 2.904 2.904 0 0 1 2.889-2.888 2.907 2.907 0 0 1 2.888 2.888 2.907 2.907 0 0 1-2.889 2.889Z" fill="#8E8E93"></path><path d="M71.709 15.886V8.933h.92v3.448l3.452-3.448h1.248l-2.917 2.818 3.045 4.135h-1.214l-2.476-3.519-1.138 1.11v2.41h-.92Zm9.855-1.622.882.11c-.139.515-.397.915-.773 1.2-.376.284-.857.426-1.442.426-.736 0-1.321-.226-1.754-.678-.43-.455-.645-1.092-.645-1.911 0-.848.218-1.505.654-1.973.436-.468 1.002-.702 1.698-.702.673 0 1.224.229 1.65.687.427.459.64 1.104.64 1.935 0 .051 0 .127-.004.228h-3.756c.031.553.188.977.47 1.271.28.294.632.441 1.052.441.313 0 .58-.082.802-.246.221-.165.397-.427.526-.788Zm-2.803-1.38h2.813c-.038-.424-.146-.741-.323-.953a1.308 1.308 0 0 0-1.058-.493c-.392 0-.722.13-.99.393-.266.263-.413.614-.442 1.053Zm4.724 4.942-.095-.801c.187.05.35.076.489.076.19 0 .341-.032.455-.095a.775.775 0 0 0 .28-.266c.053-.085.14-.297.26-.635.016-.048.042-.117.077-.21l-1.912-5.046h.92l1.048 2.917c.136.37.258.76.366 1.167.098-.392.215-.775.35-1.148L86.8 10.85h.854l-1.916 5.123c-.206.553-.365.934-.48 1.143-.151.281-.325.487-.52.616-.197.133-.431.2-.703.2-.164 0-.348-.035-.55-.105Zm5.824-1.94-1.541-5.037h.882l.801 2.908.3 1.081c.012-.054.099-.4.26-1.039l.802-2.95h.877l.754 2.922.251.963.29-.973.863-2.912h.83l-1.575 5.037h-.886l-.802-3.016-.195-.859-1.02 3.875h-.89Zm5.772-2.518c0-.933.26-1.624.778-2.073.433-.373.961-.56 1.584-.56.693 0 1.258.228 1.698.684.44.452.66 1.078.66 1.878 0 .648-.099 1.159-.295 1.532-.193.37-.476.657-.849.863-.37.205-.774.308-1.214.308-.705 0-1.276-.226-1.712-.678-.433-.452-.65-1.104-.65-1.954Zm.878 0c0 .645.14 1.129.422 1.451.281.32.635.48 1.062.48.424 0 .776-.162 1.058-.485.281-.322.422-.814.422-1.475 0-.623-.142-1.094-.427-1.413a1.34 1.34 0 0 0-1.053-.484c-.427 0-.78.16-1.062.48-.282.319-.422.8-.422 1.446Zm4.837 2.518V10.85h.769v.764c.196-.357.376-.593.54-.707a.96.96 0 0 1 .55-.17c.288 0 .581.091.878.275l-.294.792a1.224 1.224 0 0 0-.626-.185.806.806 0 0 0-.503.17.901.901 0 0 0-.318.465 3.32 3.32 0 0 0-.142.996v2.637h-.854Zm6.512 0v-.635c-.319.5-.789.75-1.408.75-.402 0-.772-.112-1.11-.333a2.213 2.213 0 0 1-.783-.925c-.183-.398-.275-.855-.275-1.37 0-.503.084-.958.251-1.366a1.98 1.98 0 0 1 .755-.944 2.014 2.014 0 0 1 1.124-.327c.303 0 .574.064.811.**************.293.578.498V8.933h.849v6.953h-.792Zm-2.698-2.513c0 .645.135 1.127.407 1.446.272.32.593.48.963.48.373 0 .69-.153.949-.456.262-.307.393-.773.393-1.4 0-.689-.132-1.194-.398-1.517-.266-.322-.593-.484-.982-.484-.379 0-.697.155-.953.465-.253.31-.379.798-.379 1.466Z" fill="#333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="m244.331 14.037 3.449 3.426-.855.862-3.456-3.432-3.455 3.432-.856-.862 3.45-3.426-3.45-3.426.856-.862 3.455 3.432 3.456-3.432.855.862-3.449 3.426Z" fill="#828282"></path><g filter="url(#b)"><path d="M37 41.984c0-3.42 0-5.132.871-6.33a4.563 4.563 0 0 1 1.01-1.01c1.199-.871 2.91-.871 6.33-.871H267.98c3.421 0 5.132 0 6.331.87a4.56 4.56 0 0 1 1.009 1.01c.872 1.2.872 2.91.872 6.331V179.16c0 3.343 0 5.014-.852 6.186a4.455 4.455 0 0 1-.986.986c-1.172.851-2.843.851-6.186.851H43.497c-2.707 0-4.06 0-5.01-.689a3.602 3.602 0 0 1-.798-.798c-.689-.949-.689-2.302-.689-5.009V41.984Z" fill="#fff"></path></g><path d="M45.998 82.613h.566v2.09l2.09-2.09h.803l-1.784 1.729 1.834 2.56h-.756l-1.5-2.152-.687.657v1.495h-.566v-4.289Zm5.22 1.092c.222 0 .437.053.645.158.209.103.367.237.476.403.105.157.175.341.21.551.032.145.047.374.047.69h-2.292c.01.317.085.572.225.765.14.19.357.286.651.286.275 0 .494-.09.657-.272a.915.915 0 0 0 .199-.365h.516c-.013.115-.059.243-.137.386-.076.14-.161.255-.257.344-.16.156-.357.261-.592.316-.127.03-.27.046-.43.046-.389 0-.719-.14-.99-.423-.27-.284-.405-.681-.405-1.191 0-.503.136-.91.408-1.224.273-.313.63-.47 1.07-.47Zm.838 1.384a1.362 1.362 0 0 0-.149-.546c-.144-.253-.384-.38-.721-.38a.797.797 0 0 0-.607.263.981.981 0 0 0-.26.663h1.737Zm3.142-1.314h.58c-.073.2-.238.658-.493 1.372-.19.538-.35.976-.478 1.314-.304.799-.518 1.285-.643 1.46-.124.175-.339.263-.642.263-.074 0-.132-.003-.172-.009a1.364 1.364 0 0 1-.146-.032v-.479c.091.026.157.041.198.047a.78.78 0 0 0 .108.009c.098 0 .169-.017.213-.05a.419.419 0 0 0 .117-.117 15.417 15.417 0 0 0 .236-.56l-1.156-3.218h.596l.838 2.546.844-2.546Zm1.279 0 .601 2.465.61-2.465h.59l.613 2.45.64-2.45h.525l-.908 3.127h-.546l-.636-2.42-.616 2.42h-.546l-.903-3.127h.576Zm5.32 2.789c.348 0 .586-.132.715-.395.13-.264.196-.558.196-.881 0-.292-.047-.53-.14-.713-.149-.288-.404-.432-.766-.432-.32 0-.554.123-.7.368-.146.245-.22.541-.22.888 0 .332.074.61.22.832.146.222.377.333.695.333Zm.02-2.88c.403 0 .744.135 1.022.404.278.268.418.663.418 1.185 0 .504-.123.92-.368 1.25-.246.329-.626.493-1.142.493-.43 0-.772-.145-1.025-.435-.253-.292-.38-.683-.38-1.174 0-.525.134-.944.4-1.255.267-.312.625-.467 1.075-.467Zm2.082.091h.5v.54c.04-.105.14-.232.3-.382a.778.778 0 0 1 .601-.225l.12.012v.555a.752.752 0 0 0-.172-.015c-.265 0-.468.085-.61.257a.886.886 0 0 0-.214.587v1.798H63.9v-3.127Zm2.31 1.6c0 .335.07.615.212.841.143.226.37.339.684.339a.717.717 0 0 0 .598-.313c.158-.21.237-.51.237-.902 0-.395-.08-.687-.242-.876a.755.755 0 0 0-.6-.286.794.794 0 0 0-.644.304c-.164.202-.246.5-.246.893Zm.79-1.655c.24 0 .44.05.602.151.094.059.2.161.318.307v-1.58h.505v4.304h-.473v-.435a1.137 1.137 0 0 1-.434.418c-.168.085-.36.128-.576.128-.348 0-.65-.146-.905-.438-.255-.294-.382-.684-.382-1.17 0-.456.115-.85.347-1.183.234-.335.566-.502.999-.502ZM139.993 82.613h.832l1.232 3.627 1.223-3.627h.824v4.29h-.552V84.37c0-.088.002-.233.006-.435.004-.203.006-.42.006-.651l-1.224 3.617h-.575l-1.232-3.617v.131c0 .105.002.266.006.482.005.214.008.372.008.473v2.531h-.554v-4.289Zm6.166 3.95c.349 0 .587-.13.716-.394.13-.264.195-.558.195-.881 0-.292-.046-.53-.14-.713-.148-.288-.403-.432-.765-.432-.321 0-.555.123-.701.368-.146.245-.219.541-.219.888 0 .332.073.61.219.832.146.222.378.333.695.333Zm.021-2.878c.403 0 .743.134 1.022.403.278.268.417.663.417 1.185 0 .504-.122.92-.368 1.25-.245.329-.626.493-1.141.493-.431 0-.772-.145-1.025-.435-.253-.292-.38-.683-.38-1.174 0-.525.134-.944.4-1.255.267-.312.625-.467 1.075-.467Zm2.067.09h.499v.444c.148-.183.305-.314.47-.394.166-.08.35-.12.552-.12.444 0 .744.155.899.464.086.17.129.412.129.727v2.006h-.534v-1.97c0-.191-.029-.345-.085-.462-.093-.195-.263-.292-.508-.292-.125 0-.227.013-.307.038a.788.788 0 0 0-.379.257.794.794 0 0 0-.173.321 2.243 2.243 0 0 0-.037.47v1.638h-.526v-3.127Zm3.434-.873h.531v.873h.499v.43h-.499v2.04c0 .11.037.182.111.22a.471.471 0 0 0 .204.031h.082a3.04 3.04 0 0 0 .102-.008v.414a1.15 1.15 0 0 1-.189.038 1.772 1.772 0 0 1-.211.012c-.245 0-.411-.062-.499-.187a.841.841 0 0 1-.131-.49v-2.07h-.424v-.43h.424v-.873Zm1.559-.303h.525v1.6c.125-.158.237-.27.336-.333.17-.111.381-.167.634-.167.453 0 .761.159.922.476.088.173.132.414.132.721v2.006h-.54v-1.97c0-.23-.03-.399-.088-.506-.095-.171-.274-.257-.537-.257a.895.895 0 0 0-.593.225c-.177.15-.266.433-.266.85v1.658h-.525V82.6Zm3.343.014h.526v4.29h-.526v-4.29Zm3.27 1.162h.581c-.074.2-.238.658-.493 1.372-.191.538-.351.976-.479 1.314-.304.799-.518 1.285-.642 1.46-.125.175-.339.263-.643.263a1.28 1.28 0 0 1-.172-.009 1.343 1.343 0 0 1-.146-.032v-.479c.091.026.158.041.198.047a.784.784 0 0 0 .109.009c.097 0 .168-.017.213-.05a.423.423 0 0 0 .116-.117c.01-.015.045-.095.106-.239.06-.144.104-.251.131-.321l-1.156-3.218h.595l.838 2.546.844-2.546Zm3.151 1.743c.013.244.071.441.172.593.193.284.532.426 1.019.426.218 0 .416-.03.596-.093.346-.12.519-.337.519-.648 0-.234-.073-.4-.219-.5-.148-.097-.379-.182-.695-.254l-.581-.131c-.379-.086-.648-.18-.806-.283-.272-.18-.408-.447-.408-.803 0-.386.133-.702.4-.95.266-.246.644-.37 1.133-.37.449 0 .831.11 1.144.327.316.216.473.563.473 1.04h-.546c-.029-.23-.091-.406-.187-.529-.177-.224-.478-.336-.902-.336-.342 0-.589.072-.739.216a.675.675 0 0 0-.224.502c0 .21.087.365.262.462.115.062.375.14.78.233l.601.138c.29.066.514.156.672.271.272.2.409.492.409.873 0 .475-.174.815-.52 1.02a2.316 2.316 0 0 1-1.203.306c-.533 0-.951-.136-1.253-.409-.301-.27-.449-.637-.443-1.1h.546Zm4.841-1.813c.222 0 .437.053.645.158.208.103.367.237.476.403.105.157.175.341.21.551.031.145.047.374.047.69h-2.292c.01.317.084.572.225.765.14.19.357.286.651.286.274 0 .493-.09.657-.272a.92.92 0 0 0 .198-.365h.517a1.091 1.091 0 0 1-.137.386 1.36 1.36 0 0 1-.257.344c-.16.156-.357.261-.593.316a1.79 1.79 0 0 1-.429.046 1.32 1.32 0 0 1-.99-.423c-.27-.284-.406-.681-.406-1.191 0-.503.137-.91.409-1.224.273-.313.629-.47 1.069-.47Zm.838 1.384a1.368 1.368 0 0 0-.149-.546c-.144-.253-.385-.38-.721-.38a.799.799 0 0 0-.608.263.985.985 0 0 0-.26.663h1.738Zm1.591.981c0 .152.055.272.166.36a.62.62 0 0 0 .395.13 1.2 1.2 0 0 0 .537-.128.723.723 0 0 0 .438-.698v-.423a.898.898 0 0 1-.248.102 2.16 2.16 0 0 1-.298.059l-.318.04a1.225 1.225 0 0 0-.43.12c-.161.092-.242.238-.242.438Zm1.273-1.063c.121-.015.201-.066.242-.151a.453.453 0 0 0 .035-.202c0-.18-.064-.308-.192-.388-.127-.082-.309-.123-.546-.123-.275 0-.469.074-.584.222-.065.082-.106.203-.126.365h-.49c.009-.386.134-.653.373-.803.242-.152.521-.228.838-.228.368 0 .667.07.897.21.227.14.341.359.341.654v1.802c0 .055.011.098.032.131.024.034.072.05.144.05.023 0 .049 0 .078-.003l.094-.015v.389a1.489 1.489 0 0 1-.187.044 1.373 1.373 0 0 1-.175.008c-.181 0-.313-.064-.394-.192a.748.748 0 0 1-.091-.29 1.34 1.34 0 0 1-.461.366 1.43 1.43 0 0 1-.663.154c-.29 0-.528-.087-.712-.262a.885.885 0 0 1-.275-.663c0-.29.091-.515.272-.675.181-.16.418-.258.712-.295l.838-.105Zm1.667-1.232h.5v.54c.04-.105.141-.232.3-.382a.779.779 0 0 1 .602-.225l.119.012v.555a.748.748 0 0 0-.172-.015c-.265 0-.468.085-.61.257a.887.887 0 0 0-.213.587v1.798h-.526v-3.127Zm3.183-.09c.352 0 .638.085.858.257.222.17.355.466.4.884h-.511a.933.933 0 0 0-.213-.478c-.111-.129-.289-.193-.534-.193-.335 0-.574.163-.718.49a1.938 1.938 0 0 0-.141.786c0 .313.067.577.199.791.132.214.341.321.625.321.218 0 .39-.066.517-.198a1.1 1.1 0 0 0 .265-.55h.511c-.058.415-.204.719-.438.912-.233.19-.532.286-.896.286-.409 0-.735-.15-.978-.447-.244-.3-.365-.673-.365-1.121 0-.55.133-.976.4-1.282a1.291 1.291 0 0 1 1.019-.458Zm1.784-1.087h.525v1.6c.125-.157.237-.268.336-.332.169-.111.381-.167.634-.167.453 0 .761.159.922.476.088.173.132.414.132.721v2.006h-.54v-1.97c0-.23-.03-.399-.088-.506-.095-.171-.274-.257-.537-.257a.895.895 0 0 0-.593.225c-.177.15-.266.433-.266.85v1.658h-.525V82.6Zm4.631 1.107c.222 0 .437.053.645.158.208.103.367.237.476.403.105.157.175.341.21.551.031.145.047.374.047.69h-2.292c.009.317.084.572.225.765.14.19.357.286.651.286.274 0 .493-.09.657-.272a.92.92 0 0 0 .198-.365h.517a1.091 1.091 0 0 1-.137.386 1.36 1.36 0 0 1-.257.344c-.16.156-.357.261-.593.316-.126.03-.27.046-.429.046a1.32 1.32 0 0 1-.99-.423c-.271-.284-.406-.681-.406-1.191 0-.503.136-.91.409-1.224.272-.313.629-.47 1.069-.47Zm.838 1.384a1.368 1.368 0 0 0-.149-.546c-.144-.253-.385-.38-.721-.38a.799.799 0 0 0-.608.263.985.985 0 0 0-.26.663h1.738Zm1.5.832c.016.175.06.31.132.403.132.17.362.254.689.254.195 0 .366-.042.514-.125a.425.425 0 0 0 .222-.395.337.337 0 0 0-.178-.306 2.12 2.12 0 0 0-.45-.149l-.418-.105c-.266-.066-.463-.14-.589-.222-.226-.142-.339-.339-.339-.59 0-.296.106-.535.318-.718.214-.183.501-.275.861-.275.472 0 .811.139 1.019.415a.896.896 0 0 1 .19.566h-.496a.61.61 0 0 0-.126-.324c-.12-.138-.33-.207-.627-.207-.199 0-.35.038-.453.114a.359.359 0 0 0-.152.3c0 .137.067.246.202.328.078.048.192.091.344.128l.348.085c.377.091.63.18.759.266.204.134.306.345.306.633a.964.964 0 0 1-.318.721c-.21.203-.531.304-.963.304-.466 0-.796-.105-.99-.315a1.209 1.209 0 0 1-.31-.786h.505ZM207.631 82.496c.543 0 .965.143 1.264.43.3.286.466.61.5.975h-.567a1.177 1.177 0 0 0-.385-.657c-.191-.162-.46-.243-.806-.243-.423 0-.764.15-1.025.447-.259.296-.388.75-.388 1.364 0 .502.116.91.35 1.223.236.312.586.467 1.051.467.428 0 .754-.164.978-.493.119-.173.208-.401.266-.683h.566c-.05.451-.218.83-.502 1.135-.34.368-.8.552-1.378.552-.498 0-.917-.15-1.255-.452-.446-.4-.669-1.016-.669-1.849 0-.632.167-1.15.502-1.556.362-.44.861-.66 1.498-.66Zm3.685 4.068c.348 0 .587-.132.715-.395a1.96 1.96 0 0 0 .196-.881c0-.292-.047-.53-.14-.713-.148-.288-.403-.432-.765-.432-.322 0-.555.123-.701.368-.146.245-.219.541-.219.888 0 .332.073.61.219.832.146.222.378.333.695.333Zm.02-2.88c.403 0 .744.135 1.022.404.279.268.418.663.418 1.185 0 .504-.123.92-.368 1.25-.245.329-.626.493-1.142.493-.43 0-.772-.145-1.025-.435-.253-.292-.379-.683-.379-1.174 0-.525.133-.944.4-1.255.266-.312.625-.467 1.074-.467Zm2.067.091h.52v.444c.125-.154.238-.266.339-.336.173-.118.37-.178.59-.178.249 0 .449.061.601.184.086.07.164.173.234.31.117-.168.254-.291.411-.371a1.14 1.14 0 0 1 .532-.123c.42 0 .706.152.858.456.082.163.123.383.123.66v2.081h-.546V84.73c0-.208-.053-.351-.158-.43a.61.61 0 0 0-.379-.116.762.762 0 0 0-.535.207c-.148.138-.222.37-.222.692v1.82h-.534V84.86c0-.212-.025-.367-.076-.464-.08-.146-.229-.219-.447-.219a.777.777 0 0 0-.543.23c-.161.154-.242.433-.242.836v1.658h-.526v-3.127Zm6.301 2.774a.748.748 0 0 0 .611-.307c.163-.206.245-.514.245-.922 0-.25-.036-.463-.108-.643-.136-.344-.386-.516-.748-.516-.364 0-.613.182-.747.546a2.164 2.164 0 0 0-.108.741c0 .242.036.447.108.616.136.323.385.485.747.485Zm-1.36-2.76h.511v.415c.105-.142.22-.252.344-.33a1.11 1.11 0 0 1 .625-.175c.354 0 .655.136.902.409.248.27.371.658.371 1.162 0 .681-.178 1.168-.534 1.46a1.209 1.209 0 0 1-.789.277c-.235 0-.433-.051-.592-.154a1.358 1.358 0 0 1-.313-.301v1.597h-.525v-4.36Zm4.671-.084c.222 0 .437.053.646.158.208.103.367.237.476.403.105.157.175.341.21.551.031.145.047.374.047.69h-2.292c.009.317.084.572.224.765.141.19.358.286.652.286.274 0 .493-.09.656-.272a.91.91 0 0 0 .199-.365h.517c-.014.115-.06.243-.137.386a1.36 1.36 0 0 1-.257.344 1.23 1.23 0 0 1-.593.316c-.127.03-.27.046-.429.046-.39 0-.72-.14-.99-.423-.271-.284-.406-.681-.406-1.191 0-.503.136-.91.409-1.224.272-.313.629-.47 1.068-.47Zm.838 1.384a1.37 1.37 0 0 0-.148-.546c-.144-.253-.385-.38-.722-.38a.798.798 0 0 0-.607.263.985.985 0 0 0-.26.663h1.737Zm1.294-2.187h.531v.873h.5v.43h-.5v2.04c0 .11.037.182.111.22a.474.474 0 0 0 .205.031h.081a2.87 2.87 0 0 0 .103-.008v.414c-.061.018-.124.03-.19.038a1.743 1.743 0 0 1-.21.012c-.246 0-.412-.062-.5-.187a.849.849 0 0 1-.131-.49v-2.07h-.423v-.43h.423v-.873Zm1.559.888h.534v3.112h-.534V83.79Zm0-1.177h.534v.596h-.534v-.596Zm1.437.29h.531v.872h.499v.43h-.499v2.04c0 .11.037.182.111.22a.471.471 0 0 0 .204.031h.082a3.04 3.04 0 0 0 .102-.008v.414a1.15 1.15 0 0 1-.189.038 1.772 1.772 0 0 1-.211.012c-.245 0-.411-.062-.499-.187a.841.841 0 0 1-.131-.49v-2.07h-.424v-.43h.424v-.873Zm1.559.887h.534v3.112h-.534V83.79Zm0-1.177h.534v.596h-.534v-.596Zm2.572 3.95c.349 0 .587-.13.716-.394.13-.264.195-.558.195-.881 0-.292-.047-.53-.14-.713-.148-.288-.403-.432-.765-.432-.321 0-.555.123-.701.368-.146.245-.219.541-.219.888 0 .332.073.61.219.832.146.222.378.333.695.333Zm.021-2.878c.403 0 .743.134 1.022.403.278.268.417.663.417 1.185 0 .504-.123.92-.368 1.25-.245.329-.626.493-1.141.493-.431 0-.772-.145-1.025-.435-.253-.292-.38-.683-.38-1.174 0-.525.133-.944.4-1.255.267-.312.625-.467 1.075-.467Zm2.067.09h.499v.444c.148-.183.305-.314.47-.394.166-.08.35-.12.552-.12.444 0 .744.155.899.464.086.17.129.412.129.727v2.006h-.534v-1.97c0-.191-.029-.345-.085-.462-.094-.195-.263-.292-.508-.292-.125 0-.227.013-.307.038a.788.788 0 0 0-.379.257.794.794 0 0 0-.173.321 2.23 2.23 0 0 0-.038.47v1.638h-.525v-3.127ZM256.644 82.496c.543 0 .964.143 1.264.43.3.286.466.61.499.975h-.566a1.173 1.173 0 0 0-.386-.657c-.19-.162-.459-.243-.805-.243-.423 0-.764.15-1.025.447-.259.296-.389.75-.389 1.364 0 .502.117.91.351 1.223.235.312.586.467 1.051.467.428 0 .754-.164.978-.493.119-.173.207-.401.266-.683h.566c-.05.451-.218.83-.502 1.135-.341.368-.8.552-1.378.552-.499 0-.917-.15-1.256-.452-.445-.4-.668-1.016-.668-1.849 0-.632.167-1.15.502-1.556.362-.44.861-.66 1.498-.66Zm2.569.117h1.93c.382 0 .689.108.923.324.233.214.35.516.35.905 0 .335-.104.627-.312.876-.209.248-.529.371-.961.371h-1.349v1.813h-.581v-4.289Zm2.616 1.232c0-.315-.117-.53-.35-.642-.129-.06-.305-.09-.529-.09h-1.156v1.486h1.156c.261 0 .472-.056.634-.167.164-.11.245-.306.245-.587Zm3.124-1.349c.544 0 .965.143 1.265.43.3.286.466.61.499.975h-.566a1.178 1.178 0 0 0-.386-.657c-.191-.162-.459-.243-.806-.243-.422 0-.764.15-1.025.447-.258.296-.388.75-.388 1.364 0 .502.117.91.351 1.223.235.312.585.467 1.051.467.428 0 .754-.164.978-.493.119-.173.207-.401.265-.683h.567c-.051.451-.218.83-.502 1.135-.341.368-.8.552-1.378.552-.499 0-.917-.15-1.256-.452-.446-.4-.669-1.016-.669-1.849 0-.632.168-1.15.503-1.556.362-.44.861-.66 1.497-.66Z" fill="#8C8F9A"></path><path d="M37 37.19a3.417 3.417 0 0 1 3.417-3.417h232.357a3.417 3.417 0 0 1 3.417 3.417v19.648H37V37.19Z" fill="#141B38"></path><circle cx="50.176" cy="45.305" r="2.925" fill="#F83C5D"></circle><circle cx="60.725" cy="45.305" r="2.925" fill="#F18200"></circle><circle cx="71.273" cy="45.305" r="2.925" fill="#07C575"></circle><rect x="85.304" y="37.867" width="170.85" height="14.876" rx="7.438" fill="#fff"></rect><g clip-path="url(#c)"><path d="M98.594 43.68h-.406v-.812a2.032 2.032 0 0 0-4.063 0v.812h-.406a.815.815 0 0 0-.813.813v4.062c0 .447.366.813.813.813h4.875a.815.815 0 0 0 .812-.813v-4.062a.815.815 0 0 0-.812-.813Zm-2.438 3.657a.815.815 0 0 1-.812-.813c0-.447.365-.812.812-.812.447 0 .813.365.813.812a.815.815 0 0 1-.813.813Zm1.26-3.657h-2.52v-.812a1.26 1.26 0 0 1 2.52 0v.812Z" fill="#D0D1D7"></path></g><rect x="105.732" y="42.052" width="116.206" height="6.506" rx="3.253" fill="#D0D1D7"></rect><path d="M49.81 68.817h-1.156l-1.328-1.88-.43.44v1.44h-.884v-4.304h.885v1.773l1.661-1.773h1.162l-1.763 1.773 1.854 2.531Zm3.166-.934c-.022.189-.12.38-.295.575-.273.31-.654.464-1.145.464-.404 0-.762-.13-1.071-.39-.31-.262-.465-.686-.465-1.274 0-.55.14-.973.418-1.267.28-.294.643-.441 1.09-.441.264 0 .502.05.714.149.213.1.388.256.526.**************.408.242.657.022.146.03.356.027.63h-2.178c.011.32.111.544.3.672.*************.415.12.171 0 .31-.049.417-.146a.693.693 0 0 0 .155-.22h.85Zm-.824-.975c-.013-.22-.08-.387-.201-.5a.613.613 0 0 0-.444-.172c-.192 0-.342.06-.45.181a.91.91 0 0 0-.198.49h1.293Zm2.722 1.074.657-2.347h.884l-1.092 3.13c-.21.603-.376.977-.499 1.12-.123.147-.368.22-.736.22-.074 0-.133-.001-.178-.003a3.41 3.41 0 0 1-.201-.009v-.666l.105.006c.082.004.16.001.233-.009a.42.42 0 0 0 .187-.067c.049-.033.094-.102.135-.207.042-.105.06-.17.052-.193l-1.168-3.322h.926l.695 2.347Zm4.45.835-.5-2.318-.505 2.318h-.864l-.894-3.182h.894l.496 2.283.459-2.283h.843l.485 2.292.496-2.292h.868l-.923 3.182h-.855Zm4.893-2.78c.269.337.403.735.403 1.195 0 .467-.134.867-.403 1.2-.268.33-.676.496-1.223.496s-.955-.165-1.224-.496c-.268-.333-.403-.733-.403-1.2 0-.46.135-.858.403-1.195.269-.336.677-.505 1.224-.505.547 0 .955.169 1.223.505Zm-1.226.2c-.244 0-.431.086-.564.259-.13.171-.195.416-.195.736 0 .319.065.565.195.739.133.173.32.26.564.26.243 0 .43-.087.56-.26.13-.174.196-.42.196-.74 0-.319-.065-.564-.196-.735-.13-.173-.317-.26-.56-.26Zm3.828.166c-.335 0-.56.108-.675.326-.064.123-.096.312-.096.567v1.521h-.838v-3.182h.794v.554c.128-.212.24-.357.336-.435.156-.13.358-.195.607-.195.016 0 .028 0 .038.003l.073.002v.853a2.336 2.336 0 0 0-.24-.014Zm3.626-1.884v4.298h-.809v-.44a1.173 1.173 0 0 1-.406.411 1.14 1.14 0 0 1-.566.129c-.372 0-.685-.15-.94-.45-.253-.302-.38-.688-.38-1.16 0-.542.125-.97.374-1.281.251-.312.586-.467 1.004-.467a1.017 1.017 0 0 1 .88.478V64.52h.843Zm-2.242 2.727c0 .294.058.529.175.704.115.177.29.266.525.266.236 0 .415-.088.538-.263.122-.175.184-.402.184-.68 0-.39-.099-.668-.295-.835a.635.635 0 0 0-.42-.152c-.244 0-.423.092-.538.277-.113.183-.17.41-.17.683Zm6.295.95c.212 0 .384-.024.516-.07.252-.09.377-.257.377-.5a.368.368 0 0 0-.187-.33c-.124-.076-.32-.143-.587-.202l-.455-.102c-.448-.1-.757-.21-.929-.33-.29-.198-.435-.509-.435-.931 0-.385.14-.706.42-.96.281-.256.693-.383 1.236-.383.454 0 .84.12 1.16.362.32.24.489.588.504 1.045h-.864c-.016-.259-.129-.443-.339-.552a1.135 1.135 0 0 0-.522-.108c-.232 0-.417.047-.555.14a.446.446 0 0 0-.207.392.37.37 0 0 0 .204.344c.088.05.274.11.56.178l.742.179c.325.077.57.181.736.312.257.202.385.495.385.879 0 .393-.15.72-.452.98-.3.26-.724.39-1.273.39-.56 0-1.002-.128-1.323-.383-.321-.257-.482-.61-.482-1.057h.859c.027.196.08.343.16.44.146.178.396.266.75.266Zm5.083-.313c-.021.189-.12.38-.295.575-.272.31-.654.464-1.144.464-.405 0-.762-.13-1.072-.39-.31-.262-.464-.686-.464-1.274 0-.55.139-.973.417-1.267.28-.294.644-.441 1.09-.441.264 0 .503.05.715.149.212.1.387.256.525.47.125.189.206.408.243.657.021.146.03.356.026.63h-2.178c.011.32.112.544.3.672.115.08.254.12.415.12.171 0 .31-.049.418-.146a.691.691 0 0 0 .154-.22h.85Zm-.823-.975c-.014-.22-.081-.387-.202-.5a.613.613 0 0 0-.444-.172c-.192 0-.342.06-.45.181a.91.91 0 0 0-.198.49h1.294Zm1.547-.929c.22-.28.598-.42 1.133-.42.349 0 .658.069.929.207.27.138.405.399.405.782v1.46c0 .102.002.224.006.368.006.11.023.183.05.222a.3.3 0 0 0 .123.097v.122h-.906a.925.925 0 0 1-.052-.18 2.145 2.145 0 0 1-.023-.194 1.71 1.71 0 0 1-.398.319c-.179.103-.381.154-.607.154-.288 0-.526-.081-.715-.245-.187-.165-.28-.399-.28-.7 0-.392.15-.675.452-.85.166-.096.409-.164.73-.205l.283-.035a1.3 1.3 0 0 0 .33-.073c.119-.05.178-.13.178-.236 0-.13-.046-.22-.137-.269-.09-.05-.222-.076-.397-.076-.197 0-.336.049-.418.146a.606.606 0 0 0-.116.292h-.803c.017-.278.095-.507.233-.686Zm.62 2.237a.435.435 0 0 0 .285.096.841.841 0 0 0 .494-.158c.152-.105.23-.296.236-.575v-.31a.965.965 0 0 1-.16.082c-.053.02-.126.038-.22.056l-.186.035c-.175.031-.3.07-.377.114a.382.382 0 0 0-.192.353c0 .142.04.244.12.307Zm4.19-1.813c-.336 0-.56.108-.675.326-.065.123-.097.312-.097.567v1.521h-.838v-3.182h.794v.554c.129-.212.24-.357.336-.435.156-.13.358-.195.608-.195.015 0 .028 0 .038.003l.073.002v.853a2.336 2.336 0 0 0-.24-.014Zm3.483.373h-.853a.74.74 0 0 0-.12-.32c-.093-.13-.238-.194-.435-.194-.28 0-.472.14-.575.418a1.746 1.746 0 0 0-.082.59c0 .233.027.421.082.563.1.265.286.397.56.397.195 0 .333-.052.415-.157a.784.784 0 0 0 .149-.41h.85c-.02.254-.111.493-.275.72-.26.363-.647.545-1.159.545s-.889-.152-1.13-.455c-.241-.304-.362-.698-.362-1.183 0-.547.133-.972.4-1.276.267-.303.635-.455 1.104-.455.399 0 .725.09.978.268.255.18.406.496.453.95Zm3.45.172v1.87H91.2V66.88c0-.17-.03-.31-.088-.414-.076-.148-.22-.222-.432-.222-.22 0-.387.074-.502.222-.113.146-.17.355-.17.627v1.723h-.829v-4.289h.83v1.521c.12-.185.26-.313.417-.385.16-.074.327-.111.502-.111.197 0 .375.034.535.102a.936.936 0 0 1 .557.68c.02.125.03.33.03.614Z" fill="#141B38"></path><rect x="45.542" y="96.56" width="75.601" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="139.51" y="96.56" width="39.296" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="205.287" y="96.56" width="23.492" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="254.406" y="96.56" width="12.814" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="45.542" y="110.655" width="54.672" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="139.51" y="110.655" width="35.024" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="205.287" y="110.655" width="26.482" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="254.406" y="110.655" width="10.678" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="45.542" y="124.75" width="72.611" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="139.51" y="124.75" width="36.306" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="205.287" y="124.75" width="29.472" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="254.406" y="124.75" width="12.814" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="45.542" y="138.846" width="64.923" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="139.51" y="138.846" width="18.794" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="205.287" y="138.846" width="20.929" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="254.406" y="138.846" width="12.814" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="45.542" y="152.941" width="31.963" height="5.126" rx="2.563" fill="#005AE0"></rect><rect x="139.51" y="152.941" width="27.763" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="205.287" y="152.941" width="26.055" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="254.406" y="152.941" width="10.678" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="45.542" y="167.036" width="70.476" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="139.51" y="167.036" width="28.19" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="205.287" y="167.036" width="14.522" height="5.126" rx="2.563" fill="#D0D1D7"></rect><rect x="254.406" y="167.036" width="10.251" height="5.126" rx="2.563" fill="#D0D1D7"></rect><g filter="url(#d)"><path d="M157.81 115.817c0-3.422 0-5.132.871-6.332a4.577 4.577 0 0 1 1.009-1.009c1.199-.871 2.91-.871 6.331-.871h222.767c3.422 0 5.132 0 6.332.871.387.282.727.622 1.009 1.009.871 1.2.871 2.91.871 6.332v137.175c0 3.343 0 5.015-.851 6.186a4.455 4.455 0 0 1-.986.986c-1.172.852-2.843.852-6.186.852H164.306c-2.707 0-4.06 0-5.009-.69a3.596 3.596 0 0 1-.798-.798c-.689-.949-.689-2.302-.689-5.008V115.817Z" fill="#fff"></path></g><g clip-path="url(#e)"><path d="M168.13 136.744h67.165v23.444H168.13v-23.444Z" fill="url(#f)"></path><path d="M227.058 138.329h158.407v20.276H227.058v-20.276Z" fill="url(#g)"></path><path d="M171.267 174.09h.889v-1.893h1.338c1.057 0 1.658-.73 1.658-1.589s-.593-1.589-1.658-1.589h-2.227v5.071Zm2.973-3.482c0 .487-.365.806-.867.806h-1.217v-1.612h1.217c.502 0 .867.319.867.806ZM177.964 174.09h.799v-2.433c0-.988-.715-1.33-1.529-1.33-.585 0-1.117.19-1.543.593l.327.555c.319-.312.677-.464 1.087-.464.51 0 .859.258.859.684v.548c-.266-.312-.692-.472-1.186-.472-.6 0-1.277.35-1.277 1.194 0 .814.677 1.217 1.277 1.217.487 0 .913-.175 1.186-.487v.395Zm0-.836c-.182.251-.524.38-.874.38-.449 0-.783-.258-.783-.654 0-.403.334-.661.783-.661.35 0 .692.129.874.38v.555ZM179.752 175.033c.449.411.913.548 1.536.548.905 0 1.886-.358 1.886-1.681v-3.482h-.799v.509a1.46 1.46 0 0 0-1.178-.6c-.943 0-1.627.699-1.627 1.885 0 1.209.692 1.886 1.627 1.886.479 0 .897-.243 1.178-.608v.441c0 .752-.562.996-1.087.996-.487 0-.867-.13-1.163-.472l-.373.578Zm2.623-2.136c-.174.273-.57.494-.942.494-.632 0-1.035-.472-1.035-1.179 0-.707.403-1.178 1.035-1.178.372 0 .768.22.942.494v1.369ZM183.98 172.25c0 1.164.822 1.932 1.924 1.932.586 0 1.125-.183 1.49-.533l-.365-.524c-.258.258-.684.403-1.041.403-.692 0-1.11-.457-1.171-1.012h2.836v-.19c0-1.155-.707-1.999-1.81-1.999-1.087 0-1.863.859-1.863 1.923Zm1.863-1.27c.723 0 1.019.54 1.034.974h-2.068c.038-.449.35-.974 1.034-.974ZM191.337 174.09h.889v-5.071h-.775l-1.46 1.475.517.54.829-.859v3.915ZM196.893 174.182c1.178 0 1.893-.875 1.893-1.932 0-1.049-.715-1.923-1.893-1.923-1.164 0-1.886.874-1.886 1.923 0 1.057.722 1.932 1.886 1.932Zm0-.708c-.677 0-1.057-.57-1.057-1.224 0-.646.38-1.216 1.057-1.216.684 0 1.064.57 1.064 1.216 0 .654-.38 1.224-1.064 1.224ZM199.784 174.09h.798v-2.973h.745v-.699h-.745v-.205c0-.411.198-.616.51-.616a.63.63 0 0 1 .319.076l.175-.586a1.327 1.327 0 0 0-.639-.144c-.676 0-1.163.448-1.163 1.27v.205h-.608v.699h.608v2.973ZM206.129 174.09h.798v-2.433c0-.988-.715-1.33-1.528-1.33-.586 0-1.118.19-1.544.593l.327.555c.32-.312.677-.464 1.088-.464.509 0 .859.258.859.684v.548c-.266-.312-.692-.472-1.186-.472-.601 0-1.278.35-1.278 1.194 0 .814.677 1.217 1.278 1.217.486 0 .912-.175 1.186-.487v.395Zm0-.836c-.183.251-.525.38-.875.38-.448 0-.783-.258-.783-.654 0-.403.335-.661.783-.661.35 0 .692.129.875.38v.555ZM207.962 174.09h.799v-.501c.281.364.699.593 1.178.593.935 0 1.627-.715 1.627-1.924 0-1.186-.684-1.931-1.627-1.931a1.45 1.45 0 0 0-1.178.6v-1.908h-.799v5.071Zm.799-1.102v-1.452c.174-.274.57-.502.942-.502.639 0 1.042.509 1.042 1.224s-.403 1.216-1.042 1.216c-.372 0-.768-.212-.942-.486ZM214.038 174.182c1.178 0 1.893-.875 1.893-1.932 0-1.049-.715-1.923-1.893-1.923-1.163 0-1.886.874-1.886 1.923 0 1.057.723 1.932 1.886 1.932Zm0-.708c-.677 0-1.057-.57-1.057-1.224 0-.646.38-1.216 1.057-1.216.684 0 1.064.57 1.064 1.216 0 .654-.38 1.224-1.064 1.224ZM219.218 174.09h.798v-3.672h-.798v2.577c-.19.251-.54.479-.943.479-.449 0-.738-.174-.738-.745v-2.311h-.798v2.6c0 .753.388 1.164 1.179 1.164.577 0 1.034-.282 1.3-.578v.486ZM222.156 174.182c.372 0 .608-.099.752-.236l-.19-.601c-.061.069-.205.129-.357.129-.228 0-.35-.182-.35-.433v-1.924h.745v-.699h-.745v-1.004h-.798v1.004h-.608v.699h.608v2.122c0 .608.327.943.943.943ZM225.229 171.566c0 1.407.6 2.616 2.091 2.616 1.133 0 1.87-.761 1.87-1.704 0-1.079-.783-1.634-1.695-1.634-.601 0-1.133.365-1.361.707-.008-.046-.008-.091-.008-.145 0-.889.494-1.673 1.338-1.673.487 0 .776.168 1.042.449l.418-.677c-.342-.334-.829-.562-1.46-.562-1.467 0-2.235 1.155-2.235 2.623Zm3.064.943c0 .41-.35.882-1.019.882-.783 0-1.08-.639-1.133-1.186.259-.365.677-.601 1.11-.601.555 0 1.042.281 1.042.905ZM230.046 173.611c.342.342.829.563 1.453.563 1.467 0 2.235-1.156 2.235-2.616 0-1.414-.601-2.623-2.091-2.623-1.133 0-1.87.768-1.87 1.703 0 1.08.79 1.643 1.703 1.643.593 0 1.125-.373 1.361-.715v.152c0 .829-.418 1.665-1.338 1.665-.479 0-.776-.167-1.042-.441l-.411.669Zm2.776-2.691a1.371 1.371 0 0 1-1.11.593c-.548 0-1.035-.282-1.035-.905 0-.403.35-.882 1.012-.882.783 0 1.087.646 1.133 1.194ZM234.362 171.558c0 1.278.608 2.624 2.03 2.624 1.422 0 2.03-1.346 2.03-2.624 0-1.277-.608-2.615-2.03-2.615-1.422 0-2.03 1.338-2.03 2.615Zm3.155 0c0 .966-.296 1.833-1.125 1.833s-1.125-.867-1.125-1.833c0-.965.296-1.825 1.125-1.825s1.125.86 1.125 1.825ZM239.333 175.102c.419-.297.715-.776.715-1.301 0-.441-.281-.692-.593-.692a.525.525 0 0 0-.517.525c0 .281.198.479.456.479a.468.468 0 0 0 .129-.023c-.053.259-.304.571-.547.715l.357.297ZM240.718 171.558c0 1.278.608 2.624 2.03 2.624 1.422 0 2.03-1.346 2.03-2.624 0-1.277-.608-2.615-2.03-2.615-1.422 0-2.03 1.338-2.03 2.615Zm3.156 0c0 .966-.297 1.833-1.126 1.833s-1.125-.867-1.125-1.833c0-.965.296-1.825 1.125-1.825s1.126.86 1.126 1.825ZM245.404 171.558c0 1.278.608 2.624 2.03 2.624 1.422 0 2.03-1.346 2.03-2.624 0-1.277-.608-2.615-2.03-2.615-1.422 0-2.03 1.338-2.03 2.615Zm3.155 0c0 .966-.297 1.833-1.125 1.833-.829 0-1.126-.867-1.126-1.833 0-.965.297-1.825 1.126-1.825.828 0 1.125.86 1.125 1.825ZM250.089 171.558c0 1.278.608 2.624 2.03 2.624 1.422 0 2.03-1.346 2.03-2.624 0-1.277-.608-2.615-2.03-2.615-1.422 0-2.03 1.338-2.03 2.615Zm3.155 0c0 .966-.296 1.833-1.125 1.833s-1.125-.867-1.125-1.833c0-.965.296-1.825 1.125-1.825s1.125.86 1.125 1.825ZM255.06 175.102c.419-.297.715-.776.715-1.301 0-.441-.281-.692-.593-.692a.525.525 0 0 0-.517.525c0 .281.198.479.456.479a.468.468 0 0 0 .129-.023c-.053.259-.304.571-.547.715l.357.297ZM256.445 171.558c0 1.278.608 2.624 2.03 2.624 1.422 0 2.03-1.346 2.03-2.624 0-1.277-.608-2.615-2.03-2.615-1.422 0-2.03 1.338-2.03 2.615Zm3.156 0c0 .966-.297 1.833-1.126 1.833s-1.125-.867-1.125-1.833c0-.965.296-1.825 1.125-1.825s1.126.86 1.126 1.825ZM261.13 171.558c0 1.278.609 2.624 2.031 2.624 1.421 0 2.03-1.346 2.03-2.624 0-1.277-.609-2.615-2.03-2.615-1.422 0-2.031 1.338-2.031 2.615Zm3.156 0c0 .966-.297 1.833-1.125 1.833-.829 0-1.126-.867-1.126-1.833 0-.965.297-1.825 1.126-1.825.828 0 1.125.86 1.125 1.825ZM265.816 171.558c0 1.278.608 2.624 2.03 2.624 1.422 0 2.03-1.346 2.03-2.624 0-1.277-.608-2.615-2.03-2.615-1.422 0-2.03 1.338-2.03 2.615Zm3.155 0c0 .966-.296 1.833-1.125 1.833s-1.125-.867-1.125-1.833c0-.965.296-1.825 1.125-1.825s1.125.86 1.125 1.825ZM272.659 174.09h.799v-2.501c.159-.259.608-.487.943-.487a1.2 1.2 0 0 1 .251.023v-.791c-.479 0-.92.274-1.194.624v-.54h-.799v3.672ZM275.067 172.25c0 1.164.822 1.932 1.924 1.932.586 0 1.125-.183 1.49-.533l-.365-.524c-.258.258-.684.403-1.041.403-.692 0-1.11-.457-1.171-1.012h2.836v-.19c0-1.155-.707-1.999-1.81-1.999-1.087 0-1.863.859-1.863 1.923Zm1.863-1.27c.723 0 1.019.54 1.034.974h-2.068c.038-.449.35-.974 1.034-.974ZM279.209 173.611c.38.373.95.571 1.573.571.989 0 1.529-.495 1.529-1.141 0-.867-.783-1.049-1.422-1.186-.433-.099-.798-.198-.798-.479 0-.259.266-.411.684-.411.456 0 .867.19 1.087.441l.335-.562c-.335-.297-.806-.517-1.43-.517-.927 0-1.444.517-1.444 1.117 0 .821.752.989 1.383 1.126.449.098.837.213.837.532 0 .274-.243.448-.723.448-.479 0-.988-.25-1.246-.517l-.365.578ZM285.593 174.09h.798v-3.672h-.798v2.577c-.19.251-.54.479-.943.479-.448 0-.737-.174-.737-.745v-2.311h-.799v2.6c0 .753.388 1.164 1.179 1.164.578 0 1.034-.282 1.3-.578v.486ZM287.428 174.09h.799v-5.071h-.799v5.071ZM290.365 174.182c.373 0 .608-.099.753-.236l-.19-.601c-.061.069-.206.129-.358.129-.228 0-.349-.182-.349-.433v-1.924h.745v-.699h-.745v-1.004h-.799v1.004h-.608v.699h.608v2.122c0 .608.327.943.943.943ZM291.371 173.611c.38.373.951.571 1.574.571.989 0 1.529-.495 1.529-1.141 0-.867-.784-1.049-1.422-1.186-.434-.099-.799-.198-.799-.479 0-.259.266-.411.685-.411.456 0 .866.19 1.087.441l.334-.562c-.334-.297-.805-.517-1.429-.517-.928 0-1.445.517-1.445 1.117 0 .821.753.989 1.384 1.126.449.098.837.213.837.532 0 .274-.244.448-.723.448s-.988-.25-1.247-.517l-.365.578Z" fill="#8C8F9A"></path><rect x="171.267" y="183.422" width="127.392" height="5.345" rx="2.673" fill="#E8E8EB"></rect><path d="m176.336 193.76-1.823-.159-.712-1.698-.712 1.701-1.822.156 1.383 1.214-.415 1.803 1.566-.957 1.566.957-.413-1.803 1.382-1.214ZM182.039 193.761l-1.823-.159-.712-1.699-.712 1.701-1.822.157 1.384 1.213-.416 1.804 1.566-.957 1.567.957-.414-1.804 1.382-1.213ZM187.741 193.761l-1.822-.159-.713-1.699-.712 1.701-1.822.157 1.384 1.213-.416 1.804 1.566-.957 1.567.957-.413-1.804 1.381-1.213ZM193.443 193.761l-1.822-.159-.712-1.699-.713 1.701-1.822.157 1.384 1.213-.416 1.804 1.567-.957 1.566.957-.413-1.804 1.381-1.213ZM199.146 193.761l-1.822-.159-.712-1.699-.713 1.701-1.822.157 1.384 1.213-.416 1.804 1.567-.957 1.566.957-.413-1.804 1.381-1.213Z" fill="#F18200"></path><path d="M202.314 194.341c0-.875.71-1.584 1.585-1.584h5.702a1.584 1.584 0 1 1 0 3.168h-5.702a1.585 1.585 0 0 1-1.585-1.584ZM214.853 194.341c0-.875.709-1.584 1.584-1.584h25.979a1.584 1.584 0 0 1 0 3.168h-25.979a1.584 1.584 0 0 1-1.584-1.584Z" fill="#DCDDE1"></path><path d="M171.267 201.866c0-1.077.709-1.95 1.584-1.95h115.048c.875 0 1.585.873 1.585 1.95s-.71 1.951-1.585 1.951H172.851c-.875 0-1.584-.874-1.584-1.951ZM171.267 210.106c0-.875.709-1.584 1.584-1.584h40.552a1.584 1.584 0 1 1 0 3.168h-40.552a1.584 1.584 0 0 1-1.584-1.584Z" fill="#E8E8EB"></path><path d="M171.267 215.626c0-.874.709-1.584 1.584-1.584h32.948a1.585 1.585 0 0 1 0 3.169h-32.948c-.875 0-1.584-.71-1.584-1.585Z" fill="#005AE0"></path><path d="M171.267 221.147c0-.875.709-1.584 1.584-1.584h37.384a1.584 1.584 0 1 1 0 3.168h-37.384a1.584 1.584 0 0 1-1.584-1.584ZM227.534 210.106c0-.875.709-1.584 1.584-1.584h33.583a1.584 1.584 0 1 1 0 3.168h-33.583a1.584 1.584 0 0 1-1.584-1.584ZM227.534 215.626c0-.874.709-1.584 1.584-1.584h40.553a1.584 1.584 0 0 1 0 3.169h-40.553c-.875 0-1.584-.71-1.584-1.585ZM227.534 221.147c0-.875.709-1.584 1.584-1.584h37.385a1.584 1.584 0 0 1 0 3.168h-37.385a1.584 1.584 0 0 1-1.584-1.584Z" fill="#E8E8EB"></path><rect x="171.267" y="228.853" width="32.128" height="2.249" rx="1.124" fill="#DCDDE1"></rect><rect x="171.267" y="233.351" width="96.383" height="3.855" rx="1.928" fill="#E8E8EB"></rect><rect x="171.267" y="240.098" width="37.268" height="21.204" rx="1.618" fill="#DCDDE1"></rect><path d="M190.07 246.345a4.374 4.374 0 0 0-4.373 4.373 4.374 4.374 0 0 0 4.373 4.373 4.374 4.374 0 0 0 4.373-4.373 4.374 4.374 0 0 0-4.373-4.373Zm-.874 6.341v-3.936l2.623 1.968-2.623 1.968Z" fill="#8C8F9A" fill-opacity=".9"></path><rect x="211.105" y="240.098" width="79.998" height="2.249" rx="1.124" fill="#DCDDE1"></rect><rect x="211.105" y="244.917" width="86.103" height="2.249" rx="1.124" fill="#E8E8EB"></rect><rect x="211.105" y="251.985" width="12.851" height="2.57" rx="1.285" fill="#DCDDE1"></rect><rect x="226.205" y="251.985" width="19.598" height="2.57" rx="1.285" fill="#DCDDE1"></rect><rect x="248.053" y="251.985" width="16.385" height="2.57" rx="1.285" fill="#E8E8EB"></rect><rect x="316.31" y="169.364" width="68.726" height="74.233" rx="1.288" fill="#fff"></rect><rect x="316.31" y="169.364" width="68.726" height="74.233" rx="1.288" stroke="#E8E8EB" stroke-width=".859"></rect><path d="M321.193 175.987a1.9 1.9 0 0 1 1.901-1.901h27.321a1.9 1.9 0 0 1 1.901 1.901v1.626a1.9 1.9 0 0 1-1.901 1.901h-27.321a1.9 1.9 0 0 1-1.901-1.901v-1.626Z" fill="#E8E8EB"></path><path d="M321.193 184.103c0-.875.71-1.585 1.584-1.585h23.02a1.585 1.585 0 0 1 0 3.169h-23.02c-.874 0-1.584-.71-1.584-1.584Z" fill="#005AE0"></path><path d="M357.896 175.987c0-1.05.852-1.901 1.901-1.901h18.455a1.9 1.9 0 0 1 1.901 1.901v7.962a1.9 1.9 0 0 1-1.901 1.901h-18.455a1.902 1.902 0 0 1-1.901-1.901v-7.962Z" fill="#E8E8EB"></path><path d="M321.465 193.873c0-.875.709-1.584 1.584-1.584h55.247a1.584 1.584 0 1 1 0 3.168h-55.247a1.584 1.584 0 0 1-1.584-1.584ZM321.465 198.704c0-.845.538-1.53 1.202-1.53h37.827c.664 0 1.202.685 1.202 1.53 0 .846-.538 1.531-1.202 1.531h-37.827c-.664 0-1.202-.685-1.202-1.531Z" fill="#DCDDE1"></path><path d="M321.666 208.575a1.9 1.9 0 0 1 1.901-1.901h8.126a1.9 1.9 0 0 1 1.901 1.901v7.962a1.9 1.9 0 0 1-1.901 1.901h-8.126a1.9 1.9 0 0 1-1.901-1.901v-7.962ZM337.028 208.575a1.9 1.9 0 0 1 1.901-1.901h8.127a1.9 1.9 0 0 1 1.9 1.901v7.962a1.9 1.9 0 0 1-1.9 1.901h-8.127a1.9 1.9 0 0 1-1.901-1.901v-7.962ZM352.391 208.575a1.9 1.9 0 0 1 1.901-1.901h8.126a1.9 1.9 0 0 1 1.901 1.901v7.962a1.9 1.9 0 0 1-1.901 1.901h-8.126a1.9 1.9 0 0 1-1.901-1.901v-7.962ZM367.753 208.575a1.9 1.9 0 0 1 1.901-1.901h8.126a1.9 1.9 0 0 1 1.901 1.901v7.962a1.9 1.9 0 0 1-1.901 1.901h-8.126a1.9 1.9 0 0 1-1.901-1.901v-7.962Z" fill="#E8E8EB"></path><path d="M321.032 223.026c0-.874.709-1.584 1.584-1.584h9.077a1.584 1.584 0 0 1 0 3.169h-9.077c-.875 0-1.584-.71-1.584-1.585ZM336.711 223.026c0-.874.709-1.584 1.584-1.584h9.077a1.584 1.584 0 0 1 0 3.169h-9.077c-.875 0-1.584-.71-1.584-1.585ZM352.391 223.026c0-.874.709-1.584 1.584-1.584h9.077a1.585 1.585 0 0 1 0 3.169h-9.077c-.875 0-1.584-.71-1.584-1.585Z" fill="#DCDDE1"></path><path d="M368.069 223.026c0-.874.71-1.584 1.584-1.584h9.077a1.584 1.584 0 0 1 0 3.169h-9.077c-.874 0-1.584-.71-1.584-1.585Z" fill="#E8E8EB"></path><path d="M327.928 231.049h-5.381c-.675 0-1.223.548-1.223 1.223v5.381c0 .675.548 1.223 1.223 1.223h5.381c.675 0 1.222-.548 1.222-1.223v-5.381c0-.675-.547-1.223-1.222-1.223Z" fill="#DCDDE1"></path><path d="M327.194 234.962c0-1.076-.88-1.956-1.956-1.956s-1.957.88-1.957 1.956c0 .979.71 1.786 1.639 1.932v-1.369h-.489v-.563h.489v-.44c0-.489.293-.758.734-.758.22 0 .44.049.44.049v.489h-.245c-.244 0-.318.147-.318.293v.367h.538l-.097.563h-.465v1.394a1.974 1.974 0 0 0 1.687-1.957Z" fill="#fff"></path><path d="M340.645 231.049h-5.38c-.676 0-1.223.548-1.223 1.223v5.381c0 .675.547 1.223 1.223 1.223h5.38c.676 0 1.223-.548 1.223-1.223v-5.381c0-.675-.547-1.223-1.223-1.223Z" fill="#DCDDE1"></path><path d="M339.913 233.74a1.443 1.443 0 0 1-.465.122.692.692 0 0 0 .343-.44c-.147.098-.318.147-.514.195a.856.856 0 0 0-.587-.244c-.513 0-.905.489-.782.978-.661-.024-1.248-.342-1.663-.831-.221.367-.098.831.244 1.076-.122 0-.244-.049-.367-.098 0 .367.269.709.636.807-.122.024-.244.049-.367.024a.794.794 0 0 0 .758.563c-.293.22-.733.342-1.149.293.367.221.783.367 1.223.367 1.492 0 2.323-1.247 2.274-2.396a1.13 1.13 0 0 0 .416-.416Z" fill="#fff"></path><path d="M353.363 231.049h-5.38c-.676 0-1.223.548-1.223 1.223v5.381c0 .675.547 1.223 1.223 1.223h5.38c.676 0 1.223-.548 1.223-1.223v-5.381c0-.675-.547-1.223-1.223-1.223Z" fill="#DCDDE1"></path><path d="M349.596 236.919h-.831v-2.617h.831v2.617Zm-.415-2.984a.45.45 0 0 1-.465-.465c0-.269.22-.464.465-.464.269 0 .464.195.464.464a.45.45 0 0 1-.464.465Zm3.448 2.984h-.832V235.5c0-.415-.171-.538-.415-.538-.245 0-.49.196-.49.563v1.394h-.831v-2.617h.783v.367c.073-.171.366-.44.782-.44.465 0 .954.269.954 1.076v1.614h.049Z" fill="#fff"></path><path d="M366.081 231.049H360.7c-.675 0-1.222.548-1.222 1.223v5.381c0 .675.547 1.223 1.222 1.223h5.381c.675 0 1.223-.548 1.223-1.223v-5.381c0-.675-.548-1.223-1.223-1.223Z" fill="#DCDDE1"></path><path d="M365.251 234.009a.485.485 0 0 0-.343-.343c-.293-.073-1.54-.073-1.54-.073s-1.223 0-1.541.073a.487.487 0 0 0-.343.343c-.048.318-.048.954-.048.954s0 .635.073.953a.484.484 0 0 0 .342.343c.294.073 1.541.073 1.541.073s1.223 0 1.541-.073a.484.484 0 0 0 .342-.343c.074-.318.074-.953.074-.953s0-.636-.098-.954Zm-2.25 1.541v-1.174l1.027.587-1.027.587Z" fill="#fff"></path><path d="M378.799 231.049h-5.381c-.675 0-1.223.548-1.223 1.223v5.381c0 .675.548 1.223 1.223 1.223h5.381c.675 0 1.223-.548 1.223-1.223v-5.381c0-.675-.548-1.223-1.223-1.223Z" fill="#DCDDE1"></path><path d="M376.108 233.299h.832c.195 0 .293.049.366.074.098.048.172.073.245.146a.519.519 0 0 1 .147.245c.024.073.049.171.073.367V235.794c0 .195-.049.293-.073.367-.049.097-.074.171-.147.244a.506.506 0 0 1-.245.147 1.838 1.838 0 0 1-.366.073H375.277c-.196 0-.294-.049-.367-.073-.098-.049-.172-.073-.245-.147a.516.516 0 0 1-.147-.244 1.87 1.87 0 0 1-.073-.367V234.131c0-.196.049-.294.073-.367.049-.098.074-.171.147-.245a.504.504 0 0 1 .245-.146c.073-.025.171-.049.367-.074h.831Zm0-.367h-.831a1.23 1.23 0 0 0-.49.098 1.058 1.058 0 0 0-.366.245 1.18 1.18 0 0 0-.245.367c-.049.122-.073.269-.098.489V235.794a1.224 1.224 0 0 0 .343.856c.122.122.22.171.366.244.123.049.269.074.49.098H376.94a1.228 1.228 0 0 0 .856-.342c.122-.123.171-.22.244-.367.049-.122.074-.269.098-.489V234.131c0-.22-.049-.367-.098-.489a1.062 1.062 0 0 0-.244-.367c-.123-.123-.221-.171-.367-.245-.123-.049-.269-.073-.489-.098h-.832Z" fill="#fff"></path><path d="M376.108 233.911c-.587 0-1.052.464-1.052 1.051s.465 1.052 1.052 1.052 1.052-.465 1.052-1.052-.465-1.051-1.052-1.051Zm0 1.736a.684.684 0 1 1 .685-.685.7.7 0 0 1-.685.685ZM377.184 234.131a.245.245 0 1 0 0-.489.245.245 0 0 0 0 .489Z" fill="#fff"></path></g><path d="M157.81 111.022a3.417 3.417 0 0 1 3.417-3.417h232.356a3.417 3.417 0 0 1 3.417 3.417v19.648H157.81v-19.648Z" fill="#141B38"></path><circle cx="170.986" cy="119.137" r="2.925" fill="#F83C5D"></circle><circle cx="181.534" cy="119.137" r="2.925" fill="#F18200"></circle><circle cx="192.082" cy="119.137" r="2.925" fill="#07C575"></circle><rect x="206.113" y="111.699" width="170.85" height="14.876" rx="7.438" fill="#fff"></rect><g clip-path="url(#h)"><path d="M219.403 117.513h-.406v-.813a2.032 2.032 0 0 0-4.062 0v.813h-.407a.814.814 0 0 0-.812.812v4.063c0 .446.365.812.812.812h4.875a.815.815 0 0 0 .813-.812v-4.063a.815.815 0 0 0-.813-.812Zm-2.437 3.656a.816.816 0 0 1-.813-.813c0-.447.366-.812.813-.812.447 0 .812.365.812.812a.815.815 0 0 1-.812.813Zm1.259-3.656h-2.519v-.813a1.26 1.26 0 0 1 2.519 0v.813Z" fill="#D0D1D7"></path></g><rect x="226.542" y="115.884" width="116.206" height="6.506" rx="3.253" fill="#D0D1D7"></rect><defs><clipPath id="c"><path fill="#fff" transform="translate(91.283 40.43)" d="M0 0h9.75v9.75H0z"></path></clipPath><clipPath id="e"><path fill="#fff" transform="translate(168.13 136.745)" d="M0 0h217.336v124.452H0z"></path></clipPath><clipPath id="h"><path fill="#fff" transform="translate(212.093 114.262)" d="M0 0h9.75v9.75H0z"></path></clipPath><pattern id="f" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#i" transform="matrix(.0024 0 0 .00685 -.002 0)"></use></pattern><pattern id="g" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#j" transform="matrix(.00103 0 0 .00794 .04 0)"></use></pattern><filter id="b" x=".593" y="10.368" width="312.004" height="226.224" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix><feMorphology radius="5.201" in="SourceAlpha" result="effect1_dropShadow_68_13107"></feMorphology><feOffset dy="13.002"></feOffset><feGaussianBlur stdDeviation="20.804"></feGaussianBlur><feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.152941 0 0 0 0 0.294118 0 0 0 0.1 0"></feColorMatrix><feBlend in2="BackgroundImageFix" result="effect1_dropShadow_68_13107"></feBlend><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix><feMorphology radius="7.801" in="SourceAlpha" result="effect2_dropShadow_68_13107"></feMorphology><feOffset dy="7.801"></feOffset><feGaussianBlur stdDeviation="9.102"></feGaussianBlur><feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.152941 0 0 0 0 0.294118 0 0 0 0.1 0"></feColorMatrix><feBlend in2="effect1_dropShadow_68_13107" result="effect2_dropShadow_68_13107"></feBlend><feBlend in="SourceGraphic" in2="effect2_dropShadow_68_13107" result="shape"></feBlend></filter><filter id="d" x="121.403" y="84.201" width="312.004" height="226.224" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix><feMorphology radius="5.201" in="SourceAlpha" result="effect1_dropShadow_68_13107"></feMorphology><feOffset dy="13.002"></feOffset><feGaussianBlur stdDeviation="20.804"></feGaussianBlur><feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.152941 0 0 0 0 0.294118 0 0 0 0.1 0"></feColorMatrix><feBlend in2="BackgroundImageFix" result="effect1_dropShadow_68_13107"></feBlend><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix><feMorphology radius="7.801" in="SourceAlpha" result="effect2_dropShadow_68_13107"></feMorphology><feOffset dy="7.801"></feOffset><feGaussianBlur stdDeviation="9.102"></feGaussianBlur><feColorMatrix values="0 0 0 0 0.0941176 0 0 0 0 0.152941 0 0 0 0 0.294118 0 0 0 0.1 0"></feColorMatrix><feBlend in2="effect1_dropShadow_68_13107" result="effect2_dropShadow_68_13107"></feBlend><feBlend in="SourceGraphic" in2="effect2_dropShadow_68_13107" result="shape"></feBlend></filter><image id="i" width="420" height="146" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAaQAAACSCAYAAAAKGL4xAAAgAElEQVR4Ae2dB3gcxfn/v7u3e0U6VdvYuGGDjW2MDbgAofdgDNiEmkDyT4HghAABQhLaL78kv0DiFEpCCylAaA4OPfQSikNxo5hmG9wbtmXpdKeru/t/ZqU7nXS7pzvdnu5O+s7z6Lnd2SnvfGa1787sO+9IhmEYYCABEiABEiCBEhOQS1w/qycBEiABEiABkwAVEm8EEiABEiCBsiBAhVQW3UAhSIAESIAEqJB4D5AACZAACZQFASqksugGCkECJEACJECFxHuABEiABEigLAhQIZVFN1AIEiABEiABKiTeAyRAAiRAAmVBgAqpLLqBQpAACZAACVAh8R4gARIgARIoCwJUSGXRDRSCBEiABEiACon3AAmQAAmQQFkQoEIqi26gECRAAiRAAlRIvAdIgARIgATKggAVUll0A4UgARIgARKgQuI9QAIkQAIkUBYEqJDKohsoBAmQAAmQABUS7wESIAESIIGyIECFVBbdQCFIgARIgASokHgPkAAJkAAJlAUBKqSy6AYKQQIkQAIkQIXEe4AESIAESKAsCFAhlUU3UAgSIAESIAEqJN4DJEACJEACZUGACqksuoFCkAAJkAAJUCHxHiABEiABEigLAlRIZdENFIIESIAESEAhAhIgARIggXYChgHE4jHEYwnEEwkkEhoSmgZN06DrBgyj/Y+8ikOACqk4XFkqCZBAhRAIR6KIiL9oDNForEKk7p9iUiH1z35lq0iABLIQaGuLIBQOQ/yKUU96cKsqVLcCVVGgqgoUlwsulwxZliFJkvmXnp7HzhGgQnKOJUsiARIoYwJiCi4YbEMw1AZN01OSut0qfF4PvF4PPB43ZElKXeNB3xKgQupb3qyNBEigjwlEYzEEWkMIhcKpmoUSqq7yobrKC0XhYzAFpsQH7IkSdwCrJwESKA6BeDyB5kBrF0Xkr65Cjb/KHAkVp1aWWggBKqRC6DEvCZBA2REQn4SaWwJoCQRTstXW+lHrr4aiuFJxPCg/AlRI5dcnlIgESKCXBISRQlNzi2muLYqo8VejrtZPRdRLnn2djQqpr4mzPhIggaIQ2NnUgtZgyCzb63Gjob6WU3NFIV28QqmQiseWJZMACfQBgVgsjh1NzRC/IghFJEZFDJVHgAqp8vqMEpMACXQQCLWFsWNns7mWSFjODWqsh8etkk+FEqBCqtCOo9gkMNAJBAJBNDUHTAzCem7woPqBjqTi20+FVPFdyAaQwMAj0Nzcapp0i5bX19WYfwOPQv9rMRVS/+tTtogE+jWBXc2dJt1iik6sK2LoHwSokPpHP7IVJDAgCIiRUXJ90ZDBDaa3hQHR8AHSSO6HNEA6ms0kgUonIL4ZCc8LIlAZVXpvWstfFiMk4edw5VYd763T8NkXOtbvSOCLoIRwTEc8LkGBjuoqFwb7DYxoVDB+mIwpo2RMGuGCQpVq3bOMJYF+REBY0yUNGMQ0nfBDx9D/CEhGd9/rfdjGTzbreOb9OF5doaE11tUFfC5i1LglHDnZhVOmqRg3lJopF2ZMQwKVRkCsL9qybYdp2k0DhkrrvfzkLYlCWrpGw32L4nh/vZaftFlSzxgr4/yjPeboKUsyXipDAjMXnGEp1eKzF1rGM3JgEdi8dbu56JWm3f2/3/t0ym5rs4FbX4jiv6ucU0TJLlqyRsfSz0I4daYHFxytwufmniZJNvwlgUolINwBiRGSWPTKdUaV2ou5y91nCunFFQn8/t9RxJzXRanWGrKMx5fGsWSNhp9/xYOxu3EaLwWHByRQYQSEo9Skbzrx3Yih/xMo+hNbN4Cbn43hhieKq4zSu2pTk46L741g8edF1H7pFfKYBEjAUQJiCwnhtVsE00kq3QE5yrdcCyvqCCmWAP7vsQgWrcxRMRg6Jo1QTOu5kY0S6nwS3KqEWMLArpCBDTsNfLw5gVWbNIjRULYQjhn426tRTB9bBZmzd9lQ8RoJlB0BsZ9RIqFBeO2mo9Sy656iCVQ0hSRMuXNVRmMHyZh7oIojJ7lQ4+1Je7jRHDLw8kcJPLY4gU3NuiWcvXYDfnOOj8rIkg4jSaB8CYidXpOLX8XoiGHgECiaQrrluWiPI6PGGgMXHefFkRMVSD3pobQ+qa+W8JWZKuZMV/Hc+wn85T8xtLR1mo2PGwr89mtVqPXlUWha+TwkARIoHYHk4lexuZ7H4y6dIKy5zwkURSEJJfHU8kTWxhw9ScHlJ7lR5em90nDJwEn7KzhkvAvzn4rg7c90UBllxc6LJFDWBKKxGEKhsCkjp+rKuquKIpzjCkmYdt/8tLih7L/xfOtIN849VEXvVVFXFmLE9KuzfHhkcRwnTFFQw5FRV0A8I4EKIRBobd/xtZbbjldIjzkrpqMKSUya3fRsBFHdXhl95yg3vnaI8xtoiSm/0w90vlxncbM0EiABOwLxRCI1Oqr1V9slY3w/JmCvOXrR6CWfa1j8ubWRgShu1n4KvloEZdQLUZmFBEigzAgEg22mRMIjg6K4ykw6itMXBBxTSGJ0dM9rMVuZRw2ScfEJHsem6Wwr4gUSIIGKJBAMtSsk7m9Ukd3niNCOKSThKPXjzfajo0u+7IaHM2qOdBoLIYH+RkB4ZdA03XQRRMu6/ta7ubfHMYX07Htx21qnjXVh2hgOwW0B8QIJDHACoXC7ZR23lRjYN4IjRg3CPdArn9h7YzjroMofGmmbNiC27B0kVn0Cbcsm6KGgeefI1X64hg2HsvckuA+YCdfI0UW5o4y21TCaXgJal0EPr4WkBcx6DFctZO8eQO10SI3HQKoaX5T6Q1EDy9fq+HSLhm3NBkJxA26XhKG1EvYZKWPGni5UldqhbSKB+EcfmH+JjeuBQAsMQ4fsr4Fr+Ego+0yBOvUASCrXthTlJimgUDFCEqG6yltAKcxa6QQcUUirt+kIhTsXpqZDGeQ3MH1shY6OEglEXnkebY8+BG3lJ+nNSh0LNWyODZ95woxzjZ+IqrlnwXvsiYBSIF4jDmPrAugbb4URWJqqUxyk0zYnSrfcbV6X/PtBGn0x5GFfBaTCXwRWbdXx0FtxvPZpDLpmM6B+B1BVAyfv58Z5h6mor5Lw6scJ3L8o2kVmcbLHEAXXzPFkxBcSoTftRPhfDyDy7JPQW5qzF1VTg6qT5sJ31tch19FhZ3ZYfXM1HImaex0Jj95Kof8zfSMyaykSgQKfmO1Svb/OfnR00Di1It33xFe8i9Ybr4e2bm1e6LVVn6D1t79A20P3ouayq8w38rwK6EhsNC+C/tH3YIStFaFdmUbwPRgfnQ9j3W8hT7wNUv3hdkmzxgfCBm5/MYrnP0j2rY0y6ihF7Oz76JI4XvgggWtP8yAQBj77IrMKl5wsL/Na3jGGjvAjDyH01zthxNqnfHoso7UVbQv+gbanH0PtD66ER7w4MJSUQCTS/uLi8zr7olLSRrHyXhHI/pTJsci1O+yNGaaMqrDRkWGg7cG70XTpvLyVUToubcNaNF0xD233/x0QrotzDgb0tb9BfNmxeSuj9CqM0KeILz4O+tobuo2n0lNZH4vt5L/711CaMrJOZxUbjBr46YNteO3T7J46rPLmE2eE29B87RUI3n5T7soovYLWVgRu+B8E77wZMOzv3/QsPC4OgUi03TrXS4VUHMAVVKojIyThhdsujB3iiM6zK97ZeMNA621/QOTRBY6M6mTDQOjvt0PfuR3+i69Ezw77DOgrr4C+4U9Z/Fzk3mRZNqB/9jMgugXyhJuBHIzuP9ig4aoFUYRjBfjRkGQsW+PgSKhbk41IGC0//SHiH77b7Ur+p+GH74eRSKDm+5fn0D/5l88c2QmId7Voh0KidV12VgPhqiMKKd2xaXdoQ+sKeLABOPb6dlci3cst5HzODBWXnJD5YbvtgbtNZVRI2VZ5w08shDxoMKrO/bbV5VScvvbXpjJKRTh0oG+8A3APgzz26qwlrt2u46f/jCISs3/ByFpAX1wULw3zf+GIMkqKK15AlNFj4Dvl9GQUf/uIQCzePjpyq2Jqv7BnRR+JzGqKSMARhRRJiLdh65up5JZXFvC2t2RO0cTfW4rWu+/IOjJR9hoH78lnwH3ADMhDh5kl61s3I7b0HUSeWojEWvvvTa133wl1yv5Qp06zkAgwdr2KxKr/RbZtnqSaKZBGXAip4WhI3nZrPiO8Fsaul2BsuANGm/33psTqn0OtPwxSwxGW9UfjwM8XRhGJ2iujap+E2VNdmLGnghENMlwuYFuzjqVrNTy5PIZdQet7wLLCXkYKw4Xoay/Z55ZVeGedDO9xs6CM2xuS1wc90IzERysQfvoxxN583TJv6E83wr3/dLhGjbG8zsjiEIiLTdMAqG5HHkXFEZKl9hmBAXkXiA/uXUIijsBNv4aYYrMKkkeF/6Ifwzvr1IxpHfEA84m/OWcg/NjDCN5+C6BnrskSZQdu/DUG3fVApvWdHoP+8fchptisgg4flEk3Qh7+rQzFL1VPgPjDiHnQN96GxOqfQtYzPWaY03effh+ug5ZbWt/dtyiG9bsyFXVSnpMPUHDhMZne2YfUuLDvKBfOOdiNv7wawyPvZLY9WUahv0awFcE7brItRlg41l37K7hGjOqSRq5rgPtLh5t/0ddfRuCX12X0kaHF0HrLfNTPvzWjj7sUxhNHCQj/dSKotK5zlGulFubIBx5vFr9TbWU4/RMXuwemhciLz0LfsC4tpvNQUr2om38bvCfNyf6gkmT4TjsbdTf8AZCtza31DWtNM/LO0tuP9G0Pwgiv6h5tnuuuKqjTn4E8XEz3ZRmBSDLkUT+Aut9j0OXM6UhRmBFaaZqRd69oZ9DAgjczTbST6b57jBuXzfJk3SpEeOG46Dg3vnesdd3Jsgr5DT/5Lxgd67+6l6NO3BcNv78jQxl1T+c5/Bj4v3dJ92jzPL58CWLLF1teY2RxCIhdYUVQ1QH5blwcqBVcqiMKqa7K/kG5rcX6rb+UzLrPVYcffdBWnJorr4E6eT/b690vuKcfBP/FV3SPTp2LNU3dg7Hhlu5RqXN14h2Q6g9Jnfd0IDUeB2XvP9gmEwYT3cPjS+PQDOtb4cSpCs4+2FrBdi9HnJ9+kIpjJhfh4WLoiPz7EasqYfhrUPvz30CqqrK8nopMJBB99SVEskz5hf95Xyo5D4pPIKG1KyRFzP8yDHgC1k+hPLGMHmyvkNZs7zoaybPooiT3qZ3yCvPsxGerLetRp06H5+gTLK9li/SdPBeuvSdaJhELbLXNG1PXhHm20fpB6rzLQcORkIad3SUqlxN5+PmQaqdbJjVal8EIf566Jl4Xnnvfepqtxi1h3nH5jXgE2XnHuuFWnO33xKqV0LZuTcmdfuA/8zzIg4akR3U5FlaObffehe1fnYPAL69C4gN767zI4ncgFtoy9A0BrUMhucRumwwDnoAjr7J7DLa/md5fr5mb5vWW9EtX935flFuej+HxJZkP24bqToUUW2Y/RVN1hvB20Jk25zZIMqrPOBeB66+zzBJ/d4npykZcNHa9bJlGRLpGX5p9ms4up5i+G/1DaCu+bpnCaHoF0og9zWsbdujY0WqZDLMOcKHGm3/7B/klHDVJ7dU6JmtJgPj7y+wuwTvrlMxrhmHmERaO4TdehqzlNlL37DsV4lsVGgdllskYxwnowu+Y2M4zmzWP47WywHIl4IhC2m+0/XD77dVxaLoHpXgBWvuF9Vv60PrOh2zis5WWfSO5FagzD7a8lkuk+IguardS1YnVaXW2Wr+t6/BCaTw+l6os00iDZ0OXXJANi/VAwfdSebJ5aD98Yu5TdakCOw4O2VtxVCElPrP+xiaP2gNy4+BU9UZbGyIvPo3IE/9MWT1a9UEqg1D5bh88J8yC79QzoOw5Lv0Sj4tMwOgwJJJ68+JXZNlYfN8TcEQhjRsqw++RIFbpdw9NIQlL1mg4aC97pdU9jxPnCR0QHgesQvpiXeEo1SrIe4wryAmn5KuCe8yY1EMxvY70KTvhKNUquPwTALkAVyouP1y+8Zam4Hp4TUpRbmyyZiTpOsYP6+lRbiV5e9z4Yc72t7bderpOGbq7WWFi7eeIPLEQkReehvDikEuQR41B1Zwz4D3+JEjV/lyyMI3DBKiQHAZa4cU5opDEy81Rk114apm1u5gFb8b7XCGt2KAhbGPhN3F454M26bW7ez/KDY3do/I+lxt2AyzWJhnBdk/hokDhtTtTjQOGu/1Bm3elaRkM7wjAYm2SlGhJpQrZGNfVVbmgFqBTBvs7R6Gpygo40EPWC6S1bVux6/J5SGSZ0kuvVqhf32FHwzfnTKj7T+/dlGx6gTwmARJwjIAjCklIM2uqaquQ3lsvtjbXMHPPAp5weTZZOPm0CoNrgN0bOhWSZGNdZpW3OHHOPrjzlVGzWXulF9hVTs/AGB3fGrq3TxilQPz1EOSGenhnnw7f7NMgD9mth9S83FcExFSdGCWJP07b9RX18q3HMYU0YbiMySNlfLjRegroludiuPM73j7ZM2d7q4EXPhCLQzsVT7ILDp+gdlnNI/utTYW1nYVbWum7LNxdi1GRv3N6yFBqk6J1+ZXim7uc9+ZEimyyHn0pdaniqm32MAoENYjlWr399tfcZjXuS1Wb94FcXW1+k8s3ozJ1f1SdeiY8hx0FKL3/JpZvvUyfGwEqpNw4DZRUmU/sXrZcvOf/v8PtTYQ379Jx07MxywdkL6u0zfbnl2O262qOn9JVB8u7j7AsR1/zOYyozXyWZY6ukeLjesxiuk6kSt/ET/ZZu6rRQp8AeneXEl3ryHqmtUKzWWwr+/ZKZR2eNlpMRYoDSUYhJvuf2xiUdKkjjxOlw1VTLlmEkYLwS9dw1wNo+MOf4TnqeCqjXMCVII0st88Q6Lr1i2wJRGKVJSTgmEISbRBblR88zn6u56UVCdzzWqZbGyfb/8x7Cbz8ofV03T4jZEzYvWuTlfHW64WE+5/YO4t6LVrsrdctxmftxSnjJnSW6z+g8zjtSLj/MXY8mxaT36Gx/d/WFnaimNrOOrMZLvx3pYWFXo5ivL2693mtqlD22tsqukuccOPk/8GPMGjh0/Bf+hMoY2kx1wVQGZ64OhbEat28p5ShqBSpDwh0fToXWKF417n4yx5z91C7ov7xRhx/eaU4I6UXVyTwu6fsRxVfPyxzBCe2HbcL4QX35bmXUUdJho7QwvvtioV7+oGpa2Lbcbugr78x772MzLIMHfp6e59vcsOxqSqFQhJOU63CU8ujEE5X8w1tUQMvrLB+Kci3rGR69YAZycPM38bBqP/d7Wj82wL45p4FqSqPtWsdCzMzC2VMXxBIemhIemzoizpZR/kScFQhiWYOq5NwxSxv1hY/+GYc1z4cQavNtudZM1tcFC9Xd78Www1PRM2pJoskmLmnjJkWpudi+kw45bQK8U9WINKxNbnVdbu48JOP2G55rk7eH/KQoamsUtV4iG3HrYLR8hb0zX+3upQ1Tt/0ZwiPDFZBqj8U8I5MXRIzJsft03UaM3lxZ1DC3a/lP215z+txBCPOfkNS9hwPaXin3EkZxa++ayckrzdvi7nY24uw41tnIv7h++nF8bgPCSgdfjDjcWdfYPqwCazKQQKOKyQh2/H7KpgzPfsH5LdWafjm7WGIKbZCRutirdGl94YhRl52we+VcMVJ3i7GDOlpq06zd8/TetP8vBxuxpa8hcBt9r7kfGeck161eSyPtnb2KS4mPr0ERpO9N4fuhRk7n0fiE3tfevKozLpOm6FArDuyCv98O4Hn3s/9YfGfjxNYWAyP35KE6rlnWYloemkXXjH0ll2W160ihRIK/N81MDZvRNMl30Xorj/CiBV3OtlKjoEel/TynfT6PdB5DPT2F0UhCagXHe/G4ROs37yT0JsjBn737yi+eWcYC9+OQ3idziXENeCt1RqueTiC7/0tjGzeBgxJx9VzPBhSaz0tJerzHvNluPawNi4Q35JafvJDhB9dANg8tE2ZdR3hhQ9g1zWXQu5wqd+9LcK/nWnt1e2CNOwcSD7rUZr4lhR/79T2jfusvC4kyzI0c5ouvnwuZMlaOUs10yANmZPMkfodNUjGiQdkTmcmE8x/Koq/vxpDh2PmZHSXX2GV/fDbcfziMfsp0y4ZenHinT0XGNzplSG9CLHYuPnyC6FtXJ8enXlsGIg8/TharpyXWkArSzraFvwDzd8/D4lV9ntKZRbGmEIJJPdBSu6LVGh5zF/ZBCQjuVS6CO0QiuOXj0Ww6NPcP3CPGdxuPi4eksKLuFeF+SBsjRjY2qxj9TYDYtFrLMcifzTbg1n7ZVeMounxFe+i6dJ5EA8nuyCUlu/k0+GediCS1nn6lk2ILX0bbU8+ArG9hF3QFQWD/vg32BlRGM2LEF92LGTDvn6htKRR34XUeCwk31izKiO8BkbTizA23WluL2Fbv6FCPegNSDWdBg3paYWZ9gV3hSA8a9iFYbUSTpmumlOfI9M26Fu+Tseji+NYu8Ne9mSZew+TcPu3u5raz1xwRvJyl9/FZy/sci5Oom/8B4H//XFGfDJCcrnhPWUuPMeeCHX8hHbrOsOAvnMHYkveRNvjC6FlUTpybQMaH3wckif7tHOyPv4WRkAsg1u3oX2Jw+hRu3PX2MJwVnzuoiokQUe8Od/2QgyPWjg5LSo9Q8ePT/Hhy1N7VkZJOcRbspi6KUYQVl89bZGtr/sd9NXZtxnvrWzyhD9CHnlh1uxiAfOP72tDwtY+MGv2nC4WqpBEJcFb5kM4Te0p6IYMpaYaRiwBI9bzyE2kb7j+93AfdGhPRfO6gwS2bNuBaDSGobsNgs9bgLssB2ViUaUhULQpu2RzxEfzH5zgxrVzPahS7N++k+md+BVWY/O/VpWXMhL1Vp11Hnynf80JEbqUUf2NC3pURiKDvMcVppfuLpkdOJH3vK5HZSSqEU5yrzrNB2QZpTkgTsFF+C+6Ap5Dj+yxHDHa1YOtuSkjSULdlVdTGfVI1fkEXk/7dHEkkr8BjfPSsMRSEii6Qko27uh9FPx1ng9HTMx9xJLMm8+vsKb763d8mD7Wfj2UbXmSBP+8S+G/8NJeeQXIKFdWUXPZVaj6xgUZl6wjJMjjf2P+CU/dhQaxc6w88TbIY623wbAq/6hJCq4/pwrpe0ZZpcsaZ+iYMbaIt5bLhdrrbmjfxTerILldFLsC1193PbwnnppbBqZylIC3Y1QUpkJylGslFlbEp0Ymjt1qJfzsKx78/lyv+TaemaL3MaMbZLPsG87xZTVg6LEGSYLvzHPRKL737NX7hZXqpH3RcPvd8M4+rccquyaQII++DOqMVyHVTOl6KY8zqe4gqDP/C3nE+Xnkak8qPLPfdYEP0/bIXykKRfars6tw2ITsVpZ5C9U9g6Kg5vJrUHv1LyHX1Xe/mvO5MnEyGu68F54jOtdm5ZyZCR0hIKbphAuhWCyOhI1BkCMVsZCyJ1D0b0jZCHy6RcdzHyTwn48SaOmF7zOXpOPg8SpO3E/Fl8a5erWXXjb5hFVd9PWXEX7koZzXqihTp0Fs7Of50uG2a6Ky1pl+0dBgfPEo9PV/hBF4M/2K7bHUcATkUZdC7Ick3P8UEoTNo/C48OB/41ixsQcrEkPH8VNUnH+0B4NrJDyxLI6bn800o540XMafvunrIlY+Rg1dMnacGKGg2UfhJx+G3pSb6bdYe1Z91nnwHHVcwZysZGJcfgS279yFUCiMhvpa1NV2+nrMrxSmrnQCJVVISXjC8GH1Nh3L12r47AsdG3dq2B7QEYxIiMd1SCpQq7pQ75cwolHCmCHCEs+FqaNk+GycgybLdupX27YF8eVLTLPghNhDKdSxhYS/BsruI6BM2AfC64M82H4r7YJkiayHvusVILAMetsaJLeQMNR6yL6xkGpnwPT64BleUDV2mbc2G6bH9pVbNWzdZSAYN+BVJAyrAyaPVHDI3i40pu3EK0zA73gpUyGJKdVfn9NVIX3jeWuruXtPmG8njnW8piH+wbuILX8H8dUrYWzbCj0YgqQARnUd1JEjoUyYDPeBX4IyptOfn3VhjO1LAm1tEXyxowlut4rhw4r0P9SXDWJdvSJQFgqpV5IzU1kTuPXFGB6xWCB7whQXfnIKTarLuvNKJNyGTVshfNrtPnQwPB2GDiUShdWWiEBhczolEprV9g0Bsetub8Nqm916Rw3K/7tUb2Vgvsoi4K9uX5/WGsxtx9/Kah2lzYUAFVIulAZQGrGYWXhlv/ieMG59PnPKLRcU4nvghxutvUVk8y6eS9lM038J+Dv2JguG2pDI5hak/yIY8C0rrg32gMdbOQDEpoZPLYvjiXcTCITaXTh9tDGBk/ZXkK8SeWxpHJqe+a6jQDc3cawcKpS0LwkIv3bV1T7TuCEQDKGx3nrzyr6UiXX1LYHMp0bf1s/ayoDAJ5t1nHNLEPctiqeUkSmWJONn/4pgV4eCykVUsTHfQ29aL3CcMU7tkx2Dc5GTacqTQG1N+9YhgUCQo6Ty7KKiSkWFVFS8lVH43rvLGDvYerC8rcXAJfdEsD4HP3VCsf3o/ghiCevb6pRpRV6bVBm4KWUWAh632xwliSQtgQ5L1izpeal/EaCVXf/qz1635r11Gi6/P2KbX6z5Ou1AD2bvr0A4vk06gRJbh4j1ZE+/G8cz78Zs1/RM2F3Crd+qSuWzrYgXBjwBsTfSpi1fmBxocTewbgcqpIHV31lb++eXY1jwlrUxQnpGv0fC4FoJwgBieyBhOyJK5TF03Pbt6ozt41PXeUAC3Qjsag6YIyTh527YUOstR7pl4Wk/IGA9t9IPGsYm5E/gO0e5ceiEns2yg1EDa7fr2NSk96yMxN5YJ3ipjPLvjgGdo76uFmI32Ug0xqm7AXQnUCENoM7uqakuGfifuV4cM9n6e1JP+a2uf/MIN74yk9+OrNgwzp6AJAGN9XVmAjFaisZ6Hrnbl8YrlUKAU3aV0lN9KKcw+uZjFfgAAARjSURBVBYb7v3lpQiiFubbuYhS45Zw2Ww3jpzknHLLpV6m6V8Edja1oDUYokuh/tWttq2hQrJFwwtbWwzc90YMz62IQddyG0yLPa9mT1Pw1UNUc8dfUiSBQgls3rrd9AQuPDkMHtR7z+6FysH8xSdAhVR8xhVfg/C8sGilhuXrNIh1RlsDBsIxDaoBVPtcpsPbPYfKmD7GhQP3Usxt5yu+0WxA2RAQ21KIXWUNw0B9XY35VzbCURBHCVAhOYqThZEACRSDQKgtjO072rcWGdRYj5oON0PFqItllo5AbvMwpZOPNZMACZAAqqt8KVdCO5uaIRQUQ/8jQIXU//qULSKBfkmgttaP+toas21itESl1P+6mQqp//UpW0QC/ZZAfX1NakdZoZS4VUX/6mp+Q+pf/cnWkMCAINDc3IrmQKvZVho69J8up0LqP33JlpDAgCIgPII3NQfMNtMkvH90PRVS/+hHtoIEBiQB8R1px85m0yTc7VYhLPA8bnoGqdSbgQqpUnuOcpMACZgExDqlHU3N5uJZEdFQX5v6zkRElUWACqmy+ovSkgAJ2BBIuhkSl4WXcKGYPB63TWpGlyMBKqRy7BXKRAIk0CsCbW0RNDW3pHabrfFXm6Ml4TmcofwJUCGVfx9RQhIggTwIGAbQ3NK+n1Iym1jDVOuvNre0SMbxt/wIUCGVX59QIhIgAQcIiJ1nhWl4KNTp1UFY4wm3Q5zKcwBwEYqgQioCVBZJAiRQPgSisRgCraEuiklY5Al3RNVVXigKt0gpl96iQiqXnqAcJEACRSUQTyQQDLYhGGqDpumpuoRy8nk98Ho95shJFrsDMpSEABVSSbCzUhIggVISEMYPoXAY4ldsa5Ee3KoK1a1AVRSoqgLF5YLLJUOWZUiSZP6lp+excwSokJxjyZJIgAQqkEA4EkVE/EVjiEZjFdiC/iMyFVL/6Uu2hARIoEACYrAUi8cQjyUgpvgSCQ0JTYOmadB1wxxNdR9RFVgls6cRoEJKg8FDEiABEiCB0hHg9hOlY8+aSYAESIAE0ghQIaXB4CEJkAAJkEDpCFAhlY49ayYBEiABEkgjQIWUBoOHJEACJEACpSNAhVQ69qyZBEiABEggjQAVUhoMHpIACZAACZSOABVS6dizZhIgARIggTQCVEhpMHhIAiRAAiRQOgJUSKVjz5pJgARIgATSCFAhpcHgIQmQAAmQQOkIUCGVjj1rJgESIAESSCNAhZQGg4ckQAIkQAKlI0CFVDr2rJkESIAESCCNABVSGgwekgAJkAAJlI4AFVLp2LNmEiABEiCBNAJUSGkweEgCJEACJFA6AlRIpWPPmkmABEiABNIIUCGlweAhCZAACZBA6QhQIZWOPWsmARIgARJII0CFlAaDhyRAAiRAAqUjQIVUOvasmQRIgARIII0AFVIaDB6SAAmQAAmUjgAVUunYs2YSIAESIIE0AlRIaTB4SAIkQAIkUDoCVEilY8+aSYAESIAE0gj8f5mDuzsYOa7+AAAAAElFTkSuQmCC"></image><image id="j" width="934" height="126" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAA6YAAAB+CAYAAAA3Bww0AAAW10lEQVR4Ae3dW4xkeV0H8F91XfpW3V07PbC7LDAGcFHB3QWiUUFBQXkAfRCND5jVxBAv+LIg+CQKMT5wM5ool8QHCRtjIkYSMMGAYnCUaCC7KCRMWGTXZXdnd2en71237jL/6q2erprZpXq6e8451Z+TVLpOzTn/y+ecl+/8/+d/Sr1erxc2AgQIECBAgAABAgQIECCQkcBURvWqlgABAgQIECBAgAABAgQI9AUEUzcCAQIECBAgQIAAAQIECGQqIJhmyq9yAgQIECBAgAABAgQIEBBM3QMECBAgQIAAAQIECBAgkKmAYJopv8oJECBAgAABAgQIECBAQDB1DxAgQIAAAQIECBAgQIBApgKCaab8KidAgAABAgQIECBAgAABwdQ9QIAAAQIECBAgQIAAAQKZCgimmfKrnAABAgQIECBAgAABAgQEU/cAAQIECBAgQIAAAQIECGQqIJhmyq9yAgQIECBAgAABAgQIEBBM3QMECBAgQIAAAQIECBAgkKmAYJopv8oJECBAgAABAgQIECBAQDB1DxAgQIAAAQIECBAgQIBApgKCaab8KidAgAABAgQIECBAgAABwdQ9QIAAAQIECBAgQIAAAQKZCgimmfKrnAABAgQIECBAgAABAgQq33noEQoECBAgQIAAAQIECBAgcEwCpVIp0mdqqhTlcjkq6VMpR7VSiWqtErVqLUqlY6psQoqpTEg/dIMAAQIECBAgQIAAAQK5EOj1epE+u7sR3e5OtK7RqunpWsykz8x0zM5MX+OI0/VTqZfEbAQIECBAgAABAgQIECBwLAJXgulu7OzsRndnJzqdbnS63ei0u9HudIbqSaOrc3MzMT872/879I+nZEcwPSUXWjcJECBAgAABAgQIEMiHwG6vF61WO5rNVmw3W9FuXwmq5fJU1Ofnol6f60/9zUeLT74VgunJG6uBAAECBAgQIECAAAECzyjQ7XZjc6sZm1vbQyF1fn42FhfmY7pWe8ZzJ+UfBNNJuZL6QYAAAQIECBAgQIBA4QXSSOr6xlZsbG7t9yUF1MbiQlSrk7tEkGC6f7l9IUCAAAECBAgQIECAQD4E0qJJaxubsba2sd+gpcV6NJYWJ3JFX8F0/zL7QoAAAQIECBAgQIAAgXwJpIC6urYR6xub/Yal186caSxN3CJJgmm+7jutIUCAAAECBAgQIECAwFUCaYrv5ZW1aLba/X9bqM/H8pmlq44r6g+CaVGvnHYTIECAAAECBAgQIHDqBNLoaQqoaavVqnH2TKP/t+gQgmnRr6D2EyBAgAABAgQIECBwqgRa7U5cemqlv4Jvegfq2eVGzM/NFtpAMC305dN4AgQIECBAgAABAgROq8CTl1b2V+8901iMxcV6YSkE08JeOg0nQIAAAQIECBAgQOC0C6ysrkf6pC29UqbRWCgkiWBayMum0QQIECBAgAABAgQIENgTSO89TVN705ZeKXNTY7FwNFOFa7EGEyBAgAABAgQIECBAgMC+wEJ9Lp5z9qb+flocaWVlbwR1/4ACfBFMC3CRNJEAAQIECBAgQIAAAQLPJpAWPxqE05W19Vhb23i2w3P3b4Jp7i6JBhEgQIAAAQIECBAgQODwAimcLp9p9E98amUtNre2D19IRmcIphnBq5YAAQIECBAgQIAAAQLHLZCm9TaW9hZASqv2ttud467iRMoTTE+EVaEECBAgQIAAAQIECBDIRiAF0/r8XPR6vXjy6UWRsmnJ+LUKpuNbOZIAAQIECBAgQIAAAQKFEDi73IhardofMb301Gru2yyY5v4SaSABAgQIECBAgAABAgQOLzB43nR9YzO2tpqHL+AGniGY3kBsVREgQIAAAQIECBAgQOBGCUzXqvvvNH1qZTV6vRtV8+HrEUwPb+YMAgQIECBAgAABAgQIFEJgabEeM9O16HZ3YmV1LbdtFkxze2k0jAABAgQIECBAgAABAkcXuKmx2C9kdW0jOp3u0Qs8gRIE0xNAVSQBAgQIECBAgAABAgTyIjA9XYuF+ny/OStr63lp1lA7BNMhDjsECBAgQIAAAQIECBCYPIE0pTdtm5vb0Wq3c9fBSu5apEEECBAgQIAAAQIECBxJ4PyFnfjnr3fjq9/Z6Zfzyu8rx+tfVomfuL18pHKdXFyBSqUci4v1WFvbiLX1zXjOci1XnSn10ltXbQQIECBAgAABAgQIFF7gu5d344OfbcfXHtoLpKMduutcOX7vTdNxa6M0+k/2T4FAWgDp4Ucu9nt62/OeG9VKfsYpBdNTcAPqIgECBAgQIECAwOkQuOeTzWcMpQOBFE4/9NaZwe6p/futi7vx7xd24v6HduKxld14bHVvvO6WpVLc0piKO19Y7o8wv+TmyXr68clLK7GxuRVpau9gUaQ83ASCaR6ugjYQIECAAAECBAgQOKJACll/8HfNsUr541+eiR///tM5rff+B3fiE//WifsevPao8ihgCvJ3v6Yad56bDK9Wqx2PXnwyyuWpeMFtt4x2N7P9/IzdZkagYgIECBAgQIAAAQLFF/jCN8Z/DcgXvt49lcH0/Z9pxee+Nr5TuitSgE2fN95RiXe/ebrwN0paobdWq0a73YmtrWbMzeVj9HyyxqULf5voAAECBAgQIECAAIHrE/jq/443AphKP8yx19eafJ210ezFb/7V9qFD6cFepED7znubkcoq+jY/N9vvwub2dm66YsQ0N5dCQwgQIECAAAECBAhcv8Da9viBafUQx15/i/JzZgqU6ZnS0S09R5pGQl9881QMniVNxz1wcbcfYtPzpwe3NHL6h59qFf4Z3fm5mbi8stYfMY3lgz3M7rtgmp29mgkQIECAAAECBAgQOGGBNH13NJSmIPr2N9Su+dxoCqjpkwJreh71Lz7f7gfVQTNTOE1lFnlab6VS2Z/Ou91sxexM9lOUTeUd3GH+EiBAgAABAgQIECAwUQIpWI4+U5pC6YffOnPNUDra+bTgUTo2nXNwS2Wmsou8DcJos9nKRTeGhXPRJI0gQIAAAQIECBAgQIDA0QXS6rsHt0Eorc+M/x7XdOy1wulo2QfrKcL3madHSZutdi6aK5jm4jJoBAECBAgQIECAAAECxymQpu+mabcHtzR99zChdHBuOiede3BLZY9OET7473n/nlbnTVt6fUxv/MeTT6xbgumJ0SqYAAECBAgQIECAAIGsBM5fGH4tTFro6CjvIk3npjIObkWezjtVKkWtWu13p93JftRUMD14Z/lOgAABAgQIECBAgMBECNz/4PAqvGkxo6Nuo2WcvzA8InvU8m/0+dXankmnPRzib3Q7Un2CaRbq6iRAgAABAgQIECBA4EQFLq4OB9PRBYyup/LRMjZbOZgDez0defqcauXpYNoVTI/A6FQCBAgQIECAAAECBAhcW+Cx1eHQOHhP6bWPHu/X0TKK/Ixp6nG1uhdMu93sR36NmI53DzqKAAECBAgQIECAAAECQwLz0+Ov7jt0Yk52KuW9Z2a7O4JpTi6JZhAgQIAAAQIECBAgMEkCNy8Nh8bjGN0cLWN0BLVofuXy3jjljmBatEunvQQIECBAgAABAgQIFEHglqXhyaEPXBx+5vR6+jBaxugzp9dTZpbnTE3tGe3uDk97zqJNw1crixaokwABAgQIECBAgAABAscscOe54ajzT/999AV+Rsso+ohpqbQ3qtzLwYtMh6/WMd8MiiNAgAABAgQIECBAgEAWAq++ffj1MPc9uBNHee9oOjeVcXAr+ohpnoLp8NU6qOw7AQIECBAgQIAAAQK5Edj87PAzk/NvGp5+WZ8pxUZz+LdnavzCzHBZ6bgn3vCjQ4c/5/P/ObRftJ00mnnnC8tx/0NXwuRffr4dH3rrTCSrw2zJNZ17cPu5H65E0UdMD/Yn6+9GTLO+AuonQIAAAQIECBAgMIbA1OKdQ0ftrt03tP+Kc3srrA79+Aw7d40c233gwtCR1TteObRf1J1f+8nqUNPT4kXvvLc5doBPJ6dQms45uPBRWo337T9bGyq7iDuDKbyDkdMs+yCYZqmvbgIECBAgQIAAAQJjCpQqjaEjR4Ppz7xs/GA6euzOY48OlT0pO3eeK0ca2Ty4DcLpONN60zGjoTSVlQLvYUddD7YhL9/zFEyHr1JehLSDAAECBAgQIECAAIEhgamlu2LnqX/d/6372D9E5fm/vr//Uz9QiR+6rRPf+O6zrz77stumIh17cGuf/+LB3ai85Pah/SLv/P7PT8cDj+/GwRV1Uzh9x73NSCPHb7yjEi967tT+tNz0b99+fDc+97XuVc+UDhzSOZOw7e7u3StTU4eb2nwSfS/1BjH5JEpXJgECBAgQIECAAAECxyKQRki3v/SKobJmfuxforz8uv3fHrq0Gx/8bDu+/vCV5yr3/zEiXv78crzzTbV44fKViZNptPTyb/1q9DbW9w+96WOfjMqLJyecpum4KYgeDKf7nT3Cl3e/ebofbI9QRKantlrtePTikzE9XYtbbz6baVuu3JGZNkPlBAgQIECAAAECBAg8m8DU4l1Rmj03dEj7G/dEr7Oy/1sKnH9+90z80Vum47U/WInF2VL/k76/9y0z8Wd3zwyF0nTi1ic+PhRKS/P1iQqlqY9p2u3Hf2P2qmm9+3BjfPnFH6nGp98xN3Tk+z/T6o+sDv1YoJ3uzt5/YFTK408DP6nuGTE9KVnlEiBAgAABAgQIEDhmgZ1LX4zml396qNSppV+I2dd8eui3cXean/tMrH/gfUOHL7zrPTHzxjcP/TZJO+m50b/+Umdotd5n619a2Tc9U5qeV03b6/9k86rDizpyurK6HumztFiPmxqLV/XrRv4gmN5IbXURIECAAAECBAgQOKJA8z9et/+saa9VjtbfvyKm5l8ai+/9QJRvuXXs0rc/9Tex8ZE/HTp+6uZbY/ne6wu5QwUVYCc9S3r+Qjfuf3A3Nlq9/Wm+6d2k9elSvPr2cj+Mjr4SJj17mkZKR7cihtMnLl2Ozc3tOHumEfX68GjwaP9Oel8wPWlh5RMgQIAAAQIECBA4RoE0dbf55dfF7tr90f7HV0X3m3tP55XqC1H/7Xu+52hneqZ08yMfjtb5KwsppealKbyND3904qbxHiP9flGTEk4fefSJaHc6cestZ2O6lu3rbwTT/dvLFwIECBAgQIAAAQLFEEgLIa1/6J5on796WmlatCitqjv4O+hR91sX+mG0c/9XBj8N/Z30KbxDnT2GnaKH091eLx76v73XBJ17wfOilPHCvILpMdyUiiBAgAABAgQIECBwowW6D1yI1fe8K3YvHu0dpGmktP477/ieI603un9FqK/I4XS72YqLj1/KxYq86VpblbcId7w2EiBAgAABAgQIEBgRSCOiZz72yaje8cqRfxl/Nz1TmqbvTvJiR+NrHP7I9D7T9Gzp6FaE1Xqbzb3nZGems53CO7AzYjqQ8JcAAQIECBAgQIBAQQXS9Ny1979v7NHTFEjn736bQHpM17uII6ePPPZEtNuduPm5yzE7c3W4PiaasYsRTMemciABAgQIECBAgACBfAuk6b1pUaPOfV+Jb373f+JFl9r9Bn97uRYvve3l/WdPp1/92qje+ap8d6SArStSOO12u/HwI49HqVSKcy8YfyXnk7wslZMsXNkECBAgQIAAAQIECNw4gf6CRy++PeLut8Xv/u0vDVX8X7/y0aF9O8crkKb1pu1ar5I53pqOXtrmVrNfyNzczNELO6YSPGN6TJCKIUCAAAECBAgQIEDgdAuMPnOa13ebbm5t9y/U/Oxsbi6YEdPcXAoNIUCAAAECBAgQIECg6AKDkdPUj4Pf89KvVqvdf7a0XJ6KPI2YCqZ5uUO0gwABAgQIECBAgACBiRDIYyAdwK5vbPW/1ufnBj/l4q+pvLm4DBpBgAABAgQIECBAgACBkxXodndiY/PpYFoXTE9WW+kECBAgQIAAAQIECBAgcJXA2sZm/7f5+dmoVvI1edbrYq66XH4gQIAAAQIECBAgQIDAZAmk0dKHH7nY79Stt5yN6VotVx00lTdXl0NjCBAgQIAAAQIECBAgcPwCq2sb/ULTaGneQmlqmGB6/NdciQQIECBAgAABAgQIEMiNQFqJd/3pabyNxYXctOtgQwTTgxq+EyBAgAABAgQIECBAYMIELq+s9Xu0tFiPajVfz5YOqAXTgYS/BAgQIECAAAECBAgQmDCBNIW32WpHpVKOxtJibnsnmOb20mgYAQIECBAgQIAAAQIErl+g1e7EYLT0TGMpSqXrL+ukzxRMT1pY+QQIECBAgAABAgQIEMhA4NJTK/1aF+rzMTc3k0ELxq9SMB3fypEECBAgQIAAAQIECBAohMCTl1ai3e5ErVaN5TNLuW+zYJr7S6SBBAgQIECAAAECBAgQGF9gZXU9Nja3olQqxdkzjfFPzPBIwTRDfFUTIECAAAECBAgQIEDgOAXWN7YiBdO0nV1u9EdMj7P8kypLMD0pWeUSIECAAAECBAgQIEDgBgpsbm3H4LnSM43FmJ+bvYG1H60qwfRofs4mQIAAAQIECBAgQIBA5gIplD7x5OV+OxqLC7G4WM+8TYdpQD7frnqYHjiWAAECBAgQIECAAAECp1ggTd8djJQuLdaj0VgonIZgWrhLpsEECBAgQIAAAQIECBDYE0jPkw6eKU0jpUUMpakngqk7mgABAgQIECBAgAABAgUUSK+ESavvpi09U1q06bsHyQXTgxq+EyBAgAABAgQIECBAIOcCrXanP3U3vae0/0qY5UahFjq6Fq9gei0VvxEgQIAAAQIECBAgQCCHAqtrG3F5Za3fslqt2n9Pafpb9E0wLfoV1H4CBAgQIECAAAECBCZeoNVq9wNps9Xu93WhPh/LZ5Ympt+C6cRcSh0hQIAAAQIECBAgQGDSBLrdnUijpOsbm/2uVSrlONNYirm5mYnqqmA6UZdTZwgQIECAAAECBAgQmASBFEjXNjZjbW1jvzv9V8EsLUaptP/TxHwRTCfmUuoIAQIECBAgQIAAAQJFF0hTdtN7SQer7ab+zM/PRnoVTLU6ufFtcntW9DtS+wkQIECAAAECBAgQOBUC3W43Nreasbm1HWml3cGWAuniwnxM12qDnyb2r2A6sZdWxwgQIECAAAECBAgQyKPAbq8XaWS02WzFdrM1FEbL5amoz89FvT4X1crpiWunp6d5vCO1iQABAgQIECBAgACBiRPo9XqRPru7u7GzsxvdnZ3odLrR6Xaj0+5Gu3NlVDR1Pr2LNC1mND87O3GLGo17cSvfeeiRcY91HAECBAgQIECAAAECBAgcg8D0dC1m0mdmOmZnpo+hxGIXYcS02NdP6wkQIECAAAECBAgQyJlAGgFNn6mpUpTL5aikT6Xcn5pbrVWiVq1N5Mq6R7kMpV4aY7YRIECAAAECBAgQIECAAIGMBKYyqle1BAgQIECAAAECBAgQIECgLyCYuhEIECBAgAABAgQIECBAIFMBwTRTfpUTIECAAAECBAgQIECAgGDqHiBAgAABAgQIECBAgACBTAUE00z5VU6AAAECBAgQIECAAAECgql7gAABAgQIECBAgAABAgQyFRBMM+VXOQECBAgQIECAAAECBAgIpu4BAgQIECBAgAABAgQIEMhUQDDNlF/lBAgQIECAAAECBAgQICCYugcIECBAgAABAgQIECBAIFMBwTRTfpUTIECAAAECBAgQIECAgGDqHiBAgAABAgQIECBAgACBTAUE00z5VU6AAAECBAgQIECAAAECgql7gAABAgQIECBAgAABAgQyFRBMM+VXOQECBAgQIECAAAECBAj8P7Ma3d4XOqy2AAAAAElFTkSuQmCC"></image><linearGradient id="a" x1="295.539" y1="37.325" x2="152.741" y2="328.933" gradientUnits="userSpaceOnUse"><stop offset=".406" stop-color="#E5F0FF"></stop><stop offset="1" stop-color="#fff" stop-opacity="0"></stop></linearGradient></defs>',50),Ho=[No];function jo(l,o){return h(),I("svg",zo,Ho)}const Xo=N(qo,[["render",jo]]),Yo={class:"empty-state"},$o={class:"empty-state__body"},Jo={class:"empty-state__title"},Wo={class:"empty-state__description"},es={__name:"EmptyState",props:{context:{type:String,default:"keywords"}},setup(l){const o="aioseo-pro",t=V(),s=l,m={keywordsDescription:a("In order to see your website's rankings you need to add some keywords. No worries, we'll help.",o),keywordsTitle:a("Track keywords for your website",o),groupsDescription:a("You can create a group and add some keywords. No worries, we'll help.",o),groupsTitle:a("Track multiple keywords by attaching them to groups",o),addKeywords:a("Add Keywords",o),createGroup:a("Create Group",o)},c=Q(()=>m[`${s.context}Title`]),n=Q(()=>m[`${s.context}Description`]);return(A,d)=>{const p=R("base-button");return h(),I("div",Yo,[g(e(Xo)),i("div",$o,[i("div",Jo,u(c.value),1),i("div",Wo,u(n.value),1),l.context==="keywords"?(h(),S(p,{key:0,size:"small-table",type:"blue",onClick:d[0]||(d[0]=x(y=>e(t).toggleModal({modal:"modalOpenAddKeywords",open:!0}),["exact"]))},{default:r(()=>[B(u(m.addKeywords),1)]),_:1})):M("",!0),l.context==="groups"?(h(),S(p,{key:1,size:"small-table",type:"blue",onClick:d[1]||(d[1]=x(y=>e(t).toggleModal({modal:"modalOpenCreateGroup",open:!0}),["exact"]))},{default:r(()=>[B(u(m.createGroup),1)]),_:1})):M("",!0)])])}}},ts=N(es,[["__scopeId","data-v-183d4fbd"]]),os={class:"post-title"},ss={key:0,class:"row-actions"},as={class:"edit"},rs=["href"],is=["href"],Ge="keyword-rank-tracker-keyword-ranking-pages-table",ns={__name:"KeywordRankingPagesTable",props:{paginatedRows:Object,loading:Boolean},setup(l){const o="aioseo-pro",t=V(),s=l,m=K(5),c=K(null),{editPost:n,viewPost:A}=We(),{processChangeItemsPerPage:d,processPagination:p,wpTableKey:y,wpTableLoading:f}=me({fetchData:t.fetchKeywordsRankingPages,tableId:Ge,tableRef:c.value,resultsPerPage:m}),k=Q(()=>s.paginatedRows.totals.page),b=Q(()=>[{slug:"name",label:a("Title",o)},{slug:"clicks",label:a("Clicks",o),width:"100px"},{slug:"ctr",label:a("Avg. CTR",o),width:"100px"},{slug:"impressions",label:a("Impressions",o),width:"110px"},{slug:"position",label:a("Position",o),width:"100px"}]);return(w,v)=>(h(),S(e(ge),{ref_key:"table",ref:c,id:Ge,"additional-filters":[],columns:b.value,filters:[],"initial-items-per-page":m.value,"initial-page-number":k.value,"initial-search-term":"",key:e(y),loading:e(f)||l.loading,rows:l.paginatedRows.rows,"show-bulk-actions":!1,"show-header":!1,"show-table-footer":!0,totals:l.paginatedRows.totals,"show-items-per-page":!0,onPaginate:e(p),"show-search":!1,"show-pagination":!0,onProcessChangeItemsPerPage:e(d)},{name:r(({row:C})=>[i("div",os,[i("b",null,u(C.objectTitle),1)]),C!=null&&C.objectId?(h(),I("div",ss,[i("span",as,[i("a",{href:C.context.permalink,target:"_blank"},u(e(A)(C.context.postType.singular)),9,rs),B(" | "),i("a",{href:C.context.editLink,target:"_blank"},u(e(n)(C.context.postType.singular)),9,is)])])):M("",!0)]),clicks:r(({row:C})=>[B(u(e(X).compactNumber(C.clicks)),1)]),ctr:r(({row:C})=>[B(u(e(X).compactNumber(C.ctr))+"% ",1)]),impressions:r(({row:C})=>[B(u(e(X).compactNumber(C.impressions)),1)]),position:r(({row:C})=>[C.difference.comparison?(h(),S(e(ve),{key:0,type:"position",total:C.position,difference:C.difference.position,"tooltip-offset":"-150px,0"},null,8,["total","difference"])):M("",!0)]),_:1},8,["columns","initial-items-per-page","initial-page-number","loading","rows","totals","onPaginate","onProcessChangeItemsPerPage"]))}},ls={class:"post-title"},cs={class:"row-actions"},ds={key:0,class:"edit"},us=["onClick"],ps={key:1,class:"delete"},hs=["onClick"],ms={key:1},gs={key:1},fs={key:1},ks={key:1},ys={key:1},Fe="keyword-rank-tracker-related-keywords-table",ws={__name:"RelatedKeywordsTable",props:{paginatedRows:Object,loading:Boolean},setup(l){const o="aioseo-pro",t=V(),s={position:a("Position",o),addKeyword:a("Add Keyword",o),removeFromKrt:a("Remove from KRT",o)},m=[{label:H.add,value:"add"}],c=l,n=K(null),{wpTableKey:A,wpTableLoading:d}=me({tableId:Fe,tableRef:n.value}),p=Q(()=>[{slug:"name",label:a("Keyword",o)},{slug:"clicks",label:a("Clicks",o),width:"100px"},{slug:"ctr",label:a("Avg. CTR",o),width:"100px"},{slug:"impressions",label:a("Impressions",o),width:"110px"},{slug:"position",label:a("Position",o),width:"100px"},{slug:"history",label:a("Position History",o),width:"140px"}]),y=(v,C)=>{var E;let _=((E=v.statistics)==null?void 0:E[C])??"";switch(C){case"ctr":_=_!==""?X.compactNumber(_)+"%":_;break;case"clicks":case"impressions":_=_!==""?X.compactNumber(_):_;break;case"position":_=_!==""?Math.round(_).toFixed(0):_;break}return _},f=({action:v,selectedRows:C})=>{C.length&&(C=C.map(_=>c.paginatedRows.rows[_].name),v==="add"&&b(C))},k=v=>{var C;return(C=v.statistics)!=null&&C.history?[{name:s.position,data:v.statistics.history.map(_=>({x:_.date,y:_.position}))}]:[]},b=v=>{t.toggleModal({modal:"modalOpenAddKeywords",open:!0,relatedKeywords:v})},w=v=>{t.toggleModal({modal:"modalOpenDeleteKeywords",open:!0,keywords:[t.keywords.all.rows.find(C=>C.name===v.name)]})};return(v,C)=>(h(),S(e(ge),{ref_key:"table",ref:n,id:Fe,"additional-filters":[],"bulk-options":m,columns:p.value,filters:[],"initial-page-number":1,"initial-search-term":"",key:e(A),loading:e(d)||l.loading,rows:l.paginatedRows.rows,"show-bulk-actions":"","show-header":!0,"show-table-footer":!1,"show-search":!1,"show-pagination":!1,totals:{},onProcessBulkAction:f},{name:r(({row:_})=>[i("div",ls,[i("b",null,u(_.name),1)]),i("div",cs,[e(t).keywords.all.rows.find(E=>E.name===_.name)?(h(),I("span",ps,[i("a",{href:"#",onClick:x(E=>w(_),["prevent","exact"])},u(s.removeFromKrt),9,hs)])):(h(),I("span",ds,[i("a",{href:"#",onClick:x(E=>b([_.name]),["prevent","exact"])},u(s.addKeyword),9,us)]))])]),clicks:r(({row:_})=>[_.statistics===null?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",ms,u(y(_,"clicks")),1))]),ctr:r(({row:_})=>[_.statistics===null?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",gs,u(y(_,"ctr")),1))]),impressions:r(({row:_})=>[_.statistics===null?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",fs,u(y(_,"impressions")),1))]),position:r(({row:_})=>[_.statistics===null?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",ks,u(y(_,"position")),1))]),history:r(({row:_})=>[_.statistics===null?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",ys,[k(_).length?(h(),S(e(ue),{key:0,series:k(_),height:25,preset:"overview","chart-overrides":{tooltip:{y:{formatter:E=>parseFloat(E).toFixed(2)}}}},null,8,["series","chart-overrides"])):M("",!0)]))]),_:1},8,["columns","loading","rows"]))}},vs=["disabled"],As={class:"btn-favorite"},bs={class:"post-title"},_s=["onClick"],Cs={class:"row-actions"},Bs={class:"edit"},Is=["href"],Es=["onClick"],Ss=["onClick"],xs={key:0},Qs=["onClick"],Ts={class:"delete"},Ps=["onClick"],Ks={class:"inner-tabs"},Ds=["onClick"],Rs=i("span",null,"|",-1),Zs=["onClick"],Ms={key:1},Gs={key:1},Fs={key:1},Os={key:1},Us={key:1},Oe="keyword-rank-tracker-keywords-table",st={__name:"KeywordsTable",props:{canEditRow:{type:Boolean,default:!0},paginatedKeywords:Object,showAdditionalFilters:{type:Boolean,default:!0},showTableFooter:{type:Boolean,default:!0},showHeader:{type:Boolean,default:!0},fetchData:{type:Function,default(l){return V().fetchKeywords(l)}},outerGroup:Object,loading:Boolean,changeItemsPerPageSlug:{type:String,default:"searchStatisticsKrtKeywords"}},setup(l){const o="all-in-one-seo-pack",t=V(),s=xe(),m={addToGroup:a("Add to Group",o),editGroup:a("Edit Group(s)",o),removeFromGroup:a("Remove from Group",o),position:a("Position",o),viewInGoogle:a("View in Google",o),relatedKeywords:a("Related Keywords",o),keywordRankingPages:a("Keyword Ranking Pages",o)},c=[{label:H.delete,value:"delete"},{label:m.addToGroup,value:"assignGroup"}],n=l,A=K(null),d=K([]),p=K("related-keywords-table"),y=K(!1),{orderBy:f,orderDir:k,processAdditionalFilters:b,processChangeItemsPerPage:w,processFilterTable:v,processPagination:C,processSearch:_,processSort:E,searchTerm:Z,wpTableKey:le,wpTableLoading:W}=me({changeItemsPerPageSlug:n.changeItemsPerPageSlug,fetchData:n.fetchData,tableId:Oe,tableRef:A.value}),z=Q(()=>n.paginatedKeywords.totals.page),D=Q(()=>{if(!n.showAdditionalFilters||!t.groups.count)return[];const F=[{label:a("All Groups",o),value:"all"},...t.groups.all.rows.map(O=>({...O,label:t.favoriteGroup.label===O.label?"&starf;":O.label}))];return[{label:a("Filter by Group",o),name:"group",options:F}]}),se=Q(()=>[{slug:"all",name:"All",active:n.paginatedKeywords.filter==="all"},{slug:"favorited",name:"Favorited",active:n.paginatedKeywords.filter==="favorited"}]),U=Q(()=>{const F=[{slug:"favorited",label:"",width:"50px"},{slug:"name",label:a("Keyword",o),sortable:1<n.paginatedKeywords.totals.total,sortDir:f.value==="name"?k.value:"asc",sorted:f.value==="name"},{slug:"clicks",label:a("Clicks",o),sortable:1<n.paginatedKeywords.totals.total,sortDir:f.value==="clicks"?k.value:"asc",sorted:f.value==="clicks",width:"100px"},{slug:"ctr",label:a("Avg. CTR",o),sortable:1<n.paginatedKeywords.totals.total,sortDir:f.value==="ctr"?k.value:"asc",sorted:f.value==="ctr",width:"100px"},{slug:"impressions",label:a("Impressions",o),sortable:1<n.paginatedKeywords.totals.total,sortDir:f.value==="impressions"?k.value:"asc",sorted:f.value==="impressions",width:"110px"},{slug:"position",label:a("Position",o),sortable:1<n.paginatedKeywords.totals.total,sortDir:f.value==="position"?k.value:"asc",sorted:f.value==="position",width:"100px"},{slug:"history",label:a("Position History",o),width:"140px"}];return n.canEditRow&&F.push({slug:"buttons",label:"",width:"60px"}),F});_e(()=>[t.keywords.count,t.range],()=>{t.keywords.count&&(A.value.activeRow=null)});const L=(F,O)=>{var ke;let G=((ke=F.statistics)==null?void 0:ke[O])??"";switch(O){case"ctr":G=G!==""?parseFloat(G)+"%":G;break;case"clicks":case"impressions":G=G!==""?X.compactNumber(G):G;break;case"position":G=G!==""?Math.round(G).toFixed(0):G;break}return G},Y=({action:F,selectedRows:O})=>{O.length&&(O=n.paginatedKeywords.rows.filter(G=>O.includes(String(G.id))),F==="delete"&&t.toggleModal({modal:"modalOpenDeleteKeywords",open:!0,keywords:O,fetchKeywordsCallback:n.fetchData}),F==="assignGroup"&&t.toggleModal({modal:"modalOpenAssignGroups",open:!0,keywords:O.map(G=>({...G,groups:[]})),fetchKeywordsCallback:n.fetchData}))},q=F=>{var O;return(O=F.statistics)!=null&&O.history?[{name:m.position,data:F.statistics.history.map(G=>({x:G.date,y:G.position}))}]:[]},P=async(F,O)=>{d.value[O]=!0;try{await t.updateKeyword({id:F.id,payload:{favorited:!F.favorited}}),await n.fetchData(),await t.fetchGroups(),t.maybeFetchStatistics({context:"groups"})}catch(G){console.error(G)}finally{d.value=[]}},ae=F=>`https://www.google.com/search?q=${encodeURIComponent(F)}`,oe=async(F,O)=>{t.resetRelatedKeywords(),t.resetKeywordsRankingPages(),y.value=!0,p.value=F;try{F==="keyword-ranking-pages-table"&&await t.fetchKeywordsRankingPages({keywords:[O.name]}),F==="related-keywords-table"&&(await t.fetchRelatedKeywords(O.name),t.maybeFetchRelatedKeywordsStatistics())}catch(G){console.error(G)}finally{y.value=!1}},re=F=>{t.toggleModal({modal:"modalOpenDeleteKeywords",open:!0,keywords:[F],fetchKeywordsCallback:n.fetchData})},fe=async(F,O,G)=>{G(F),await we(),A.value.activeRow!==null&&await oe("related-keywords-table",O)},De=async(F,O)=>{try{W.value=!0,await t.updateRelationships({keywords:[F],groups:F.groups.filter(G=>G.id!==O.id)}),await t.fetchGroups().then(()=>{t.maybeFetchStatistics({context:"groups"}),n.fetchData({updateKeywords:!0})})}catch(G){console.error(G)}finally{W.value=!1}};return(F,O)=>{var ke;const G=R("base-button");return h(),S(e(ge),{ref_key:"table",ref:A,id:Oe,"additional-filters":D.value,"bulk-options":c,columns:U.value,filters:se.value,"initial-items-per-page":e(s).settings.tablePagination[l.changeItemsPerPageSlug],"initial-page-number":z.value,"initial-search-term":((ke=l.paginatedKeywords)==null?void 0:ke.searchTerm)||e(Z),key:e(le),loading:e(W)||l.loading,rows:l.paginatedKeywords.rows,"show-bulk-actions":"","show-header":l.showHeader,"show-table-footer":l.showTableFooter,totals:l.paginatedKeywords.totals,"show-items-per-page":"",onFilterTable:e(v),onPaginate:e(C),onProcessAdditionalFilters:O[0]||(O[0]=T=>e(b)({filters:T.filters,term:T.searchTerm,number:T.pageNumber})),onProcessBulkAction:Y,onProcessChangeItemsPerPage:e(w),onSearch:e(_),onSortColumn:e(E)},{filters:r(({slug:T,active:$})=>[i("button",{type:"button",class:te([`btn-filter-favorited button ${T}`,{"btn-filter-favorited--not-active":!$}]),disabled:e(W)||l.loading,tabindex:"-1"},[g(e(Se),{active:!0})],10,vs)]),favorited:r(({row:T,index:$})=>[i("div",As,[g(G,{class:te(["btn-favorite__button",{"btn-favorite__button--active":T.favorited}]),loading:d.value[$],onClick:x(Ae=>P(T,$),["exact"])},{default:r(()=>[g(e(Se),{width:"20",active:T.favorited},null,8,["active"])]),_:2},1032,["class","loading","onClick"])])]),name:r(({row:T,index:$,editRow:Ae})=>[i("div",bs,[i("a",{href:"#",onClick:x(ce=>fe($,T,Ae),["prevent","exact"])},u(T.name),9,_s)]),i("div",Cs,[i("span",Bs,[i("a",{href:ae(T.name),target:"_blank"},[B(u(m.viewInGoogle)+" ",1),g(e(et))],8,Is),B(" | "),i("span",null,[T.groups.length?(h(),I("a",{key:0,href:"#",onClick:x(ce=>e(t).toggleModal({modal:"modalOpenAssignGroups",open:!0,keywords:[T],fetchKeywordsCallback:l.fetchData}),["prevent","exact"])},u(m.editGroup),9,Es)):(h(),I("a",{key:1,href:"#",onClick:x(ce=>e(t).toggleModal({modal:"modalOpenAssignGroups",open:!0,keywords:[T],fetchKeywordsCallback:l.fetchData}),["prevent","exact"])},u(m.addToGroup),9,Ss)),B(" | ")]),T.groups.length&&l.outerGroup?(h(),I("span",xs,[i("a",{href:"#",onClick:x(ce=>De(T,l.outerGroup),["prevent","exact"])},u(m.removeFromGroup),9,Qs),B(" | ")])):M("",!0)]),i("span",Ts,[i("a",{href:"#",onClick:x(ce=>re(T),["prevent","exact"])},u(e(H).delete),9,Ps)])])]),"edit-row":r(({row:T})=>[i("div",Ks,[i("a",{href:"#",class:te({active:p.value==="related-keywords-table"}),onClick:x($=>oe("related-keywords-table",T),["prevent"])},u(m.relatedKeywords),11,Ds),Rs,i("a",{href:"#",class:te({active:p.value==="keyword-ranking-pages-table"}),onClick:x($=>oe("keyword-ranking-pages-table",T),["prevent"])},u(m.keywordRankingPages),11,Zs)]),p.value==="related-keywords-table"?(h(),S(e(ws),{key:0,class:"inner-table","paginated-rows":e(t).keywords.related.paginated,loading:y.value},null,8,["paginated-rows","loading"])):M("",!0),p.value==="keyword-ranking-pages-table"?(h(),S(e(ns),{key:1,class:"inner-table","paginated-rows":e(t).keywords.rankingPages.paginated,loading:y.value},null,8,["paginated-rows","loading"])):M("",!0)]),clicks:r(({row:T})=>[T.statistics===null?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",Ms,u(L(T,"clicks")),1))]),ctr:r(({row:T})=>[T.statistics===null?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",Gs,u(L(T,"ctr")),1))]),impressions:r(({row:T})=>[T.statistics===null?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",Fs,u(L(T,"impressions")),1))]),position:r(({row:T})=>[T.statistics===null?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",Os,u(L(T,"position")),1))]),history:r(({row:T})=>[T.statistics===null?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",Us,[q(T).length?(h(),S(e(ue),{key:0,series:q(T),height:25,preset:"overview","chart-overrides":{tooltip:{y:{formatter:$=>parseFloat($).toFixed(2)}}}},null,8,["series","chart-overrides"])):M("",!0)]))]),buttons:r(({row:T,index:$,editRow:Ae})=>{var ce,Re;return[g(G,{onClick:qa=>fe($,T,Ae),type:((ce=A.value)==null?void 0:ce.activeRow)===$?"blue":"gray",disabled:e(W)||l.loading,class:te([{active:((Re=A.value)==null?void 0:Re.activeRow)===$},"btn-toggle-row"])},{default:r(()=>[g(e(Qe),{width:"18"})]),_:2},1032,["onClick","type","disabled","class"])]}),_:1},8,["additional-filters","columns","filters","initial-items-per-page","initial-page-number","initial-search-term","loading","rows","show-header","show-table-footer","totals","onFilterTable","onPaginate","onProcessChangeItemsPerPage","onSearch","onSortColumn"])}}},Vs={class:"post-title"},Ls=["onClick"],qs={class:"row-actions"},zs={key:0,class:"edit"},Ns=["onClick"],Hs={class:"delete"},js=["onClick"],Xs={key:1},Ys={key:1},$s={key:1},Js={key:1},Ue="searchStatisticsKrtGroups",Ve="keyword-rank-tracker-groups-table",Ws={__name:"GroupsTable",props:{groups:Object,showTableFooter:{type:Boolean,default(){return!0}},showHeader:{type:Boolean,default(){return!0}}},setup(l){const o="all-in-one-seo-pack",t=V(),s=xe(),m=[{label:H.delete,value:"delete"}],c=l,n=K(null),A=K(!1),{orderBy:d,orderDir:p,processAdditionalFilters:y,processChangeItemsPerPage:f,processFilterTable:k,processPagination:b,processSearch:w,processSort:v,searchTerm:C,wpTableKey:_,wpTableLoading:E}=me({changeItemsPerPageSlug:Ue,fetchData:t.fetchGroups,tableId:Ve,tableRef:n.value}),Z=Q(()=>c.groups.totals.page),le=Q(()=>[{slug:"name",label:a("Group",o),sortable:1<c.groups.totals.total,sortDir:d.value==="name"?p.value:"asc",sorted:d.value==="name"},{slug:"keywords_qty",label:a("Keywords",o),width:"100px"},{slug:"clicks",label:a("Clicks",o),sortable:1<c.groups.totals.total,sortDir:d.value==="clicks"?p.value:"asc",sorted:d.value==="clicks",width:"100px"},{slug:"ctr",label:a("Avg. CTR",o),sortable:1<c.groups.totals.total,sortDir:d.value==="ctr"?p.value:"asc",sorted:d.value==="ctr",width:"100px"},{slug:"impressions",label:a("Impressions",o),sortable:1<c.groups.totals.total,sortDir:d.value==="impressions"?p.value:"asc",sorted:d.value==="impressions",width:"100px"},{slug:"position",label:a("Avg. Position",o),sortable:1<c.groups.totals.total,sortDir:d.value==="position"?p.value:"asc",sorted:d.value==="position",width:"140px"},{slug:"buttons",label:"",width:"60px"}]),W=(L,Y)=>{var P;let q=L.keywordsQty?((P=L.statistics)==null?void 0:P[Y])??"":"0";switch(Y){case"ctr":q=q!==""?X.compactNumber(q)+"%":q;break;case"clicks":case"impressions":q=q!==""?X.compactNumber(q):q;break;case"position":q=q!==""?Math.round(q).toFixed(0):q;break}return q},z=(L,Y)=>{v(L,Y).finally(()=>{n.value.editRow(null)})},D=({action:L,selectedRows:Y})=>{t.groups.selected=[],Y.length&&(t.groups.selected=t.groups.paginated.rows.filter(q=>Y.includes(String(q.id))),L==="delete"&&(t.modalOpenDeleteGroups=!0))},se=L=>{t.groups.selected=[L],t.modalOpenDeleteGroups=!0,n.value.activeRow!==null&&n.value.activeRow===c.groups.rows.findIndex(Y=>Y.id===L.id)&&(n.value.activeRow=null)},U=async(L,Y,q)=>{if(q(L),await we(),n.value.activeRow!==null){t.resetGroupsTableKeywords(),A.value=!0;try{await t.fetchGroupsTableKeywords({ids:Y.keywords.map(P=>P.id)})}catch(P){console.error(P)}finally{A.value=!1}}};return(L,Y)=>{const q=R("base-button");return h(),S(e(ge),{ref_key:"table",ref:n,id:Ve,"additional-filters":[],"bulk-options":m,columns:le.value,filters:[],"initial-items-per-page":e(s).settings.tablePagination[Ue],"initial-page-number":Z.value,"initial-search-term":e(C),key:e(_),loading:e(E),rows:l.groups.rows,"show-bulk-actions":"","show-header":l.showHeader,"show-table-footer":l.showTableFooter,totals:l.groups.totals,"show-items-per-page":"",onFilterTable:e(k),onPaginate:e(b),onProcessAdditionalFilters:e(y),onProcessBulkAction:D,onProcessChangeItemsPerPage:e(f),onSearch:e(w),onSortColumn:z},{name:r(({row:P,index:ae,editRow:oe})=>[i("div",Vs,[i("a",{href:"#",onClick:x(re=>U(ae,P,oe),["prevent","exact"])},[e(t).favoriteGroup.label===P.name?(h(),S(e(Se),{key:0,width:"20",active:!0})):(h(),I(de,{key:1},[B(u(P.name),1)],64))],8,Ls)]),i("div",qs,[e(t).favoriteGroup.label!==P.name?(h(),I("span",zs,[i("a",{href:"#",onClick:x(re=>{e(t).groups.selected=[P],e(t).toggleModal({modal:"modalOpenUpdateGroup",open:!0})},["prevent","exact"])},u(e(H).edit),9,Ns),B(" | ")])):M("",!0),i("span",Hs,[i("a",{href:"#",onClick:x(re=>se(P),["prevent","exact"])},u(e(H).delete),9,js)])])]),"edit-row":r(({index:P})=>[g(e(st),{class:"inner-table","show-additional-filters":!1,"paginated-keywords":e(t).groups.tableKeywords.paginated,"fetch-data":ae=>{var oe;return e(t).fetchGroupsTableKeywords({...ae,ids:((oe=e(t).groups.paginated.rows[P])==null?void 0:oe.keywords.map(re=>re.id))||[]})},"can-edit-row":!1,"outer-group":e(t).groups.paginated.rows[P],loading:A.value,"change-items-per-page-slug":"searchStatisticsKrtGroupsTableKeywords"},null,8,["paginated-keywords","fetch-data","outer-group","loading"])]),keywords_qty:r(({row:P})=>[B(u(P.keywordsQty),1)]),clicks:r(({row:P})=>[P.statistics===null&&P.keywordsQty?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",Xs,u(W(P,"clicks")),1))]),ctr:r(({row:P})=>[P.statistics===null&&P.keywordsQty?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",Ys,u(W(P,"ctr")),1))]),impressions:r(({row:P})=>[P.statistics===null&&P.keywordsQty?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",$s,u(W(P,"impressions")),1))]),position:r(({row:P})=>[P.statistics===null&&P.keywordsQty?(h(),S(e(j),{key:0,dark:""})):(h(),I("div",Js,u(W(P,"position")),1))]),buttons:r(({row:P,index:ae,editRow:oe})=>{var re,fe;return[g(q,{onClick:De=>U(ae,P,oe),type:((re=n.value)==null?void 0:re.activeRow)===ae?"blue":"gray",disabled:e(E),class:te([{active:((fe=n.value)==null?void 0:fe.activeRow)===ae},"btn-toggle-row"])},{default:r(()=>[g(e(Qe),{width:"18"})]),_:2},1032,["onClick","type","disabled","class"])]}),_:1},8,["columns","initial-items-per-page","initial-page-number","initial-search-term","loading","rows","show-header","show-table-footer","totals","onFilterTable","onPaginate","onProcessAdditionalFilters","onProcessChangeItemsPerPage","onSearch"])}}},ea=N(Ws,[["__scopeId","data-v-d5867cf9"]]),ta={class:"keyword-rank-tracker-graphs"},oa={class:"aioseo-settings-row aioseo-settings-row--graph no-border"},sa={class:"settings-name"},aa={class:"name"},ra={class:"aioseo-settings-row aioseo-settings-row--graph no-border"},ia={class:"settings-name"},na={class:"name"},la={__name:"GroupsGraphs",setup(l){const o="aioseo-pro",t=V(),s={topPositions:a("Top Positions",o),estimatedTraffic:a("Estimated Traffic",o)},m=Q(()=>t.isFetchingStatistics.groups),c=Q(()=>{const d=t.keywords.all.rows.filter(k=>k.groups.length&&k.statistics);if(!d.length)return[];const p={};for(const k of t.groups.all.rows){const b=d.filter(w=>w.groups.find(v=>Number(v.id)===Number(k.id)));b.length&&(p[k.name]=b)}const y=5,f=[];for(const k in p){const b=[];for(let w=0;w<y;w++){const v=p[k].filter(E=>E.statistics.history.length).map(E=>E.statistics.history[w]),C=v[0].date,_=v.reduce((E,Z)=>E+Z.clicks,0);b.push({date:C,total:_})}f.push({name:t.favoriteGroup.label===k?'<span style="color: #f18200">&starf;</span>':k,data:b.map(w=>({x:w.date,y:w.total}))})}return f}),n=Q(()=>{const d=t.keywords.all.rows.filter(y=>y.groups.length&&y.statistics);if(!d.length)return[];const p=A(d);return[{name:a("Keywords",o),data:[{x:a("Top 3 Position",o),y:p.top3,fillColor:"#005AE0"},{x:a("4-10 Position",o),y:p.top10,fillColor:"#00AA63"},{x:a("11-50 Position",o),y:p.top50,fillColor:"#F18200"},{x:a("50-100 Position",o),y:p.top100,fillColor:"#DF2A4A"}]}]}),A=d=>{const p={top3:0,top10:0,top50:0,top100:0},y=d.length;if(y===0)return p;d.forEach(k=>{const b=Math.round(k.statistics.position);3>=b?p.top3++:10>=b?p.top10++:50>=b?p.top50++:p.top100++});let f=0;for(const k in p){if(k==="top100"){p[k]=(100-f).toFixed(2);break}p[k]=(p[k]/y*100).toFixed(2),f+=parseFloat(p[k])}return p};return(d,p)=>(h(),I("div",ta,[i("div",oa,[i("div",sa,[i("div",aa,u(s.estimatedTraffic),1)]),g(e(ue),{series:c.value,loading:m.value,"legend-style":"simple"},null,8,["series","loading"])]),i("div",ra,[i("div",ia,[i("div",na,u(s.topPositions),1)]),g(e(ue),{series:n.value,loading:m.value,preset:"keywordsDistribution"},null,8,["series","loading"])])]))}},ca=N(la,[["__scopeId","data-v-dca3b75c"]]),da={class:"keyword-rank-tracker-summary"},ua={class:"keyword-rank-tracker-summary__vision__header"},pa=["innerHTML"],ha={__name:"GroupsSummary",setup(l){const o="all-in-one-seo-pack",t=V(),s=[{name:"groups",label:"Total Groups",tooltip:J(a("The %1$stotal number of groups of keywords that are being tracked%2$s for your website.",o),"<strong>","</strong>")},{name:"keywords",label:"Total Keywords",tooltip:J(a("The %1$stotal number of keywords that are assigned to your groups%2$s.",o),"<strong>","</strong>")},{name:"impressions",label:"Search Impressions",tooltip:J(a("The %1$stotal number of impressions the keywords from your groups have aggregated in search results%2$s within the selected timeframe.",o),"<strong>","</strong>")},{name:"clicks",label:"Clicks",tooltip:J(a("The %1$stotal number of clicks the keywords from your groups have aggregated from search results%2$s within the selected timeframe.",o),"<strong>","</strong>")},{name:"ctr",label:"Avg. CTR",tooltip:J(a("The %1$saverage click-through rate for the keywords from your groups in search results%2$s within the selected timeframe.",o),"<strong>","</strong>")}],m=Q(()=>t.isFetchingStatistics.groups),c=Q(()=>{const n=t.groups.all.rows.filter(f=>f.statistics),A=n.length?X.compactNumber(n.map(f=>f.statistics.clicks).reduce((f,k)=>f+k,0)):0,d=n.length?X.compactNumber(n.map(f=>f.statistics.impressions).reduce((f,k)=>f+k,0)):0,p=n.length?(n.map(f=>Number(f.statistics.position)).reduce((f,k)=>f+k,0)/n.length).toFixed(0):0,y=n.length?(n.map(f=>Number(f.statistics.ctr)).reduce((f,k)=>f+k,0)/n.length).toFixed(2)+"%":0;return{groups:t.groups.all.rows.length,keywords:t.keywords.all.rows.filter(f=>f.groups.length&&f.statistics).length,clicks:A,impressions:d,position:p,ctr:y}});return(n,A)=>(h(),I("div",da,[(h(),I(de,null,Ce(s,(d,p)=>{var y,f;return i("div",{key:p,class:"keyword-rank-tracker-summary__vision"},[i("div",ua,[B(u(d.label)+" ",1),d.tooltip?(h(),S(e(Be),{key:0},{tooltip:r(()=>[i("span",{innerHTML:d.tooltip},null,8,pa)]),default:r(()=>[g(e(Te))]),_:2},1024)):M("",!0)]),m.value&&d.name!=="groups"?(h(),S(e(j),{key:0,dark:""})):M("",!0),i("div",{class:te(["keyword-rank-tracker-summary__vision__body",{"keyword-rank-tracker-summary__vision__body--invisible":m.value&&d.name!=="groups"}])},[B(u(c.value[d.name])+" ",1),(f=(y=e(t).groups.statistics)==null?void 0:y.difference)!=null&&f[d.name]?(h(),S(e(ve),{key:0,"show-current":!1,type:d.name,difference:Number(e(t).groups.statistics.difference[d.name])},null,8,["type","difference"])):M("",!0)],2)])}),64))]))}},ma=N(ha,[["__scopeId","data-v-940ba7f6"]]),ga={class:"keyword-rank-tracker-main"},fa={class:"aioseo-settings-row aioseo-settings-row--summary"},ka={__name:"Groups",setup(l){const o=V();return(t,s)=>(h(),I("div",ga,[i("div",fa,[g(e(ma))]),g(e(ca)),g(e(ea),{groups:e(o).groups.paginated},null,8,["groups"])]))}},ya=N(ka,[["__scopeId","data-v-05df8d7e"]]),wa={class:"keyword-rank-tracker-tabs"},at={__name:"Tabs",props:{activeTab:{type:String,default:"keywords"}},emits:["update:activeTab"],setup(l,{emit:o}){const t="all-in-one-seo-pack",s=V(),m=o,c={addKeywords:a("Add Keywords",t),createGroup:a("Create Group",t)},n=[{slug:"keywords",name:a("Keywords",t)},{slug:"groups",name:a("Groups",t)}];return(A,d)=>{const p=R("base-button");return h(),I("div",wa,[g(e(Pe),{tabs:n,active:l.activeTab,onChanged:d[2]||(d[2]=y=>{m("update:activeTab",y)})},{button:r(()=>[l.activeTab==="keywords"?(h(),S(p,{key:0,class:"btn-add-keywords",size:"small-table",type:"blue",onClick:d[0]||(d[0]=x(y=>e(s).toggleModal({modal:"modalOpenAddKeywords",open:!0}),["exact"]))},{default:r(()=>[B(u(c.addKeywords),1)]),_:1})):M("",!0),l.activeTab==="groups"?(h(),S(p,{key:1,class:"btn-create-group",size:"small-table",type:"blue",onClick:d[1]||(d[1]=x(y=>e(s).toggleModal({modal:"modalOpenCreateGroup",open:!0}),["exact"]))},{default:r(()=>[B(u(c.createGroup),1)]),_:1})):M("",!0)]),_:1},8,["active"]),g(je,{name:"route-fade",mode:"out-in"},{default:r(()=>[Ee(A.$slots,"tab-content")]),_:3})])}}},va={class:"keyword-rank-tracker-graphs"},Aa={__name:"KeywordsGraphs",setup(l){const o="all-in-one-seo-pack",t=V(),s=Q(()=>t.isFetchingStatistics.keywords),m=Q(()=>{var d,p;const n=(d=t.keywords.statistics)==null?void 0:d.distribution,A=(p=t.keywords.statistics)==null?void 0:p.distributionIntervals;return!n||!A?[]:[{name:a("Top 3 Position",o),data:A.map(y=>({x:y.date,y:y.top3}))},{name:a("4-10 Position",o),data:A.map(y=>({x:y.date,y:y.top10}))},{name:a("11-50 Position",o),data:A.map(y=>({x:y.date,y:y.top50}))},{name:a("50-100 Position",o),data:A.map(y=>({x:y.date,y:y.top100}))}]}),c=Q(()=>{var A;const n=(A=t.keywords.statistics)==null?void 0:A.distribution;return n?[{name:a("Keywords",o),data:[{x:a("Top 3 Position",o),y:n.top3,fillColor:"#005AE0"},{x:a("4-10 Position",o),y:n.top10,fillColor:"#00AA63"},{x:a("11-50 Position",o),y:n.top50,fillColor:"#F18200"},{x:a("50-100 Position",o),y:n.top100,fillColor:"#DF2A4A"}]}]:[]});return(n,A)=>(h(),I("div",va,[g(e(ue),{series:m.value,loading:s.value,"legend-style":"simple"},null,8,["series","loading"]),g(e(ue),{series:c.value,loading:s.value,preset:"keywordsDistribution"},null,8,["series","loading"])]))}},ba=N(Aa,[["__scopeId","data-v-f5d86067"]]),_a={class:"keyword-rank-tracker-summary"},Ca={class:"keyword-rank-tracker-summary__vision__header"},Ba=["innerHTML"],Ia={__name:"KeywordsSummary",setup(l){const o="all-in-one-seo-pack",t=V(),s=[{name:"keywords",label:"Total Keywords",tooltip:J(a("The %1$stotal number of keywords that are being tracked%2$s for your website.",o),"<strong>","</strong>")},{name:"impressions",label:"Search Impressions",tooltip:J(a("The %1$stotal number of impressions your tracked keywords have aggregated in search results%2$s within the selected timeframe.",o),"<strong>","</strong>")},{name:"clicks",label:"Clicks",tooltip:J(a("The %1$stotal number of clicks your tracked keywords have aggregated from search results%2$s within the selected timeframe.",o),"<strong>","</strong>")},{name:"ctr",label:"Avg. CTR",tooltip:J(a("The %1$saverage click-through rate of your tracked keywords in search results%2$s within the selected timeframe.",o),"<strong>","</strong>")}],m=Q(()=>t.isFetchingStatistics.keywords),c=Q(()=>{const n=t.keywords.all.rows.filter(f=>f.statistics),A=n.length?X.compactNumber(n.map(f=>f.statistics.clicks).reduce((f,k)=>f+k,0)):0,d=n.length?X.compactNumber(n.map(f=>f.statistics.impressions).reduce((f,k)=>f+k,0)):0,p=n.length?(n.map(f=>Number(f.statistics.position)).reduce((f,k)=>f+k,0)/n.length).toFixed(0):0,y=n.length?(n.map(f=>Number(f.statistics.ctr)).reduce((f,k)=>f+k,0)/n.length).toFixed(2)+"%":0;return{keywords:t.keywords.all.rows.length,clicks:A,impressions:d,position:p,ctr:y}});return(n,A)=>(h(),I("div",_a,[(h(),I(de,null,Ce(s,(d,p)=>{var y,f;return i("div",{key:p,class:"keyword-rank-tracker-summary__vision"},[i("div",Ca,[B(u(d.label)+" ",1),d.tooltip?(h(),S(e(Be),{key:0},{tooltip:r(()=>[i("span",{innerHTML:d.tooltip},null,8,Ba)]),default:r(()=>[g(e(Te))]),_:2},1024)):M("",!0)]),m.value&&d.name!=="keywords"?(h(),S(e(j),{key:0,dark:""})):M("",!0),i("div",{class:te(["keyword-rank-tracker-summary__vision__body",{"keyword-rank-tracker-summary__vision__body--invisible":m.value&&d.name!=="keywords"}])},[B(u(c.value[d.name])+" ",1),(f=(y=e(t).keywords.statistics)==null?void 0:y.difference)!=null&&f[d.name]?(h(),S(e(ve),{key:0,"show-current":!1,type:d.name,difference:Number(e(t).keywords.statistics.difference[d.name])},null,8,["type","difference"])):M("",!0)],2)])}),64))]))}},Ea=N(Ia,[["__scopeId","data-v-c90704ec"]]),Sa={class:"keyword-rank-tracker-main"},xa={class:"aioseo-settings-row aioseo-settings-row--summary"},Qa={class:"aioseo-settings-row aioseo-settings-row--graphs"},Ta={class:"settings-name"},Pa={class:"name"},Ka={__name:"Keywords",setup(l){const o="all-in-one-seo-pack",t=V(),s={topPositions:a("Top Positions",o)};return(m,c)=>(h(),I("div",Sa,[i("div",xa,[g(e(Ea))]),i("div",Qa,[i("div",Ta,[i("div",Pa,u(s.topPositions),1)]),g(e(ba))]),g(e(st),{"paginated-keywords":e(t).keywords.paginated},null,8,["paginated-keywords"])]))}},rt=N(Ka,[["__scopeId","data-v-673b8029"]]),Da={__name:"RankTracker",setup(l){const o=V(),t=pe(),s=K("keywords");return Xe(()=>{if(t.isConnected&&!t.shouldShowSampleReports&&!o.keywords.all.rows.length)try{const m=new URLSearchParams(document.location.search)||{};m!=null&&m.get("search")&&(o.keywords.paginated.searchTerm=m.get("search"),Ke("search")),o.maybeUpdateKeywords(),o.maybeUpdateGroups()}catch(m){console.error(m)}}),(m,c)=>(h(),I("div",null,[g(e(at),{"active-tab":s.value,"onUpdate:activeTab":c[0]||(c[0]=n=>s.value=n)},{"tab-content":r(()=>[(h(),S(Ye(e(o)[s.value].count?s.value==="keywords"?e(rt):e(ya):e(ts)),{context:s.value},null,8,["context"]))]),_:1},8,["active-tab"])]))}},Ra={class:"keyword-rank-tracker-update-group"},Za={class:"aioseo-search-statistics-keyword-rank-tracker-modal__body"},Ma={class:"aioseo-search-statistics-keyword-rank-tracker-modal__footer"},Ga={__name:"UpdateGroup",props:{modalOpen:Boolean,group:Object},emits:["update:modalOpen"],setup(l,{emit:o}){const t="aioseo-pro",s=V(),m=o,c=l,n=K(null),A=K(!1),d=Q(()=>{const f=(n.value===null?c.group.name:n.value).trim();return!f||f===c.group.name}),p={headerTitle:a("Update Group",t),newGroupName:a("New Group Name",t)},y=async()=>{A.value=!0;try{await s.updateGroup({id:c.group.id,payload:{name:n.value}}).then(async()=>{await s.fetchGroups(),await s.fetchKeywords()}),m("update:modalOpen",!1)}catch(f){console.error(f)}finally{A.value=!1}};return(f,k)=>{const b=R("base-input"),w=R("base-button");return h(),I("div",Ra,[g(e(ne),{show:l.modalOpen,onClose:k[3]||(k[3]=v=>f.$emit("update:modalOpen",!1)),classes:["aioseo-search-statistics-keyword-rank-tracker-modal"]},{headerTitle:r(()=>[B(u(p.headerTitle)+' "'+u(l.group.name)+'" ',1)]),body:r(()=>[i("div",Za,[e(s).errors.crud?(h(),S(e(he),{key:0,type:"red"},{default:r(()=>[B(u(e(s).errors.crud),1)]),_:1})):M("",!0),g(e(ie),{name:p.newGroupName,"left-size":"12","right-size":"12","no-vertical-margin":"","no-border":"",style:{padding:"0"}},{content:r(()=>[g(b,{modelValue:l.group.name,"onUpdate:modelValue":k[0]||(k[0]=v=>n.value=v),size:"medium",placeholder:p.newGroupName,maxlength:e(s).options.input.group.maxlength},null,8,["modelValue","placeholder","maxlength"])]),_:1},8,["name"])])]),footer:r(()=>[i("div",Ma,[g(w,{type:"gray",size:"medium",onClick:k[1]||(k[1]=x(v=>f.$emit("update:modalOpen",!1),["exact"]))},{default:r(()=>[B(u(e(H).cancel),1)]),_:1}),g(w,{type:"blue",size:"medium",loading:A.value,disabled:d.value,onClick:k[2]||(k[2]=x(v=>y(),["exact"]))},{default:r(()=>[B(u(p.headerTitle),1)]),_:1},8,["loading","disabled"])])]),_:1},8,["show"])])}}},Fa={__name:"Index",setup(l){const o=V(),t="all-in-one-seo-pack",s=[{slug:"rank-tracker",name:a("Rank Tracker",t)},{slug:"all-keywords",name:a("All Keywords",t)}];return Xe(()=>{var n,A;const m=new URLSearchParams(((n=window.location)==null?void 0:n.search)||"")||{},c=dt();(m.has("tab")||(A=c==null?void 0:c.query)!=null&&A.tab)&&((m.get("tab")||c.query.tab)==="AllKeywords"&&(o.parentActiveTab="all-keywords"),c.query.tab=void 0,Ke("tab"))}),(m,c)=>(h(),S(e($e),{slug:"keywordRankTracker","hide-header":!0,toggles:!1,"no-slide":""},{tabs:r(()=>[g(e(Pe),{tabs:s,active:e(o).parentActiveTab,"show-save-button":!1,onChanged:c[0]||(c[0]=n=>{e(o).parentActiveTab=n}),internal:""},null,8,["active"])]),default:r(()=>[g(je,{name:"route-fade",mode:"out-in"},{default:r(()=>[(h(),S(Ye(e(o).parentActiveTab==="rank-tracker"?e(Da):e(Lo))))]),_:1}),g(e(po),{"modal-open":e(o).modalOpenDeleteKeywords,"onUpdate:modalOpen":c[1]||(c[1]=n=>e(o).toggleModal({modal:"modalOpenDeleteKeywords",open:n}))},null,8,["modal-open"]),g(e(X1),{"modal-open":e(o).modalOpenAddKeywords,"onUpdate:modalOpen":c[2]||(c[2]=n=>e(o).toggleModal({modal:"modalOpenAddKeywords",open:n}))},null,8,["modal-open"]),g(e(W1),{"modal-open":e(o).modalOpenAssignGroups,"onUpdate:modalOpen":c[3]||(c[3]=n=>e(o).toggleModal({modal:"modalOpenAssignGroups",open:n}))},null,8,["modal-open"]),g(e(so),{"modal-open":e(o).modalOpenCreateGroup,"onUpdate:modalOpen":c[4]||(c[4]=n=>e(o).toggleModal({modal:"modalOpenCreateGroup",open:n}))},null,8,["modal-open"]),g(e(Ga),{group:e(o).groups.selected[0],"modal-open":e(o).modalOpenUpdateGroup,"onUpdate:modalOpen":c[5]||(c[5]=n=>e(o).toggleModal({modal:"modalOpenUpdateGroup",open:n}))},null,8,["group","modal-open"]),g(e(no),{"modal-open":e(o).modalOpenDeleteGroups,"onUpdate:modalOpen":c[6]||(c[6]=n=>e(o).modalOpenDeleteGroups=n)},null,8,["modal-open"])]),_:1}))}},Oa={__name:"Blur",setup(l){const o="all-in-one-seo-pack",t=[{slug:"rank-tracker",name:a("Rank Tracker",o)},{slug:"all-keywords",name:a("All Keywords",o)}];return(s,m)=>(h(),S(e(Ct),null,{default:r(()=>[g(e($e),{slug:"keywordRankTracker","hide-header":!0,toggles:!1,"no-slide":""},{tabs:r(()=>[g(e(Pe),{tabs:t,active:"rank-tracker","show-save-button":!1,onChanged:m[0]||(m[0]=c=>{s.activeTab=c}),internal:""})]),default:r(()=>[g(e(at),{"active-tab":"keywords"},{"tab-content":r(()=>[g(e(rt))]),_:1})]),_:1})]),_:1}))}},Ua={class:"aioseo-search-statistics-keyword-rank-tracker"},Va={__name:"Index",setup(l){const{strings:o}=It(),t=Le(),s=qe(),m=pe();return(c,n)=>(h(),I("div",Ua,[e(m).shouldShowSampleReports?M("",!0):(h(),S(e(Oa),{key:0})),e(m).shouldShowSampleReports?M("",!0):(h(),S(e(Je),{key:1,"cta-second-button-action":"",onCtaSecondButtonClick:e(m).showSampleReports,"cta-link":e(Ie).getPricingUrl("search-statistics","search-statistics-upsell","keyword-rank-tracker"),"button-text":e(o).ctaButtonText,"second-button-text":e(o).ctaSecondButtonText,"cta-second-button-new-badge":"","cta-second-button-visible":"","learn-more-link":e(Ie).getUpsellUrl("search-statistics","keyword-rank-tracker",e(s).isPro?"pricing":"liteUpgrade"),"feature-list":[e(o).feature1,e(o).feature2,e(o).feature3,e(o).feature4],"align-top":"","hide-bonus":!e(t).isUnlicensed},{"header-text":r(()=>[B(u(e(o).ctaHeader),1)]),description:r(()=>[g(e(Bt),{"core-feature":["search-statistics"]}),B(" "+u(e(o).ctaDescription),1)]),_:1},8,["onCtaSecondButtonClick","cta-link","button-text","second-button-text","learn-more-link","feature-list","hide-bonus"]))]))}},La=N(Va,[["__scopeId","data-v-9e791043"]]),Vr={__name:"KeywordRankTracker",setup(l){const o=pe(),{shouldShowLite:t,shouldShowUpgrade:s}=Et();return(m,c)=>(e(s)("search-statistics","keyword-rank-tracker")||e(t))&&!e(o).shouldShowSampleReports?(h(),S(e(La),{key:0,class:"aioseo-search-statistics-keyword-rank-tracker"})):(h(),S(e(Fa),{key:1,class:te([{"aioseo-search-statistics-keyword-rank-tracker--disable-click":e(o).shouldShowSampleReports},"aioseo-search-statistics-keyword-rank-tracker"])},null,8,["class"]))}};export{Vr as default};
