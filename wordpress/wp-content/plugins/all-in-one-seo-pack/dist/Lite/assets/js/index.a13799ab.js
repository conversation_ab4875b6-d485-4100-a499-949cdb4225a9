var Gs=Object.defineProperty;var Vs=(e,t,r)=>t in e?Gs(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var k=(e,t,r)=>Vs(e,typeof t!="symbol"?t+"":t,r);import{aC as ts,g as rs,aT as tr,bl as ss,f as os,aP as ot,aN as rr,bm as js,i as Ks,w as Ws,r as Ys,aH as Js,aD as Xs,b1 as Zs,n as Qs,C as eo}from"./runtime-dom.esm-bundler.baf35205.js";import{g as to,s as U,c as Rt,_ as n}from"./default-i18n.20001971.js";import{m as ro,B as so,k as oo,l as uo,r as io,U as vr,e as no,O as ao,y as lo,P as co,F as po,G as us,b as ho,c as go,a as kr,Q as fo,J as mo,d as xr,h as is,R as Do,s as yo,z as bo,T as Ao,V as ns,g as wo}from"./helpers.a0b389be.js";import"./translations.d159963e.js";var Eo=!1;/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let as;const nt=e=>as=e,ls=Symbol();function Gt(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var st;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(st||(st={}));function Co(){const e=ts(!0),t=e.run(()=>rs({}));let r=[],s=[];const o=tr({install(u){nt(o),o._a=u,u.provide(ls,o),u.config.globalProperties.$pinia=o,s.forEach(a=>r.push(a)),s=[]},use(u){return!this._a&&!Eo?s.push(u):r.push(u),this},_p:r,_a:null,_e:e,_s:new Map,state:t});return o}const cs=()=>{};function Sr(e,t,r,s=cs){e.push(t);const o=()=>{const u=e.indexOf(t);u>-1&&(e.splice(u,1),s())};return!r&&Xs()&&Zs(o),o}function Ge(e,...t){e.slice().forEach(r=>{r(...t)})}const vo=e=>e(),Br=Symbol(),_t=Symbol();function Vt(e,t){e instanceof Map&&t instanceof Map?t.forEach((r,s)=>e.set(s,r)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const r in t){if(!t.hasOwnProperty(r))continue;const s=t[r],o=e[r];Gt(o)&&Gt(s)&&e.hasOwnProperty(r)&&!ot(s)&&!rr(s)?e[r]=Vt(o,s):e[r]=s}return e}const ko=Symbol();function xo(e){return!Gt(e)||!e.hasOwnProperty(ko)}const{assign:Te}=Object;function So(e){return!!(ot(e)&&e.effect)}function Bo(e,t,r,s){const{state:o,actions:u,getters:a}=t,c=r.state.value[e];let A;function x(){c||(r.state.value[e]=o?o():{});const T=eo(r.state.value[e]);return Te(T,u,Object.keys(a||{}).reduce((I,W)=>(I[W]=tr(os(()=>{nt(r);const v=r._s.get(e);return a[W].call(v,v)})),I),{}))}return A=ds(e,x,t,r,s,!0),A}function ds(e,t,r={},s,o,u){let a;const c=Te({actions:{}},r),A={deep:!0};let x,T,I=[],W=[],v;const V=s.state.value[e];!u&&!V&&(s.state.value[e]={}),rs({});let le;function ie(d){let g;x=T=!1,typeof d=="function"?(d(s.state.value[e]),g={type:st.patchFunction,storeId:e,events:v}):(Vt(s.state.value[e],d),g={type:st.patchObject,payload:d,storeId:e,events:v});const y=le=Symbol();Qs().then(()=>{le===y&&(x=!0)}),T=!0,Ge(I,g,s.state.value[e])}const ge=u?function(){const{state:g}=r,y=g?g():{};this.$patch(b=>{Te(b,y)})}:cs;function j(){a.stop(),I=[],W=[],s._s.delete(e)}const ee=(d,g="")=>{if(Br in d)return d[_t]=g,d;const y=function(){nt(s);const b=Array.from(arguments),B=[],L=[];function X(G){B.push(G)}function ne(G){L.push(G)}Ge(W,{args:b,name:y[_t],store:S,after:X,onError:ne});let de;try{de=d.apply(this&&this.$id===e?this:S,b)}catch(G){throw Ge(L,G),G}return de instanceof Promise?de.then(G=>(Ge(B,G),G)).catch(G=>(Ge(L,G),Promise.reject(G))):(Ge(B,de),de)};return y[Br]=!0,y[_t]=g,y},N={_p:s,$id:e,$onAction:Sr.bind(null,W),$patch:ie,$reset:ge,$subscribe(d,g={}){const y=Sr(I,d,g.detached,()=>b()),b=a.run(()=>Ws(()=>s.state.value[e],B=>{(g.flush==="sync"?T:x)&&d({storeId:e,type:st.direct,events:v},B)},Te({},A,g)));return y},$dispose:j},S=Ys(N);s._s.set(e,S);const f=(s._a&&s._a.runWithContext||vo)(()=>s._e.run(()=>(a=ts()).run(()=>t({action:ee}))));for(const d in f){const g=f[d];if(ot(g)&&!So(g)||rr(g))u||(V&&xo(g)&&(ot(g)?g.value=V[d]:Vt(g,V[d])),s.state.value[e][d]=g);else if(typeof g=="function"){const y=ee(g,d);f[d]=y,c.actions[d]=g}}return Te(S,f),Te(ss(S),f),Object.defineProperty(S,"$state",{get:()=>s.state.value[e],set:d=>{ie(g=>{Te(g,d)})}}),s._p.forEach(d=>{Te(S,a.run(()=>d({store:S,app:s._a,pinia:s,options:c})))}),V&&u&&r.hydrate&&r.hydrate(S.$state,V),x=!0,T=!0,S}/*! #__NO_SIDE_EFFECTS__ */function M(e,t,r){let s,o;const u=typeof t=="function";typeof e=="string"?(s=e,o=u?r:t):(o=e,s=e.id);function a(c,A){const x=Js();return c=c||(x?Ks(ls,null):null),c&&nt(c),c=as,c._s.has(s)||(u?ds(s,t,o,c):Bo(s,o,c)),c._s.get(s)}return a.$id=s,a}function ai(e){{const t=ss(e),r={};for(const s in t){const o=t[s];o.effect?r[s]=os({get:()=>e[s],set(u){e[s]=u}}):(ot(o)||rr(o))&&(r[s]=js(e,s))}return r}}const jt=M("AddonsStore",{state:()=>({addons:[]}),actions:{updateAddon(e){const t=this.addons.findIndex(r=>r.sku===e.sku);t!==-1&&(this.addons[t]=e)}}});var Kt={exports:{}},ps={exports:{}};(function(e){e.exports=t;function t(s){if(s)return r(s)}function r(s){for(var o in t.prototype)s[o]=t.prototype[o];return s}t.prototype.on=t.prototype.addEventListener=function(s,o){return this._callbacks=this._callbacks||{},(this._callbacks["$"+s]=this._callbacks["$"+s]||[]).push(o),this},t.prototype.once=function(s,o){function u(){this.off(s,u),o.apply(this,arguments)}return u.fn=o,this.on(s,u),this},t.prototype.off=t.prototype.removeListener=t.prototype.removeAllListeners=t.prototype.removeEventListener=function(s,o){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var u=this._callbacks["$"+s];if(!u)return this;if(arguments.length==1)return delete this._callbacks["$"+s],this;for(var a,c=0;c<u.length;c++)if(a=u[c],a===o||a.fn===o){u.splice(c,1);break}return u.length===0&&delete this._callbacks["$"+s],this},t.prototype.emit=function(s){this._callbacks=this._callbacks||{};for(var o=new Array(arguments.length-1),u=this._callbacks["$"+s],a=1;a<arguments.length;a++)o[a-1]=arguments[a];if(u){u=u.slice(0);for(var a=0,c=u.length;a<c;++a)u[a].apply(this,o)}return this},t.prototype.listeners=function(s){return this._callbacks=this._callbacks||{},this._callbacks["$"+s]||[]},t.prototype.hasListeners=function(s){return!!this.listeners(s).length}})(ps);var To=ps.exports,Fo=ut;ut.default=ut;ut.stable=fs;ut.stableStringify=fs;var yt="[...]",hs="[Circular]",Ne=[],Pe=[];function gs(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function ut(e,t,r,s){typeof s>"u"&&(s=gs()),Wt(e,"",0,[],void 0,0,s);var o;try{Pe.length===0?o=JSON.stringify(e,t,r):o=JSON.stringify(e,ms(t),r)}catch{return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;Ne.length!==0;){var u=Ne.pop();u.length===4?Object.defineProperty(u[0],u[1],u[3]):u[0][u[1]]=u[2]}}return o}function Ve(e,t,r,s){var o=Object.getOwnPropertyDescriptor(s,r);o.get!==void 0?o.configurable?(Object.defineProperty(s,r,{value:e}),Ne.push([s,r,t,o])):Pe.push([t,r,e]):(s[r]=e,Ne.push([s,r,t]))}function Wt(e,t,r,s,o,u,a){u+=1;var c;if(typeof e=="object"&&e!==null){for(c=0;c<s.length;c++)if(s[c]===e){Ve(hs,e,t,o);return}if(typeof a.depthLimit<"u"&&u>a.depthLimit){Ve(yt,e,t,o);return}if(typeof a.edgesLimit<"u"&&r+1>a.edgesLimit){Ve(yt,e,t,o);return}if(s.push(e),Array.isArray(e))for(c=0;c<e.length;c++)Wt(e[c],c,c,s,e,u,a);else{var A=Object.keys(e);for(c=0;c<A.length;c++){var x=A[c];Wt(e[x],x,c,s,e,u,a)}}s.pop()}}function Oo(e,t){return e<t?-1:e>t?1:0}function fs(e,t,r,s){typeof s>"u"&&(s=gs());var o=Yt(e,"",0,[],void 0,0,s)||e,u;try{Pe.length===0?u=JSON.stringify(o,t,r):u=JSON.stringify(o,ms(t),r)}catch{return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;Ne.length!==0;){var a=Ne.pop();a.length===4?Object.defineProperty(a[0],a[1],a[3]):a[0][a[1]]=a[2]}}return u}function Yt(e,t,r,s,o,u,a){u+=1;var c;if(typeof e=="object"&&e!==null){for(c=0;c<s.length;c++)if(s[c]===e){Ve(hs,e,t,o);return}try{if(typeof e.toJSON=="function")return}catch{return}if(typeof a.depthLimit<"u"&&u>a.depthLimit){Ve(yt,e,t,o);return}if(typeof a.edgesLimit<"u"&&r+1>a.edgesLimit){Ve(yt,e,t,o);return}if(s.push(e),Array.isArray(e))for(c=0;c<e.length;c++)Yt(e[c],c,c,s,e,u,a);else{var A={},x=Object.keys(e).sort(Oo);for(c=0;c<x.length;c++){var T=x[c];Yt(e[T],T,c,s,e,u,a),A[T]=e[T]}if(typeof o<"u")Ne.push([o,t,e]),o[t]=A;else return A}s.pop()}}function ms(e){return e=typeof e<"u"?e:function(t,r){return r},function(t,r){if(Pe.length>0)for(var s=0;s<Pe.length;s++){var o=Pe[s];if(o[1]===t&&o[0]===r){r=o[2],Pe.splice(s,1);break}}return e.call(this,t,r)}}var kt={};(function(e){e.type=t=>t.split(/ *; */).shift(),e.params=t=>{const r={};for(const s of t.split(/ *; */)){const o=s.split(/ *= */),u=o.shift(),a=o.shift();u&&a&&(r[u]=a)}return r},e.parseLinks=t=>{const r={};for(const s of t.split(/ *, */)){const o=s.split(/ *; */),u=o[0].slice(1,-1),a=o[1].split(/ *= */)[1].slice(1,-1);r[a]=u}return r},e.cleanHeader=(t,r)=>(delete t["content-type"],delete t["content-length"],delete t["transfer-encoding"],delete t.host,r&&(delete t.authorization,delete t.cookie),t),e.isObject=t=>t!==null&&typeof t=="object",e.hasOwn=Object.hasOwn||function(t,r){if(t==null)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(new Object(t),r)},e.mixin=(t,r)=>{for(const s in r)e.hasOwn(r,s)&&(t[s]=r[s])}})(kt);const{isObject:bt,hasOwn:it}=kt;var qo=q;function q(){}q.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this};q.prototype.parse=function(e){return this._parser=e,this};q.prototype.responseType=function(e){return this._responseType=e,this};q.prototype.serialize=function(e){return this._serializer=e,this};q.prototype.timeout=function(e){if(!e||typeof e!="object")return this._timeout=e,this._responseTimeout=0,this._uploadTimeout=0,this;for(const t in e)if(it(e,t))switch(t){case"deadline":this._timeout=e.deadline;break;case"response":this._responseTimeout=e.response;break;case"upload":this._uploadTimeout=e.upload;break;default:console.warn("Unknown timeout option",t)}return this};q.prototype.retry=function(e,t){return(arguments.length===0||e===!0)&&(e=1),e<=0&&(e=0),this._maxRetries=e,this._retries=0,this._retryCallback=t,this};const Lo=new Set(["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"]),Ro=new Set([408,413,429,500,502,503,504,521,522,524]);q.prototype._shouldRetry=function(e,t){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{const r=this._retryCallback(e,t);if(r===!0)return!0;if(r===!1)return!1}catch(r){console.error(r)}return!!(t&&t.status&&Ro.has(t.status)||e&&(e.code&&Lo.has(e.code)||e.timeout&&e.code==="ECONNABORTED"||e.crossDomain))};q.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()};q.prototype.then=function(e,t){if(!this._fullfilledPromise){const r=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise((s,o)=>{r.on("abort",()=>{if(this._maxRetries&&this._maxRetries>this._retries)return;if(this.timedout&&this.timedoutError){o(this.timedoutError);return}const u=new Error("Aborted");u.code="ABORTED",u.status=this.status,u.method=this.method,u.url=this.url,o(u)}),r.end((u,a)=>{u?o(u):s(a)})})}return this._fullfilledPromise.then(e,t)};q.prototype.catch=function(e){return this.then(void 0,e)};q.prototype.use=function(e){return e(this),this};q.prototype.ok=function(e){if(typeof e!="function")throw new Error("Callback required");return this._okCallback=e,this};q.prototype._isResponseOK=function(e){return e?this._okCallback?this._okCallback(e):e.status>=200&&e.status<300:!1};q.prototype.get=function(e){return this._header[e.toLowerCase()]};q.prototype.getHeader=q.prototype.get;q.prototype.set=function(e,t){if(bt(e)){for(const r in e)it(e,r)&&this.set(r,e[r]);return this}return this._header[e.toLowerCase()]=t,this.header[e]=t,this};q.prototype.unset=function(e){return delete this._header[e.toLowerCase()],delete this.header[e],this};q.prototype.field=function(e,t,r){if(e==null)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(bt(e)){for(const s in e)it(e,s)&&this.field(s,e[s]);return this}if(Array.isArray(t)){for(const s in t)it(t,s)&&this.field(e,t[s]);return this}if(t==null)throw new Error(".field(name, val) val can not be empty");return typeof t=="boolean"&&(t=String(t)),r?this._getFormData().append(e,t,r):this._getFormData().append(e,t),this};q.prototype.abort=function(){return this._aborted?this:(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req&&this.req.abort(),this.clearTimeout(),this.emit("abort"),this)};q.prototype._auth=function(e,t,r,s){switch(r.type){case"basic":this.set("Authorization",`Basic ${s(`${e}:${t}`)}`);break;case"auto":this.username=e,this.password=t;break;case"bearer":this.set("Authorization",`Bearer ${e}`);break}return this};q.prototype.withCredentials=function(e){return e===void 0&&(e=!0),this._withCredentials=e,this};q.prototype.redirects=function(e){return this._maxRedirects=e,this};q.prototype.maxResponseSize=function(e){if(typeof e!="number")throw new TypeError("Invalid argument");return this._maxResponseSize=e,this};q.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}};q.prototype.send=function(e){const t=bt(e);let r=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(t&&!this._data)Array.isArray(e)?this._data=[]:this._isHost(e)||(this._data={});else if(e&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(t&&bt(this._data))for(const s in e){if(typeof e[s]=="bigint"&&!e[s].toJSON)throw new Error("Cannot serialize BigInt value to json");it(e,s)&&(this._data[s]=e[s])}else{if(typeof e=="bigint")throw new Error("Cannot send value of type BigInt");typeof e=="string"?(r||this.type("form"),r=this._header["content-type"],r&&(r=r.toLowerCase().trim()),r==="application/x-www-form-urlencoded"?this._data=this._data?`${this._data}&${e}`:e:this._data=(this._data||"")+e):this._data=e}return!t||this._isHost(e)?this:(r||this.type("json"),this)};q.prototype.sortQuery=function(e){return this._sort=typeof e>"u"?!0:e,this};q.prototype._finalizeQueryString=function(){const e=this._query.join("&");if(e&&(this.url+=(this.url.includes("?")?"&":"?")+e),this._query.length=0,this._sort){const t=this.url.indexOf("?");if(t>=0){const r=this.url.slice(t+1).split("&");typeof this._sort=="function"?r.sort(this._sort):r.sort(),this.url=this.url.slice(0,t)+"?"+r.join("&")}}};q.prototype._appendQueryString=()=>{console.warn("Unsupported")};q.prototype._timeoutError=function(e,t,r){if(this._aborted)return;const s=new Error(`${e+t}ms exceeded`);s.timeout=t,s.code="ECONNABORTED",s.errno=r,this.timedout=!0,this.timedoutError=s,this.abort(),this.callback(s)};q.prototype._setTimeouts=function(){const e=this;this._timeout&&!this._timer&&(this._timer=setTimeout(()=>{e._timeoutError("Timeout of ",e._timeout,"ETIME")},this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout(()=>{e._timeoutError("Response timeout of ",e._responseTimeout,"ETIMEDOUT")},this._responseTimeout))};const Pt=kt;var _o=xt;function xt(){}xt.prototype.get=function(e){return this.header[e.toLowerCase()]};xt.prototype._setHeaderProperties=function(e){const t=e["content-type"]||"";this.type=Pt.type(t);const r=Pt.params(t);for(const s in r)Object.prototype.hasOwnProperty.call(r,s)&&(this[s]=r[s]);this.links={};try{e.link&&(this.links=Pt.parseLinks(e.link))}catch{}};xt.prototype._setStatusProperties=function(e){const t=Math.trunc(e/100);this.statusCode=e,this.status=this.statusCode,this.statusType=t,this.info=t===1,this.ok=t===2,this.redirect=t===3,this.clientError=t===4,this.serverError=t===5,this.error=t===4||t===5?this.toError():!1,this.created=e===201,this.accepted=e===202,this.noContent=e===204,this.badRequest=e===400,this.unauthorized=e===401,this.notAcceptable=e===406,this.forbidden=e===403,this.notFound=e===404,this.unprocessableEntity=e===422};const Po=["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"];class Ds{constructor(){this._defaults=[]}_setDefaults(t){for(const r of this._defaults)t[r.fn](...r.args)}}for(const e of Po)Ds.prototype[e]=function(){for(var t=arguments.length,r=new Array(t),s=0;s<t;s++)r[s]=arguments[s];return this._defaults.push({fn:e,args:r}),this};var No=Ds;(function(e,t){let r;typeof window<"u"?r=window:typeof self>"u"?(console.warn("Using browser-only version of superagent in non-browser environment"),r=void 0):r=self;const s=To,o=Fo,u=ro,a=qo,{isObject:c,mixin:A,hasOwn:x}=kt,T=_o,I=No;function W(){}e.exports=function(f,d){return typeof d=="function"?new t.Request("GET",f).end(d):arguments.length===1?new t.Request("GET",f):new t.Request(f,d)},t=e.exports;const v=t;t.Request=S,v.getXHR=()=>{if(r.XMLHttpRequest)return new r.XMLHttpRequest;throw new Error("Browser-only version of superagent could not find XHR")};const V="".trim?f=>f.trim():f=>f.replace(/(^\s*|\s*$)/g,"");function le(f){if(!c(f))return f;const d=[];for(const g in f)x(f,g)&&ie(d,g,f[g]);return d.join("&")}function ie(f,d,g){if(g!==void 0){if(g===null){f.push(encodeURI(d));return}if(Array.isArray(g))for(const y of g)ie(f,d,y);else if(c(g))for(const y in g)x(g,y)&&ie(f,`${d}[${y}]`,g[y]);else f.push(encodeURI(d)+"="+encodeURIComponent(g))}}v.serializeObject=le;function ge(f){const d={},g=f.split("&");let y,b;for(let B=0,L=g.length;B<L;++B)y=g[B],b=y.indexOf("="),b===-1?d[decodeURIComponent(y)]="":d[decodeURIComponent(y.slice(0,b))]=decodeURIComponent(y.slice(b+1));return d}v.parseString=ge,v.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},v.serialize={"application/x-www-form-urlencoded":u.stringify,"application/json":o},v.parse={"application/x-www-form-urlencoded":ge,"application/json":JSON.parse};function j(f){const d=f.split(/\r?\n/),g={};let y,b,B,L;for(let X=0,ne=d.length;X<ne;++X)b=d[X],y=b.indexOf(":"),y!==-1&&(B=b.slice(0,y).toLowerCase(),L=V(b.slice(y+1)),g[B]=L);return g}function ee(f){return/[/+]json($|[^-\w])/i.test(f)}function N(f){this.req=f,this.xhr=this.req.xhr,this.text=this.req.method!=="HEAD"&&(this.xhr.responseType===""||this.xhr.responseType==="text")||typeof this.xhr.responseType>"u"?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;let{status:d}=this.xhr;d===1223&&(d=204),this._setStatusProperties(d),this.headers=j(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),this.text===null&&f._responseType?this.body=this.xhr.response:this.body=this.req.method==="HEAD"?null:this._parseBody(this.text?this.text:this.xhr.response)}A(N.prototype,T.prototype),N.prototype._parseBody=function(f){let d=v.parse[this.type];return this.req._parser?this.req._parser(this,f):(!d&&ee(this.type)&&(d=v.parse["application/json"]),d&&f&&(f.length>0||f instanceof Object)?d(f):null)},N.prototype.toError=function(){const{req:f}=this,{method:d}=f,{url:g}=f,y=`cannot ${d} ${g} (${this.status})`,b=new Error(y);return b.status=this.status,b.method=d,b.url=g,b},v.Response=N;function S(f,d){const g=this;this._query=this._query||[],this.method=f,this.url=d,this.header={},this._header={},this.on("end",()=>{let y=null,b=null;try{b=new N(g)}catch(L){return y=new Error("Parser is unable to parse the response"),y.parse=!0,y.original=L,g.xhr?(y.rawResponse=typeof g.xhr.responseType>"u"?g.xhr.responseText:g.xhr.response,y.status=g.xhr.status?g.xhr.status:null,y.statusCode=y.status):(y.rawResponse=null,y.status=null),g.callback(y)}g.emit("response",b);let B;try{g._isResponseOK(b)||(B=new Error(b.statusText||b.text||"Unsuccessful HTTP response"))}catch(L){B=L}B?(B.original=y,B.response=b,B.status=B.status||b.status,g.callback(B,b)):g.callback(null,b)})}s(S.prototype),A(S.prototype,a.prototype),S.prototype.type=function(f){return this.set("Content-Type",v.types[f]||f),this},S.prototype.accept=function(f){return this.set("Accept",v.types[f]||f),this},S.prototype.auth=function(f,d,g){arguments.length===1&&(d=""),typeof d=="object"&&d!==null&&(g=d,d=""),g||(g={type:typeof btoa=="function"?"basic":"auto"});const y=g.encoder?g.encoder:b=>{if(typeof btoa=="function")return btoa(b);throw new Error("Cannot use basic auth, btoa is not a function")};return this._auth(f,d,g,y)},S.prototype.query=function(f){return typeof f!="string"&&(f=le(f)),f&&this._query.push(f),this},S.prototype.attach=function(f,d,g){if(d){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(f,d,g||d.name)}return this},S.prototype._getFormData=function(){return this._formData||(this._formData=new r.FormData),this._formData},S.prototype.callback=function(f,d){if(this._shouldRetry(f,d))return this._retry();const g=this._callback;this.clearTimeout(),f&&(this._maxRetries&&(f.retries=this._retries-1),this.emit("error",f)),g(f,d)},S.prototype.crossDomainError=function(){const f=new Error(`Request has been terminated
Possible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.`);f.crossDomain=!0,f.status=this.status,f.method=this.method,f.url=this.url,this.callback(f)},S.prototype.agent=function(){return console.warn("This is not supported in browser version of superagent"),this},S.prototype.ca=S.prototype.agent,S.prototype.buffer=S.prototype.ca,S.prototype.write=()=>{throw new Error("Streaming is not supported in browser version of superagent")},S.prototype.pipe=S.prototype.write,S.prototype._isHost=function(f){return f&&typeof f=="object"&&!Array.isArray(f)&&Object.prototype.toString.call(f)!=="[object Object]"},S.prototype.end=function(f){this._endCalled&&console.warn("Warning: .end() was called twice. This is not supported in superagent"),this._endCalled=!0,this._callback=f||W,this._finalizeQueryString(),this._end()},S.prototype._setUploadTimeout=function(){const f=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout(()=>{f._timeoutError("Upload timeout of ",f._uploadTimeout,"ETIMEDOUT")},this._uploadTimeout))},S.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));const f=this;this.xhr=v.getXHR();const{xhr:d}=this;let g=this._formData||this._data;this._setTimeouts(),d.addEventListener("readystatechange",()=>{const{readyState:b}=d;if(b>=2&&f._responseTimeoutTimer&&clearTimeout(f._responseTimeoutTimer),b!==4)return;let B;try{B=d.status}catch{B=0}if(!B)return f.timedout||f._aborted?void 0:f.crossDomainError();f.emit("end")});const y=(b,B)=>{B.total>0&&(B.percent=B.loaded/B.total*100,B.percent===100&&clearTimeout(f._uploadTimeoutTimer)),B.direction=b,f.emit("progress",B)};if(this.hasListeners("progress"))try{d.addEventListener("progress",y.bind(null,"download")),d.upload&&d.upload.addEventListener("progress",y.bind(null,"upload"))}catch{}d.upload&&this._setUploadTimeout();try{this.username&&this.password?d.open(this.method,this.url,!0,this.username,this.password):d.open(this.method,this.url,!0)}catch(b){return this.callback(b)}if(this._withCredentials&&(d.withCredentials=!0),!this._formData&&this.method!=="GET"&&this.method!=="HEAD"&&typeof g!="string"&&!this._isHost(g)){const b=this._header["content-type"];let B=this._serializer||v.serialize[b?b.split(";")[0]:""];!B&&ee(b)&&(B=v.serialize["application/json"]),B&&(g=B(g))}for(const b in this.header)this.header[b]!==null&&x(this.header,b)&&d.setRequestHeader(b,this.header[b]);this._responseType&&(d.responseType=this._responseType),this.emit("request",this),d.send(typeof g>"u"?null:g)},v.agent=()=>new I;for(const f of["GET","POST","OPTIONS","PATCH","PUT","DELETE"])I.prototype[f.toLowerCase()]=function(d,g){const y=new v.Request(f,d);return this._setDefaults(y),g&&y.end(g),y};I.prototype.del=I.prototype.delete,v.get=(f,d,g)=>{const y=v("GET",f);return typeof d=="function"&&(g=d,d=null),d&&y.query(d),g&&y.end(g),y},v.head=(f,d,g)=>{const y=v("HEAD",f);return typeof d=="function"&&(g=d,d=null),d&&y.query(d),g&&y.end(g),y},v.options=(f,d,g)=>{const y=v("OPTIONS",f);return typeof d=="function"&&(g=d,d=null),d&&y.send(d),g&&y.end(g),y};function ce(f,d,g){const y=v("DELETE",f);return typeof d=="function"&&(g=d,d=null),d&&y.send(d),g&&y.end(g),y}v.del=ce,v.delete=ce,v.patch=(f,d,g)=>{const y=v("PATCH",f);return typeof d=="function"&&(g=d,d=null),d&&y.send(d),g&&y.end(g),y},v.post=(f,d,g)=>{const y=v("POST",f);return typeof d=="function"&&(g=d,d=null),d&&y.send(d),g&&y.end(g),y},v.put=(f,d,g)=>{const y=v("PUT",f);return typeof d=="function"&&(g=d,d=null),d&&y.send(d),g&&y.end(g),y}})(Kt,Kt.exports);var Uo=Kt.exports;const Io=to(Uo),m="https://aioseo.com/",At={home:`${m}docs/`,ultimateGuide:`${m}ultimate-wordpress-seo-guide/`,quickStartGuide:`${m}docs/quick-start-guide/`,googleSearchConsole:`${m}docs/how-to-verify-your-site-with-google-search-console/`,bingWebmasterVerification:`${m}docs/how-to-verify-your-site-with-bing-webmaster-tools/`,yandexWebmasterVerification:`${m}docs/how-to-verify-your-site-with-yandex-webmaster-tools/`,baiduWebmasterVerification:`${m}docs/baidu-webmaster-tools-verification/`,pinterestSiteVerification:`${m}docs/how-to-verify-your-site-with-pinterest/`,indexNow:`${m}docs/integrating-with-indexnow-to-instantly-re-index-your-content/`,microsoftClarityDocumentation:`${m}docs/how-to-verify-your-site-with-microsoft-clarity/`,gtmContainerId:`${m}docs/how-to-connect-your-site-with-google-tag-manager/`,rssContent:`${m}docs/how-to-protect-your-content-with-rss-content-settings/`,twitter:`${m}docs/beginners-guide-to-social-networks-settings-for-twitter/`,facebook:`${m}docs/beginners-guide-to-social-networks-settings-for-facebook/`,xmlSitemaps:`${m}docs/how-to-create-an-xml-sitemap/`,blankSitemap:`${m}docs/how-to-fix-a-404-error-when-viewing-your-sitemap/`,sitemapIndexes:`${m}docs/using-sitemap-indexes-and-pagination/`,maxLinks:`${m}docs/using-sitemap-indexes-and-pagination/`,maxLinksRss:`${m}docs/how-to-create-an-rss-sitemap/#configuring-the-rss-sitemap`,selectPostTypes:`${m}docs/choosing-which-content-to-include-in-your-xml-sitemap/`,selectPostTypesColumns:`${m}docs/hiding-the-aioseo-column-on-all-posts-screens/`,selectPostTypesNews:`${m}docs/how-to-create-a-google-news-sitemap/#configuring-the-news-sitemap`,selectPostTypesVideo:`${m}docs/how-to-create-a-video-sitemap/#configuring-the-video-sitemap`,selectPostTypesRss:`${m}docs/how-to-create-an-rss-sitemap/#configuring-the-rss-sitemap`,selectTaxonomies:`${m}docs/choosing-which-content-to-include-in-your-xml-sitemap/`,selectTaxonomiesColumns:`${m}docs/hiding-the-aioseo-column-on-taxonomy-screens/`,selectTaxonomiesVideo:`${m}docs/how-to-create-a-video-sitemap/#configuring-the-video-sitemap`,includeArchivePages:`${m}docs/including-date-and-author-archives-in-your-xml-sitemap/`,excludeImages:`${m}docs/excluding-images-from-the-xml-sitemap/`,dynamicallyGenerate:`${m}docs/what-is-a-dynamically-generated-sitemap-and-why-is-it-better-to-use/`,dynamicallyGenerateVideo:`${m}docs/what-is-a-dynamically-generated-sitemap-and-why-is-it-better-to-use/`,videoSitemaps:`${m}docs/how-to-create-a-video-sitemap/`,includeCustomFields:`${m}docs/including-videos-in-custom-fields-in-your-video-sitemap/`,newsSitemaps:`${m}docs/how-to-create-a-google-news-sitemap/`,rssSitemaps:`${m}docs/how-to-create-an-rss-sitemap/`,facebookAdminId:`${m}docs/adding-your-facebook-admin-id/`,facebookAppId:`${m}docs/adding-your-facebook-app-id/`,facebookAuthorUrl:`${m}docs/setting-the-content-author-for-facebook/`,usageTracking:`${m}docs/usage-tracking/`,schemaSettings:`${m}docs/schema-settings/`,imageSeo:`${m}docs/image-seo-module/`,localSeo:`${m}introducing-local-seo/`,robotsEditor:`${m}docs/using-the-robots-txt-tool-in-all-in-one-seo/`,robotsRewrite:`${m}docs/nginx-rewrite-rules-for-robots-txt/`,useKeyphrasesTooltip:`${m}docs/using-the-focus-keyphrase-to-analyze-your-content/`,whenToUseNoindex:`${m}docs/when-to-use-noindex-or-the-robots-txt/`,installAioseoPro:`${m}docs/installing-all-in-one-seo-pro/`,importProcessSeoData:`${m}docs/importing-and-exporting-aioseo-settings-and-meta-data/`,whatAreMediaAttachments:`${m}docs/what-are-media-attachments-and-should-i-submit-them-to-search-engines/`,minimumRequirements:`${m}docs/what-are-the-minimum-requirements-for-all-in-one-seo-pack/`,apiCodeExamples:`${m}docs/how-do-i-use-your-api-code-examples/`,troubleshootIssues:`${m}docs/how-to-troubleshoot-issues-with-all-in-one-seo-pack/`,staticHomePage:`${m}docs/setting-the-seo-for-your-home-page/#setting-the-seo-when-your-homepage-displays-a-static-page`,staticHomePageFacebook:`${m}docs/setting-facebook-social-meta-for-your-homepage/#setting-the-facebook-social-meta-when-your-homepage-displays-a-static-page`,staticHomePageTwitter:`${m}docs/setting-twitter-social-meta-for-your-homepage/#setting-the-twitter-social-meta-when-your-homepage-displays-a-static-page`,restApi:`${m}docs/aioseo-uses-rest-api/`,configuringSchema:`${m}docs/configuring-the-schema-settings-in-all-in-one-seo/`,unfilteredHtml:`${m}docs/unfiltered-html-capability/`,customFields:`${m}docs/including-custom-fields-in-the-seo-page-analysis/`,productIdentifiers:`${m}docs/unique-product-identifiers/`,redirectManagerRegex:`${m}docs/redirect-manager-regex/`,redirectGdpr:`${m}docs/redirect-gdpr-privacy-information/`,redirectCustomRulesUserAgent:`${m}docs/redirection-manager-custom-rules/#user-agent`,redirectCanonicalHttps:`${m}docs/full-site-redirect/#canonical-settings`,redirectUnknownWebserver:`${m}docs/redirect-manager-unknown-web-server/`,redirectServerConfigReload:`${m}docs/redirect-manager-configuration-reload/`,localSeoShortcodeBusinessInfo:`${m}docs/shortcode-aioseo_local_business_info/`,localSeoShortcodeOpeningHours:`${m}docs/shortcode-aioseo_local_opening_hours/`,localSeoShortcodeLocations:`${m}docs/shortcode-aioseo_local_locations/`,localSeoShortcodeMap:`${m}docs/shortcode-aioseo_local_map/`,localSeoFunctionBusinessInfo:`${m}docs/function-aioseo_local_business_info/`,localSeoFunctionOpeningHours:`${m}docs/function-aioseo_local_opening_hours/`,localSeoFunctionLocations:`${m}docs/function-aioseo_local_locations/`,localSeoFunctionMap:`${m}docs/function-aioseo_local_map/`,localSeoSearchQueryConflict:`${m}docs/enhanced-search-query-conflict/`,localSeoMapSetup:`${m}docs/setting-up-google-maps/`,localSeoMapEmbedApi:`${m}docs/using-places-on-your-maps/`,breadcrumbsDisplay:`${m}docs/displaying-breadcrumbs-on-your-site/`,breadcrumbsShortcode:`${m}docs/shortcode-aioseo_breadcrumbs/`,breadcrumbsFunction:`${m}docs/function-aioseo_breadcrumbs/`,seoAnalyzer:`${m}docs/using-the-seo-analysis-tool/`,seoAnalyzerIssues:`${m}docs/seo-analysis-unable-to-connect-to-your-site/`,htmlSitemap:`${m}docs/html-sitemap/`,htmlSitemapShortcode:`${m}docs/shortcode-html-sitemap/`,htmlSitemapFunction:`${m}docs/function-html-sitemap/`,htmlSitemapCompactArchives:`${m}docs/html-sitemap#compact-archives/`,linkAssistant:`${m}docs/link-assistant`,linkAssistantPostTypes:`${m}docs/link-assistant-settings/#post-types`,linkAssistantPostStatuses:`${m}docs/link-assistant-settings/#post-statuses`,updateWordPress:`${m}docs/update-wordpress/`,runningShortcodes:`${m}docs/running-shortcodes/`,crawlCleanup:`${m}docs/crawl-cleanup-best-practices`,schema:`${m}docs/a-guide-to-schema-org-markup-for-rich-snippets/`,schemaJsonLd:`${m}docs/a-guide-to-schema-org-markup-for-rich-snippets/#schema-markup-in-all-in-one-seo`,smartTags:`${m}docs/using-the-smart-tags-in-titles-and-descriptions/`,wpcode:`${m}docs/wpcode-snippet-library/`,primaryTerm:`${m}docs/setting-the-primary-term-for-breadcrumbs/`,cornerstoneContent:`${m}docs/cornerstone-content/`,eeat:`${m}docs/adding-author-seo-e-e-a-t-to-your-site/`,eeatAuthorBioInjection:`${m}docs/adding-author-seo-e-e-a-t-to-your-site/#aioseo-automatically-displaying-the-author-excerpt`,queryArgMonitor:`${m}docs/using-the-query-arg-monitoring-in-all-in-one-seo/`,businessPhoneNumber:`${m}docs/best-business-phone-services`,keywordRankTracker:`${m}docs/using-the-keyword-rank-tracker-feature-in-search-statistics/`,writingAssistantHowToUse:`${m}docs/how-to-use-the-writing-assistant-in-aioseo/`,aiContentGenerator:`${m}docs/ai-content-generator/`},wt={home:m,liteUpgrade:`${m}lite-upgrade/`,pricing:`${m}pricing/`,aiCredits:`${m}pricing-ai-credits/`,semrushPricing:`${m}semrush-pricing/`},Ho=(e,t=null,r)=>{if(e==="feature-manager-upgrade"&&t!=="no-license-key"){const s=t==="aioseo-local-business"?"&features[]=local-seo":"&features[]="+(t?t.replace("aioseo-",""):"");return ke(e,t,wt[r])+s}return ke(e,t,wt[r])},zo=e=>ke("documentation",e,At[e]),$o=(e,t,r,s=!1)=>{const o=s?U('<a href="%1$s" class="no-underline" target="_blank">&nbsp;&rarr;</a>',ke(e,r,wt[r])):"";return U('<a href="%1$s" target="_blank">%2$s</a>%3$s',ke(e,r,wt[r]),t,o)},Mo=(e,t,r=!1,s=!0)=>{const o=s?'target="_blank"':"_self",u=r?U(`<a href="%1$s" class="no-underline" target="${o}">&nbsp;&rarr;</a>`,t):"";return U(`<a href="%1$s" target="${o}">%2$s</a>%3$s`,t,e,u)},Go=(e,t,r=!1)=>{const s=r?U('<a href="%1$s" class="no-underline" target="_blank">&nbsp;&rarr;</a>',ke("documentation",t,At[t])):"";return U('<a href="%1$s" target="_blank">%2$s</a>%3$s',ke("documentation",t,At[t]),e,s)},Vo=(e,t,r,s=`${m}pricing/`)=>ke(t,r,s)+"&features[]="+e,ke=(e,t=null,r=`${m}pricing/`)=>{let s=!1;(`${m}pricing/`===r||`${m}lite-upgrade/`===r)&&"Lite".toLowerCase()!=="pro"&&(s=P().aioseo.urls.upgradeUrl!==m,r=`${m}lite-upgrade/`);const o=r.split("#"),u=[{key:"utm_source",value:"WordPress"},{key:"utm_campaign",value:"Lite".toLowerCase()==="pro"?"proplugin":"liteplugin"},{key:"utm_medium",value:e}];t&&u.push({key:"utm_content",value:t}),/^https?:\/\//i.test(o[0])||(o[0]=m+o[0]);const c=o[0].split("?");return o[0]=c[0]+(c[1]?"?"+c[1]+"&":"?"),o[0]+=u.map(A=>`${A.key}=${A.value}`).join("&"),r=o[0],o[1]&&(r=r+"#"+o[1]),s&&(r=P().aioseo.urls.upgradeUrl.replace("https%3A%2F%2Faioseo.com%2F",jo(r))),r},jo=e=>{const t={};t["'"]="%27",t["("]="%28",t[")"]="%29",t["*"]="%2A",t["~"]="%7E",t["!"]="%21",e=encodeURIComponent(e),e=e.replace("%20"," ");for(const r in t)e=e.replace(r,t[r]);return e.replace(/(%([a-z0-9]{2}))/g,function(r,s,o){return"%"+o.toUpperCase()})},ys=e=>e&&e.replace(/^\//,""),sr=e=>e&&e.replace(/\/$/,""),mt=e=>sr(e)+"/",Ko=(e,t="aioseo/v1")=>{const r=P();return e=r.aioseo.data.hasUrlTrailingSlash?mt(e):sr(e),mt(r.aioseo.urls.restUrl)+mt(t)+ys(e)},p={docLinks:At,getDocLink:Go,getDocUrl:zo,getPlainLink:Mo,getPricingUrl:Vo,getUpsellLink:$o,getUpsellUrl:Ho,restUrl:Ko,trailingSlashIt:mt,unForwardSlashIt:ys,unTrailingSlashIt:sr,utmUrl:ke};var Zr;const h=Io.agent().set("X-WP-Nonce",(Zr=window==null?void 0:window.aioseo)==null?void 0:Zr.nonce).use(e=>{e.url[0]==="/"&&(e.url=p.unTrailingSlashIt(p.restUrl(e.url))),e.on("response",t=>{(t.status===401||t.status===403)&&console.error(t)})});var Qr,es;!((Qr=window.wp)!=null&&Qr.blockEditor)&&((es=window.wp)!=null&&es.blocks)&&window.wp.oldEditor&&(window.wp.blockEditor=window.wp.editor);const Wo=()=>document.body.classList.contains("block-editor-page")&&window.wp.data&&ru(),li=()=>!!document.querySelector("#wp-content-wrap.tmce-active, #wp-content-wrap.html-active"),ci=()=>document.querySelector("#post input#title")&&!document.querySelector("#wp-content-wrap"),Yo=()=>!!(document.body.classList.contains("elementor-editor-active")&&window.elementor),Jo=()=>{const e=document.body;return!window.ET_Builder?!1:e.classList.contains("et_pb_pagebuilder_layout")||e.classList.contains("et_divi_builder")&&e.classList.contains("et-fb")},Xo=()=>!!(document.body.classList.contains("seedprod-builder")&&window.seedprod_data),Zo=()=>!!(window.vc&&window.vc_mode),Qo=()=>{var e;return(e=window.FusionApp||window.FusionPageBuilderApp)==null?void 0:e.builderActive},eu=()=>!!(window.TVE&&window.TVE.Editor_Page),tu=()=>{const e=s=>!!(s!=null&&s.offsetWidth||s!=null&&s.offsetHeight||s!=null&&s.getClientRects().length),t=document.querySelectorAll(".block-editor-page").length&&typeof window.soPanelsBuilderView<"u",r=e(document.querySelector("#so-panels-panels.attached-to-editor"));return t||r},di=()=>{const e=De();return P().aioseo.data.isWooCommerceActive&&e.currentPost&&e.currentPost.postType==="product"},pi=()=>Yo()||Jo()||Xo()||Zo()||Qo()||tu()||eu(),ru=()=>{const e=window.wp;return typeof e<"u"&&typeof e.blocks<"u"&&typeof e.blockEditor<"u"},hi=()=>{var e,t;if(Wo()){const r=(t=(e=window.wp)==null?void 0:e.data)==null?void 0:t.select("core/edit-post");return(r==null?void 0:r.getEditorMode())==="text"}return!1};var bs=so(Object.getPrototypeOf,Object),su="[object Object]",ou=Function.prototype,uu=Object.prototype,As=ou.toString,iu=uu.hasOwnProperty,nu=As.call(Object);function au(e){if(!oo(e)||uo(e)!=su)return!1;var t=bs(e);if(t===null)return!0;var r=iu.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&As.call(r)==nu}var ws=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Tr=ws&&typeof module=="object"&&module&&!module.nodeType&&module,lu=Tr&&Tr.exports===ws,Fr=lu?io.Buffer:void 0,Or=Fr?Fr.allocUnsafe:void 0;function cu(e,t){if(t)return e.slice();var r=e.length,s=Or?Or(r):new e.constructor(r);return e.copy(s),s}function du(e){var t=new e.constructor(e.byteLength);return new vr(t).set(new vr(e)),t}function pu(e,t){var r=t?du(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}function hu(e){return typeof e.constructor=="function"&&!no(e)?ao(bs(e)):{}}function Jt(e,t,r){(r!==void 0&&!lo(e[t],r)||r===void 0&&!(t in e))&&co(e,t,r)}function Xt(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}function gu(e){return po(e,us(e))}function fu(e,t,r,s,o,u,a){var c=Xt(e,r),A=Xt(t,r),x=a.get(A);if(x){Jt(e,r,x);return}var T=u?u(c,A,r+"",e,t,a):void 0,I=T===void 0;if(I){var W=kr(A),v=!W&&ho(A),V=!W&&!v&&go(A);T=A,W||v||V?kr(c)?T=c:fo(c)?T=mo(c):v?(I=!1,T=cu(A,!0)):V?(I=!1,T=pu(A,!0)):T=[]:au(A)||xr(A)?(T=c,xr(c)?T=gu(c):(!is(c)||Do(c))&&(T=hu(A))):I=!1}I&&(a.set(A,T),o(T,A,s,u,a),a.delete(A)),Jt(e,r,T)}function Es(e,t,r,s,o){e!==t&&yo(t,function(u,a){if(o||(o=new bo),is(u))fu(e,t,a,r,Es,s,o);else{var c=s?s(Xt(e,a),u,a+"",e,t,o):void 0;c===void 0&&(c=u),Jt(e,a,c)}},us)}var _=Ao(function(e,t,r){Es(e,t,r)});/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:Cs,setPrototypeOf:qr,isFrozen:mu,getPrototypeOf:Du,getOwnPropertyDescriptor:yu}=Object;let{freeze:se,seal:he,create:vs}=Object,{apply:Zt,construct:Qt}=typeof Reflect<"u"&&Reflect;se||(se=function(t){return t});he||(he=function(t){return t});Zt||(Zt=function(t,r,s){return t.apply(r,s)});Qt||(Qt=function(t,r){return new t(...r)});const gt=oe(Array.prototype.forEach),bu=oe(Array.prototype.lastIndexOf),Lr=oe(Array.prototype.pop),Ze=oe(Array.prototype.push),Au=oe(Array.prototype.splice),Dt=oe(String.prototype.toLowerCase),Nt=oe(String.prototype.toString),Rr=oe(String.prototype.match),Qe=oe(String.prototype.replace),wu=oe(String.prototype.indexOf),Eu=oe(String.prototype.trim),me=oe(Object.prototype.hasOwnProperty),re=oe(RegExp.prototype.test),et=Cu(TypeError);function oe(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var r=arguments.length,s=new Array(r>1?r-1:0),o=1;o<r;o++)s[o-1]=arguments[o];return Zt(e,t,s)}}function Cu(e){return function(){for(var t=arguments.length,r=new Array(t),s=0;s<t;s++)r[s]=arguments[s];return Qt(e,r)}}function O(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Dt;qr&&qr(e,null);let s=t.length;for(;s--;){let o=t[s];if(typeof o=="string"){const u=r(o);u!==o&&(mu(t)||(t[s]=u),o=u)}e[o]=!0}return e}function vu(e){for(let t=0;t<e.length;t++)me(e,t)||(e[t]=null);return e}function ve(e){const t=vs(null);for(const[r,s]of Cs(e))me(e,r)&&(Array.isArray(s)?t[r]=vu(s):s&&typeof s=="object"&&s.constructor===Object?t[r]=ve(s):t[r]=s);return t}function tt(e,t){for(;e!==null;){const s=yu(e,t);if(s){if(s.get)return oe(s.get);if(typeof s.value=="function")return oe(s.value)}e=Du(e)}function r(){return null}return r}const _r=se(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ut=se(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),It=se(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),ku=se(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ht=se(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),xu=se(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Pr=se(["#text"]),Nr=se(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),zt=se(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Ur=se(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ft=se(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Su=he(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Bu=he(/<%[\w\W]*|[\w\W]*%>/gm),Tu=he(/\$\{[\w\W]*/gm),Fu=he(/^data-[\-\w.\u00B7-\uFFFF]+$/),Ou=he(/^aria-[\-\w]+$/),ks=he(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),qu=he(/^(?:\w+script|data):/i),Lu=he(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),xs=he(/^html$/i),Ru=he(/^[a-z][.\w]*(-[.\w]+)+$/i);var Ir=Object.freeze({__proto__:null,ARIA_ATTR:Ou,ATTR_WHITESPACE:Lu,CUSTOM_ELEMENT:Ru,DATA_ATTR:Fu,DOCTYPE_NAME:xs,ERB_EXPR:Bu,IS_ALLOWED_URI:ks,IS_SCRIPT_OR_DATA:qu,MUSTACHE_EXPR:Su,TMPLIT_EXPR:Tu});const rt={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},_u=function(){return typeof window>"u"?null:window},Pu=function(t,r){if(typeof t!="object"||typeof t.createPolicy!="function")return null;let s=null;const o="data-tt-policy-suffix";r&&r.hasAttribute(o)&&(s=r.getAttribute(o));const u="dompurify"+(s?"#"+s:"");try{return t.createPolicy(u,{createHTML(a){return a},createScriptURL(a){return a}})}catch{return console.warn("TrustedTypes policy "+u+" could not be created."),null}},Hr=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function Ss(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:_u();const t=E=>Ss(E);if(t.version="3.2.6",t.removed=[],!e||!e.document||e.document.nodeType!==rt.document||!e.Element)return t.isSupported=!1,t;let{document:r}=e;const s=r,o=s.currentScript,{DocumentFragment:u,HTMLTemplateElement:a,Node:c,Element:A,NodeFilter:x,NamedNodeMap:T=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:I,DOMParser:W,trustedTypes:v}=e,V=A.prototype,le=tt(V,"cloneNode"),ie=tt(V,"remove"),ge=tt(V,"nextSibling"),j=tt(V,"childNodes"),ee=tt(V,"parentNode");if(typeof a=="function"){const E=r.createElement("template");E.content&&E.content.ownerDocument&&(r=E.content.ownerDocument)}let N,S="";const{implementation:ce,createNodeIterator:f,createDocumentFragment:d,getElementsByTagName:g}=r,{importNode:y}=s;let b=Hr();t.isSupported=typeof Cs=="function"&&typeof ee=="function"&&ce&&ce.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:B,ERB_EXPR:L,TMPLIT_EXPR:X,DATA_ATTR:ne,ARIA_ATTR:de,IS_SCRIPT_OR_DATA:G,ATTR_WHITESPACE:Ae,CUSTOM_ELEMENT:C}=Ir;let{IS_ALLOWED_URI:R}=Ir,F=null;const te=O({},[..._r,...Ut,...It,...Ht,...Pr]);let z=null;const xe=O({},[...Nr,...zt,...Ur,...ft]);let H=Object.seal(vs(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),pe=null,K=null,qe=!0,Le=!0,We=!1,Re=!0,fe=!1,_e=!0,ye=!1,Se=!1,Be=!1,Ue=!1,at=!1,lt=!1,ir=!0,nr=!1;const Ps="user-content-";let St=!0,Ye=!1,Ie={},He=null;const ar=O({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let lr=null;const cr=O({},["audio","video","img","source","image","track"]);let Bt=null;const dr=O({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ct="http://www.w3.org/1998/Math/MathML",dt="http://www.w3.org/2000/svg",we="http://www.w3.org/1999/xhtml";let ze=we,Tt=!1,Ft=null;const Ns=O({},[ct,dt,we],Nt);let pt=O({},["mi","mo","mn","ms","mtext"]),ht=O({},["annotation-xml"]);const Us=O({},["title","style","font","a","script"]);let Je=null;const Is=["application/xhtml+xml","text/html"],Hs="text/html";let J=null,$e=null;const zs=r.createElement("form"),pr=function(i){return i instanceof RegExp||i instanceof Function},Ot=function(){let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!($e&&$e===i)){if((!i||typeof i!="object")&&(i={}),i=ve(i),Je=Is.indexOf(i.PARSER_MEDIA_TYPE)===-1?Hs:i.PARSER_MEDIA_TYPE,J=Je==="application/xhtml+xml"?Nt:Dt,F=me(i,"ALLOWED_TAGS")?O({},i.ALLOWED_TAGS,J):te,z=me(i,"ALLOWED_ATTR")?O({},i.ALLOWED_ATTR,J):xe,Ft=me(i,"ALLOWED_NAMESPACES")?O({},i.ALLOWED_NAMESPACES,Nt):Ns,Bt=me(i,"ADD_URI_SAFE_ATTR")?O(ve(dr),i.ADD_URI_SAFE_ATTR,J):dr,lr=me(i,"ADD_DATA_URI_TAGS")?O(ve(cr),i.ADD_DATA_URI_TAGS,J):cr,He=me(i,"FORBID_CONTENTS")?O({},i.FORBID_CONTENTS,J):ar,pe=me(i,"FORBID_TAGS")?O({},i.FORBID_TAGS,J):ve({}),K=me(i,"FORBID_ATTR")?O({},i.FORBID_ATTR,J):ve({}),Ie=me(i,"USE_PROFILES")?i.USE_PROFILES:!1,qe=i.ALLOW_ARIA_ATTR!==!1,Le=i.ALLOW_DATA_ATTR!==!1,We=i.ALLOW_UNKNOWN_PROTOCOLS||!1,Re=i.ALLOW_SELF_CLOSE_IN_ATTR!==!1,fe=i.SAFE_FOR_TEMPLATES||!1,_e=i.SAFE_FOR_XML!==!1,ye=i.WHOLE_DOCUMENT||!1,Ue=i.RETURN_DOM||!1,at=i.RETURN_DOM_FRAGMENT||!1,lt=i.RETURN_TRUSTED_TYPE||!1,Be=i.FORCE_BODY||!1,ir=i.SANITIZE_DOM!==!1,nr=i.SANITIZE_NAMED_PROPS||!1,St=i.KEEP_CONTENT!==!1,Ye=i.IN_PLACE||!1,R=i.ALLOWED_URI_REGEXP||ks,ze=i.NAMESPACE||we,pt=i.MATHML_TEXT_INTEGRATION_POINTS||pt,ht=i.HTML_INTEGRATION_POINTS||ht,H=i.CUSTOM_ELEMENT_HANDLING||{},i.CUSTOM_ELEMENT_HANDLING&&pr(i.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(H.tagNameCheck=i.CUSTOM_ELEMENT_HANDLING.tagNameCheck),i.CUSTOM_ELEMENT_HANDLING&&pr(i.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(H.attributeNameCheck=i.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),i.CUSTOM_ELEMENT_HANDLING&&typeof i.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(H.allowCustomizedBuiltInElements=i.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),fe&&(Le=!1),at&&(Ue=!0),Ie&&(F=O({},Pr),z=[],Ie.html===!0&&(O(F,_r),O(z,Nr)),Ie.svg===!0&&(O(F,Ut),O(z,zt),O(z,ft)),Ie.svgFilters===!0&&(O(F,It),O(z,zt),O(z,ft)),Ie.mathMl===!0&&(O(F,Ht),O(z,Ur),O(z,ft))),i.ADD_TAGS&&(F===te&&(F=ve(F)),O(F,i.ADD_TAGS,J)),i.ADD_ATTR&&(z===xe&&(z=ve(z)),O(z,i.ADD_ATTR,J)),i.ADD_URI_SAFE_ATTR&&O(Bt,i.ADD_URI_SAFE_ATTR,J),i.FORBID_CONTENTS&&(He===ar&&(He=ve(He)),O(He,i.FORBID_CONTENTS,J)),St&&(F["#text"]=!0),ye&&O(F,["html","head","body"]),F.table&&(O(F,["tbody"]),delete pe.tbody),i.TRUSTED_TYPES_POLICY){if(typeof i.TRUSTED_TYPES_POLICY.createHTML!="function")throw et('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof i.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw et('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');N=i.TRUSTED_TYPES_POLICY,S=N.createHTML("")}else N===void 0&&(N=Pu(v,o)),N!==null&&typeof S=="string"&&(S=N.createHTML(""));se&&se(i),$e=i}},hr=O({},[...Ut,...It,...ku]),gr=O({},[...Ht,...xu]),$s=function(i){let D=ee(i);(!D||!D.tagName)&&(D={namespaceURI:ze,tagName:"template"});const w=Dt(i.tagName),$=Dt(D.tagName);return Ft[i.namespaceURI]?i.namespaceURI===dt?D.namespaceURI===we?w==="svg":D.namespaceURI===ct?w==="svg"&&($==="annotation-xml"||pt[$]):!!hr[w]:i.namespaceURI===ct?D.namespaceURI===we?w==="math":D.namespaceURI===dt?w==="math"&&ht[$]:!!gr[w]:i.namespaceURI===we?D.namespaceURI===dt&&!ht[$]||D.namespaceURI===ct&&!pt[$]?!1:!gr[w]&&(Us[w]||!hr[w]):!!(Je==="application/xhtml+xml"&&Ft[i.namespaceURI]):!1},be=function(i){Ze(t.removed,{element:i});try{ee(i).removeChild(i)}catch{ie(i)}},Me=function(i,D){try{Ze(t.removed,{attribute:D.getAttributeNode(i),from:D})}catch{Ze(t.removed,{attribute:null,from:D})}if(D.removeAttribute(i),i==="is")if(Ue||at)try{be(D)}catch{}else try{D.setAttribute(i,"")}catch{}},fr=function(i){let D=null,w=null;if(Be)i="<remove></remove>"+i;else{const Y=Rr(i,/^[\r\n\t ]+/);w=Y&&Y[0]}Je==="application/xhtml+xml"&&ze===we&&(i='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+i+"</body></html>");const $=N?N.createHTML(i):i;if(ze===we)try{D=new W().parseFromString($,Je)}catch{}if(!D||!D.documentElement){D=ce.createDocument(ze,"template",null);try{D.documentElement.innerHTML=Tt?S:$}catch{}}const Z=D.body||D.documentElement;return i&&w&&Z.insertBefore(r.createTextNode(w),Z.childNodes[0]||null),ze===we?g.call(D,ye?"html":"body")[0]:ye?D.documentElement:Z},mr=function(i){return f.call(i.ownerDocument||i,i,x.SHOW_ELEMENT|x.SHOW_COMMENT|x.SHOW_TEXT|x.SHOW_PROCESSING_INSTRUCTION|x.SHOW_CDATA_SECTION,null)},qt=function(i){return i instanceof I&&(typeof i.nodeName!="string"||typeof i.textContent!="string"||typeof i.removeChild!="function"||!(i.attributes instanceof T)||typeof i.removeAttribute!="function"||typeof i.setAttribute!="function"||typeof i.namespaceURI!="string"||typeof i.insertBefore!="function"||typeof i.hasChildNodes!="function")},Dr=function(i){return typeof c=="function"&&i instanceof c};function Ee(E,i,D){gt(E,w=>{w.call(t,i,D,$e)})}const yr=function(i){let D=null;if(Ee(b.beforeSanitizeElements,i,null),qt(i))return be(i),!0;const w=J(i.nodeName);if(Ee(b.uponSanitizeElement,i,{tagName:w,allowedTags:F}),_e&&i.hasChildNodes()&&!Dr(i.firstElementChild)&&re(/<[/\w!]/g,i.innerHTML)&&re(/<[/\w!]/g,i.textContent)||i.nodeType===rt.progressingInstruction||_e&&i.nodeType===rt.comment&&re(/<[/\w]/g,i.data))return be(i),!0;if(!F[w]||pe[w]){if(!pe[w]&&Ar(w)&&(H.tagNameCheck instanceof RegExp&&re(H.tagNameCheck,w)||H.tagNameCheck instanceof Function&&H.tagNameCheck(w)))return!1;if(St&&!He[w]){const $=ee(i)||i.parentNode,Z=j(i)||i.childNodes;if(Z&&$){const Y=Z.length;for(let ue=Y-1;ue>=0;--ue){const Ce=le(Z[ue],!0);Ce.__removalCount=(i.__removalCount||0)+1,$.insertBefore(Ce,ge(i))}}}return be(i),!0}return i instanceof A&&!$s(i)||(w==="noscript"||w==="noembed"||w==="noframes")&&re(/<\/no(script|embed|frames)/i,i.innerHTML)?(be(i),!0):(fe&&i.nodeType===rt.text&&(D=i.textContent,gt([B,L,X],$=>{D=Qe(D,$," ")}),i.textContent!==D&&(Ze(t.removed,{element:i.cloneNode()}),i.textContent=D)),Ee(b.afterSanitizeElements,i,null),!1)},br=function(i,D,w){if(ir&&(D==="id"||D==="name")&&(w in r||w in zs))return!1;if(!(Le&&!K[D]&&re(ne,D))){if(!(qe&&re(de,D))){if(!z[D]||K[D]){if(!(Ar(i)&&(H.tagNameCheck instanceof RegExp&&re(H.tagNameCheck,i)||H.tagNameCheck instanceof Function&&H.tagNameCheck(i))&&(H.attributeNameCheck instanceof RegExp&&re(H.attributeNameCheck,D)||H.attributeNameCheck instanceof Function&&H.attributeNameCheck(D))||D==="is"&&H.allowCustomizedBuiltInElements&&(H.tagNameCheck instanceof RegExp&&re(H.tagNameCheck,w)||H.tagNameCheck instanceof Function&&H.tagNameCheck(w))))return!1}else if(!Bt[D]){if(!re(R,Qe(w,Ae,""))){if(!((D==="src"||D==="xlink:href"||D==="href")&&i!=="script"&&wu(w,"data:")===0&&lr[i])){if(!(We&&!re(G,Qe(w,Ae,"")))){if(w)return!1}}}}}}return!0},Ar=function(i){return i!=="annotation-xml"&&Rr(i,C)},wr=function(i){Ee(b.beforeSanitizeAttributes,i,null);const{attributes:D}=i;if(!D||qt(i))return;const w={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:z,forceKeepAttr:void 0};let $=D.length;for(;$--;){const Z=D[$],{name:Y,namespaceURI:ue,value:Ce}=Z,Xe=J(Y),Lt=Ce;let Q=Y==="value"?Lt:Eu(Lt);if(w.attrName=Xe,w.attrValue=Q,w.keepAttr=!0,w.forceKeepAttr=void 0,Ee(b.uponSanitizeAttribute,i,w),Q=w.attrValue,nr&&(Xe==="id"||Xe==="name")&&(Me(Y,i),Q=Ps+Q),_e&&re(/((--!?|])>)|<\/(style|title)/i,Q)){Me(Y,i);continue}if(w.forceKeepAttr)continue;if(!w.keepAttr){Me(Y,i);continue}if(!Re&&re(/\/>/i,Q)){Me(Y,i);continue}fe&&gt([B,L,X],Cr=>{Q=Qe(Q,Cr," ")});const Er=J(i.nodeName);if(!br(Er,Xe,Q)){Me(Y,i);continue}if(N&&typeof v=="object"&&typeof v.getAttributeType=="function"&&!ue)switch(v.getAttributeType(Er,Xe)){case"TrustedHTML":{Q=N.createHTML(Q);break}case"TrustedScriptURL":{Q=N.createScriptURL(Q);break}}if(Q!==Lt)try{ue?i.setAttributeNS(ue,Y,Q):i.setAttribute(Y,Q),qt(i)?be(i):Lr(t.removed)}catch{Me(Y,i)}}Ee(b.afterSanitizeAttributes,i,null)},Ms=function E(i){let D=null;const w=mr(i);for(Ee(b.beforeSanitizeShadowDOM,i,null);D=w.nextNode();)Ee(b.uponSanitizeShadowNode,D,null),yr(D),wr(D),D.content instanceof u&&E(D.content);Ee(b.afterSanitizeShadowDOM,i,null)};return t.sanitize=function(E){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},D=null,w=null,$=null,Z=null;if(Tt=!E,Tt&&(E="<!-->"),typeof E!="string"&&!Dr(E))if(typeof E.toString=="function"){if(E=E.toString(),typeof E!="string")throw et("dirty is not a string, aborting")}else throw et("toString is not a function");if(!t.isSupported)return E;if(Se||Ot(i),t.removed=[],typeof E=="string"&&(Ye=!1),Ye){if(E.nodeName){const Ce=J(E.nodeName);if(!F[Ce]||pe[Ce])throw et("root node is forbidden and cannot be sanitized in-place")}}else if(E instanceof c)D=fr("<!---->"),w=D.ownerDocument.importNode(E,!0),w.nodeType===rt.element&&w.nodeName==="BODY"||w.nodeName==="HTML"?D=w:D.appendChild(w);else{if(!Ue&&!fe&&!ye&&E.indexOf("<")===-1)return N&&lt?N.createHTML(E):E;if(D=fr(E),!D)return Ue?null:lt?S:""}D&&Be&&be(D.firstChild);const Y=mr(Ye?E:D);for(;$=Y.nextNode();)yr($),wr($),$.content instanceof u&&Ms($.content);if(Ye)return E;if(Ue){if(at)for(Z=d.call(D.ownerDocument);D.firstChild;)Z.appendChild(D.firstChild);else Z=D;return(z.shadowroot||z.shadowrootmode)&&(Z=y.call(s,Z,!0)),Z}let ue=ye?D.outerHTML:D.innerHTML;return ye&&F["!doctype"]&&D.ownerDocument&&D.ownerDocument.doctype&&D.ownerDocument.doctype.name&&re(xs,D.ownerDocument.doctype.name)&&(ue="<!DOCTYPE "+D.ownerDocument.doctype.name+`>
`+ue),fe&&gt([B,L,X],Ce=>{ue=Qe(ue,Ce," ")}),N&&lt?N.createHTML(ue):ue},t.setConfig=function(){let E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Ot(E),Se=!0},t.clearConfig=function(){$e=null,Se=!1},t.isValidAttribute=function(E,i,D){$e||Ot({});const w=J(E),$=J(i);return br(w,$,D)},t.addHook=function(E,i){typeof i=="function"&&Ze(b[E],i)},t.removeHook=function(E,i){if(i!==void 0){const D=bu(b[E],i);return D===-1?void 0:Au(b[E],D,1)[0]}return Lr(b[E])},t.removeHooks=function(E){b[E]=[]},t.removeAllHooks=function(){b=Hr()},t}var Bs=Ss(),Et={exports:{}};/*! https://mths.be/he v1.2.0 by @mathias | MIT license */Et.exports;(function(e,t){(function(r){var s=t,o=e&&e.exports==s&&e,u=typeof Rt=="object"&&Rt;(u.global===u||u.window===u)&&(r=u);var a=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,c=/[\x01-\x7F]/g,A=/[\x01-\t\x0B\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,x=/<\u20D2|=\u20E5|>\u20D2|\u205F\u200A|\u219D\u0338|\u2202\u0338|\u2220\u20D2|\u2229\uFE00|\u222A\uFE00|\u223C\u20D2|\u223D\u0331|\u223E\u0333|\u2242\u0338|\u224B\u0338|\u224D\u20D2|\u224E\u0338|\u224F\u0338|\u2250\u0338|\u2261\u20E5|\u2264\u20D2|\u2265\u20D2|\u2266\u0338|\u2267\u0338|\u2268\uFE00|\u2269\uFE00|\u226A\u0338|\u226A\u20D2|\u226B\u0338|\u226B\u20D2|\u227F\u0338|\u2282\u20D2|\u2283\u20D2|\u228A\uFE00|\u228B\uFE00|\u228F\u0338|\u2290\u0338|\u2293\uFE00|\u2294\uFE00|\u22B4\u20D2|\u22B5\u20D2|\u22D8\u0338|\u22D9\u0338|\u22DA\uFE00|\u22DB\uFE00|\u22F5\u0338|\u22F9\u0338|\u2933\u0338|\u29CF\u0338|\u29D0\u0338|\u2A6D\u0338|\u2A70\u0338|\u2A7D\u0338|\u2A7E\u0338|\u2AA1\u0338|\u2AA2\u0338|\u2AAC\uFE00|\u2AAD\uFE00|\u2AAF\u0338|\u2AB0\u0338|\u2AC5\u0338|\u2AC6\u0338|\u2ACB\uFE00|\u2ACC\uFE00|\u2AFD\u20E5|[\xA0-\u0113\u0116-\u0122\u0124-\u012B\u012E-\u014D\u0150-\u017E\u0192\u01B5\u01F5\u0237\u02C6\u02C7\u02D8-\u02DD\u0311\u0391-\u03A1\u03A3-\u03A9\u03B1-\u03C9\u03D1\u03D2\u03D5\u03D6\u03DC\u03DD\u03F0\u03F1\u03F5\u03F6\u0401-\u040C\u040E-\u044F\u0451-\u045C\u045E\u045F\u2002-\u2005\u2007-\u2010\u2013-\u2016\u2018-\u201A\u201C-\u201E\u2020-\u2022\u2025\u2026\u2030-\u2035\u2039\u203A\u203E\u2041\u2043\u2044\u204F\u2057\u205F-\u2063\u20AC\u20DB\u20DC\u2102\u2105\u210A-\u2113\u2115-\u211E\u2122\u2124\u2127-\u2129\u212C\u212D\u212F-\u2131\u2133-\u2138\u2145-\u2148\u2153-\u215E\u2190-\u219B\u219D-\u21A7\u21A9-\u21AE\u21B0-\u21B3\u21B5-\u21B7\u21BA-\u21DB\u21DD\u21E4\u21E5\u21F5\u21FD-\u2205\u2207-\u2209\u220B\u220C\u220F-\u2214\u2216-\u2218\u221A\u221D-\u2238\u223A-\u2257\u2259\u225A\u225C\u225F-\u2262\u2264-\u228B\u228D-\u229B\u229D-\u22A5\u22A7-\u22B0\u22B2-\u22BB\u22BD-\u22DB\u22DE-\u22E3\u22E6-\u22F7\u22F9-\u22FE\u2305\u2306\u2308-\u2310\u2312\u2313\u2315\u2316\u231C-\u231F\u2322\u2323\u232D\u232E\u2336\u233D\u233F\u237C\u23B0\u23B1\u23B4-\u23B6\u23DC-\u23DF\u23E2\u23E7\u2423\u24C8\u2500\u2502\u250C\u2510\u2514\u2518\u251C\u2524\u252C\u2534\u253C\u2550-\u256C\u2580\u2584\u2588\u2591-\u2593\u25A1\u25AA\u25AB\u25AD\u25AE\u25B1\u25B3-\u25B5\u25B8\u25B9\u25BD-\u25BF\u25C2\u25C3\u25CA\u25CB\u25EC\u25EF\u25F8-\u25FC\u2605\u2606\u260E\u2640\u2642\u2660\u2663\u2665\u2666\u266A\u266D-\u266F\u2713\u2717\u2720\u2736\u2758\u2772\u2773\u27C8\u27C9\u27E6-\u27ED\u27F5-\u27FA\u27FC\u27FF\u2902-\u2905\u290C-\u2913\u2916\u2919-\u2920\u2923-\u292A\u2933\u2935-\u2939\u293C\u293D\u2945\u2948-\u294B\u294E-\u2976\u2978\u2979\u297B-\u297F\u2985\u2986\u298B-\u2996\u299A\u299C\u299D\u29A4-\u29B7\u29B9\u29BB\u29BC\u29BE-\u29C5\u29C9\u29CD-\u29D0\u29DC-\u29DE\u29E3-\u29E5\u29EB\u29F4\u29F6\u2A00-\u2A02\u2A04\u2A06\u2A0C\u2A0D\u2A10-\u2A17\u2A22-\u2A27\u2A29\u2A2A\u2A2D-\u2A31\u2A33-\u2A3C\u2A3F\u2A40\u2A42-\u2A4D\u2A50\u2A53-\u2A58\u2A5A-\u2A5D\u2A5F\u2A66\u2A6A\u2A6D-\u2A75\u2A77-\u2A9A\u2A9D-\u2AA2\u2AA4-\u2AB0\u2AB3-\u2AC8\u2ACB\u2ACC\u2ACF-\u2ADB\u2AE4\u2AE6-\u2AE9\u2AEB-\u2AF3\u2AFD\uFB00-\uFB04]|\uD835[\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDCCF\uDD04\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDD6B]/g,T={"­":"shy","‌":"zwnj","‍":"zwj","‎":"lrm","⁣":"ic","⁢":"it","⁡":"af","‏":"rlm","​":"ZeroWidthSpace","⁠":"NoBreak","̑":"DownBreve","⃛":"tdot","⃜":"DotDot","	":"Tab","\n":"NewLine"," ":"puncsp"," ":"MediumSpace"," ":"thinsp"," ":"hairsp"," ":"emsp13"," ":"ensp"," ":"emsp14"," ":"emsp"," ":"numsp"," ":"nbsp","  ":"ThickSpace","‾":"oline",_:"lowbar","‐":"dash","–":"ndash","—":"mdash","―":"horbar",",":"comma",";":"semi","⁏":"bsemi",":":"colon","⩴":"Colone","!":"excl","¡":"iexcl","?":"quest","¿":"iquest",".":"period","‥":"nldr","…":"mldr","·":"middot","'":"apos","‘":"lsquo","’":"rsquo","‚":"sbquo","‹":"lsaquo","›":"rsaquo",'"':"quot","“":"ldquo","”":"rdquo","„":"bdquo","«":"laquo","»":"raquo","(":"lpar",")":"rpar","[":"lsqb","]":"rsqb","{":"lcub","}":"rcub","⌈":"lceil","⌉":"rceil","⌊":"lfloor","⌋":"rfloor","⦅":"lopar","⦆":"ropar","⦋":"lbrke","⦌":"rbrke","⦍":"lbrkslu","⦎":"rbrksld","⦏":"lbrksld","⦐":"rbrkslu","⦑":"langd","⦒":"rangd","⦓":"lparlt","⦔":"rpargt","⦕":"gtlPar","⦖":"ltrPar","⟦":"lobrk","⟧":"robrk","⟨":"lang","⟩":"rang","⟪":"Lang","⟫":"Rang","⟬":"loang","⟭":"roang","❲":"lbbrk","❳":"rbbrk","‖":"Vert","§":"sect","¶":"para","@":"commat","*":"ast","/":"sol",undefined:null,"&":"amp","#":"num","%":"percnt","‰":"permil","‱":"pertenk","†":"dagger","‡":"Dagger","•":"bull","⁃":"hybull","′":"prime","″":"Prime","‴":"tprime","⁗":"qprime","‵":"bprime","⁁":"caret","`":"grave","´":"acute","˜":"tilde","^":"Hat","¯":"macr","˘":"breve","˙":"dot","¨":"die","˚":"ring","˝":"dblac","¸":"cedil","˛":"ogon","ˆ":"circ","ˇ":"caron","°":"deg","©":"copy","®":"reg","℗":"copysr","℘":"wp","℞":"rx","℧":"mho","℩":"iiota","←":"larr","↚":"nlarr","→":"rarr","↛":"nrarr","↑":"uarr","↓":"darr","↔":"harr","↮":"nharr","↕":"varr","↖":"nwarr","↗":"nearr","↘":"searr","↙":"swarr","↝":"rarrw","↝̸":"nrarrw","↞":"Larr","↟":"Uarr","↠":"Rarr","↡":"Darr","↢":"larrtl","↣":"rarrtl","↤":"mapstoleft","↥":"mapstoup","↦":"map","↧":"mapstodown","↩":"larrhk","↪":"rarrhk","↫":"larrlp","↬":"rarrlp","↭":"harrw","↰":"lsh","↱":"rsh","↲":"ldsh","↳":"rdsh","↵":"crarr","↶":"cularr","↷":"curarr","↺":"olarr","↻":"orarr","↼":"lharu","↽":"lhard","↾":"uharr","↿":"uharl","⇀":"rharu","⇁":"rhard","⇂":"dharr","⇃":"dharl","⇄":"rlarr","⇅":"udarr","⇆":"lrarr","⇇":"llarr","⇈":"uuarr","⇉":"rrarr","⇊":"ddarr","⇋":"lrhar","⇌":"rlhar","⇐":"lArr","⇍":"nlArr","⇑":"uArr","⇒":"rArr","⇏":"nrArr","⇓":"dArr","⇔":"iff","⇎":"nhArr","⇕":"vArr","⇖":"nwArr","⇗":"neArr","⇘":"seArr","⇙":"swArr","⇚":"lAarr","⇛":"rAarr","⇝":"zigrarr","⇤":"larrb","⇥":"rarrb","⇵":"duarr","⇽":"loarr","⇾":"roarr","⇿":"hoarr","∀":"forall","∁":"comp","∂":"part","∂̸":"npart","∃":"exist","∄":"nexist","∅":"empty","∇":"Del","∈":"in","∉":"notin","∋":"ni","∌":"notni","϶":"bepsi","∏":"prod","∐":"coprod","∑":"sum","+":"plus","±":"pm","÷":"div","×":"times","<":"lt","≮":"nlt","<⃒":"nvlt","=":"equals","≠":"ne","=⃥":"bne","⩵":"Equal",">":"gt","≯":"ngt",">⃒":"nvgt","¬":"not","|":"vert","¦":"brvbar","−":"minus","∓":"mp","∔":"plusdo","⁄":"frasl","∖":"setmn","∗":"lowast","∘":"compfn","√":"Sqrt","∝":"prop","∞":"infin","∟":"angrt","∠":"ang","∠⃒":"nang","∡":"angmsd","∢":"angsph","∣":"mid","∤":"nmid","∥":"par","∦":"npar","∧":"and","∨":"or","∩":"cap","∩︀":"caps","∪":"cup","∪︀":"cups","∫":"int","∬":"Int","∭":"tint","⨌":"qint","∮":"oint","∯":"Conint","∰":"Cconint","∱":"cwint","∲":"cwconint","∳":"awconint","∴":"there4","∵":"becaus","∶":"ratio","∷":"Colon","∸":"minusd","∺":"mDDot","∻":"homtht","∼":"sim","≁":"nsim","∼⃒":"nvsim","∽":"bsim","∽̱":"race","∾":"ac","∾̳":"acE","∿":"acd","≀":"wr","≂":"esim","≂̸":"nesim","≃":"sime","≄":"nsime","≅":"cong","≇":"ncong","≆":"simne","≈":"ap","≉":"nap","≊":"ape","≋":"apid","≋̸":"napid","≌":"bcong","≍":"CupCap","≭":"NotCupCap","≍⃒":"nvap","≎":"bump","≎̸":"nbump","≏":"bumpe","≏̸":"nbumpe","≐":"doteq","≐̸":"nedot","≑":"eDot","≒":"efDot","≓":"erDot","≔":"colone","≕":"ecolon","≖":"ecir","≗":"cire","≙":"wedgeq","≚":"veeeq","≜":"trie","≟":"equest","≡":"equiv","≢":"nequiv","≡⃥":"bnequiv","≤":"le","≰":"nle","≤⃒":"nvle","≥":"ge","≱":"nge","≥⃒":"nvge","≦":"lE","≦̸":"nlE","≧":"gE","≧̸":"ngE","≨︀":"lvnE","≨":"lnE","≩":"gnE","≩︀":"gvnE","≪":"ll","≪̸":"nLtv","≪⃒":"nLt","≫":"gg","≫̸":"nGtv","≫⃒":"nGt","≬":"twixt","≲":"lsim","≴":"nlsim","≳":"gsim","≵":"ngsim","≶":"lg","≸":"ntlg","≷":"gl","≹":"ntgl","≺":"pr","⊀":"npr","≻":"sc","⊁":"nsc","≼":"prcue","⋠":"nprcue","≽":"sccue","⋡":"nsccue","≾":"prsim","≿":"scsim","≿̸":"NotSucceedsTilde","⊂":"sub","⊄":"nsub","⊂⃒":"vnsub","⊃":"sup","⊅":"nsup","⊃⃒":"vnsup","⊆":"sube","⊈":"nsube","⊇":"supe","⊉":"nsupe","⊊︀":"vsubne","⊊":"subne","⊋︀":"vsupne","⊋":"supne","⊍":"cupdot","⊎":"uplus","⊏":"sqsub","⊏̸":"NotSquareSubset","⊐":"sqsup","⊐̸":"NotSquareSuperset","⊑":"sqsube","⋢":"nsqsube","⊒":"sqsupe","⋣":"nsqsupe","⊓":"sqcap","⊓︀":"sqcaps","⊔":"sqcup","⊔︀":"sqcups","⊕":"oplus","⊖":"ominus","⊗":"otimes","⊘":"osol","⊙":"odot","⊚":"ocir","⊛":"oast","⊝":"odash","⊞":"plusb","⊟":"minusb","⊠":"timesb","⊡":"sdotb","⊢":"vdash","⊬":"nvdash","⊣":"dashv","⊤":"top","⊥":"bot","⊧":"models","⊨":"vDash","⊭":"nvDash","⊩":"Vdash","⊮":"nVdash","⊪":"Vvdash","⊫":"VDash","⊯":"nVDash","⊰":"prurel","⊲":"vltri","⋪":"nltri","⊳":"vrtri","⋫":"nrtri","⊴":"ltrie","⋬":"nltrie","⊴⃒":"nvltrie","⊵":"rtrie","⋭":"nrtrie","⊵⃒":"nvrtrie","⊶":"origof","⊷":"imof","⊸":"mumap","⊹":"hercon","⊺":"intcal","⊻":"veebar","⊽":"barvee","⊾":"angrtvb","⊿":"lrtri","⋀":"Wedge","⋁":"Vee","⋂":"xcap","⋃":"xcup","⋄":"diam","⋅":"sdot","⋆":"Star","⋇":"divonx","⋈":"bowtie","⋉":"ltimes","⋊":"rtimes","⋋":"lthree","⋌":"rthree","⋍":"bsime","⋎":"cuvee","⋏":"cuwed","⋐":"Sub","⋑":"Sup","⋒":"Cap","⋓":"Cup","⋔":"fork","⋕":"epar","⋖":"ltdot","⋗":"gtdot","⋘":"Ll","⋘̸":"nLl","⋙":"Gg","⋙̸":"nGg","⋚︀":"lesg","⋚":"leg","⋛":"gel","⋛︀":"gesl","⋞":"cuepr","⋟":"cuesc","⋦":"lnsim","⋧":"gnsim","⋨":"prnsim","⋩":"scnsim","⋮":"vellip","⋯":"ctdot","⋰":"utdot","⋱":"dtdot","⋲":"disin","⋳":"isinsv","⋴":"isins","⋵":"isindot","⋵̸":"notindot","⋶":"notinvc","⋷":"notinvb","⋹":"isinE","⋹̸":"notinE","⋺":"nisd","⋻":"xnis","⋼":"nis","⋽":"notnivc","⋾":"notnivb","⌅":"barwed","⌆":"Barwed","⌌":"drcrop","⌍":"dlcrop","⌎":"urcrop","⌏":"ulcrop","⌐":"bnot","⌒":"profline","⌓":"profsurf","⌕":"telrec","⌖":"target","⌜":"ulcorn","⌝":"urcorn","⌞":"dlcorn","⌟":"drcorn","⌢":"frown","⌣":"smile","⌭":"cylcty","⌮":"profalar","⌶":"topbot","⌽":"ovbar","⌿":"solbar","⍼":"angzarr","⎰":"lmoust","⎱":"rmoust","⎴":"tbrk","⎵":"bbrk","⎶":"bbrktbrk","⏜":"OverParenthesis","⏝":"UnderParenthesis","⏞":"OverBrace","⏟":"UnderBrace","⏢":"trpezium","⏧":"elinters","␣":"blank","─":"boxh","│":"boxv","┌":"boxdr","┐":"boxdl","└":"boxur","┘":"boxul","├":"boxvr","┤":"boxvl","┬":"boxhd","┴":"boxhu","┼":"boxvh","═":"boxH","║":"boxV","╒":"boxdR","╓":"boxDr","╔":"boxDR","╕":"boxdL","╖":"boxDl","╗":"boxDL","╘":"boxuR","╙":"boxUr","╚":"boxUR","╛":"boxuL","╜":"boxUl","╝":"boxUL","╞":"boxvR","╟":"boxVr","╠":"boxVR","╡":"boxvL","╢":"boxVl","╣":"boxVL","╤":"boxHd","╥":"boxhD","╦":"boxHD","╧":"boxHu","╨":"boxhU","╩":"boxHU","╪":"boxvH","╫":"boxVh","╬":"boxVH","▀":"uhblk","▄":"lhblk","█":"block","░":"blk14","▒":"blk12","▓":"blk34","□":"squ","▪":"squf","▫":"EmptyVerySmallSquare","▭":"rect","▮":"marker","▱":"fltns","△":"xutri","▴":"utrif","▵":"utri","▸":"rtrif","▹":"rtri","▽":"xdtri","▾":"dtrif","▿":"dtri","◂":"ltrif","◃":"ltri","◊":"loz","○":"cir","◬":"tridot","◯":"xcirc","◸":"ultri","◹":"urtri","◺":"lltri","◻":"EmptySmallSquare","◼":"FilledSmallSquare","★":"starf","☆":"star","☎":"phone","♀":"female","♂":"male","♠":"spades","♣":"clubs","♥":"hearts","♦":"diams","♪":"sung","✓":"check","✗":"cross","✠":"malt","✶":"sext","❘":"VerticalSeparator","⟈":"bsolhsub","⟉":"suphsol","⟵":"xlarr","⟶":"xrarr","⟷":"xharr","⟸":"xlArr","⟹":"xrArr","⟺":"xhArr","⟼":"xmap","⟿":"dzigrarr","⤂":"nvlArr","⤃":"nvrArr","⤄":"nvHarr","⤅":"Map","⤌":"lbarr","⤍":"rbarr","⤎":"lBarr","⤏":"rBarr","⤐":"RBarr","⤑":"DDotrahd","⤒":"UpArrowBar","⤓":"DownArrowBar","⤖":"Rarrtl","⤙":"latail","⤚":"ratail","⤛":"lAtail","⤜":"rAtail","⤝":"larrfs","⤞":"rarrfs","⤟":"larrbfs","⤠":"rarrbfs","⤣":"nwarhk","⤤":"nearhk","⤥":"searhk","⤦":"swarhk","⤧":"nwnear","⤨":"toea","⤩":"tosa","⤪":"swnwar","⤳":"rarrc","⤳̸":"nrarrc","⤵":"cudarrr","⤶":"ldca","⤷":"rdca","⤸":"cudarrl","⤹":"larrpl","⤼":"curarrm","⤽":"cularrp","⥅":"rarrpl","⥈":"harrcir","⥉":"Uarrocir","⥊":"lurdshar","⥋":"ldrushar","⥎":"LeftRightVector","⥏":"RightUpDownVector","⥐":"DownLeftRightVector","⥑":"LeftUpDownVector","⥒":"LeftVectorBar","⥓":"RightVectorBar","⥔":"RightUpVectorBar","⥕":"RightDownVectorBar","⥖":"DownLeftVectorBar","⥗":"DownRightVectorBar","⥘":"LeftUpVectorBar","⥙":"LeftDownVectorBar","⥚":"LeftTeeVector","⥛":"RightTeeVector","⥜":"RightUpTeeVector","⥝":"RightDownTeeVector","⥞":"DownLeftTeeVector","⥟":"DownRightTeeVector","⥠":"LeftUpTeeVector","⥡":"LeftDownTeeVector","⥢":"lHar","⥣":"uHar","⥤":"rHar","⥥":"dHar","⥦":"luruhar","⥧":"ldrdhar","⥨":"ruluhar","⥩":"rdldhar","⥪":"lharul","⥫":"llhard","⥬":"rharul","⥭":"lrhard","⥮":"udhar","⥯":"duhar","⥰":"RoundImplies","⥱":"erarr","⥲":"simrarr","⥳":"larrsim","⥴":"rarrsim","⥵":"rarrap","⥶":"ltlarr","⥸":"gtrarr","⥹":"subrarr","⥻":"suplarr","⥼":"lfisht","⥽":"rfisht","⥾":"ufisht","⥿":"dfisht","⦚":"vzigzag","⦜":"vangrt","⦝":"angrtvbd","⦤":"ange","⦥":"range","⦦":"dwangle","⦧":"uwangle","⦨":"angmsdaa","⦩":"angmsdab","⦪":"angmsdac","⦫":"angmsdad","⦬":"angmsdae","⦭":"angmsdaf","⦮":"angmsdag","⦯":"angmsdah","⦰":"bemptyv","⦱":"demptyv","⦲":"cemptyv","⦳":"raemptyv","⦴":"laemptyv","⦵":"ohbar","⦶":"omid","⦷":"opar","⦹":"operp","⦻":"olcross","⦼":"odsold","⦾":"olcir","⦿":"ofcir","⧀":"olt","⧁":"ogt","⧂":"cirscir","⧃":"cirE","⧄":"solb","⧅":"bsolb","⧉":"boxbox","⧍":"trisb","⧎":"rtriltri","⧏":"LeftTriangleBar","⧏̸":"NotLeftTriangleBar","⧐":"RightTriangleBar","⧐̸":"NotRightTriangleBar","⧜":"iinfin","⧝":"infintie","⧞":"nvinfin","⧣":"eparsl","⧤":"smeparsl","⧥":"eqvparsl","⧫":"lozf","⧴":"RuleDelayed","⧶":"dsol","⨀":"xodot","⨁":"xoplus","⨂":"xotime","⨄":"xuplus","⨆":"xsqcup","⨍":"fpartint","⨐":"cirfnint","⨑":"awint","⨒":"rppolint","⨓":"scpolint","⨔":"npolint","⨕":"pointint","⨖":"quatint","⨗":"intlarhk","⨢":"pluscir","⨣":"plusacir","⨤":"simplus","⨥":"plusdu","⨦":"plussim","⨧":"plustwo","⨩":"mcomma","⨪":"minusdu","⨭":"loplus","⨮":"roplus","⨯":"Cross","⨰":"timesd","⨱":"timesbar","⨳":"smashp","⨴":"lotimes","⨵":"rotimes","⨶":"otimesas","⨷":"Otimes","⨸":"odiv","⨹":"triplus","⨺":"triminus","⨻":"tritime","⨼":"iprod","⨿":"amalg","⩀":"capdot","⩂":"ncup","⩃":"ncap","⩄":"capand","⩅":"cupor","⩆":"cupcap","⩇":"capcup","⩈":"cupbrcap","⩉":"capbrcup","⩊":"cupcup","⩋":"capcap","⩌":"ccups","⩍":"ccaps","⩐":"ccupssm","⩓":"And","⩔":"Or","⩕":"andand","⩖":"oror","⩗":"orslope","⩘":"andslope","⩚":"andv","⩛":"orv","⩜":"andd","⩝":"ord","⩟":"wedbar","⩦":"sdote","⩪":"simdot","⩭":"congdot","⩭̸":"ncongdot","⩮":"easter","⩯":"apacir","⩰":"apE","⩰̸":"napE","⩱":"eplus","⩲":"pluse","⩳":"Esim","⩷":"eDDot","⩸":"equivDD","⩹":"ltcir","⩺":"gtcir","⩻":"ltquest","⩼":"gtquest","⩽":"les","⩽̸":"nles","⩾":"ges","⩾̸":"nges","⩿":"lesdot","⪀":"gesdot","⪁":"lesdoto","⪂":"gesdoto","⪃":"lesdotor","⪄":"gesdotol","⪅":"lap","⪆":"gap","⪇":"lne","⪈":"gne","⪉":"lnap","⪊":"gnap","⪋":"lEg","⪌":"gEl","⪍":"lsime","⪎":"gsime","⪏":"lsimg","⪐":"gsiml","⪑":"lgE","⪒":"glE","⪓":"lesges","⪔":"gesles","⪕":"els","⪖":"egs","⪗":"elsdot","⪘":"egsdot","⪙":"el","⪚":"eg","⪝":"siml","⪞":"simg","⪟":"simlE","⪠":"simgE","⪡":"LessLess","⪡̸":"NotNestedLessLess","⪢":"GreaterGreater","⪢̸":"NotNestedGreaterGreater","⪤":"glj","⪥":"gla","⪦":"ltcc","⪧":"gtcc","⪨":"lescc","⪩":"gescc","⪪":"smt","⪫":"lat","⪬":"smte","⪬︀":"smtes","⪭":"late","⪭︀":"lates","⪮":"bumpE","⪯":"pre","⪯̸":"npre","⪰":"sce","⪰̸":"nsce","⪳":"prE","⪴":"scE","⪵":"prnE","⪶":"scnE","⪷":"prap","⪸":"scap","⪹":"prnap","⪺":"scnap","⪻":"Pr","⪼":"Sc","⪽":"subdot","⪾":"supdot","⪿":"subplus","⫀":"supplus","⫁":"submult","⫂":"supmult","⫃":"subedot","⫄":"supedot","⫅":"subE","⫅̸":"nsubE","⫆":"supE","⫆̸":"nsupE","⫇":"subsim","⫈":"supsim","⫋︀":"vsubnE","⫋":"subnE","⫌︀":"vsupnE","⫌":"supnE","⫏":"csub","⫐":"csup","⫑":"csube","⫒":"csupe","⫓":"subsup","⫔":"supsub","⫕":"subsub","⫖":"supsup","⫗":"suphsub","⫘":"supdsub","⫙":"forkv","⫚":"topfork","⫛":"mlcp","⫤":"Dashv","⫦":"Vdashl","⫧":"Barv","⫨":"vBar","⫩":"vBarv","⫫":"Vbar","⫬":"Not","⫭":"bNot","⫮":"rnmid","⫯":"cirmid","⫰":"midcir","⫱":"topcir","⫲":"nhpar","⫳":"parsim","⫽":"parsl","⫽⃥":"nparsl","♭":"flat","♮":"natur","♯":"sharp","¤":"curren","¢":"cent",$:"dollar","£":"pound","¥":"yen","€":"euro","¹":"sup1","½":"half","⅓":"frac13","¼":"frac14","⅕":"frac15","⅙":"frac16","⅛":"frac18","²":"sup2","⅔":"frac23","⅖":"frac25","³":"sup3","¾":"frac34","⅗":"frac35","⅜":"frac38","⅘":"frac45","⅚":"frac56","⅝":"frac58","⅞":"frac78","𝒶":"ascr","𝕒":"aopf","𝔞":"afr","𝔸":"Aopf","𝔄":"Afr","𝒜":"Ascr",ª:"ordf",á:"aacute",Á:"Aacute",à:"agrave",À:"Agrave",ă:"abreve",Ă:"Abreve",â:"acirc",Â:"Acirc",å:"aring",Å:"angst",ä:"auml",Ä:"Auml",ã:"atilde",Ã:"Atilde",ą:"aogon",Ą:"Aogon",ā:"amacr",Ā:"Amacr",æ:"aelig",Æ:"AElig","𝒷":"bscr","𝕓":"bopf","𝔟":"bfr","𝔹":"Bopf",ℬ:"Bscr","𝔅":"Bfr","𝔠":"cfr","𝒸":"cscr","𝕔":"copf",ℭ:"Cfr","𝒞":"Cscr",ℂ:"Copf",ć:"cacute",Ć:"Cacute",ĉ:"ccirc",Ĉ:"Ccirc",č:"ccaron",Č:"Ccaron",ċ:"cdot",Ċ:"Cdot",ç:"ccedil",Ç:"Ccedil","℅":"incare","𝔡":"dfr","ⅆ":"dd","𝕕":"dopf","𝒹":"dscr","𝒟":"Dscr","𝔇":"Dfr","ⅅ":"DD","𝔻":"Dopf",ď:"dcaron",Ď:"Dcaron",đ:"dstrok",Đ:"Dstrok",ð:"eth",Ð:"ETH","ⅇ":"ee",ℯ:"escr","𝔢":"efr","𝕖":"eopf",ℰ:"Escr","𝔈":"Efr","𝔼":"Eopf",é:"eacute",É:"Eacute",è:"egrave",È:"Egrave",ê:"ecirc",Ê:"Ecirc",ě:"ecaron",Ě:"Ecaron",ë:"euml",Ë:"Euml",ė:"edot",Ė:"Edot",ę:"eogon",Ę:"Eogon",ē:"emacr",Ē:"Emacr","𝔣":"ffr","𝕗":"fopf","𝒻":"fscr","𝔉":"Ffr","𝔽":"Fopf",ℱ:"Fscr",ﬀ:"fflig",ﬃ:"ffilig",ﬄ:"ffllig",ﬁ:"filig",fj:"fjlig",ﬂ:"fllig",ƒ:"fnof",ℊ:"gscr","𝕘":"gopf","𝔤":"gfr","𝒢":"Gscr","𝔾":"Gopf","𝔊":"Gfr",ǵ:"gacute",ğ:"gbreve",Ğ:"Gbreve",ĝ:"gcirc",Ĝ:"Gcirc",ġ:"gdot",Ġ:"Gdot",Ģ:"Gcedil","𝔥":"hfr",ℎ:"planckh","𝒽":"hscr","𝕙":"hopf",ℋ:"Hscr",ℌ:"Hfr",ℍ:"Hopf",ĥ:"hcirc",Ĥ:"Hcirc",ℏ:"hbar",ħ:"hstrok",Ħ:"Hstrok","𝕚":"iopf","𝔦":"ifr","𝒾":"iscr","ⅈ":"ii","𝕀":"Iopf",ℐ:"Iscr",ℑ:"Im",í:"iacute",Í:"Iacute",ì:"igrave",Ì:"Igrave",î:"icirc",Î:"Icirc",ï:"iuml",Ï:"Iuml",ĩ:"itilde",Ĩ:"Itilde",İ:"Idot",į:"iogon",Į:"Iogon",ī:"imacr",Ī:"Imacr",ĳ:"ijlig",Ĳ:"IJlig",ı:"imath","𝒿":"jscr","𝕛":"jopf","𝔧":"jfr","𝒥":"Jscr","𝔍":"Jfr","𝕁":"Jopf",ĵ:"jcirc",Ĵ:"Jcirc","ȷ":"jmath","𝕜":"kopf","𝓀":"kscr","𝔨":"kfr","𝒦":"Kscr","𝕂":"Kopf","𝔎":"Kfr",ķ:"kcedil",Ķ:"Kcedil","𝔩":"lfr","𝓁":"lscr",ℓ:"ell","𝕝":"lopf",ℒ:"Lscr","𝔏":"Lfr","𝕃":"Lopf",ĺ:"lacute",Ĺ:"Lacute",ľ:"lcaron",Ľ:"Lcaron",ļ:"lcedil",Ļ:"Lcedil",ł:"lstrok",Ł:"Lstrok",ŀ:"lmidot",Ŀ:"Lmidot","𝔪":"mfr","𝕞":"mopf","𝓂":"mscr","𝔐":"Mfr","𝕄":"Mopf",ℳ:"Mscr","𝔫":"nfr","𝕟":"nopf","𝓃":"nscr",ℕ:"Nopf","𝒩":"Nscr","𝔑":"Nfr",ń:"nacute",Ń:"Nacute",ň:"ncaron",Ň:"Ncaron",ñ:"ntilde",Ñ:"Ntilde",ņ:"ncedil",Ņ:"Ncedil","№":"numero",ŋ:"eng",Ŋ:"ENG","𝕠":"oopf","𝔬":"ofr",ℴ:"oscr","𝒪":"Oscr","𝔒":"Ofr","𝕆":"Oopf",º:"ordm",ó:"oacute",Ó:"Oacute",ò:"ograve",Ò:"Ograve",ô:"ocirc",Ô:"Ocirc",ö:"ouml",Ö:"Ouml",ő:"odblac",Ő:"Odblac",õ:"otilde",Õ:"Otilde",ø:"oslash",Ø:"Oslash",ō:"omacr",Ō:"Omacr",œ:"oelig",Œ:"OElig","𝔭":"pfr","𝓅":"pscr","𝕡":"popf",ℙ:"Popf","𝔓":"Pfr","𝒫":"Pscr","𝕢":"qopf","𝔮":"qfr","𝓆":"qscr","𝒬":"Qscr","𝔔":"Qfr",ℚ:"Qopf",ĸ:"kgreen","𝔯":"rfr","𝕣":"ropf","𝓇":"rscr",ℛ:"Rscr",ℜ:"Re",ℝ:"Ropf",ŕ:"racute",Ŕ:"Racute",ř:"rcaron",Ř:"Rcaron",ŗ:"rcedil",Ŗ:"Rcedil","𝕤":"sopf","𝓈":"sscr","𝔰":"sfr","𝕊":"Sopf","𝔖":"Sfr","𝒮":"Sscr","Ⓢ":"oS",ś:"sacute",Ś:"Sacute",ŝ:"scirc",Ŝ:"Scirc",š:"scaron",Š:"Scaron",ş:"scedil",Ş:"Scedil",ß:"szlig","𝔱":"tfr","𝓉":"tscr","𝕥":"topf","𝒯":"Tscr","𝔗":"Tfr","𝕋":"Topf",ť:"tcaron",Ť:"Tcaron",ţ:"tcedil",Ţ:"Tcedil","™":"trade",ŧ:"tstrok",Ŧ:"Tstrok","𝓊":"uscr","𝕦":"uopf","𝔲":"ufr","𝕌":"Uopf","𝔘":"Ufr","𝒰":"Uscr",ú:"uacute",Ú:"Uacute",ù:"ugrave",Ù:"Ugrave",ŭ:"ubreve",Ŭ:"Ubreve",û:"ucirc",Û:"Ucirc",ů:"uring",Ů:"Uring",ü:"uuml",Ü:"Uuml",ű:"udblac",Ű:"Udblac",ũ:"utilde",Ũ:"Utilde",ų:"uogon",Ų:"Uogon",ū:"umacr",Ū:"Umacr","𝔳":"vfr","𝕧":"vopf","𝓋":"vscr","𝔙":"Vfr","𝕍":"Vopf","𝒱":"Vscr","𝕨":"wopf","𝓌":"wscr","𝔴":"wfr","𝒲":"Wscr","𝕎":"Wopf","𝔚":"Wfr",ŵ:"wcirc",Ŵ:"Wcirc","𝔵":"xfr","𝓍":"xscr","𝕩":"xopf","𝕏":"Xopf","𝔛":"Xfr","𝒳":"Xscr","𝔶":"yfr","𝓎":"yscr","𝕪":"yopf","𝒴":"Yscr","𝔜":"Yfr","𝕐":"Yopf",ý:"yacute",Ý:"Yacute",ŷ:"ycirc",Ŷ:"Ycirc",ÿ:"yuml",Ÿ:"Yuml","𝓏":"zscr","𝔷":"zfr","𝕫":"zopf",ℨ:"Zfr",ℤ:"Zopf","𝒵":"Zscr",ź:"zacute",Ź:"Zacute",ž:"zcaron",Ž:"Zcaron",ż:"zdot",Ż:"Zdot",Ƶ:"imped",þ:"thorn",Þ:"THORN",ŉ:"napos",α:"alpha",Α:"Alpha",β:"beta",Β:"Beta",γ:"gamma",Γ:"Gamma",δ:"delta",Δ:"Delta",ε:"epsi","ϵ":"epsiv",Ε:"Epsilon",ϝ:"gammad",Ϝ:"Gammad",ζ:"zeta",Ζ:"Zeta",η:"eta",Η:"Eta",θ:"theta",ϑ:"thetav",Θ:"Theta",ι:"iota",Ι:"Iota",κ:"kappa",ϰ:"kappav",Κ:"Kappa",λ:"lambda",Λ:"Lambda",μ:"mu",µ:"micro",Μ:"Mu",ν:"nu",Ν:"Nu",ξ:"xi",Ξ:"Xi",ο:"omicron",Ο:"Omicron",π:"pi",ϖ:"piv",Π:"Pi",ρ:"rho",ϱ:"rhov",Ρ:"Rho",σ:"sigma",Σ:"Sigma",ς:"sigmaf",τ:"tau",Τ:"Tau",υ:"upsi",Υ:"Upsilon",ϒ:"Upsi",φ:"phi",ϕ:"phiv",Φ:"Phi",χ:"chi",Χ:"Chi",ψ:"psi",Ψ:"Psi",ω:"omega",Ω:"ohm",а:"acy",А:"Acy",б:"bcy",Б:"Bcy",в:"vcy",В:"Vcy",г:"gcy",Г:"Gcy",ѓ:"gjcy",Ѓ:"GJcy",д:"dcy",Д:"Dcy",ђ:"djcy",Ђ:"DJcy",е:"iecy",Е:"IEcy",ё:"iocy",Ё:"IOcy",є:"jukcy",Є:"Jukcy",ж:"zhcy",Ж:"ZHcy",з:"zcy",З:"Zcy",ѕ:"dscy",Ѕ:"DScy",и:"icy",И:"Icy",і:"iukcy",І:"Iukcy",ї:"yicy",Ї:"YIcy",й:"jcy",Й:"Jcy",ј:"jsercy",Ј:"Jsercy",к:"kcy",К:"Kcy",ќ:"kjcy",Ќ:"KJcy",л:"lcy",Л:"Lcy",љ:"ljcy",Љ:"LJcy",м:"mcy",М:"Mcy",н:"ncy",Н:"Ncy",њ:"njcy",Њ:"NJcy",о:"ocy",О:"Ocy",п:"pcy",П:"Pcy",р:"rcy",Р:"Rcy",с:"scy",С:"Scy",т:"tcy",Т:"Tcy",ћ:"tshcy",Ћ:"TSHcy",у:"ucy",У:"Ucy",ў:"ubrcy",Ў:"Ubrcy",ф:"fcy",Ф:"Fcy",х:"khcy",Х:"KHcy",ц:"tscy",Ц:"TScy",ч:"chcy",Ч:"CHcy",џ:"dzcy",Џ:"DZcy",ш:"shcy",Ш:"SHcy",щ:"shchcy",Щ:"SHCHcy",ъ:"hardcy",Ъ:"HARDcy",ы:"ycy",Ы:"Ycy",ь:"softcy",Ь:"SOFTcy",э:"ecy",Э:"Ecy",ю:"yucy",Ю:"YUcy",я:"yacy",Я:"YAcy",ℵ:"aleph",ℶ:"beth",ℷ:"gimel",ℸ:"daleth"},I=/["&'<>`]/g,W={'"':"&quot;","&":"&amp;","'":"&#x27;","<":"&lt;",">":"&gt;","`":"&#x60;"},v=/&#(?:[xX][^a-fA-F0-9]|[^0-9xX])/,V=/[\0-\x08\x0B\x0E-\x1F\x7F-\x9F\uFDD0-\uFDEF\uFFFE\uFFFF]|[\uD83F\uD87F\uD8BF\uD8FF\uD93F\uD97F\uD9BF\uD9FF\uDA3F\uDA7F\uDABF\uDAFF\uDB3F\uDB7F\uDBBF\uDBFF][\uDFFE\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,le=/&(CounterClockwiseContourIntegral|DoubleLongLeftRightArrow|ClockwiseContourIntegral|NotNestedGreaterGreater|NotSquareSupersetEqual|DiacriticalDoubleAcute|NotRightTriangleEqual|NotSucceedsSlantEqual|NotPrecedesSlantEqual|CloseCurlyDoubleQuote|NegativeVeryThinSpace|DoubleContourIntegral|FilledVerySmallSquare|CapitalDifferentialD|OpenCurlyDoubleQuote|EmptyVerySmallSquare|NestedGreaterGreater|DoubleLongRightArrow|NotLeftTriangleEqual|NotGreaterSlantEqual|ReverseUpEquilibrium|DoubleLeftRightArrow|NotSquareSubsetEqual|NotDoubleVerticalBar|RightArrowLeftArrow|NotGreaterFullEqual|NotRightTriangleBar|SquareSupersetEqual|DownLeftRightVector|DoubleLongLeftArrow|leftrightsquigarrow|LeftArrowRightArrow|NegativeMediumSpace|blacktriangleright|RightDownVectorBar|PrecedesSlantEqual|RightDoubleBracket|SucceedsSlantEqual|NotLeftTriangleBar|RightTriangleEqual|SquareIntersection|RightDownTeeVector|ReverseEquilibrium|NegativeThickSpace|longleftrightarrow|Longleftrightarrow|LongLeftRightArrow|DownRightTeeVector|DownRightVectorBar|GreaterSlantEqual|SquareSubsetEqual|LeftDownVectorBar|LeftDoubleBracket|VerticalSeparator|rightleftharpoons|NotGreaterGreater|NotSquareSuperset|blacktriangleleft|blacktriangledown|NegativeThinSpace|LeftDownTeeVector|NotLessSlantEqual|leftrightharpoons|DoubleUpDownArrow|DoubleVerticalBar|LeftTriangleEqual|FilledSmallSquare|twoheadrightarrow|NotNestedLessLess|DownLeftTeeVector|DownLeftVectorBar|RightAngleBracket|NotTildeFullEqual|NotReverseElement|RightUpDownVector|DiacriticalTilde|NotSucceedsTilde|circlearrowright|NotPrecedesEqual|rightharpoondown|DoubleRightArrow|NotSucceedsEqual|NonBreakingSpace|NotRightTriangle|LessEqualGreater|RightUpTeeVector|LeftAngleBracket|GreaterFullEqual|DownArrowUpArrow|RightUpVectorBar|twoheadleftarrow|GreaterEqualLess|downharpoonright|RightTriangleBar|ntrianglerighteq|NotSupersetEqual|LeftUpDownVector|DiacriticalAcute|rightrightarrows|vartriangleright|UpArrowDownArrow|DiacriticalGrave|UnderParenthesis|EmptySmallSquare|LeftUpVectorBar|leftrightarrows|DownRightVector|downharpoonleft|trianglerighteq|ShortRightArrow|OverParenthesis|DoubleLeftArrow|DoubleDownArrow|NotSquareSubset|bigtriangledown|ntrianglelefteq|UpperRightArrow|curvearrowright|vartriangleleft|NotLeftTriangle|nleftrightarrow|LowerRightArrow|NotHumpDownHump|NotGreaterTilde|rightthreetimes|LeftUpTeeVector|NotGreaterEqual|straightepsilon|LeftTriangleBar|rightsquigarrow|ContourIntegral|rightleftarrows|CloseCurlyQuote|RightDownVector|LeftRightVector|nLeftrightarrow|leftharpoondown|circlearrowleft|SquareSuperset|OpenCurlyQuote|hookrightarrow|HorizontalLine|DiacriticalDot|NotLessGreater|ntriangleright|DoubleRightTee|InvisibleComma|InvisibleTimes|LowerLeftArrow|DownLeftVector|NotSubsetEqual|curvearrowleft|trianglelefteq|NotVerticalBar|TildeFullEqual|downdownarrows|NotGreaterLess|RightTeeVector|ZeroWidthSpace|looparrowright|LongRightArrow|doublebarwedge|ShortLeftArrow|ShortDownArrow|RightVectorBar|GreaterGreater|ReverseElement|rightharpoonup|LessSlantEqual|leftthreetimes|upharpoonright|rightarrowtail|LeftDownVector|Longrightarrow|NestedLessLess|UpperLeftArrow|nshortparallel|leftleftarrows|leftrightarrow|Leftrightarrow|LeftRightArrow|longrightarrow|upharpoonleft|RightArrowBar|ApplyFunction|LeftTeeVector|leftarrowtail|NotEqualTilde|varsubsetneqq|varsupsetneqq|RightTeeArrow|SucceedsEqual|SucceedsTilde|LeftVectorBar|SupersetEqual|hookleftarrow|DifferentialD|VerticalTilde|VeryThinSpace|blacktriangle|bigtriangleup|LessFullEqual|divideontimes|leftharpoonup|UpEquilibrium|ntriangleleft|RightTriangle|measuredangle|shortparallel|longleftarrow|Longleftarrow|LongLeftArrow|DoubleLeftTee|Poincareplane|PrecedesEqual|triangleright|DoubleUpArrow|RightUpVector|fallingdotseq|looparrowleft|PrecedesTilde|NotTildeEqual|NotTildeTilde|smallsetminus|Proportional|triangleleft|triangledown|UnderBracket|NotHumpEqual|exponentiale|ExponentialE|NotLessTilde|HilbertSpace|RightCeiling|blacklozenge|varsupsetneq|HumpDownHump|GreaterEqual|VerticalLine|LeftTeeArrow|NotLessEqual|DownTeeArrow|LeftTriangle|varsubsetneq|Intersection|NotCongruent|DownArrowBar|LeftUpVector|LeftArrowBar|risingdotseq|GreaterTilde|RoundImplies|SquareSubset|ShortUpArrow|NotSuperset|quaternions|precnapprox|backepsilon|preccurlyeq|OverBracket|blacksquare|MediumSpace|VerticalBar|circledcirc|circleddash|CircleMinus|CircleTimes|LessGreater|curlyeqprec|curlyeqsucc|diamondsuit|UpDownArrow|Updownarrow|RuleDelayed|Rrightarrow|updownarrow|RightVector|nRightarrow|nrightarrow|eqslantless|LeftCeiling|Equilibrium|SmallCircle|expectation|NotSucceeds|thickapprox|GreaterLess|SquareUnion|NotPrecedes|NotLessLess|straightphi|succnapprox|succcurlyeq|SubsetEqual|sqsupseteq|Proportion|Laplacetrf|ImaginaryI|supsetneqq|NotGreater|gtreqqless|NotElement|ThickSpace|TildeEqual|TildeTilde|Fouriertrf|rmoustache|EqualTilde|eqslantgtr|UnderBrace|LeftVector|UpArrowBar|nLeftarrow|nsubseteqq|subsetneqq|nsupseteqq|nleftarrow|succapprox|lessapprox|UpTeeArrow|upuparrows|curlywedge|lesseqqgtr|varepsilon|varnothing|RightFloor|complement|CirclePlus|sqsubseteq|Lleftarrow|circledast|RightArrow|Rightarrow|rightarrow|lmoustache|Bernoullis|precapprox|mapstoleft|mapstodown|longmapsto|dotsquare|downarrow|DoubleDot|nsubseteq|supsetneq|leftarrow|nsupseteq|subsetneq|ThinSpace|ngeqslant|subseteqq|HumpEqual|NotSubset|triangleq|NotCupCap|lesseqgtr|heartsuit|TripleDot|Leftarrow|Coproduct|Congruent|varpropto|complexes|gvertneqq|LeftArrow|LessTilde|supseteqq|MinusPlus|CircleDot|nleqslant|NotExists|gtreqless|nparallel|UnionPlus|LeftFloor|checkmark|CenterDot|centerdot|Mellintrf|gtrapprox|bigotimes|OverBrace|spadesuit|therefore|pitchfork|rationals|PlusMinus|Backslash|Therefore|DownBreve|backsimeq|backprime|DownArrow|nshortmid|Downarrow|lvertneqq|eqvparsl|imagline|imagpart|infintie|integers|Integral|intercal|LessLess|Uarrocir|intlarhk|sqsupset|angmsdaf|sqsubset|llcorner|vartheta|cupbrcap|lnapprox|Superset|SuchThat|succnsim|succneqq|angmsdag|biguplus|curlyvee|trpezium|Succeeds|NotTilde|bigwedge|angmsdah|angrtvbd|triminus|cwconint|fpartint|lrcorner|smeparsl|subseteq|urcorner|lurdshar|laemptyv|DDotrahd|approxeq|ldrushar|awconint|mapstoup|backcong|shortmid|triangle|geqslant|gesdotol|timesbar|circledR|circledS|setminus|multimap|naturals|scpolint|ncongdot|RightTee|boxminus|gnapprox|boxtimes|andslope|thicksim|angmsdaa|varsigma|cirfnint|rtriltri|angmsdab|rppolint|angmsdac|barwedge|drbkarow|clubsuit|thetasym|bsolhsub|capbrcup|dzigrarr|doteqdot|DotEqual|dotminus|UnderBar|NotEqual|realpart|otimesas|ulcorner|hksearow|hkswarow|parallel|PartialD|elinters|emptyset|plusacir|bbrktbrk|angmsdad|pointint|bigoplus|angmsdae|Precedes|bigsqcup|varkappa|notindot|supseteq|precneqq|precnsim|profalar|profline|profsurf|leqslant|lesdotor|raemptyv|subplus|notnivb|notnivc|subrarr|zigrarr|vzigzag|submult|subedot|Element|between|cirscir|larrbfs|larrsim|lotimes|lbrksld|lbrkslu|lozenge|ldrdhar|dbkarow|bigcirc|epsilon|simrarr|simplus|ltquest|Epsilon|luruhar|gtquest|maltese|npolint|eqcolon|npreceq|bigodot|ddagger|gtrless|bnequiv|harrcir|ddotseq|equivDD|backsim|demptyv|nsqsube|nsqsupe|Upsilon|nsubset|upsilon|minusdu|nsucceq|swarrow|nsupset|coloneq|searrow|boxplus|napprox|natural|asympeq|alefsym|congdot|nearrow|bigstar|diamond|supplus|tritime|LeftTee|nvinfin|triplus|NewLine|nvltrie|nvrtrie|nwarrow|nexists|Diamond|ruluhar|Implies|supmult|angzarr|suplarr|suphsub|questeq|because|digamma|Because|olcross|bemptyv|omicron|Omicron|rotimes|NoBreak|intprod|angrtvb|orderof|uwangle|suphsol|lesdoto|orslope|DownTee|realine|cudarrl|rdldhar|OverBar|supedot|lessdot|supdsub|topfork|succsim|rbrkslu|rbrksld|pertenk|cudarrr|isindot|planckh|lessgtr|pluscir|gesdoto|plussim|plustwo|lesssim|cularrp|rarrsim|Cayleys|notinva|notinvb|notinvc|UpArrow|Uparrow|uparrow|NotLess|dwangle|precsim|Product|curarrm|Cconint|dotplus|rarrbfs|ccupssm|Cedilla|cemptyv|notniva|quatint|frac35|frac38|frac45|frac56|frac58|frac78|tridot|xoplus|gacute|gammad|Gammad|lfisht|lfloor|bigcup|sqsupe|gbreve|Gbreve|lharul|sqsube|sqcups|Gcedil|apacir|llhard|lmidot|Lmidot|lmoust|andand|sqcaps|approx|Abreve|spades|circeq|tprime|divide|topcir|Assign|topbot|gesdot|divonx|xuplus|timesd|gesles|atilde|solbar|SOFTcy|loplus|timesb|lowast|lowbar|dlcorn|dlcrop|softcy|dollar|lparlt|thksim|lrhard|Atilde|lsaquo|smashp|bigvee|thinsp|wreath|bkarow|lsquor|lstrok|Lstrok|lthree|ltimes|ltlarr|DotDot|simdot|ltrPar|weierp|xsqcup|angmsd|sigmav|sigmaf|zeetrf|Zcaron|zcaron|mapsto|vsupne|thetav|cirmid|marker|mcomma|Zacute|vsubnE|there4|gtlPar|vsubne|bottom|gtrarr|SHCHcy|shchcy|midast|midcir|middot|minusb|minusd|gtrdot|bowtie|sfrown|mnplus|models|colone|seswar|Colone|mstpos|searhk|gtrsim|nacute|Nacute|boxbox|telrec|hairsp|Tcedil|nbumpe|scnsim|ncaron|Ncaron|ncedil|Ncedil|hamilt|Scedil|nearhk|hardcy|HARDcy|tcedil|Tcaron|commat|nequiv|nesear|tcaron|target|hearts|nexist|varrho|scedil|Scaron|scaron|hellip|Sacute|sacute|hercon|swnwar|compfn|rtimes|rthree|rsquor|rsaquo|zacute|wedgeq|homtht|barvee|barwed|Barwed|rpargt|horbar|conint|swarhk|roplus|nltrie|hslash|hstrok|Hstrok|rmoust|Conint|bprime|hybull|hyphen|iacute|Iacute|supsup|supsub|supsim|varphi|coprod|brvbar|agrave|Supset|supset|igrave|Igrave|notinE|Agrave|iiiint|iinfin|copysr|wedbar|Verbar|vangrt|becaus|incare|verbar|inodot|bullet|drcorn|intcal|drcrop|cularr|vellip|Utilde|bumpeq|cupcap|dstrok|Dstrok|CupCap|cupcup|cupdot|eacute|Eacute|supdot|iquest|easter|ecaron|Ecaron|ecolon|isinsv|utilde|itilde|Itilde|curarr|succeq|Bumpeq|cacute|ulcrop|nparsl|Cacute|nprcue|egrave|Egrave|nrarrc|nrarrw|subsup|subsub|nrtrie|jsercy|nsccue|Jsercy|kappav|kcedil|Kcedil|subsim|ulcorn|nsimeq|egsdot|veebar|kgreen|capand|elsdot|Subset|subset|curren|aacute|lacute|Lacute|emptyv|ntilde|Ntilde|lagran|lambda|Lambda|capcap|Ugrave|langle|subdot|emsp13|numero|emsp14|nvdash|nvDash|nVdash|nVDash|ugrave|ufisht|nvHarr|larrfs|nvlArr|larrhk|larrlp|larrpl|nvrArr|Udblac|nwarhk|larrtl|nwnear|oacute|Oacute|latail|lAtail|sstarf|lbrace|odblac|Odblac|lbrack|udblac|odsold|eparsl|lcaron|Lcaron|ograve|Ograve|lcedil|Lcedil|Aacute|ssmile|ssetmn|squarf|ldquor|capcup|ominus|cylcty|rharul|eqcirc|dagger|rfloor|rfisht|Dagger|daleth|equals|origof|capdot|equest|dcaron|Dcaron|rdquor|oslash|Oslash|otilde|Otilde|otimes|Otimes|urcrop|Ubreve|ubreve|Yacute|Uacute|uacute|Rcedil|rcedil|urcorn|parsim|Rcaron|Vdashl|rcaron|Tstrok|percnt|period|permil|Exists|yacute|rbrack|rbrace|phmmat|ccaron|Ccaron|planck|ccedil|plankv|tstrok|female|plusdo|plusdu|ffilig|plusmn|ffllig|Ccedil|rAtail|dfisht|bernou|ratail|Rarrtl|rarrtl|angsph|rarrpl|rarrlp|rarrhk|xwedge|xotime|forall|ForAll|Vvdash|vsupnE|preceq|bigcap|frac12|frac13|frac14|primes|rarrfs|prnsim|frac15|Square|frac16|square|lesdot|frac18|frac23|propto|prurel|rarrap|rangle|puncsp|frac25|Racute|qprime|racute|lesges|frac34|abreve|AElig|eqsim|utdot|setmn|urtri|Equal|Uring|seArr|uring|searr|dashv|Dashv|mumap|nabla|iogon|Iogon|sdote|sdotb|scsim|napid|napos|equiv|natur|Acirc|dblac|erarr|nbump|iprod|erDot|ucirc|awint|esdot|angrt|ncong|isinE|scnap|Scirc|scirc|ndash|isins|Ubrcy|nearr|neArr|isinv|nedot|ubrcy|acute|Ycirc|iukcy|Iukcy|xutri|nesim|caret|jcirc|Jcirc|caron|twixt|ddarr|sccue|exist|jmath|sbquo|ngeqq|angst|ccaps|lceil|ngsim|UpTee|delta|Delta|rtrif|nharr|nhArr|nhpar|rtrie|jukcy|Jukcy|kappa|rsquo|Kappa|nlarr|nlArr|TSHcy|rrarr|aogon|Aogon|fflig|xrarr|tshcy|ccirc|nleqq|filig|upsih|nless|dharl|nlsim|fjlig|ropar|nltri|dharr|robrk|roarr|fllig|fltns|roang|rnmid|subnE|subne|lAarr|trisb|Ccirc|acirc|ccups|blank|VDash|forkv|Vdash|langd|cedil|blk12|blk14|laquo|strns|diams|notin|vDash|larrb|blk34|block|disin|uplus|vdash|vBarv|aelig|starf|Wedge|check|xrArr|lates|lbarr|lBarr|notni|lbbrk|bcong|frasl|lbrke|frown|vrtri|vprop|vnsup|gamma|Gamma|wedge|xodot|bdquo|srarr|doteq|ldquo|boxdl|boxdL|gcirc|Gcirc|boxDl|boxDL|boxdr|boxdR|boxDr|TRADE|trade|rlhar|boxDR|vnsub|npart|vltri|rlarr|boxhd|boxhD|nprec|gescc|nrarr|nrArr|boxHd|boxHD|boxhu|boxhU|nrtri|boxHu|clubs|boxHU|times|colon|Colon|gimel|xlArr|Tilde|nsime|tilde|nsmid|nspar|THORN|thorn|xlarr|nsube|nsubE|thkap|xhArr|comma|nsucc|boxul|boxuL|nsupe|nsupE|gneqq|gnsim|boxUl|boxUL|grave|boxur|boxuR|boxUr|boxUR|lescc|angle|bepsi|boxvh|varpi|boxvH|numsp|Theta|gsime|gsiml|theta|boxVh|boxVH|boxvl|gtcir|gtdot|boxvL|boxVl|boxVL|crarr|cross|Cross|nvsim|boxvr|nwarr|nwArr|sqsup|dtdot|Uogon|lhard|lharu|dtrif|ocirc|Ocirc|lhblk|duarr|odash|sqsub|Hacek|sqcup|llarr|duhar|oelig|OElig|ofcir|boxvR|uogon|lltri|boxVr|csube|uuarr|ohbar|csupe|ctdot|olarr|olcir|harrw|oline|sqcap|omacr|Omacr|omega|Omega|boxVR|aleph|lneqq|lnsim|loang|loarr|rharu|lobrk|hcirc|operp|oplus|rhard|Hcirc|orarr|Union|order|ecirc|Ecirc|cuepr|szlig|cuesc|breve|reals|eDDot|Breve|hoarr|lopar|utrif|rdquo|Umacr|umacr|efDot|swArr|ultri|alpha|rceil|ovbar|swarr|Wcirc|wcirc|smtes|smile|bsemi|lrarr|aring|parsl|lrhar|bsime|uhblk|lrtri|cupor|Aring|uharr|uharl|slarr|rbrke|bsolb|lsime|rbbrk|RBarr|lsimg|phone|rBarr|rbarr|icirc|lsquo|Icirc|emacr|Emacr|ratio|simne|plusb|simlE|simgE|simeq|pluse|ltcir|ltdot|empty|xharr|xdtri|iexcl|Alpha|ltrie|rarrw|pound|ltrif|xcirc|bumpe|prcue|bumpE|asymp|amacr|cuvee|Sigma|sigma|iiint|udhar|iiota|ijlig|IJlig|supnE|imacr|Imacr|prime|Prime|image|prnap|eogon|Eogon|rarrc|mdash|mDDot|cuwed|imath|supne|imped|Amacr|udarr|prsim|micro|rarrb|cwint|raquo|infin|eplus|range|rangd|Ucirc|radic|minus|amalg|veeeq|rAarr|epsiv|ycirc|quest|sharp|quot|zwnj|Qscr|race|qscr|Qopf|qopf|qint|rang|Rang|Zscr|zscr|Zopf|zopf|rarr|rArr|Rarr|Pscr|pscr|prop|prod|prnE|prec|ZHcy|zhcy|prap|Zeta|zeta|Popf|popf|Zdot|plus|zdot|Yuml|yuml|phiv|YUcy|yucy|Yscr|yscr|perp|Yopf|yopf|part|para|YIcy|Ouml|rcub|yicy|YAcy|rdca|ouml|osol|Oscr|rdsh|yacy|real|oscr|xvee|andd|rect|andv|Xscr|oror|ordm|ordf|xscr|ange|aopf|Aopf|rHar|Xopf|opar|Oopf|xopf|xnis|rhov|oopf|omid|xmap|oint|apid|apos|ogon|ascr|Ascr|odot|odiv|xcup|xcap|ocir|oast|nvlt|nvle|nvgt|nvge|nvap|Wscr|wscr|auml|ntlg|ntgl|nsup|nsub|nsim|Nscr|nscr|nsce|Wopf|ring|npre|wopf|npar|Auml|Barv|bbrk|Nopf|nopf|nmid|nLtv|beta|ropf|Ropf|Beta|beth|nles|rpar|nleq|bnot|bNot|nldr|NJcy|rscr|Rscr|Vscr|vscr|rsqb|njcy|bopf|nisd|Bopf|rtri|Vopf|nGtv|ngtr|vopf|boxh|boxH|boxv|nges|ngeq|boxV|bscr|scap|Bscr|bsim|Vert|vert|bsol|bull|bump|caps|cdot|ncup|scnE|ncap|nbsp|napE|Cdot|cent|sdot|Vbar|nang|vBar|chcy|Mscr|mscr|sect|semi|CHcy|Mopf|mopf|sext|circ|cire|mldr|mlcp|cirE|comp|shcy|SHcy|vArr|varr|cong|copf|Copf|copy|COPY|malt|male|macr|lvnE|cscr|ltri|sime|ltcc|simg|Cscr|siml|csub|Uuml|lsqb|lsim|uuml|csup|Lscr|lscr|utri|smid|lpar|cups|smte|lozf|darr|Lopf|Uscr|solb|lopf|sopf|Sopf|lneq|uscr|spar|dArr|lnap|Darr|dash|Sqrt|LJcy|ljcy|lHar|dHar|Upsi|upsi|diam|lesg|djcy|DJcy|leqq|dopf|Dopf|dscr|Dscr|dscy|ldsh|ldca|squf|DScy|sscr|Sscr|dsol|lcub|late|star|Star|Uopf|Larr|lArr|larr|uopf|dtri|dzcy|sube|subE|Lang|lang|Kscr|kscr|Kopf|kopf|KJcy|kjcy|KHcy|khcy|DZcy|ecir|edot|eDot|Jscr|jscr|succ|Jopf|jopf|Edot|uHar|emsp|ensp|Iuml|iuml|eopf|isin|Iscr|iscr|Eopf|epar|sung|epsi|escr|sup1|sup2|sup3|Iota|iota|supe|supE|Iopf|iopf|IOcy|iocy|Escr|esim|Esim|imof|Uarr|QUOT|uArr|uarr|euml|IEcy|iecy|Idot|Euml|euro|excl|Hscr|hscr|Hopf|hopf|TScy|tscy|Tscr|hbar|tscr|flat|tbrk|fnof|hArr|harr|half|fopf|Fopf|tdot|gvnE|fork|trie|gtcc|fscr|Fscr|gdot|gsim|Gscr|gscr|Gopf|gopf|gneq|Gdot|tosa|gnap|Topf|topf|geqq|toea|GJcy|gjcy|tint|gesl|mid|Sfr|ggg|top|ges|gla|glE|glj|geq|gne|gEl|gel|gnE|Gcy|gcy|gap|Tfr|tfr|Tcy|tcy|Hat|Tau|Ffr|tau|Tab|hfr|Hfr|ffr|Fcy|fcy|icy|Icy|iff|ETH|eth|ifr|Ifr|Eta|eta|int|Int|Sup|sup|ucy|Ucy|Sum|sum|jcy|ENG|ufr|Ufr|eng|Jcy|jfr|els|ell|egs|Efr|efr|Jfr|uml|kcy|Kcy|Ecy|ecy|kfr|Kfr|lap|Sub|sub|lat|lcy|Lcy|leg|Dot|dot|lEg|leq|les|squ|div|die|lfr|Lfr|lgE|Dfr|dfr|Del|deg|Dcy|dcy|lne|lnE|sol|loz|smt|Cup|lrm|cup|lsh|Lsh|sim|shy|map|Map|mcy|Mcy|mfr|Mfr|mho|gfr|Gfr|sfr|cir|Chi|chi|nap|Cfr|vcy|Vcy|cfr|Scy|scy|ncy|Ncy|vee|Vee|Cap|cap|nfr|scE|sce|Nfr|nge|ngE|nGg|vfr|Vfr|ngt|bot|nGt|nis|niv|Rsh|rsh|nle|nlE|bne|Bfr|bfr|nLl|nlt|nLt|Bcy|bcy|not|Not|rlm|wfr|Wfr|npr|nsc|num|ocy|ast|Ocy|ofr|xfr|Xfr|Ofr|ogt|ohm|apE|olt|Rho|ape|rho|Rfr|rfr|ord|REG|ang|reg|orv|And|and|AMP|Rcy|amp|Afr|ycy|Ycy|yen|yfr|Yfr|rcy|par|pcy|Pcy|pfr|Pfr|phi|Phi|afr|Acy|acy|zcy|Zcy|piv|acE|acd|zfr|Zfr|pre|prE|psi|Psi|qfr|Qfr|zwj|Or|ge|Gg|gt|gg|el|oS|lt|Lt|LT|Re|lg|gl|eg|ne|Im|it|le|DD|wp|wr|nu|Nu|dd|lE|Sc|sc|pi|Pi|ee|af|ll|Ll|rx|gE|xi|pm|Xi|ic|pr|Pr|in|ni|mp|mu|ac|Mu|or|ap|Gt|GT|ii);|&(Aacute|Agrave|Atilde|Ccedil|Eacute|Egrave|Iacute|Igrave|Ntilde|Oacute|Ograve|Oslash|Otilde|Uacute|Ugrave|Yacute|aacute|agrave|atilde|brvbar|ccedil|curren|divide|eacute|egrave|frac12|frac14|frac34|iacute|igrave|iquest|middot|ntilde|oacute|ograve|oslash|otilde|plusmn|uacute|ugrave|yacute|AElig|Acirc|Aring|Ecirc|Icirc|Ocirc|THORN|Ucirc|acirc|acute|aelig|aring|cedil|ecirc|icirc|iexcl|laquo|micro|ocirc|pound|raquo|szlig|thorn|times|ucirc|Auml|COPY|Euml|Iuml|Ouml|QUOT|Uuml|auml|cent|copy|euml|iuml|macr|nbsp|ordf|ordm|ouml|para|quot|sect|sup1|sup2|sup3|uuml|yuml|AMP|ETH|REG|amp|deg|eth|not|reg|shy|uml|yen|GT|LT|gt|lt)(?!;)([=a-zA-Z0-9]?)|&#([0-9]+)(;?)|&#[xX]([a-fA-F0-9]+)(;?)|&([0-9a-zA-Z]+)/g,ie={aacute:"á",Aacute:"Á",abreve:"ă",Abreve:"Ă",ac:"∾",acd:"∿",acE:"∾̳",acirc:"â",Acirc:"Â",acute:"´",acy:"а",Acy:"А",aelig:"æ",AElig:"Æ",af:"⁡",afr:"𝔞",Afr:"𝔄",agrave:"à",Agrave:"À",alefsym:"ℵ",aleph:"ℵ",alpha:"α",Alpha:"Α",amacr:"ā",Amacr:"Ā",amalg:"⨿",amp:"&",AMP:"&",and:"∧",And:"⩓",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",aogon:"ą",Aogon:"Ą",aopf:"𝕒",Aopf:"𝔸",ap:"≈",apacir:"⩯",ape:"≊",apE:"⩰",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",aring:"å",Aring:"Å",ascr:"𝒶",Ascr:"𝒜",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",atilde:"ã",Atilde:"Ã",auml:"ä",Auml:"Ä",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",barwed:"⌅",Barwed:"⌆",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",bcy:"б",Bcy:"Б",bdquo:"„",becaus:"∵",because:"∵",Because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",beta:"β",Beta:"Β",beth:"ℶ",between:"≬",bfr:"𝔟",Bfr:"𝔅",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bNot:"⫭",bopf:"𝕓",Bopf:"𝔹",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxdl:"┐",boxdL:"╕",boxDl:"╖",boxDL:"╗",boxdr:"┌",boxdR:"╒",boxDr:"╓",boxDR:"╔",boxh:"─",boxH:"═",boxhd:"┬",boxhD:"╥",boxHd:"╤",boxHD:"╦",boxhu:"┴",boxhU:"╨",boxHu:"╧",boxHU:"╩",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxul:"┘",boxuL:"╛",boxUl:"╜",boxUL:"╝",boxur:"└",boxuR:"╘",boxUr:"╙",boxUR:"╚",boxv:"│",boxV:"║",boxvh:"┼",boxvH:"╪",boxVh:"╫",boxVH:"╬",boxvl:"┤",boxvL:"╡",boxVl:"╢",boxVL:"╣",boxvr:"├",boxvR:"╞",boxVr:"╟",boxVR:"╠",bprime:"‵",breve:"˘",Breve:"˘",brvbar:"¦",bscr:"𝒷",Bscr:"ℬ",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpe:"≏",bumpE:"⪮",bumpeq:"≏",Bumpeq:"≎",cacute:"ć",Cacute:"Ć",cap:"∩",Cap:"⋒",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",ccaron:"č",Ccaron:"Č",ccedil:"ç",Ccedil:"Ç",ccirc:"ĉ",Ccirc:"Ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",Cdot:"Ċ",cedil:"¸",Cedilla:"¸",cemptyv:"⦲",cent:"¢",centerdot:"·",CenterDot:"·",cfr:"𝔠",Cfr:"ℭ",chcy:"ч",CHcy:"Ч",check:"✓",checkmark:"✓",chi:"χ",Chi:"Χ",cir:"○",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"®",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cire:"≗",cirE:"⧃",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",colon:":",Colon:"∷",colone:"≔",Colone:"⩴",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",conint:"∮",Conint:"∯",ContourIntegral:"∮",copf:"𝕔",Copf:"ℂ",coprod:"∐",Coproduct:"∐",copy:"©",COPY:"©",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",cross:"✗",Cross:"⨯",cscr:"𝒸",Cscr:"𝒞",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cup:"∪",Cup:"⋓",cupbrcap:"⩈",cupcap:"⩆",CupCap:"≍",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dagger:"†",Dagger:"‡",daleth:"ℸ",darr:"↓",dArr:"⇓",Darr:"↡",dash:"‐",dashv:"⊣",Dashv:"⫤",dbkarow:"⤏",dblac:"˝",dcaron:"ď",Dcaron:"Ď",dcy:"д",Dcy:"Д",dd:"ⅆ",DD:"ⅅ",ddagger:"‡",ddarr:"⇊",DDotrahd:"⤑",ddotseq:"⩷",deg:"°",Del:"∇",delta:"δ",Delta:"Δ",demptyv:"⦱",dfisht:"⥿",dfr:"𝔡",Dfr:"𝔇",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",diamond:"⋄",Diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",DJcy:"Ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",dopf:"𝕕",Dopf:"𝔻",dot:"˙",Dot:"¨",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",downarrow:"↓",Downarrow:"⇓",DownArrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",dscr:"𝒹",Dscr:"𝒟",dscy:"ѕ",DScy:"Ѕ",dsol:"⧶",dstrok:"đ",Dstrok:"Đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",dzcy:"џ",DZcy:"Џ",dzigrarr:"⟿",eacute:"é",Eacute:"É",easter:"⩮",ecaron:"ě",Ecaron:"Ě",ecir:"≖",ecirc:"ê",Ecirc:"Ê",ecolon:"≕",ecy:"э",Ecy:"Э",eDDot:"⩷",edot:"ė",eDot:"≑",Edot:"Ė",ee:"ⅇ",efDot:"≒",efr:"𝔢",Efr:"𝔈",eg:"⪚",egrave:"è",Egrave:"È",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",emacr:"ē",Emacr:"Ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp:" ",emsp13:" ",emsp14:" ",eng:"ŋ",ENG:"Ŋ",ensp:" ",eogon:"ę",Eogon:"Ę",eopf:"𝕖",Eopf:"𝔼",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",Epsilon:"Ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",escr:"ℯ",Escr:"ℰ",esdot:"≐",esim:"≂",Esim:"⩳",eta:"η",Eta:"Η",eth:"ð",ETH:"Ð",euml:"ë",Euml:"Ë",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",exponentiale:"ⅇ",ExponentialE:"ⅇ",fallingdotseq:"≒",fcy:"ф",Fcy:"Ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"𝔣",Ffr:"𝔉",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"𝕗",Fopf:"𝔽",forall:"∀",ForAll:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"𝒻",Fscr:"ℱ",gacute:"ǵ",gamma:"γ",Gamma:"Γ",gammad:"ϝ",Gammad:"Ϝ",gap:"⪆",gbreve:"ğ",Gbreve:"Ğ",Gcedil:"Ģ",gcirc:"ĝ",Gcirc:"Ĝ",gcy:"г",Gcy:"Г",gdot:"ġ",Gdot:"Ġ",ge:"≥",gE:"≧",gel:"⋛",gEl:"⪌",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"𝔤",Gfr:"𝔊",gg:"≫",Gg:"⋙",ggg:"⋙",gimel:"ℷ",gjcy:"ѓ",GJcy:"Ѓ",gl:"≷",gla:"⪥",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gnE:"≩",gneq:"⪈",gneqq:"≩",gnsim:"⋧",gopf:"𝕘",Gopf:"𝔾",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",gscr:"ℊ",Gscr:"𝒢",gsim:"≳",gsime:"⪎",gsiml:"⪐",gt:">",Gt:"≫",GT:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"½",hamilt:"ℋ",hardcy:"ъ",HARDcy:"Ъ",harr:"↔",hArr:"⇔",harrcir:"⥈",harrw:"↭",Hat:"^",hbar:"ℏ",hcirc:"ĥ",Hcirc:"Ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"𝔥",Hfr:"ℌ",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"𝕙",Hopf:"ℍ",horbar:"―",HorizontalLine:"─",hscr:"𝒽",Hscr:"ℋ",hslash:"ℏ",hstrok:"ħ",Hstrok:"Ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",iacute:"í",Iacute:"Í",ic:"⁣",icirc:"î",Icirc:"Î",icy:"и",Icy:"И",Idot:"İ",iecy:"е",IEcy:"Е",iexcl:"¡",iff:"⇔",ifr:"𝔦",Ifr:"ℑ",igrave:"ì",Igrave:"Ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",IJlig:"Ĳ",Im:"ℑ",imacr:"ī",Imacr:"Ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",Implies:"⇒",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",int:"∫",Int:"∬",intcal:"⊺",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",iocy:"ё",IOcy:"Ё",iogon:"į",Iogon:"Į",iopf:"𝕚",Iopf:"𝕀",iota:"ι",Iota:"Ι",iprod:"⨼",iquest:"¿",iscr:"𝒾",Iscr:"ℐ",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",itilde:"ĩ",Itilde:"Ĩ",iukcy:"і",Iukcy:"І",iuml:"ï",Iuml:"Ï",jcirc:"ĵ",Jcirc:"Ĵ",jcy:"й",Jcy:"Й",jfr:"𝔧",Jfr:"𝔍",jmath:"ȷ",jopf:"𝕛",Jopf:"𝕁",jscr:"𝒿",Jscr:"𝒥",jsercy:"ј",Jsercy:"Ј",jukcy:"є",Jukcy:"Є",kappa:"κ",Kappa:"Κ",kappav:"ϰ",kcedil:"ķ",Kcedil:"Ķ",kcy:"к",Kcy:"К",kfr:"𝔨",Kfr:"𝔎",kgreen:"ĸ",khcy:"х",KHcy:"Х",kjcy:"ќ",KJcy:"Ќ",kopf:"𝕜",Kopf:"𝕂",kscr:"𝓀",Kscr:"𝒦",lAarr:"⇚",lacute:"ĺ",Lacute:"Ĺ",laemptyv:"⦴",lagran:"ℒ",lambda:"λ",Lambda:"Λ",lang:"⟨",Lang:"⟪",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"«",larr:"←",lArr:"⇐",Larr:"↞",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",latail:"⤙",lAtail:"⤛",late:"⪭",lates:"⪭︀",lbarr:"⤌",lBarr:"⤎",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",Lcaron:"Ľ",lcedil:"ļ",Lcedil:"Ļ",lceil:"⌈",lcub:"{",lcy:"л",Lcy:"Л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",lE:"≦",LeftAngleBracket:"⟨",leftarrow:"←",Leftarrow:"⇐",LeftArrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",Leftrightarrow:"⇔",LeftRightArrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",leg:"⋚",lEg:"⪋",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",lfr:"𝔩",Lfr:"𝔏",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",ljcy:"љ",LJcy:"Љ",ll:"≪",Ll:"⋘",llarr:"⇇",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",lmidot:"ŀ",Lmidot:"Ŀ",lmoust:"⎰",lmoustache:"⎰",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lnE:"≨",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",Longleftarrow:"⟸",LongLeftArrow:"⟵",longleftrightarrow:"⟷",Longleftrightarrow:"⟺",LongLeftRightArrow:"⟷",longmapsto:"⟼",longrightarrow:"⟶",Longrightarrow:"⟹",LongRightArrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",lopf:"𝕝",Lopf:"𝕃",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"𝓁",Lscr:"ℒ",lsh:"↰",Lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",lstrok:"ł",Lstrok:"Ł",lt:"<",Lt:"≪",LT:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"¯",male:"♂",malt:"✠",maltese:"✠",map:"↦",Map:"⤅",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",mcy:"м",Mcy:"М",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",mfr:"𝔪",Mfr:"𝔐",mho:"℧",micro:"µ",mid:"∣",midast:"*",midcir:"⫰",middot:"·",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",mopf:"𝕞",Mopf:"𝕄",mp:"∓",mscr:"𝓂",Mscr:"ℳ",mstpos:"∾",mu:"μ",Mu:"Μ",multimap:"⊸",mumap:"⊸",nabla:"∇",nacute:"ń",Nacute:"Ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",ncaron:"ň",Ncaron:"Ň",ncedil:"ņ",Ncedil:"Ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",Ncy:"Н",ndash:"–",ne:"≠",nearhk:"⤤",nearr:"↗",neArr:"⇗",nearrow:"↗",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:`
`,nexist:"∄",nexists:"∄",nfr:"𝔫",Nfr:"𝔑",nge:"≱",ngE:"≧̸",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",ngt:"≯",nGt:"≫⃒",ngtr:"≯",nGtv:"≫̸",nharr:"↮",nhArr:"⇎",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",njcy:"њ",NJcy:"Њ",nlarr:"↚",nlArr:"⇍",nldr:"‥",nle:"≰",nlE:"≦̸",nleftarrow:"↚",nLeftarrow:"⇍",nleftrightarrow:"↮",nLeftrightarrow:"⇎",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nlt:"≮",nLt:"≪⃒",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:" ",nopf:"𝕟",Nopf:"ℕ",not:"¬",Not:"⫬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrarr:"↛",nrArr:"⇏",nrarrc:"⤳̸",nrarrw:"↝̸",nrightarrow:"↛",nRightarrow:"⇏",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",nscr:"𝓃",Nscr:"𝒩",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsube:"⊈",nsubE:"⫅̸",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupe:"⊉",nsupE:"⫆̸",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",ntilde:"ñ",Ntilde:"Ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nu:"ν",Nu:"Ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nvdash:"⊬",nvDash:"⊭",nVdash:"⊮",nVDash:"⊯",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwarr:"↖",nwArr:"⇖",nwarrow:"↖",nwnear:"⤧",oacute:"ó",Oacute:"Ó",oast:"⊛",ocir:"⊚",ocirc:"ô",Ocirc:"Ô",ocy:"о",Ocy:"О",odash:"⊝",odblac:"ő",Odblac:"Ő",odiv:"⨸",odot:"⊙",odsold:"⦼",oelig:"œ",OElig:"Œ",ofcir:"⦿",ofr:"𝔬",Ofr:"𝔒",ogon:"˛",ograve:"ò",Ograve:"Ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",omacr:"ō",Omacr:"Ō",omega:"ω",Omega:"Ω",omicron:"ο",Omicron:"Ο",omid:"⦶",ominus:"⊖",oopf:"𝕠",Oopf:"𝕆",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",or:"∨",Or:"⩔",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",oscr:"ℴ",Oscr:"𝒪",oslash:"ø",Oslash:"Ø",osol:"⊘",otilde:"õ",Otilde:"Õ",otimes:"⊗",Otimes:"⨷",otimesas:"⨶",ouml:"ö",Ouml:"Ö",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",par:"∥",para:"¶",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",pcy:"п",Pcy:"П",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",pfr:"𝔭",Pfr:"𝔓",phi:"φ",Phi:"Φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",pi:"π",Pi:"Π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",Poincareplane:"ℌ",pointint:"⨕",popf:"𝕡",Popf:"ℙ",pound:"£",pr:"≺",Pr:"⪻",prap:"⪷",prcue:"≼",pre:"⪯",prE:"⪳",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",prime:"′",Prime:"″",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportion:"∷",Proportional:"∝",propto:"∝",prsim:"≾",prurel:"⊰",pscr:"𝓅",Pscr:"𝒫",psi:"ψ",Psi:"Ψ",puncsp:" ",qfr:"𝔮",Qfr:"𝔔",qint:"⨌",qopf:"𝕢",Qopf:"ℚ",qprime:"⁗",qscr:"𝓆",Qscr:"𝒬",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',QUOT:'"',rAarr:"⇛",race:"∽̱",racute:"ŕ",Racute:"Ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",Rang:"⟫",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"»",rarr:"→",rArr:"⇒",Rarr:"↠",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",Rarrtl:"⤖",rarrw:"↝",ratail:"⤚",rAtail:"⤜",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rBarr:"⤏",RBarr:"⤐",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",Rcaron:"Ř",rcedil:"ŗ",Rcedil:"Ŗ",rceil:"⌉",rcub:"}",rcy:"р",Rcy:"Р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",Re:"ℜ",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",reg:"®",REG:"®",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",rfr:"𝔯",Rfr:"ℜ",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",rho:"ρ",Rho:"Ρ",rhov:"ϱ",RightAngleBracket:"⟩",rightarrow:"→",Rightarrow:"⇒",RightArrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"𝕣",Ropf:"ℝ",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",rscr:"𝓇",Rscr:"ℛ",rsh:"↱",Rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",sacute:"ś",Sacute:"Ś",sbquo:"‚",sc:"≻",Sc:"⪼",scap:"⪸",scaron:"š",Scaron:"Š",sccue:"≽",sce:"⪰",scE:"⪴",scedil:"ş",Scedil:"Ş",scirc:"ŝ",Scirc:"Ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",scy:"с",Scy:"С",sdot:"⋅",sdotb:"⊡",sdote:"⩦",searhk:"⤥",searr:"↘",seArr:"⇘",searrow:"↘",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",sfr:"𝔰",Sfr:"𝔖",sfrown:"⌢",sharp:"♯",shchcy:"щ",SHCHcy:"Щ",shcy:"ш",SHcy:"Ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"­",sigma:"σ",Sigma:"Σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",SOFTcy:"Ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"𝕤",Sopf:"𝕊",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",square:"□",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squf:"▪",srarr:"→",sscr:"𝓈",Sscr:"𝒮",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",star:"☆",Star:"⋆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",sub:"⊂",Sub:"⋐",subdot:"⪽",sube:"⊆",subE:"⫅",subedot:"⫃",submult:"⫁",subne:"⊊",subnE:"⫋",subplus:"⪿",subrarr:"⥹",subset:"⊂",Subset:"⋐",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",sum:"∑",Sum:"∑",sung:"♪",sup:"⊃",Sup:"⋑",sup1:"¹",sup2:"²",sup3:"³",supdot:"⪾",supdsub:"⫘",supe:"⊇",supE:"⫆",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supne:"⊋",supnE:"⫌",supplus:"⫀",supset:"⊃",Supset:"⋑",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swarr:"↙",swArr:"⇙",swarrow:"↙",swnwar:"⤪",szlig:"ß",Tab:"	",target:"⌖",tau:"τ",Tau:"Τ",tbrk:"⎴",tcaron:"ť",Tcaron:"Ť",tcedil:"ţ",Tcedil:"Ţ",tcy:"т",Tcy:"Т",tdot:"⃛",telrec:"⌕",tfr:"𝔱",Tfr:"𝔗",there4:"∴",therefore:"∴",Therefore:"∴",theta:"θ",Theta:"Θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",thinsp:" ",ThinSpace:" ",thkap:"≈",thksim:"∼",thorn:"þ",THORN:"Þ",tilde:"˜",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",times:"×",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",topf:"𝕥",Topf:"𝕋",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",TRADE:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"𝓉",Tscr:"𝒯",tscy:"ц",TScy:"Ц",tshcy:"ћ",TSHcy:"Ћ",tstrok:"ŧ",Tstrok:"Ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",uacute:"ú",Uacute:"Ú",uarr:"↑",uArr:"⇑",Uarr:"↟",Uarrocir:"⥉",ubrcy:"ў",Ubrcy:"Ў",ubreve:"ŭ",Ubreve:"Ŭ",ucirc:"û",Ucirc:"Û",ucy:"у",Ucy:"У",udarr:"⇅",udblac:"ű",Udblac:"Ű",udhar:"⥮",ufisht:"⥾",ufr:"𝔲",Ufr:"𝔘",ugrave:"ù",Ugrave:"Ù",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",Umacr:"Ū",uml:"¨",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",uogon:"ų",Uogon:"Ų",uopf:"𝕦",Uopf:"𝕌",uparrow:"↑",Uparrow:"⇑",UpArrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",updownarrow:"↕",Updownarrow:"⇕",UpDownArrow:"↕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",upsi:"υ",Upsi:"ϒ",upsih:"ϒ",upsilon:"υ",Upsilon:"Υ",UpTee:"⊥",UpTeeArrow:"↥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",Uring:"Ů",urtri:"◹",uscr:"𝓊",Uscr:"𝒰",utdot:"⋰",utilde:"ũ",Utilde:"Ũ",utri:"▵",utrif:"▴",uuarr:"⇈",uuml:"ü",Uuml:"Ü",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",vArr:"⇕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vBar:"⫨",Vbar:"⫫",vBarv:"⫩",vcy:"в",Vcy:"В",vdash:"⊢",vDash:"⊨",Vdash:"⊩",VDash:"⊫",Vdashl:"⫦",vee:"∨",Vee:"⋁",veebar:"⊻",veeeq:"≚",vellip:"⋮",verbar:"|",Verbar:"‖",vert:"|",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",vfr:"𝔳",Vfr:"𝔙",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",vopf:"𝕧",Vopf:"𝕍",vprop:"∝",vrtri:"⊳",vscr:"𝓋",Vscr:"𝒱",vsubne:"⊊︀",vsubnE:"⫋︀",vsupne:"⊋︀",vsupnE:"⫌︀",Vvdash:"⊪",vzigzag:"⦚",wcirc:"ŵ",Wcirc:"Ŵ",wedbar:"⩟",wedge:"∧",Wedge:"⋀",wedgeq:"≙",weierp:"℘",wfr:"𝔴",Wfr:"𝔚",wopf:"𝕨",Wopf:"𝕎",wp:"℘",wr:"≀",wreath:"≀",wscr:"𝓌",Wscr:"𝒲",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",xfr:"𝔵",Xfr:"𝔛",xharr:"⟷",xhArr:"⟺",xi:"ξ",Xi:"Ξ",xlarr:"⟵",xlArr:"⟸",xmap:"⟼",xnis:"⋻",xodot:"⨀",xopf:"𝕩",Xopf:"𝕏",xoplus:"⨁",xotime:"⨂",xrarr:"⟶",xrArr:"⟹",xscr:"𝓍",Xscr:"𝒳",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",yacute:"ý",Yacute:"Ý",yacy:"я",YAcy:"Я",ycirc:"ŷ",Ycirc:"Ŷ",ycy:"ы",Ycy:"Ы",yen:"¥",yfr:"𝔶",Yfr:"𝔜",yicy:"ї",YIcy:"Ї",yopf:"𝕪",Yopf:"𝕐",yscr:"𝓎",Yscr:"𝒴",yucy:"ю",YUcy:"Ю",yuml:"ÿ",Yuml:"Ÿ",zacute:"ź",Zacute:"Ź",zcaron:"ž",Zcaron:"Ž",zcy:"з",Zcy:"З",zdot:"ż",Zdot:"Ż",zeetrf:"ℨ",ZeroWidthSpace:"​",zeta:"ζ",Zeta:"Ζ",zfr:"𝔷",Zfr:"ℨ",zhcy:"ж",ZHcy:"Ж",zigrarr:"⇝",zopf:"𝕫",Zopf:"ℤ",zscr:"𝓏",Zscr:"𝒵",zwj:"‍",zwnj:"‌"},ge={aacute:"á",Aacute:"Á",acirc:"â",Acirc:"Â",acute:"´",aelig:"æ",AElig:"Æ",agrave:"à",Agrave:"À",amp:"&",AMP:"&",aring:"å",Aring:"Å",atilde:"ã",Atilde:"Ã",auml:"ä",Auml:"Ä",brvbar:"¦",ccedil:"ç",Ccedil:"Ç",cedil:"¸",cent:"¢",copy:"©",COPY:"©",curren:"¤",deg:"°",divide:"÷",eacute:"é",Eacute:"É",ecirc:"ê",Ecirc:"Ê",egrave:"è",Egrave:"È",eth:"ð",ETH:"Ð",euml:"ë",Euml:"Ë",frac12:"½",frac14:"¼",frac34:"¾",gt:">",GT:">",iacute:"í",Iacute:"Í",icirc:"î",Icirc:"Î",iexcl:"¡",igrave:"ì",Igrave:"Ì",iquest:"¿",iuml:"ï",Iuml:"Ï",laquo:"«",lt:"<",LT:"<",macr:"¯",micro:"µ",middot:"·",nbsp:" ",not:"¬",ntilde:"ñ",Ntilde:"Ñ",oacute:"ó",Oacute:"Ó",ocirc:"ô",Ocirc:"Ô",ograve:"ò",Ograve:"Ò",ordf:"ª",ordm:"º",oslash:"ø",Oslash:"Ø",otilde:"õ",Otilde:"Õ",ouml:"ö",Ouml:"Ö",para:"¶",plusmn:"±",pound:"£",quot:'"',QUOT:'"',raquo:"»",reg:"®",REG:"®",sect:"§",shy:"­",sup1:"¹",sup2:"²",sup3:"³",szlig:"ß",thorn:"þ",THORN:"Þ",times:"×",uacute:"ú",Uacute:"Ú",ucirc:"û",Ucirc:"Û",ugrave:"ù",Ugrave:"Ù",uml:"¨",uuml:"ü",Uuml:"Ü",yacute:"ý",Yacute:"Ý",yen:"¥",yuml:"ÿ"},j={0:"�",128:"€",130:"‚",131:"ƒ",132:"„",133:"…",134:"†",135:"‡",136:"ˆ",137:"‰",138:"Š",139:"‹",140:"Œ",142:"Ž",145:"‘",146:"’",147:"“",148:"”",149:"•",150:"–",151:"—",152:"˜",153:"™",154:"š",155:"›",156:"œ",158:"ž",159:"Ÿ"},ee=[1,2,3,4,5,6,7,8,11,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,64976,64977,64978,64979,64980,64981,64982,64983,64984,64985,64986,64987,64988,64989,64990,64991,64992,64993,64994,64995,64996,64997,64998,64999,65e3,65001,65002,65003,65004,65005,65006,65007,65534,65535,131070,131071,196606,196607,262142,262143,327678,327679,393214,393215,458750,458751,524286,524287,589822,589823,655358,655359,720894,720895,786430,786431,851966,851967,917502,917503,983038,983039,1048574,1048575,1114110,1114111],N=String.fromCharCode,S={},ce=S.hasOwnProperty,f=function(C,R){return ce.call(C,R)},d=function(C,R){for(var F=-1,te=C.length;++F<te;)if(C[F]==R)return!0;return!1},g=function(C,R){if(!C)return R;var F={},te;for(te in R)F[te]=f(C,te)?C[te]:R[te];return F},y=function(C,R){var F="";return C>=55296&&C<=57343||C>1114111?(R&&L("character reference outside the permissible Unicode range"),"�"):f(j,C)?(R&&L("disallowed character reference"),j[C]):(R&&d(ee,C)&&L("disallowed character reference"),C>65535&&(C-=65536,F+=N(C>>>10&1023|55296),C=56320|C&1023),F+=N(C),F)},b=function(C){return"&#x"+C.toString(16).toUpperCase()+";"},B=function(C){return"&#"+C+";"},L=function(C){throw Error("Parse error: "+C)},X=function(C,R){R=g(R,X.options);var F=R.strict;F&&V.test(C)&&L("forbidden code point");var te=R.encodeEverything,z=R.useNamedReferences,xe=R.allowUnsafeSymbols,H=R.decimal?B:b,pe=function(K){return H(K.charCodeAt(0))};return te?(C=C.replace(c,function(K){return z&&f(T,K)?"&"+T[K]+";":pe(K)}),z&&(C=C.replace(/&gt;\u20D2/g,"&nvgt;").replace(/&lt;\u20D2/g,"&nvlt;").replace(/&#x66;&#x6A;/g,"&fjlig;")),z&&(C=C.replace(x,function(K){return"&"+T[K]+";"}))):z?(xe||(C=C.replace(I,function(K){return"&"+T[K]+";"})),C=C.replace(/&gt;\u20D2/g,"&nvgt;").replace(/&lt;\u20D2/g,"&nvlt;"),C=C.replace(x,function(K){return"&"+T[K]+";"})):xe||(C=C.replace(I,pe)),C.replace(a,function(K){var qe=K.charCodeAt(0),Le=K.charCodeAt(1),We=(qe-55296)*1024+Le-56320+65536;return H(We)}).replace(A,pe)};X.options={allowUnsafeSymbols:!1,encodeEverything:!1,strict:!1,useNamedReferences:!1,decimal:!1};var ne=function(C,R){R=g(R,ne.options);var F=R.strict;return F&&v.test(C)&&L("malformed character reference"),C.replace(le,function(te,z,xe,H,pe,K,qe,Le,We){var Re,fe,_e,ye,Se,Be;return z?(Se=z,ie[Se]):xe?(Se=xe,Be=H,Be&&R.isAttributeValue?(F&&Be=="="&&L("`&` did not start a character reference"),te):(F&&L("named character reference was not terminated by a semicolon"),ge[Se]+(Be||""))):pe?(_e=pe,fe=K,F&&!fe&&L("character reference was not terminated by a semicolon"),Re=parseInt(_e,10),y(Re,F)):qe?(ye=qe,fe=Le,F&&!fe&&L("character reference was not terminated by a semicolon"),Re=parseInt(ye,16),y(Re,F)):(F&&L("named character reference was not terminated by a semicolon"),te)})};ne.options={isAttributeValue:!1,strict:!1};var de=function(C){return C.replace(I,function(R){return W[R]})},G={version:"1.2.0",encode:X,decode:ne,escape:de,unescape:ne};if(s&&!s.nodeType)if(o)o.exports=G;else for(var Ae in G)f(G,Ae)&&(s[Ae]=G[Ae]);else r.he=G})(Rt)})(Et,Et.exports);var zr=Et.exports;function Ts(e){for(;e!==zr.decode(e);)e=zr.decode(e);return e}const er=e=>ns(e)?(e=Ts(e),Bs.sanitize(e,{ALLOWED_TAGS:[],ALLOWED_ATTR:[],ALLOWED_URI_REGEX:[]})):"",Nu=e=>ns(e)?Bs.sanitize(Ts(e)):"",gi=e=>{if(!e)return"#";if(e.startsWith("#")||e.startsWith("/"))return er(e);try{const t=new URL(e);return t.protocol&&!["http:","https:","mailto:","tel:"].includes(t.protocol)?"#":er(t.toString())}catch{return"#"}},l="all-in-one-seo-pack";class Uu{constructor(){k(this,"personalize",!1);k(this,"head",(t,r)=>this[t+"Head"]!==void 0?this[t+"Head"](r):"");k(this,"body",(t,r)=>this[t+"Body"]!==void 0?this[t+"Body"](r):"");k(this,"titleHead",t=>{switch((t==null?void 0:t.error)||""){case"title-missing":return n("We couldn't find an SEO Title.",l);case"title-too-short":return this.personalize?U(n("Your SEO title is only %1$d characters long, which is too short.",l),t.value.length):U(n("The SEO title is only %1$d characters long, which is too short.",l),t.value.length);case"title-too-long":return this.personalize?U(n("Your SEO title is %1$d characters long, which is too long.",l),t.value.length):U(n("The SEO title is %1$d characters long, which is too long.",l),t.value.length);default:return this.personalize?U(n("Your SEO title is set and is %1$d characters long.",l),t.value.length):U(n("The SEO title is set and is %1$d characters long.",l),t.value.length)}});k(this,"titleBody",t=>{const r=P();return{code:t.value,message:n("Ensure your page's title includes your target keywords, and design it to encourage users to click.",l)+"<br><br>"+n("Writing compelling titles is both a science and an art. There are automated tools that can analyze your title against known metrics for readability and click-worthiness. You also need to understand the psychology of your target audience.",l),buttonText:n("Edit Your Page Title",l),buttonLink:r.aioseo.data.staticHomePage?`${r.aioseo.urls.staticHomePage}&aioseo-scroll=aioseo-post-settings-post-title-row&aioseo-highlight=aioseo-post-settings-post-title-row`:`${r.aioseo.urls.aio.searchAppearance}&aioseo-scroll=aioseo-home-page-site-title&aioseo-highlight=aioseo-home-page-site-title`}});k(this,"descriptionHead",t=>{switch((t==null?void 0:t.error)||""){case"description-missing":return this.personalize?n("No meta description was found for your page.",l):n("No meta description was found for the page.",l);case"description-too-short":return this.personalize?U(n("Your meta description is only %1$d characters long, which is too short.",l),t.value.length):U(n("The meta description is only %1$d characters long, which is too short.",l),t.value.length);case"description-too-long":return this.personalize?U(n("Your meta description is %1$d characters long, which is too long.",l),t.value.length):U(n("The meta description is %1$d characters long, which is too long.",l),t.value.length);default:return this.personalize?U(n("Your meta description is set and is %1$d characters long.",l),t.value.length):U(n("The meta description is set and is %1$d characters long.",l),t.value.length)}});k(this,"descriptionBody",t=>{const r=P();return{code:t.error==="description-missing"?null:t.value,message:n("Write a meta description for your page. Use your target keywords (in a natural way) and write with human readers in mind. Summarize the content - describe the topics your article discusses.",l)+"<br><br>"+n("The description should stimulate reader interest and get them to click on the article. Think of it as a mini-advertisement for your content.",l),buttonText:n("Edit Your Meta Description",l),buttonLink:r.aioseo.data.staticHomePage?`${r.aioseo.urls.staticHomePage}&aioseo-scroll=aioseo-post-settings-meta-description-row&aioseo-highlight=aioseo-post-settings-meta-description-row`:`${r.aioseo.urls.aio.searchAppearance}&aioseo-scroll=aioseo-home-page-meta-description&aioseo-highlight=aioseo-home-page-meta-description`}});k(this,"h1TagsHead",t=>{switch((t==null?void 0:t.error)||""){case"h1-missing":return n("No H1 tag was found.",l)+" "+n("For the best user experience there should be exactly one H1 tag on each page.",l);case"h1-too-many":return U(n("%1$d H1 tags were found.",l),t.value.length)+" "+n("For the best user experience there should be exactly one H1 tag on each page.",l);default:return this.personalize?n("One H1 tag was found on your page.",l):n("One H1 tag was found on the page.",l)}});k(this,"h1TagsBody",t=>{const r=P();return{code:t.error==="h1-missing"?null:t.value.join("<br>"),message:n("WordPress sites usually insert the page or post title as an H1 tag (although custom themes can change this behavior).",l)+"<br><br>"+n("Ensure your most important keywords appear in the H1 tag - don't force it, use them in a natural way that makes sense to human readers.",l)+"<br><br>"+n("Because your headline plays a large role in reader engagement, it's worth spending extra time perfecting it. Many top copywriters spend hours getting their headlines just right - sometimes they spend longer on the headline than the rest of the article!",l)+"<br><br>"+n("A good headline stimulates reader interest and offers a compelling reason to read your content. It promises a believable benefit.",l)+"<br><br>"+n("You should write as if your readers are selfish people with short attention spans (because that describes a large percentage of the world's population). Readers visit websites for selfish reasons - they're not there to make you happy.",l),buttonText:n("Edit Your Page",l),buttonLink:r.aioseo.data.staticHomePage?r.aioseo.urls.staticHomePage:null}});k(this,"h2TagsHead",t=>t.error==="h2-missing"?this.personalize?n("No H2 tags were found on your page.",l):n("No H2 tags were found on the page.",l):this.personalize?n("H2 tags were found on your page.",l)+" ("+t.value.length+")":n("H2 tags were found on the page.",l)+" ("+t.value.length+")");k(this,"h2TagsBody",t=>{const r=P();return{code:t.error==="h2-missing"?null:t.value.join("<br>"),message:n("Make sure you have a good balance of H2 tags to plain text in your content. Break the content down into logical sections, and use headings to introduce each new topic.",l)+"<br><br>"+n("Also, try to include synonyms and relevant terminology in H2 tag text. Search engines are pretty smart - they know which words usually occur together in each niche.",l)+"<br><br>"+n("It should be easy to include your main and supporting keywords in the H2 tags - after all, these keywords describe your content! If it's hard to work the keywords into your subheadings, it could be a sign that the keywords aren't closely related to your content.",l)+"<br><br>"+n("Don't try to force keywords into sub-headings if they feel unnatural. It will send the wrong message to your readers, possibly driving them away.",l),buttonText:n("Edit Your Page",l),buttonLink:r.aioseo.data.staticHomePage?r.aioseo.urls.staticHomePage:null}});k(this,"noImgAltAttsHead",t=>t.error==="image-missing-alt"?this.personalize?n("Some images on your page have no alt attribute.",l)+" ("+t.value.length+")":n("Some images on the page have no alt attribute.",l)+" ("+t.value.length+")":this.personalize?n("All images on your page have alt attributes.",l):n("All images on the page have alt attributes.",l));k(this,"noImgAltAttsBody",t=>{const r=P();return{codeAlt:t.error!=="image-missing-alt"?null:t.value.map(o=>Nu(o)).join(`
`),message:n("Make sure every image has an alt tag, and add useful descriptions to each image. Add your keywords or synonyms - but do it in a natural way.",l),buttonText:n("Edit Your Page",l),buttonLink:r.aioseo.data.staticHomePage?r.aioseo.urls.staticHomePage:null}});k(this,"linksRatioHead",t=>{switch((t==null?void 0:t.error)||""){case"internal-links-missing":return this.personalize?n("No internal links were found on your page.",l):n("No internal links were found on the page.",l);case"internal-links-too-few":return this.personalize?n("Too few internal links on your page.",l):n("Too few internal links on the page.",l);case"invalid-ratio":return n("The ratio of internal links to external links is uneven.",l);default:return this.personalize?n("Your page has a correct number of internal and external links.",l):n("The page has a correct number of internal and external links.",l)}});k(this,"linksRatioBody",t=>{const r=P();return{code:n("Internal:",l)+" "+t.value.internal+"<br>"+n("External:",l)+" "+t.value.external,message:n(`Add links to internal and external resources that are useful for your readers. For Internal links, make sure the links are highly relevant to the subject you're writing about. For external links, make sure you link to high-quality sites - Google penalizes pages that link to "spammy" sites (ones that break the Google webmaster guidelines).`,l)+"<br><br>"+n("It's impossible to cover every aspect of a subject on a single page, but your readers may be fascinated by some detail you barely touch on. If you link to a resource where they can learn more, they'll be grateful. What's more, you'll be rewarded with higher rankings!",l),buttonText:n("Edit Your Page",l),buttonLink:r.aioseo.data.staticHomePage?r.aioseo.urls.staticHomePage:null}});k(this,"canonicalTagHead",t=>t.error==="canonical-missing"?this.personalize?n("No canonical link tag found on your page.",l):n("No canonical link tag found on the page.",l):this.personalize?n("Your page is using the canonical link tag.",l):n("The page is using the canonical link tag.",l));k(this,"canonicalTagBody",t=>{const r=P();return{code:t.value,message:n(`Every page on your site should have a <link> tag with a 'rel="canonical"' attribute. The link tag should go inside the page's head tag, and it should contain the page's "correct" URL.`,l)+"<br><br>"+n(`If you've republished an article from another source (such as another site or a different section of your own site) then you need to pick which URL is the "correct" one and use that!`,l),buttonText:n("Edit Your Page",l),buttonLink:r.aioseo.data.staticHomePage?`${r.aioseo.urls.staticHomePage}&aioseo-tab=advanced&aioseo-scroll=aioseo-post-canonical-url&aioseo-highlight=aioseo-post-canonical-url`:null}});k(this,"noindexHead",t=>t.error==="noindex"?this.personalize?n("Your page contains a noindex header or meta tag.",l):n("The page contains a noindex header or meta tag.",l):this.personalize?n("Your page does not contain any noindex header or meta tag.",l):n("The page does not contain any noindex header or meta tag.",l));k(this,"noindexBody",()=>{const t=P();return{message:n("Only ever use noindex meta tag or header on pages you want to keep out of the reach of search engines!",l),buttonText:n("Edit Your Page",l),buttonLink:t.aioseo.data.staticHomePage?`${t.aioseo.urls.staticHomePage}&aioseo-tab=advanced&aioseo-scroll=aioseo-post-robots-setting&aioseo-highlight=aioseo-post-robots-setting`:null}});k(this,"wwwCanonicalizationHead",t=>t.error==="www-canonicalization"?this.personalize?n("The www and non-www versions of your URL are not redirected to the same site.",l):n("The www and non-www versions of the URL are not redirected to the same site.",l):this.personalize?n("Both the www and non-www versions of your URL are redirected to the same site.",l):n("Both the www and non-www versions of the URL are redirected to the same site.",l));k(this,"wwwCanonicalizationBody",()=>{const t=P();return{message:n(`Decide whether you want your site's URLs to include a "www", or if you prefer a plain domain name. There are marketing pros and cons for each choice, but neither one is better or worse for SEO purposes - as long as you're consistent.`,l)+"<br><br>"+n('You should use HTTP redirections (301 permanant redirects) to pass PageRank from the "wrong" URLs to the standard (canonical) ones. That way, your content will still benefit from backlinks if someone makes a mistake and uses the wrong URL.',l),buttonText:n("Edit Your Page",l),buttonLink:t.aioseo.data.staticHomePage?t.aioseo.urls.staticHomePage:null}});k(this,"robotsRulesHead",t=>t.error==="no-robots"?this.personalize?n("Your robots.txt file is missing or unavailable.",l):n("The robots.txt file is missing or unavailable.",l):t.value.match(/disallow:/i)?this.personalize?n('Your site has a robots.txt file which includes one or more "disallow" directives.',l):n('The site has a robots.txt file which includes one or more "disallow" directives.',l):this.personalize?n("Your site has a robots.txt file.",l):n("The site has a robots.txt file.",l));k(this,"robotsRulesBody",t=>{const r=P();return{code:t.error==="no-robots"?null:t.value,message:n("Make sure that you only block parts you don't want to be indexed.",l)+"<br><br>"+n("You can manually create a robots.txt file and upload it to your site's web root. A simpler option is to use a plugin for your CMS platform.",l)+"<br><br>"+U(n("%1$s has a full suite of tools to manage the robots.txt file, along with other related technologies, like XML Sitemaps.",l),"AIOSEO"),buttonText:n("Edit Your Page",l),buttonLink:r.aioseo.data.staticHomePage?r.aioseo.urls.staticHomePage:null}});k(this,"openGraphHead",t=>t.error==="ogp-missing"?n("Some Open Graph meta tags are missing.",l):t.error==="ogp-duplicates"?n("Duplicate Open Graph meta tags were found.",l):n("All the required Open Graph meta tags have been found.",l));k(this,"openGraphBody",t=>({code:t.value?t.value.join("<br>"):null,message:n("Duplicate Open Graph meta tags were found. This means that another plugin on your site or your current theme is generating social meta tags for Facebook (Open Graph) that are also generated by AIOSEO, and there are two sets of Open Graph tags present on your website. You'll want to deactivate this setting in your theme or the plugin that's causing this to avoid issues when sharing content on Facebook, LinkedIn, WhatsApp, or Instagram.",l)}));k(this,"schemaHead",t=>t.error==="schema-missing"?this.personalize?n("No Schema.org data was found on your page.",l):n("No Schema.org data was found on the page.",l):this.personalize?n("We found Schema.org data on your page.",l):n("We found Schema.org data on the page.",l));k(this,"schemaBody",()=>{const t=P();return{message:U(n("%1$s makes it extremely easy to add highly relevant Schema.org markup to your site. It has a simple graphical interface, so you don't have to get your hands dirty with complex HTML markup.",l),"AIOSEO"),buttonText:n("Edit Your Page",l),buttonLink:t.aioseo.data.staticHomePage?`${t.aioseo.urls.staticHomePage}&aioseo-tab=schema&aioseo-scroll=aioseo-post-schema&aioseo-highlight=aioseo-post-schema`:null}});k(this,"hasImgExpiresHead",t=>t.error==="image-expires-missing"?this.personalize?n('Your server is not using "expires" headers for your images.',l):n('The server is not using "expires" headers for the images.',l):this.personalize?n('Your server is using "expires" headers for your images.',l):n('The server is using "expires" headers for the images.',l));k(this,"hasImgExpiresBody",()=>({message:n('If you use the Apache or NGINX web servers, you can edit the configuration files to set the "expires" header for all image files. For Apache, you can also use a ".htaccess" file to change the settings for each folder.',l)+"<br><br>"+n("Alternatively, you can use a CMS plugin to simplify the process - it's a more user-friendly option. WordPress has a host of caching plugins, and most of them give you options to control the caching headers.",l)}));k(this,"unminifiedJsHead",t=>t.error==="js-unminified"?n("Some Javascript files don't seem to be minified.",l):n("All Javascript files appear to be minified.",l));k(this,"unminifiedJsBody",t=>({codeAlt:t.error!=="js-unminified"?null:t.value.join(`
`),message:n("JavaScript files appear in many places, including frameworks (like Bootstrap), themes and templates, and third-party plugins.",l)+"<br><br>"+n("We recommend tracking down where the un-minified JavaScript files come from",l)+"<br><br>"+n("There are server-side tools (including WordPress plugins) to automatically minify JavaScript files.",l)}));k(this,"unminifiedCssHead",t=>t.error==="css-unminified"?n("Some CSS files don't seem to be minified.",l):n("All CSS files appear to be minified.",l));k(this,"unminifiedCssBody",t=>({codeAlt:t.error!=="css-unminified"?null:t.value.join(`
`),message:n("CSS files appear in many places, including frameworks (like Bootstrap), themes and templates, and third-party plugins.",l)+"<br><br>"+n("We recommend tracking down where the un-minified CSS files come from.",l)+"<br><br>"+n("There are server-side tools (including WordPress plugins) to automatically minify CSS files.",l)}));k(this,"pageObjectsHead",t=>{const r=this.personalize?U(n("Your page makes %1$d requests.",l),t.total):U(n("The page makes %1$d requests.",l),t.total);return t.error==="page-objects-too-many"?r+" "+n("More than 20 requests can result in slow page loading.",l):r});k(this,"pageObjectsBody",t=>({code:t.error!=="page-objects-too-many"?null:n("Images:",l)+" "+t.value.images+"<br>"+n("JavaScript:",l)+" "+t.value.js+"<br>"+n("CSS:",l)+" "+t.value.css,message:n("Try to replace embedded objects with HTML5 alternatives.",l)}));k(this,"pageSizeHead",t=>{let r=Math.round(t.value/1e3),s="";return t.error==="page-size-too-big"&&(r=Math.ceil(t.value/1e3),s=n("This is over our recommendation of 50 KB.",l)),33>Math.round(t.value/1e3)&&(s=n("This is under the average of 33 KB.",l)),U(n("The size of the HTML document is %1$d KB.",l),r)+" "+s});k(this,"pageSizeBody",()=>({message:n("In order to reduce page size, remove any unnecessary tags from your markup. This includes developer comments, which are invisible to your users - search engines ignore the text in comments, too.",l)+"<br><br>"+n("Sometimes inline CSS is a culprit. A little inline CSS can help your page render faster. Too much will bloat the HTML file and increase the page loading time.",l)+"<br><br>"+n(`You can reduce CSS repetition with HTML class and ID attributes. Often the same rules will be repeated across many page elements, embedded in each tag's "style" attribute. You can extract them into a single "style" tag and use classes and ID's to target each element.`,l)+"<br><br>"+n("Removing white space can also have an impact on your HTML page's size. White space characters like carriage returns and tabs are ignored by the browser, but they make the markup easier for developers to read. So you should always strip them from your templates or themes before you use them in a production environment.",l)}));k(this,"responseTimeHead",t=>t.error==="response-time-too-long"?this.personalize?U(n("The response time of your page is %1$f seconds. It is recommended to keep it equal to or below 0.2 seconds.",l),t.value):U(n("The response time of the page is %1$f seconds. It is recommended to keep it equal to or below 0.2 seconds.",l),t.value):this.personalize?n("Your response time is under 0.2 seconds.",l):n("The response time is under 0.2 seconds.",l));k(this,"responseTimeBody",()=>({message:n("If you want to continue to improve your response time, the simplest and fastest fix is to use a caching plugin. Caching plugins keep a cached version of each page on your site. Instead of building the page from scratch, the server will send the cached copy.",l)+"<br><br>"+n("You can get an even greater boost in speed with a content delivery network service. These services host a copy of your content on multiple servers spread out across the globe. A user's request is handled by the edge server that's closest to their physical location, so the content arrives incredibly fast.",l)}));k(this,"visiblePluginsHead",t=>t.error==="plugins-visible"?this.personalize?n("Plugins from your website are publicly visible.",l)+" ("+t.value.length+")":n("Plugins from the website are publicly visible.",l)+" ("+t.value.length+")":this.personalize?n("You have no visible plugins!",l):n("There are no visible plugins.",l));k(this,"visiblePluginsBody",t=>({code:t.error!=="plugins-visible"?null:t.value.join("<br>"),message:n("It's a great idea to try and hide the plugins you have visible. From time to time vulnerabilities are found in plugins and if your site is not updated in a timely fashion, outdated plugins and themes can be exploited.",l)}));k(this,"visibleThemesHead",t=>t.error==="themes-visible"?this.personalize?U(n("Anyone can see that you are using the %1$s theme.",l),t.value[0]):U(n("Anyone can see that they are using the %1$s theme.",l),t.value[0]):this.personalize?n("Your theme is not visible!",l):n("The theme is not visible.",l));k(this,"visibleThemesBody",()=>({message:n("It's a great idea to try and hide the theme you have visible. From time to time vulnerabilities are found in themes and if your site is not updated in a timely fashion, outdated plugins and themes can be exploited.",l)}));k(this,"directoryListingHead",t=>t.error==="directory-listing-open"?this.personalize?n("Directory Listing seems to be enabled on your server.",l):n("Directory Listing seems to be enabled on the server.",l):this.personalize?n("Directory Listing seems to be disabled on your server.",l):n("Directory Listing seems to be disabled on the server.",l));k(this,"directoryListingBody",()=>({message:n(`Fortunately, every popular web server has options to prevent directory listings. They'll show a "403 forbidden" message instead.`,l)+"<br><br>"+n("Alternatively, you can create an empty index.php file and save it in every directory on your site. That's an approach that WordPress uses and it works well.",l)}));k(this,"googleSafeBrowsingHead",t=>t.error==="google-safe-browsing"?this.personalize?n("It looks like your site has been added to one of Google's malwares lists.",l):n("It looks like this site has been added to one of Google's malwares lists.",l):this.personalize?n("Google has not flagged your site for malware!",l):n("Google has not flagged this site for malware.",l));k(this,"googleSafeBrowsingBody",()=>({message:n("Google Safe browsing shows warnings and alerts to users if they visit a suspicious website. If you are flagged by Google Safe Browsing, you should take immediate steps to fix that.",l)}));k(this,"secureConnectionHead",t=>t.error==="insecure-connection"?this.personalize?n("Your site is not using a secure transfer protocol (https).",l):n("The site is not using a secure transfer protocol (https).",l):this.personalize?n("Your site is using a secure transfer protocol (https).",l):n("The site is using a secure transfer protocol (https).",l));k(this,"secureConnectionBody",()=>({message:n("If you aren't using an SSL certificate for your site that means you are losing a lot of potential traffic. We recommend getting an SSL certificate installed immediately.",l)}))}}const Iu=new Uu,$r="all-in-one-seo-pack",$t=e=>(Object.keys(e).forEach(t=>{const r=e[t];Iu.head(t,r)||["searchPreview","mobileSearchPreview","mobileSnapshot"].includes(t)||delete e[t]}),e),Hu=M("AnalyzerStore",{state:()=>({analyzer:null,analyzing:!1,analyzeError:null,homeResults:{results:[],score:0},competitors:{}}),getters:{getHeadlineAnalysisResults:()=>ae().internalOptions.internal.headlineAnalysis.headlines||{},allItemsCount:e=>t=>e.recommendedCount(t)+e.criticalCount(t)+e.goodCount(t),recommendedCount:e=>t=>{var s;let r=0;return t=t||((s=e.homeResults)==null?void 0:s.results)||{},Object.keys(t).forEach(o=>{const u=$t(t[o]);Object.keys(u).forEach(a=>{u[a].status==="warning"&&r++})}),r},criticalCount:e=>t=>{var s;let r=0;return t=t||((s=e.homeResults)==null?void 0:s.results)||{},Object.keys(t).forEach(o=>{const u=$t(t[o]);Object.keys(u).forEach(a=>{u[a].status==="error"&&r++})}),r},goodCount:e=>t=>{var s;let r=0;return t=t||((s=e.homeResults)==null?void 0:s.results)||{},Object.keys(t).forEach(o=>{const u=$t(t[o]);Object.keys(u).forEach(a=>{u[a].status==="passed"&&r++})}),r}},actions:{getSiteAnalysisResults(){var e,t;return(t=(e=this.homeResults)==null?void 0:e.results)!=null&&t.length?this.homeResults:(this.analyzing=!0,h.get(p.restUrl("seo-analysis/homeresults")).then(r=>(this.homeResults=r.body.result,this.analyzing=!1,this.homeResults)))},getCompetitorSiteAnalysisResults(){var e;return(e=this.competitors)!=null&&e.length?this.competitors:h.get(p.restUrl("seo-analysis/competitors")).then(t=>(this.competitors=t.body.result,this.competitors))},runSiteAnalyzer(e={}){return this.analyzing=!0,this.analyzer="competitor-site",h.post(p.restUrl("analyze")).send({url:e.url,refresh:e.refresh}).then(t=>{if(e.url)return this.analyzing=!1,t;this.homeResults=t.body,this.analyzing=!1}).catch(t=>{var s;this.analyzing=!1;let r=n("We couldn't connect to the site, please try again later.",$r);(s=t.response.body.response)!=null&&s.error&&(r=t.response.body.response.error),this.analyzeError=r})},runHeadlineAnalyzer(e={}){return this.analyzer="headline",h.post(p.restUrl("analyze-headline")).send({headline:e.headline,shouldStoreHeadline:e.shouldStoreHeadline}).then(t=>{ae().updateOption("internalOptions",{groups:["internal","headlineAnalysis"],key:"headlines",value:t.body}),this.analyzing=!1}).catch(t=>{var s;this.analyzing=!1;let r=n("We couldn't analyze your title, please try again later.",$r);(s=t.response.body)!=null&&s.message&&(r=t.response.body.message),this.analyzeError=r})},deleteCompetitorSite(e){return h.post(p.restUrl("analyze/delete-site")).send({url:e}).then(()=>{delete this.competitors[e],this.analyzing=!1})},deleteHeadline(e){return h.post(p.restUrl("analyze-headline/delete")).send({headline:e}).then(t=>{ae().updateOption("internalOptions",{groups:["internal","siteAnalysis"],key:"headlines",value:t.body}),this.analyzing=!1})}}}),zu=M("BackupsStore",{state:()=>({backups:[],networkBackups:{}}),actions:{createBackup(e){return h.post(p.restUrl("backup")).send(e).then(t=>{if(e.siteId){this.networkBackups[e.siteId]=t.body.backups;return}this.backups=t.body.backups})},restoreBackup(e){return h.post(p.restUrl("backup/restore")).send(e).then(t=>{if(t.body.license&&!e.siteId){const s=Os();s.license=t.body.license,s.clearLicenseNotices()}const r=ae();if(r.options=t.body.options,r.internalOptions=t.body.internalOptions,e.siteId){this.networkBackups[e.siteId]=t.body.backups;return}this.backups=t.body.backups})},deleteBackup(e){return h.delete(p.restUrl("backup")).send(e).then(t=>{if(e.siteId){this.networkBackups[e.siteId]=t.body.backups;return}this.backups=t.body.backups})}}}),Mr=()=>{const e=(s,o)=>(s==="licenseKey"&&(o=""),s==="rules"&&Array.isArray(o)&&o.forEach((u,a)=>{const c=JSON.parse(u);c.userAgent===null&&c.rule==="allow"&&c.directoryPath===null&&o.splice(a,1)}),s==="separator"&&(o=er(o)),o===null?"":o);return{actions:{updateOriginalOptions(s,o){this[s]=JSON.parse(JSON.stringify(o))},disableDirtyCheck(s){this.disabled.push(s)}},normalize:s=>s?JSON.stringify(JSON.parse(JSON.stringify(s)),e):{}}},Ke=M("DirtyOptionsStore",{state:()=>({disabled:[],options:{},dynamicOptions:{},networkOptions:{},redirectOptions:{},indexNowOptions:{}}),getters:{isDirty:e=>{const t=Mr(),r=or(),s=ae(),o=ur(),u=[[e.options,s.options],[e.networkOptions,s.networkOptions],[e.dynamicOptions,s.dynamicOptions]];return e.disabled.includes("redirects")||u.push([e.redirectOptions,o.options]),e.disabled.includes("indexNow")||u.push([e.indexNowOptions,r.options]),!u.every(([a,c])=>t.normalize(a)===t.normalize(c))}},actions:{...Mr().actions}}),$u=M("HelpPanelStore",{state:()=>({categories:{},docs:{}})}),or=M("IndexNowStore",{state:()=>({options:{indexNow:{apiKey:null}}}),actions:{generateApiKey(){return h.get(p.restUrl("index-now/generate-api-key")).then(e=>(this.options.indexNow.apiKey=e.body.key,Ke().updateOriginalOptions("indexNowOptions",this.options),e.body.key))},getApiKey(){return h.get(p.restUrl("index-now/api-key")).then(e=>(this.options.indexNow.apiKey=e.body.key,Ke().updateOriginalOptions("indexNowOptions",this.options),e.body.key))}}}),Mu=M("IndexStatusStore",{state:()=>({objects:{paginated:{rows:[],totals:{page:1,pages:0,total:0},orderBy:"",orderDir:"",limit:"",offset:"",searchTerm:"",additionalFilters:{}},fetching:!1},overview:null,options:{}}),actions:{fetchIndexStatusObjects(e={}){this.objects.fetching=!0;const t=vt();return this.objects.paginated={...this.objects.paginated,...e},h.get(p.restUrl("search-statistics/index-status/objects")).query({orderBy:this.objects.paginated.orderBy,orderDir:this.objects.paginated.orderDir,limit:this.objects.paginated.limit,offset:this.objects.paginated.offset,searchTerm:this.objects.paginated.searchTerm,additionalFilters:this.objects.paginated.additionalFilters,endDate:t.latestAvailableDate}).then(r=>(this.objects.paginated.rows=r.body.paginated.rows,this.objects.paginated.totals=r.body.paginated.totals,r)).catch(r=>{throw r}).finally(()=>{this.objects.fetching=!1})},fetchIndexStatusOverview(){return h.get(p.restUrl("search-statistics/index-status/overview")).query().then(e=>(this.overview=e.body.data,e)).catch(e=>{throw e})}}}),Fs=M("KeywordRankTrackerStore",{state:()=>({parentActiveTab:"rank-tracker",groups:{selected:[],all:{rows:[]},paginated:{rows:[],totals:{page:1,pages:0,total:0},orderBy:"",orderDir:"",limit:"",offset:"",searchTerm:"",filter:"all",additionalFilters:{}},count:0,statistics:null,tableKeywords:{paginated:{rows:[],totals:{page:1,pages:0,total:0},orderBy:"",orderDir:"",limit:"",offset:"",searchTerm:"",filter:"all",additionalFilters:{}}}},keywords:{selected:[],all:{rows:[]},paginated:{rows:[],totals:{page:1,pages:0,total:0},orderBy:"",orderDir:"",limit:"",offset:"",searchTerm:"",filter:"all",additionalFilters:{}},count:0,statistics:null,related:{selected:[],paginated:{rows:[]}},rankingPages:{paginated:{rows:[],totals:{page:1,pages:0,total:0},limit:5,offset:0}}},siteFocusKeywords:[],gscKeywords:[],isFetchingGscKeywords:!1,options:{},modalOpenAddKeywords:!1,modalOpenCreateGroup:!1,modalOpenAssignGroups:!1,modalOpenDeleteKeywords:!1,modalOpenUpdateGroup:!1,modalOpenDeleteGroups:!1,modalOpenPostEdit:!1,isFetchingStatistics:{keywords:!1,groups:!1},fetchKeywordsCallback:null,keywordsLimit:0,errors:{crud:null},favoriteGroup:{}}),getters:{range(){return vt().range}},actions:{toggleModal(e){if(this.fetchKeywordsCallback=(e==null?void 0:e.fetchKeywordsCallback)||null,e.modal==="modalOpenAddKeywords"&&e.open&&(this.keywords.selected=[],this.keywords.related.selected=(e==null?void 0:e.relatedKeywords)??[]),e.modal==="modalOpenAssignGroups"&&e.open&&(this.keywords.selected=(e==null?void 0:e.keywords)??[]),e.modal==="modalOpenDeleteKeywords"&&e.open&&(this.keywords.selected=(e==null?void 0:e.keywords)??[]),e.modal==="modalOpenCreateGroup"&&(this.errors.crud=null),e.modal==="modalOpenUpdateGroup"&&e.open&&(this.errors.crud=null),e.modal==="modalOpenPostEdit"&&!e.open){const t=vt();t.shouldShowSampleReports=!1}this[e.modal]=e.open},insertKeywords({keywords:e,groups:t}){return this.errors.crud=null,h.post(p.restUrl("search-statistics/keyword-rank-tracker/keywords")).send({keywords:e,groups:t}).then(r=>r).catch(r=>{var s,o;throw this.errors.crud=((o=(s=r==null?void 0:r.response)==null?void 0:s.body)==null?void 0:o.error)||null,r})},abstractFetchKeywords(e={}){return this.fetchKeywordsCallback?this.fetchKeywordsCallback(e):this.fetchKeywords(e)},fetchKeywords(e={}){return this.keywords.paginated={...this.keywords.paginated,...e},h.get(p.restUrl("search-statistics/keyword-rank-tracker/keywords")).query({orderBy:this.keywords.paginated.orderBy,orderDir:this.keywords.paginated.orderDir,limit:this.keywords.paginated.limit,offset:this.keywords.paginated.offset,searchTerm:this.keywords.paginated.searchTerm,filter:this.keywords.paginated.filter,additionalFilters:this.keywords.paginated.additionalFilters,startDate:this.range.start,endDate:this.range.end,"names[]":(e==null?void 0:e.names)||[],"ids[]":(e==null?void 0:e.ids)||[],postId:(e==null?void 0:e.postId)||0}).then(t=>(this.keywords.paginated.rows=t.body.paginated.rows,this.keywords.paginated.totals=t.body.paginated.totals,this.keywords.paginated.orderBy=t.body.paginated.orderBy,this.keywords.paginated.orderDir=t.body.paginated.orderDir,this.keywords.all.rows=t.body.all.rows,this.keywords.count=t.body.count,t)).catch(t=>{throw t})},updateKeyword({id:e,payload:t}){return h.put(p.restUrl(`search-statistics/keyword-rank-tracker/keywords/${e}/`)).send(t).then(r=>r).catch(r=>{throw r})},deleteKeywords(e){return h.delete(p.restUrl("search-statistics/keyword-rank-tracker/keywords")).send({ids:e}).then(t=>([this.keywords.paginated,this.groups.tableKeywords.paginated].forEach(s=>{const o=Math.max(0,s.offset-t.body.rowsAffected);s.offset=s.totals.total-t.body.rowsAffected<=s.offset?Math.floor(o/s.limit)*s.limit:Math.ceil(o/s.limit)*s.limit}),t)).catch(t=>{throw t})},insertGroups({groups:e,keywords:t}){return this.errors.crud=null,h.post(p.restUrl("search-statistics/keyword-rank-tracker/groups")).send({groups:e,keywords:t}).then(r=>r).catch(r=>{var s,o;throw this.errors.crud=((o=(s=r==null?void 0:r.response)==null?void 0:s.body)==null?void 0:o.error)||null,r})},fetchGroups(e={}){return this.groups.paginated={...this.groups.paginated,...e},h.get(p.restUrl("search-statistics/keyword-rank-tracker/groups")).query({orderBy:this.groups.paginated.orderBy,orderDir:this.groups.paginated.orderDir,limit:this.groups.paginated.limit,offset:this.groups.paginated.offset,searchTerm:this.groups.paginated.searchTerm,filter:this.groups.paginated.filter,additionalFilters:this.groups.paginated.additionalFilters,startDate:this.range.start,endDate:this.range.end}).then(t=>(this.groups.paginated.rows=t.body.paginated.rows,this.groups.paginated.totals=t.body.paginated.totals,this.groups.all.rows=t.body.all.rows,this.groups.count=t.body.count,t)).catch(t=>{throw t})},fetchGroupsTableKeywords(e={}){var r;const t=_s();return this.groups.tableKeywords.paginated={...this.groups.tableKeywords.paginated,...e,limit:(r=t.settings.tablePagination)==null?void 0:r.searchStatisticsKrtGroupsTableKeywords},h.get(p.restUrl("search-statistics/keyword-rank-tracker/keywords")).query({orderBy:this.groups.tableKeywords.paginated.orderBy,orderDir:this.groups.tableKeywords.paginated.orderDir,limit:this.groups.tableKeywords.paginated.limit,offset:this.groups.tableKeywords.paginated.offset,searchTerm:this.groups.tableKeywords.paginated.searchTerm,filter:this.groups.tableKeywords.paginated.filter,additionalFilters:this.groups.tableKeywords.paginated.additionalFilters,startDate:this.range.start,endDate:this.range.end,"ids[]":e!=null&&e.ids?e.ids.length?e.ids:[0]:[]}).then(async s=>(this.groups.tableKeywords.paginated.rows=s.body.paginated.rows,this.groups.tableKeywords.paginated.totals=s.body.paginated.totals,e!=null&&e.updateKeywords&&(await this.fetchKeywords(),this.maybeFetchStatistics({context:"keywords"})),s)).catch(s=>{throw s})},updateGroup({id:e,payload:t}){return this.errors.crud=null,h.put(p.restUrl(`search-statistics/keyword-rank-tracker/groups/${e}/`)).send(t).then(r=>r).catch(r=>{var s,o;throw this.errors.crud=((o=(s=r==null?void 0:r.response)==null?void 0:s.body)==null?void 0:o.error)||null,r})},deleteGroups(e){return h.delete(p.restUrl("search-statistics/keyword-rank-tracker/groups")).send({ids:e}).then(t=>{const r=Math.max(0,this.groups.paginated.offset-t.body.rowsAffected);return this.groups.paginated.offset=this.groups.paginated.totals.total-t.body.rowsAffected<=this.groups.paginated.offset?Math.floor(r/this.groups.paginated.limit)*this.groups.paginated.limit:Math.ceil(r/this.groups.paginated.limit)*this.groups.paginated.limit,t}).catch(t=>{throw t})},updateRelationships({keywords:e,groups:t}){return h.post(p.restUrl("search-statistics/keyword-rank-tracker/relationships")).send({keywords:e,groups:t}).then(r=>r).catch(r=>{throw r})},maybeFetchStatistics({context:e,postId:t=0}){if(!(!this[e].paginated.rows.length&&!this[e].all.rows.length))return this.isFetchingStatistics[e]=!0,h.post(p.restUrl("search-statistics/keyword-rank-tracker/statistics")).send({context:e,startDate:this.range.start,endDate:this.range.end,paginated:this[e].paginated,all:this[e].all,postId:t||0}).then(r=>{this[e].paginated.rows=r.body.paginated.rows,this[e].all.rows=r.body.all.rows,this[e].statistics=r.body.statistics}).catch(r=>{throw r}).finally(()=>{this.isFetchingStatistics[e]=!1})},maybeUpdateKeywords(e={}){if(this.keywords.count)return this.fetchKeywords(e).then(()=>this.maybeFetchStatistics({context:"keywords",postId:e==null?void 0:e.postId}))},maybeUpdateGroups(){if(this.groups.count)return this.fetchGroups().then(()=>this.maybeFetchStatistics({context:"groups"}))},resetGroupsTableKeywords(){this.groups.tableKeywords={paginated:{rows:[],totals:{page:1,pages:0,total:0},orderBy:"",orderDir:"",limit:"",offset:"",searchTerm:"",filter:"all",additionalFilters:{}}}},resetRelatedKeywords(){this.keywords.related={selected:[],paginated:{rows:[]}}},resetKeywordsRankingPages(){this.keywords.rankingPages={paginated:{rows:[],totals:{page:1,pages:0,total:0},limit:5,offset:0}}},fetchGscKeywords(){return this.isFetchingGscKeywords=!0,h.get(p.restUrl("search-statistics/stats/keywords")).query({startDate:this.range.start,endDate:this.range.end,filter:"all",searchTerm:"",limit:50}).then(e=>{e.body.success&&(this.gscKeywords=e.body.data.paginated.rows||[])}).catch(e=>{throw e}).finally(()=>{this.isFetchingGscKeywords=!1})},fetchRelatedKeywords(e){return h.get(p.restUrl("search-statistics/keyword-rank-tracker/related-keywords")).query({keyword:e,startDate:this.range.start,endDate:this.range.end}).then(t=>(this.keywords.related.paginated.rows=t.body.paginated.rows,t)).catch(t=>{throw t})},fetchKeywordsRankingPages(e={}){this.keywords.rankingPages.paginated={...this.keywords.rankingPages.paginated,...e};const t=this.keywords.rankingPages.paginated.keywords;return h.post(p.restUrl("search-statistics/stats/keywords/posts")).send({limit:this.keywords.rankingPages.paginated.limit,offset:this.keywords.rankingPages.paginated.offset,startDate:this.range.start,endDate:this.range.end,keywords:t}).then(r=>{var s,o;if(r.body.success){const u=((o=(s=r.body)==null?void 0:s.data)==null?void 0:o[t[0]].paginated)||{};this.keywords.rankingPages.paginated.rows=Object.values((u==null?void 0:u.rows)||{}),this.keywords.rankingPages.paginated.totals=(u==null?void 0:u.totals)||{}}}).catch(r=>{throw r})},maybeFetchRelatedKeywordsStatistics(){if(!this.keywords.related.paginated.rows.every(e=>e.statistics!==null))return h.post(p.restUrl("search-statistics/keyword-rank-tracker/statistics")).send({context:"keywords",startDate:this.range.start,endDate:this.range.end,all:this.keywords.related.paginated}).then(e=>{this.keywords.related.paginated.rows=e.body.all.rows}).catch(e=>{throw e})}}}),Gu="all-in-one-seo-pack",Gr=()=>{const e=P(),t=document.createElement("a");t.href=e.aioseo.urls.aio.settings,t.classList.add("ab-item");const r=document.createElement("span");return r.innerText=n("Add License Key",Gu),r.classList.add("aioseo-menu-highlight"),r.classList.add("green"),t.appendChild(r),t},Os=M("LicenseStore",{state:()=>({license:{expires:0,isActive:!1,isDisabled:!1,isExpired:!1,isInvalid:!1,features:{}}}),getters:{isUnlicensed:e=>"Lite".toLowerCase()!=="pro"||!e.license.isActive,licenseKey:()=>{const e=P(),t=ae();return e.aioseo.data.isNetworkAdmin?t.networkOptions.general.licenseKey:t.options.general.licenseKey}},actions:{activate(e){const t=je(),r=P();return h.post(p.restUrl("activate")).send({licenseKey:e.trim(),network:r.aioseo.data.isNetworkAdmin}).then(s=>{const o=ae(),u=r.aioseo.data.isNetworkAdmin?"networkOptions":"options";return o.updateOption(u,{groups:["general"],key:"licenseKey",value:e}),t.updateNotifications(s.body.notifications),s.body.licenseData&&(Object.keys(s.body.licenseData).forEach(a=>{const c=r.aioseo.data.isNetworkAdmin?"internalNetworkOptions":"internalOptions";o.updateOption(c,{groups:["internal","license"],key:a,value:s.body.licenseData[a]})}),this.license=s.body.license,r.aioseo.data.isNetworkLicensed=r.aioseo.data.isNetworkAdmin,this.clearLicenseNotices()),s.body.aiOptions&&Object.keys(s.body.aiOptions).forEach(a=>{const c=r.aioseo.data.isNetworkAdmin?"internalNetworkOptions":"internalOptions";o.updateOption(c,{groups:["internal","ai"],key:a,value:s.body.aiOptions[a]})}),s})},multisite(e){const t=je(),r=P();return h.post(p.restUrl("multisite")).send({network:r.aioseo.data.isNetworkAdmin,sites:e}).then(s=>{r.aioseo.data={...r.aioseo.data,network:{...r.aioseo.data.network}},t.updateNotifications(s.body.notifications)})},deactivate(){const e=je(),t=P();return h.post(p.restUrl("deactivate")).send({network:t.aioseo.data.isNetworkAdmin}).then(r=>{var u;const s=ae(),o=t.aioseo.data.isNetworkAdmin?"networkOptions":"options";return s.updateOption(o,{groups:["general"],key:"licenseKey",value:null}),e.updateNotifications(r.body.notifications),r.body.licenseData&&(Object.keys(r.body.licenseData).forEach(a=>{const c=t.aioseo.data.isNetworkAdmin?"internalNetworkOptions":"internalOptions";s.updateOption(c,{groups:["internal","license"],key:a,value:r.body.licenseData[a]})}),this.license=r.body.license,(u=r==null?void 0:r.body)!=null&&u.aiData&&Object.keys(r.body.aiData).forEach(a=>{const c=t.aioseo.data.isNetworkAdmin?"internalNetworkOptions":"internalOptions";s.updateOption(c,{groups:["internal","ai"],key:a,value:r.body.aiData[a]})}),t.aioseo.isUnlicensed=!0,this.addLicenseNotices()),r})},clearLicenseNotices(){const e=document.querySelector(".aioseo-submenu-highlight");e&&e.remove();const t=document.querySelector("#wp-admin-bar-aioseo-pro-license");t&&t.remove()},addLicenseNotices(){this.clearLicenseNotices();const e=document.querySelector("#toplevel_page_aioseo ul.wp-submenu-wrap");if(e){const r=document.createElement("li");r.classList.add("aioseo-submenu-highlight");const s=Gr();r.appendChild(s),e.appendChild(r)}const t=document.querySelector("#wp-admin-bar-aioseo-main-default");if(t){const r=document.createElement("li");r.id="wp-admin-bar-aioseo-pro-license";const s=Gr();r.appendChild(s),t.appendChild(r)}}}}),qs=M("LinkAssistantStore",{state:()=>({options:{main:{affiliatePrefix:"",wordsToIgnore:"",skipSentences:3,postTypes:{all:!0,included:null},postStatuses:{all:!0,included:null},excludePosts:[]}},internalOptions:{internal:{minimumLinkScanDate:null,minimumSuggestionScanDate:null,dismissedAlerts:{suggestions:!1}}},overview:{totals:{crawledPosts:0,externalLinks:0,internalLinks:0,affiliateLinks:0,orphanedPosts:0},linkingOpportunities:[],mostLinkedDomains:[]},domainsReport:{rows:[],totals:{page:1,pages:1,total:1},innerPagination:{}},linksReport:{rows:[],totals:{page:1,pages:1,total:1},prioritizedPosts:[],counts:[{inboundInternal:0,outboundInternal:0,affiliate:0,external:0,suggestionsInbound:0,suggestionsOutbound:0}]},postReport:{inboundInternal:{rows:[],totals:{page:1,pages:1,total:0}},outboundInternal:{rows:[],totals:{page:1,pages:1,total:0}},affiliate:{rows:[],totals:{page:1,pages:1,total:0}},external:{rows:[],totals:{page:1,pages:1,total:0}},suggestionsInbound:{rows:[],totals:{page:1,pages:1,total:0}},suggestionsOutbound:{rows:[],totals:{page:1,pages:1,total:0}}},suggestionsScan:{percent:0,showProcessingPopup:!1},skipNextPostSettingsUpdate:!1}),actions:{linkDelete({postId:e,linkId:t,linksReport:r,postReport:s}){const o=r?"links-report-inner":s?"post-report":"post-settings";return h.post(p.restUrl(`link-assistant/${o}/links/delete`)).send({postId:e,linkId:t}).then(u=>{if(!r&&!s){if(u.body){const a=De(),c=a.currentPost;c.linkAssistant.links=u.body.links,a.updateState(c)}return}this.getOverviewData(),!s&&r&&this.setLinksReportCounts()})},linksBulk({postId:e,action:t,linkType:r,linkIds:s,linksReport:o,postReport:u}){const a=o?"links-report-inner":u?"post-report":"post-settings";return h.post(p.restUrl(`link-assistant/${a}/links/bulk`)).send({postId:e,action:t,linkType:r,linkIds:s}).then(c=>{if(!o&&!u){if(c.body){const A=De(),x=A.currentPost;x.linkAssistant.links=c.body.links,A.updateState(x)}return}this.getOverviewData(),!u&&o&&this.setLinksReportCounts()})},linksRefresh({postId:e,linksReport:t,postReport:r}){const s=t?"links-report-inner":r?"post-report":"post-settings";return h.post(p.restUrl(`link-assistant/${s}/refresh`)).send({postId:e}).then(()=>{this.linksReport.prioritizedPosts.push(e),t&&this.setLinksReportCounts(),this.pollSuggestionsScan()}).catch(o=>{console.error("Couldn't prioritize the post for the next scan.",o)})},suggestionDismiss({postIndex:e,postId:t,suggestionId:r,linksReport:s,postReport:o}){const u=De(),c=s?"links-report-inner":o?"post-report":"post-settings";return h.post(p.restUrl(`link-assistant/${c}/suggestions/dismiss`)).send({postId:t||u.currentPost.id,suggestionId:r}).then(A=>{if(!s&&!o){if(A.body){const x=u.currentPost;x.linkAssistant.links=A.body.links,u.updateState(x)}return}o||s&&((e||e===0)&&(this.linksReport.rows[e].links=A.body.links),this.getOverviewData(),this.setLinksReportCounts())})},suggestionsBulk({postId:e,action:t,suggestionType:r,suggestionRows:s,linksReport:o,postReport:u}){const a=o?"links-report-inner":u?"post-report":"post-settings";return h.post(p.restUrl(`link-assistant/${a}/suggestions/bulk`)).send({postId:e,action:t,suggestionType:r,suggestionRows:s}).then(c=>{if(!o&&!u){if(c.body){const A=De(),x=A.currentPost;x.linkAssistant.links=c.body.links,A.updateState(x)}return}this.getOverviewData(),!u&&o&&this.setLinksReportCounts()})},fetchLinksReport({orderBy:e,orderDir:t,limit:r,offset:s,searchTerm:o,filter:u,additionalFilters:a}){return h.post(p.restUrl(`link-assistant/links-report/${u}`)).send({orderBy:e,orderDir:t,limit:r,offset:s,searchTerm:o,additionalFilters:a}).then(c=>{this.linksReport=c.body.linksReport,this.getOverviewData(),this.setLinksReportCounts()})},fetchLinksReportInner({filter:e,additionalFilters:t}){return h.post(p.restUrl(`link-assistant/links-report-inner/${e}`)).send({additionalFilters:t}).then(r=>{t!=null&&t.postIndex&&(this.linksReport.rows[t.postIndex].links=r.body.links),this.getOverviewData()})},fetchDomainsReport({orderBy:e,orderDir:t,limit:r,offset:s,searchTerm:o,filter:u,additionalFilters:a}){return h.post(p.restUrl(`link-assistant/domains-report/${u}`)).send({orderBy:e,orderDir:t,limit:r,offset:s,searchTerm:o,additionalFilters:a}).then(c=>{this.domainsReport=c.body.domainsReport,this.getOverviewData()})},fetchDomainsReportInner({orderBy:e,orderDir:t,offset:r,searchTerm:s,filter:o,additionalFilters:u}){return h.post(p.restUrl(`link-assistant/domains-report-inner/${o}`)).send({orderBy:e,orderDir:t,offset:r,searchTerm:s,additionalFilters:u}).then(a=>{this.domainsReport.rows[u.domainIndex][u.domain]=a.body.posts||[],this.getOverviewData()})},fetchPostReport({orderBy:e,orderDir:t,limit:r,offset:s,searchTerm:o,filter:u,additionalFilters:a={}}){return h.post(p.restUrl(`link-assistant/post-report/${u}`)).send({orderBy:e,orderDir:t,limit:r,offset:s,searchTerm:o,additionalFilters:a}).then(c=>(a.type&&(this.postReport[a.type]=c.body.links),c))},linksReportDeleteAll({postId:e}){return h.delete(p.restUrl(`link-assistant/links-report/post/${e}`)).then(()=>{})},domainsReportBulk({action:e,rowIndexes:t}){const r=[];return Array.isArray(t)?t.forEach(s=>{r.push(Object.keys(this.domainsReport.rows[s])[0])}):r.push(Object.keys(this.domainsReport.rows[t])[0]),h.post(p.restUrl(`link-assistant/domains-report/bulk/${e}`)).send({hostnames:r}).then(()=>{this.getOverviewData()})},domainsReportInnerBulk({offset:e,searchTerm:t,action:r,domainIndex:s,linkIndexes:o}){const u=[];if(Array.isArray(o))o.forEach(a=>{const c=Object.keys(this.domainsReport.rows[s])[0];this.domainsReport.rows[s][c][a].links.forEach(x=>{u.push(x)})});else{const a=Object.keys(this.domainsReport.rows[s])[0];this.domainsReport.rows[s][a][o].links.forEach(A=>{u.push(A)})}return h.post(p.restUrl(`link-assistant/domains-report-inner/bulk/${r}`)).send({searchTerm:t,links:u,offset:e}).then(()=>{this.getOverviewData()})},domainsReportInnerLinkUpdate({domain:e,link:t}){return h.put(p.restUrl("link-assistant/domains-report-inner/link")).send({hostname:e,link:t}).then(()=>{this.getOverviewData()})},domainsReportInnerLinkDelete({searchTerm:e,rows:t,postIndex:r,linkIndex:s,offset:o}){const u=t[r].links[s];return h.delete(p.restUrl("link-assistant/domains-report-inner/link")).send({searchTerm:e,offset:o,link:u}).then(()=>{this.getOverviewData()})},postSettingsUpdate({postContent:e,skipNextRun:t}){if(this.skipNextPostSettingsUpdate){this.skipNextPostSettingsUpdate=!1;return}const r=De();return window.aioseoBus.$emit("updatingLinks",!0),h.post(p.restUrl("link-assistant/post-settings/update")).send({postId:r.currentPost.id,postContent:e}).then(s=>{if(s.body){const o=r.currentPost;o.linkAssistant.links=s.body.links,r.updateState(o)}t&&(this.skipNextPostSettingsUpdate=!0)}).catch(s=>{console.error("Couldn't get updated Link Assistant data:",s)}).finally(()=>{window.aioseoBus.$emit("updatingLinks",!1)})},getMenuData(){return h.get(p.restUrl("link-assistant/data/menu")).then(e=>{e.body.data&&this.resetState(e.body.data)})},getOverviewData(){return h.get(p.restUrl("link-assistant/data/overview")).then(e=>{e.body.data&&(this.overview=e.body.data)})},getPostData(){const e=De(),t=e.currentPost.id;return h.get(p.restUrl(`link-assistant/data/post/${t}`)).then(r=>{if(r.body.data){const s=e.currentPost;s.linkAssistant=r.body.data.currentPost.linkAssistant,e.updateState(s),this.resetState(r.body.data)}})},triggerScan(){return h.get(p.restUrl("link-assistant/data/trigger-scan")).then(e=>{e.body.data&&this.resetState(e.body.data)})},pollSuggestionsScan(){return h.get(p.restUrl("link-assistant/data/suggestions-scan-percent")).then(e=>{e.body&&"percent"in e.body&&(this.suggestionsScan.percent!==e.body.percent&&this.getOverviewData(),this.suggestionsScan.percent=e.body.percent,e.body.percent!==100&&setTimeout(()=>{this.pollSuggestionsScan()},1e4))})},setLinksReportCounts(){const e=[];this.linksReport.rows.forEach((t,r)=>{e[r]={inboundInternal:t.links.inboundInternal.totals.total,outboundInternal:t.links.outboundInternal.totals.total,affiliate:t.links.affiliate.totals.total,external:t.links.external.totals.total,suggestionsInbound:t.links.suggestionsInbound.totals.total,suggestionsOutbound:t.links.suggestionsOutbound.totals.total}}),this.linksReport.counts=e},resetState(e){e.options&&(this.options=e.options),e.internalOptions&&(this.internalOptions=e.internalOptions),e.overview&&(this.overview=e.overview),e.domainsReport&&(this.domainsReport=e.domainsReport),e.linksReport&&(this.linksReport=e.linksReport)},toggleProcessingPopup(){this.suggestionsScan.showProcessingPopup=!this.suggestionsScan.showProcessingPopup},setDomainsReportInnerPaginatedPage({domain:e,page:t}){const r=this.domainsReport.innerPagination||{};r[e]=t,this.domainsReport.innerPagination=r},resetPostReportState(){this.postReport={inboundInternal:{rows:[],totals:{page:1,pages:1,total:0}},outboundInternal:{rows:[],totals:{page:1,pages:1,total:0}},affiliate:{rows:[],totals:{page:1,pages:1,total:0}},external:{rows:[],totals:{page:1,pages:1,total:0}},suggestionsInbound:{rows:[],totals:{page:1,pages:1,total:0}},suggestionsOutbound:{rows:[],totals:{page:1,pages:1,total:0}}}}}}),Vu=M("LocalSeoStore",{state:()=>({importers:[]}),actions:{importPlugins(e){return h.post(p.restUrl("local-business/import-plugins")).send(e).then(t=>{var r,s;if((r=t.body)!=null&&r.localBusinessOptions){const o=ae();o.options.localBusiness=_({...o.options.localBusiness},{...((s=t.body)==null?void 0:s.localBusinessOptions)||{}})}})}}}),Ls=M("NetworkStore",{state:()=>({activeSites:[],networkData:{},networkRobots:{rules:[]},currentSite:{}}),getters:{getNetworkRobots:()=>{const e=ae();return e.networkOptions.tools?e.networkOptions.tools.robots:[]}},actions:{fetchNetworkSites({orderBy:e,orderDir:t,limit:r,offset:s,searchTerm:o,filter:u}){return h.post(p.restUrl(`network-sites/${u}`)).send({orderBy:e,orderDir:t,limit:r,offset:s,searchTerm:o}).then(a=>{const c=P();c.aioseo.data={...c.aioseo.data,network:{...c.aioseo.data.network,sites:a.body.sites}},this.networkData.sites=a.body.sites})},fetchSiteRobots(e){return h.get(p.restUrl(`network-robots/${e}`)).then(t=>{this.networkRobots.rules=t.body.rules})},getActiveSites(e){return h.post(p.restUrl("activated")).send({domains:e}).then(t=>{this.activeSites=t.body.activeSites})},importRobotsTxt({url:e,text:t,source:r,networkLevel:s,blogId:o}){return h.post(p.restUrl("tools/import-robots-txt")).send({url:e,text:t,source:r,networkLevel:s,blogId:o}).then(u=>u)}}}),ju=e=>{const t=document.querySelector(".aioseo-menu-notification-counter");if(t)if(e.active.length)t.innerText=e.active.length;else{t.remove();const r=document.querySelector("#wp-admin-bar-aioseo-notifications");r&&r.remove();const s=document.querySelector("#toplevel_page_aioseo .wp-first-item"),o=document.querySelector("#toplevel_page_aioseo .wp-first-item .aioseo-menu-notification-indicator");s!=null&&s.contains(o)&&s.remove()}},je=M("NotificationsStore",{state:()=>({active:[],new:[],dismissed:[],force:!1,showNotifications:!1}),getters:{activeNotifications:e=>e.active,activeNotificationsCount:e=>e.active.length,dismissedNotifications:e=>e.dismissed,dismissedNotificationsCount:e=>e.dismissed.length},actions:{toggleNotifications(){this.showNotifications=!this.showNotifications},dismissNotifications(e){return e.reverse().forEach(r=>{const s=this.active.findIndex(o=>o.slug===r);s!==-1&&this.active.splice(s,1)}),h.post(p.restUrl("notifications/dismiss")).send(e).then(r=>{if(!r.body.success)throw new Error(r.body.message);this.updateNotifications(r.body.notifications)})},updateNotifications(e){e.new.length&&window.aioseoNotifications&&(window.aioseoNotifications.newNotifications=e.new.length),this.active=e.active,this.new=e.new,this.dismissed=e.dismissed,ju(e)},processButtonAction(e){const t=P();return h.post(p.restUrl(`${e}`)).send({network:t.aioseo.data.isNetworkAdmin}).then(r=>{if(this.updateNotifications(r.body.notifications),!r.body.success)throw new Error(r.body.message)})}}}),ae=M("OptionsStore",{state:()=>({dynamicOptions:{},internalNetworkOptions:{},internalOptions:{},networkOptions:{},options:{},htaccessError:null,saveError:null}),getters:{aiCreditPercentage(){return Math.round(this.internalOptions.internal.ai.credits.remaining/this.internalOptions.internal.ai.credits.total*100)}},actions:{saveChanges(){const e=Ke(),t=or(),r=qs(),s=je(),o=ur(),u=P(),a={options:this.options,dynamicOptions:this.dynamicOptions,network:u.aioseo.data.isNetworkAdmin,networkOptions:this.networkOptions};switch(u.aioseo.page){case"redirects":{a.redirectOptions=o.options;break}case"link-assistant":{a.linkAssistantOptions=r.options;break}case"settings":{a.indexNowOptions=t.options;break}}return h.post(p.restUrl("options")).send(a).then(c=>(s.updateNotifications(c.body.notifications),e.updateOriginalOptions("options",this.options),e.updateOriginalOptions("dynamicOptions",this.dynamicOptions),o!=null&&o.options&&e.updateOriginalOptions("redirectOptions",o.options),t.options.indexNow.apiKey&&e.updateOriginalOptions("indexNowOptions",t.options),c.body.redirection&&(c.body.redirection==="reload"?window.location.reload():window.location.href=c.body.redirection),c)).catch(c=>{console.error("Failed to save the options.",c),this.saveError=c.response.body.message})},saveHtaccess(){const e=P();return h.post(p.restUrl("htaccess")).send({htaccess:e.aioseo.data.htaccess}).then(()=>{}).catch(t=>{console.error("Failed to update .htaccess file.",t),this.htaccessError=t.response.body.message})},saveNetworkRobots(){var a,c;const e=Ke(),t=Ls(),r=P(),s=((a=t.currentSite)==null?void 0:a.blog_id)==="network",o=s?this.networkOptions:this.options,u={enabled:s?this.networkOptions.tools.robots.enable:this.options.tools.robots.enable,network:r.aioseo.data.isNetworkAdmin,rules:t.networkRobots.rules,searchAppearance:o.searchAppearance};return h.post(p.restUrl(`network-robots/${(c=t.currentSite)==null?void 0:c.blog_id}`)).send(u).then(()=>{e.updateOriginalOptions(s?"networkOptions":"options",o)})},getObjects(e){return h.post(p.restUrl("objects")).send(e).then(t=>{if(!t.body.success)throw new Error(t.body.message);return t})},updateOption(e,{groups:t,key:r,value:s}){let o=this[e];t.forEach(u=>{o=o[u]}),!(o===void 0||o[r]===void 0)&&(o[r]=s)},fetchOptions(e){return h.get(p.restUrl("options")).query({siteId:e}).then(t=>(this.options=t.body.options,t)).catch(t=>{throw t})}}}),Ku=M("PluginsStore",{state:()=>({plugins:{}}),actions:{installPlugins(e){const t=P();return h.post(p.restUrl("plugins/install")).send({network:t.aioseo.data.isNetworkAdmin,plugins:e}).then(r=>{if(!r.body.success)throw new Error(r.body.message);const s=jt();return Object.keys(r.body.completed).forEach(o=>{const u=r.body.completed[o],a=s.addons.find(c=>o===c.sku);a&&(a.basename=u,a.installed=!0,a.hasMinimumVersion=!0,a.installedVersion=a.version,s.updateAddon(a))}),r})},upgradePlugins(e){const t=P();return h.post(p.restUrl("plugins/upgrade")).send({network:t.aioseo.data.isNetworkAdmin,plugins:e}).then(r=>{if(!r.body.success)throw new Error(r.body.message);const s=jt();return Object.keys(r.body.completed).forEach(o=>{s.updateAddon(r.body.completed[o])}),r})},deactivatePlugins(e){const t=P();return h.post(p.restUrl("plugins/deactivate")).send({network:t.aioseo.data.isNetworkAdmin,plugins:e}).then(r=>r)}}});let Mt=null;const Vr=e=>{const t=["modalOpen","seo_score","page_analysis"],r=JSON.parse(JSON.stringify(e));return t.forEach(s=>{delete r[s]}),JSON.stringify(r)},De=M("PostEditorStore",{state:()=>({isDirty:!1,currentPost:{}}),getters:{newHeadlineAnaylzerData(){var r,s,o,u,a,c;const e=(s=(r=this.currentPost.headlineAnalyzer)==null?void 0:r.newData)!=null&&s.headline?this.currentPost.headlineAnalyzer.newData.headline:"";let t=(a=(o=this.currentPost.headlineAnalyzer)==null?void 0:o.newData)!=null&&a.data[(u=Object.keys(this.currentPost.headlineAnalyzer.newData.data))==null?void 0:u[0]]?this.currentPost.headlineAnalyzer.newData.data[(c=Object.keys(this.currentPost.headlineAnalyzer.newData.data))==null?void 0:c[0]]:null;return t=t?JSON.parse(t):null,{newTitle:e,newResult:t}}},actions:{updateTitle(e){this.currentPost.title=e,window.aioseoBus.$emit("updateTitleKey")},updateDescription(e){this.currentPost.description=e,window.aioseoBus.$emit("updateDescriptionKey")},updatePostHeadlineAnalyzerData(e,t){var r,s;if(this.currentPost.headlineAnalyzer=this.currentPost.headlineAnalyzer||{},this.currentPost.headlineAnalyzer.data=e,this.currentPost.headlineAnalyzer.headline=t,this.currentPost.headlineAnalyzer.previousHeadlines||(this.currentPost.headlineAnalyzer.previousHeadlines=[]),this.currentPost.headlineAnalyzer.data[(r=Object.keys(this.currentPost.headlineAnalyzer.data))==null?void 0:r[0]]){let o=this.currentPost.headlineAnalyzer.data[(s=Object.keys(this.currentPost.headlineAnalyzer.data))==null?void 0:s[0]];o=JSON.parse(o),this.currentPost.headlineAnalyzer.previousHeadlines.some(a=>a.headline===t)||(this.currentPost.headlineAnalyzer.previousHeadlines.push({headline:t,result:o,score:o.score}),this.currentPost.headlineAnalyzer.latestScore=o.score)}},updateLatestScore(e){this.currentPost.headlineAnalyzer.latestScore=e},shouldShowPrevScores(){this.currentPost.headlineAnalyzer.showPrevScores=!0},updateNewHeadlineAnalyzerData(e,t){var o;this.currentPost.headlineAnalyzer.newData=this.currentPost.headlineAnalyzer.newData||{},this.currentPost.headlineAnalyzer.newData.data=e,this.currentPost.headlineAnalyzer.newData.headline=t,this.currentPost.headlineAnalyzer.newData.showPreview=!0,this.currentPost.headlineAnalyzer.previousHeadlines||(this.currentPost.headlineAnalyzer.previousHeadlines=[]);let r=this.currentPost.headlineAnalyzer.newData.data[(o=Object.keys(this.currentPost.headlineAnalyzer.newData.data))==null?void 0:o[0]];r=JSON.parse(r),this.currentPost.headlineAnalyzer.previousHeadlines.some(u=>u.headline===t)||(this.currentPost.headlineAnalyzer.previousHeadlines.push({headline:t,result:r,score:r.score}),this.currentPost.headlineAnalyzer.latestScore=r.score)},toggleShowNewHeadlineAnalyzerData(e){this.currentPost.headlineAnalyzer.showNewData=e},toggleShowNewHeadlineAnalyzerPreview(e){this.currentPost.headlineAnalyzer.newData.showPreview=e},changeGeneralPreview(e){this.currentPost.generalMobilePrev=e},saveCurrentPost(e){return this.currentPost=e,h.post(p.restUrl("post")).send(e).then(()=>{}).catch(t=>{console.error(`Unable to update the post data: ${t}`)})},updateState(e){this.currentPost=e},savePostState(){if(!this.currentPost||!Object.keys(this.currentPost).length)return;Mt===null&&(Mt=Vr(this.currentPost)),Mt!==Vr(this.currentPost)&&window.aioseoBus.$emit("postSettingsUpdated");const e=document.querySelector("#aioseo-post-settings");if(e&&(e.value=JSON.stringify(this.currentPost)),this.currentPost.context==="term"){const t=document.querySelector("#aioseo-term-settings");t&&(t.value=JSON.stringify(this.currentPost))}},disablePrimaryTermEducation(){return this.currentPost.options.primaryTerm.productEducationDismissed=!0,h.post(p.restUrl(`post/${this.currentPost.id}/disable-primary-term-education`))},disableLinkAssistantEducation(){return this.currentPost.options.linkFormat.linkAssistantDismissed=!0,h.post(p.restUrl(`post/${this.currentPost.id}/disable-link-format-education`))},incrementInternalLinkCount(){const e=this.currentPost.options.linkFormat.internalLinkCount||0;return this.currentPost.options.linkFormat.internalLinkCount=e+1,h.post(p.restUrl(`post/${this.currentPost.id}/update-internal-link-count`)).send({count:e})},getUserImage({userId:e}){return h.get(p.restUrl(`user/${e}/image`)).then(t=>t.statusCode===200?t.body.url:"")},getFirstAttachedImage({postId:e}){return h.get(p.restUrl(`post/${e}/first-attached-image`)).then(t=>t.statusCode===200?t.body.url:"")},getMediaData({mediaId:e}){return h.get(p.restUrl(`media/${e}`,"wp/v2")).then(t=>t.statusCode===200?t.body:{})},processContent({content:e}){return h.post(p.restUrl(`post/${this.currentPost.id}/process-content`)).send({content:e}).then(t=>{this.currentPost.processedContent=t.body.content})},fetchPostData(e={}){return h.get(p.restUrl("post")).query(e).then(t=>t).catch(t=>{throw t})}}}),ur=M("RedirectsStore",{state:()=>({options:{},importers:[],redirects:[],rows:[],logs:[],logs404:[],filters:[],selectedFilters:{},manualUrls:[],sort:{logs:"last_accessed",logs404:"last_accessed"},sortDir:{logs:"desc",logs404:"desc"},totals:{main:{total:0,pages:0,page:1},total404:{total:0,pages:0,page:1},logs:{total:0,pages:0,page:1}},server:{redirectTest:{testing:!1,failed:!1},filePath:""},lateRefresh:{redirects:!1,logs:!1,logs404:!1},protectedPaths:[],path:"",addNewRedirect:{sourceUrls:[],targetUrl:null,redirectType:null,queryParam:null,customRules:[],showAdvancedSettings:!1}}),actions:{updateState(e=null){for(const t in e||{})this[t]=e[t]},bulk({action:e,rowIds:t}){if(t=t.filter(o=>{const u=this.rows.find(a=>a.id===parseInt(o));return!(e==="enable"&&u.enabled||e==="disable"&&!u.enabled||e==="reset-hits"&&!u.hits)}),!t.length)return Promise.resolve();const r=e==="delete"?"delete":"post",s=e==="delete"?"":`${e}/`;return h[r](p.restUrl(`redirects/bulk/${s}`)).send({rowIds:t})},fetchRedirects({orderBy:e,orderDir:t,limit:r,offset:s,searchTerm:o,filter:u,additionalFilters:a}){var A,x;const c=De();return((A=c.currentPost)==null?void 0:A.context)==="post"||((x=c.currentPost)==null?void 0:x.context)==="term"?this.getPostRedirects():h.post(p.restUrl(`redirects/${u}`)).send({orderBy:e,orderDir:t,limit:r,offset:s,searchTerm:o,additionalFilters:a}).then(T=>{this.filters=T.body.filters,this.rows=T.body.rows,this.totals.main=T.body.totals})},fetchLogs({slug:e,orderBy:t,orderDir:r,limit:s,offset:o,searchTerm:u,filter:a}){return h.post(p.restUrl(`redirects/${e}/${a}`)).send({orderBy:t,orderDir:r,limit:s,offset:o,searchTerm:u}).then(c=>{this[e==="404"?"logs404":"logs"]=c.body.rows,this.totals[e==="404"?"total404":"logs"]=c.body.totals})},create(e){return(e==null?void 0:e.group)==="404"&&this.setLateRefresh(!0,"redirects"),h.post(p.restUrl("redirects")).send(e)},update({id:e,payload:t}){return h.post(p.restUrl(`redirects/${e}/`)).send(t).then(r=>{var s;if((s=r.body.redirect)!=null&&s.id){const o=this.rows.findIndex(u=>u.id===r.body.redirect.id);o!==-1&&(this.rows[o]=r.body.redirect)}})},delete(e){return h.delete(p.restUrl(`redirects/${e}`))},test({id:e,payload:t}){return h.post(p.restUrl(`redirects/${e}/test/`)).send(t)},deleteLog({slug:e,ids:t}){return h.delete(p.restUrl(`redirects/logs/${e}`)).send({ids:t})},exportServerRedirects(e){return h.get(p.restUrl(`redirects/export/${e}/`))},exportRedirects({groups:e,type:t}){return h.post(p.restUrl(`redirects/export/${t}/`)).send({groups:e})},exportLogs(e){return h.get(p.restUrl(`redirects/export-logs/${e}/`))},uploadFile({file:e,filename:t}){return h.post(p.restUrl("redirects/import")).attach("file",e,t).then(r=>{this.filters=r.body.filters,this.rows=r.body.rows,this.totals.main=r.body.totals})},importPlugins(e){return h.post(p.restUrl("redirects/import-plugins")).send({plugins:e}).then(()=>this.setLateRefresh({value:!0,type:"redirects"}))},importCsvRedirects(e){return h.post(p.restUrl("redirects/import-csv")).send(e)},getPosts(e){return h.post(p.restUrl("redirects/posts")).send(e).then(t=>{if(!t.body.success)throw new Error(t.body.message);return t})},getRedirectOptions(){return h.get(p.restUrl("redirects/options")).then(e=>{e.body.options&&(this.options=e.body.options,this.importers=e.body.importers,Ke().updateOriginalOptions("redirectOptions",this.options))})},testServerRedirects(){const e=je();if(!this.server.redirectTest.testing)return this.server.redirectTest.testing=!0,h.get(p.restUrl("redirects/server/test")).then(t=>{this.server.redirectTest.testing=!1,this.server.redirectTest.failed=!t.body.success,e.updateNotifications(t.body.notifications)}).catch(()=>{this.server.redirectTest.testing=!1,this.server.redirectTest.failed=!0})},getPostRedirects(){const e=De();return h.get(p.restUrl("redirects/"+e.currentPost.context+"/"+e.currentPost.id)).then(t=>{this.rows=t.body.rows,e.currentPost.permalinkPath=t.body.permalinkPath,e.currentPost.postStatus=t.body.postStatus}).catch(()=>{})},setLateRefresh({value:e=!0,type:t="all"}){t=t==="all"?["redirects","logs","logs404"]:[t];for(const r in t)this.lateRefresh[t[r]]=e},resetPageNumbers(){const{main:e,total404:t,logs:r}=this.totals;e.page=1,t.page=1,r.page=1;const s={main:e,total404:t,logs:r};this.totals=s}}}),P=M("RootStore",{state:()=>({pong:!0,loaded:!1,loading:!1,isPro:"Lite".toLowerCase()==="pro",aioseo:{},navigate:{scroll:null,highlight:null},modals:{active:null,all:[]}}),actions:{ping(){h.get(p.restUrl("ping")).catch(()=>{this.pong=!1})},setActiveModal(e){this.modals.active=e,this.modals.all.includes(e)||this.modals.all.push(e)},unsetActiveModal(e){this.modals.all.includes(e)&&(this.modals.all=this.modals.all.filter(t=>t!==e),this.modals.active=this.modals.all[this.modals.all.length-1]||null)}}}),Rs=()=>P().aioseo.user.locale.replace("_","-")||"en-US",jr={d:e=>Fe(e.getDate()),D:e=>Kr(e).substr(0,3),j:e=>e.getDate(),l:e=>Kr(e),N:e=>e.getDay()||7,S:e=>Yu(e.getDate()),w:e=>e.getDay(),z:e=>{const t=e.getFullYear(),r=e.getMonth(),s=e.getDate();return Math.floor((Date.UTC(t,r,s)-Date.UTC(t,0,1))/864e5)},W:e=>Yr(e)[1],F:e=>Wr(e),m:e=>Fe(e.getMonth()+1),M:e=>Wr(e).substr(0,3),n:e=>e.getMonth()+1,t:e=>new Date(e.getFullYear(),e.getMonth()+1,0).getDate(),L:e=>new Date(e.getFullYear(),1,29).getDate()===29?1:0,o:e=>Yr(e)[0],Y:e=>{let t=e.getFullYear();return 0>t&&(t="-"+("000"+Math.abs(t)).slice(-4)),t},y:e=>{let t=e.getFullYear();return 0<=t?("0"+t).slice(-2):(t=Math.abs(t),-+("0"+t).slice(-2))},a:e=>12>e.getHours()?"am":"pm",A:e=>12>e.getHours()?"AM":"PM",B:e=>((+e+36e5)%864e5/86400).toFixed(0),g:e=>e.getHours()%12||12,G:e=>e.getHours(),h:e=>Fe(e.getHours()%12||12),H:e=>Fe(e.getHours()),i:e=>Fe(e.getMinutes()),s:e=>Fe(e.getSeconds()),u:()=>"000000",v:e=>Xu(e.getMilliseconds()),e:()=>0,I:e=>e.getTimezoneOffset()===Wu(e)[0]?0:1,O:e=>Jr(-e.getTimezoneOffset(),!1),P:e=>Jr(-e.getTimezoneOffset(),!0),T:e=>e.toLocaleString("en",{year:"numeric",timeZoneName:"long"}).replace(/[^A-Z]/g,""),Z:e=>e.getTimezoneOffset()*-60,c:e=>Ct(e,"Y-m-d\\TH:i:sP"),r:e=>Ct(e,"D, d M Y H:i:s O"),U:e=>e.getTime()/1e3|0},Kr=e=>e.toLocaleString(Rs(),{weekday:"long"}),Wr=e=>e.toLocaleString(Rs(),{month:"long"}),Wu=e=>{const t=e.getFullYear(),r=[0,2,5,9].map(s=>new Date(t,s).getTimezoneOffset());return[Math.max(...r),Math.min(...r)]},Yu=e=>{e=e%100;const t=["th","st","nd","rd"];return(10>e||13<e)&&t[e%10]||"th"},Yr=e=>{const t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate()));t.setUTCDate(t.getUTCDate()+4-(t.getUTCDay()||7));const r=new Date(Date.UTC(t.getUTCFullYear(),0,1)),s=Math.ceil(((t-r)/864e5+1)/7);return[t.getUTCFullYear(),s]},Ju=e=>Object.prototype.toString.call(e)==="[object Date]",Jr=(e,t)=>{const r=0>e?"-":"+";e=Math.abs(e);const s=Fe(e/60|0),o=Fe(e%60);return r+s+(t?":":"")+o},Fe=e=>(10>e?"0":"")+e,Xu=e=>(10>e?"00":100>e?"0":"")+e,Ct=(e,t)=>{if(!(!Ju(e)||typeof t!="string"))return t.split("").reduce((r,s,o,u)=>(s==="\\"?r+=u.splice(o+1,1):s in jr?r+=jr[s](e):r+=s,r),"")},vt=M("SearchStatisticsStore",{state:()=>({isConnected:!1,hasInitialized:!1,latestAvailableDate:null,unverifiedSite:!1,authedSite:null,quotaExceeded:{},rolling:null,sitemapsWithErrors:[],range:{start:null,end:null,compareStart:null,compareEnd:null},loading:{seoStatistics:!1,keywords:!1,contentRankings:!1,postDetailSeoStatistics:!1,postDetailKeywords:!1},data:{seoStatistics:{statistics:{clicks:0,impressions:0,ctr:0,position:0,keywords:0,difference:{clicks:0,impressions:0,ctr:0,position:0}},intervals:[],pages:{topPages:{rows:[]},topWinning:{rows:[]},topLosing:{rows:[]},paginated:{rows:[],totals:{page:0,pages:0,total:0}}},timelineMarkers:{}},keywords:{distribution:[],distributionIntervals:[],topWinning:[],topLosing:[],topKeywords:[],paginated:{rows:[],totals:{page:0,pages:0,total:0}}},contentRankings:{paginated:{rows:[],totals:{page:0,pages:0,total:0}}},postDetail:{postId:0,seoStatistics:{intervals:[],statistics:{clicks:0,impressions:0,ctr:0,position:0,keywords:0,difference:{clicks:0,impressions:0,ctr:0,position:0}},timelineMarkers:{}},keywords:{paginated:{rows:[],totals:{page:0,pages:0,total:0}}}}},shouldShowSampleReports:!1}),actions:{getAuthUrl({returnTo:e}){return h.get(p.restUrl("search-statistics/url/auth")).query({returnTo:e}).then(t=>t.body.url)},getReauthUrl({returnTo:e}){return h.get(p.restUrl("search-statistics/url/reauth")).query({returnTo:e}).then(t=>t.body.url)},deleteAuth(){return h.delete(p.restUrl("search-statistics/auth")).send().then(e=>(e.body.success===!0&&(this.isConnected=!1),e.body))},async setDateRange(e,t=!0){const r=Fs();if(this.range={start:Ct(e.dateRange[0],"Y-m-d"),end:Ct(e.dateRange[1],"Y-m-d")},this.rolling=e.rolling,t){const s=[()=>this.updateSeoStatistics({filter:"all",searchTerm:""}),()=>this.updateKeywords({filter:"all",searchTerm:""}),()=>this.updateContentRankings({searchTerm:""}),()=>r.maybeUpdateKeywords(),()=>r.maybeUpdateGroups()];await Promise.all(s.map(o=>o()))}},loadInitialData(){var e,t,r,s,o,u,a,c;this.hasInitialized||(this.hasInitialized=!0,(t=(e=this.data.seoStatistics)==null?void 0:e.statistics)!=null&&t.impressions||this.updateSeoStatistics({filter:"all",searchTerm:""}),(o=(s=(r=this.data.keywords)==null?void 0:r.paginated)==null?void 0:s.rows)!=null&&o.length||this.updateKeywords({filter:"all",searchTerm:""}),(c=(a=(u=this.data.contentRankings)==null?void 0:u.paginated)==null?void 0:a.totals)!=null&&c.total||this.updateContentRankings({searchTerm:""}))},updateSeoStatistics(e){return this.loading.seoStatistics=!0,h.get(p.restUrl("search-statistics/stats/seo-statistics")).query({startDate:this.range.start,endDate:this.range.end,rolling:this.rolling,...e}).then(t=>{t.body.success&&(this.range=t.body.range,this.data.seoStatistics=t.body.data)}).finally(()=>{this.loading.seoStatistics=!1})},updateKeywords(e){return this.loading.keywords=!0,h.get(p.restUrl("search-statistics/stats/keywords")).query({startDate:this.range.start,endDate:this.range.end,rolling:this.rolling,...e}).then(t=>{t.body.success&&(this.range=t.body.range,this.data.keywords=t.body.data)}).finally(()=>{this.loading.keywords=!1})},updateContentRankings(e){return this.loading.contentRankings=!0,h.get(p.restUrl("search-statistics/stats/content-rankings")).query({endDate:this.latestAvailableDate,...e}).then(t=>{t.body.success&&(this.data.contentRankings=t.body.data)}).finally(()=>{this.loading.contentRankings=!1})},getInspectionResult({paths:e,force:t}){return h.get(p.restUrl("search-statistics/inspection-result")).query({"paths[]":e,force:t??!1}).then(r=>(this.quotaExceeded.urlInspection=r.body.quotaExceeded,r.body.data)).catch(r=>{throw r})},getPagesByKeywords(e={}){const t=e.keywords;return h.post(p.restUrl("search-statistics/stats/keywords/posts")).send({limit:e.limit,offset:e.offset,startDate:this.range.start,endDate:this.range.end,keywords:t}).then(r=>{var s,o;return((o=(s=r.body)==null?void 0:s.data)==null?void 0:o[t[0]].paginated)||{}}).catch(r=>{throw r})},getPostDetail(e){return h.get(p.restUrl("search-statistics/post-detail")).query({startDate:this.range.start,endDate:this.range.end,postId:e}).then(t=>t)},getPostDetailFocusKeywordTrend({focusKeyword:e}){return h.get(p.restUrl("search-statistics/post-detail/focus-keyword")).query({startDate:this.range.start,endDate:this.range.end,postId:this.data.postDetail.postId,focusKeyword:e}).then(t=>t)},updatePostDetailSeoStatistics(e){return this.loading.postDetailSeoStatistics=!0,h.get(p.restUrl("search-statistics/post-detail/seo-statistics")).query({startDate:this.range.start,endDate:this.range.end,rolling:this.rolling,postId:this.data.postDetail.postId,...e}).then(t=>{t.body.success&&(this.data.postDetail.seoStatistics=t.body.data)}).finally(()=>{this.loading.postDetailSeoStatistics=!1})},updatePostDetailKeywords(e){return this.loading.postDetailKeywords=!0,h.get(p.restUrl("search-statistics/post-detail/keywords")).query({startDate:this.range.start,endDate:this.range.end,rolling:this.rolling,postId:this.data.postDetail.postId,...e}).then(t=>{t.body.success&&(this.data.postDetail.keywords=t.body.data)}).finally(()=>{this.loading.postDetailKeywords=!1})},getPageSpeed(e){return h.get(p.restUrl("search-statistics/pagespeed")).query(e).then(t=>t)},deleteSitemap({sitemap:e}){return h.post(p.restUrl("search-statistics/sitemap/delete")).send({sitemap:e}).then(t=>{ae().updateOption("internalOptions",{groups:["internal","searchStatistics"],key:"sitemap",value:t.body.data.internalOptions}),this.sitemapsWithErrors=t.body.data.sitemapsWithErrors})},ignoreSitemap({sitemap:e}){return h.post(p.restUrl("search-statistics/sitemap/ignore")).send({sitemap:e}).then(t=>{ae().updateOption("internalOptions",{groups:["internal","searchStatistics"],key:"sitemap",value:t.body.data.internalOptions}),this.sitemapsWithErrors=t.body.data.sitemapsWithErrors})},updateSeoRevision(e){const t=this.data.postDetail.seoStatistics.timelineMarkers;Object.keys(t).forEach(r=>{t[r].forEach((s,o)=>{s.type==="aioseoRevision"&&(t[r][o].revisions=t[r][o].revisions.map(u=>u.id===e[0].id?e[0]:u))})}),this.data.postDetail.seoStatistics.timelineMarkers=t},deleteSeoRevision(e){const t=this.data.postDetail.seoStatistics.timelineMarkers;Object.keys(t).forEach(r=>{t[r].forEach((s,o)=>{s.type==="aioseoRevision"&&(t[r][o].revisions=t[r][o].revisions.filter(u=>u.id!==e.id))})}),this.data.postDetail.seoStatistics.timelineMarkers=t},showSampleReports(){this.shouldShowSampleReports=!0}}}),Zu=M("SeoRevisionsStore",{state:()=>({items:[],currentUser:{avatar:null,display_name:null},itemFrom:{},itemTo:{},itemContext:"",noteMaxLength:0,itemsLimit:0,itemsTotalCount:0,seoRevisionsDiff:[],seoRevisionsDiffCache:{},modalOpenSidebar:!1,error:null}),getters:{hasDiff(){return 0<this.seoRevisionsDiff.length?0<wo(this.seoRevisionsDiff,"diff").filter(e=>e).length:!0}},actions:{updateState(e=null){for(const t in e||{})this[t]=e[t]},delete(e){return h.delete(p.restUrl(`seo-revisions/${e}`)).then(t=>(this.itemsTotalCount=t.body.itemsTotalCount,t))},fetch(e={},t=!1){const r=De();return h.get(p.restUrl("seo-revisions/"+r.currentPost.context+"/"+r.currentPost.id)).query(e).then(s=>{if(this.itemsTotalCount=s.body.itemsTotalCount,t)for(const o of s.body.items)this.items.push(o);else this.items=s.body.items;return s})},update({id:e,payload:t}){return h.post(p.restUrl(`seo-revisions/${e}/`)).send(t).then(r=>r)},restore({id:e}){return h.post(p.restUrl(`seo-revisions/restore/${e}/`)).then(t=>t)},fetchDiff(e){const t=e.fromId+"_"+e.toId;return new Promise(r=>{this.seoRevisionsDiffCache[t]?(this.seoRevisionsDiff=this.seoRevisionsDiffCache[t],r({})):h.get(p.restUrl("seo-revisions/diff")).query(e).then(s=>{this.seoRevisionsDiff=s.body.diff,this.seoRevisionsDiffCache[t]=s.body.diff,r(s)})})}}}),_s=M("SettingsStore",{state:()=>({settings:{},metaBoxTabs:{mainSidebar:{},main:"general",modal:"general",social:"facebook",socialModal:"facebook",linkAssistant:"inbound-internal"},modals:{aiGenerator:!1},userProfile:{}}),actions:{closeCard(e){this.settings.toggledCards[e]=!1},changeTabSettings({setting:e,value:t}){this.metaBoxTabs[e]=t},setModalState({modalName:e,value:t}){this.modals[e]=t},toggleCard({slug:e,shouldSave:t}){this.settings.toggledCards[e]=!this.settings.toggledCards[e],t&&h.post(p.restUrl("settings/toggle-card")).send({card:e}).then(()=>{})},toggleRadio({slug:e,value:t}){this.settings.toggledRadio[e]=t,h.post(p.restUrl("settings/toggle-radio")).send({radio:e,value:t}).then(()=>{})},dismissAlert(e){this.settings.dismissedAlerts[e]=!0,h.post(p.restUrl("settings/dismiss-alert")).send({alert:e}).then(()=>{})},changeItemsPerPage({slug:e,value:t}){return this.settings.tablePagination[e]=t,h.post(p.restUrl("settings/items-per-page")).send({table:e,value:t}).then(()=>{})},changeTab({slug:e,value:t}){this.settings.internalTabs[e]=t},hideUpgradeBar(){return this.settings.showUpgradeBar=!1,h.post(p.restUrl("settings/hide-upgrade-bar")).send({}).then(()=>{})},hideSetupWizard(){return this.settings.showSetupWizard=!1,h.post(p.restUrl("settings/hide-setup-wizard")).send({}).then(()=>{})},changeSemrushCountry({value:e}){return this.settings.semrushCountry=e,h.post(p.restUrl("settings/semrush-country")).send({value:e}).then(()=>{})}}}),Qu=M("TagsStore",{state:()=>({tags:[],context:{},liveTags:{post_title:null,post_content:null,post_excerpt:null,taxonomy_title:null,taxonomy_description:null,custom_field:[],permalink:null,attachment_caption:null,attachment_description:null,alt_tag:null,categories:null,woocommerce_brand:null,woocommerce_sku:null,woocommerce_price:null},permalinkSlug:null}),actions:{getTags(){return h.get(p.restUrl("tags")).then(e=>{this.tags=e.body.tags})},updateTaxonomyTitle(e){this.liveTags.taxonomy_title=e},updateTaxonomyDescription(e){this.liveTags.taxonomy_description=e},updatePermalink(e){this.liveTags.permalink=e},updatePostTitle(e){this.liveTags.post_title=e},updatePostContent(e){this.liveTags.post_content=e},updatePostExcerpt(e){this.liveTags.post_excerpt=e},updateAttachmentCaption(e){this.liveTags.attachment_caption=e},updateAttachmentDescription(e){this.liveTags.attachment_description=e},updateAltTag(e){this.liveTags.alt_tag=e},updateCategories(e){this.liveTags.categories=e},updateWooCommerceBrand(e){this.liveTags.woocommerce_brand=e},updateWooCommerceSku(e){this.liveTags.woocommerce_sku=e},updateWooCommercePrice(e){this.liveTags.woocommerce_price=e},updatePermalinkSlug(e){this.permalinkSlug=e}}}),ei=M("WritingAssistantSettings",{state:()=>({seoBoost:{isLoggedIn:!1,loginUrl:"",userOptions:{},countries:{},languages:{},searchEngines:{}},updating:{userOptions:!1}}),getters:{loading:e=>e.updating.userOptions,getCountriesOptions:e=>{var o,u;const t=((o=e.seoBoost)==null?void 0:o.countries)||[],r=((u=e.seoBoost)==null?void 0:u.searchEngines)||[];if(!t||!r)return[];const s=[];return Object.keys(t).forEach(function(a){s.push({label:t[a]+" ("+r[a]+")",value:a})}),s},userCountryOption:e=>e.getCountriesOptions.find(t=>t.value===e.seoBoost.userOptions.country)||[],getLanguagesOptions:e=>{var s;const t=((s=e.seoBoost)==null?void 0:s.languages)||[];if(!t)return[];const r=[];return Object.keys(t).forEach(function(o){r.push({label:t[o],value:o})}),r},userLanguageOption:e=>e.getLanguagesOptions.find(t=>t.value===e.seoBoost.userOptions.language)||[]},actions:{setUserLoggedIn(e){this.seoBoost.isLoggedIn=e,this.refreshUserOptions()},disconnect(){h.post(p.restUrl("writing-assistant/disconnect")).send().then(e=>{e.body.success&&(this.seoBoost.isLoggedIn=!1)})},hookSaveUserOptions(){window.aioseoBus.$on("saving-changes",()=>{this.saveUserOptions()})},saveUserOptions(){this.seoBoost.isLoggedIn&&h.post(p.restUrl("writing-assistant/user-options")).send({country:this.seoBoost.userOptions.country,language:this.seoBoost.userOptions.language}).then(e=>{if(!e.body.success)throw new Error(e.body.message)})},getCountryLabel(e){var t;return((t=this.getCountriesOptions.find(r=>r.value.toUpperCase()===e.toUpperCase()))==null?void 0:t.label)||""},getLanguageLabel(e){var t;return((t=this.getLanguagesOptions.find(r=>r.value===e))==null?void 0:t.label)||""},refreshUserOptions(){this.updating.userOptions||(this.updating.userOptions=!0,h.get("/writing-assistant/user-options").then(e=>{var t;(t=e.body)!=null&&t.error||(this.seoBoost.userOptions=e.body||{}),this.updating.userOptions=!1}))}}}),ti=M("WpCodeStore",{state:()=>({snippets:[],pluginInstalled:!1,pluginActive:!1,pluginNeedsUpdate:!1,ctaUrl:""}),getters:{getSnippets:e=>e.snippets},actions:{loadSnippets(){return h.post(p.restUrl("integration/wpcode/snippets")).send().then(e=>{e.body.snippets&&(this.snippets=e.body.snippets),this.pluginInstalled=e.body.pluginInstalled,this.pluginActive=e.body.pluginActive,this.pluginNeedsUpdate=e.body.pluginNeedsUpdate})}}}),Oe=Co();let Xr=!1;const fi=(e,t=null,r=()=>{})=>{var L,X,ne,de,G,Ae,C;ri(e,t);const s=P();if(s.loaded)return Oe;const o=JSON.parse(JSON.stringify(window.aioseo||{})),u=jt(),a=Hu(),c=zu(),A=Ke(),x=$u(),T=or(),I=Mu(),W=Fs(),v=Os(),V=qs(),le=Vu(),ie=Ls(),ge=je(),j=ae(),ee=Ku(),N=De(),S=ur(),ce=vt(),f=Zu(),d=_s(),g=Qu(),y=ti(),b=ei(),B={sites:(X=(L=o.data)==null?void 0:L.network)==null?void 0:X.sites,activeSites:(de=(ne=o.data)==null?void 0:ne.network)==null?void 0:de.activeSites};return j.dynamicOptions=_({...j.dynamicOptions},{...o.dynamicOptions||{}}),j.internalOptions=_({...j.internalOptions},{...o.internalOptions||{}}),j.options=_({...j.options},{...o.options||{}}),j.internalNetworkOptions=_({...j.internalNetworkOptions},{...o.internalNetworkOptions||{}}),j.networkOptions=_({...j.networkOptions},{...o.networkOptions||{}}),u.addons=_([...u.addons],[...o.addons||[]]),a.$state=_({...a.$state},{...o.analyzer||{}}),c.backups=_([...c.backups],[...o.backups||[]]),c.networkBackups=_({...c.networkBackups},{...((Ae=(G=o.data)==null?void 0:G.network)==null?void 0:Ae.backups)||{}}),x.$state=_({...x.$state},{...o.helpPanel||{}}),T.$state=_({...T.$state},{...o.indexNow||{}}),I.$state=_({...I.$state},{...o.indexStatus||{}}),W.$state=_({...W.$state},{...o.keywordRankTracker||{}}),v.license=_({...v.license},{...o.license||{}}),V.$state=_({...V.$state},{...o.linkAssistant||{}}),le.$state=_({...le.$state},{...o.localBusiness||{}}),ie.networkData=_({...ie.networkData},{...B}),ge.$state=_({...ge.$state},{...o.notifications||{}}),ee.plugins=_({...ee.plugins},{...o.plugins||{}}),N.currentPost=_({...N.currentPost},{...o.currentPost||{}}),S.$state=_({...S.$state},{...o.redirects||{}}),ce.$state=_({...ce.$state},{...o.searchStatistics||{}}),f.$state=_({...f.$state},{...o.seoRevisions||{}}),d.settings=_({...d.settings},{...o.settings||{}}),d.userProfile=_({...d.userProfile},{...o.userProfile||{}}),g.$state=_({...g.$state},{...o.tags||{}}),b.$state=_({...b.$state},{...o.writingAssistantSettings||{}}),(C=o.integrations)!=null&&C.wpcode&&(y.$state=_({...y.$state},{...o.integrations.wpcode||{}})),r(o),delete o.addons,delete o.analyzer,delete o.backups,delete o.currentPost,delete o.dynamicOptions,delete o.helpPanel,delete o.indexNow,delete o.internalNetworkOptions,delete o.internalOptions,delete o.keywordRankTracker,delete o.license,delete o.linkAssistant,delete o.networkOptions,delete o.notifications,delete o.options,delete o.plugins,delete o.redirects,delete o.searchStatistics,delete o.seoRevisions,delete o.settings,delete o.tags,delete o.userProfile,o.publicPath="/",o.translations={},s.aioseo=_({...s.aioseo},{...o||{}}),A.updateOriginalOptions("options",j.options),A.updateOriginalOptions("dynamicOptions",j.dynamicOptions),A.updateOriginalOptions("networkOptions",j.networkOptions),S!=null&&S.options&&A.updateOriginalOptions("redirectOptions",S.options),A.updateOriginalOptions("indexNowOptions",T.options),T.options.indexNow.apiKey||A.disableDirtyCheck("indexNowOptions"),s.loaded=!0,Xr||(Xr=!0,window.addEventListener("beforeunload",R=>{if(!A.isDirty)return;const F=n("Are you sure you want to leave? you have unsaved changes!","all-in-one-seo-pack");return(R||window.event).returnValue=F,F})),Oe},ri=(e,t=null)=>(t&&Oe.use(({store:r})=>{r.$router=tr(t)}),e?(e.use(Oe),Oe):(nt(Oe),Oe));export{ru as $,Yo as A,Jo as B,Qo as C,eu as D,di as E,Qu as F,ei as G,gi as H,qs as I,Nu as J,ai as K,Mu as L,$u as M,bs as N,du as O,pu as P,cu as Q,hu as R,Iu as S,zu as T,ri as U,or as V,Io as W,tu as X,Zo as Y,Xo as Z,pi as _,p as a,Fs as a0,hi as a1,Ke as b,ae as c,M as d,De as e,jt as f,je as g,h,Wo as i,Os as j,vt as k,fi as l,_s as m,Hu as n,Ls as o,ur as p,_ as q,Ku as r,er as s,Ct as t,P as u,ti as v,zr as w,Zu as x,li as y,ci as z};
