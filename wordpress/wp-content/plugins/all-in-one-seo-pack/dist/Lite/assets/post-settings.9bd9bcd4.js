var ce=Object.defineProperty;var le=(n,s,c)=>s in n?ce(n,s,{enumerable:!0,configurable:!0,writable:!0,value:c}):n[s]=c;var A=(n,s,c)=>le(n,typeof s!="symbol"?s+"":s,c);import{v as l,o as _,c as f,H as i,a,t as m,l as g,k as V,b as k,x as O,G as de,N as pe,q as ue,T as me,Y as N,h as U}from"./js/runtime-dom.esm-bundler.baf35205.js";import{e as K,l as _e}from"./js/index.7c01c5f2.js";import{l as ge}from"./js/index.d63536bd.js";import{l as fe}from"./js/index.d80c2c2c.js";import{e as D,i as B,f as be,u as ne,E as he,p as Se,y as ve,z as we,l as xe}from"./js/index.a13799ab.js";import{A as J,j as ye}from"./js/SchemaStore.93bb47b2.js";import{d as Ce}from"./js/debounce.f2bce338.js";import{_ as ie}from"./js/App.f847e843.js";import{s as ae}from"./js/metabox.e4ccf554.js";import"./js/translations.d159963e.js";import{_ as o}from"./js/default-i18n.20001971.js";import{e as Pe}from"./js/elemLoaded.2921fc72.js";import{l as Le}from"./js/loadTruSeo.1a45f87b.js";import{d as Ee}from"./js/Caret.7cc96622.js";import{C as re}from"./js/SettingsRow.67d94513.js";import{_ as S}from"./js/_plugin-vue_export-helper.eefbdd86.js";import{d as Ae,L as Ie,C as ke,G as Ve,e as Q,H as X}from"./js/constants.a8a14dc3.js";import{B as De}from"./js/Phone.8dd3b846.js";import{C as He}from"./js/Tabs.4788ba43.js";import{B as Oe}from"./js/Checkbox.d4a94670.js";import{B as Be}from"./js/RadioToggle.c3ae1828.js";import{S as Te}from"./js/Settings.5791bd18.js";import"./js/helpers.a0b389be.js";import"./js/cleanForSlug.cacc99d0.js";import"./js/toString.1e64e8a6.js";import"./js/contentHasAssets.e1428256.js";import"./js/_baseSet.ca702273.js";import"./js/regex.8a6101c0.js";import"./js/toNumber.89dbadc6.js";/* empty css                */import"./js/LicenseKeyBar.790f77f2.js";import"./js/ScrollTo.81bea8a7.js";import"./js/params.af7ed354.js";import"./js/LogoGear.383c5312.js";import"./js/allowed.b3be0973.js";import"./js/Index.278f7f5c.js";import"./js/Row.c7b7fb90.js";import"./js/Url.772412e1.js";import"./js/Cta.31c7f3e4.js";import"./js/datetime.f197aeae.js";import"./js/Tooltip.78b61f71.js";import"./js/CheckSolid.1941c38f.js";import"./js/popup.92105c51.js";import"./js/Index.9f340035.js";import"./js/stripHTMLTags.82cef782.js";import"./js/_arrayEach.6af5abac.js";import"./js/_getTag.69d3a807.js";import"./js/Plus.d91e5f23.js";import"./js/HighlightToggle.f01975c9.js";import"./js/Radio.2d9be588.js";import"./js/Checkmark.b35299d3.js";import"./js/Blur.601d8ea8.js";import"./js/MaxCounts.7c38e980.js";import"./js/Ellipse.14cb5809.js";import"./js/Eye.60f8b51e.js";import"./js/TruSeoScore.ded23f16.js";import"./js/Statistics.ec3f46e9.js";import"./js/GoogleSearchPreview.ed1b5a5f.js";import"./js/HtmlTagsEditor.795b77e2.js";import"./js/Editor.cb52ad98.js";import"./js/isEqual.34cebf90.js";import"./js/_baseIsEqual.e5574158.js";import"./js/_baseClone.475f8e3d.js";import"./js/UnfilteredHtml.c5e8e521.js";import"./js/Slide.1db617da.js";import"./js/ProBadge.2e262a91.js";import"./js/ConnectStore.82ab5d01.js";import"./js/SetupWizardStore.2bf26928.js";import"./js/license.61cecabd.js";import"./js/upperFirst.9d3c89a3.js";import"./js/Mobile.e6e7cfc6.js";import"./js/Cta.bce043d1.js";import"./js/GoogleSearchConsole.94b0d931.js";import"./js/ConnectCta.75b295db.js";import"./js/Graph.63f9e093.js";import"./js/numbers.9fc174f3.js";import"./js/vue3-apexcharts.b03ec956.js";import"./js/WpTable.1371581e.js";import"./js/Table.5c46c27d.js";import"./js/Download.7a5bbc25.js";import"./js/RequiredPlans.545990bc.js";import"./js/addons.90aa6c58.js";import"./js/PostTypes.dafa8837.js";import"./js/External.e551aa30.js";import"./js/InternalOutbound.73bb0872.js";import"./js/Image.54787c2f.js";import"./js/FacebookPreview.5f6acf17.js";import"./js/Img.633d5228.js";import"./js/Profile.a3a0dc3c.js";import"./js/ImageUploader.4aebc2ee.js";import"./js/TwitterPreview.e45bd2ee.js";import"./js/Book.5c6b4513.js";import"./js/Build.7bd177ef.js";import"./js/Redirects.9efef94e.js";import"./js/Index.7fe9a35c.js";import"./js/JsonValues.3fcfec97.js";import"./js/External.49bc8f29.js";import"./js/escapeRegExp.8deca77d.js";import"./js/Exclamation.a4801e64.js";import"./js/Gear.9759526d.js";import"./js/date.a0d85d51.js";import"./js/DatePicker.bbc70f84.js";import"./js/Calendar.ad6a53a7.js";import"./js/pick.f3b96ebd.js";import"./js/Card.b4864217.js";import"./js/Upsell.1257d7b5.js";import"./js/preload-helper.5f06849a.js";import"./js/vue-router.fd2bb90e.js";import"./js/Information.8cb16b63.js";import"./js/TableOfContentsStore.395f3f7c.js";const ee=()=>{let s=D().currentPost.postStatus;return B()&&(s=window.wp.data.select("core/editor").getCurrentPostAttribute("status")),s};class Re{constructor(){A(this,"previousPostSlug");A(this,"previousPostStatus");A(this,"updatingRedirects",!1);A(this,"update",Ce(()=>{const s=J(),c=ee();if(this.previousPostSlug===s&&this.previousPostStatus===c)return;this.previousPostSlug=s,this.previousPostStatus=c,this.updatingRedirects=!0,Se().getPostRedirects({}).finally(()=>{this.updatingRedirects=!1})},2500));const s=be(),c=ne(),t=s.addons.find(e=>e.sku==="aioseo-redirects");!c.aioseo.currentPost||!t||!t.isActive||c.aioseo.redirectsWatcherSet||(this.initWatchers(),c.aioseo.redirectsWatcherSet=!0)}initWatchers(){if(!he()&&B()){const s=window.setInterval(()=>{window.wp.data.select("core/editor").getCurrentPost().id&&(window.clearInterval(s),this.previousPostSlug=J(),this.previousPostStatus=ee(),this.watchBlockEditor())},50)}}watchBlockEditor(){window.wp.data.subscribe(()=>{this.updatingRedirects||this.update()})}}function $e(){var H,W,F,G,Y,Z,q,j;const n="all-in-one-seo-pack";if(!B()||!ae())return;const s=ne();if(s.aioseo.registerScoreTogglerSet)return;s.aioseo.registerScoreTogglerSet=!0;const c=window.wp.plugins.registerPlugin,t=((W=(H=window==null?void 0:window.wp)==null?void 0:H.editor)==null?void 0:W.PluginSidebarMoreMenuItem)||((G=(F=window.wp)==null?void 0:F.editPost)==null?void 0:G.PluginSidebarMoreMenuItem),e=((Z=(Y=window==null?void 0:window.wp)==null?void 0:Y.editor)==null?void 0:Z.PluginSidebar)||((j=(q=window.wp)==null?void 0:q.editPost)==null?void 0:j.PluginSidebar),d=window.wp.element.Fragment,r=window.wp.element.createElement,p=s.aioseo.user.capabilities.aioseo_page_analysis,x=D().currentPost.seo_score,y=o("N/A",n),C=function(T){return!p||!ye()?"score-disabled":79<T?"score-green":49<T?"score-orange":0<T?"score-red":"score-disabled"},b=r("svg",{width:24,height:24,fill:"none",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},r("path",{d:"M11.9811 23.7877C18.5428 23.7877 23.8623 18.4684 23.8623 11.9066C23.8623 5.34477 18.5428 0.0253906 11.9811 0.0253906C5.41924 0.0253906 0.0998535 5.34477 0.0998535 11.9066C0.0998535 18.4684 5.41924 23.7877 11.9811 23.7877ZM10.0892 4.37389C9.92824 4.12859 9.6301 4.01391 9.35674 4.11048C9.04535 4.22048 8.74079 4.34987 8.44488 4.49781C8.18513 4.6277 8.05479 4.92439 8.11199 5.21372L8.31571 6.24468C8.36815 6.51003 8.25986 6.77935 8.0543 6.95044C7.72937 7.22084 7.42944 7.52654 7.16069 7.86489C6.99366 8.07521 6.73011 8.18668 6.46987 8.13409L5.45923 7.92995C5.17534 7.87259 4.88492 8.00678 4.75864 8.27251C4.68731 8.42264 4.61997 8.57591 4.55683 8.73224C4.49369 8.88855 4.43564 9.04574 4.38258 9.20355C4.28872 9.4829 4.40211 9.78694 4.64318 9.95035L5.50129 10.5321C5.72226 10.6819 5.8323 10.9505 5.80561 11.2198C5.76265 11.6532 5.76441 12.0857 5.80825 12.5112C5.83598 12.7804 5.72684 13.0494 5.5064 13.2L4.64996 13.785C4.40958 13.9493 4.29718 14.2535 4.3918 14.5324C4.49961 14.8502 4.62641 15.1609 4.7714 15.4629C4.89868 15.728 5.18943 15.8609 5.47301 15.8026L6.48336 15.5947C6.7434 15.5412 7.00735 15.6517 7.17499 15.8615C7.43997 16.193 7.73956 16.499 8.07114 16.7733C8.27723 16.9437 8.38649 17.2127 8.33498 17.4782L8.13487 18.5095C8.07868 18.7992 8.2102 19.0955 8.47059 19.2244C8.61773 19.2971 8.76793 19.3659 8.92112 19.4303C9.07434 19.4947 9.22835 19.5539 9.38302 19.6081C9.83552 19.7664 10.4688 19.1996 10.937 18.7805C11.1679 18.5738 11.3103 18.2813 11.3119 17.9682C11.3119 17.9665 11.3119 17.9648 11.3119 17.9632V16.2386C11.3119 16.2204 11.3125 16.2022 11.3139 16.1843C9.93098 15.847 8.90283 14.5775 8.90283 13.0629V11.2317C8.90283 11.0925 9.01342 10.9797 9.14984 10.9797H10.0064V9.17798C10.0064 8.92921 10.204 8.72754 10.4478 8.72754C10.6916 8.72754 10.8892 8.92921 10.8892 9.17798V10.9797H13.2067V9.17798C13.2067 8.92921 13.4043 8.72754 13.6481 8.72754C13.8919 8.72754 14.0895 8.92921 14.0895 9.17798V10.9797H14.9461C15.0825 10.9797 15.193 11.0925 15.193 11.2317V13.0629C15.193 14.6253 14.0989 15.927 12.6497 16.2135C12.6501 16.2218 12.6502 16.2302 12.6502 16.2386V17.9557C12.6502 18.275 12.7969 18.5727 13.0347 18.7801C13.5113 19.1958 14.1555 19.7576 14.6053 19.5987C14.9167 19.4887 15.2213 19.3593 15.5172 19.2113C15.7769 19.0814 15.9073 18.7848 15.8501 18.4954L15.6464 17.4644C15.5939 17.1991 15.7022 16.9298 15.9078 16.7587C16.2327 16.4883 16.5326 16.1826 16.8013 15.8442C16.9684 15.634 17.2319 15.5225 17.4922 15.575L18.5028 15.7792C18.7867 15.8366 19.0771 15.7024 19.2034 15.4366C19.2747 15.2865 19.3421 15.1333 19.4052 14.9769C19.4683 14.8206 19.5264 14.6634 19.5795 14.5056C19.6733 14.2263 19.5599 13.9222 19.3189 13.7588L18.4607 13.177C18.2398 13.0272 18.1297 12.7586 18.1564 12.4893C18.1994 12.056 18.1976 11.6234 18.1538 11.1979C18.1261 10.9287 18.2352 10.6598 18.4556 10.5092L19.3121 9.92409C19.5525 9.75989 19.6649 9.45566 19.5702 9.17674C19.4624 8.85899 19.3356 8.5482 19.1907 8.24628C19.0634 7.98121 18.7726 7.84823 18.489 7.90657L17.4787 8.11444C17.2187 8.16796 16.9547 8.05746 16.7871 7.84769C16.5221 7.51615 16.2225 7.2101 15.8909 6.93588C15.6848 6.76543 15.5756 6.49649 15.6271 6.23094L15.8272 5.19968C15.8834 4.90999 15.7519 4.61365 15.4914 4.48481C15.3443 4.412 15.1941 4.34331 15.0409 4.27886C14.8877 4.21444 14.7337 4.1552 14.579 4.10107C14.3053 4.00526 14.0073 4.12099 13.8472 4.36697L13.277 5.24259C13.1302 5.46808 12.867 5.58035 12.6031 5.55312C12.1784 5.5093 11.7545 5.51109 11.3375 5.55581C11.0737 5.58411 10.8101 5.47276 10.6625 5.24782L10.0892 4.37389Z",fillRule:"evenodd",clipRule:"evenodd",fill:"#005AE0"})),h=r("div",{id:"aioseo-post-settings-sidebar-button",className:C(x)},b,r("span",{id:"aioseo-post-score-disabled"},y),r("span",{id:"aioseo-post-score"},x),r("span",{},"/100")),v=s.aioseo.user;c("aioseo-post-settings-sidebar",{render:function(){return!v.capabilities.aioseo_page_analysis&&!v.capabilities.aioseo_page_general_settings&&!v.capabilities.aioseo_page_social_settings&&!v.capabilities.aioseo_page_schema_settings&&!v.capabilities.aioseo_page_advanced_settings?null:r(d,{},r(t,{target:"aioseo-post-settings-sidebar",icon:b},"AIOSEO"),r(e,{name:"aioseo-post-settings-sidebar",icon:h,title:"AIOSEO"},r("div",{id:"aioseo-post-settings-sidebar",className:"aioseo-post-settings-sidebar"},r("div",{id:"aioseo-post-settings-sidebar-vue",className:"aioseo-post-settings-sidebar-vue"},r("div",{className:"aioseo-loading-spinner dark",style:{left:0,right:0,margin:"30px auto"}},r("div",{className:"double-bounce1"},null),r("div",{className:"double-bounce2"},null))))))}})}let P;const Me=()=>{if(B()){const{subscribe:n,select:s}=window.wp.data,c=s("core/editor");n(()=>{const t=c==null?void 0:c.getEditedPostAttribute("featured_media");(!P||P!==t)&&(K.emit("updateFeaturedImage",t),P=t)})}if(ve()||we()){const n=window.MutationObserver||window.WebKitMutationObserver,s=new n(()=>{var e;const t=(e=document.getElementById("_thumbnail_id"))==null?void 0:e.value;(!P||P!==t)&&(K.emit("updateFeaturedImage",t),P=t)}),c=document.getElementById("postimagediv");c&&s.observe(c,{subtree:!0,childList:!0})}},Ne=()=>{Me()},Ue="all-in-one-seo-pack",ze={data(){return{strings:{areaServedDescription:o("The geographic area where a service or offered item is provided.",Ue)}}}},We={class:"aioseo-col col-xs-12 text-xs-left"},Fe={class:"field-description"};function Ge(n,s,c,t,e,d){const r=l("base-input");return _(),f("div",We,[i(r,{type:"text",size:"medium"}),a("span",Fe,m(e.strings.areaServedDescription),1)])}const Ye=S(ze,[["render",Ge],["__scopeId","data-v-f926573a"]]),L="all-in-one-seo-pack",Ze={data(){return{COUNTRY_LIST:Ae,strings:{streetAddress:o("Address Line 1",L),streetAddress2:o("Address Line 2",L),zipCode:o("Zip Code",L),city:o("City",L),state:o("State",L),country:o("Country",L)}}}},qe={class:"columns field-row"},je={class:"aioseo-col col-xs-12 text-xs-left"},Ke={class:"field-description"},Je={class:"aioseo-col col-xs-12 text-xs-left"},Qe={class:"field-description mt-8"},Xe={class:"aioseo-address-wrapper"},es={class:"aioseo-col col-xs-12 col-sm-12 col-md-4 text-xs-left"},ss={class:"field-description mt-8"},os={class:"aioseo-col col-xs-12 col-sm-12 col-md-4 text-xs-left"},ts={class:"field-description mt-8"},ns={class:"aioseo-col col-xs-12 col-sm-12 col-md-4 text-xs-left"},is={class:"field-description mt-8"},as={class:"aioseo-col col-xs-12 col-sm-6 text-xs-left"},rs={class:"field-description mt-8"};function cs(n,s,c,t,e,d){const r=l("base-input"),p=l("base-select");return _(),f("div",qe,[a("div",je,[a("span",Ke,m(e.strings.streetAddress),1),i(r,{type:"text",size:"medium"})]),a("div",Je,[a("span",Qe,m(e.strings.streetAddress2),1),i(r,{type:"text",size:"medium"})]),a("div",Xe,[a("div",es,[a("span",ss,m(e.strings.zipCode),1),i(r,{type:"text",size:"medium"})]),a("div",os,[a("span",ts,m(e.strings.city),1),i(r,{type:"text",size:"medium"})]),a("div",ns,[a("span",is,m(e.strings.state),1),i(r,{type:"text",size:"medium"})])]),a("div",as,[a("span",rs,m(e.strings.country),1),i(p,{size:"medium",options:e.COUNTRY_LIST},null,8,["options"])])])}const ls=S(Ze,[["render",cs],["__scopeId","data-v-720b3124"]]),R="all-in-one-seo-pack",ds={components:{BasePhone:De},data(){return{strings:{emailAddress:o("Email Address",R),phoneNumber:o("Phone Number",R),faxNumber:o("Fax Number",R)}}}},ps={class:"aioseo-col col-xs-12 text-xs-left"},us={class:"field-description"},ms={class:"aioseo-col col-xs-12 text-xs-left"},_s={class:"field-description mt-8"},gs={class:"aioseo-col col-xs-12 text-xs-left"},fs={class:"field-description mt-8"};function bs(n,s,c,t,e,d){const r=l("base-input"),p=l("base-phone");return _(),f("div",null,[a("div",ps,[a("span",us,m(e.strings.emailAddress),1),i(r,{type:"text",size:"medium"})]),a("div",ms,[a("span",_s,m(e.strings.phoneNumber),1),i(p)]),a("div",gs,[a("span",fs,m(e.strings.faxNumber),1),i(p)])])}const hs=S(ds,[["render",bs],["__scopeId","data-v-17c98d44"]]),se="all-in-one-seo-pack",Ss={data(){return{strings:{vatID:o("VAT ID:",se),taxID:o("Tax ID:",se)}}}},vs={class:"aioseo-col col-xs-12 text-xs-left"},ws={class:"field-description"},xs={class:"aioseo-col col-xs-12 text-xs-left"},ys={class:"field-description mt-8"};function Cs(n,s,c,t,e,d){const r=l("base-input");return _(),f("div",null,[a("div",vs,[a("span",ws,m(e.strings.vatID),1),i(r,{type:"text",size:"medium"})]),a("div",xs,[a("span",ys,m(e.strings.taxID),1),i(r,{type:"text",size:"medium"})])])}const Ps=S(Ss,[["render",Cs],["__scopeId","data-v-462e3315"]]),Ls="all-in-one-seo-pack",Es={data(){return{LOCAL_SEO_BUSINESS_TYPES:Ie,strings:{businessType:o("Type",Ls)}}}};function As(n,s,c,t,e,d){const r=l("base-select");return _(),f("div",null,[i(r,{size:"large",options:e.LOCAL_SEO_BUSINESS_TYPES},null,8,["options"])])}const Is=S(Es,[["render",As]]),ks={};function Vs(n,s){return _(),f("div")}const Ds=S(ks,[["render",Vs]]),Hs={};function Os(n,s){return _(),f("div")}const Bs=S(Hs,[["render",Os]]),oe="all-in-one-seo-pack",Ts={data(){return{strings:{name:o("name",oe),nameDesc:o("Your name or company name.",oe)}}}},Rs={class:"aioseo-col col-xs-12 text-xs-left"},$s={class:"field-description"};function Ms(n,s,c,t,e,d){const r=l("base-input");return _(),f("div",Rs,[i(r,{type:"text",size:"medium"}),a("span",$s,m(e.strings.nameDesc),1)])}const Ns=S(Ts,[["render",Ms],["__scopeId","data-v-1b48195c"]]),$="all-in-one-seo-pack",Us={data(){return{currencies:ke,strings:{priceIndicator:o("Price Indicator",$),currenciesAccepted:o("Currencies Accepted",$),paymentMethods:o("Payment Methods Accepted",$)}}}},zs={class:"aioseo-col col-xs-12 text-xs-left"},Ws={class:"field-description"},Fs={class:"aioseo-col col-xs-12 text-xs-left"},Gs={class:"field-description mt-8"},Ys={class:"aioseo-col col-xs-12 text-xs-left"},Zs={class:"field-description mt-8"};function qs(n,s,c,t,e,d){const r=l("base-select"),p=l("base-input");return _(),f("div",null,[a("div",zs,[a("span",Ws,m(e.strings.priceIndicator),1),i(r,{size:"medium",options:e.currencies},null,8,["options"])]),a("div",Fs,[a("span",Gs,m(e.strings.currenciesAccepted),1),i(r,{size:"medium",label:"currenciesAccepted","track-by":"value",class:"aioseo-multiselect",options:e.currencies,multiple:""},null,8,["options"])]),a("div",Ys,[a("span",Zs,m(e.strings.paymentMethods),1),i(p,{type:"text",size:"medium"})])])}const js=S(Us,[["render",qs],["__scopeId","data-v-e89dcce9"]]),w="all-in-one-seo-pack",Ks={components:{CoreSettingsRow:re,LocalBusinessAreaServed:Ye,LocalBusinessBusinessAddress:ls,LocalBusinessBusinessContact:hs,LocalBusinessBusinessIds:Ps,LocalBusinessBusinessType:Is,LocalBusinessImage:Ds,LocalBusinessMap:Bs,LocalBusinessName:Ns,LocalBusinessPaymentInfo:js},data(){return{strings:{pageName:o("Business Info",w),name:o("Name",w),businessType:o("Type",w),image:o("Image",w),urls:o("URLs",w),websiteDesc:o("Website URL",w),aboutDesc:o("About Page URL",w),contactDesc:o("Contact Page URL",w),businessAddress:o("Address",w),businessContact:o("Contact Info",w),businessIDs:o("IDs",w),paymentInfo:o("Payment Info",w),areaServed:o("Area Served",w),map:o("Map",w)}}}},Js={class:"aioseo-tab-content aioseo-localseo-info"};function Qs(n,s,c,t,e,d){const r=l("local-business-name"),p=l("core-settings-row"),E=l("local-business-business-type"),x=l("local-business-image"),y=l("local-business-business-address"),C=l("local-business-map"),b=l("local-business-business-contact"),h=l("local-business-business-ids"),v=l("local-business-payment-info"),H=l("local-business-area-served");return _(),f("div",Js,[i(p,{name:e.strings.name,class:"info-name-row",align:""},{content:g(()=>[i(r)]),_:1},8,["name"]),i(p,{name:e.strings.businessType,class:"info-business-type",align:""},{content:g(()=>[i(E)]),_:1},8,["name"]),i(p,{class:"info-business-image",name:e.strings.image,align:""},{content:g(()=>[i(x)]),_:1},8,["name"]),i(p,{name:e.strings.businessAddress,class:"info-businessaddress-row",align:""},{content:g(()=>[i(y)]),_:1},8,["name"]),i(p,{name:e.strings.map,align:""},{content:g(()=>[i(C)]),_:1},8,["name"]),i(p,{name:e.strings.businessContact,class:"info-businesscontact-row",align:""},{content:g(()=>[i(b)]),_:1},8,["name"]),i(p,{name:e.strings.businessIDs,class:"info-businessids-row",align:""},{content:g(()=>[i(h)]),_:1},8,["name"]),i(p,{name:e.strings.paymentInfo,class:"info-paymentinfo-row",align:""},{content:g(()=>[i(v)]),_:1},8,["name"]),i(p,{name:e.strings.areaServed,class:"info-area-row",align:""},{content:g(()=>[i(H)]),_:1},8,["name"])])}const Xs=S(Ks,[["render",Qs]]),u="all-in-one-seo-pack",eo={setup(){return{postEditorStore:D(),GLOBAL_STRINGS:Ve,HOURS_12H_FORMAT:Q,HOURS_24H_FORMAT:X}},components:{BaseCheckbox:Oe,BaseRadioToggle:Be,CoreSettingsRow:re},data(){return{selectTimezone:[{value:"none",label:o("Select your timezone",u)}],strings:{pageName:o("Opening Hours",u),useDefaults:o("Use Defaults",u),useDefaultsDesc:o("Will default opening hours set globally",u),showOpeningHours:o("Show Opening Hours",u),labels:o("Labels",u),closedLabel:o("Closed",u),closedLabelDesc:o("Displayed when the business is closed.",u),closed:o("Closed",u),settings:o("Settings",u),open24h:o("Open 24h",u),open24Label:o("Open 24h",u),open24LabelDesc:o("Displayed when the business is open all day long.",u),alwaysOpen:o("Open 24/7",u),use24hFormat:o("Use 24h format",u),twoSets:o("I have two sets of opening hours per day",u),timezone:o("Timezone",u),hours:o("Hours",u)},weekdays:{monday:o("Monday",u),tuesday:o("Tuesday",u),wednesday:o("Wednesday",u),thursday:o("Thursday",u),friday:o("Friday",u),saturday:o("Saturday",u),sunday:o("Sunday",u)}}},computed:{toggled:function(){return!0},unToggled:function(){return!1},closedLabel:{get(){return this.postEditorStore.currentPost.local_seo.openingHours.closedLabel},set(n){this.postEditorStore.currentPost.local_seo.openingHours.closedLabel=n}}},methods:{getSelectOptions(n){return this.postEditorStore.currentPost.local_seo.openingHours.use24hFormat?X.find(s=>s.value===n):Q.find(s=>s.value===n)},saveDay(n,s,c){this.postEditorStore.currentPost.local_seo.openingHours.days[n][s]=c},getWeekDay(n){return this.postEditorStore.currentPost.local_seo.openingHours.days[n]}}},so={class:"aioseo-tab-content aioseo-localseo-opening"},oo={class:"aioseo-col col-xs-12 text-xs-left"},to={key:0},no={class:"aioseo-col col-xs-12 text-xs-left"},io={class:"aioseo-col col-xs-12 text-xs-left"},ao={class:"field-description"},ro={class:"field-description mt-10"},co={class:"aioseo-col col-xs-12 text-xs-left"},lo={class:"field-description mt-8"},po={class:"field-description mt-10"},uo={class:"aioseo-col col-xs-12 text-xs-left"},mo={class:"aioseo-col col-xs-12 text-xs-left"},_o={class:"aioseo-col col-xs-12 text-xs-left"},go={class:"aioseo-col-day text-xs-left"},fo={class:"aioseo-col-hours text-xs-left"},bo=a("span",{class:"separator"},"-",-1),ho={class:"aioseo-col-alwaysopen text-xs-left"};function So(n,s,c,t,e,d){const r=l("base-radio-toggle"),p=l("core-settings-row"),E=l("base-input"),x=l("base-toggle"),y=l("base-select"),C=l("base-checkbox");return _(),f("div",so,[i(p,{name:e.strings.useDefaults,align:""},{content:g(()=>[a("div",oo,[i(r,{name:"useDefaults",modelValue:t.postEditorStore.currentPost.local_seo.openingHours.useDefaults,"onUpdate:modelValue":s[0]||(s[0]=b=>t.postEditorStore.currentPost.local_seo.openingHours.useDefaults=b),options:[{label:t.GLOBAL_STRINGS.no,value:!1,activeClass:"dark"},{label:t.GLOBAL_STRINGS.yes,value:!0}]},null,8,["modelValue","options"])])]),_:1},8,["name"]),t.postEditorStore.currentPost.local_seo.openingHours.useDefaults?k("",!0):(_(),f("div",to,[i(p,{name:e.strings.showOpeningHours,class:"info-openinghours-row",align:""},{content:g(()=>[a("div",no,[i(r,{name:"openingHours",modelValue:t.postEditorStore.currentPost.local_seo.openingHours.show,"onUpdate:modelValue":s[1]||(s[1]=b=>t.postEditorStore.currentPost.local_seo.openingHours.show=b),options:[{label:t.GLOBAL_STRINGS.no,value:!1,activeClass:"dark"},{label:t.GLOBAL_STRINGS.yes,value:!0}]},null,8,["modelValue","options"])])]),_:1},8,["name"]),t.postEditorStore.currentPost.local_seo.openingHours.show?(_(),V(p,{key:0,name:e.strings.labels,class:"info-labels-row",align:""},{content:g(()=>[a("div",io,[a("span",ao,m(e.strings.closedLabel),1),i(E,{type:"text",size:"medium",modelValue:t.postEditorStore.currentPost.local_seo.openingHours.labels.closed,"onUpdate:modelValue":s[2]||(s[2]=b=>t.postEditorStore.currentPost.local_seo.openingHours.labels.closed=b)},null,8,["modelValue"]),a("span",ro,m(e.strings.closedLabelDesc),1)]),a("div",co,[a("span",lo,m(e.strings.open24Label),1),i(E,{size:"medium",modelValue:t.postEditorStore.currentPost.local_seo.openingHours.labels.alwaysOpen,"onUpdate:modelValue":s[3]||(s[3]=b=>t.postEditorStore.currentPost.local_seo.openingHours.labels.alwaysOpen=b)},null,8,["modelValue"]),a("span",po,m(e.strings.open24LabelDesc),1)])]),_:1},8,["name"])):k("",!0),t.postEditorStore.currentPost.local_seo.openingHours.show?(_(),V(p,{key:1,name:e.strings.settings,class:"info-settings-row",align:""},{content:g(()=>[a("div",uo,[i(x,{modelValue:t.postEditorStore.currentPost.local_seo.openingHours.alwaysOpen,"onUpdate:modelValue":s[4]||(s[4]=b=>t.postEditorStore.currentPost.local_seo.openingHours.alwaysOpen=b)},{default:g(()=>[O(m(e.strings.alwaysOpen),1)]),_:1},8,["modelValue"])]),a("div",mo,[i(x,{modelValue:t.postEditorStore.currentPost.local_seo.openingHours.use24hFormat,"onUpdate:modelValue":s[5]||(s[5]=b=>t.postEditorStore.currentPost.local_seo.openingHours.use24hFormat=b)},{default:g(()=>[O(m(e.strings.use24hFormat),1)]),_:1},8,["modelValue"])])]),_:1},8,["name"])):k("",!0),t.postEditorStore.currentPost.local_seo.openingHours.show&&!t.postEditorStore.currentPost.local_seo.openingHours.alwaysOpen?(_(),V(p,{key:2,name:e.strings.hours,class:"info-hours-row",align:""},{content:g(()=>[a("div",_o,[(_(!0),f(de,null,pe(e.weekdays,(b,h)=>(_(),f("div",{class:"aioseo-col-flex text-xs-left",key:h},[a("div",go,m(b),1),a("div",fo,[i(y,{disabled:d.getWeekDay(h).open24h||d.getWeekDay(h).closed,size:"medium",options:t.postEditorStore.currentPost.local_seo.openingHours.use24hFormat?t.HOURS_24H_FORMAT:t.HOURS_12H_FORMAT,modelValue:d.getSelectOptions(d.getWeekDay(h).openTime),"onUpdate:modelValue":v=>d.saveDay(h,"openTime",v.value)},null,8,["disabled","options","modelValue","onUpdate:modelValue"]),bo,i(y,{disabled:d.getWeekDay(h).open24h||d.getWeekDay(h).closed,size:"medium",options:t.postEditorStore.currentPost.local_seo.openingHours.use24hFormat?t.HOURS_24H_FORMAT:t.HOURS_12H_FORMAT,modelValue:d.getSelectOptions(d.getWeekDay(h).closeTime),"onUpdate:modelValue":v=>d.saveDay(h,"closeTime",v.value)},null,8,["disabled","options","modelValue","onUpdate:modelValue"])]),a("div",ho,[i(C,{disabled:d.getWeekDay(h).closed,size:"medium",modelValue:d.getWeekDay(h).open24h,"onUpdate:modelValue":v=>d.getWeekDay(h).open24h=v},{default:g(()=>[O(m(e.strings.open24h),1)]),_:2},1032,["disabled","modelValue","onUpdate:modelValue"]),i(C,{size:"medium",class:"closed-label",modelValue:d.getWeekDay(h).closed,"onUpdate:modelValue":v=>d.getWeekDay(h).closed=v},{default:g(()=>[O(m(e.strings.closed),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])])]))),128))])]),_:1},8,["name"])):k("",!0)]))])}const vo=S(eo,[["render",So]]),wo={};function xo(n,s){return _(),f("div")}const yo=S(wo,[["render",xo]]),Co={};function Po(n,s){return _(),f("div")}const Lo=S(Co,[["render",Po]]),Eo={components:{LocalBusinessMapCustomMarker:yo,LocalBusinessMapDefaultStyle:Lo},data(){return{strings:{}}}},Ao={class:"aioseo-tab-content aioseo-localseo-maps"};function Io(n,s,c,t,e,d){const r=l("local-business-map-default-style"),p=l("local-business-map-custom-marker");return _(),f("div",Ao,[i(r),i(p)])}const ko=S(Eo,[["render",Io]]),M="all-in-one-seo-pack",Vo={setup(){return{postEditorStore:D()}},components:{BusinessInfo:Xs,CoreMainTabs:He,OpeningHours:vo,Maps:ko,SvgSettings:Te},data(){return{tab:"business-info",tabs:[{slug:"business-info",icon:"svg-settings",name:o("Business Info",M)},{slug:"opening-hours",icon:"svg-settings",name:o("Opening Hours",M)},{slug:"maps",icon:"svg-settings",name:o("Maps",M)}]}},watch:{"postEditorStore.currentPost":{deep:!0,handler(){Ee(this.postEditorStore.savePostState,250)}}},methods:{processChangeTab(n){this.tab=n}}},Do={class:"aioseo-app aioseo-post-settings"};function Ho(n,s,c,t,e,d){const r=l("core-main-tabs");return _(),f("div",Do,[i(r,{tabs:e.tabs,showSaveButton:!1,active:e.tab,internal:"",disableMobile:"",onChanged:s[0]||(s[0]=p=>d.processChangeTab(p))},null,8,["tabs","active"]),i(me,{name:"route-fade",mode:"out-in"},{default:g(()=>[(_(),V(ue(e.tab)))]),_:1})])}const Oo=S(Vo,[["render",Ho]]),Bo={setup(){return{postEditorStore:D()}},components:{"main-view":Oo}};function To(n,s,c,t,e,d){const r=l("main-view");return _(),f("div",null,[t.postEditorStore.currentPost.id?(_(),V(r,{key:0})):k("",!0)])}const Ro=S(Bo,[["render",To]]);Ne();const z=n=>(n=_e(n),n=ge(n),n=fe(n),xe(n),new Re,$e(),window.addEventListener("load",()=>Le()),n);let I;const te=()=>{I&&I.unmount(),I=N({name:"Standalone/PostSettings/Sidebar",data(){return{tableContext:"post",screenContext:"sidebar"}},render:()=>U(ie)}),window.aioseo.headlineAnalyzerSidebarApp&&window.aioseo.headlineAnalyzerSidebarApp.unmount(),z(I).mount("#aioseo-post-settings-sidebar-vue"),window.aioseo.postSettingsSidebarApp=I};if(window.aioseo.currentPost){const n=window.aioseo.currentPost.context;document.querySelector(`#aioseo-${n}-settings-metabox`)&&ae()&&(!window.wp.blockEditor&&window.wp.blocks&&window.wp.oldEditor&&(window.wp.blockEditor=window.wp.editor),z(N({name:"Standalone/PostSettings/Metabox",data(){return{tableContext:n,screenContext:"metabox"}},render:()=>U(ie)})).mount(`#aioseo-${n}-settings-metabox`),n==="post"&&(document.getElementById("aioseo-post-settings-sidebar-vue")?te():(Pe("#aioseo-post-settings-sidebar-vue","aioseoSidebarVisible"),document.addEventListener("animationstart",function(t){t.animationName==="aioseoSidebarVisible"&&te()},{passive:!0}))))}window.aioseo.currentPost&&window.aioseo.localBusiness&&document.getElementById("aioseo-location-settings-metabox")&&z(N({name:"Standalone/LocalSeo/Metabox",data(){return{screenContext:"metabox"}},render:()=>U(Ro)})).mount("#aioseo-location-settings-metabox");
