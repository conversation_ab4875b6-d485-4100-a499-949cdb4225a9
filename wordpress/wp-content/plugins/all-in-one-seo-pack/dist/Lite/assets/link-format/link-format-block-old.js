/*! For license information please see link-format-block-old.js.LICENSE.txt */
(()=>{var t={184:(t,e)=>{var o;!function(){"use strict";var n={}.hasOwnProperty;function s(){for(var t=[],e=0;e<arguments.length;e++){var o=arguments[e];if(o){var i=typeof o;if("string"===i||"number"===i)t.push(o);else if(Array.isArray(o)&&o.length){var r=s.apply(null,o);r&&t.push(r)}else if("object"===i)for(var l in o)n.call(o,l)&&o[l]&&t.push(l)}}return t.join(" ")}t.exports?(s.default=s,t.exports=s):void 0===(o=function(){return s}.apply(e,[]))||(t.exports=o)}()}},e={};function o(n){var s=e[n];if(void 0!==s)return s.exports;var i=e[n]={exports:{}};return t[n](i,i.exports,o),i.exports}o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var n in e)o.o(e,n)&&!o.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),(()=>{"use strict";const t=window.wp.element,e=window.lodash,n=window.wp,{getProtocol:s,isValidProtocol:i,getAuthority:r,isValidAuthority:l,getPath:a,isValidPath:c,getQueryString:u,isValidQueryString:p,getFragment:d,isValidFragment:h}=n.url,{__:g,sprintf:f}=n.i18n;function m(t){if(!t)return!1;const o=t.trim();if(!o)return!1;if(/^\S+:/.test(o)){const t=s(o);if(!i(t))return!1;if((0,e.startsWith)(t,"http")&&!/^https?:\/\/[^\/\s]/i.test(o))return!1;const n=r(o);if(!l(n))return!1;const g=a(o);if(g&&!c(g))return!1;const f=u(o);if(f&&!p(f))return!1;const m=d(o);if(m&&!h(m))return!1}return!((0,e.startsWith)(o,"#")&&!h(o))}function w({url:t,opensInNewWindow:e,noFollow:o,sponsored:n,ugc:s,text:i}){const r={type:"core/link",attributes:{url:t}},l=[];if(e){const t=f(g("%1$s (opens in a new tab)","all-in-one-seo-pack"),i);r.attributes.target="_blank",r.attributes["aria-label"]=t,l.push("noopener")}return o&&l.push("nofollow"),n&&l.push("sponsored"),s&&l.push("ugc"),0<l.length&&(r.attributes.rel=l.join(" ")),r}const{Component:v}=wp.element,{getOffsetParent:b,getRectangleFromRange:k}=wp.dom;function y(){const t=window.getSelection();if(0===t.rangeCount)return{};const e=k(t.getRangeAt(0));let o=e.top+e.height,n=e.left+e.width/2;const s=b(t.anchorNode);if(s){const t=s.getBoundingClientRect();o-=t.top,n-=t.left}return{top:o,left:n}}const S=class extends v{constructor(){super(...arguments),this.state={style:y()}}render(){const{children:e}=this.props,{style:o}=this.state;return(0,t.createElement)("div",{className:"editor-format-toolbar__selection-position",style:o},e)}};var L=o(184),E=o.n(L);function C(t){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},C(t)}function N(t,e,o){return e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}function R(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,n)}return o}function F(t,e){var o=t["page".concat(e?"Y":"X","Offset")],n="scroll".concat(e?"Top":"Left");if("number"!=typeof o){var s=t.document;"number"!=typeof(o=s.documentElement[n])&&(o=s.body[n])}return o}function T(t){return F(t)}function _(t){return F(t,!0)}function O(t){var e=function(t){var e,o,n,s=t.ownerDocument,i=s.body,r=s&&s.documentElement;return o=(e=t.getBoundingClientRect()).left,n=e.top,{left:o-=r.clientLeft||i.clientLeft||0,top:n-=r.clientTop||i.clientTop||0}}(t),o=t.ownerDocument,n=o.defaultView||o.parentWindow;return e.left+=T(n),e.top+=_(n),e}var P,x=new RegExp("^(".concat(/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source,")(?!px)[a-z%]+$"),"i"),W=/^(top|right|bottom|left)$/,A="currentStyle",I="runtimeStyle",D="left";function V(t,e){for(var o=0;o<t.length;o++)e(t[o])}function U(t){return"border-box"===P(t,"boxSizing")}"undefined"!=typeof window&&(P=window.getComputedStyle?function(t,e,o){var n="",s=t.ownerDocument,i=o||s.defaultView.getComputedStyle(t,null);return i&&(n=i.getPropertyValue(e)||i[e]),n}:function(t,e){var o=t[A]&&t[A][e];if(x.test(o)&&!W.test(e)){var n=t.style,s=n[D],i=t[I][D];t[I][D]=t[A][D],n[D]="fontSize"===e?"1em":o||0,o=n.pixelLeft+"px",n[D]=s,t[I][D]=i}return""===o?"auto":o});var j=["margin","border","padding"],M=-1,$=2,K=1;function B(t,e,o){var n,s,i,r=0;for(s=0;s<e.length;s++)if(n=e[s])for(i=0;i<o.length;i++){var l;l="border"===n?"".concat(n+o[i],"Width"):n+o[i],r+=parseFloat(P(t,l))||0}return r}function H(t){return null!=t&&t==t.window}var z={};function q(t,e,o){if(H(t))return"width"===e?z.viewportWidth(t):z.viewportHeight(t);if(9===t.nodeType)return"width"===e?z.docWidth(t):z.docHeight(t);var n="width"===e?["Left","Right"]:["Top","Bottom"],s="width"===e?t.offsetWidth:t.offsetHeight,i=(P(t),U(t)),r=0;(null==s||s<=0)&&(s=void 0,(null==(r=P(t,e))||Number(r)<0)&&(r=t.style[e]||0),r=parseFloat(r)||0),void 0===o&&(o=i?K:M);var l=void 0!==s||i,a=s||r;if(o===M)return l?a-B(t,["border","padding"],n):r;if(l){var c=o===$?-B(t,["border"],n):B(t,["margin"],n);return a+(o===K?0:c)}return r+B(t,j.slice(o),n)}V(["Width","Height"],(function(t){z["doc".concat(t)]=function(e){var o=e.document;return Math.max(o.documentElement["scroll".concat(t)],o.body["scroll".concat(t)],z["viewport".concat(t)](o))},z["viewport".concat(t)]=function(e){var o="client".concat(t),n=e.document,s=n.body,i=n.documentElement[o];return"CSS1Compat"===n.compatMode&&i||s&&s[o]||i}}));var Q={position:"absolute",visibility:"hidden",display:"block"};function G(t){var e,o=arguments;return 0!==t.offsetWidth?e=q.apply(void 0,o):function(t,n,s){var i,r={},l=t.style;for(i in n)n.hasOwnProperty(i)&&(r[i]=l[i],l[i]=n[i]);for(i in function(){e=q.apply(void 0,o)}.call(t),n)n.hasOwnProperty(i)&&(l[i]=r[i])}(t,Q),e}function X(t,e,o){var n=o;if("object"!==C(e))return void 0!==n?("number"==typeof n&&(n+="px"),void(t.style[e]=n)):P(t,e);for(var s in e)e.hasOwnProperty(s)&&X(t,s,e[s])}V(["width","height"],(function(t){var e=t.charAt(0).toUpperCase()+t.slice(1);z["outer".concat(e)]=function(e,o){return e&&G(e,t,o?0:K)};var o="width"===t?["Left","Right"]:["Top","Bottom"];z[t]=function(e,n){return void 0===n?e&&G(e,t,M):e?(P(e),U(e)&&(n+=B(e,["padding","border"],o)),X(e,t,n)):void 0}}));var Y=function(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?R(o,!0).forEach((function(e){N(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):R(o).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}({getWindow:function(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow},offset:function(t,e){if(void 0===e)return O(t);!function(t,e){"static"===X(t,"position")&&(t.style.position="relative");var o,n,s=O(t),i={};for(n in e)e.hasOwnProperty(n)&&(o=parseFloat(X(t,n))||0,i[n]=o+e[n]-s[n]);X(t,i)}(t,e)},isWindow:H,each:V,css:X,clone:function(t){var e={};for(var o in t)t.hasOwnProperty(o)&&(e[o]=t[o]);if(t.overflow)for(var n in t)t.hasOwnProperty(n)&&(e.overflow[n]=t.overflow[n]);return e},scrollLeft:function(t,e){if(H(t)){if(void 0===e)return T(t);window.scrollTo(e,_(t))}else{if(void 0===e)return t.scrollLeft;t.scrollLeft=e}},scrollTop:function(t,e){if(H(t)){if(void 0===e)return _(t);window.scrollTo(T(t),e)}else{if(void 0===e)return t.scrollTop;t.scrollTop=e}},viewportWidth:0,viewportHeight:0},z);const J=window.wp,{__:Z,sprintf:tt,_n:et}=J.i18n,{Component:ot,createRef:nt}=J.element,{decodeEntities:st}=J.htmlEntities,{UP:it,DOWN:rt,ENTER:lt,TAB:at}=J.keycodes,{Spinner:ct,withSpokenMessages:ut,Popover:pt}=J.components,{withInstanceId:dt}=J.compose,ht=J.apiFetch,{addQueryArgs:gt}=J.url,ft=t=>t.stopPropagation(),mt=ut(dt(class extends ot{constructor({autocompleteRef:t}){super(...arguments),this.onChange=this.onChange.bind(this),this.onKeyDown=this.onKeyDown.bind(this),this.autocompleteRef=t||nt(),this.inputRef=nt(),this.updateSuggestions=(0,e.throttle)(this.updateSuggestions.bind(this),200),this.suggestionNodes=[],this.state={posts:[],showSuggestions:!1,selectedSuggestion:null}}componentDidUpdate(){const{showSuggestions:t,selectedSuggestion:e}=this.state;t&&null!==e&&!this.scrollingIntoView&&(this.scrollingIntoView=!0,function(t,e,o){o=o||{},9===e.nodeType&&(e=Y.getWindow(e));var n=o.allowHorizontalScroll,s=o.onlyScrollIfNeeded,i=o.alignWithTop,r=o.alignWithLeft,l=o.offsetTop||0,a=o.offsetLeft||0,c=o.offsetBottom||0,u=o.offsetRight||0;n=void 0===n||n;var p,d,h,g,f,m,w,v,b,k,y=Y.isWindow(e),S=Y.offset(t),L=Y.outerHeight(t),E=Y.outerWidth(t);y?(w=e,k=Y.height(w),b=Y.width(w),v={left:Y.scrollLeft(w),top:Y.scrollTop(w)},f={left:S.left-v.left-a,top:S.top-v.top-l},m={left:S.left+E-(v.left+b)+u,top:S.top+L-(v.top+k)+c},g=v):(p=Y.offset(e),d=e.clientHeight,h=e.clientWidth,g={left:e.scrollLeft,top:e.scrollTop},f={left:S.left-(p.left+(parseFloat(Y.css(e,"borderLeftWidth"))||0))-a,top:S.top-(p.top+(parseFloat(Y.css(e,"borderTopWidth"))||0))-l},m={left:S.left+E-(p.left+h+(parseFloat(Y.css(e,"borderRightWidth"))||0))+u,top:S.top+L-(p.top+d+(parseFloat(Y.css(e,"borderBottomWidth"))||0))+c}),f.top<0||m.top>0?!0===i?Y.scrollTop(e,g.top+f.top):!1===i?Y.scrollTop(e,g.top+m.top):f.top<0?Y.scrollTop(e,g.top+f.top):Y.scrollTop(e,g.top+m.top):s||((i=void 0===i||!!i)?Y.scrollTop(e,g.top+f.top):Y.scrollTop(e,g.top+m.top)),n&&(f.left<0||m.left>0?!0===r?Y.scrollLeft(e,g.left+f.left):!1===r?Y.scrollLeft(e,g.left+m.left):f.left<0?Y.scrollLeft(e,g.left+f.left):Y.scrollLeft(e,g.left+m.left):s||((r=void 0===r||!!r)?Y.scrollLeft(e,g.left+f.left):Y.scrollLeft(e,g.left+m.left)))}(this.suggestionNodes[e],this.autocompleteRef.current,{onlyScrollIfNeeded:!0}),setTimeout((()=>{this.scrollingIntoView=!1}),100))}componentWillUnmount(){delete this.suggestionsRequest}bindSuggestionNode(t){return e=>{this.suggestionNodes[t]=e}}updateSuggestions(t){if(2>t.length||/^https?:/.test(t))return void this.setState({showSuggestions:!1,selectedSuggestion:null,loading:!1});this.setState({showSuggestions:!0,selectedSuggestion:null,loading:!0});const e=ht({path:gt("/wp/v2/search",{search:t,per_page:20,type:"post"})});e.then((t=>{this.suggestionsRequest===e&&(this.setState({posts:t,loading:!1}),t.length?this.props.debouncedSpeak(tt(et("%1$d result found, use up and down arrow keys to navigate.","%1$d results found, use up and down arrow keys to navigate.",t.length),t.length),"assertive"):this.props.debouncedSpeak(Z("No results.","all-in-one-seo-pack"),"assertive"))})).catch((()=>{this.suggestionsRequest===e&&this.setState({loading:!1})})),this.suggestionsRequest=e}onChange(t){const e=t.target.value;this.props.onChange(e),this.updateSuggestions(e)}onKeyDown(t){const{showSuggestions:e,selectedSuggestion:o,posts:n,loading:s}=this.state;if(!e||!n.length||s){switch(t.keyCode){case it:0!==t.target.selectionStart&&(t.stopPropagation(),t.preventDefault(),t.target.setSelectionRange(0,0));break;case rt:this.props.value.length!==t.target.selectionStart&&(t.stopPropagation(),t.preventDefault(),t.target.setSelectionRange(this.props.value.length,this.props.value.length))}return}const i=this.state.posts[this.state.selectedSuggestion];switch(t.keyCode){case it:{t.stopPropagation(),t.preventDefault();const e=o?o-1:n.length-1;this.setState({selectedSuggestion:e});break}case rt:{t.stopPropagation(),t.preventDefault();const e=null===o||o===n.length-1?0:o+1;this.setState({selectedSuggestion:e});break}case at:null!==this.state.selectedSuggestion&&(this.selectLink(i),this.props.speak(Z("Link selected.","all-in-one-seo-pack")));break;case lt:null!==this.state.selectedSuggestion&&(t.stopPropagation(),this.selectLink(i))}}selectLink(t){this.props.onChange(t.url,t),this.setState({selectedSuggestion:null,showSuggestions:!1})}handleOnClick(t){this.selectLink(t),this.inputRef.current.focus()}render(){const{value:e="",autoFocus:o=!0,instanceId:n,className:s}=this.props,{showSuggestions:i,posts:r,selectedSuggestion:l,loading:a}=this.state;return(0,t.createElement)("div",{className:E()("editor-url-input block-editor-url-input",s)},(0,t.createElement)("input",{autoFocus:o,type:"text","aria-label":Z("URL","all-in-one-seo-pack"),required:!0,value:e,onChange:this.onChange,onInput:ft,placeholder:Z("Paste URL or type to search","all-in-one-seo-pack"),onKeyDown:this.onKeyDown,role:"combobox","aria-expanded":i,"aria-autocomplete":"list","aria-owns":`editor-url-input-suggestions-${n}`,"aria-activedescendant":null!==l?`editor-url-input-suggestion-${n}-${l}`:void 0,ref:this.inputRef}),a&&(0,t.createElement)(ct,null),i&&!!r.length&&(0,t.createElement)(pt,{position:"bottom",noArrow:!0,focusOnMount:!1},(0,t.createElement)("div",{className:E()("editor-url-input__suggestions","block-editor-url-input__suggestions",`${s}__suggestions`),id:`editor-url-input-suggestions-${n}`,ref:this.autocompleteRef,role:"listbox"},r.map(((e,o)=>(0,t.createElement)("button",{key:e.id,role:"option",tabIndex:"-1",id:`editor-url-input-suggestion-${n}-${o}`,ref:this.bindSuggestionNode(o),className:E()("editor-url-input__suggestion block-editor-url-input__suggestion",{"is-selected":o===l}),onClick:()=>this.handleOnClick(e),"aria-selected":o===l},st(e.title)||Z("(no title)")))))))}})),wt=window.wp,{__:vt}=wt.i18n,{IconButton:bt}=wt.components;function kt({autocompleteRef:e,className:o,onChangeInputValue:n,value:s,...i}){return(0,t.createElement)("form",{className:E()("block-editor-url-popover__link-editor",o),...i},(0,t.createElement)(mt,{value:s,onChange:n,autocompleteRef:e}),(0,t.createElement)(bt,{icon:"editor-break",label:vt("Apply","all-in-one-seo-pack"),type:"submit"}))}const yt=window.wp,{__:St}=yt.i18n,{ExternalLink:Lt,IconButton:Et}=yt.components,{safeDecodeURI:Ct,filterURLForDisplay:Nt}=yt.url;function Rt({url:e,urlLabel:o,className:n}){const s=E()(n,"block-editor-url-popover__link-viewer-url");return e?(0,t.createElement)(Lt,{className:s,href:e},o||Nt(Ct(e))):(0,t.createElement)("span",{className:s})}function Ft({className:e,linkClassName:o,onEditLinkClick:n,url:s,urlLabel:i,...r}){return(0,t.createElement)("div",{className:E()("block-editor-url-popover__link-viewer",e),...r},(0,t.createElement)(Rt,{url:s,urlLabel:i,className:o}),n&&(0,t.createElement)(Et,{icon:"edit",label:St("Edit","all-in-one-seo-pack"),onClick:n}))}const Tt=window.wp,{__:_t}=Tt.i18n,{Component:Ot,createRef:Pt,useMemo:xt,Fragment:Wt}=Tt.element,{ToggleControl:At,withSpokenMessages:It}=Tt.components,{LEFT:Dt,RIGHT:Vt,UP:Ut,DOWN:jt,BACKSPACE:Mt,ENTER:$t,ESCAPE:Kt}=Tt.keycodes,{getRectangleFromRange:Bt}=Tt.dom,{prependHTTP:Ht}=Tt.url,{create:zt,insert:qt,isCollapsed:Qt,applyFormat:Gt,getTextContent:Xt,slice:Yt}=Tt.richText,{URLPopover:Jt}=Tt.blockEditor,Zt=t=>t.stopPropagation();function te(t,e){return t.addingLink||e.editLink}const ee=({isActive:e,addingLink:o,value:n,resetOnMount:s,...i})=>{const r=xt((()=>{const t=window.getSelection(),e=0<t.rangeCount?t.getRangeAt(0):null;if(!e)return;if(o)return Bt(e);let n=e.startContainer;for(n=n.nextElementSibling||n;n.nodeType!==window.Node.ELEMENT_NODE;)n=n.parentNode;const s=n.closest("a");return s?s.getBoundingClientRect():void 0}),[e,o,n.start,n.end]);return r?(s(r),(0,t.createElement)(Jt,{anchorRect:r,...i})):null},oe=It(class extends Ot{constructor(){super(...arguments),this.editLink=this.editLink.bind(this),this.submitLink=this.submitLink.bind(this),this.onKeyDown=this.onKeyDown.bind(this),this.onChangeInputValue=this.onChangeInputValue.bind(this),this.setLinkTarget=this.setLinkTarget.bind(this),this.setNoFollow=this.setNoFollow.bind(this),this.setSponsored=this.setSponsored.bind(this),this.setUgc=this.setUgc.bind(this),this.onFocusOutside=this.onFocusOutside.bind(this),this.resetState=this.resetState.bind(this),this.autocompleteRef=Pt(),this.resetOnMount=this.resetOnMount.bind(this),this.state={opensInNewWindow:!1,noFollow:!1,sponsored:!1,ugc:!1,inputValue:"",anchorRect:!1}}static getDerivedStateFromProps(t,e){const{activeAttributes:{url:o,target:n,rel:s}}=t,i="_blank"===n,r={};if(!te(t,e)&&(o!==e.inputValue&&(r.inputValue=o),i!==e.opensInNewWindow&&(r.opensInNewWindow=i),"string"==typeof s)){const t=s.split(" ").includes("nofollow"),o=s.split(" ").includes("sponsored"),n=s.split(" ").includes("ugc");t!==e.noFollow&&(r.noFollow=t),o!==e.sponsored&&(r.sponsored=o),n!==e.ugc&&(r.ugc=n)}return r}onKeyDown(t){-1<[Dt,jt,Vt,Ut,Mt,$t].indexOf(t.keyCode)&&t.stopPropagation(),-1<[Kt].indexOf(t.keyCode)&&this.resetState()}onChangeInputValue(t){this.setState({inputValue:t})}setLinkTarget(t){const{activeAttributes:{url:e=""},value:o,onChange:n}=this.props;if(this.setState({opensInNewWindow:t}),!te(this.props,this.state)){const s=Xt(Yt(o));n(Gt(o,w({url:e,opensInNewWindow:t,noFollow:this.state.noFollow,sponsored:this.state.sponsored,ugc:this.state.ugc,text:s})))}}setNoFollow(t){const{activeAttributes:{url:e=""},value:o,onChange:n}=this.props;if(this.setState({noFollow:t}),!te(this.props,this.state)){const s=Xt(Yt(o));n(Gt(o,w({url:e,opensInNewWindow:this.state.opensInNewWindow,noFollow:t,sponsored:this.state.sponsored,ugc:this.state.ugc,text:s})))}}setSponsored(t){const{activeAttributes:{url:e=""},value:o,onChange:n}=this.props;if(this.setState({sponsored:t}),!te(this.props,this.state)){const s=Xt(Yt(o));n(Gt(o,w({url:e,opensInNewWindow:this.state.opensInNewWindow,noFollow:this.state.noFollow,sponsored:t,ugc:this.state.ugc,text:s})))}}setUgc(t){const{activeAttributes:{url:e=""},value:o,onChange:n}=this.props;if(this.setState({ugc:t}),!te(this.props,this.state)){const s=Xt(Yt(o));n(Gt(o,w({url:e,opensInNewWindow:this.state.opensInNewWindow,noFollow:this.state.noFollow,sponsored:this.state.sponsored,ugc:t,text:s})))}}editLink(t){this.setState({editLink:!0}),t.preventDefault()}submitLink(t){const{isActive:e,value:o,onChange:n,speak:s}=this.props,{inputValue:i,opensInNewWindow:r,noFollow:l,sponsored:a,ugc:c}=this.state,u=Ht(i),p=w({url:u,opensInNewWindow:r,noFollow:l,sponsored:a,ugc:c,text:Xt(Yt(o))});if(t.preventDefault(),Qt(o)&&!e){const t=Gt(zt({text:u}),p,0,u.length);n(qt(o,t))}else n(Gt(o,p));this.resetState(),m(u)?s(_t(e?"Link edited.":"Link inserted.","all-in-one-seo-pack"),"assertive"):s(_t("Warning: the link has been inserted but could have errors. Please test it.","all-in-one-seo-pack"),"assertive")}onFocusOutside(){const t=this.autocompleteRef.current;t&&t.contains(event.target)||this.resetState()}resetState(){this.props.stopAddingLink(),this.setState({editLink:!1})}resetOnMount(t){this.state.anchorRect!==t&&this.setState({opensInNewWindow:!1,noFollow:!1,sponsored:!1,ugc:!1,anchorRect:t})}render(){const{isActive:e,activeAttributes:{url:o},addingLink:n,value:s}=this.props;if(!e&&!n)return null;const{inputValue:i,opensInNewWindow:r,noFollow:l,sponsored:a,ugc:c}=this.state,u=te(this.props,this.state);return(0,t.createElement)(S,{key:`${s.start}${s.end}`},(0,t.createElement)(ee,{resetOnMount:this.resetOnMount,value:s,isActive:e,addingLink:n,onFocusOutside:this.onFocusOutside,onClose:()=>{i||this.resetState()},focusOnMount:!!u&&"firstElement",renderSettings:()=>(0,t.createElement)(Wt,null,(0,t.createElement)(At,{label:_t("Open in New Tab","all-in-one-seo-pack"),checked:r,onChange:this.setLinkTarget}),(0,t.createElement)(At,{label:_t('Add "nofollow" to link',"all-in-one-seo-pack"),checked:l,onChange:this.setNoFollow}),(0,t.createElement)(At,{label:_t('Add "sponsored" to link',"all-in-one-seo-pack"),checked:a,onChange:this.setSponsored}),(0,t.createElement)(At,{label:_t('Add "ugc" to link',"all-in-one-seo-pack"),checked:c,onChange:this.setUgc}))},u?(0,t.createElement)(kt,{className:"editor-format-toolbar__link-container-content block-editor-format-toolbar__link-container-content",value:i,onChangeInputValue:this.onChangeInputValue,onKeyDown:this.onKeyDown,onKeyPress:Zt,onSubmit:this.submitLink,autocompleteRef:this.autocompleteRef}):(0,t.createElement)(Ft,{className:"editor-format-toolbar__link-container-content block-editor-format-toolbar__link-container-content",onKeyPress:Zt,url:o,onEditLinkClick:this.editLink,linkClassName:m(Ht(o))?void 0:"has-invalid-link"})))}}),ne=window.wp,{__:se}=ne.i18n,{Component:ie,Fragment:re}=ne.element,{select:le,withSelect:ae}=ne.data,{BlockControls:ce,RichTextToolbarButton:ue,RichTextShortcut:pe}=ne.blockEditor,{getTextContent:de,applyFormat:he,removeFormat:ge,slice:fe}=ne.richText,{isURL:me}=ne.url,{Toolbar:we,withSpokenMessages:ve}=ne.components,{compose:be,ifCondition:ke}=ne.compose,ye="core/link",Se=se("Add Link","all-in-one-seo-pack"),Le=/^(mailto:)?[a-z0-9._%+-]+@[a-z0-9][a-z0-9.-]*\.[a-z]{2,63}$/i,Ee=be(ae((()=>({isDisabled:le("core/edit-post").isFeatureActive("disableEditorsKitLinkFormats")}))),ke((t=>!t.isDisabled)),ve)(class extends ie{constructor(){super(...arguments),this.isEmail=this.isEmail.bind(this),this.addLink=this.addLink.bind(this),this.stopAddingLink=this.stopAddingLink.bind(this),this.onRemoveFormat=this.onRemoveFormat.bind(this),this.state={addingLink:!1}}isEmail(t){return Le.test(t)}addLink(){const{value:t,onChange:e}=this.props,o=de(fe(t));o&&me(o)?e(he(t,{type:ye,attributes:{url:o}})):o&&this.isEmail(o)?e(he(t,{type:ye,attributes:{url:`mailto:${o}`}})):this.setState({addingLink:!0})}stopAddingLink(){this.setState({addingLink:!1})}onRemoveFormat(){const{value:t,onChange:o,speak:n}=this.props;let s=t;(0,e.map)(["core/link"],(t=>{s=ge(s,t)})),o({...s}),n(se("Link removed.","all-in-one-seo-pack"),"assertive")}render(){const{activeAttributes:e,onChange:o}=this.props,{isActive:n,value:s}=this.props;return(0,t.createElement)(re,null,(0,t.createElement)(ce,null,(0,t.createElement)(we,{className:"editorskit-components-toolbar"},(0,t.createElement)(pe,{type:"primary",character:"k",onUse:this.addLink}),(0,t.createElement)(pe,{type:"primaryShift",character:"k",onUse:this.onRemoveFormat}),n&&(0,t.createElement)(ue,{name:"link",icon:"editor-unlink",title:se("Unlink","all-in-one-seo-pack"),onClick:this.onRemoveFormat,isActive:n,shortcutType:"primaryShift",shortcutCharacter:"k"}),!n&&(0,t.createElement)(ue,{name:"link",icon:"admin-links",title:Se,onClick:this.addLink,isActive:n,shortcutType:"primary",shortcutCharacter:"k"}),(0,t.createElement)(oe,{addingLink:this.state.addingLink,stopAddingLink:this.stopAddingLink,isActive:n,activeAttributes:e,value:s,onChange:o,contentRef:this.props.contentRef}))))}}),{__:Ce}=wp.i18n,{registerFormatType:Ne,unregisterFormatType:Re,applyFormat:Fe,isCollapsed:Te}=wp.richText,{decodeEntities:_e}=wp.htmlEntities,{isURL:Oe}=wp.url,Pe="core/link";[{name:Pe,title:Ce("Link","all-in-one-seo-pack"),tagName:"a",className:null,attributes:{url:"href",target:"target",rel:"rel"},__unstablePasteRule(t,{html:e,plainText:o}){if(Te(t))return t;const n=(e||o).replace(/<[^>]+>/g,"").trim();return Oe(n)?Fe(t,{type:Pe,attributes:{url:_e(n)}}):t},edit:Ee}].forEach((({name:t,...e})=>{t&&(Re("core/link"),Ne(t,e))}))})()})();