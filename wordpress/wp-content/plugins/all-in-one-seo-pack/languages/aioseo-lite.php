<?php
// phpcs:ignore Generic.Files.LineLength.MaxExceeded
/* THIS IS A GENERATED FILE. DO NOT EDIT DIRECTLY. */
$generated_i18n_strings = [
	// Reference: /src/vue/standalone/setup-wizard/views/Features.vue:106
	// Translators: 1 - A plugin's name (e.g. "OptinMonster", "Broken Link Checker").
	__( ' and %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:80
	_n( '__("Insert FAQ Block", td)', '__("Insert FAQ Blocks", td)', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:83
	_n( '__("Insert Key Points Block", td)', '__("Insert Key Points Blocks", td)', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/router/paths.js:26
	// Reference: /src/vue/pages/tools/views/HtaccessEditor.vue:27
	__( '.htaccess Editor', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:170
	__( '\'noindex\' detected in \'robots\' meta tag', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:175
	__( '\'noindex\' detected in \'X-Robots-Tag\' http header', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/GraphCard.vue:106
	// Reference: /src/vue/standalone/post-settings/views/partials/GraphCard.vue:108
	__( '(Default)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:101
	__( '*10 credits will be charged for each selected option.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Competitors.vue:49
	__( '#', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/MostLinkedDomains.vue:97
	__( '# of Links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:69
	// Translators: 1 - Number of credits.
	_n( '%1$d credit', '%1$d credits', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:123
	// Translators: 1 - The number of H1 tags found.
	__( '%1$d H1 tags were found.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:154
	// Translators: 1 - The number of posts (e.g. "1 post", "2 posts").
	_n( '%1$d post', '%1$d posts', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/subheadingsDistribution.js:48
	// Translators: 1 - Expand to the number of text sections not separated by subheadings, 2 - expands to the recommended number of words following a subheading.
	_n( '%1$d section of your text is longer than %2$d words and is not separated by any subheadings. Add subheadings to improve readability.', '%1$d sections of your text are longer than %2$d words and are not separated by any subheadings. Add subheadings to improve readability.', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Widgets.js:17
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s - Breadcrumbs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Widgets.js:12
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s - HTML Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:346
	// Translators: 1 - The issue message, 2 - The number of occurrences of the issue message.
	__( '%1$s (%2$d occurrences)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GoogleSearchPreview.vue:148
	// Translators: 1 - Amount of reviews, 2 - "vote(s)" or "review(s)".
	__( '%1$s %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/Statistic.vue:75
	// Translators: 1 - The direction (up or down), 2 - The difference, 3 - "in search results", 4 - The first date, 5 - The second date.
	__( '%1$s %2$s %3$s compared to the previous period (%4$s - %5$s)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/cta/Index.vue:101
	// Reference: /src/vue/pages/about/views/GettingStarted.vue:138
	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:139
	// Translators: 1 - Plugin short name ("AIOSEO"), 2 - "Pro".
	__( '%1$s %2$s comes with many additional features to help take your site\'s SEO to the next level!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/CustomFields.vue:41
	// Translators: 1 - Plugin short name ("AIOSEO"), 2 - "Pro".
	__( '%1$s %2$s gives you advanced customizations for our page analysis feature, letting you add custom fields to analyze.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Taxonomies.vue:45
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - "Pro".
	__( '%1$s %2$s lets you set the SEO title and description for custom taxonomies. You can also control all of the robots meta and other options just like the default category and tags taxonomies.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/UnlicensedAddons.vue:24
	// Translators: 1 - Plugin short name ("AIOSEO").
	__( '%1$s Addons Not Configured Properly', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/CtaExportTaxonomies.vue:24
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s allows you to fully control your SEO and social meta for custom taxonomies, including the ability to import/export them.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:135
	// Translators: 1 - Date, 2 - Timestamp.
	__( '%1$s at %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/vue/App.vue:40
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s can automatically output a table of contents based on your heading tags below. Search engines sometimes use table of contents in search results or rich snippets which can help you increase your rankings.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/GoogleSearchConsole.js:48
	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:108
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s can now verify whether your site is correctly verified with Google Search Console and that your sitemaps have been submitted correctly. Connect with Google Search Console now to ensure your content is being added to Google as soon as possible for increased rankings.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/MiIntro.vue:40
	// Reference: /src/vue/components/common/core/MiIntro.vue:50
	// Translators: 1 - The name of one of our partner plugins, 2 - The plugin short name ("AIOSEO").
	__( '%1$s connects %2$s to Google Analytics, providing a powerful integration. %1$s is a partner of %2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:25
	// Translators: 1 - Number of credits, 2 - Date of expiration.
	__( '%1$s credits will expire on %2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keywordDensity.js:25
	// Translators: 1 - Focus Keyword or Keyword.
	__( '%1$s density', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keywordDensity.js:66
	// Translators: 1 - Focus Keyword or Keyword, 2 - Keyword Density Number, 3 - Keyword Matches Number.
	__( '%1$s Density is %2$s, the keyword appears %3$s times.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keywordDensity.js:50
	// Translators: 1 - Focus Keyword or Keyword, 2 - Keyword Density Number, 3 - Keyword Matches Number.
	__( '%1$s Density is high at %2$s, the keyword appears %3$s times. For better results, try to aim for lower than %4$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keywordDensity.js:33
	// Translators: 1 - Focus Keyword or Keyword, 2 - Keyword Density Number, 3 - Keyword Matches Number.
	__( '%1$s Density is low at %2$s, the keyword appears %3$s times. For better results, try to aim for more than %4$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:64
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s Documentation', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/TruSeoScore.js:24
	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:271
	// Translators: 1 - The amount of errors.
	_n( '%1$s Error', '%1$s Errors', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:145
	// Translators: 1 - How many errors were found.
	_n( '%1$s error found!', '%1$s errors found!', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:39
	// Translators: 1 - The type of format ("Title", "Alt Tag", "Caption" or "Description").
	__( '%1$s Format', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInImageAlt.js:31
	// Translators: 1 - Focus Keyword or Keyword.
	__( '%1$s found in image alt attribute(s).', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInDescription.js:25
	// Translators: 1 - Focus Keyword or Keyword.
	__( '%1$s found in meta description.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:257
	// Translators: 1 - The Plugin short name ("AIOSEO").
	__( '%1$s has a full suite of tools to manage the robots.txt file, along with other related technologies, like XML Sitemaps.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:91
	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:96
	// Translators: 1 - A plugin name (e.g. "MonsterInsights", "Broken Link Checker", etc.).
	__( '%1$s has an intuitive setup wizard to guide you through the setup process.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:136
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - The plugin short name ("AIOSEO").
	__( '%1$s has detected a physical robots.txt file in the root folder of your WordPress installation. We recommend removing this file as it could cause conflicts with WordPress\' dynamically generated one. %2$s can import this file and delete it, or you can simply delete it.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/admin-bar-noindex-warning/App.vue:44
	// Translators: 1 - The short plugin name ("AIOSEO"), 2 - Opening HTML anchor tag, 3 - Closing HTML anchor tag.
	__( '%1$s has detected that you are blocking access to search engines, which will prevent your site from ranking in search results. You can re-enable indexing under %2$sSettings > Reading →%3$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/GoogleSearchConsole.js:57
	// Reference: /src/vue/pages/sitemaps/views/partials/SitemapsWithErrorsModal.vue:77
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s has found some errors in sitemaps that were previously submitted to Google Search Console. Since %1$s manages your sitemaps, these additional sitemaps can be removed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:235
	// Translators: 1 - "Clarity", 2 - Learn more link.
	__( '%1$s helps you understand how users interact with your website through heatmaps and session recordings. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInImageAlt.js:26
	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInImageAlt.js:42
	// Translators: 1 - Focus Keyword or Keyword.
	__( '%1$s in image alt attributes', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInIntroduction.js:19
	// Translators: 1 - Focus Keyword or Keyword.
	__( '%1$s in introduction', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInDescription.js:17
	// Translators: 1 - Focus Keyword or Keyword.
	__( '%1$s in meta description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:91
	// Translators: 1 - Plugin short name "AIOSEO", 2 - Semrush.
	__( '%1$s integrates directly with %2$s to provide you with actionable keywords to help you write better content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:65
	// Translators: 1 - The plugin name ("All in One SEO"), 2 - Company name ("Awesome Motive").
	__( '%1$s is brought to you by %2$s, the same team that’s behind the largest WordPress resource site, WPBeginner, the most popular lead-generation software, OptinMonster, the best WordPress analytics plugin, MonsterInsights and many more.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:46
	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:51
	// Translators: 1 - A plugin name (e.g. "MonsterInsights", "Broken Link Checker", etc.).
	__( '%1$s is Installed & Active', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseLength.js:54
	// Translators: 1 - Focus Keyword or Keyword.
	__( '%1$s is slightly long. Try to make it shorter.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:37
	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:87
	// Translators: 1 - Plugin name ("All in One SEO"), 2 - The number of active users, 3 - Plugin short name ("AIOSEO").
	__( '%1$s is the best WordPress SEO plugin. Join over %2$s Professionals who are already using %3$s to improve their website search rankings.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseLength.js:67
	// Translators: 1 - Focus Keyword or Keyword.
	__( '%1$s is too long. Try to make it shorter.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseLength.js:22
	// Translators: 1 - Focus Keyword or Keyword.
	__( '%1$s length', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/link-assistant/InboundInternal.vue:128
	// Translators: 1 - The type of link.
	__( '%1$s Link Suggestions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:21
	// Translators: 1 - The abbreviated plugin name ("AIOSEO").
	__( '%1$s Lite vs. Pro', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Widgets.js:32
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s Local - Business Info', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Widgets.js:27
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s Local - Map', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Widgets.js:22
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s Local - Opening Hours', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Welcome.vue:40
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s makes it easy to configure your site\'s SEO settings without the need to hire an expert. And it takes less than 10 minutes too!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:292
	// Translators: 1 - The Plugin short name ("AIOSEO").
	__( '%1$s makes it extremely easy to add highly relevant Schema.org markup to your site. It has a simple graphical interface, so you don\'t have to get your hands dirty with complex HTML markup.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInImageAlt.js:47
	// Translators: 1 - Focus Keyword or Keyword.
	__( '%1$s not found in image alt attribute(s). Add an image with your %1$s as alt text.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInDescription.js:37
	// Translators: 1 - Focus Keyword or Keyword.
	__( '%1$s not found in meta description.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/sentenceLength.js:48
	// Translators: 1 - Number of the sentences, 2 - Number of words, 3 - Recommended maximum of words.
	__( '%1$s of the sentences contain more than %2$s words, which is more than the recommended maximum of %3$s. Try to shorten the sentences.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/passiveVoice.js:50
	// Translators: 1 - Percentage of the sentences, 2 - Expected maximum percentage of sentences.
	__( '%1$s of the sentences contain passive voice, which is more than the recommended maximum of %2$s. Try to use their active counterparts.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/MaxCounts.js:8
	// Translators: 1 - A number, 2 - A number.
	__( '%1$s out of %2$s max recommended characters.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:112
	// Translators: 1 - The plugin shortname ("AIOSEO").
	__( '%1$s Overview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:118
	// Translators: 1 - Name of the Pro license plan ("Basic, ""Plus", "Pro", "Elite").
	__( '%1$s Plan AI Credits', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/GraphDecay.vue:106
	// Translators: 1 - The number of points.
	__( '%1$s points', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/composables/Statistic.js:15
	// Translators: 1 - The number of points.
	__( '%1$s Points', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:230
	// Translators: 1 - "Clarity".
	__( '%1$s Project ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ApiBar.vue:40
	// Translators: 1 - The plugin name ("All in One SEO"), 2 - "upgrading to Pro".
	__( '%1$s relies on the WordPress Rest API and your site might have it disabled. %2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/page-builders/Modal.vue:36
	// Reference: /src/vue/standalone/page-builders/avada/components/Sidebar.vue:20
	// Reference: /src/vue/standalone/page-builders/siteorigin/components/Sidebar.vue:17
	// Reference: /src/vue/standalone/page-builders/thrive-architect/components/Sidebar.vue:15
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:65
	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:76
	// Translators: 1 - A plugin name (e.g. "MonsterInsights", "Broken Link Checker", etc.).
	__( '%1$s shows you exactly which content gets the most visits, so you can analyze and optimize it for higher conversions.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/vue/App.vue:34
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( '%1$s Table of Contents', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/Advanced.vue:68
	// Reference: /src/vue/pages/search-appearance/views/partials/TitleDescription.vue:87
	// Reference: /src/vue/standalone/post-settings/views/General.vue:171
	// Translators: 1 - The type of page (Post, Page, Category, Tag, etc.).
	__( '%1$s Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:97
	// Translators: 1 - The name of the plugin being imported (e.g "Yoast SEO").
	__( '%1$s was successfully imported!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:50
	// Translators: 1 - Example URL, 2 - Example URL.
	__( '%1$s will redirect to %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:105
	// Translators: 1 - Opening link tag, 2 - Closing link tag, 3 - Semrush.
	__( '%1$sA valid license key is required%2$s in order to connect with %3$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/cta/Type/1.vue:41
	// Reference: /src/vue/components/common/cta/Type/6.vue:39
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:49
	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:85
	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:76
	// Translators: 1 - Opening bold tag, 2 - Closing bold tag, 3 - "Pro", 4 - Opening bold tag, 5 - A discount percentage (e.g. "50%"), 6 - Closing bold tag.
	__( '%1$sBonus:%2$s You can upgrade to the %3$s plan today and %4$ssave %5$s off%6$s (discount auto-applied).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/location-map/LocationMapSidebar.vue:38
	// Translators: 1 - Strong tag, 2 - Close strong tag.
	__( '%1$sThe custom marker should be: 100x100 px.%2$s If the image exceeds those dimensions it could (partially) cover the info popup.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/keywords-graph/Upgrade.vue:16
	// Translators: 1 - Opening HTML link tag, 2 - Plugin short name ("AIOSEO"), 3 - "Pro", 4 - Closing HTML link tag.
	__( '%1$sUpgrade your %2$s %3$s%4$s plan to see Keyword Positions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/redirects/add-redirect/App.vue:34
	// Translators: 1 - A internal link for Redirects, 2 - Open strong tag, 3 - Close strong tag.
	__( '%2$sYour redirect was added and you may edit it <a href="%1$s" target="_blank">here</a>.%3$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/Statistic.vue:88
	// Translators: 1 - The number of clicks.
	_n( '%s click', '%s clicks', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/Statistic.vue:94
	// Translators: 1 - The number of impressions.
	_n( '%s impression', '%s impressions', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/Statistic.vue:109
	// Translators: 1 - The number of keywords.
	_n( '%s keyword', '%s keywords', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/Statistic.vue:116
	// Translators: 1 - The number of points.
	_n( '%s point', '%s points', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/Statistic.vue:103
	// Translators: 1 - The number of spots.
	_n( '%s spot', '%s spots', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:113
	__( '1 day', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:112
	__( '1 hour', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:114
	__( '1 week', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:91
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:20
	__( '10-15%', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:82
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:18
	__( '10-20%', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsGraphs.vue:32
	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsGraphs.vue:61
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsDistributionGraph.vue:36
	// Reference: /src/vue/pages/search-statistics/views/partials/keywords-graph/KeywordsGraph.vue:39
	__( '11-50 Position', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:73
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:16
	__( '20-30%', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsGraphs.vue:28
	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsGraphs.vue:56
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsDistributionGraph.vue:31
	// Reference: /src/vue/pages/search-statistics/views/partials/keywords-graph/KeywordsGraph.vue:33
	__( '4-10 Position', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:103
	__( '404 Error Format', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/router/paths.js:58
	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:35
	// Reference: /src/vue/pages/tools/views/partials/DatabaseTools.vue:47
	__( '404 Logs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Redirects.vue:40
	__( '404 Monitoring', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1305
	__( '404 Redirects', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsGraphs.vue:36
	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsGraphs.vue:66
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsDistributionGraph.vue:41
	// Reference: /src/vue/pages/search-statistics/views/partials/keywords-graph/KeywordsGraph.vue:45
	__( '50-100 Position', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/date.js:17
	__( 'a day ago', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:93
	__( 'A Description tag is required in order to properly display your meta descriptions on your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/date.js:10
	__( 'a few seconds ago', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:134
	__( 'A good headline stimulates reader interest and offers a compelling reason to read your content. It promises a believable benefit.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportAioseo.vue:45
	__( 'A JSON, CSV or INI file is required to import.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/date.js:11
	__( 'a minute ago', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/date.js:22
	__( 'a month ago', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:125
	__( 'A name that Google may use for your homepage in mobile search results. This will default to the WordPress site title if left blank.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:353
	__( 'A redirect already exists for this source URL. To make changes, edit the original instead.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/tags.js:55
	__( 'A short description for your product.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:370
	__( 'A simple & powerful event calendar plugin for WordPress that comes with all the event management features including payments, scheduling, timezones, ticketing, recurring events, and more.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/SeoSiteScore.js:28
	__( 'A valid license key is required', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:88
	__( 'A valid license key is required in order to use our addons.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/PanelNewScore.vue:17
	// Reference: /src/vue/standalone/headline-analyzer/components/TabCurrentScore.vue:16
	// Translators: 1 - Initial score range, 2 - Final score range.
	__( 'A very good score is between %1$d and %2$d.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Score.vue:19
	// Reference: /src/vue/components/common/core/site-score/Analyze.vue:50
	// Translators: 1 - Opening bold HTML tag, 2 - Closing bold HTML tag, 3 - Initial score range, 4 - Final score range.
	__( 'A very good score is between %1$s%3$d and %4$d%2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/date.js:28
	__( 'a year ago', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/seo-preview/views/partials/GoogleSerpWireframe.vue:36
	__( 'About 61,000,000,000 results (0.40 seconds)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1518
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:136
	__( 'About Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:36
	__( 'About Page URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:30
	__( 'About Page URL:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/router/paths.js:17
	// Reference: /src/vue/pages/about/views/Main.vue:17
	__( 'About Us', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:47
	__( 'Academic', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:58
	// Reference: /src/vue/pages/settings/router/paths.js:88
	__( 'Access Control', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/AccessControl.vue:35
	// Translators: 1 - "PRO".
	__( 'Access Control is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:14
	__( 'Access Control Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:122
	__( 'Access our Premium Support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/lite/overview/Overview.vue:34
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:176
	__( 'Actionable Link Suggestions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:166
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:94
	// Reference: /src/vue/pages/sitemaps/views/partials/SitemapsWithErrorsModal.vue:109
	__( 'Actions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:31
	// Reference: /src/vue/pages/about/views/AboutUs.vue:79
	__( 'Activate', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:36
	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:41
	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:60
	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:71
	// Translators: 1 - A plugin name (e.g. "MonsterInsights", "Broken Link Checker", etc.).
	__( 'Activate %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:79
	__( 'Activate All Features', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:22
	__( 'Activate Author SEO (E-E-A-T)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/IndexNowSettings.vue:55
	__( 'Activate IndexNow', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:27
	__( 'Activate License', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/WpCode.vue:67
	__( 'Activate WPCode', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:72
	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:62
	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:93
	// Reference: /src/vue/pages/about/views/AboutUs.vue:84
	__( 'Activated', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Notifications.js:11
	__( 'Active Notifications', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:49
	__( 'Active Plugins', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:47
	__( 'Active Theme', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1171
	__( 'Activities', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1173
	__( 'Activity', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1209
	__( 'Actor', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1468
	__( 'Ad free (no banner adverts)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:26
	__( 'Add', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/PreventCrawling.vue:30
	// Translators: 1 - Example URL, 2 - Example URL, 3 - Example URL.
	__( 'Add a \'disallow\' rule to your robots.txt file to prevent crawling of URLs like %1$s , %2$s and %3$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/vue/App.vue:37
	__( 'Add a heading block below to begin generating the Table of Contents.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/redirects/add-redirect/App.vue:31
	__( 'Add a Redirect', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/AdditionalKeyphrases.vue:39
	__( 'Add Additional Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/RssContent.vue:47
	__( 'Add content after each post in your site feed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/RssContent.vue:46
	__( 'Add content before each post in your site feed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:54
	__( 'Add Custom Rule', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/Advanced/EmailSummary.vue:50
	__( 'Add Email Address', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:73
	__( 'Add Focus Keyword', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:133
	__( 'Add IndexNow support to instantly notify search engines when your content has changed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:105
	__( 'Add IndexNow support to instantly notify search engines when your content has changed. This helps the search engines to prioritize the changes on your website and helps you rank faster.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:68
	__( 'Add Item', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:79
	__( 'Add Keyword', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/Tabs.vue:22
	__( 'Add Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/LicenseStore.js:17
	__( 'Add License Key', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:189
	__( 'Add links to internal and external resources that are useful for your readers. For Internal links, make sure the links are highly relevant to the subject you\'re writing about. For external links, make sure you link to high-quality sites - Google penalizes pages that link to "spammy" sites (ones that break the Google webmaster guidelines).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Blur.vue:28
	__( 'Add New Redirection', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:104
	__( 'Add Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:181
	__( 'Add Redirect', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:181
	__( 'Add Redirects', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:105
	__( 'Add Rule', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:81
	__( 'Add to Group', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:164
	__( 'Add to KRT', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:85
	__( 'Add URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:98
	__( 'Additional Data', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:131
	// Reference: /src/vue/standalone/post-settings/views/partials/general/AdditionalKeyphrases.vue:38
	__( 'Additional Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/AdditionalKeyphrases.vue:43
	// Translators: 1 - "Pro" string, 2 - "Learn more link".
	__( 'Additional Keywords are a %1$s feature. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:70
	// Translators: 1 - Semrush.
	__( 'Additional Keywords by %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:74
	__( 'Additional Pages', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:95
	__( 'Additional Profiles', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:58
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:52
	__( 'Additional Schema Types', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/router/paths.js:44
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:50
	__( 'Additional Site Information', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:72
	__( 'Additional URLs to help identify the item (e.g. "https://en.wikipedia.org/wiki/Amazon_(company)").', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:31
	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:39
	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:38
	__( 'Address', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/business/Address.vue:9
	__( 'Address Line 1', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/business/Address.vue:10
	__( 'Address Line 2', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/index.js:57
	__( 'Address:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:76
	__( 'Admin Bar Menu', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:109
	// Translators: 1 - "PRO", 2 - "Learn more".
	__( 'Admin Bar Menu is a %1$s feature. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:19
	__( 'Administrator', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1558
	__( 'Adults', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:25
	// Reference: /src/vue/pages/search-appearance/router/paths.js:72
	// Reference: /src/vue/pages/search-appearance/views/Archives.vue:43
	// Reference: /src/vue/pages/search-appearance/views/ContentTypes.vue:67
	// Reference: /src/vue/pages/search-appearance/views/Media.vue:95
	// Reference: /src/vue/pages/search-appearance/views/Taxonomies.vue:64
	// Reference: /src/vue/pages/settings/router/paths.js:97
	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:69
	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:56
	// Reference: /src/vue/standalone/post-settings/views/Main.vue:189
	__( 'Advanced', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/breadcrumbs/index.js:116
	__( 'Advanced > Breadcrumbs.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:151
	__( 'Advanced Rich Snippets + Schema Markups', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SeoSiteAnalysisResults.vue:40
	__( 'Advanced SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:91
	// Reference: /src/vue/components/common/core/ui-element-slider/SlideContent.vue:32
	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:63
	// Reference: /src/vue/pages/search-appearance/views/Media.vue:77
	// Reference: /src/vue/pages/settings/views/Advanced.vue:58
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:77
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:165
	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:102
	__( 'Advanced Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1463
	__( 'Advanced support for e-commerce', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:76
	__( 'Advanced WooCommerce', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:213
	__( 'Affiliate', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkCount.vue:55
	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkRatio.vue:47
	__( 'Affiliate Links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/router/paths.js:50
	// Reference: /src/vue/standalone/post-settings/views/Main.vue:200
	__( 'AI Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/Main.vue:35
	__( 'AI Content Generation', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/AiContent.vue:14
	__( 'AI Content Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/breadcrumbs/index.js:36
	__( 'AIOSEO - Breadcrumbs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/html-sitemap/index.js:40
	__( 'AIOSEO - HTML Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/index.js:42
	__( 'AIOSEO Local - Business Info', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/location-categories/index.js:31
	__( 'AIOSEO Local - Location Categories', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/locations/index.js:40
	__( 'AIOSEO Local - Locations', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/location-map/index.js:43
	__( 'AIOSEO Local - Map', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/opening-hours/index.js:42
	__( 'AIOSEO Local - Opening Hours', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/registerSidebar.js:74
	// Reference: /src/vue/standalone/writing-assistant/registerSidebar.js:81
	__( 'AIOSEO Writing Assistant', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1231
	__( 'Album', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:58
	__( 'Alias Of', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:89
	// Reference: /src/vue/standalone/seo-preview/views/partials/GoogleSerpWireframe.vue:35
	__( 'All', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:91
	__( 'All (recommended)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ResetSettings.vue:58
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'All %1$s Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/UnwantedBots.vue:39
	__( 'All AI Crawlers', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:329
	__( 'All CSS files appear to be minified.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/SitemapsWithErrorsModal.vue:80
	__( 'All errors have been resolved', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:105
	__( 'All good', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/TruSeoScore.js:18
	__( 'All Good!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Blur.vue:75
	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:130
	__( 'All Groups', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:160
	__( 'All images on the page have alt attributes.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:160
	__( 'All images on your page have alt attributes.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/faq/index.js:30
	// Reference: /src/vue/standalone/blocks/key-points/index.js:27
	__( 'All in One SEO is perfect for business owners, bloggers, designers, developers, photographers, and basically everyone else. If you want to optimize your WordPress SEO, then you need to use All in One SEO Pack.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/views/SeoAuditChecklist.vue:58
	__( 'All Items', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:316
	__( 'All Javascript files appear to be minified.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/Index.vue:31
	// Reference: /src/vue/pages/search-statistics/views/lite/keyword-rank-tracker/Blur.vue:19
	__( 'All Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:95
	__( 'All letters are converted to lower case (small) letters.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostTypeOptions.vue:59
	__( 'All post types are set to noindex or your site does not have any post types registered that are supported by this feature.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:48
	__( 'All Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostTypeOptions.vue:60
	__( 'All taxonomies are set to noindex or your site does not have any taxonomies registered that are supported by this feature.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:272
	__( 'All the required Open Graph meta tags have been found.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1588
	__( 'All Wheel Drive', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:106
	__( 'Allow', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:51
	__( 'Allows you to specify the maximum number of posts for the RSS Sitemap. We recommend an amount of 50 posts.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:21
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:65
	__( 'Allows you to specify the maximum number of posts in a sitemap (up to 50,000).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:95
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:19
	__( 'Alphabetical', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/LicenseKey.vue:84
	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:113
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'Already purchased? Simply enter your license key below to connect with %1$s!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:150
	__( 'Also, try to include synonyms and relevant terminology in H2 tag text. Search engines are pretty smart - they know which words usually occur together in each niche.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Media.vue:108
	__( 'Alt Tag', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:126
	__( 'Alternate Website Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:443
	__( 'Alternatively, you can create an empty index.php file and save it in every directory on your site. That\'s an approach that WordPress uses and it works well.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:308
	__( 'Alternatively, you can use a CMS plugin to simplify the process - it\'s a more user-friendly option. WordPress has a host of caching plugins, and most of them give you options to control the caching headers.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/partials/EeatBlur.vue:19
	__( 'Alumni Of', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:17
	__( 'always', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:34
	__( 'Ampersand (&)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:127
	__( 'An alternate name for your site. This could be an acronym or shorter version of your website name.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/Advanced/EmailSummary.vue:105
	// Translators: 1 - An email address.
	__( 'An email was sent out to %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/addon/Activate.vue:49
	// Reference: /src/vue/components/common/core/addon/Update.vue:52
	// Reference: /src/vue/pages/tools/views/WpCode.vue:39
	__( 'An error occurred while activating the addon. Please upload it manually or contact support for more information.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:95
	__( 'An error occurred while adding your redirects. Please try again later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/SeoSiteScore.js:29
	__( 'An error occurred while analyzing your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:81
	__( 'An error occurred while changing the addon status. Please try again or contact support for more information.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:142
	__( 'An error occurred while fetching keywords. Please try again later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:104
	// Translators: 1 - The name of the plugin being imported (e.g "Yoast SEO").
	__( 'An error occurred while importing %1$s. Please try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:70
	__( 'An error occurred while trying to add the query arg to the blocklist. Please try again later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:74
	__( 'An error occurred while trying to delete the query arg. Please try again later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:72
	__( 'An error occurred while trying to remove the query arg from the blocklist. Please try again later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/BuyOrConnectButtons.vue:92
	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:139
	// Reference: /src/vue/stores/integrations/SemrushStore.js:47
	// Reference: /src/vue/stores/integrations/SemrushStore.js:63
	__( 'An unknown error occurred, please try again later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:84
	// Translators: 1 - Plugin short name ("AIOSEO"), 2 - Pro.
	__( 'An update is required for this addon to continue to work with %1$s %2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:79
	__( 'An upgrade is required to unlock the following features.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:54
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'An XML Sitemap is a list of all your content that search engines use when they crawl your site. This is an essential part of SEO because it contains some important pieces of information that search engines need when crawling your site. The XML Sitemap created by %1$s tells search engines where to find all of the content on your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:41
	// Reference: /src/vue/pages/monsterinsights/router/paths.js:17
	// Reference: /src/vue/pages/monsterinsights/views/Main.vue:13
	__( 'Analytics', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/analyze/Index.vue:32
	// Reference: /src/vue/pages/seo-analysis/views/AnalyzeCompetitorSite.vue:52
	// Reference: /src/vue/pages/seo-analysis/views/HeadlineAnalyzer.vue:45
	__( 'Analyze', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/router/paths.js:26
	__( 'Analyze Competitor Site', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/TabNewScore.vue:16
	__( 'Analyze Headline', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/standalones/WritingAssistantStore.js:283
	__( 'analyzing serps', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:84
	// Translators: 1 - Plugin short name + Pro "AIOSEO Pro", 2 - Semrush, 3 - Link to learn more.
	__( 'Analyzing your content with %1$s keywords is only available to licensed %2$s users. %3$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Index.vue:24
	__( 'Analyzing...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Url.vue:113
	__( 'Anchor values are not sent to the server and cannot be redirected.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:78
	__( 'And many more...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:35
	// Reference: /src/vue/plugins/constants.js:1309
	__( 'Animal Shelter', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:88
	__( 'Announcements', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1546
	__( 'Annual', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:80
	__( 'Any posts that are assigned to these terms will also be excluded from your sitemap.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:140
	__( 'Any sitemaps that this URL was listed in, as known by Google. Not guaranteed to be an exhaustive list, especially if Google did not discover this URL through a sitemap. Empty if no entries in sitemaps were found.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:423
	// Translators: 1 - The name of the theme.
	__( 'Anyone can see that they are using the %1$s theme.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:419
	// Translators: 1 - The name of the theme.
	__( 'Anyone can see that you are using the %1$s theme.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/maps/Blur.vue:16
	__( 'API Key', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:32
	__( 'Apostrophe (\')', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:75
	__( 'Append Author Bio to Posts', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/wp/BulkActions.vue:17
	__( 'Apply', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/PanelNewScore.vue:28
	__( 'Apply Headline', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/tags.js:71
	__( 'archive', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/tags.js:69
	// Reference: /src/vue/utils/tags.js:70
	__( 'Archive', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:99
	__( 'Archive Format', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1310
	__( 'Archive Organization', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/router/paths.js:63
	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:45
	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:63
	__( 'Archives', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review.vue:57
	// Reference: /src/vue/components/common/notifications/Review2.vue:45
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'Are you enjoying %1$s?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:151
	__( 'Are you sure you want to activate these addons across the network?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:113
	__( 'Are you sure you want to activate this addon across the network?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:78
	__( 'Are you sure you want to block these query args?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:77
	__( 'Are you sure you want to block this query arg?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:149
	__( 'Are you sure you want to deactivate these addons across the network?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:111
	__( 'Are you sure you want to deactivate this addon across the network?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/link-assistant/Links.js:43
	__( 'Are you sure you want to delete all links?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/link-assistant/Links.js:42
	__( 'Are you sure you want to delete these links?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:77
	__( 'Are you sure you want to delete these pages?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:76
	__( 'Are you sure you want to delete these query args?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:49
	__( 'Are you sure you want to delete this backup?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/link-assistant/Links.js:41
	__( 'Are you sure you want to delete this link?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:76
	__( 'Are you sure you want to delete this page?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:75
	__( 'Are you sure you want to delete this query arg?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/DisconnectModal.vue:23
	__( 'Are you sure you want to disconnect from AI Content?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/authenticate/DisconnectModal.vue:23
	__( 'Are you sure you want to disconnect from SEOBoost?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/DisconnectModal.vue:17
	__( 'Are you sure you want to disconnect Google Search Console?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/index.js:151
	__( 'Are you sure you want to leave? you have unsaved changes!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/debug/WritingAssistant.vue:15
	__( 'Are you sure you want to reset SEOBoost logins?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ResetSettings.vue:47
	__( 'Are you sure you want to reset the selected settings to default?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:50
	__( 'Are you sure you want to restore this backup?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:80
	__( 'Are you sure you want to unblock these query args?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:79
	__( 'Are you sure you want to unblock this query arg?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:42
	__( 'Area Served', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:38
	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:43
	// Reference: /src/vue/plugins/constants.js:1246
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:10
	// Reference: /src/vue/standalone/writing-assistant/views/report/Competitors.vue:50
	__( 'Article', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:88
	__( 'Article Section', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:89
	__( 'Article Tags', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:118
	__( 'Article Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:42
	__( 'Article Type', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/LicenseKey.vue:70
	// Reference: /src/vue/pages/settings/views/GeneralSettings.vue:91
	// Translators: 1 - "50% off".
	__( 'As a valued user you receive %1$s, automatically applied at checkout!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:99
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:13
	__( 'Ascending', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1541
	__( 'Associate Degree', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:65
	__( 'at', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:100
	__( 'at least one', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:22
	__( 'At least one', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/paragraphLength.js:19
	__( 'At least one paragraph is long. Consider using short paragraphs.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:142
	// Reference: /src/vue/standalone/headline-analyzer/components/CharacterCount.vue:76
	__( 'At this length, it will get cut off in search results. Try reducing it to about 55 characters.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1210
	__( 'Athlete', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:77
	__( 'Atom Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Image.js:63
	__( 'Attached Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Media.vue:73
	__( 'Attachment', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Media.vue:74
	__( 'Attachment Parent', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:69
	__( 'Attachments Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:73
	// Reference: /src/vue/pages/settings/views/partials/AiContent/Main.vue:44
	__( 'Audience', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:39
	// Reference: /src/vue/plugins/constants.js:1211
	__( 'Author', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:7
	__( 'Author Bio Block', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:13
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:58
	__( 'Author Experience Topics (E-E-A-T)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:63
	__( 'Author Feeds', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:12
	__( 'Author Info (E-E-A-T)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PriorityScore.vue:54
	__( 'Author Pages', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:6
	__( 'Author Schema', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:71
	// Reference: /src/vue/pages/search-appearance/router/paths.js:53
	// Reference: /src/vue/standalone/user-profile-tab/App.vue:41
	// Reference: /src/vue/standalone/user-profile-tab/follow-up-emails-nav-bar.js:14
	__( 'Author SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:94
	// Reference: /src/vue/composables/Wizard.js:72
	__( 'Author SEO (E-E-A-T)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:16
	// Translators: 1 - "PRO".
	__( 'Author SEO (E-E-A-T) is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:71
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:57
	__( 'Author Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:54
	// Translators: 1 - The image attribute name ("Caption" or "Description").
	__( 'Autogenerate %1$s on Upload', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:65
	__( 'Autogenerate Descriptions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/ImageSeo.vue:37
	__( 'Autogenerate image attributes', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Redirects.vue:38
	__( 'Automatic Redirects', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:90
	__( 'Automatic Updates', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/RssContent.vue:33
	__( 'Automatically add content to your site\'s RSS feed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:127
	__( 'Automatically Generate Article Tags', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/breadcrumbs/index.js:37
	__( 'Automatically output a breadcrumb trail to help your users and search engines navigate your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:66
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:52
	__( 'Automatically Ping Search Engines', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1311
	__( 'Automotive Business', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:142
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:152
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:162
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:172
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:182
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:192
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:212
	__( 'Available as Addon Plugin', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:182
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordInner.vue:80
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:211
	// Reference: /src/vue/pages/search-statistics/views/partials/SeoStatisticsOverview.vue:77
	__( 'Avg. CTR', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/SeoStatisticsOverview.vue:87
	__( 'Avg. Position', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1542
	__( 'Bachelor Degree', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:48
	__( 'Backup Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1290
	__( 'Bad Gateway', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1281
	__( 'Bad Request', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:178
	__( 'Baidu Verification Code', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:173
	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:182
	__( 'Baidu Webmaster Tools', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1200
	__( 'Band', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1180
	__( 'Bar', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:73
	__( 'Basic Guide to Google Analytics', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:77
	__( 'Basic Guide to Google Search Console', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SeoSiteAnalysisResults.vue:39
	// Reference: /src/vue/standalone/post-settings/views/General.vue:133
	// Reference: /src/vue/standalone/post-settings/views/partials/general/PageAnalysis.vue:42
	// Reference: /src/vue/standalone/seo-preview/views/SeoInspector.vue:41
	__( 'Basic SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:134
	__( 'Because your headline plays a large role in reader engagement, it\'s worth spending extra time perfecting it. Many top copywriters spend hours getting their headlines just right - sometimes they spend longer on the headline than the rest of the article!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ExcludePosts.vue:46
	// Reference: /src/vue/components/common/html-sitemap/ExcludeObjects.vue:38
	__( 'Begin typing a post ID, title or slug to search...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ExcludePosts.vue:47
	// Reference: /src/vue/components/common/html-sitemap/ExcludeObjects.vue:39
	__( 'Begin typing a term ID or name to search...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/StartEndWords.vue:12
	__( 'Beginning & Ending Words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/StartEndWords.vue:14
	__( 'Beginning Words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:48
	__( 'Below are the TruSEO scores of your published posts. Take some time to improve your TruSEO score to help increase your rankings.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/KeywordRankTracker.vue:54
	__( 'Below you can track how your page is performing in search results based on your keyword(s).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:81
	__( 'Best Practices for Domains and URLs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1163
	__( 'Billing Support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:146
	__( 'Bing Verification Code', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:141
	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:150
	__( 'Bing Webmaster Tools', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1474
	__( 'Blended', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/UnwantedBots.vue:38
	__( 'Block AI Crawlers', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/PreventCrawling.vue:27
	__( 'Block Crawling of Internal Site Search URLs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:39
	__( 'Block internal site searches which contain complex and non-alphanumeric characters.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:41
	__( 'Block internal site searches which match the patterns of known spam attacks.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:178
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:66
	__( 'Block Key', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:179
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:67
	__( 'Block Key & Value', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:20
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:22
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:89
	__( 'Block Query Arg', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:90
	__( 'Block Query Args', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/UnwantedBots.vue:36
	__( 'Block Unwanted Bots', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:97
	__( 'Blocked by regex: ', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:226
	__( 'Blocked by robots.txt', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:246
	__( 'Blocked due to access forbidden (403)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:250
	__( 'Blocked due to other 4xx issue (not 403, 404)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:234
	__( 'Blocked due to unauthorized request (401)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:125
	__( 'Blocked!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:180
	__( 'Blocking robots.txt rule detected', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:114
	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:70
	__( 'Blog', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:115
	__( 'Blog Page Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:44
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:112
	__( 'Blog Post', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/social-posts/Email.vue:12
	__( 'Body', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1232
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:15
	__( 'Book', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:213
	__( 'Boost your sales and conversions by up to 15% with real-time social proof notifications. TrustPulse helps you show live user activity and purchases to help convince other users to purchase.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:73
	__( 'Boost your SEO performance by highlighting the professional expertise and trustworthiness of your authors, aligning with Google\'s E-E-A-T standards.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:230
	__( 'Both the www and non-www versions of the URL are redirected to the same site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:230
	__( 'Both the www and non-www versions of your URL are redirected to the same site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:97
	__( 'Breadcrumb Prefix', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:87
	// Reference: /src/vue/standalone/blocks/breadcrumbs/index.js:96
	__( 'Breadcrumb Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:111
	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:31
	__( 'Breadcrumb Templates', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:36
	// Translators: 1 - "PRO".
	__( 'Breadcrumb Templates is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:50
	// Reference: /src/vue/pages/settings/router/paths.js:60
	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:84
	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:58
	// Reference: /src/vue/standalone/post-settings/views/Breadcrumbs.vue:26
	__( 'Breadcrumbs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:80
	// Translators: 1 - The plugin name ("AIOSEO").
	__( 'Breadcrumbs are an essential part of SEO. By default %1$s will automatically add breadcrumbs to the schema markup that we add to your site and you don\'t need to make any changes for that to work. Breadcrumbs can also be used as a secondary navigation system that tells users where they are on a website relative to the homepage.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/Breadcrumbs.vue:21
	// Translators: 1 - "PRO", 2 - "Learn more".
	__( 'Breadcrumbs is a %1$s feature. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:31
	// Reference: /src/vue/composables/Wizard.js:32
	__( 'Broken Link Checker', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:93
	__( 'Broken Link Checker by AIOSEO is an essential tool for ensuring that all internal and external links on your website are functioning correctly. Quickly check your site for broken links and easily fix them to improve SEO.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Help.vue:50
	// Translators: 1 - The plugin short name ("AIOEO").
	__( 'Browse documentation, reference material, and tutorials for %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:177
	// Translators: 1 - Plugin short name ("AIOSEO").
	__( 'Build a Better %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/wp/BulkActions.vue:16
	__( 'Bulk Actions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/Advanced.vue:56
	__( 'Bulk Editing', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:51
	__( 'Business', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1483
	__( 'Business Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/Locations.vue:21
	// Reference: /src/vue/pages/local-seo/views/lite/locations/Locations.vue:38
	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:29
	// Reference: /src/vue/standalone/blocks/location-map/LocationMapSidebar.vue:31
	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:30
	// Reference: /src/vue/standalone/local-business-seo/views/Main.vue:32
	__( 'Business Info', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Locations.vue:33
	__( 'Business Info and Location blocks, widgets and shortcodes', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1178
	__( 'Businesses', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/BuyOrConnectButtons.vue:41
	// Reference: /src/vue/components/common/ai/CreditCounter.vue:95
	__( 'Buy Credits', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:97
	__( 'By allowing us to track usage data we can better help you as we will know which WordPress configurations, themes and plugins we should test.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:23
	// Translators: 1 - Opening HTML bold tag, 2 - Closing HTML bold tag.
	__( 'By default Admins have access to %1$sall SEO site settings%2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:43
	// Translators: 1 - Opening HTML bold tag, 2 - Closing HTML bold tag.
	__( 'By default Authors have access to %1$sSEO settings for individual pages and posts that they already have permission to edit.%2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:53
	// Translators: 1 - Opening HTML bold tag, 2 - Closing HTML bold tag.
	__( 'By default Contributors have access to %1$sSEO settings for individual pages and posts that they already have permission to edit.%2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:33
	// Translators: 1 - Opening HTML bold tag, 2 - Closing HTML bold tag.
	__( 'By default Editors have access to %1$sSEO settings for General Settings, Search Appearance, Social Networks, and Redirects as well as all settings for individual pages and posts.%2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:73
	// Translators: 1 - Opening HTML bold tag, 2 - Closing HTML bold tag.
	__( 'By default SEO Editors have access to %1$sSEO settings for individual pages and posts.%2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:63
	// Translators: 1 - Opening HTML bold tag, 2 - Closing HTML bold tag.
	__( 'By default SEO Managers have access to %1$sSEO settings for General Settings, Sitemaps, Link Assistant, Redirects, Local SEO, and individual pages and posts.%2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:86
	// Translators: 1 - The name of the WP role, 2 - Opening bold tag, 3 - Closing bold tag, 4 - Plugin Name ("All in One SEO").
	__( 'By default the %1$s role %2$shas no access%3$s to %4$s settings.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:11
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'By default, only Administrators have permission to manage %1$s within WordPress. With Access Controls, you can easily extend access permissions to other user roles.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1182
	__( 'Cafe', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/standalones/WritingAssistantStore.js:286
	__( 'calculating keywords heading presence', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/standalones/WritingAssistantStore.js:288
	__( 'calculating keywords importance', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/standalones/WritingAssistantStore.js:287
	__( 'calculating keywords use suggestions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:94
	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:107
	// Reference: /src/vue/plugins/constants.js:27
	__( 'Cancel', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1505
	__( 'Cancelled', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:59
	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:58
	// Reference: /src/vue/standalone/seo-preview/views/MetaTags.vue:27
	__( 'Canonical URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Media.vue:113
	__( 'Caption', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:80
	__( 'Car', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:97
	__( 'Card Type', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:49
	__( 'Casing', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/router/paths.js:35
	__( 'Category', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:112
	__( 'Category Hierarchy', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:110
	__( 'Category Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:117
	__( 'Category Parent', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1191
	__( 'Cause', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:56
	// Reference: /src/vue/standalone/headline-analyzer/components/CharacterCount.vue:11
	__( 'Character Count', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:132
	__( 'Check how your site scores with our SEO analyzer and compare against your competitor\'s site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:83
	__( 'Check this if you want your categories for a given post used as the Meta Keywords for this post (in addition to any keywords you specify on the Edit Post screen).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:87
	__( 'Check this if you want your keywords on your Posts page (set in WordPress under Settings, Reading, Front Page Displays) and your archive pages to be dynamically generated from the keywords of the posts showing on that page. If unchecked, it will use the keywords set in the edit page screen for the posts page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:85
	__( 'Check this if you want your tags for a given post used as the Meta Keywords for this post (in addition to any keywords you specify on the Edit Post screen).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:139
	// Translators: 1 - Plugin Short Name ("AIOSEO").
	__( 'Check this if you would like to remove ALL %1$s data upon plugin deletion. All settings and SEO data will be unrecoverable.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1519
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:140
	__( 'Checkout Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1312
	__( 'Childcare', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1484
	__( 'Childrens Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:118
	// Reference: /src/vue/pages/tools/views/partials/ImportAioseo.vue:42
	__( 'Choose a File', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:95
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:52
	__( 'Choose a Person', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/import/Blur.vue:18
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'Choose a plugin to import Local SEO directly into %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:43
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'Choose a plugin to import SEO data directly into %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:49
	__( 'Choose a Post Type', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Phone.vue:19
	__( 'Choose country', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/router/paths.js:53
	__( 'Choose Features', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ImageUploader.vue:58
	// Reference: /src/vue/components/common/core/ImageUploader.vue:60
	__( 'Choose Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:70
	// Translators: 1 - Link to the AI Content settings page.
	__( 'Choose the tone and target audience for your content. You can manage your default settings under <a href="%1$s" target="_blank" rel="noopener noreferrer">General Settings > AI Content</a>.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:78
	// Translators: 1 - Opening <code> tag, 2 - Closing </code> tag.
	__( 'Choose whether %1$s should automatically append a compact author bio at the end of every post. You can also manually insert the author bio using the Author Bio block.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:94
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:55
	__( 'Choose whether the site represents a person or an organization.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:68
	__( 'Choose whether you want to use the OG data from the Facebook tab in your individual pages/posts by default.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/TitleDescription.vue:97
	// Translators: 1 - The plural name of the content type (e.g. "Posts" or "Categories").
	__( 'Choose whether your %1$s should be included in search results. If you select "No", then your %1$s will be noindexed and excluded from the sitemap so that search engines ignore them.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:50
	__( 'Choose which casing should be applied to the attribute.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/business/Address.vue:12
	// Reference: /src/vue/plugins/constants.js:1222
	__( 'City', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/ImageSeo.vue:38
	__( 'Clean uploaded image filenames', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/robots-editor/RuleErrors.vue:17
	__( 'Clean-param must start with at least one param which is optionally followed by one path.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ExcludePosts.vue:49
	// Reference: /src/vue/components/common/html-sitemap/ExcludeObjects.vue:41
	__( 'Clear', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:36
	// Reference: /src/vue/pages/tools/views/partials/DatabaseTools.vue:48
	__( 'Clear 404 Logs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:38
	// Reference: /src/vue/pages/tools/views/partials/DatabaseTools.vue:50
	__( 'Clear Redirect Logs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:34
	// Reference: /src/vue/pages/tools/views/partials/DatabaseTools.vue:46
	__( 'Cleared', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:27
	__( 'Click here', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ApiBar.vue:25
	__( 'Click here to learn more', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:44
	// Translators: 1 - The name of the image attribute ("Title", "Alt Tag", "Caption" or "Description").
	__( 'Click on the tags below to insert variables into your %1$s attribute.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:101
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:89
	__( 'Click on the tags below to insert variables into your description.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:98
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:86
	__( 'Click on the tags below to insert variables into your home page title.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:88
	// Reference: /src/vue/pages/search-appearance/views/partials/TitleDescription.vue:66
	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:87
	// Reference: /src/vue/standalone/post-settings/views/General.vue:128
	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:70
	__( 'Click on the tags below to insert variables into your meta description.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:97
	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:86
	__( 'Click on the tags below to insert variables into your site name.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:86
	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:69
	__( 'Click on the tags below to insert variables into your site title.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/HtmlTagsEditor.vue:68
	__( 'Click on the tags below to insert variables into your template.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/TitleDescription.vue:64
	// Reference: /src/vue/standalone/post-settings/views/General.vue:126
	__( 'Click on the tags below to insert variables into your title.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/CopyBlock.vue:25
	__( 'Click to Copy', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/sidebar/OptimizationWizard.vue:30
	__( 'Click to Sort...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:174
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordInner.vue:75
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:206
	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:230
	// Reference: /src/vue/pages/search-statistics/views/partials/TopKeywords.vue:25
	// Reference: /src/vue/standalone/post-settings/views/partials/keyword-rank-tracker/KeywordsTable.vue:84
	__( 'Clicks', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Help.vue:43
	__( 'Close', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:174
	__( 'Close and Exit Wizard Without Saving', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:39
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:39
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:41
	__( 'Closed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/router/paths.js:62
	// Reference: /src/vue/pages/tools/views/WpCode.vue:33
	__( 'Code Snippets', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1520
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:144
	__( 'Collection Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1485
	__( 'Comedy Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/UnwantedBots.vue:42
	__( 'Common Crawl CCBot', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:40
	__( 'Common Spam Patterns', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:67
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:15
	__( 'Common Words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:159
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:31
	__( 'Compact Archives', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1181
	__( 'Company', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-revisions/views/lite/partials/Controls.vue:11
	__( 'Compare any two revisions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-revisions/views/lite/Index.vue:19
	__( 'Compare Revisions of', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:14
	__( 'Compare the percentages of your results to the goal for each category and adjust as necessary.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Main.vue:52
	__( 'Competitors', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Competitors.vue:48
	__( 'competitors.csv', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:100
	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:95
	// Translators: 1 - Opening HTML link and bold tag, 2 - Closing HTML link and bold tag.
	__( 'Complete documentation on usage tracking is available %1$shere%2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/views/SeoAuditChecklist.vue:46
	__( 'Complete SEO Checklist', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Competitor.vue:52
	// Reference: /src/vue/composables/SeoSiteScore.js:34
	__( 'Complete Site Audit Checklist', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:110
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:121
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:132
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:67
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:78
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:99
	__( 'Complete Support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:152
	__( 'Complete support for schema markup so you can get more clicks and traffic with rich snippets.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:130
	__( 'Configure how your website content will look in Google, Bing and other search engines.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:119
	__( 'Configure Local SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/Schema.vue:39
	__( 'Configure Schema Markup for your content. Search engines use structured data to display rich results in SERPs.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:59
	__( 'Congratulations, your site is now SEO ready!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/LicenseKey.vue:48
	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:79
	__( 'Connect', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/AiContent/Main.vue:46
	__( 'Connect to AI Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/SeoSiteScore.js:60
	__( 'Connect to AIOSEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/BuyOrConnectButtons.vue:42
	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:50
	__( 'Connect to an Existing Account', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/GoogleSearchConsole.js:51
	// Reference: /src/vue/pages/search-statistics/views/Main.vue:65
	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:111
	// Reference: /src/vue/standalone/post-settings/views/lite/KeywordRankTracker.vue:57
	__( 'Connect to Google Search Console', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/GoogleSearchConsole.js:52
	__( 'Connect to Google to automatically add sitemaps and keep them in sync.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:49
	__( 'Connect to SEOBoost', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:51
	__( 'Connect to SEOBoost to get access to the Writing Assistant.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/SeoSiteScore.js:104
	// Reference: /src/vue/standalone/connect-pro/router/paths.js:19
	// Reference: /src/vue/standalone/connect/router/paths.js:19
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'Connect with %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:89
	__( 'Connect with Google Search Console to track how your site is performing in search rankings and generate reports with actionable insights that help you get the most out of your content. (Elite plan only)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:307
	__( 'Connect with your visitors after they leave your website with the leading web push notification software. Over 10,000+ businesses worldwide use PushEngage to send 15 billion notifications each month.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/GoogleSearchConsoleSettings.vue:62
	__( 'Connect Your Site to Google Search Console', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/composables/Cta.js:11
	__( 'Connect your site to Google Search Console to receive insights on how content is being discovered. Identify areas for improvement and drive traffic to your website.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/BuyOrConnectButtons.vue:64
	__( 'Connect Your Site with AI Content Generation', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/connect-pro/views/Main.vue:17
	// Reference: /src/vue/standalone/connect/views/Main.vue:17
	__( 'Connecting...', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/consecutiveSentences.js:27
	// Reference: /src/app/tru-seo/analyzer/analysis/consecutiveSentences.js:40
	__( 'Consecutive sentences', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:45
	// Translators: 1 - "?s=".
	__( 'Consolidates WordPress\' multiple site search URL formats into the %1$s e.g.,', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:39
	__( 'Contact Info', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1521
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:148
	__( 'Contact Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:37
	__( 'Contact Page URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:31
	__( 'Contact Page URL:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/lite/seo-statistics/Blur.vue:33
	__( 'Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/GoogleSearchConsole.js:64
	__( 'Content Decay Tracking', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/lengthContent.js:31
	// Reference: /src/app/tru-seo/analyzer/analysis/lengthContent.js:44
	// Reference: /src/app/tru-seo/analyzer/analysis/lengthContent.js:53
	// Reference: /src/app/tru-seo/analyzer/analysis/lengthContent.js:61
	__( 'Content length', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:61
	// Reference: /src/vue/pages/search-statistics/views/seo-statistics/Index.vue:33
	__( 'Content Performance', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/router/paths.js:61
	// Reference: /src/vue/pages/search-statistics/views/content-rankings/Index.vue:27
	// Reference: /src/vue/pages/search-statistics/views/lite/content-rankings/Blur.vue:27
	// Reference: /src/vue/pages/search-statistics/views/lite/dashboard/Blur.vue:45
	__( 'Content Rankings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/router/paths.js:26
	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:43
	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:51
	__( 'Content Types', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1531
	__( 'Contractor', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:49
	__( 'Contributor', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:133
	__( 'Control the priority & frequency of each Post, Page, Category, Tag, etc.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:129
	__( 'Control the priority & frequency per Post Type/Taxonomy', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:163
	__( 'Control the title, alt tag, caption, description and filename of your images (Plus, Pro & Elite plans only)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:51
	__( 'Conversion Tools', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/ImageSeo.vue:40
	__( 'Convert casing of image attributes', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1441
	__( 'Cookie', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/CopyBlock.vue:25
	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:51
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:128
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:148
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:98
	__( 'Copied!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:127
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:147
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:97
	__( 'Copy', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:42
	__( 'Copy to Clipboard', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:129
	__( 'Cornerstone Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/CornerstoneContent.vue:33
	// Translators: 1 - "PRO", "Learn more".
	__( 'Cornerstone Content is a %1$s feature. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/CornerstoneContent.vue:29
	__( 'Cornerstone content refers to the most  important and informative articles or pages on your website that serve as the foundation for your content strategy. AIOSEO uses cornerstone content for', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:74
	__( 'Corporation', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1410
	__( 'CORS', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/PanelNewScore.vue:69
	// Reference: /src/vue/standalone/headline-analyzer/components/TabCurrentScore.vue:51
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:81
	__( 'Could Be Better', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review.vue:65
	__( 'Could you please do us a BIG favor and give it a 5-star rating on WordPress to help us spread the word and boost our motivation?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkingOpportunities.vue:55
	__( 'Count', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/business/Address.vue:14
	// Reference: /src/vue/plugins/constants.js:1223
	// Reference: /src/vue/standalone/writing-assistant/views/report/Details.vue:26
	__( 'Country', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Phone.vue:18
	__( 'Country code', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:20
	__( 'Course', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:94
	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:52
	__( 'Crawl Cleanup', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/robots-editor/RuleErrors.vue:18
	__( 'Crawl-delay must be a number starting from 1.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:79
	__( 'Crawled As', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:130
	__( 'Crawling allowed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:134
	__( 'Crawling blocked', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:58
	__( 'Create a Free Account', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:113
	__( 'Create and manage redirects for your broken links.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:56
	__( 'Create Backup', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/Tabs.vue:23
	__( 'Create Group', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:183
	__( 'Create, manage and monitor redirects for 404\'s or modified posts + server redirects, full site redirects and site aliases (Pro & Elite plans only)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:46
	// Reference: /src/vue/composables/AiContent.js:67
	__( 'Credits', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:334
	__( 'CSS files appear in many places, including frameworks (like Bootstrap), themes and templates, and third-party plugins.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:355
	__( 'CSS:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/wp/Table.vue:157
	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:71
	__( 'CSV', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:116
	__( 'CSV example file', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/keyword-rank-tracker/KeywordsTable.vue:92
	__( 'CTR', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/PaymentInfo.vue:10
	__( 'Currencies Accepted', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:105
	__( 'Current Item', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/Main.vue:32
	// Reference: /src/vue/standalone/headline-analyzer/components/PanelNewScore.vue:27
	__( 'Current Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1462
	__( 'Custom Breadcrumb Templates', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:77
	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:78
	__( 'Custom Field Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:5
	__( 'Custom Field Support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/ContentTypes.vue:61
	// Reference: /src/vue/pages/search-appearance/views/partials/lite/CustomFields.vue:37
	__( 'Custom Fields', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/CustomFields.vue:48
	// Translators: 1 - "PRO".
	__( 'Custom Fields is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:42
	__( 'Custom HTML templates', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Image.js:77
	__( 'Custom Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/location-map/LocationMapSidebar.vue:35
	__( 'Custom Marker', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:107
	__( 'Custom Robots.txt Preview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/AccessControl.vue:44
	__( 'Custom Role', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:51
	__( 'Custom Rules', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:64
	// Reference: /src/vue/standalone/post-settings/views/partials/GraphCard.vue:44
	__( 'Custom Schema', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SettingsSeparator.vue:33
	__( 'Custom separator:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Taxonomies.vue:51
	// Translators: 1 - "PRO".
	__( 'Custom Taxonomy Support is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/App.vue:48
	__( 'Customer Data', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:217
	// Reference: /src/vue/plugins/constants.js:1164
	__( 'Customer Support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:19
	__( 'daily', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1477
	__( 'Daily', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1486
	__( 'Dance Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:34
	// Reference: /src/vue/pages/dashboard/router/paths.js:17
	// Reference: /src/vue/pages/dashboard/views/Main.vue:106
	// Reference: /src/vue/pages/search-statistics/router/paths.js:31
	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/MicrosoftClaritySettings.vue:61
	__( 'Dashboard', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:82
	__( 'Dashboard Widgets', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:115
	// Translators: 1 - "PRO", 2 - "Learn more".
	__( 'Dashboard Widgets is a %1$s feature. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:28
	__( 'Dashes (-)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/router/paths.js:44
	__( 'Database Tools', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:25
	__( 'Dataset', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PriorityScore.vue:53
	__( 'Date Archive Pages', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:69
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:55
	__( 'Date Archive Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1549
	__( 'Day', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1535
	__( 'Day-by-Day Basis', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/date.js:21
	// Translators: A number will be prepended to this string, e.g. "2 days ago".
	__( 'days ago', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:32
	__( 'Deactivate', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:80
	__( 'Deactivate All Features', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:28
	__( 'Deactivate License', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:74
	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:97
	// Reference: /src/vue/pages/about/views/AboutUs.vue:85
	__( 'Deactivated', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:235
	__( 'Decide whether you want your site\'s URLs to include a "www", or if you prefer a plain domain name. There are marketing pros and cons for each choice, but neither one is better or worse for SEO purposes - as long as you\'re consistent.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/html-sitemap/DisplayInfo.vue:65
	// Translators: 1 - Opening link tag, 2 - Closing link tag.
	__( 'Dedicated HTML Sitemaps do not work while using "plain" permalinks. Please update your %1$spermalink structure%2$s to use this option.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/html-sitemap/DisplayInfo.vue:55
	__( 'Dedicated Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PriorityScore.vue:60
	// Reference: /src/vue/components/common/core/PriorityScore.vue:63
	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:34
	// Reference: /src/vue/plugins/constants.js:1308
	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:79
	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:82
	__( 'default', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/PostSocial.js:8
	__( 'Default', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/PostSocial.js:16
	__( 'Default (Set under Social Networks)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:69
	__( 'Default Card Type', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Image.js:61
	__( 'Default Image (Set Below)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Image.js:74
	__( 'Default Image Source (Set in Social Networks)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:56
	__( 'Default Language', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/PostSocial.js:10
	__( 'Default Object Type (Set in Social Networks)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:83
	__( 'Default Post Facebook Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:77
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:72
	__( 'Default Post Image Source', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:113
	__( 'Default Post Type Object Types', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:78
	__( 'Default Post X Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:55
	__( 'Default Region', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/AccessControl.vue:30
	__( 'Default settings that just work', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:61
	__( 'Default Social Share Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:114
	__( 'Default Taxonomy Object Types', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:117
	// Translators: 1 - "PRO", 2 - Learn more link.
	__( 'Default Taxonomy Object Types is a %1$s feature. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:118
	__( 'Default template for all pages.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:84
	__( 'Default Term Facebook Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:78
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:73
	__( 'Default Term Image Source', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:123
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:102
	// Translators: 1 - "PRO", 2 - Learn more link.
	__( 'Default Term Image Source is a %1$s feature. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:79
	__( 'Default Term X Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Keyphrase.vue:38
	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:53
	// Reference: /src/vue/composables/link-assistant/Links.js:51
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:69
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:21
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:184
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:59
	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:68
	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:73
	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:58
	// Reference: /src/vue/plugins/constants.js:28
	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:97
	__( 'Delete', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/link-assistant/InboundInternal.vue:123
	// Translators: 1 - The type of link.
	__( 'Delete All %1$s Links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/link-assistant/Links.js:38
	__( 'Delete Link', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:80
	__( 'Delete Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:87
	__( 'Delete Query Arg', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:88
	__( 'Delete Query Args', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:108
	__( 'Delete Rule', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:81
	__( 'Delete selected pages', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1487
	__( 'Delivery Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1313
	__( 'Dentist', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Header.vue:80
	__( 'Depending on the amount of content on your site, this process can take some time. You can safely leave this page and check back later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Header.vue:76
	__( 'Depending on the number of posts being scanned, this process can take some time. You can safely leave this page and check back later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:100
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:14
	__( 'Descending', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostColumn.vue:77
	// Reference: /src/vue/pages/search-appearance/views/Media.vue:118
	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:99
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:87
	// Reference: /src/vue/standalone/posts-table/TermApp.vue:52
	// Reference: /src/vue/standalone/seo-preview/views/MetaTags.vue:23
	__( 'Description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:90
	__( 'Description Format', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/Examples.vue:22
	__( 'desktop', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:97
	__( 'Desktop user agent', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Locations.vue:34
	__( 'Detailed Address, Contact and Payment Info', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/SitemapsWithErrorsModal.vue:83
	__( 'Details', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/primary-term/views/lite/PrimaryTerm.vue:29
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - Opening strong tag, 3 - Closing strong tag.
	__( 'Did you know that %1$s Pro allows you to choose a %2$sprimary category%3$s for your posts? This feature works hand in hand with our powerful Breadcrumbs template to give you full navigational control to help improve your search rankings!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/link-format/App.vue:34
	// Translators: 1 - Learn more link.
	__( 'Did you know you can automatically add internal links using Link Assistant? %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:226
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:231
	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:265
	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:270
	__( 'Diff', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:120
	__( 'Directive', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1212
	__( 'Director', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:439
	__( 'Directory Listing seems to be disabled on the server.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:439
	__( 'Directory Listing seems to be disabled on your server.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:437
	__( 'Directory Listing seems to be enabled on the server.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:437
	__( 'Directory Listing seems to be enabled on your server.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:29
	__( 'Disabled', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:56
	__( 'Disabling the global RSS feed is NOT recommended. This will prevent users from subscribing to your content and can hurt your SEO rankings.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:95
	__( 'Disabling unnecessary features can help save search engine crawl quota and speed up content indexing for larger sites.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:45
	__( 'Disabling Unnecessary RSS Feeds', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:48
	// Translators: 1 - "Learn More" link.
	__( 'Disabling unnecessary RSS feeds can help save search engine crawl quota and speed up content indexing for larger sites. If you choose to disable any feeds, those feed links will automatically redirect to your homepage or applicable archive page. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:121
	__( 'Disallow', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostColumn.vue:81
	// Reference: /src/vue/standalone/posts-table/TermApp.vue:54
	__( 'Discard Changes', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/BuyOrConnectButtons.vue:46
	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:53
	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/GoogleSearchConsoleSettings.vue:65
	__( 'Disconnect', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Notification.vue:57
	// Reference: /src/vue/components/common/notifications/Review.vue:37
	// Reference: /src/vue/components/common/notifications/Review2.vue:31
	__( 'Dismiss', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Notifications.vue:37
	// Reference: /src/vue/pages/dashboard/views/Main.vue:159
	__( 'Dismiss All', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Notifications.vue:36
	__( 'Dismissed Notifications', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/faq/lite/sidebar.js:63
	__( 'Display Block', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:113
	__( 'Display complete category hierarchy even if not selected on each individual post.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/html-sitemap/DisplayInfo.vue:54
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:148
	__( 'Display HTML Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ui-element-slider/Index.vue:28
	__( 'Display Info', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:24
	__( 'Display Opening Hours', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/index.js:253
	// Reference: /src/vue/standalone/blocks/html-sitemap/index.js:154
	// Reference: /src/vue/standalone/blocks/opening-hours/index.js:234
	__( 'Display Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:104
	__( 'Display the sitemap on a dedicated page:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:40
	__( 'Displayed when the business is closed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:45
	__( 'Displayed when the business is open all day long.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:15
	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:13
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:63
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:49
	__( 'Do you get a blank sitemap or 404 error?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:78
	__( 'Do you have multiple authors?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Help.vue:46
	// Reference: /src/vue/standalone/footer-links/App.vue:30
	__( 'Docs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:50
	// Reference: /src/vue/pages/link-assistant/views/partials/overview/MostLinkedDomains.vue:93
	__( 'Domain', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/GeneralSettings.vue:69
	__( 'Domain Activations', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:37
	// Translators: 1 - "Elite".
	__( 'Domain Activations is an %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/router/paths.js:46
	__( 'Domains Report', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:150
	__( 'Don\'t try to force keywords into sub-headings if they feel unnatural. It will send the wrong message to your readers, possibly driving them away.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/limit-modified-date/App.vue:17
	// Reference: /src/vue/standalone/page-builders/siteorigin/components/LimitModifiedDate.vue:29
	__( 'Don\'t update the modified date', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/vue/App.vue:45
	__( 'Done', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/Statistic.vue:68
	__( 'Down', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:115
	__( 'Download Sample CSV File', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:41
	__( 'Download System Info File', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/UrlResults.vue:23
	__( 'DRAFT', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1233
	__( 'Drink', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/maps/Maps.vue:33
	__( 'Driving Directions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:255
	__( 'Drop (%)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1314
	__( 'Dry Cleaning/Laundry', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:270
	__( 'Duplicate Open Graph meta tags were found.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:277
	__( 'Duplicate Open Graph meta tags were found. This means that another plugin on your site or your current theme is generating social meta tags for Facebook (Open Graph) that are also generated by AIOSEO, and there are two sets of Open Graph tags present on your website. You\'ll want to deactivate this setting in your theme or the plugin that\'s causing this to avoid issues when sharing content on Facebook, LinkedIn, WhatsApp, or Instagram.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:88
	__( 'Dynamically creates the XML Sitemap instead of using a static file.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:87
	__( 'Dynamically Generate', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:86
	__( 'Dynamically Generate Meta Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:9
	__( 'E-E-A-T for Higher Rankings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/html-sitemap/DisplayInfo.vue:58
	// Translators: 1 - A URL.
	__( 'e.g. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:144
	__( 'Easily create and manage redirects for your broken links to avoid confusing search engines and users, as well as losing valuable backlinks.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:239
	__( 'Easily display Facebook content on your WordPress site without writing any code. Comes with multiple templates, ability to embed albums, group content, reviews, live videos, comments, and reactions.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:256
	__( 'Easily display Instagram content on your WordPress site without writing any code. Comes with multiple templates, ability to show content from multiple accounts, hashtags, and more. Trusted by 1 million websites.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:273
	__( 'Easily display Twitter content in WordPress without writing any code. Comes with multiple layouts, ability to combine multiple Twitter feeds, Twitter card support, tweet moderation, and more.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:290
	__( 'Easily display YouTube videos on your WordPress site without writing any code. Comes with multiple layouts, ability to embed live streams, video filtering, ability to combine multiple channel videos, and more.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:46
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:40
	__( 'Easily generate unlimited schema markup for your content to help you rank higher in search results. Our schema validator ensures your schema works out of the box.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/seo-revisions/Upsell.vue:36
	__( 'Easy to manage revisions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:72
	// Reference: /src/vue/plugins/constants.js:30
	__( 'Edit', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/HtaccessEditor.vue:28
	__( 'Edit .htaccess', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/PostTypes.js:14
	// Translators: 1 - A noun for something that's being edited ("Post", "Page", "Article", "Product", etc.).
	__( 'Edit %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Breadcrumbs.vue:25
	__( 'Edit Breadcrumbs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/seo-preview/App.vue:93
	__( 'Edit Facebook Meta Data', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:82
	__( 'Edit Group(s)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/vue/List.vue:55
	__( 'Edit HTML Anchor:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/WpCode.vue:35
	// Reference: /src/vue/standalone/post-settings/views/General.vue:125
	// Reference: /src/vue/standalone/seo-preview/App.vue:88
	__( 'Edit Snippet', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:68
	__( 'Edit Title and Description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/seo-preview/App.vue:98
	__( 'Edit X Meta Data', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:110
	__( 'Edit Your Meta Description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:135
	// Reference: /src/vue/classes/SiteAnalysis.js:151
	// Reference: /src/vue/classes/SiteAnalysis.js:167
	// Reference: /src/vue/classes/SiteAnalysis.js:190
	// Reference: /src/vue/classes/SiteAnalysis.js:206
	// Reference: /src/vue/classes/SiteAnalysis.js:221
	// Reference: /src/vue/classes/SiteAnalysis.js:236
	// Reference: /src/vue/classes/SiteAnalysis.js:260
	// Reference: /src/vue/classes/SiteAnalysis.js:295
	__( 'Edit Your Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:63
	__( 'Edit Your Page Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:29
	__( 'Editor', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1488
	__( 'Education Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:55
	__( 'Educational', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/authenticate/Seoboost.vue:44
	__( 'Elevate your SEO with AIOSEO Writing Assistant', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:44
	__( 'Email', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/business/Contact.vue:11
	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:106
	// Reference: /src/vue/pages/settings/views/partials/Advanced/EmailSummary.vue:48
	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:34
	__( 'Email Address', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:43
	__( 'Email Debug Information', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:159
	// Reference: /src/vue/pages/settings/views/Advanced.vue:142
	__( 'Email Reports', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/index.js:77
	__( 'Email:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1315
	__( 'Emergency Service', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:38
	__( 'Emojis and Other Special Characters', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:85
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:19
	__( 'Emotional Words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:86
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:65
	__( 'Emotionally triggered headlines are likely to drive more clicks.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/partials/EeatBlur.vue:23
	__( 'Employer', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1316
	__( 'Employment Agency', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/partials/EeatBlur.vue:18
	__( 'Enable Author Info', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:19
	__( 'Enable Author SEO (E-E-A-T) on Your Site', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:85
	__( 'Enable Breadcrumbs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:122
	__( 'Enable Custom Robots.txt', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/Advanced/EmailSummary.vue:47
	__( 'Enable email reports to receive a digest of the most important SEO updates for your site, right in your inbox.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:80
	__( 'Enable Email reports?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:76
	__( 'Enable Open Graph Markup', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:62
	__( 'Enable our Headline Analyzer to help you write irresistible headlines and rank better in search results.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:60
	__( 'Enable our TruSEO score to help you optimize your content for maximum traffic.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:57
	__( 'Enable Post Types', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:44
	__( 'Enable RSS Feed settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:121
	__( 'Enable Schema Markup', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:12
	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:10
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:57
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:145
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:43
	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:77
	__( 'Enable Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:22
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:59
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:45
	__( 'Enable Sitemap Indexes', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:75
	__( 'Enable this feature if you want Facebook and other social media to display a preview with images and a text excerpt when a link to your site is shared.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:65
	__( 'Enable this feature if you want X to display a preview card with images and a text excerpt when a link to your site is shared.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:99
	__( 'Enable this option to show additional X data on your posts and pages (i.e., who the post was written by and how long it might take to read the article).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:66
	__( 'Enable X Card', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:31
	__( 'Enabled', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:29
	__( 'Enables advanced settings that help to prevent spammers from abusing your internal site search.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/DatePicker.vue:83
	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:60
	__( 'End Date', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/StartEndWords.vue:15
	__( 'Ending Words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:134
	__( 'Ensure your most important keywords appear in the H1 tag - don\'t force it, use them in a natural way that makes sense to human readers.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:62
	__( 'Ensure your page\'s title includes your target keywords, and design it to encourage users to click.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Editor.vue:78
	__( 'Enter a custom field/taxonomy name...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/TabNewScore.vue:15
	__( 'Enter a different headline than your post title to see how it compares.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:159
	__( 'Enter a Locale Code, e.g.: en_GB, es_ES', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:97
	// Translators: 1 - An example URL (e.g. https://aioseo.com/example).
	__( 'Enter a page URL, e.g. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:88
	// Translators: 1 - Oening link tag, 2 - Closing link tag.
	__( 'Enter a relative URL to redirect from or start by typing in page or post title, slug or ID. You can also use regex (%1$s)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:73
	__( 'Enter a URL and press enter', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:84
	__( 'Enter a URL or start by typing a page or post title, slug or ID.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:61
	__( 'Enter a URL to change the default Canonical URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:151
	__( 'Enter a WordPress Filter Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:130
	__( 'Enter an IP Address', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/views/AnalyzeCompetitorSite.vue:50
	__( 'Enter Competitor URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:89
	__( 'Enter License Key', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:26
	__( 'Enter one or multiple values', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:102
	// Translators: 1 - Opening HTML link tag, 2 - Closing HTML link tag.
	__( 'Enter the primary phone number for your business. Don’t have a business phone number? %1$sSee this guide on how to get one.%2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:138
	__( 'Enter the Server Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:60
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'Enter your %1$s License Key', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:65
	__( 'Enter your answer', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:106
	__( 'Enter your Facebook Admin ID here. You can enter multiple Facebook Admin IDs by separating them with a comma.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/views/HeadlineAnalyzer.vue:38
	__( 'Enter Your Headline', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:91
	__( 'Enter your verification codes below to activate webmaster tools.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1317
	__( 'Entertainment Business', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1240
	__( 'Episode', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/robots-editor/RuleErrors.vue:48
	// Translators: 1 - The table row index, 2 - A message telling this index comes is on the network level.
	__( 'Equivalent to rule #%1$s%2$s. The trailing wildcard is ignored.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:30
	__( 'Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:205
	__( 'Every page on your site should have a <link> tag with a \'rel="canonical"\' attribute. The link tag should go inside the page\'s head tag, and it should contain the page\'s "correct" URL.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1298
	__( 'Exact match all parameters in any order', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Phone.vue:23
	__( 'Example', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Score.vue:43
	// Reference: /src/vue/composables/SeoSiteScore.js:25
	// Reference: /src/vue/composables/TruSeoScore.js:17
	__( 'Excellent!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:85
	__( 'Exclude Images', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:86
	__( 'Exclude Images from your sitemap.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:9
	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:6
	__( 'Exclude Pages/Posts', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:91
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:78
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:167
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:34
	__( 'Exclude Posts / Pages', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:92
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:79
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:168
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:35
	__( 'Exclude Terms', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1489
	__( 'Exhibition Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/authenticate/Seoboost.vue:46
	__( 'Experience the power of AI-driven writing assistance seamlessly integrated into SEOBoost. Login to enhance your content creation process and boost your search rankings.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/composables/Cta.js:13
	__( 'Explore Sample Reports', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:55
	__( 'Export All Post Types', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ExportSettings.vue:44
	__( 'Export All Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:56
	__( 'Export All Taxonomies', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:57
	__( 'Export As', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:62
	__( 'Export as JSON', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:54
	__( 'Export Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ExportSettings.vue:43
	__( 'Export Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/CtaExportTaxonomies.vue:19
	// Translators: 1 - "PRO".
	__( 'Exporting Terms is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:222
	__( 'External', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/isExternalLink.js:15
	// Reference: /src/app/tru-seo/analyzer/analysis/isExternalLink.js:23
	__( 'External links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkCount.vue:45
	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkRatio.vue:42
	__( 'External Links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:188
	__( 'External:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:33
	__( 'Extract and summarize the key points from your content to provide quick insights and improve readability.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/router/paths.js:26
	// Reference: /src/vue/standalone/post-settings/views/Social.vue:40
	__( 'Facebook', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:103
	__( 'Facebook Admin ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:104
	__( 'Facebook App ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:105
	__( 'Facebook Author URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:84
	__( 'Facebook Description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:82
	__( 'Facebook Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:122
	__( 'Facebook Post', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:75
	__( 'Facebook Preview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:83
	__( 'Facebook Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:40
	__( 'Fact Check', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/Advanced/EmailSummary.vue:112
	// Translators: 1 - An email address.
	__( 'Failed to send email to %1$s. Make sure the address is correct and that your site is configured correctly to send emails.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:35
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:137
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:139
	__( 'FAQ', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/faq/lite/sidebar.js:46
	__( 'FAQ Options', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/faq/lite/sidebar.js:70
	__( 'FAQ schema is a Pro feature.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:22
	__( 'FAQs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Redirects.vue:37
	__( 'Fast Server Redirects', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:43
	__( 'Fax', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/business/Contact.vue:13
	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:33
	__( 'Fax Number', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/index.js:73
	__( 'Fax:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/router/paths.js:17
	// Reference: /src/vue/pages/feature-manager/views/Main.vue:13
	__( 'Feature Manager', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Image.js:62
	__( 'Featured Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:31
	__( 'Features:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1448
	__( 'Feeds', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1554
	__( 'Female', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1490
	__( 'Festival', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/standalones/WritingAssistantStore.js:284
	__( 'fetching competitor data', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/standalones/WritingAssistantStore.js:285
	__( 'fetching keywords data', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/standalones/WritingAssistantStore.js:289
	__( 'fetching keywords examples', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/standalones/WritingAssistantStore.js:290
	__( 'fetching report', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/standalones/WritingAssistantStore.js:282
	__( 'fetching serps', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Media.vue:123
	__( 'Filename', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/wp/AdditionalFilters.vue:20
	__( 'Filter', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Blur.vue:73
	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:140
	__( 'Filter by Group', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:28
	__( 'Filter Search', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1318
	__( 'Financial Service', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:138
	__( 'Fine-tune your site with our powerful tools including Robots.txt editor, import/export and more.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:60
	__( 'Finish Setup and Go to the Dashboard', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Image.js:67
	__( 'First Available Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Image.js:64
	__( 'First Image in Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/GoogleSearchConsole.js:54
	__( 'Fix Errors', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/calculateFleschReading.js:66
	// Reference: /src/app/tru-seo/analyzer/analysis/calculateFleschReading.js:79
	__( 'Flesch reading ease', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/calculateFleschReading.js:93
	__( 'Flesch reading ease N/A', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/researches/helpers/getKeyphraseType.js:5
	__( 'Focus keyword', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:130
	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:96
	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:81
	// Reference: /src/vue/standalone/seo-preview/views/SeoInspector.vue:39
	__( 'Focus Keyword', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseBeginningTitle.js:14
	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseBeginningTitle.js:22
	__( 'Focus Keyword at the beginning of SEO Title', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseBeginningTitle.js:23
	__( 'Focus Keyword doesn\'t appear at the beginning of SEO title.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInContent.js:16
	__( 'Focus Keyword found in content.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInTitle.js:16
	__( 'Focus Keyword found in SEO title.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInContent.js:15
	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInContent.js:23
	__( 'Focus Keyword in content', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInTitle.js:15
	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInTitle.js:23
	__( 'Focus Keyword in SEO title', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInSubHeadings.js:105
	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInSubHeadings.js:62
	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInSubHeadings.js:75
	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInSubHeadings.js:88
	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInSubHeadings.js:97
	__( 'Focus Keyword in Subheadings', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInURL.js:18
	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInURL.js:26
	__( 'Focus Keyword in URL', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInContent.js:24
	__( 'Focus Keyword not found in content.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInTitle.js:24
	__( 'Focus Keyword not found in SEO title.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInURL.js:27
	__( 'Focus Keyword not found in the URL.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseBeginningTitle.js:15
	__( 'Focus Keyword used at the beginning of SEO title.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInURL.js:19
	__( 'Focus Keyword used in the URL.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:64
	__( 'Follow on X (Twitter)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:65
	__( 'Follow on YouTube', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1234
	__( 'Food', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1319
	__( 'Food Establishment', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1491
	__( 'Food Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/PanelNewScore.vue:23
	// Reference: /src/vue/standalone/headline-analyzer/components/TabCurrentScore.vue:22
	// Translators: 1 - Score.
	__( 'For best results, you should strive for %1$d and above.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Score.vue:27
	// Reference: /src/vue/components/common/core/site-score/Analyze.vue:58
	// Translators: 1 - Opening bold HTML tag, 2 - Closing bold HTML tag, 3 - Score.
	__( 'For best results, you should strive for %1$s%3$d and above%2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/robots-editor/RuleErrors.vue:19
	__( 'For some crawlers, encountering conflicting "Crawl-delay" might lead to unpredictable behavior.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:119
	// Reference: /src/vue/classes/SiteAnalysis.js:125
	__( 'For the best user experience there should be exactly one H1 tag on each page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1283
	__( 'Forbidden', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:115
	__( 'Forever', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:17
	__( 'Formal', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:100
	__( 'Format the label used for archives page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:104
	__( 'Format the label used for the 404 error page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:102
	__( 'Format the label used for the search results page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:443
	__( 'Fortunately, every popular web server has options to prevent directory listings. They\'ll show a "403 forbidden" message instead.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1276
	__( 'Found', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:128
	__( 'Founding Date', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1589
	__( 'Four Wheel Drive', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GoogleSearchPreview.vue:53
	// Reference: /src/vue/components/lite/core/UpgradeBar.vue:25
	// Reference: /src/vue/pages/settings/views/GeneralSettings.vue:45
	// Reference: /src/vue/plugins/constants.js:1513
	__( 'Free', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/footer-links/App.vue:31
	__( 'Free Plugins', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PriorityScore.vue:51
	// Reference: /src/vue/pages/settings/views/partials/Advanced/EmailSummary.vue:49
	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:120
	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:102
	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:64
	__( 'Frequency', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:35
	// Reference: /src/vue/standalone/blocks/opening-hours/OpeningHoursSidebar.vue:33
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:57
	__( 'Friday', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:13
	__( 'Friendly', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:131
	__( 'From', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/robots-editor/RuleErrors.vue:16
	__( 'from the network level', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/link-assistant/Links.js:37
	__( 'Front Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1590
	__( 'Front Wheel Drive', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/router/paths.js:30
	__( 'Full Site Redirect', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Redirects.vue:41
	__( 'Full Site Redirects', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1529
	__( 'Full Time', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/UrlResults.vue:25
	__( 'FUTURE', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:179
	__( 'Future proof your WordPress customizations with the most popular code snippet management plugin for WordPress. Trusted by over 1,500,000+ websites for easily adding code to WordPress right from the admin area.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1235
	__( 'Game', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1292
	__( 'Gateway Timeout', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:59
	// Reference: /src/vue/plugins/constants.js:1403
	// Reference: /src/vue/standalone/post-settings/views/General.vue:122
	// Reference: /src/vue/standalone/post-settings/views/Main.vue:157
	// Reference: /src/vue/standalone/post-settings/views/ModalContent.vue:36
	__( 'General', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:74
	__( 'General Facebook Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/router/paths.js:31
	// Reference: /src/vue/pages/settings/views/Main.vue:35
	__( 'General Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/router/paths.js:17
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:51
	__( 'General Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:43
	__( 'Generate a compelling SEO title for your post to improve click-through rates and search engine visibility.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:64
	__( 'Generate a LLMs.txt file to help AI engines discover the content on your site more easily.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/GenerateReport.vue:25
	__( 'Generate a New Report', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/New.vue:38
	__( 'Generate a Report', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:93
	__( 'Generate an XML Sitemap specifically for videos on your site to help search engines find them.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:24
	__( 'Generate FAQs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:130
	__( 'Generate FAQs (10 credits)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:23
	__( 'Generate helpful FAQs based on your content to enhance user engagement and boost SEO.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:34
	__( 'Generate Key Points', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:150
	__( 'Generate Key Points (10 credits)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:54
	__( 'Generate Meta Descriptions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaDescriptionModal.vue:45
	__( 'Generate Meta Descriptions (10 credits)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:13
	__( 'Generate posts you can easily share on social media so you can reach a broader audience.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/New.vue:39
	__( 'Generate Report', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/Schema.vue:42
	__( 'Generate Schema', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:44
	__( 'Generate SEO Titles', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaTitleModal.vue:45
	__( 'Generate SEO Titles (10 credits)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:62
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:66
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:14
	__( 'Generate Social Posts', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Details.vue:24
	// Reference: /src/vue/standalone/writing-assistant/views/report/Processing.vue:19
	__( 'Generating Report For', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/New.vue:40
	__( 'Generating Report...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:40
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - "Pro" string.
	__( 'Get %1$s %2$s and Unlock all the Powerful Features', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:46
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - "Pro" string.
	__( 'Get %1$s %2$s and Unlock all the Powerful Features.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:74
	__( 'Get Additional Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:52
	// Translators: 1 - The upgrade call to action.
	__( 'Get additional keywords and many more modules! %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:100
	// Translators: 1 - Semrush.
	__( 'Get Additional Keywords with %1$s!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/AiContent/Main.vue:41
	__( 'Get advanced AI suggestions for meta tags, social media posts, FAQs, key points, and more — all perfectly tailored to your content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:13
	__( 'Get all the right tools to make sure your website shows up in Google Search.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:67
	// Translators: 1 - Plugin short name ("AIOSEO").
	__( 'Get helpful suggestions from %1$s on how to optimize your website content, so you can rank higher in search results.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:182
	// Translators: 1 - Plugin short name ("AIOSEO").
	__( 'Get improved features and faster fixes by sharing non-sensitive data via usage tracking that shows us how %1$s is being used. No personal data is tracked or stored.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:149
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - "Pro".
	__( 'Get more features in %1$s %2$s:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/seoboost/ReportsRemaining.vue:26
	__( 'Get More Reports', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/publish-panel/PostPublish.vue:25
	__( 'Get out the word!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/lite/overview/Overview.vue:33
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:174
	__( 'Get relevant suggestions for adding internal links to all your content as well as finding any orphaned posts that have no internal links.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:123
	__( 'Get relevant suggestions for adding internal links to older content as well as finding any orphaned posts that have no internal links.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/MiIntro.vue:58
	__( 'Get Started with Google Analytics for Free', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Help.vue:54
	__( 'Get Support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:43
	__( 'Get the #1 analytics plugin to see how people find and use your website. Simply put, see stats that matter.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:53
	__( 'Get the #1 conversion optimization plugin to convert your growing website traffic into subscribers, leads and sales.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:33
	__( 'Get the best tool to monitor your site for broken links and easily fix them to improve your SEO.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:26
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'Get the most out of %1$s by upgrading to Pro and unlocking all of the powerful features.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:181
	// Translators: 1 - "Baidu Webmaster Tools".
	__( 'Get your Baidu verification code in %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:149
	// Translators: 1 - "Bing Webmaster Tools".
	__( 'Get your Bing verification code in %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:255
	// Translators: 1 - "Google Tag Manager account".
	__( 'Get your Google Tag Manager ID in your %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:133
	// Translators: 1 - "Google Search Console".
	__( 'Get your Google verification code in %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:197
	// Translators: 1 - "Pinterest account".
	__( 'Get your Pinterest verification code in your %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:165
	// Translators: 1 - "Yandex Webmaster Tools".
	__( 'Get your Yandex verification code in %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/router/paths.js:26
	__( 'Getting Started', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:125
	__( 'Getting started? Read the Beginners Guide', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/PanelNewScore.vue:72
	// Reference: /src/vue/standalone/headline-analyzer/components/TabCurrentScore.vue:54
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:84
	__( 'Getting There', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review.vue:42
	// Reference: /src/vue/components/common/notifications/Review2.vue:36
	__( 'Give feedback', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:57
	__( 'Global Comments RSS Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:64
	__( 'Global Robots Meta', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:53
	__( 'Global RSS Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/router/paths.js:17
	__( 'Global Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:101
	__( 'Globally control the Title attribute and Alt text for images in your content. These attributes are essential for both accessibility and SEO.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:63
	__( 'Globally control the title, alt text, description and filename for attachment pages & images that are embedded in your content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:172
	__( 'Go Back', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Welcome.vue:44
	__( 'Go back to the Dashboard', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-revisions/views/lite/Index.vue:20
	__( 'Go to editor', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:98
	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:92
	__( 'Go to Social Networks', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:137
	__( 'Go to the Advanced tab to add/edit meta keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/partials/WordsBlock.vue:40
	__( 'Goal:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:60
	__( 'Goal: ', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1285
	__( 'Gone', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:74
	// Reference: /src/vue/components/common/core/headline/Result.vue:137
	// Reference: /src/vue/components/common/core/headline/Result.vue:167
	__( 'Good', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/CharacterCount.vue:55
	// Reference: /src/vue/standalone/headline-analyzer/components/WordCount.vue:50
	__( 'Good 🙂', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/calculateFleschReading.js:38
	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseLength.js:42
	__( 'Good job!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Competitor.vue:51
	// Reference: /src/vue/composables/SeoSiteScore.js:33
	// Reference: /src/vue/pages/seo-analysis/views/SeoAuditChecklist.vue:82
	__( 'Good Results', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:121
	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:139
	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:161
	__( 'Good!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/UnwantedBots.vue:40
	__( 'Google AdsBot', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:244
	__( 'Google Analytics', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/MiIntro.vue:45
	// Reference: /src/vue/components/common/core/MiIntro.vue:55
	__( 'Google Analytics Dashboard + Real Time Stats', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/MiIntro.vue:46
	// Reference: /src/vue/components/common/core/MiIntro.vue:56
	__( 'Google Analytics Enhanced Ecommerce Tracking', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/GoogleAnalyticsSettings.vue:61
	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/GoogleAnalyticsSettings.vue:66
	// Translators: 1 - The name of one of our partner plugins.
	__( 'Google Analytics is now handled by %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/UnwantedBots.vue:43
	__( 'Google Gemini & Vertex AI Bots', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:451
	__( 'Google has not flagged this site for malware.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:451
	__( 'Google has not flagged your site for malware!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/Maps.vue:21
	// Reference: /src/vue/pages/local-seo/views/lite/maps/Maps.vue:37
	__( 'Google Maps API Key', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/maps/Maps.vue:31
	__( 'Google Places Support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:111
	__( 'Google recommends you making sure that the image looks how you intend it to look on a purely white background.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/maps/Maps.vue:32
	__( 'Google Reviews', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:455
	__( 'Google Safe browsing shows warnings and alerts to users if they visit a suspicious website. If you are flagged by Google Safe Browsing, you should take immediate steps to fix that.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:124
	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:134
	__( 'Google Search Console', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:83
	// Reference: /src/vue/plugins/constants.js:1459
	__( 'Google Search Console Integration', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/GoogleSearchConsoleSettings.vue:63
	__( 'Google Search Console is Connected.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/GoogleSearchConsole.js:61
	__( 'Google Search Console Metrics', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:256
	__( 'Google Tag Manager account', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:250
	__( 'Google Tag Manager Container ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:130
	__( 'Google Verification Code', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:276
	__( 'Google-Selected Canonical', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:90
	__( 'Google, Bing and other search engines use specific data from your schema markup to output data in their Knowledge Panels. This data is known as the Knowledge Graph. Use these settings to change how that data looks.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/page-builders/elementor/introduction.js:31
	__( 'Got It!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1320
	__( 'Government Office', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Competitors.vue:53
	__( 'Grade', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Overview.vue:29
	__( 'Grade Summary', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:122
	__( 'Granular control over the template for each post type, taxonomy and archive.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/AccessControl.vue:31
	__( 'Granular controls per role', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/MicrosoftClaritySettings.vue:70
	__( 'Great choice! Get started with MonsterInsights today to see how people find and use your website.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/subheadingsDistribution.js:37
	// Reference: /src/app/tru-seo/analyzer/analysis/subheadingsDistribution.js:75
	__( 'Great job!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/NotificationCards.vue:31
	__( 'Great Scott! Where\'d they all go?', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/isExternalLink.js:16
	__( 'Great! You are linking to external resources.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/seo-revisions/Upsell.vue:37
	__( 'Greater transparency and accountability', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Blur.vue:59
	__( 'Group', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:82
	__( 'Grouped', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/Tabs.vue:32
	// Reference: /src/vue/plugins/constants.js:1188
	__( 'Groups', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ui-element-slider/Index.vue:47
	__( 'Gutenberg Block', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ui-element-slider/Index.vue:53
	__( 'Gutenberg Blocks', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:144
	__( 'H2 tags were found on the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:144
	__( 'H2 tags were found on your page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1492
	__( 'Hackathon', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:105
	__( 'Have you connected your site to Google Search Console?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/sidebar/Keyword.vue:31
	__( 'Heading', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/HeadingPresence.vue:17
	__( 'Heading presence', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/sidebar/Keyword.vue:33
	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:30
	__( 'Heading Presence', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/sidebar/OptimizationWizard.vue:48
	__( 'Heading Presence (Highest)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/sidebar/OptimizationWizard.vue:55
	__( 'Heading Presence (Lowest)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/router/paths.js:35
	// Reference: /src/vue/pages/settings/views/Advanced.vue:61
	// Reference: /src/vue/standalone/headline-analyzer/registerHeadlineAnalyzer.js:82
	__( 'Headline Analyzer', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostColumn.vue:83
	__( 'Headline Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:59
	// Reference: /src/vue/standalone/headline-analyzer/components/PanelType.vue:11
	__( 'Headline Type', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:179
	__( 'Headlines are more likely to be clicked on in search results if they have about 6 words.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:138
	// Reference: /src/vue/standalone/headline-analyzer/components/CharacterCount.vue:70
	__( 'Headlines that are about 55 characters long will display fully in search results and tend to get more clicks.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:226
	// Reference: /src/vue/standalone/headline-analyzer/components/PanelType.vue:14
	// Translators: 1 - HTML line break tag, 2 - Opening HTML link tag, 3 - Closing HTML link tag.
	__( 'Headlines that are lists and how-to get more engagement on average than other types of headlines. %1$s%2$sLearn More%3$s →', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:199
	// Reference: /src/vue/standalone/headline-analyzer/components/Sentiment.vue:13
	__( 'Headlines that are strongly positive or negative tend to get more engagement than neutral ones.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:68
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:47
	__( 'Headlines with 20-30% common words are more likely to get clicks.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:95
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:74
	__( 'Headlines with power words are more likely to get clicks.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:56
	__( 'Headlines with uncommon words are more likely to get clicks.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1321
	__( 'Health & Beauty Business', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:80
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:75
	// Reference: /src/vue/standalone/blocks/location-map/LocationMapSidebar.vue:34
	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:80
	__( 'Height', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:74
	// Translators: 1 - Plugin short name ("AIOSEO").
	__( 'Help make %1$s better for everyone', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review.vue:53
	__( 'Help us improve', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/SearchPreview.vue:14
	__( 'Here is how your headline will look like in Google search results page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/RedirectsSideBar.vue:22
	__( 'Here you can add a redirect or view your existing redirects from this page. Click on the button below to view the Redirects panel.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/LinksSideBar.vue:22
	__( 'Here you can view an overview of your existing links as well as find suggestions for new internal links. Click on the button below to view the Link Assistant panel.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/SocialSideBar.vue:24
	__( 'Here you can view and edit the thumbnail, title and description that will be displayed when your site is shared on social media. Click on the button below to view and edit the preview.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:61
	__( 'Here\'s what to do next:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review2.vue:52
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'Hey, we noticed you have been using %1$s for some time - that’s awesome! Could you please do us a BIG favor and give it a 5-star rating on WordPress to help us spread the word and boost our motivation?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:32
	__( 'Hide', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/HeadingPresence.vue:12
	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:38
	__( 'High', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1540
	__( 'High School', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/tru-seo/ToggleHighlighter.vue:26
	__( 'Highlight sections in the Editor', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/tru-seo/ToggleHighlighter.vue:27
	__( 'Highlighting is disabled for current view', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/seo-revisions/Upsell.vue:38
	__( 'Historical record of optimization efforts', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Blur.vue:49
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:156
	__( 'Hits', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:108
	__( 'Home', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1322
	__( 'Home & Construction Business', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PriorityScore.vue:52
	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:84
	__( 'Home Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:83
	__( 'Home Page Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:67
	__( 'Home Page Meta Description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:86
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:82
	__( 'Home Page Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:84
	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:66
	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:71
	__( 'Home Page Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:95
	__( 'Homepage label', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:94
	__( 'Homepage Link', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1183
	__( 'Hotel', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1550
	__( 'Hour', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:18
	__( 'hourly', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:50
	__( 'Hours', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:45
	__( 'How To', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:85
	__( 'How to Control Search Results', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GettingStarted.vue:36
	__( 'How to Get Started', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:109
	__( 'How to get your Facebook Admin ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:110
	__( 'How to get your Facebook App ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:111
	__( 'How to get your Facebook Author URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/router/paths.js:55
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:144
	__( 'HTML Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:146
	__( 'HTML Sitemap Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1444
	__( 'HTTP Header', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1293
	__( 'HTTP Version Not Supported', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:133
	__( 'https://any-domain.com/robots.txt', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:21
	__( 'Humorous', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:48
	__( 'I have two sets of opening hours per day', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:81
	__( 'I\'ll do it later', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1286
	__( 'I\'m a Teapot', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ExcludePosts.vue:50
	// Reference: /src/vue/components/common/html-sitemap/ExcludeObjects.vue:42
	__( 'ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:40
	__( 'IDs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:467
	__( 'If you aren\'t using an SSL certificate for your site that means you are losing a lot of potential traffic. We recommend getting an SSL certificate installed immediately.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/MicrosoftClaritySettings.vue:65
	// Translators: 1 - "Clarity", 2 - Opening HTML link tag, 3 - Closing HTML link tag.
	__( 'If you don\'t already have a project on %1$s, create a project %2$shere%3$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Pinterest.vue:30
	__( 'If you have already confirmed your website with Pinterest, you can skip the step below.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:308
	__( 'If you use the Apache or NGINX web servers, you can edit the configuration files to set the "expires" header for all image files. For Apache, you can also use a ".htaccess" file to change the settings for each folder.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:398
	__( 'If you want to continue to improve your response time, the simplest and fastest fix is to use a caching plugin. Caching plugins keep a cached version of each page on your site. Instead of building the page from scratch, the server will send the cached copy.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:205
	__( 'If you\'ve republished an article from another source (such as another site or a different section of your own site) then you need to pick which URL is the "correct" one and use that!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/SitemapsWithErrorsModal.vue:70
	// Reference: /src/vue/pages/sitemaps/views/partials/SitemapsWithErrorsModal.vue:82
	__( 'Ignore', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1299
	__( 'Ignore & pass parameters to the target', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:389
	// Reference: /src/vue/plugins/constants.js:1297
	__( 'Ignore all parameters', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1300
	__( 'Ignore all parameters except UTM', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Url.vue:79
	__( 'Ignore Case', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Url.vue:78
	__( 'Ignore Slash', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:24
	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:93
	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:33
	__( 'Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostColumn.vue:79
	__( 'Image Alt Tag', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Image.js:65
	__( 'Image from Custom Field', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:66
	// Reference: /src/vue/composables/Wizard.js:61
	// Reference: /src/vue/composables/Wizard.js:62
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:157
	// Reference: /src/vue/pages/search-appearance/router/paths.js:44
	// Reference: /src/vue/pages/search-appearance/views/Media.vue:76
	// Reference: /src/vue/plugins/constants.js:1461
	__( 'Image SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/ImageSeo.vue:32
	// Translators: 1 - "PRO".
	__( 'Image SEO is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:74
	__( 'Image SEO Optimization', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:76
	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:77
	__( 'Image Source', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostColumn.vue:78
	__( 'Image Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:355
	__( 'Images:', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/contentHasAssets.js:42
	// Reference: /src/app/tru-seo/analyzer/analysis/contentHasAssets.js:50
	__( 'Images/videos in content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/router/paths.js:44
	// Reference: /src/vue/pages/local-seo/views/lite/import/Blur.vue:22
	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:119
	// Reference: /src/vue/pages/tools/views/partials/ImportAioseo.vue:44
	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:47
	// Reference: /src/vue/plugins/constants.js:33
	// Reference: /src/vue/standalone/setup-wizard/router/paths.js:26
	__( 'Import', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/router/paths.js:72
	__( 'Import / Export', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportAioseo.vue:38
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'Import / Restore %1$s Settings or Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:108
	__( 'Import Additional Pages', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:123
	__( 'Import and Delete', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Import.vue:53
	__( 'Import Data and Continue', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Import.vue:47
	__( 'Import data from your current plugins', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportAioseo.vue:41
	__( 'Import from a JSON, CSV or INI file...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:105
	__( 'Import from CSV', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:117
	__( 'Import from CSV file...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:124
	__( 'Import from URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/Import.vue:20
	// Reference: /src/vue/pages/local-seo/views/lite/import/Import.vue:28
	__( 'Import Local SEO From Other Plugins', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:125
	__( 'Import Robots.txt', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:40
	__( 'Import Settings From Other Plugins', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/import/Import.vue:35
	__( 'Import your Local SEO settings and locations from other plugins.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/router/paths.js:35
	// Reference: /src/vue/pages/tools/views/lite/CtaExportTaxonomies.vue:31
	__( 'Import/Export', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/sidebar/Keyword.vue:32
	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:32
	__( 'Importance', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/sidebar/OptimizationWizard.vue:76
	__( 'Importance (Highest)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/sidebar/OptimizationWizard.vue:83
	__( 'Importance (Lowest)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Competitor.vue:48
	// Reference: /src/vue/composables/SeoSiteScore.js:30
	// Reference: /src/vue/pages/seo-analysis/views/SeoAuditChecklist.vue:66
	__( 'Important Issues', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportAioseo.vue:43
	__( 'Imported will overwrite existing data and will not be merged.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:190
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordInner.vue:85
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:216
	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:235
	// Reference: /src/vue/standalone/post-settings/views/partials/keyword-rank-tracker/KeywordsTable.vue:100
	__( 'Impressions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:134
	__( 'Improve local SEO rankings with schema for business address, open hours, contact, and more.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SeoSetup.vue:40
	__( 'Improve SEO Rankings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/AdditionalKeyphrases.vue:40
	__( 'Improve your SEO rankings with additional keywords.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:162
	__( 'Improve your WordPress email deliverability and make sure that your website emails reach user’s inbox with the #1 SMTP plugin for WordPress. Over 3 million websites use it to fix WordPress email issues.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/seo-revisions/Upsell.vue:35
	__( 'Improved SEO strategy', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:117
	// Translators: 1 - Link to learn more.
	__( 'In order to continue searching for additional keywords, you\'ll need to upgrade. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:378
	__( 'In order to reduce page size, remove any unnecessary tags from your markup. This includes developer comments, which are invisible to your users - search engines ignore the text in comments, too.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:11
	__( 'In order to submit a News Sitemap to Google, you must have added your site to Google’s Publisher Center and had it approved.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/Statistic.vue:78
	__( 'in search results', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:50
	__( 'Inactive Plugins', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/link-assistant/InboundInternal.vue:129
	__( 'Inbound', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/link-assistant/InboundInternal.vue:124
	// Reference: /src/vue/components/common/link-assistant/InboundInternal.vue:151
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:195
	__( 'Inbound Internal', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkingOpportunities.vue:41
	__( 'Inbound Suggestions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:34
	__( 'Include', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:72
	__( 'Include All Post Type Archives', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:47
	__( 'Include all post types', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/html-sitemap/IncludedObjects.vue:44
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:82
	// Reference: /src/vue/pages/settings/views/Advanced.vue:69
	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:22
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:73
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:151
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:59
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:26
	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:76
	__( 'Include All Post Types', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/html-sitemap/IncludedObjects.vue:45
	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:75
	// Reference: /src/vue/pages/settings/views/Advanced.vue:120
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:75
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:153
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:61
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:27
	__( 'Include All Taxonomies', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:72
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:58
	__( 'Include Author Archives in your sitemap.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:70
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:56
	__( 'Include Date Archives in your sitemap.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:202
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:88
	__( 'Included as Pro Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:57
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:51
	__( 'Increase Rankings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:131
	// Reference: /src/vue/composables/Wizard.js:132
	__( 'Index Now', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/router/paths.js:71
	__( 'Index Status', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:138
	__( 'Index Status Result', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:224
	__( 'Indexed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:165
	__( 'Indexing allowed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:145
	__( 'Indexing Allowed?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:169
	// Reference: /src/vue/composables/IndexStatus.js:174
	// Reference: /src/vue/composables/IndexStatus.js:179
	__( 'Indexing not allowed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:205
	__( 'IndexNow', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:211
	__( 'IndexNow API Key', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/IndexNowSettings.vue:51
	// Translators: 1 - "PRO", 2 - "Learn more".
	__( 'IndexNow is a %1$s feature. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:33
	__( 'Indicates the index status of the page in Search Statistics. This is the verdict result for the analysis.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:81
	__( 'Indicates whether Google crawled the page as a mobile or desktop user agent. This is important because Google uses mobile-first indexing for most websites.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:193
	__( 'Indicates whether Google successfully fetched the page during its last visit.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1561
	// Reference: /src/vue/plugins/constants.js:1567
	__( 'Infants', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:25
	__( 'Informal', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:29
	__( 'Informative', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:78
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:81
	__( 'Insert FAQ Block', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:82
	__( 'Insert FAQ Blocks', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:81
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:84
	__( 'Insert Key Points Block', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:85
	__( 'Insert Key Points Blocks', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/New.vue:41
	__( 'Insert keyword that you want to rank for.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:26
	__( 'Inspect in Google Search Console', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:127
	__( 'Instagram Post', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:68
	__( 'Install', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/WebmasterTools.js:7
	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:31
	// Translators: 1 - A plugin name (e.g. "MonsterInsights", "Broken Link Checker", etc.).
	__( 'Install %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:78
	__( 'Install Free Plugin', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/WpCode.vue:65
	// Reference: /src/vue/pages/tools/views/WpCode.vue:69
	__( 'Install WPCode', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:91
	// Translators: 1 - The plugin short name ("AIOSEO Pro"), 2 - "Pro" string.
	__( 'Installing %1$s %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:74
	__( 'Installs AIOSEO Author SEO (E-E-A-T)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:64
	__( 'Installs AIOSEO Image SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:134
	__( 'Installs AIOSEO Index Now', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:124
	__( 'Installs AIOSEO Link Assistant', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:84
	__( 'Installs AIOSEO Local SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:104
	__( 'Installs AIOSEO News Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:114
	__( 'Installs AIOSEO Redirects', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:144
	__( 'Installs AIOSEO REST API', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:94
	__( 'Installs AIOSEO Video Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:34
	__( 'Installs Broken Link Checker', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:44
	__( 'Installs MonsterInsights Free', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:54
	__( 'Installs OptinMonster', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:102
	__( 'Instantly get more subscribers, leads, and sales with the #1 conversion optimization toolkit. Create high converting popups, announcement bars, spin a wheel, and more with smart targeting and personalization.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:42
	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:43
	__( 'Integrate seamlessly with SEOBoost via AIOSEO to supercharge your WordPress content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/maps/Blur.vue:15
	__( 'Integrating with Google Maps will allow your users to find exactly where your business is located. Our interactive maps let them see your Google Reviews and get directions directly from your site. Create multiple maps for use with multiple locations.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1533
	__( 'Intern', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:254
	__( 'Internal error', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/CornerstoneContent.vue:30
	__( 'internal linking recommendations in Link Assistant.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/isInternalLink.js:15
	// Reference: /src/app/tru-seo/analyzer/analysis/isInternalLink.js:23
	__( 'Internal links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkCount.vue:50
	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkRatio.vue:52
	__( 'Internal Links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1288
	__( 'Internal Server Error', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:133
	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:30
	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:32
	__( 'Internal Site Search Cleanup', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkRatio.vue:27
	__( 'Internal vs External vs Affiliate Links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:188
	__( 'Internal:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1323
	__( 'Internet Cafe', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:128
	__( 'Invalid robots.txt URL.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:258
	__( 'Invalid URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1442
	__( 'IP Address', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:73
	__( 'Is the site under construction or live (ready to be indexed)?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:143
	__( 'Issues', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:200
	// Translators: 1 - Server name, 2 - Opening link tag, 3 - Closing link tag.
	__( 'It appears that your server is running on %1$s, so the fix should be as simple as checking the %2$scorrect .htaccess implementation on wordpress.org%3$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:208
	// Translators: 1 - Opening link tag, 2 - Closing link tag.
	__( 'It appears that your server is running on nginx, so the fix will most likely require adding the correct rewrite rules to our nginx configuration. %1$sCheck our documentation for more information%2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:449
	__( 'It looks like this site has been added to one of Google\'s malwares lists.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/SeoSiteScore.js:39
	__( 'It looks like you are accessing our analyzer from a local install. Our SEO analyzer does not work on local installs because we are unable to access it. Please try again once the site has been published.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:194
	__( 'It looks like you are missing the proper rewrite rules for the robots.txt file.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:55
	__( 'It looks like you haven\'t selected any post types yet!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:449
	__( 'It looks like your site has been added to one of Google\'s malwares lists.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:150
	__( 'It should be easy to include your main and supporting keywords in the H2 tags - after all, these keywords describe your content! If it\'s hard to work the keywords into your subheadings, it could be a sign that the keywords aren\'t closely related to your content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:411
	__( 'It\'s a great idea to try and hide the plugins you have visible. From time to time vulnerabilities are found in plugins and if your site is not updated in a timely fashion, outdated plugins and themes can be exploited.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:431
	__( 'It\'s a great idea to try and hide the theme you have visible. From time to time vulnerabilities are found in themes and if your site is not updated in a timely fashion, outdated plugins and themes can be exploited.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:189
	__( 'It\'s impossible to cover every aspect of a subject on a single page, but your readers may be fascinated by some detail you barely touch on. If you link to a resource where they can learn more, they\'ll be grateful. What\'s more, you\'ll be rewarded with higher rankings!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1522
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:152
	__( 'Item Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/wp/Pagination.vue:25
	// Reference: /src/vue/components/common/core/wp/Table.vue:155
	__( 'items', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/wp/ItemsPerPage.vue:13
	__( 'items per page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:321
	__( 'JavaScript files appear in many places, including frameworks (like Bootstrap), themes and templates, and third-party plugins.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:355
	__( 'JavaScript:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:50
	__( 'Job Posting', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:63
	__( 'Join on Facebook', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:62
	__( 'Join our Community', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/flyout-menu/App.vue:50
	__( 'Join Our Community', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:67
	__( 'JSON', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:117
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'Just like WordPress, %1$s generates a dynamic file so there is no static file to be found on your server.  The content of the robots.txt file is stored in your WordPress database.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:57
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:25
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:61
	__( 'Key', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:157
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:159
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:32
	__( 'Key Points', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/researches/helpers/getKeyphraseType.js:7
	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:167
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:202
	// Reference: /src/vue/pages/search-statistics/views/partials/TopKeywords.vue:24
	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:76
	// Reference: /src/vue/standalone/post-settings/views/partials/keyword-rank-tracker/KeywordsTable.vue:77
	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/New.vue:35
	// Reference: /src/vue/standalone/writing-assistant/views/report/Details.vue:25
	// Reference: /src/vue/standalone/writing-assistant/views/report/History.vue:40
	__( 'Keyword', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rankings/Index.vue:39
	__( 'Keyword Performance', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/KeywordRankTracker.vue:24
	__( 'Keyword Performance Tracking', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:57
	// Reference: /src/vue/pages/search-statistics/views/keyword-rankings/Index.vue:37
	// Reference: /src/vue/pages/search-statistics/views/lite/dashboard/Blur.vue:43
	// Reference: /src/vue/standalone/post-settings/views/lite/KeywordRankTracker.vue:55
	__( 'Keyword Positions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/router/paths.js:51
	__( 'Keyword Rank Tracker', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:87
	__( 'Keyword Ranking Pages', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:55
	// Reference: /src/vue/pages/search-statistics/views/lite/dashboard/Blur.vue:42
	__( 'Keyword Rankings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:171
	// Translators: 1 - "PRO".
	__( 'Keyword Tracking is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:122
	// Reference: /src/vue/pages/search-appearance/views/partials/Advanced.vue:59
	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsGraphs.vue:48
	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/Tabs.vue:28
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsDistributionGraph.vue:23
	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:71
	__( 'Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1559
	// Reference: /src/vue/plugins/constants.js:1565
	__( 'Kids', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1595
	__( 'Kilometers', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:89
	__( 'Knowledge Graph', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/location-map/LocationMapSidebar.vue:42
	// Reference: /src/vue/standalone/blocks/opening-hours/OpeningHoursSidebar.vue:36
	__( 'Label', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:96
	__( 'Label used for homepage link (first item) in breadcrumbs.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostTypeOptions.vue:55
	// Reference: /src/vue/components/common/html-sitemap/IncludedObjects.vue:42
	// Reference: /src/vue/pages/search-appearance/views/ContentTypes.vue:43
	// Reference: /src/vue/pages/search-appearance/views/Taxonomies.vue:39
	__( 'Label:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:25
	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:38
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:38
	__( 'Labels', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1224
	__( 'Landmark', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/New.vue:37
	// Reference: /src/vue/standalone/writing-assistant/views/report/Details.vue:27
	// Reference: /src/vue/standalone/writing-assistant/views/report/History.vue:42
	__( 'Language', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:44
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:30
	__( 'Large', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/Main.vue:120
	// Reference: /src/vue/pages/search-statistics/views/Main.vue:163
	__( 'Last 28 Days', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/Main.vue:127
	// Reference: /src/vue/pages/search-statistics/views/Main.vue:170
	__( 'Last 3 Months', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/Main.vue:177
	__( 'Last 6 Months', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/Main.vue:113
	// Reference: /src/vue/pages/search-statistics/views/Main.vue:156
	__( 'Last 7 Days', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:161
	__( 'Last Accessed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:68
	__( 'Last Crawl', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:125
	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:103
	__( 'Last Modified', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:18
	__( 'Last Updated', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:94
	__( 'Last Updated Date', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:245
	__( 'Last Updated On', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:54
	__( 'Launch Setup Wizard', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GettingStarted.vue:47
	__( 'Launch the Setup Wizard', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:396
	__( 'Leading WordPress backup & site migration plugin. Over 1,500,000+ smart website owners use Duplicator to make reliable and secure WordPress backups to protect their websites. It also makes website migration really easy.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Pinterest.vue:29
	__( 'Learn how to get your Pinterest Verification Code', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:107
	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:112
	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:117
	// Reference: /src/vue/pages/settings/views/RssContent.vue:35
	// Reference: /src/vue/standalone/primary-term/views/lite/PrimaryTerm.vue:38
	__( 'Learn more', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/UnlicensedAddons.vue:27
	// Reference: /src/vue/composables/SeoSiteScore.js:42
	// Reference: /src/vue/composables/SeoSiteScore.js:81
	// Reference: /src/vue/composables/SeoSiteScore.js:93
	// Reference: /src/vue/plugins/constants.js:35
	// Reference: /src/vue/standalone/post-settings/views/lite/KeywordRankTracker.vue:47
	__( 'Learn More', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/cta/Index.vue:105
	__( 'Learn more about all features', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:24
	__( 'Learn more about Author SEO (E-E-A-T)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Editor.vue:79
	__( 'Learn more about Smart Tags', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/WpCode.vue:37
	__( 'Learn More about WPCode Snippets', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1324
	__( 'Legal Service', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInSubHeadings.js:65
	// Translators: 1 - The percentage of headings.
	__( 'Less than %1$s of your H2 and H3 subheadings reflect the topic of your copy. That\'s too few.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Welcome.vue:43
	__( 'Let\'s Get Started', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1449
	__( 'Libraries', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1325
	__( 'Library', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/GeneralSettings.vue:41
	__( 'License', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/LicenseKey.vue:45
	// Reference: /src/vue/standalone/setup-wizard/router/paths.js:89
	__( 'License Key', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:35
	__( 'Limit the length of internal site search queries to reduce the impact of spam attacks and confusing URLs.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:106
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:117
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:128
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:219
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:63
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:74
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:95
	__( 'Limited Support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:87
	// Reference: /src/vue/composables/Wizard.js:121
	// Reference: /src/vue/composables/Wizard.js:122
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:187
	// Reference: /src/vue/pages/dashboard/views/Main.vue:141
	// Reference: /src/vue/pages/link-assistant/views/Main.vue:50
	// Reference: /src/vue/plugins/constants.js:1458
	// Reference: /src/vue/standalone/post-settings/views/LinksSideBar.vue:21
	// Reference: /src/vue/standalone/post-settings/views/Main.vue:207
	__( 'Link Assistant', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/lite/overview/Overview.vue:30
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:171
	// Translators: 1 - "PRO".
	__( 'Link Assistant is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:107
	__( 'Link current item', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:231
	__( 'Link Suggestions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Header.vue:75
	__( 'Link suggestions are being processed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:107
	__( 'LinkedIn Post', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkingOpportunities.vue:30
	__( 'Linking Opportunities', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:239
	// Translators: 1 - The post title.
	__( 'Links & Suggestions for "%1$s"', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:20
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:61
	__( 'Links Per Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/router/paths.js:31
	__( 'Links Report', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/CustomFields.vue:38
	__( 'List of custom field names to include as post content for tags and the SEO Page Analysis. Add one per line.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/sidebar-controls.js:16
	__( 'List Style', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/router/paths.js:35
	__( 'Lite vs. Pro', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1493
	__( 'Literary Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:75
	__( 'Live Site', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:63
	__( 'LLMs.txt', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:82
	// Translators: 1 - The title of the content.
	__( 'Loading %1$s Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/FetchingModal.vue:24
	__( 'Loading new report data...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/index.js:143
	// Reference: /src/vue/standalone/blocks/location-categories/index.js:53
	// Reference: /src/vue/standalone/blocks/location-map/index.js:104
	// Reference: /src/vue/standalone/blocks/locations/index.js:69
	// Reference: /src/vue/standalone/blocks/opening-hours/index.js:125
	__( 'Loading...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Locations.vue:31
	__( 'Local Business Schema', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:173
	__( 'Local Business schema (multiple locations supported) + Business Info & Location blocks, widgets & shortcodes (Plus, Pro & Elite plans only)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:113
	__( 'Local Business schema markup enables you to tell Google about your business, including your business name, address and phone number, opening hours and price range. This information may be displayed as a Knowledge Graph card or business carousel.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Locations.vue:37
	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/OpeningHours.vue:36
	__( 'Local Business schema markup informs Google about your business details like name, address, phone number, hours, and price range, which can appear in a Knowledge Graph card or business carousel.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:73
	// Reference: /src/vue/composables/Wizard.js:82
	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:75
	__( 'Local Business SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:81
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:167
	// Reference: /src/vue/pages/dashboard/views/Main.vue:133
	// Reference: /src/vue/pages/local-seo/views/Main.vue:19
	// Reference: /src/vue/plugins/constants.js:1456
	__( 'Local SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/import/Import.vue:32
	// Reference: /src/vue/pages/local-seo/views/lite/locations/Locations.vue:42
	// Reference: /src/vue/pages/local-seo/views/lite/maps/Maps.vue:41
	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/OpeningHours.vue:41
	// Translators: 1 - "PRO".
	__( 'Local SEO is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1446
	__( 'Locale', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/router/paths.js:17
	__( 'Locations', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1326
	__( 'Lodging Business', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:39
	// Reference: /src/vue/pages/tools/views/partials/DatabaseTools.vue:51
	__( 'Log sizes may fluctuate and not always be 100% accurate since the results can be cached. Also after clearing a log, it may not show as "0" since database tables also include additional information such as indexes that we don\'t clear.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1450
	__( 'Logged In', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1451
	__( 'Logged Out', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1437
	__( 'Login Status', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/authenticate/Seoboost.vue:47
	__( 'Login to SEOBoost', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:107
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:60
	__( 'Logo', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/router/paths.js:44
	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:33
	// Reference: /src/vue/pages/tools/views/partials/DatabaseTools.vue:45
	__( 'Logs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:108
	__( 'Logs Retention', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:136
	__( 'Looking for meta keywords?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/PanelNewScore.vue:75
	// Reference: /src/vue/standalone/headline-analyzer/components/TabCurrentScore.vue:57
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:87
	__( 'Looks Good! 👍👍', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:250
	__( 'Loss', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/UnwantedBots.vue:37
	__( 'Lots of traffic comes from bots crawling the web. Some can benefit your site or business, while other bots don\'t. Blocking unwanted bots can save server resources, help with site performance, and protect copyrighted content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/HeadingPresence.vue:14
	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:40
	__( 'Low', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:94
	__( 'Lower Case', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/footer-links/App.vue:28
	__( 'Made with ♥ by the AIOSEO Team', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:99
	__( 'Major words are capitalized and minor words remain in their original casing.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:166
	__( 'Make sure every image has an alt tag, and add useful descriptions to each image. Add your keywords or synonyms - but do it in a natural way.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:255
	__( 'Make sure that you only block parts you don\'t want to be indexed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:150
	__( 'Make sure you have a good balance of H2 tags to plain text in your content. Break the content down into logical sections, and use headings to introduce each new topic.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1553
	__( 'Male', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:80
	// Reference: /src/vue/pages/about/views/AboutUs.vue:80
	// Reference: /src/vue/pages/dashboard/views/Main.vue:128
	__( 'Manage', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:140
	__( 'Manage all of your sitemap settings, including XML, Video, News and more.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:142
	__( 'Manage existing links, get relevant suggestions for adding internal links to older content, discover orphaned posts and more.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/GoogleAnalyticsSettings.vue:69
	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/MicrosoftClaritySettings.vue:72
	__( 'Manage Google Analytics', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:143
	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:125
	__( 'Manage your post and term SEO meta via the WordPress REST API. This addon also works seamlessly with headless WordPress installs.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:213
	__( 'Manage your post and term SEO meta via the WordPress REST API. This addon also works seamlessly with headless WordPress installs. (Plus, Pro & Elite plans only)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1303
	__( 'Manual Redirects', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:145
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:78
	__( 'Manually Enter Person', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1162
	__( 'Manually Enter Type', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:43
	__( 'Map', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/location-map/LocationMapSidebar.vue:32
	__( 'Map Display', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/maps/Blur.vue:17
	__( 'Map Preview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/router/paths.js:35
	// Reference: /src/vue/standalone/local-business-seo/views/Main.vue:42
	__( 'Maps', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/CornerstoneContent.vue:37
	__( 'Mark as Cornerstone', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:117
	__( 'Marketing Email', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:41
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:27
	__( 'Max Image Preview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:39
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:25
	__( 'Max Snippet', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:40
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:26
	__( 'Max Video Preview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:34
	__( 'Max. Number of Characters', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1327
	__( 'Medical Business', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1523
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:156
	__( 'Medical Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:87
	// Reference: /src/vue/pages/search-appearance/views/partials/TitleDescription.vue:65
	// Reference: /src/vue/standalone/post-settings/views/General.vue:127
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaDescriptionModal.vue:53
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaDescriptionModal.vue:55
	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:72
	__( 'Meta Description', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/metadescriptionLength.js:19
	// Reference: /src/app/tru-seo/analyzer/analysis/metadescriptionLength.js:28
	// Reference: /src/app/tru-seo/analyzer/analysis/metadescriptionLength.js:37
	// Reference: /src/app/tru-seo/analyzer/analysis/metadescriptionLength.js:46
	__( 'Meta description length', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:52
	__( 'Meta Descriptions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/seo-preview/views/MetaTags.vue:38
	__( 'Meta Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/seo-preview/views/SeoInspector.vue:44
	__( 'Meta Tags', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1594
	__( 'Miles', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ImageUploader.vue:40
	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:27
	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:110
	__( 'Minimum size: 112px x 112px, The image must be in JPG, PNG, GIF, SVG, or WEBP format.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:80
	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:83
	__( 'Minimum size: 144px x 144px, ideal ratio 1:1, 5MB max. JPG, PNG, WEBP and GIF formats only.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:85
	__( 'Minimum size: 200px x 200px, ideal ratio 1.91:1, 5MB max. (eg: 1640px x 856px or 3280px x 1712px for Retina screens). JPG, PNG, WEBP and GIF formats only.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:85
	__( 'Minimum size: 200px x 200px, ideal ratio 1.91:1, 8MB max. (eg: 1640px x 856px or 3280px x 1712px for Retina screens). JPG, PNG, WEBP and GIF formats only.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:81
	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:84
	__( 'Minimum size: 300px x 157px, ideal ratio 2:1, 5MB max. JPG, PNG, WEBP and GIF formats only.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:93
	__( 'Minor Only', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/date.js:15
	// Translators: A number will be prepended to this string, e.g. "2 minutes ago".
	__( 'minutes ago', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:92
	__( 'Miscellaneous Verification', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:168
	__( 'Missing social markup!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1447
	__( 'Mobile', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Competitor.vue:54
	__( 'Mobile Snapshot', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:101
	__( 'Mobile user agent', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/ModalContent.vue:30
	__( 'Modal Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/HeadingPresence.vue:13
	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:39
	__( 'Moderate', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1304
	__( 'Modified Posts', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:31
	// Reference: /src/vue/standalone/blocks/opening-hours/OpeningHoursSidebar.vue:29
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:53
	__( 'Monday', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:42
	__( 'MonsterInsights Free', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1547
	__( 'Month', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:21
	__( 'monthly', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/Advanced/EmailSummary.vue:62
	// Reference: /src/vue/plugins/constants.js:1479
	__( 'Monthly', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/date.js:26
	// Translators: A number will be prepended to this string, e.g. "2 months ago".
	__( 'months ago', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInSubHeadings.js:78
	// Translators: 1 - The percentage of headings.
	__( 'More than %1$s of your H2 and H3 subheadings reflect the topic of your copy. That\'s too much. Don\'t over-optimize!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:349
	__( 'More than 20 requests can result in slow page loading.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/MostLinkedDomains.vue:31
	__( 'Most Linked to Domains', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/StartEndWords.vue:13
	__( 'Most readers only look at the first and last 3 words of a headline before deciding whether to click.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1508
	__( 'Moved Online', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:388
	// Reference: /src/vue/plugins/constants.js:1275
	__( 'Moved Permanently', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1236
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:55
	__( 'Movie', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Locations.vue:32
	// Reference: /src/vue/pages/local-seo/views/lite/maps/Maps.vue:34
	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/OpeningHours.vue:32
	__( 'Multiple Locations', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:60
	__( 'Music', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:122
	__( 'Music Album', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1494
	__( 'Music Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:126
	__( 'Music Group', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1213
	__( 'Musician', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:48
	__( 'Must-Use Plugins', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/HeadingPresence.vue:15
	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:41
	__( 'n/a', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:145
	// Reference: /src/vue/plugins/constants.js:1571
	// Reference: /src/vue/standalone/post-settings/registerScoreToggler.js:27
	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:130
	__( 'N/A', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/Name.vue:7
	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:20
	__( 'name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:65
	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:30
	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:31
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:56
	__( 'Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:105
	__( 'Needs improvement', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:64
	__( 'Needs Improvement', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Score.vue:39
	// Reference: /src/vue/composables/SeoSiteScore.js:21
	// Reference: /src/vue/composables/TruSeoScore.js:13
	// Translators: 1 - HTML Line break tag.
	__( 'Needs%1$sImprovement!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:211
	// Reference: /src/vue/standalone/headline-analyzer/components/Sentiment.vue:30
	__( 'Negative', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:215
	// Reference: /src/vue/standalone/headline-analyzer/components/Sentiment.vue:17
	__( 'Negative headlines are attention-grabbing and tend to perform better than neutral ones.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:73
	__( 'Network Activated', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/NetworkSiteSelector.vue:30
	__( 'Network Admin (no site)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/router/paths.js:31
	// Reference: /src/vue/pages/settings/views/Main.vue:35
	__( 'Network Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/Main.vue:46
	__( 'Network Tools', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:42
	// Reference: /src/vue/pages/tools/views/lite/ImportExport.vue:39
	// Translators: 1 - "PRO".
	__( 'Network Tools is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:195
	// Reference: /src/vue/composables/AiContent.js:33
	// Reference: /src/vue/standalone/headline-analyzer/components/Sentiment.vue:30
	__( 'Neutral', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/Sentiment.vue:12
	__( 'Neutral Sentiment', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:23
	__( 'never', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:74
	__( 'Never', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1584
	__( 'New', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Notifications.js:10
	__( 'New Notifications', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/PanelNewScore.vue:26
	__( 'New Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/page-builders/elementor/introduction.js:13
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - The Elementor plugin name ("Elementor").
	__( 'New: %1$s %2$s integration', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Button.vue:52
	// Reference: /src/vue/components/common/core/main/Tabs.vue:65
	// Reference: /src/vue/standalone/post-settings/views/Main.vue:110
	__( 'NEW!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1562
	// Reference: /src/vue/plugins/constants.js:1568
	__( 'Newborns', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1460
	__( 'News & Video Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:45
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:116
	__( 'News Article', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:71
	__( 'News Channel', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:101
	// Reference: /src/vue/composables/Wizard.js:102
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:147
	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:5
	// Reference: /src/vue/pages/sitemaps/router/paths.js:45
	__( 'News Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:19
	// Translators: 1 - "PRO".
	__( 'News Sitemaps is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-revisions/views/lite/partials/Controls.vue:12
	__( 'Next', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:64
	// Reference: /src/vue/plugins/constants.js:36
	__( 'No', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/index.js:169
	// Reference: /src/vue/standalone/blocks/location-categories/index.js:79
	// Reference: /src/vue/standalone/blocks/location-map/index.js:130
	// Reference: /src/vue/standalone/blocks/locations/index.js:174
	// Reference: /src/vue/standalone/blocks/locations/index.js:95
	// Reference: /src/vue/standalone/blocks/opening-hours/index.js:151
	// Translators: 1 - The plural label of the custom post type.
	__( 'No %1$s found', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:93
	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:87
	// Translators: 1 - "Open Graph", 2 - "Go to Social Networks ->".
	__( 'No %1$s markup will be output for your post because it is currently disabled. You can enable %1$s markup in the Social Networks settings. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseLength.js:30
	// Translators: 1 - Focus Keyword or Keyword.
	__( 'No %1$s was set. Set a %1$s in order to calculate your SEO score.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:25
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:33
	__( 'No Archive', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:197
	__( 'No canonical link tag found on the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:197
	__( 'No canonical link tag found on your page.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInIntroduction.js:25
	__( 'No content added yet.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/contentHasAssets.js:31
	// Reference: /src/app/tru-seo/analyzer/analysis/lengthContent.js:22
	__( 'No content yet', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/seo-preview/views/SeoInspector.vue:46
	__( 'No data yet', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:152
	__( 'No Focus Keyword!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:24
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:32
	__( 'No Follow', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:33
	__( 'No Follow Paginated', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:119
	__( 'No H1 tag was found.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:142
	__( 'No H2 tags were found on the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:142
	__( 'No H2 tags were found on your page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:27
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:35
	__( 'No Image Index', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:23
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:31
	__( 'No Index', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:32
	__( 'No Index Paginated', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:34
	__( 'No Index RSS Feeds', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:176
	__( 'No internal links were found on the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:176
	__( 'No internal links were found on your page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:144
	__( 'No issues', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/wp/Table.vue:156
	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkingOpportunities.vue:31
	// Reference: /src/vue/pages/link-assistant/views/partials/overview/MostLinkedDomains.vue:33
	__( 'No items found.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/seo-preview/views/SeoInspector.vue:45
	__( 'No keyword found', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/metadescriptionLength.js:20
	__( 'No meta description has been specified. Search engines will display copy from the page instead. Make sure to write one!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:72
	__( 'No meta description was found for the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:72
	__( 'No meta description was found for your page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:29
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:37
	__( 'No ODP', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/isExternalLink.js:24
	__( 'No outbound links were found. Link out to external resources.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:79
	__( 'No Pagination for Canonical URLs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostTypeOptions.vue:57
	__( 'No post types available.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:81
	__( 'No results', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ExcludePosts.vue:48
	// Reference: /src/vue/components/common/html-sitemap/ExcludeObjects.vue:40
	__( 'No results found for your search. Try again!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:25
	__( 'No results yet', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:283
	__( 'No Schema.org data was found on the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:283
	__( 'No Schema.org data was found on your page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:28
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:36
	__( 'No Snippet', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostTypeOptions.vue:58
	__( 'No taxonomies available.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review.vue:43
	// Reference: /src/vue/components/common/notifications/Review2.vue:37
	// Reference: /src/vue/composables/Wizard.js:185
	__( 'No thanks', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/titleLength.js:26
	__( 'No title has been specified. Make sure to write one!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:26
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:34
	__( 'No Translate', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:79
	__( 'No TruSEO Score Yet', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:131
	__( 'No User-agent found in the content beginning.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/DisconnectModal.vue:30
	// Reference: /src/vue/components/common/core/FeatureCard.vue:90
	// Reference: /src/vue/composables/link-assistant/Links.js:48
	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:93
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:93
	// Reference: /src/vue/pages/search-statistics/views/partials/DisconnectModal.vue:24
	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:82
	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:53
	// Reference: /src/vue/standalone/writing-assistant/views/partials/authenticate/DisconnectModal.vue:30
	__( 'No, I changed my mind', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ResetSettings.vue:55
	__( 'No, I need to make a backup', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:14
	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:12
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:62
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:48
	__( 'Noindexed content will not be displayed in your sitemap.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1201
	__( 'Non-Profit', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:43
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:29
	// Reference: /src/vue/composables/IndexStatus.js:273
	// Reference: /src/vue/composables/IndexStatus.js:280
	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:37
	// Reference: /src/vue/pages/settings/views/Advanced.vue:95
	__( 'None', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/transitionWords.js:38
	__( 'None of the sentences contain transition words. Use some.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review.vue:41
	// Reference: /src/vue/components/common/notifications/Review2.vue:35
	__( 'Nope, maybe later', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:139
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:149
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:159
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:169
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:179
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:189
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:199
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:209
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:85
	__( 'Not Available', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:159
	__( 'Not Enough Words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/WordCount.vue:48
	__( 'Not Enough Words 🙃', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1284
	__( 'Not Found', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:230
	__( 'Not found (404)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1289
	__( 'Not Implemented', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/import/Blur.vue:23
	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:49
	__( 'not installed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:75
	// Reference: /src/vue/pages/about/views/AboutUs.vue:86
	__( 'Not Installed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/PanelNewScore.vue:66
	// Reference: /src/vue/standalone/headline-analyzer/components/TabCurrentScore.vue:48
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:78
	__( 'Not Looking Great', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1278
	__( 'Not Modified', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review.vue:39
	// Reference: /src/vue/components/common/notifications/Review2.vue:33
	__( 'Not Really...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:149
	// Translators: 1 - "Learn more link".
	__( 'Not sure what keywords are used for? Check out our documentation for more information. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:76
	// Translators: 1 - "Learn More" link.
	__( 'NOTE: Enabling this setting may cause conflicts with third-party plugins/themes. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Notifications.js:9
	__( 'Notifications', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:59
	__( 'Now Connect to Your SEOBoost Account', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/authenticate/Seoboost.vue:45
	__( 'Now Integrated into SEOBoost', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/FirstReport.vue:16
	__( 'Now you\'re ready to harness the power of data-driven insights. Create your first report and discover how to optimize your content for better search visibility and engagement.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:129
	__( 'Number of Employees', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:47
	__( 'Number of Posts', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:30
	__( 'Numbers (0-9)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:137
	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:81
	__( 'Object Type', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/wp/Pagination.vue:24
	__( 'of', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/LicenseKey.vue:43
	// Reference: /src/vue/pages/settings/views/GeneralSettings.vue:60
	// Translators: This refers to a discount (e.g. "As a valued user you receive 50%, automatically applied at checkout!").
	__( 'off', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:37
	__( 'Off', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review.vue:40
	// Reference: /src/vue/components/common/notifications/Review2.vue:34
	__( 'Ok, you deserve it', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:69
	__( 'Okay', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:38
	__( 'On', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:127
	__( 'One H1 tag was found on the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:127
	__( 'One H1 tag was found on your page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1472
	__( 'Online', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:72
	__( 'Online Store', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/transitionWords.js:52
	// Translators: 1 - Percentage of the sentences.
	__( 'Only %1$s of the sentences contain transition words, which is not enough. Use more of them.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:220
	__( 'Only ever use noindex meta tag or header on pages you want to keep out of the reach of search engines!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1473
	__( 'Onsite', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/main/Index.vue:119
	// Translators: 1 - Opening link tag, 2 - Closing link tag.
	__( 'Oops! It looks like an error occurred while saving the changes. Please try again or %1$scontact our support team%2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:26
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:46
	__( 'Open 24/7', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:38
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:43
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:44
	__( 'Open 24h', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:94
	__( 'Open Graph', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/html-sitemap/DisplayInfo.vue:61
	__( 'Open HTML Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/keyword-rank-tracker/KeywordsTable.vue:40
	__( 'Open in Keyword Rank Tracker', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/LinksSideBar.vue:23
	__( 'Open Link Assistant', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:65
	__( 'Open LLMs.txt', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:13
	__( 'Open News Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/RedirectsSideBar.vue:23
	__( 'Open Redirects', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/CardFooter.vue:13
	// Translators: 1 - Right arrow.
	__( 'Open Report %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:129
	__( 'Open Robots.txt', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:50
	__( 'Open RSS Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/SeoRevisions.vue:29
	__( 'Open SEO Revisions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:64
	__( 'Open Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:11
	__( 'Open Video Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:83
	__( 'Open Your Atom Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:59
	__( 'Open Your Comments RSS Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:90
	__( 'Open Your RDF Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:55
	// Reference: /src/vue/pages/settings/views/RssContent.vue:43
	__( 'Open Your RSS Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:62
	__( 'Open Your Static Posts Page RSS Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/UnwantedBots.vue:41
	__( 'OpenAI GPTBot', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/router/paths.js:26
	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:30
	// Reference: /src/vue/standalone/local-business-seo/views/Main.vue:37
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:34
	__( 'Opening Hours', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/OpeningHours.vue:33
	__( 'Opening Hours block, widget and shortcode', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/OpeningHours.vue:21
	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/OpeningHours.vue:37
	__( 'Opening Hours Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Main.vue:47
	__( 'Optimization Wizard', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:36
	__( 'optimization-wizard.csv', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:97
	__( 'Optimize UTM Parameters', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:20
	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:97
	__( 'Optimize your site for Google\'s E-E-A-T ranking factor by proving your writer\'s expertise through author schema markup and new UI elements.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:12
	__( 'Optimized Search Appearance', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:54
	__( 'Optimizing individual content for SEO is a low-hanging fruit of opportunity because it can often be done quickly and easily with a relatively small time investment. By making sure that each piece of content on your website is optimized for relevant keywords and follows best practices for on-page SEO, you can improve the visibility and ranking of that content in the search results. This can drive more traffic to your website and help you achieve better overall SEO results.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:98
	__( 'Optimizing your Content Headings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:52
	__( 'OptinMonster', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:39
	__( 'Or', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/BuyOrConnectButtons.vue:43
	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:57
	__( 'OR', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/Cta.vue:18
	__( 'Or upgrade to Pro to unlock additional AI credits.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:93
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:54
	__( 'Organization', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:97
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:58
	__( 'Organization Description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:96
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:57
	__( 'Organization Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1197
	__( 'Organizations', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:60
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:46
	__( 'Organize sitemap entries into distinct files in your sitemap. We recommend you enable this setting if your sitemap contains more than 1,000 URLs.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkCount.vue:60
	__( 'Orphaned Posts', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkCount.vue:35
	__( 'Orphaned posts are posts that have no inbound internal links yet and may be more difficult to find by search engines.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1536
	__( 'Other', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/MostLinkedDomains.vue:63
	__( 'other domains', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/Advanced.vue:58
	__( 'Other Options', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:77
	__( 'Other:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:62
	__( 'Our goal is to take the pain out of optimizing your website for search engines.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:117
	__( 'Our Google News Sitemap lets you control which content you submit to Google News and only contains articles that were published in the last 48 hours. In order to submit a News Sitemap to Google, you must have added your site to Google’s Publisher Center and had it approved.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/views/HeadlineAnalyzer.vue:42
	// Translators: 1 - HTML Line break tag.
	__( 'Our Headline Analyzer tool enables you to write irresistible SEO headlines%1$sthat drive traffic, shares, and rank better in search results.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:115
	// Translators: 1 - Opening HTML bold tag, 2 - Closing HTML bold tag.
	__( 'Our Local SEO addon enables you to tell Google about your business (name, address, opening hours, contact info & more) and further enhances your Knowledge Graph schema markup.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/location-map/index.js:74
	__( 'Our location:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/opening-hours/index.js:93
	__( 'Our Opening Hours:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/seo-revisions/Upsell.vue:33
	__( 'Our powerful revisions feature provides a valuable record of SEO updates, allowing you to monitor the effectiveness of your SEO efforts and make informed decisions.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:121
	__( 'Our Redirection Manager allows you to create and manage redirects for 404s or modified posts.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Redirects.vue:43
	__( 'Our Redirection Manager lets you easily create and manage redirects for broken links to avoid confusing search engines and users and prevents losing backlinks.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:170
	__( 'Our SEO News widget provides helpful links that enable you to get the most out of your SEO and help you continue to rank higher than your competitors in search results.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:165
	__( 'Our SEO Overview widget helps you determine which posts or pages you should focus on for content updates to help you rank higher in search results.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:160
	__( 'Our SEO Setup Wizard dashboard widget helps you remember to finish setting up some initial crucial settings for your site to help you rank higher in search results. Once the setup wizard is completed this widget will automatically disappear.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:39
	__( 'Our template editor will allow you to easily customize how breadcrumbs are displayed on your site based on each post type or taxonomy.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:204
	__( 'Outbound Internal', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkingOpportunities.vue:45
	__( 'Outbound Suggestions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:61
	__( 'Over the years, we found that most other WordPress SEO plugins were bloated, buggy, slow, and very hard to use. So we designed our plugin as an easy and powerful tool.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Score.vue:16
	__( 'Overall Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:32
	__( 'Override the default template for breadcrumbs on your site using our easy-to-use template editor.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/router/paths.js:21
	// Reference: /src/vue/standalone/writing-assistant/views/report/Main.vue:35
	__( 'Overview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:132
	// Reference: /src/vue/standalone/seo-preview/views/SeoInspector.vue:40
	__( 'Page Analysis', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:191
	__( 'Page Fetch', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:111
	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:100
	__( 'Page URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:88
	__( 'Paged Format', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:91
	__( 'Paginated RSS Feeds', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1511
	__( 'Paid', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/paragraphLength.js:18
	// Reference: /src/app/tru-seo/analyzer/analysis/paragraphLength.js:27
	__( 'Paragraphs length', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/GraphCard.vue:54
	__( 'Parsing Block Data...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1530
	__( 'Part Time', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1514
	__( 'Partially Free', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1294
	__( 'Pass through', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/passiveVoice.js:38
	// Reference: /src/app/tru-seo/analyzer/analysis/passiveVoice.js:47
	__( 'Passive voice', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/passiveVoice.js:17
	__( 'Passive voice is not supported in your current language.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:130
	__( 'Paste Robots.txt text', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ImageUploader.vue:41
	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:26
	__( 'Paste your image URL or select a new image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/LicenseKey.vue:47
	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:78
	__( 'Paste your license key here', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:54
	__( 'Path', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:96
	__( 'PAYG AI Credits', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:41
	__( 'Payment Info', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/PaymentInfo.vue:11
	__( 'Payment Methods Accepted', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/GraphDecay.vue:38
	__( 'Peak', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/UrlResults.vue:24
	__( 'PENDING', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1207
	__( 'People', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/views/AnalyzeCompetitorSite.vue:51
	__( 'Perform in-depth SEO Analysis of your competitor\'s website.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SeoSiteAnalysisResults.vue:41
	__( 'Performance', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:260
	__( 'Performance Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/redirects/Redirect.js:35
	__( 'Permalinks are not currently supported.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1280
	__( 'Permanent Redirect', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:92
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:65
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:53
	__( 'Person', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:98
	__( 'Person Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:91
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:51
	__( 'Person or Organization', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/App.vue:37
	__( 'Personal Options', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:37
	__( 'Persuasive', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:42
	__( 'Phone', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Phone.vue:22
	__( 'Phone number', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/business/Contact.vue:12
	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:99
	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:32
	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:59
	__( 'Phone Number', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/index.js:69
	__( 'Phone:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:37
	__( 'Phone/Fax Country Code', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ui-element-slider/Index.vue:48
	// Reference: /src/vue/components/common/core/ui-element-slider/Index.vue:54
	__( 'PHP Code', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/link-assistant/InboundInternal.vue:139
	__( 'Phrase', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/router/paths.js:44
	// Reference: /src/vue/pages/social-networks/views/Pinterest.vue:27
	__( 'Pinterest', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:198
	__( 'Pinterest account', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:189
	__( 'Pinterest Site Verification', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Pinterest.vue:28
	__( 'Pinterest uses Open Graph metadata just like Facebook, so be sure to keep Open Graph enabled on the Facebook tab checked if you want to optimize your site for Pinterest.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:194
	// Reference: /src/vue/pages/social-networks/views/Pinterest.vue:31
	__( 'Pinterest Verification Code', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1220
	__( 'Places', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/WpCode.vue:57
	__( 'Please Activate WPCode to load the AIOSEO Snippet Library', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/titleLength.js:17
	__( 'Please add a title first.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/contentHasAssets.js:32
	// Reference: /src/app/tru-seo/analyzer/analysis/lengthContent.js:23
	__( 'Please add some content first.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/location-map/index.js:268
	// Translators: 1 - The title of the location.
	__( 'Please configure the map for this location: %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/index.js:155
	// Reference: /src/vue/standalone/blocks/location-categories/index.js:65
	// Reference: /src/vue/standalone/blocks/location-map/index.js:116
	// Reference: /src/vue/standalone/blocks/locations/index.js:81
	// Reference: /src/vue/standalone/blocks/opening-hours/index.js:137
	__( 'Please enable multiple locations before using this block.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Input.vue:163
	// Translators: 1 - Minimum value, 2 - Maximum value.
	__( 'Please enter a number between %1$s and %2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Input.vue:156
	__( 'Please enter a valid email.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/redirects/Redirect.js:30
	__( 'Please enter a valid relative source URL.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/views/AnalyzeCompetitorSite.vue:53
	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:124
	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:60
	__( 'Please enter a valid URL.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/WpCode.vue:55
	// Reference: /src/vue/pages/tools/views/WpCode.vue:59
	__( 'Please Install WPCode to load the AIOSEO Snippet Library', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportAioseo.vue:50
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'Please note that if you are importing post/term meta from %1$s v3.7.1 or below, this will only be successful if the post/term IDs of this site are identical to those of the source site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/WpCode.vue:53
	__( 'Please Update WPCode to load the AIOSEO Snippet Library', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:404
	__( 'Plugins from the website are publicly visible.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:404
	__( 'Plugins from your website are publicly visible.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:31
	__( 'Plus (+)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1214
	__( 'Politician', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:75
	__( 'Portfolio', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:198
	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:84
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordInner.vue:90
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:167
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:221
	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:164
	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:240
	// Reference: /src/vue/standalone/post-settings/views/partials/keyword-rank-tracker/KeywordsTable.vue:108
	// Reference: /src/vue/standalone/post-settings/views/partials/keyword-rank-tracker/KeywordsTable.vue:39
	__( 'Position', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:206
	// Reference: /src/vue/standalone/post-settings/views/partials/keyword-rank-tracker/KeywordsTable.vue:116
	__( 'Position History', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:203
	// Reference: /src/vue/standalone/headline-analyzer/components/Sentiment.vue:30
	__( 'Positive', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:207
	// Reference: /src/vue/standalone/headline-analyzer/components/Sentiment.vue:15
	__( 'Positive headlines tend to get better engagement than neutral or negative ones.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/PostTypes.js:15
	// Reference: /src/vue/composables/PostTypes.js:8
	__( 'Post', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Image.js:66
	__( 'Post Author Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:65
	__( 'Post Comment Feeds', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:81
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:76
	__( 'Post Custom Field Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/router/paths.js:91
	__( 'Post Detail', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/Index.vue:22
	__( 'Post Index Status', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:64
	__( 'Post Meta', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:53
	// Reference: /src/vue/pages/search-statistics/views/lite/dashboard/Blur.vue:41
	__( 'Post Optimizations', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/router/paths.js:75
	__( 'Post Report', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/link-assistant/InboundInternal.vue:135
	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkingOpportunities.vue:51
	__( 'Post Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:168
	// Translators: 1 - "PRO".
	__( 'Post Tracking is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:71
	__( 'Post Type Archive Feeds', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:68
	__( 'Post Type Columns', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:83
	__( 'Post Type Priority Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PriorityScore.vue:48
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:81
	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:46
	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:8
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:67
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:149
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:53
	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:78
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:24
	__( 'Post Types', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Taxonomies.vue:41
	__( 'Post Types:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:96
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:20
	__( 'Post/Term ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1543
	__( 'Postgraduate Degree', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1506
	__( 'Postponed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkCount.vue:40
	__( 'Posts Crawled', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:64
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:75
	__( 'Posts, Pages and custom Post Types only', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:107
	__( 'Posts, Pages, Categories and Tags only', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:111
	__( 'Posts, Pages, Categories, Tags + Breadcrumb Navigation + advanced graphs (Product, FAQ Page, Recipe, etc.)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:68
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:79
	__( 'Posts, Pages, custom Post Types + Categories, Tags and custom Taxonomies', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:33
	__( 'Pound (#)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:94
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:21
	__( 'Power Words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:98
	__( 'Prefix for breadcrumb path.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/Index.vue:20
	__( 'Presence on Google', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:123
	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:72
	__( 'Press enter to create a keyword', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:27
	__( 'Press enter to create a value', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:90
	__( 'Press enter to create an article tag', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:74
	__( 'Press enter to insert a URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:40
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/social-posts/Email.vue:11
	__( 'Preview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/SocialSideBar.vue:25
	__( 'Preview & Edit', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Main.vue:109
	__( 'Preview Snippet Editor', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-revisions/views/lite/partials/Controls.vue:13
	__( 'Previous', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/PreviousScores.vue:13
	__( 'Previous Scores', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/PaymentInfo.vue:9
	__( 'Price Indicator', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PriorityScore.vue:50
	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:115
	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:101
	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:63
	__( 'Priority', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:81
	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:62
	__( 'Priority Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:67
	// Translators: 1 - "PRO", 2 - "Learn more".
	__( 'Priority Score is a %1$s feature. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:222
	__( 'Priority Support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/standalones/WritingAssistantStore.js:126
	__( 'processing keyword', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1237
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:70
	__( 'Product', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:75
	__( 'Product Review', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/tags.js:54
	__( 'Product Short Description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1229
	__( 'Products & Entertainment', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:41
	__( 'Professional', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1539
	__( 'Professional Certificate', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1328
	__( 'Professional Service', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1215
	__( 'Profile', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1524
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:160
	__( 'Profile Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GoogleSearchPreview.vue:55
	__( 'Pros and cons include', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1216
	__( 'Public Figure', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:157
	__( 'Publication Date', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1495
	__( 'Publication Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:7
	__( 'Publication Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:93
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:17
	__( 'Publish Date', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:48
	__( 'Punctuation Characters to Keep:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:82
	// Reference: /src/vue/components/common/ai/CreditCounter.vue:88
	// Reference: /src/vue/components/common/ai/CreditCounter.vue:93
	__( 'purchase a Pay-As-You-Go bundle', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:82
	__( 'Purchase and Install Now', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:90
	__( 'Purchase License', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/Main.vue:48
	__( 'purchase PAYG credits', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:96
	__( 'Query Arg Monitoring', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:92
	__( 'Query Parameters:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/standalones/WritingAssistantStore.js:281
	__( 'querying search engines', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/MiIntro.vue:44
	// Reference: /src/vue/components/common/core/MiIntro.vue:54
	__( 'Quick & Easy Google Analytics Setup', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/flyout-menu/App.vue:33
	__( 'Quick Links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:126
	__( 'Quicklinks', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/GoogleSearchConsoleSettings.vue:66
	__( 'Quickly verify ownership in Google Search Console and automatically submit sitemaps with one click. Speed up indexing, increase visibility and optimize your site\'s performance to effortlessly attract more organic traffic.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1329
	__( 'Radio Station', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/import/Import.vue:39
	__( 'Rank Math SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/Index.vue:27
	// Reference: /src/vue/pages/search-statistics/views/lite/keyword-rank-tracker/Blur.vue:15
	__( 'Rank Tracker', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GoogleSearchPreview.vue:54
	__( 'Rating', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:84
	__( 'RDF/RSS 1.0 Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/Advanced.vue:57
	__( 'Read Only', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:66
	__( 'Read our Step By Step Guide to Improve your SEO Rankings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:119
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'Read the %1$s user guide', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GettingStarted.vue:48
	__( 'Read the Setup Guide', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Analyze.vue:63
	__( 'Read the Ultimate WordPress SEO Guide', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/summary/Readability.vue:10
	// Reference: /src/vue/standalone/writing-assistant/views/report/Overview.vue:35
	__( 'readability', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:135
	// Reference: /src/vue/standalone/post-settings/views/partials/general/PageAnalysis.vue:50
	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:76
	// Reference: /src/vue/standalone/seo-preview/views/SeoInspector.vue:42
	// Reference: /src/vue/standalone/writing-assistant/views/report/Competitors.vue:51
	__( 'Readability', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1330
	__( 'Real Estate Agent', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1525
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:164
	__( 'Real Estate Listing', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1591
	__( 'Rear Wheel Drive', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:85
	__( 'Recipe', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Competitor.vue:50
	// Reference: /src/vue/composables/SeoSiteScore.js:32
	// Reference: /src/vue/pages/seo-analysis/views/SeoAuditChecklist.vue:74
	__( 'Recommended Improvements', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/GoogleSearchConsoleSettings.vue:64
	__( 'Reconnect', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:114
	__( 'Reconnect Google Search Console', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1331
	__( 'Recycling Center', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:79
	__( 'Redirect attachment pages?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Media.vue:72
	__( 'Redirect Attachment URLs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:37
	// Reference: /src/vue/pages/tools/views/partials/DatabaseTools.vue:49
	__( 'Redirect Logs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Redirects.vue:39
	__( 'Redirect Monitoring', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:42
	__( 'Redirect Pretty to "RAW" URLs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:82
	__( 'Redirect Type:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:242
	__( 'Redirection error', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:177
	// Reference: /src/vue/pages/dashboard/views/Main.vue:143
	// Reference: /src/vue/plugins/constants.js:1457
	__( 'Redirection Manager', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:80
	// Reference: /src/vue/composables/Wizard.js:112
	// Reference: /src/vue/pages/redirects/router/paths.js:21
	// Reference: /src/vue/pages/redirects/views/Main.vue:52
	// Reference: /src/vue/standalone/post-settings/views/Main.vue:175
	// Reference: /src/vue/standalone/post-settings/views/RedirectsSideBar.vue:21
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-redirects/Redirects.vue:26
	__( 'Redirects', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Redirects.vue:34
	// Translators: 1 - "PRO".
	__( 'Redirects is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:175
	__( 'Reduce Word Count', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/WordCount.vue:52
	__( 'Reduce Word Count 🙂', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1438
	__( 'Referrer', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:141
	__( 'Referring URLs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Competitor.vue:53
	// Reference: /src/vue/pages/seo-analysis/views/SeoAuditChecklist.vue:47
	__( 'Refresh Results', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:146
	__( 'Refresh Status', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/FetchingModal.vue:23
	__( 'Refreshing data', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:131
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:151
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaDescriptionModal.vue:46
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaTitleModal.vue:46
	__( 'Regenerate (5 credits)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/IndexNowSettings.vue:67
	__( 'Regenerate API Key', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:55
	// Reference: /src/vue/components/common/core/add-redirection/Url.vue:80
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:23
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:149
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:60
	__( 'Regex', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/New.vue:36
	// Reference: /src/vue/standalone/writing-assistant/views/report/History.vue:41
	__( 'Region', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:86
	__( 'Related Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:160
	// Reference: /src/vue/pages/settings/views/GeneralSettings.vue:63
	__( 'Relaunch Setup Wizard', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Url.vue:118
	// Translators: 1 - Adds a html tag with an option like: <code>Regex</code>
	__( 'Remember to enable the %1$s option if this is a regular expression.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ImageUploader.vue:42
	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:28
	// Reference: /src/vue/pages/sitemaps/views/partials/SitemapsWithErrorsModal.vue:66
	// Reference: /src/vue/pages/sitemaps/views/partials/SitemapsWithErrorsModal.vue:81
	// Reference: /src/vue/plugins/constants.js:41
	__( 'Remove', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/Advanced.vue:60
	__( 'Remove Category Base Prefix', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:83
	__( 'Remove from Group', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:165
	__( 'Remove from KRT', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:80
	__( 'Remove Keyword', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Editor.vue:80
	__( 'Remove Smart Tag', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:378
	__( 'Removing white space can also have an impact on your HTML page\'s size. White space characters like carriage returns and tabs are ignored by the browser, but they make the markup easier for developers to read. So you should always strip them from your templates or themes before you use them in a production environment.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/vue/App.vue:43
	__( 'Reorder', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/vue/lite/Reorder.vue:29
	// Translators: "PRO".
	__( 'Reordering Headings is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/vue/lite/Reorder.vue:32
	__( 'Reordering the headings in the Table of Contents block is a feature that can only be used by Pro users. Upgrade to Pro to unlock this advanced functionality.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:100
	// Translators: 1 - "utm", 2 - "#", 3 - "301", 4 - <br>, 5 - Example URL, 6 - Example URL.
	__( 'Replaces %1$s tracking parameters for Google Analytics with the (more performant) %2$s equivalent, via a %3$s redirect. e.g., %4$s %5$s will be redirected to %6$s. This also helps to prevent duplicate URLs in search results.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:54
	__( 'Report Defaults', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Details.vue:23
	__( 'Report Details', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/History.vue:39
	__( 'Report History', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/CurrentKeyword.vue:22
	__( 'Report Keyword', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/seoboost/ReportsRemaining.vue:25
	__( 'Reports', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1507
	__( 'Rescheduled', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1165
	__( 'Reservations', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:32
	// Reference: /src/vue/pages/tools/views/partials/DatabaseTools.vue:44
	__( 'Reset / Restore Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ResetSettings.vue:45
	__( 'Reset Selected Settings to Default', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/debug/WritingAssistant.vue:11
	__( 'Reset SEOBoost Logins', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:141
	// Reference: /src/vue/composables/Wizard.js:142
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:207
	__( 'REST API', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1467
	__( 'REST API support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1184
	__( 'Restaurant', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:57
	__( 'Restore', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:137
	__( 'Results of the Google Rich Results Test. These are the different schema graphs that are added to the post. Empty if no rich results were found.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:278
	__( 'Reveals the canonical URL chosen by Googlebot. Sometimes, Googlebot may select a different canonical URL than the user-declared one.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GoogleSearchPreview.vue:145
	_n( 'review', 'reviews', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:8
	__( 'Reviewed By Schema', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:136
	__( 'Rich Result Types', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:135
	__( 'Rich Results', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Advanced.vue:57
	__( 'Robots Meta', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/Advanced.vue:55
	__( 'Robots Meta Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:38
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:24
	__( 'Robots meta:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:112
	// Reference: /src/vue/composables/ToolsSettings.js:45
	__( 'Robots.txt', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/router/paths.js:17
	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:140
	__( 'Robots.txt Editor', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/RssContent.vue:45
	__( 'RSS After Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/RssContent.vue:44
	__( 'RSS Before Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:20
	// Reference: /src/vue/pages/settings/router/paths.js:69
	__( 'RSS Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/RssContent.vue:42
	__( 'RSS Content Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:43
	__( 'RSS Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:121
	__( 'RSS Feeds', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/router/paths.js:26
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:41
	__( 'RSS Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/debug/AddonsList.vue:27
	__( 'Run Action', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:67
	__( 'Run Shortcodes', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1496
	__( 'Sale Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1166
	__( 'Sales', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:67
	__( 'Same As URLs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/Main.vue:64
	// Reference: /src/vue/standalone/post-settings/views/lite/KeywordRankTracker.vue:56
	__( 'Sample data is available for you to explore. Connect your site to Google Search Console to receive insights on how content is being discovered. Identify areas for improvement and drive traffic to your website.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/tags.js:56
	__( 'Sample short description for your product.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:36
	// Reference: /src/vue/standalone/blocks/opening-hours/OpeningHoursSidebar.vue:34
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:58
	__( 'Saturday', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/vue/App.vue:44
	__( 'Save', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/page-builders/avada/limit-modified-date.js:7
	// Reference: /src/vue/standalone/page-builders/divi/limit-modified-date/main.js:13
	// Reference: /src/vue/standalone/page-builders/elementor/limit-modified-date.js:6
	// Reference: /src/vue/standalone/page-builders/seedprod/limit-modified-date.js:7
	// Reference: /src/vue/standalone/page-builders/thrive-architect/limit-modified-date.js:5
	__( 'Save (Don\'t Modify Date)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:173
	__( 'Save and Continue', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostColumn.vue:80
	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:93
	// Reference: /src/vue/components/common/core/main/Index.vue:77
	// Reference: /src/vue/components/common/core/main/Tabs.vue:64
	// Reference: /src/vue/standalone/posts-table/TermApp.vue:53
	__( 'Save Changes', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1452
	__( 'Schedule', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1504
	__( 'Scheduled', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Main.vue:169
	__( 'Schema', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:77
	__( 'Schema Catalog', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:33
	__( 'Schema Generator', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:44
	// Translators: 1 - "PRO".
	__( 'Schema Generator is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/Schema.vue:41
	__( 'Schema In Use', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/ContentTypes.vue:55
	// Reference: /src/vue/pages/search-appearance/views/Media.vue:89
	__( 'Schema Markup', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:50
	// Translators: 1 - "PRO".
	__( 'Schema Markup Generator is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:104
	__( 'Schema Rich Snippets', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:58
	__( 'Schema Templates', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:41
	__( 'Schema Type', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:70
	__( 'Schema Validation', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1202
	__( 'School', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/partials/EeatBlur.vue:21
	__( 'School, college, or university URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/TabCurrentScore.vue:25
	// Reference: /src/vue/standalone/writing-assistant/views/report/History.vue:43
	__( 'Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1497
	__( 'Screening Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Help.vue:44
	// Reference: /src/vue/components/common/core/wp/Table.vue:96
	__( 'Search', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:30
	// Reference: /src/vue/pages/dashboard/views/Main.vue:129
	// Reference: /src/vue/pages/search-appearance/views/Main.vue:27
	// Reference: /src/vue/standalone/setup-wizard/router/paths.js:62
	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:65
	__( 'Search Appearance', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:143
	__( 'Search Appearance > Advanced', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/lite/seo-statistics/Blur.vue:60
	// Reference: /src/vue/pages/search-statistics/views/seo-statistics/Index.vue:60
	__( 'Search Clicks', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/router/paths.js:71
	__( 'Search Console', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:61
	__( 'Search Engine Optimization (SEO)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/admin-bar-noindex-warning/App.vue:41
	__( 'Search Engines Blocked!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:67
	__( 'Search Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Editor.vue:77
	__( 'Search for an item...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:81
	__( 'Search for Features...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/lite/seo-statistics/Blur.vue:53
	// Reference: /src/vue/pages/search-statistics/views/partials/SeoStatisticsOverview.vue:57
	// Reference: /src/vue/pages/search-statistics/views/seo-statistics/Index.vue:53
	__( 'Search Impressions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/SearchPreview.vue:13
	__( 'Search Preview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:101
	__( 'Search Result Format', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1526
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:168
	__( 'Search Results Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:39
	__( 'Search Schema', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:145
	// Reference: /src/vue/pages/search-statistics/views/Main.vue:63
	__( 'Search Statistics', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Header.vue:79
	__( 'Search statistics are being fetched.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/composables/Cta.js:8
	// Translators: 1 - "PRO".
	__( 'Search Statistics is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:109
	__( 'search string', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/composables/Cta.js:14
	__( 'Search traffic insights', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Blur.vue:29
	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:71
	__( 'Search URLs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SeoSiteAnalysisResults.vue:42
	// Reference: /src/vue/plugins/constants.js:1421
	__( 'Security', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/MostLinkedDomains.vue:37
	__( 'See a Full Domains Report', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkRatio.vue:32
	__( 'See a Full Links Report', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:72
	__( 'See Advanced Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/lite/overview/Overview.vue:36
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:178
	__( 'See Affiliate Links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Overview.vue:36
	__( 'See All', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/link-assistant/InboundInternal.vue:149
	// Translators: 1 - The amount of links, 2 - The type of link.
	__( 'See All %1$s %2$s Links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:108
	__( 'See all dismissed notifications.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/cta/Index.vue:106
	__( 'See all features', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkingOpportunities.vue:36
	__( 'See All Linking Opportunities', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/NotificationCards.vue:33
	__( 'See Dismissed Notifications', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/sidebar/Keyword.vue:34
	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:34
	__( 'See Examples', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/lite/overview/Overview.vue:35
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:177
	__( 'See Orphaned Posts', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1277
	__( 'See Other', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:67
	__( 'See our full documentation', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/index.js:280
	// Reference: /src/vue/standalone/blocks/location-map/index.js:247
	// Reference: /src/vue/standalone/blocks/locations/index.js:190
	// Reference: /src/vue/standalone/blocks/opening-hours/index.js:261
	// Translators: 1 - The singular label of the custom post type.
	__( 'Select a %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:64
	__( 'Select a category to help us narrow down the SEO options that work best for you and your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/import/Blur.vue:21
	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:46
	__( 'Select a plugin...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:56
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:24
	__( 'Select a Value or Add a New One', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:100
	__( 'Select media to generate content for', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:86
	__( 'Select Roles', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:52
	__( 'Select Rule', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ResetSettings.vue:43
	__( 'Select Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ResetSettings.vue:44
	__( 'Select settings that you would like to reset:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:141
	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:31
	// Reference: /src/vue/pages/tools/views/partials/DatabaseTools.vue:43
	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:53
	// Reference: /src/vue/pages/tools/views/partials/ExportSettings.vue:42
	// Reference: /src/vue/pages/tools/views/partials/ImportAioseo.vue:35
	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:39
	__( 'Select Site', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:75
	__( 'Select Status', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:83
	__( 'Select the post types for which you want to automatically inject an author bio.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:48
	__( 'Select the post types you want the Writing Assistant to be available.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:129
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:149
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaDescriptionModal.vue:44
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaTitleModal.vue:44
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:99
	__( 'Select tone and audience', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:85
	// Translators: 1 - Plugin Short Name ("AIOSEO").
	__( 'Select which %1$s widgets to display on the dashboard.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:73
	__( 'Select which post type archives should include an RSS feed. This only applies to post types that include an archive page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:23
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:74
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:152
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:60
	__( 'Select which Post Types appear in your sitemap.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:72
	// Translators: 1 - Plugin Short Name ("AIOSEO").
	__( 'Select which Post Types you want to use the %1$s columns with.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:76
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:154
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:62
	__( 'Select which Taxonomies appear in your sitemap.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:76
	__( 'Select which Taxonomies should include an RSS feed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:123
	// Translators: 1 - Plugin Short Name ("AIOSEO").
	__( 'Select which Taxonomies you want to use the %1$s columns with.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:31
	__( 'Select your timezone', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:29
	__( 'Select your timezone:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1332
	__( 'Self Storage', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/Advanced/EmailSummary.vue:51
	__( 'Send Test Email', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/Advanced/EmailSummary.vue:54
	// Translators: 1 - "PRO", 2 - "Learn more".
	__( 'Sending summaries to additional email addresses is a %1$s feature. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:102
	__( 'Sentence Case', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/sentenceLength.js:37
	__( 'Sentence length is looking great!', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/sentenceLength.js:36
	// Reference: /src/app/tru-seo/analyzer/analysis/sentenceLength.js:45
	__( 'Sentences length', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:57
	// Reference: /src/vue/standalone/headline-analyzer/components/Sentiment.vue:11
	__( 'Sentiment', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:131
	// Reference: /src/vue/pages/seo-analysis/views/Main.vue:17
	// Reference: /src/vue/pages/settings/views/Advanced.vue:67
	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:71
	__( 'SEO Analysis', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/router/paths.js:17
	__( 'SEO Audit Checklist', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/GoogleSearchConsole.js:62
	__( 'SEO Changes Performance Tracking', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:69
	__( 'SEO Editor', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/AccessControl.vue:29
	__( 'SEO Editor Role', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:77
	// Reference: /src/vue/plugins/constants.js:1465
	__( 'SEO for Categories, Tags and Custom Taxonomies', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/seo-preview/App.vue:74
	__( 'SEO Inspector', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AccessControl.js:59
	__( 'SEO Manager', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/AccessControl.vue:28
	__( 'SEO Manager Role', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:169
	__( 'SEO News', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:164
	__( 'SEO Overview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/seo-preview/App.vue:50
	__( 'SEO Preview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:197
	// Reference: /src/vue/pages/tools/views/lite/CtaExportTaxonomies.vue:30
	// Reference: /src/vue/standalone/post-settings/views/Main.vue:182
	// Reference: /src/vue/standalone/post-settings/views/lite/SeoRevisions.vue:31
	__( 'SEO Revisions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-revisions/router/paths.js:17
	__( 'SEO Revisions Comparison', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/seo-revisions/Upsell.vue:30
	// Translators: 1 - "PRO".
	__( 'SEO Revisions is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:203
	__( 'SEO Revisions provide a historical record of SEO updates, allowing you to monitor the effectiveness of your SEO efforts and make informed decisions. (Plus, Pro & Elite plans only)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:63
	__( 'SEO Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:115
	__( 'SEO Setup', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:159
	__( 'SEO Setup Wizard', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:109
	__( 'SEO Site Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/router/paths.js:41
	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:59
	// Reference: /src/vue/pages/search-statistics/views/lite/dashboard/Blur.vue:44
	// Reference: /src/vue/pages/search-statistics/views/lite/seo-statistics/Blur.vue:31
	// Reference: /src/vue/pages/search-statistics/views/seo-statistics/Index.vue:31
	__( 'SEO Statistics', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaTitleModal.vue:53
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaTitleModal.vue:55
	__( 'SEO Title', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/titleLength.js:16
	// Reference: /src/app/tru-seo/analyzer/analysis/titleLength.js:25
	// Reference: /src/app/tru-seo/analyzer/analysis/titleLength.js:34
	// Reference: /src/app/tru-seo/analyzer/analysis/titleLength.js:43
	// Reference: /src/app/tru-seo/analyzer/analysis/titleLength.js:52
	__( 'SEO Title length', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/CtaExportTaxonomies.vue:28
	__( 'SEO Title/Description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:42
	__( 'SEO Titles', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:45
	__( 'SEOBoost CTA', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/debug/WritingAssistant.vue:20
	__( 'SEOBoost logins have been reset.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/import/Import.vue:40
	__( 'SEOPress', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:93
	__( 'Separator', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:77
	__( 'Separator Character', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:123
	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:57
	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:67
	__( 'SERP Preview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:124
	__( 'SERP: Search Engine Results Page preview. Your site\'s potential appearance in Google search results. Final display may vary, but this preview closely resembles it.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1443
	__( 'Server', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:238
	__( 'Server error (5xx)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:46
	__( 'Server Info', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:90
	__( 'Service', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1291
	__( 'Service Unavailable', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:6
	__( 'Set Publication Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/router/paths.js:61
	// Reference: /src/vue/pages/redirects/router/paths.js:86
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:57
	// Reference: /src/vue/pages/search-statistics/router/paths.js:82
	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/MicrosoftClaritySettings.vue:62
	// Reference: /src/vue/standalone/blocks/location-map/index.js:219
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:42
	__( 'Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:81
	// Reference: /src/vue/pages/monsterinsights/views/Monsterinsights.vue:86
	// Translators: 1 - A plugin name (e.g. "MonsterInsights", "Broken Link Checker", etc.).
	__( 'Setup %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:136
	__( 'Setup Open Graph for Facebook, X (Twitter), etc. to show the right content / thumbnail preview.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:64
	__( 'Setup Site Analyzer + Smart Recommendations', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:73
	__( 'Setup Webmaster Tools', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/GeneralSettings.vue:62
	__( 'Setup Wizard', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/publish-panel/PostPublish.vue:26
	__( 'Share your content on your favorite social media platforms to drive engagement and increase your SEO.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1333
	__( 'Shopping Center', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ui-element-slider/Index.vue:49
	__( 'Shortcode', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ui-element-slider/Index.vue:55
	__( 'Shortcodes', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:42
	__( 'Show', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/Advanced.vue:82
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'Show %1$s Meta Box', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/Advanced.vue:75
	// Translators: 1 - The type of page (Post, Page, Category, Tag, etc.).
	__( 'Show %1$s Thumbnail in Google Custom Search', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:116
	__( 'Show Blog Home', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:86
	__( 'Show Breadcrumbs on Your Website', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:106
	__( 'Show current item', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:112
	__( 'Show Facebook Author', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/location-map/LocationMapSidebar.vue:30
	__( 'Show icon', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:28
	__( 'Show icons', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/opening-hours/OpeningHoursSidebar.vue:28
	__( 'Show Icons', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/TitleDescription.vue:63
	__( 'Show in Search Results', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/location-map/LocationMapSidebar.vue:29
	__( 'Show label', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:27
	__( 'Show labels', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:28
	__( 'Show Labels', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SettingsSeparator.vue:35
	__( 'Show Less', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:63
	__( 'Show more', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SettingsSeparator.vue:34
	__( 'Show More', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:23
	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/OpeningHours.vue:31
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:37
	__( 'Show Opening Hours', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:29
	__( 'Show Publication Date', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:75
	__( 'Show Results For:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:36
	__( 'Show Tax ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/opening-hours/OpeningHoursSidebar.vue:27
	__( 'Show Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:35
	__( 'Show VAT ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:90
	__( 'Show X Author', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/maps/Maps.vue:44
	__( 'Show your location to your visitors using an interactive Google Map. Create multiple maps for use with multiple locations.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:271
	__( 'Shows the canonical URL specified by you (the website owner). Canonical URLs help indicate the preferred version of a page, especially for duplicate content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Redirects.vue:42
	__( 'Site Aliases', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:94
	__( 'Site Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:85
	__( 'Site Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/SitemapsWithErrorsModal.vue:105
	__( 'Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/SitemapsWithErrorsModal.vue:84
	__( 'Sitemap Errors', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:58
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:44
	__( 'Sitemap Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:40
	// Reference: /src/vue/composables/Wizard.js:20
	// Reference: /src/vue/pages/dashboard/views/Main.vue:139
	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:139
	// Reference: /src/vue/pages/sitemaps/views/Main.vue:21
	__( 'Sitemaps', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:21
	__( 'Sitemaps are a list of all your content that search engines use when they crawl your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:171
	__( 'Skip this Step', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/GraphDecay.vue:37
	__( 'Slowly Recovering', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostTypeOptions.vue:56
	// Reference: /src/vue/components/common/html-sitemap/IncludedObjects.vue:43
	// Reference: /src/vue/pages/search-appearance/views/ContentTypes.vue:44
	// Reference: /src/vue/pages/search-appearance/views/Taxonomies.vue:40
	__( 'Slug:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:73
	__( 'Small Offline Business', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/router/paths.js:80
	__( 'Smart Recommendations', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:111
	__( 'Smart Redirects + 404 Detection', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1455
	__( 'Smart Schema', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Main.vue:163
	// Reference: /src/vue/standalone/post-settings/views/ModalContent.vue:41
	// Reference: /src/vue/standalone/post-settings/views/Social.vue:35
	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:86
	__( 'Social', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1498
	__( 'Social Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/SocialSideBar.vue:23
	__( 'Social Media', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/CtaExportTaxonomies.vue:29
	__( 'Social Meta', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:72
	__( 'Social Meta (Open Graph Markup)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1466
	__( 'Social meta for Categories, Tags and Custom Taxonomies', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:35
	// Reference: /src/vue/pages/dashboard/views/Main.vue:135
	// Reference: /src/vue/pages/social-networks/views/Main.vue:19
	__( 'Social Networks', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:12
	__( 'Social Posts', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/router/paths.js:17
	// Reference: /src/vue/pages/social-networks/views/SocialProfiles.vue:14
	// Reference: /src/vue/standalone/user-profile-tab/App.vue:28
	__( 'Social Profiles', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:222
	__( 'Soft 404', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:95
	__( 'Software', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:327
	__( 'Some CSS files don\'t seem to be minified.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:158
	__( 'Some images on the page have no alt attribute.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:158
	__( 'Some images on your page have no alt attribute.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:314
	__( 'Some Javascript files don\'t seem to be minified.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:267
	__( 'Some Open Graph meta tags are missing.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Url.vue:156
	__( 'Some servers may be configured to serve file resources directly, preventing a redirect occurring.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:378
	__( 'Sometimes inline CSS is a culprit. A little inline CSS can help your page render faster. Too much will bloat the HTML file and increase the page loading time.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1238
	__( 'Song', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Competitors.vue:47
	// Reference: /src/vue/standalone/writing-assistant/views/sidebar/OptimizationWizard.vue:29
	__( 'Sort By', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:156
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:33
	__( 'Sort Direction', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:155
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:32
	__( 'Sort Order', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:178
	// Reference: /src/vue/pages/redirects/views/lite/redirects/Blur.vue:41
	__( 'Source URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:96
	__( 'source url set once post is published', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:178
	__( 'Source URLs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/composables/Cta.js:17
	__( 'Speed tests for individual pages/posts', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1174
	__( 'Sport', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1334
	__( 'Sports Activity Location', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1499
	__( 'Sports Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1192
	__( 'Sports League', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1193
	__( 'Sports Team', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/utils.js:53
	__( 'Stand out in search results with a meta description that sparks curiosity and drives clicks to your content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:42
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:28
	__( 'Standard', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/DatePicker.vue:82
	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:59
	__( 'Start Date', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:277
	__( 'Start Date and End Date must be different.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/business/Address.vue:13
	__( 'State', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1225
	__( 'State/Province', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:60
	__( 'Static Posts Page Feed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:31
	__( 'Status', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:82
	__( 'Status:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:160
	__( 'Stay ahead in SEO with our new email digest! Get the latest tips, trends, and tools delivered right to your inbox, helping you optimize smarter and faster. Enable it today and never miss an update that can take your rankings to the next level.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SeoSetup.vue:50
	// Reference: /src/vue/components/common/wizard/Steps.vue:16
	// Translators: 1 - The current step count. 2 - The total step count.
	__( 'Step %1$s of %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1335
	__( 'Store', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:47
	__( 'Strip Punctuation', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/ImageSeo.vue:39
	__( 'Strip punctuation from image attributes', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/subheadingsDistribution.js:36
	// Reference: /src/app/tru-seo/analyzer/analysis/subheadingsDistribution.js:45
	// Reference: /src/app/tru-seo/analyzer/analysis/subheadingsDistribution.js:64
	// Reference: /src/app/tru-seo/analyzer/analysis/subheadingsDistribution.js:74
	// Reference: /src/app/tru-seo/analyzer/analysis/subheadingsDistribution.js:83
	__( 'Subheading distribution', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/social-posts/Email.vue:10
	__( 'Subject', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:44
	__( 'Submit', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Help.vue:56
	__( 'Submit a Support Ticket', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Help.vue:55
	__( 'Submit a ticket and our world class support team will be in touch soon.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:103
	__( 'Submit articles to Google News that were published in the last 48 hours.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:153
	__( 'Submit your latest news stories to Google News (Pro & Elite plans only)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:143
	__( 'Submit your videos to search engines (Pro & Elite plans only)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1512
	__( 'Subscription', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/router/paths.js:98
	__( 'Success', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/WebmasterTools.js:10
	__( 'Success!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:59
	__( 'Success! The backup was deleted.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:60
	__( 'Success! The backup was restored.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportAioseo.vue:46
	__( 'Success! Your settings have been imported.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:218
	__( 'Successful', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/flyout-menu/App.vue:59
	__( 'Suggest a Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/PostSocial.js:17
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:70
	__( 'Summary', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/PostSocial.js:18
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:71
	__( 'Summary with Large Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:37
	// Reference: /src/vue/standalone/blocks/opening-hours/OpeningHoursSidebar.vue:35
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:59
	__( 'Sunday', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:109
	__( 'Super-charge your SEO with Link Assistant! Get relevant suggestions for adding internal links to older content as well as finding any orphaned posts that have no internal links. Use our reporting feature to see all link suggestions or add them directly from any page or post.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/PanelNewScore.vue:78
	// Reference: /src/vue/standalone/headline-analyzer/components/TabCurrentScore.vue:60
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:90
	__( 'Super! 🔥🔥🔥', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:116
	// Reference: /src/vue/standalone/footer-links/App.vue:29
	// Reference: /src/vue/standalone/footer-links/lite/SupportLink.vue:10
	__( 'Support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/flyout-menu/App.vue:41
	__( 'Support & Docs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/router/paths.js:53
	__( 'System Status', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:40
	__( 'System Status Info', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/sidebar-controls.js:14
	__( 'Table of Contents Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:83
	// Reference: /src/vue/pages/redirects/views/lite/redirects/Blur.vue:45
	__( 'Target URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:41
	__( 'Tax ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/business/Ids.vue:8
	// Reference: /src/vue/standalone/blocks/business-info/index.js:65
	__( 'Tax ID:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PriorityScore.vue:49
	// Reference: /src/vue/pages/search-appearance/router/paths.js:35
	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:44
	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:57
	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:68
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:150
	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:54
	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:82
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:25
	__( 'Taxonomies', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:119
	__( 'Taxonomy Columns', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:128
	// Translators: 1 - "PRO", 2 - "Learn more".
	__( 'Taxonomy Columns is a %1$s feature. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:74
	__( 'Taxonomy Feeds', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:84
	__( 'Taxonomy Priority Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:63
	__( 'Technical', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1167
	__( 'Technical Support', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1336
	__( 'Television Station', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:83
	__( 'Tell Google about your business for display as a Knowledge Graph card or business carousel.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1532
	__( 'Temporary', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1279
	__( 'Temporary Redirect', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:29
	__( 'Term', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/sidebar/OptimizationWizard.vue:34
	__( 'Term (A-Z)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/sidebar/OptimizationWizard.vue:41
	__( 'Term (Z-A)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:82
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:77
	__( 'Term Custom Field Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:67
	__( 'Term Meta', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:27
	__( 'Test with Google', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GettingStarted.vue:44
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'Thank you for choosing the best WordPress SEO plugin. %1$s default settings works great out of the box. We created the setup wizard to guide you through some important configuration settings & custom-tailored SEO best practices for your site to help you improve rankings.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review.vue:51
	__( 'That\'s Awesome!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/robots-editor/RuleErrors.vue:15
	__( 'The "Allow" rule takes precedence.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:95
	__( 'The "Block key" blocks both the key and all of its values, while "Block Key & values" lets you selectively block only certain values, without blocking the entire key.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:326
	__( 'The #1 affiliate management plugin for WordPress. Easily create an affiliate program for your eCommerce store or membership site within minutes and start growing your sales with the power of referral marketing.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:336
	__( 'The #1 Stripe payments plugin for WordPress. Start accepting one-time and recurring payments on your WordPress site without setting up a shopping cart. No code required.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:72
	// Translators: 1 - Company name ("Awesome Motive").
	__( 'The %1$s Team', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/SeoStatisticsOverview.vue:80
	// Translators: 1 - Opening HTML strong tag, 2 - Closing HTML strong tag.
	__( 'The %1$saverage click-through rate of your content in search results%2$s within the selected timeframe.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsSummary.vue:57
	// Translators: 1 - Opening HTML strong tag, 2 - Closing HTML strong tag.
	__( 'The %1$saverage click-through rate of your tracked keywords in search results%2$s within the selected timeframe.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/SeoStatisticsOverview.vue:90
	// Translators: 1 - Opening HTML strong tag, 2 - Closing HTML strong tag.
	__( 'The %1$saverage position of your content in search results%2$s within the selected timeframe.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/SeoStatisticsOverview.vue:70
	// Translators: 1 - Opening HTML strong tag, 2 - Closing HTML strong tag.
	__( 'The %1$stotal number of clicks that your website received from search results%2$s within the selected timeframe.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsSummary.vue:47
	// Translators: 1 - Opening HTML strong tag, 2 - Closing HTML strong tag.
	__( 'The %1$stotal number of clicks your tracked keywords have aggregated from search results%2$s within the selected timeframe.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsSummary.vue:37
	// Translators: 1 - Opening HTML strong tag, 2 - Closing HTML strong tag.
	__( 'The %1$stotal number of impressions your tracked keywords have aggregated in search results%2$s within the selected timeframe.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsSummary.vue:27
	// Translators: 1 - Opening HTML strong tag, 2 - Closing HTML strong tag.
	__( 'The %1$stotal number of keywords that are being tracked%2$s for your website.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/SeoStatisticsOverview.vue:100
	// Translators: 1 - Opening HTML strong tag, 2 - Closing HTML strong tag.
	__( 'The %1$stotal number of keywords that your website ranks for in search results%2$s within the selected timeframe.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/SeoStatisticsOverview.vue:60
	// Translators: 1 - Opening HTML strong tag, 2 - Closing HTML strong tag.
	__( 'The %1$stotal number of times your website appeared in search results%2$s within the selected timeframe.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:70
	__( 'The attachments feed allows users to subscribe to any changes to your site made to media file categories.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:64
	__( 'The authors feed allows your users to subscribe to any new content written by a specific author.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:111
	__( 'The best drag & drop WordPress form builder. Easily create beautiful contact forms, surveys, payment forms, and more with our 1000+ form templates. Trusted by over 6 million websites as the best forms plugin.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/MiIntro.vue:37
	// Reference: /src/vue/components/common/core/MiIntro.vue:47
	__( 'The Best Google Analytics Plugin for WordPress', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:353
	__( 'The best WordPress eCommerce plugin for selling digital downloads. Start selling eBooks, software, music, digital art, and more within minutes. Accept payments, manage subscriptions, advanced access control, and more.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Url.vue:134
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:129
	// Translators: 1 - Adds a html tag with an option like: <code>^</code>, 2 - Adds a html tag with an option like: <code>^</code>.
	__( 'The caret %1$s should be at the start. For example: %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:95
	// Translators: 1 - "<head></head>".
	__( 'The code above will be added between the %1$s tags on every page on your website.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/lengthContent.js:54
	// Reference: /src/app/tru-seo/analyzer/analysis/lengthContent.js:62
	__( 'The content is below the minimum of words. Add more content.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/lengthContent.js:32
	__( 'The content length is ok. Good job!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/content-rankings/Index.vue:28
	// Reference: /src/vue/pages/search-statistics/views/lite/content-rankings/Blur.vue:28
	__( 'The Content Rankings report provides valuable insights into the performance of your content in search results and helps you optimize your posts for better results. This report is generated on a monthly basis, covering the past 12 months leading up to the current month. By regularly reviewing this report, you can identify trends in your post rankings and make informed decisions to improve your content\'s visibility and ultimately increase rankings in search results.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/calculateFleschReading.js:69
	// Translators: 1 - Flesch Reading Result Number, 2 - Read difficulty string.
	__( 'The copy scores %1$s in the test, which is considered %2$s to read.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/calculateFleschReading.js:82
	// Translators: 1 - Flesch Reading Result Number, 2 - Read difficulty string, 3 - Note string.
	__( 'The copy scores %1$s in the test, which is considered %2$s to read. %3$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/AiContent/Main.vue:45
	__( 'The default audience that usually reads your content. This can be overridden while using the AI Content Generator.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/AiContent/Main.vue:43
	__( 'The default tone that characterizes your content. This can be overridden while using the AI Content Generator.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:109
	__( 'The description should stimulate reader interest and get them to click on the article. Think of it as a mini-advertisement for your content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Url.vue:149
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:137
	// Translators: 1 - The dollar symbol, 2 - Dollar symbol example.
	__( 'The dollar symbol %1$s should be at the end. For example: %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:145
	__( 'The ExactMetrics Google Analytics for WordPress plugin helps you properly setup all the powerful Google Analytics tracking features without writing any code or hiring a developer.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:107
	__( 'The Facebook App ID of the site\'s app. In order to use Facebook Insights, you must add the App ID to your page. Insights lets you view analytics for traffic to your site from Facebook. Find the App ID in your App Dashboard.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:196
	__( 'The fastest drag & drop landing page builder for WordPress. Create custom landing pages without writing code, connect them with your CRM, collect subscribers, and grow your audience. Trusted by 1 million sites.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/partials/EeatBlur.vue:17
	__( 'The fields below provide structured information for search engines about the current author. By filling out these fields, you will enhance your online presence and improve search engine visibility. This increases the chances of your author details appearing prominently in search results, making it easier for readers, publishers, and media representatives to discover and connect with you.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:120
	__( 'The file that you\'ve currently selected is not a CSV file.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:103
	__( 'The first word of each sentence starts with a capital.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Features.vue:80
	// Translators: 1 - Plugin short name ("AIOSEO"), 2 - A list of plugin names.
	__( 'The following %1$s addons will be installed: %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:191
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:223
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:255
	__( 'The following function arguments can be used to override the default settings:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Features.vue:88
	// Translators: 1 - Plugin short name ("AIOSEO"), 2 - A list of plugin names.
	__( 'The following plugins and %1$s addons will be installed: %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Features.vue:73
	// Translators: 1 - A list of plugin names.
	__( 'The following plugins will be installed: %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:60
	__( 'The following SEO Statistics graphs are useful metrics for understanding the visibility of your website or pages in search results and can help you identify trends or changes over time.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/lite/seo-statistics/Blur.vue:32
	// Reference: /src/vue/pages/search-statistics/views/seo-statistics/Index.vue:32
	__( 'The following SEO Statistics graphs are useful metrics for understanding the visibility of your website or pages in search results and can help you identify trends or changes over time.<br /><br />Note: This data is capped at the top 100 keywords per day to speed up processing and to help you prioritize your SEO efforts, so while the data may seem inconsistent with Google Search Console, this is intentional.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:61
	// Translators: 1 - Opening <code> tag, 2 - Closing </code> tag.
	__( 'The following settings will be added directly to an author\'s schema meta data via the %1$sknowsAbout%2$s property. This property helps with the Experience aspect of Google\'s E-E-A-T guidelines. After setting the global options here, you can add them directly in an authors profile page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:107
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:139
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:171
	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:125
	__( 'The following shortcode attributes can be used to override the default settings:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:140
	__( 'The function accepts an associative array with the following arguments that can be used to override the default settings:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/AreaServed.vue:7
	__( 'The geographic area where a service or offered item is provided.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:58
	__( 'The global comments feed allows users to subscribe to any new comments added to your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:54
	__( 'The global RSS feed is how users subscribe to any new content that has been created on your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:10
	__( 'The Google News Sitemap lets you control which content you submit to Google News and only contains articles that were published in the last 48 hours.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/table-of-contents/vue/List.vue:58
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'The HTML anchor allows %1$s to link directly to your header from this table of contents block. Feel free to edit if you want, but an anchor is required. For headings without an anchor, %1$s will automatically generate them.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:49
	// Translators: 1 - The default value.
	__( 'The HTML tag that is used for the label of each section. Defaults to %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/ImageSeo.vue:28
	__( 'The Image SEO module is a premium feature that enables you to globally control the title, alt tag, caption, description and filename of the images on your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/IndexNowSettings.vue:56
	__( 'The IndexNow addon is required to use this feature.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/IndexNowSettings.vue:59
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - Pro, 3 - Version Number ("1.0.0"), 4 - Addon name ("Redirects"), 5 - Version Number ("1.0.0").
	__( 'The IndexNow addon requires an update. %1$s %2$s requires a minimum version of %3$s for the %4$s addon. You currently have %5$s installed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:33
	__( 'The internal site search feature can create lots of confusing URLs for search engines, and can even be used by SEO spammers to attack your site. Most sites benefit from these protections and optimizations, even if internal search has been disabled.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/New.vue:42
	__( 'The keyword is being processed. This can take a couple of minutes.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:128
	__( 'The leading WordPress analytics plugin that shows you how people find and use your website, so you can make data driven decisions to grow your business. Properly set up Google Analytics without writing code.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:152
	__( 'The license key provided cannot be used for this domain as it has been disabled. Please use a different key to continue receiving automatic updates, or contact our support team for more information. (Error Code RW94KXEO54I2)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:148
	__( 'The license key provided has been suspended. Please use a different key to continue receiving automatic updates. (Error Code AH12BGDT45D9)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:144
	__( 'The license key provided is invalid. Please use a different key to continue receiving automatic updates.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:90
	// Translators: 1 - The length of the meta description as a number.
	__( 'The meta description is %1$d characters long, which is too long.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:80
	// Translators: 1 - The length of the meta description as a number.
	__( 'The meta description is only %1$d characters long, which is too short.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/metadescriptionLength.js:38
	__( 'The meta description is over 160 characters.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:100
	// Translators: 1 - The length of the meta description as a number.
	__( 'The meta description is set and is %1$d characters long.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/metadescriptionLength.js:29
	__( 'The meta description is too short.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:316
	__( 'The most advanced WordPress search plugin. Customize your WordPress search algorithm, reorder search results, track search metrics, and everything you need to leverage search to grow your business.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:70
	__( 'The name of the item the author knows about (e.g. "Amazon").', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/robots-editor/RuleErrors.vue:20
	__( 'The network rule takes precedence.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:37
	__( 'The number must be between 1 and 50.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/page-builders/divi-admin/App.vue:21
	// Translators: 1 - The plugin name ("All in One SEO"), 2 - Opening HTML link tag, 3 - Closing HTML link tag.
	__( 'The options below are disabled because you are using %1$s to manage your SEO. They can be changed in the %2$sSearch Appearance menu%3$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/partials/EeatBlur.vue:24
	__( 'The organization the author works for.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:213
	__( 'The page contains a noindex header or meta tag.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:215
	__( 'The page does not contain any noindex header or meta tag.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:182
	__( 'The page has a correct number of internal and external links.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:199
	__( 'The page is using the canonical link tag.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:345
	// Translators: 1 - The total number of page requests.
	__( 'The page makes %1$d requests.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/html-sitemap/DisplayInfo.vue:62
	__( 'The page that you have entered is invalid or already exists. Please enter a page with a unique slug.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:92
	__( 'The paginated RSS feeds are for any posts or pages that are paginated.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:66
	__( 'The post comments feed allows your users to subscribe to any new comments on a specific page or post.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:39
	__( 'The post types (by slug, comma-separated) that are included in the sitemap.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:83
	__( 'The purpose of breadcrumb navigation is to help users navigate around your website. It also helps search engines understand the structure and hierarchy of links on a web page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:69
	__( 'The query arg has been successfully added to the blocklist.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:73
	__( 'The query arg has been successfully deleted.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:71
	__( 'The query arg has been successfully unblocked.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:96
	__( 'The query arg you\'ve entered is already being blocked.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:180
	__( 'The ratio of internal links to external links is uneven.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/redirects/Redirect.js:21
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:28
	__( 'The regex syntax is invalid.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:394
	__( 'The response time is under 0.2 seconds.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:390
	// Translators: 1 - The total number of page requests.
	__( 'The response time of the page is %1$f seconds. It is recommended to keep it equal to or below 0.2 seconds.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:386
	// Translators: 1 - The total number of page requests.
	__( 'The response time of your page is %1$f seconds. It is recommended to keep it equal to or below 0.2 seconds.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:111
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - The plugin short name ("AIOSEO").
	__( 'The robots.txt editor in %1$s allows you to set up a robots.txt file for your site that will override the default robots.txt file that WordPress creates. By creating a robots.txt file with %2$s you have greater control over the instructions you give web crawlers about your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:243
	__( 'The robots.txt file is missing or unavailable.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/partials/EeatBlur.vue:20
	__( 'The school, college, or university where the author studied.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:68
	__( 'The search feed allows visitors to subscribe to your content based on a specific search term.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/SeoSiteScore.js:90
	__( 'The SEO analysis failed due to an unknown error. Please wait a moment and try again. If the issue continues to occur, then please contact our support team.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:43
	// Translators: 1 - The length of the SEO title as a number.
	__( 'The SEO title is %1$d characters long, which is too long.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:33
	// Translators: 1 - The length of the SEO title as a number.
	__( 'The SEO title is only %1$d characters long, which is too short.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:53
	// Translators: 1 - The length of the SEO title as a number.
	__( 'The SEO title is set and is %1$d characters long.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:302
	__( 'The server is not using "expires" headers for the images.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:304
	__( 'The server is using "expires" headers for the images.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:247
	__( 'The site has a robots.txt file which includes one or more "disallow" directives.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:249
	__( 'The site has a robots.txt file.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:461
	__( 'The site is not using a secure transfer protocol (https).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:463
	__( 'The site is using a secure transfer protocol (https).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:371
	// Translators: 1 - The total number of page requests.
	__( 'The size of the HTML document is %1$d KB.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:74
	// Translators: 1 - HTML code opening tag, 2 - HTML code closing tag.
	__( 'The sort direction. The supported values are %1$s and %2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:83
	// Translators: 1 - HTML code opening tag, 2 - HTML code closing tag.
	__( 'The sort order. The supported values are %1$s, %2$s, %3$s and %4$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Url.vue:108
	// Reference: /src/vue/components/common/core/add-redirection/Url.vue:142
	// Translators: 1 - Adds a html tag with an option like: <code>^/</code>
	__( 'The source URL should probably start with a %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:273
	__( 'The Start Date must be lower than the End Date.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:61
	__( 'The static posts page feed allows users to subscribe to any new content added to your blog page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/Index.vue:23
	__( 'The table below shows the index status of each of your posts, along with any extra information from Google that may be relevant as to why they are not indexed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:43
	__( 'The taxonomies (by slug, comma-separated) that are included in the sitemap.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/consecutiveSentences.js:30
	// Translators: 1 - Number of sentences.
	__( 'The text contains at least %1$d consecutive sentences starting with the same word. Try to mix things up!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:427
	__( 'The theme is not visible.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/titleLength.js:44
	__( 'The title is over 60 characters.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/titleLength.js:35
	__( 'The title is too short.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:87
	__( 'The Title of the Page or Site you are Sharing', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:71
	__( 'The URL of the item the author knows about (e.g. "https://amazon.com").', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/partials/EeatBlur.vue:22
	__( 'The URL of the school, college, or university where the author studied.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Dashboard.vue:64
	// Reference: /src/vue/pages/seo-analysis/views/AnalyzeCompetitorSite.vue:68
	__( 'The URL provided is invalid.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:8
	__( 'The Video Sitemap generates an XML Sitemap for video content on your site. Search engines use this information to display rich snippet information in search results.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:129
	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:9
	__( 'The Video Sitemap works in much the same way as the XML Sitemap module, it generates an XML Sitemap specifically for video content on your site. Search engines use this information to display rich snippet information in search results.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:66
	__( 'The way your site is displayed in search results is very important. Take some time to look over these settings and tweak as needed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:228
	__( 'The www and non-www versions of the URL are not redirected to the same site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:228
	__( 'The www and non-www versions of your URL are not redirected to the same site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1500
	__( 'Theater Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/debug/AddonsList.vue:28
	__( 'There are no active addons at the moment.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:107
	__( 'There are no new notifications at this moment.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:406
	__( 'There are no visible plugins.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:334
	__( 'There are server-side tools (including WordPress plugins) to automatically minify CSS files.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:321
	__( 'There are server-side tools (including WordPress plugins) to automatically minify JavaScript files.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/GoogleSearchConsole.js:60
	// Reference: /src/vue/pages/sitemaps/views/partials/SitemapsWithErrorsModal.vue:85
	__( 'There are sitemaps with errors', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/consecutiveSentences.js:41
	__( 'There is enough variety in your sentences. That\'s great!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:164
	__( 'There was an error connecting to the licensing API. Please try again later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportAioseo.vue:47
	__( 'There was an error importing your file. Please make sure you are uploading the correct file or it is in the proper format.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/views/SeoAuditChecklist.vue:48
	__( 'These are the results our SEO Analzyer has generated after analyzing the homepage of your website.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:38
	// Translators: 1 - Date of expiration.
	__( 'These credits will reset when your license renews on %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:32
	__( 'These credits will reset when your license renews. Your license details can be found in the Network Admin area.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:126
	__( 'These custom robots.txt rules will apply globally to your entire network. To adjust the robots.txt rules for an individual site, please choose it in the list above.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:127
	__( 'These custom robots.txt rules will apply globally to your entire network. To adjust the robots.txt rules for an individual site, please visit the dashboard for that site directly and update the settings there.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:56
	__( 'These lists can be useful for understanding the performance of specific keywords and identifying opportunities for improvement. For example, the top winning keywords may be good candidates for further optimization or promotion, while the top losing keywords may need to be reevaluated and potentially modified or replaced.<br /><br />Note: This data is capped at the top 100 keywords per day to speed up processing and to help you prioritize your SEO efforts.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:62
	// Reference: /src/vue/pages/search-statistics/views/lite/seo-statistics/Blur.vue:34
	// Reference: /src/vue/pages/search-statistics/views/seo-statistics/Index.vue:34
	__( 'These lists can be useful for understanding the performance of specific pages or posts and identifying opportunities for improvement. For example, the top winning content may be good candidates for further optimization or promotion, while the top losing may need to be reevaluated and potentially updated.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:90
	// Translators: 1 - The plugin name ("AIOSEO").
	__( 'These settings will affect all the breadcrumbs displayed by %1$s throughout your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/link-assistant/Links.js:44
	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:54
	__( 'This action cannot be undone.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ResetSettings.vue:50
	// Translators: 1 - Opening bold tag, 2 - Closing bold tag.
	__( 'This action cannot be undone. Before taking this action, we recommend that you make a %1$sfull website backup first%2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:84
	__( 'This action will block the selected query args.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:83
	__( 'This action will block this query arg.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/DisconnectModal.vue:26
	// Translators: 1 - Plugin Short Name ("AIOSEO").
	__( 'This action will disconnect %1$s from AI Content. By disconnecting from AI Content, you will no longer be able to use the AI Content features.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/DisconnectModal.vue:20
	// Translators: 1 - Plugin Short Name ("AIOSEO").
	__( 'This action will disconnect %1$s from Google Search Console. By disconnecting from Google Search Console, you will no longer receive valuable insights on how your content is being discovered.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/authenticate/DisconnectModal.vue:26
	// Translators: 1 - Plugin Short Name ("AIOSEO").
	__( 'This action will disconnect %1$s from SEOBoost. By disconnecting from SEOBoost, you will no longer be able to get actionable insights for your target keywords to help your content rank better in SERPs.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:82
	__( 'This action will permanently remove the selected query args.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:81
	__( 'This action will permanently remove this query arg.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:86
	__( 'This action will unblock the selected query args.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:85
	__( 'This action will unblock this query arg.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/addon/Update.vue:56
	// Translators: 1 - Plugin Short Name ("AIOSEO"), 2 - Pro, 3 - Version Number (e.g. "1.0.0"), 4 - Addon name (e.g. "Redirection Manager"), 5 - Version Number (e.g. "1.0.0").
	__( 'This addon requires an update. %1$s %2$s requires a minimum version of %3$s for the %4$s addon. You currently have %5$s installed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:79
	// Translators: 1 - Plugin Short Name ("AIOSEO").
	__( 'This adds %1$s to the admin toolbar for easy access to your SEO settings.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/HtaccessEditor.vue:31
	// Translators: 1 - Opening bold tag, 2 - Closing bold tag.
	__( 'This allows you to edit the .htaccess file for your site. All WordPress sites on an Apache server have a .htaccess file and we have provided you with a convenient way of editing it. Care should always be taken when editing important files from within WordPress as an incorrect change could cause WordPress to become inaccessible. %1$sBe sure to make a backup before making changes and ensure that you have FTP access to your web server and know how to access and edit files via FTP.%2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:89
	__( 'This allows you to hide plugin announcements and update details in the Notification Center.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:109
	__( 'This feature allows you to log all query arguments that are used on your site and block them. This will help prevent search engines from crawling every variation of your pages with unrecognized query arguments and help save search engine crawl quota.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/RssContent.vue:34
	__( 'This feature is used to automatically add content to your site\'s RSS feed. More specifically, it allows you to add links back to your blog and your blog posts so scrapers will automatically add these links too. This helps search engines identify you as the original source of the content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/core/upsells/RequiredPlans.vue:36
	// Reference: /src/vue/pages/search-statistics/composables/Cta.js:12
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:175
	__( 'This feature requires one of the following plans:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/core/upsells/RequiredPlans.vue:37
	__( 'This feature requires the following plan:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Widgets.js:36
	__( 'This functionality relies on widget support, which is not available in your current theme.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:58
	// Reference: /src/vue/pages/search-statistics/views/keyword-rankings/Index.vue:38
	__( 'This graph is a visual representation of how well <strong>keywords are ranking in search results over time</strong> based on their position and average CTR. This can help you understand the performance of keywords and identify any trends or fluctuations.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/Main.vue:47
	// Translators: 1 - The short plugin name ("AIOSEO"), 2 - Opening HTML link/span tag, 3 - Closing HTML span tag, 4 - Closing HTML link tag.
	__( 'This Headline Analyzer is part of %1$s to help you increase your traffic. %2$sAnalyze your site further here%3$s →%4$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/cta/Index.vue:96
	// Translators: 1 - "PRO".
	__( 'This is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:399
	__( 'This is a duplicate of a URL you are already adding. You can only add unique source URLs.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:80
	// Translators: 1 - Learn more link.
	__( 'This is a global feed of your site output in the Atom format. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RssFeeds.vue:87
	// Translators: 1 - Learn more link.
	__( 'This is a global feed of your site output in the RDF/RSS 1.0 format. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:88
	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:91
	__( 'This is a network-wide change.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/lengthContent.js:45
	__( 'This is far below the recommended minimum of words.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:364
	__( 'This is over our recommendation of 50 KB.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/Schema.vue:44
	__( 'This is the default graph for this post type. All data for this graph will be automatically generated.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:367
	__( 'This is under the average of 33 KB.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:90
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'This is what your page configured with %1$s will look like when shared via Facebook. The site title and description will be automatically added.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:160
	__( 'This license key has reached the maximum number of activations. Please deactivate it from another site, or upgrade your license to continue receiving automatic updates. (Error Code ZL53IPPJ80RF)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:70
	// Translators: 1 - The short plugin name ("AIOSEO"), 2 - "Learn More" link.
	__( 'This option allows you to control whether %1$s should parse shortcodes when generating data such as the SEO title/meta description. Enabling this setting may cause conflicts with third-party plugins/themes. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:81
	__( 'This option allows you to toggle the use of Meta Keywords throughout the whole of the site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/RssSitemap.vue:42
	__( 'This option will generate a separate RSS Sitemap which can be submitted to Google, Bing and any other search engines that support this type of sitemap. The RSS Sitemap contains an RSS feed of the latest updates to your site content. It is not a full sitemap of all your content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/redirects/Redirect.js:38
	__( 'This redirect is supported using the Relocate Site feature under Full Site Redirect tab.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/robots-editor/RuleErrors.vue:56
	// Reference: /src/vue/pages/tools/views/partials/robots-editor/RuleErrors.vue:73
	// Translators: 1 - The table row index, 2 - Warn this index is on the network level, 3 - Additional warnings.
	__( 'This rule conflicts with rule #%1$s%2$s.%3$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/robots-editor/RuleErrors.vue:40
	// Translators: 1 - The table row index, 2 - A message telling this index comes is on the network level.
	__( 'This rule is a duplicate of rule #%1$s%2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/robots-editor/RuleErrors.vue:65
	// Translators: 1 - The table row index, 2 - A message telling this index comes is on the network level.
	__( 'This rule overrides the default rule #%1$s%2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:162
	// Translators: 1 - "Learn More" link.
	__( 'This setting allows you to toggle between the regular sitemap or the compact date archive sitemap. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RobotsAlert.vue:14
	__( 'This setting can also be managed under the Robots.txt Editor and will continue to work even if Crawl Cleanup is disabled.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:158
	__( 'This setting only applies to posts and pages.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:70
	__( 'This shows the date and time when Google\'s crawler (Googlebot) last visited and crawled the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:285
	// Translators: 1 - The url to the main site.
	__( 'This site is running in a sub-directory of your main site located at %1$s. Your robots.txt file should only appear in the root directory of that site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:289
	__( 'This site runs in a sub-directory. The robots.txt file must be located at the root of the website host to which it applies.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:147
	__( 'This specifies whether your website\'s robots meta tag allows Googlebot to index the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:114
	__( 'This specifies whether your website\'s robots.txt file allows Googlebot to crawl the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:89
	__( 'This string gets appended to the titles and descriptions of paginated pages (like term or archive pages).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rankings/Index.vue:40
	__( 'This table displays the performance of keywords that your site ranks for over time, including metrics such as impressions, click-through rate, and average position in search results. It allows for easy analysis of how keywords are performing and identification of any underperforming keywords that may need to be optimized or replaced.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/competitor/CouldNotBeAnalyzed.vue:11
	__( 'This URL was unreachable when the report was run and could not be analyzed with the rest of the articles.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:79
	__( 'This will permanently remove the selected pages from the additional pages sitemap.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:78
	__( 'This will permanently remove this page from the additional pages sitemap.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:34
	// Reference: /src/vue/standalone/blocks/opening-hours/OpeningHoursSidebar.vue:32
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:56
	__( 'Thursday', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:28
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:49
	__( 'Timezone', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostColumn.vue:76
	// Reference: /src/vue/pages/search-appearance/views/Media.vue:103
	// Reference: /src/vue/pages/search-appearance/views/Media.vue:68
	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:40
	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:45
	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:55
	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:193
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordInner.vue:70
	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:214
	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:95
	// Reference: /src/vue/standalone/post-settings/views/General.vue:134
	// Reference: /src/vue/standalone/post-settings/views/partials/general/PageAnalysis.vue:46
	// Reference: /src/vue/standalone/posts-table/TermApp.vue:51
	// Reference: /src/vue/standalone/seo-preview/views/MetaTags.vue:19
	// Reference: /src/vue/standalone/seo-preview/views/SeoInspector.vue:43
	__( 'Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Archives.vue:37
	// Reference: /src/vue/pages/search-appearance/views/ContentTypes.vue:49
	// Reference: /src/vue/pages/search-appearance/views/Media.vue:83
	// Reference: /src/vue/pages/search-appearance/views/Taxonomies.vue:58
	__( 'Title & Description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/ImageSeo.vue:27
	__( 'Title Attribute Format', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:98
	__( 'Title Case', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:76
	__( 'Title Separator', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/faq/lite/sidebar.js:48
	__( 'Title Wrapper', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:132
	__( 'To', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:51
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - "Learn More" link.
	__( 'To add this block, edit a page or post and search for the "%1$s - Breadcrumbs" block. %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:110
	// Translators: 1 - Opening HTML strong tag, 2 - The plugin short name ("AIOSEO"), 3 - Closing HTML strong tag.
	__( 'To add this block, edit a page or post and search for the %1$s"%2$s - HTML Sitemap"%3$s block.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Widgets.js:42
	// Translators: 1 - Opening HTML link tag, 2 - Closing HTML link tag, 3 - Widget name
	__( 'To add this widget, visit the %1$swidgets page%2$s and look for the "%3$s" widget.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/SeoSiteScore.js:27
	__( 'to analyze a competitor site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/Cta.vue:17
	__( 'To continue using awesome AI Content features such as FAQs, Key Points, and Social Posts, you must purchase additional credits or connect to an existing account.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/SocialProfiles.vue:15
	__( 'To let search engines know which profiles are associated with this site, enter them below:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/App.vue:29
	__( 'To let search engines know which profiles are associated with this user, enter them below:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/BlockArg.vue:121
	// Translators: 1 - To prevent a greedy regular expression you can use <code>^/</code>, 2 - to anchor it to the start of the Query Arg. For example: <code>^</code>.
	__( 'To prevent a greedy regular expression you can use %1$s to anchor it to the start of the Query Arg. For example: %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Url.vue:126
	// Translators: 1 - Adds a html tag with an option like: <code>^</code>, 2 - Adds a html tag with an option like: <code>^</code>.
	__( 'To prevent a greedy regular expression you can use %1$s to anchor it to the start of the URL. For example: %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/SeoSiteScore.js:26
	__( 'to see your Site Score.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:80
	// Reference: /src/vue/components/common/ai/CreditCounter.vue:86
	// Translators: 1 - Upgrade to Pro link text. 2 - Purchase a Pay-As-You-Go bundle link text.
	// Translators: [DUPLICATE] 1 - Upgrade to higher plan link text. 2 - Purchase a Pay-As-You-Go bundle link text.
	__( 'To unlock additional credits, %1$s or %2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:92
	// Translators: 1 - Purchase a Pay-As-You-Go bundle link text.
	__( 'To unlock additional credits, %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/LicenseKey.vue:63
	// Reference: /src/vue/pages/settings/views/GeneralSettings.vue:84
	// Translators: 1 - "upgrading to Pro".
	__( 'To unlock more features, consider %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:106
	// Translators: 1 - "upgrade to Pro".
	__( 'To unlock the selected features, please %1$s and enter your license key below.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:104
	__( 'To unlock the selected features, please enter your license key below.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:95
	__( 'To use this feature, first add a focus keyword.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:66
	__( 'To view the LLMs.txt file, first save changes.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/html-sitemap/DisplayInfo.vue:70
	__( 'To view the new sitemap, first save changes.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/html-sitemap/DisplayInfo.vue:69
	__( 'To view the sitemap, enter a URL and save changes.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1560
	// Reference: /src/vue/plugins/constants.js:1566
	__( 'Toddlers', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/AiContent.js:74
	// Reference: /src/vue/pages/settings/views/partials/AiContent/Main.vue:42
	__( 'Tone', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:178
	__( 'Too few internal links on the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:178
	__( 'Too few internal links on your page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:141
	__( 'Too Long', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/CharacterCount.vue:58
	__( 'Too Long 😑', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:183
	__( 'Too Many Words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/WordCount.vue:54
	__( 'Too Many Words 😑', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:133
	__( 'Too Short', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/CharacterCount.vue:52
	// Reference: /src/vue/standalone/headline-analyzer/components/CharacterCount.vue:60
	__( 'Too Short 🙃', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:137
	// Reference: /src/vue/pages/tools/views/Main.vue:46
	__( 'Tools', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/seo-analysis/views/HeadlineAnalyzer.vue:39
	__( 'Top 10 Ways to Increase Traffic', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsGraphs.vue:24
	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsGraphs.vue:51
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsDistributionGraph.vue:26
	// Reference: /src/vue/pages/search-statistics/views/partials/keywords-graph/KeywordsGraph.vue:27
	__( 'Top 3 Position', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Overview.vue:30
	__( 'Top Competitor Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/GoogleSearchConsole.js:63
	__( 'Top Content Discovery', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/lite/overview/Overview.vue:37
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:179
	__( 'Top Domain Reports', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:69
	// Reference: /src/vue/pages/search-statistics/views/lite/dashboard/Blur.vue:50
	__( 'Top Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:83
	// Reference: /src/vue/pages/search-statistics/views/lite/dashboard/Blur.vue:64
	// Reference: /src/vue/pages/search-statistics/views/partials/WinningLosingKeywords.vue:25
	__( 'Top Losing', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:79
	// Reference: /src/vue/pages/search-statistics/views/lite/dashboard/Blur.vue:60
	__( 'Top Pages', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/Keywords.vue:21
	__( 'Top Positions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:87
	// Reference: /src/vue/pages/search-statistics/views/lite/dashboard/Blur.vue:68
	// Reference: /src/vue/pages/search-statistics/views/partials/WinningLosingKeywords.vue:24
	__( 'Top Winning', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:387
	__( 'Top-rated WordPress donation and fundraising plugin. Over 10,000+ non-profit organizations and website owners use Charitable to create fundraising campaigns and raise more money online.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:113
	// Translators: 1 - The post type plural name.
	__( 'Total %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/SeoStatisticsOverview.vue:67
	__( 'Total Clicks', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/MostLinkedDomains.vue:32
	__( 'Total External Links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/SeoStatisticsOverview.vue:97
	__( 'Total Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/partials/overview/LinkRatio.vue:28
	__( 'Total Links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/Index.vue:24
	__( 'Total Posts', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1337
	__( 'Tourist Information Center', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/keywords-graph/Upgrade.vue:22
	__( 'Track how well keywords are ranking in search results over time based on their position and average CTR. This can help you understand the performance of keywords and identify any trends or fluctuations.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:146
	__( 'Track how your site is performing in search rankings and generate reports with actionable insights.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/composables/Cta.js:16
	__( 'Track keyword rankings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/composables/Cta.js:15
	__( 'Track page rankings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/keyword-rank-tracker/KeywordsTable.vue:121
	__( 'Tracking', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/transitionWords.js:37
	// Reference: /src/app/tru-seo/analyzer/analysis/transitionWords.js:49
	// Reference: /src/app/tru-seo/analyzer/analysis/transitionWords.js:62
	__( 'Transition words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1338
	__( 'Travel Agency', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:78
	__( 'Trend', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:44
	__( 'Trial Credits', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/MetaboxAnalysisDetail.vue:30
	__( 'TruSEO cannot analyze the post while you are using the Code Editor. Please switch back to the Visual Editor to view your results.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/PostColumn.vue:82
	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:219
	__( 'TruSEO Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:59
	__( 'TruSEO Score & Content', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:56
	__( 'TruSEO scoring can improve your search engine rankings. To see TruSEO scores for your published posts, enable at least one post type by turning on "Show in Search Results" in the Search Appearance settings.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/SeoSiteScore.js:76
	__( 'Try Again', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/Main.vue:33
	// Reference: /src/vue/standalone/headline-analyzer/components/TabNewScore.vue:14
	__( 'Try New Headline', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:356
	__( 'Try to replace embedded objects with HTML5 alternatives.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:32
	// Reference: /src/vue/standalone/blocks/opening-hours/OpeningHoursSidebar.vue:30
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:54
	__( 'Tuesday', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:222
	__( 'Turn your website visitors into brand ambassadors! Easily grow your email list, website traffic, and social media followers with the most powerful giveaways & contests plugin for WordPress.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1239
	__( 'TV Show', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ExcludePosts.vue:51
	// Reference: /src/vue/components/common/html-sitemap/ExcludeObjects.vue:43
	// Reference: /src/vue/components/lite/local-business/business/Type.vue:9
	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:22
	// Reference: /src/vue/pages/redirects/views/lite/redirects/Blur.vue:54
	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:32
	__( 'Type', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Select.vue:86
	// Reference: /src/vue/components/common/core/ExcludePosts.vue:45
	// Reference: /src/vue/components/common/html-sitemap/ExcludeObjects.vue:37
	__( 'Type to search...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/Uses.vue:10
	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:33
	// Reference: /src/vue/standalone/writing-assistant/views/report/Overview.vue:33
	__( 'Typical', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Overview.vue:32
	__( 'Typical Top', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:121
	__( 'Unable to read CSV file. Please check if the file is valid and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1282
	__( 'Unauthorized', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1287
	__( 'Unavailable for Legal Reasons', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:182
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:68
	__( 'Unblock', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:91
	__( 'Unblock Query Arg', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:92
	__( 'Unblock Query Args', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:108
	__( 'Unblocked', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:76
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:17
	__( 'Uncommon Words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SearchAppearance.vue:74
	__( 'Under Construction', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:29
	__( 'Underscores (_)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:134
	// Translators: 1 - Plugin Short Name ("AIOSEO").
	__( 'Uninstall %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1555
	__( 'Unisex', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/MiIntro.vue:57
	__( 'Universal Tracking for AMP and Instant Articles', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1203
	__( 'University', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:139
	// Reference: /src/vue/composables/IndexStatus.js:185
	__( 'Unknown', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:263
	__( 'Unknown fetch state', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:62
	__( 'Unknown indexing status', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:106
	__( 'Unknown user agent', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:55
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:49
	__( 'Unlimited Schema', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/AccessControl.vue:32
	__( 'Unlock Access Control', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:87
	__( 'Unlock All Features', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:21
	__( 'Unlock Author SEO (E-E-A-T)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/Breadcrumbs.vue:33
	__( 'Unlock Breadcrumb Templates', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/CustomFields.vue:45
	__( 'Unlock Custom Fields', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Taxonomies.vue:42
	// Reference: /src/vue/pages/tools/views/lite/CtaExportTaxonomies.vue:33
	__( 'Unlock Custom Taxonomies', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:40
	__( 'Unlock Domain Activations', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/ImageSeo.vue:29
	__( 'Unlock Image SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:168
	__( 'Unlock Keyword Tracking', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/link-assistant/views/lite/overview/Overview.vue:27
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-links/Links.vue:168
	__( 'Unlock Link Assistant', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/import/Import.vue:29
	// Reference: /src/vue/pages/local-seo/views/lite/locations/Locations.vue:39
	// Reference: /src/vue/pages/local-seo/views/lite/maps/Maps.vue:38
	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/OpeningHours.vue:38
	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:120
	__( 'Unlock Local SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:45
	// Reference: /src/vue/pages/tools/views/lite/ImportExport.vue:42
	__( 'Unlock Network Tools', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:41
	__( 'Unlock network-level tools to manage all your sites from one easy-to-use location. Manage your license key activations for each individual domain.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/lite/DatabaseTools.vue:46
	// Reference: /src/vue/pages/tools/views/lite/ImportExport.vue:43
	__( 'Unlock network-level tools to manage all your sites from one easy-to-use location. Migrate data or create backups without the need to visit each dashboard.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/NewsSitemap.js:16
	__( 'Unlock News Sitemaps', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/PostsTable.vue:165
	__( 'Unlock Post Tracking', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/redirects/views/lite/redirects/Redirects.vue:31
	__( 'Unlock Redirects', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:41
	__( 'Unlock Schema Generator', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:47
	__( 'Unlock Schema Markup Generator', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/composables/Cta.js:5
	// Reference: /src/vue/pages/search-statistics/views/Main.vue:66
	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/GoogleSearchConsoleSettings.vue:67
	// Reference: /src/vue/standalone/post-settings/views/lite/KeywordRankTracker.vue:58
	__( 'Unlock Search Statistics', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/seo-revisions/Upsell.vue:40
	__( 'Unlock SEO Revisions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:14
	__( 'Unlock Video Sitemaps', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:127
	__( 'Unwanted Bots', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/Statistic.vue:68
	__( 'Up', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/page-builders/wpbakery/limit-modified-date/main.js:13
	__( 'Update (Don\'t Modify Date)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:78
	__( 'Update Addon', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/EeatCta.js:23
	__( 'Update Author SEO (E-E-A-T)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/IndexNowSettings.vue:66
	__( 'Update IndexNow', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/debug/DeprecatedOptions.vue:29
	__( 'Update Options', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:106
	__( 'Update Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:71
	__( 'Update to version', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/WpCode.vue:63
	__( 'Update WPCode', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/UnlicensedAddons.vue:28
	__( 'Upgrade', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:84
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'Upgrade %1$s to Pro and Unlock all Features!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:75
	// Translators: 1 - "Pro".
	__( 'upgrade to %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/cta/Index.vue:91
	// Translators: 1 - "Pro".
	__( 'Upgrade to %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/flyout-menu/App.vue:72
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - "Pro" string.
	__( 'Upgrade to %1$s %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:155
	// Translators: 1 - "Pro", 2 - A discount percentage (e.g. "50%").
	__( 'Upgrade to %1$s and Save %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:52
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:44
	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:94
	// Translators: 1 - "Pro".
	__( 'Upgrade to %1$s Today', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:87
	__( 'upgrade to higher plan', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/CreditCounter.vue:81
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/Main.vue:43
	__( 'upgrade to Pro', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:76
	// Reference: /src/vue/components/common/core/Help.vue:57
	__( 'Upgrade to Pro', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:34
	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:84
	__( 'Upgrade to Pro to Unlock Powerful SEO Features', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/GoogleSearchConsole.js:53
	__( 'Upgrade to Pro to unlock Search Statistics and sync your site with Google Search Console. Get valuable insights right inside your WordPress dashboard, track keyword rankings and search performance for individual posts with actionable insights to help you rank higher in search results!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:53
	__( 'Upgrade to Pro Today!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:77
	__( 'Upgrade Your Plan', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/core/UpgradeBar.vue:30
	// Reference: /src/vue/components/lite/settings/LicenseKey.vue:37
	// Reference: /src/vue/pages/settings/views/GeneralSettings.vue:54
	// Translators: 1 - "Pro".
	__( 'upgrading to %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ImageUploader.vue:43
	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:25
	__( 'Upload or Select Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:66
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:142
	// Reference: /src/vue/standalone/writing-assistant/views/report/Competitors.vue:54
	__( 'URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:125
	__( 'URL already exists.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:51
	__( 'URL is indexed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:57
	__( 'URL not indexed', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:23
	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:34
	__( 'URLs', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/partials/ObjectsTable.vue:142
	__( 'URLs that link to the inspected URL, directly and indirectly.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/sidebar/Keyword.vue:35
	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:35
	__( 'Usage Examples', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:75
	__( 'Usage Tracking', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:27
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:47
	__( 'Use 24h format', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/Breadcrumbs/ContentTypesLite.vue:27
	__( 'Use a default template', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/ai/Generator.vue:31
	__( 'Use AI Generator', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:82
	__( 'Use Categories for Meta Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:129
	__( 'Use Categories in Article Tags', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:66
	__( 'Use Content for Autogenerated Descriptions', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:67
	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:76
	__( 'Use Data from Facebook Tab', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/RobotsMeta.vue:37
	// Reference: /src/vue/components/common/core/SingleRobotsMeta.vue:23
	// Reference: /src/vue/composables/AccessControl.js:15
	// Reference: /src/vue/standalone/blocks/html-sitemap/HtmlSitemapSidebar.vue:23
	__( 'Use Default Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:35
	__( 'Use Defaults', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:128
	__( 'Use Keywords in Article Tags', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:80
	__( 'Use Meta Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/GeneralSettings.vue:66
	// Translators: 1 - The plugin name ("All in One SEO")
	__( 'Use our configuration wizard to properly set up %1$s with your WordPress website.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:36
	// Translators: 1 - Learn more link.
	__( 'Use our powerful Schema Generator to configure Schema Markup for your content. Search Engines use structured data to better understand what your site is about as well as to display rich snippets in search results. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:130
	__( 'Use Post Tags in Article Tags', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:130
	__( 'Use Range', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/WpCode.vue:34
	__( 'Use Snippet', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Advanced.vue:84
	__( 'Use Tags for Meta Keywords', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:72
	// Translators: 1 - Learn more link.
	__( 'Use the following PHP code anywhere in your theme (in the loop) to display the breadcrumbs. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:135
	// Translators: 1 - Learn more link.
	__( 'Use the following PHP code anywhere in your theme to display the sitemap. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:179
	__( 'Use the following PHP code anywhere in your theme\'s post templates or author archive template to display a bio for the author.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:195
	__( 'Use the following PHP code anywhere in your theme\'s post templates to display a bio for the post author.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:227
	__( 'Use the following PHP code anywhere in your theme\'s post templates to display a bio for the post reviewer.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:95
	__( 'Use the following shortcode to display the author bio.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:111
	__( 'Use the following shortcode to display the author name.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Breadcrumbs.vue:60
	// Translators: 1 - Learn more link.
	__( 'Use the following shortcode to display the current breadcrumbs. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:120
	// Translators: 1 - Learn more link.
	__( 'Use the following shortcode to display the HTML Sitemap. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:143
	__( 'Use the following shortcode to display the reviewer name.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:100
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:88
	__( 'Use the home page description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:96
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:85
	__( 'Use the home page title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:93
	__( 'Use the same username for multiple social networks', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInSubHeadings.js:106
	__( 'Use your Focus Keyword more in your H2 and H3 subheadings.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1585
	__( 'Used', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1464
	__( 'User Access Control', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/RobotsEditor.vue:142
	// Reference: /src/vue/plugins/constants.js:1440
	__( 'User Agent', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/IndexStatus.js:269
	__( 'User-Declared Canonical', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/sidebar/Keyword.vue:30
	// Reference: /src/vue/standalone/writing-assistant/views/report/OptimizationWizard.vue:31
	__( 'Uses', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/sidebar/OptimizationWizard.vue:62
	__( 'Uses (Highest)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/sidebar/OptimizationWizard.vue:69
	__( 'Uses (Lowest)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:147
	__( 'Using the custom-built tools below, you can add an HTML sitemap to your website and help visitors discover all your content. Adding an HTML sitemap to your website may also help search engines find your content more easily.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/WpCode.vue:36
	__( 'Using WPCode you can install AIOSEO code snippets with 1-click directly from this page or the WPCode library inside the WordPress admin.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/Schema.vue:43
	__( 'Validate Schema', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/lite/Schema.vue:56
	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:50
	__( 'Validate with Google', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/CustomRules.vue:58
	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:62
	// Reference: /src/vue/plugins/constants.js:43
	__( 'Value', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/business-info/BusinessInfoSidebar.vue:40
	__( 'Vat ID', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/business/Ids.vue:7
	// Reference: /src/vue/standalone/blocks/business-info/index.js:61
	__( 'VAT ID:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:70
	__( 'Version', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/calculateFleschReading.js:41
	__( 'very easy', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Score.vue:42
	// Reference: /src/vue/composables/SeoSiteScore.js:24
	// Reference: /src/vue/composables/TruSeoScore.js:16
	__( 'Very Good!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/composables/schema.js:100
	__( 'Video', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:73
	__( 'Video and News Sitemaps', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:91
	// Reference: /src/vue/composables/Wizard.js:92
	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:137
	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:7
	// Reference: /src/vue/pages/sitemaps/router/paths.js:35
	__( 'Video Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/composables/VideoSitemap.js:17
	// Translators: 1 - "PRO".
	__( 'Video Sitemaps is a %1$s Feature', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:57
	__( 'Video Tutorials', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:78
	__( 'Video URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/PostTypes.js:7
	// Translators: 1 - The singular label for the current post type.
	__( 'View %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Help.vue:45
	__( 'View All', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Help.vue:53
	__( 'View All Documentation', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/HtmlTagsEditor.vue:89
	__( 'View all tags', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/GettingStarted.vue:58
	__( 'View all video tutorials', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/index-status/Index.vue:21
	__( 'View at a glance how many of your posts have been indexed and discovered by Google.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:193
	__( 'View detailed link & domain reports, manage existing links and discover new internal linking opportunities through smart suggestions (Pro & Elite plans only)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Help.vue:47
	__( 'View Documentation', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GoogleSearchPreview.vue:56
	__( 'View full list', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/keyword-rank-tracker/partials/KeywordsTable.vue:85
	// Reference: /src/vue/pages/search-statistics/views/partials/KeywordsTable.vue:166
	__( 'View in Google', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/FaqsModal.vue:132
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/KeyPointsModal.vue:152
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaDescriptionModal.vue:47
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaTitleModal.vue:47
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:102
	__( 'View Previous Results', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RobotsAlert.vue:13
	__( 'View robots.txt', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:123
	__( 'View the Changelog', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/faq/lite/sidebar.js:60
	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:66
	__( 'Visibility', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/NetworkSitesActivation.vue:33
	__( 'Visit Site', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1501
	__( 'Visual Arts Event', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:115
	__( 'Visual Breadcrumb Trails', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:77
	__( 'Volume', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1534
	__( 'Volunteer', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GoogleSearchPreview.vue:145
	_n( 'vote', 'votes', 0, 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/MicrosoftClaritySettings.vue:71
	__( 'Want to get the most out of Clarity? Integrate Clarity with Google Analytics using MonsterInsights today!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/General.vue:140
	// Translators: 1 - The plugin short name ("AIOSEO"), 2 - A link to "Search Appearance > Advanced".
	__( 'Warning: You have disabled Autogenerate Descriptions and are using the default description format. %1$s will not output a description unless you enter a custom one. You can enable Autogenerate Descriptions under %2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Competitor.vue:49
	// Reference: /src/vue/composables/SeoSiteScore.js:31
	__( 'Warnings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Success.vue:69
	// Translators: 1 - Plugin short name ("AIOSEO").
	__( 'Watch our Guided Tour of %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:124
	__( 'Watch video tutorials', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/SeoSiteScore.js:71
	// Translators: 1 - The plugin short name ('AIOSEO').
	__( 'We are unable to retrieve the content for your site. This could be due to a number of reasons, but most likely the connection timed out while our analyzer was trying to access it. Please try again soon.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/AnalyzerStore.js:140
	__( 'We couldn\'t analyze your title, please try again later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/stores/AnalyzerStore.js:122
	__( 'We couldn\'t connect to the site, please try again later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:25
	__( 'We couldn\'t find an SEO Title.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/isInternalLink.js:24
	__( 'We couldn\'t find any internal links in your content. Add internal links in your content.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:113
	__( 'We detected that your site has been removed from Google Search Console. If this was done in error, click below to re-sync and resolve this issue.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ImportOthers.vue:145
	// Translators: 1 - The name of the plugin (e.g. "Yoast SEO"), 2 - The version of the plugin (e.g. "10.2.3").
	__( 'We do not support importing from the currently installed version of %1$s (%2$s). Please upgrade to the latest version and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:59
	__( 'We don\'t have any data to export.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/WpCode.vue:38
	__( 'We encountered an error loading the code snippets, please try again later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:285
	__( 'We found Schema.org data on the page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:285
	__( 'We found Schema.org data on your page.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/ExportContents.vue:58
	// Reference: /src/vue/pages/tools/views/partials/ExportSettings.vue:45
	__( 'We had a problem when exporting data.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Features.vue:52
	__( 'We have already selected our recommended features based on your site category, but you can use the following features to fine-tune your site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Import.vue:50
	// Translators: 1 - Plugin short name ("AIOSEO").
	__( 'We have detected other SEO plugins installed on your website. Select which plugins you would like to import data to %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/PageAnalysis.vue:69
	// Translators: 1 - The Page Builder name, 2 - HTML code opening tag, 3 - HTML code closing tag.
	__( 'We have detected that you are currently using the %1$s Page Builder. Please click %2$shere%3$s to use the %1$s editor for a most accurate result.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:36
	__( 'We recommend entering a number between 1 and 50 characters.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:89
	__( 'We recommend setting the amount of URLs per sitemap index to 1,000 or less. The more links, the longer it will take for the sitemap to load.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:334
	__( 'We recommend tracking down where the un-minified CSS files come from.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:321
	__( 'We recommend tracking down where the un-minified JavaScript files come from', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/GoogleAnalyticsSettings.vue:82
	// Translators: 1 - Opening HTML bold tag, 2 - Closing HTML bold tag.
	__( 'We recommend using the %1$sFree ExactMetrics%2$s plugin to get the most out of Google Analytics.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/WebmasterTools/GoogleAnalyticsSettings.vue:74
	// Translators: 1 - Opening HTML bold tag, 2 - Closing HTML bold tag.
	__( 'We recommend using the %1$sFree MonsterInsights%2$s plugin to get the most out of Google Analytics.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/Media.vue:75
	__( 'We recommended redirecting attachment URLs back to the attachment since the default WordPress attachment pages have little SEO value.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Dashboard.vue:66
	// Reference: /src/vue/pages/seo-analysis/views/AnalyzeCompetitorSite.vue:72
	__( 'We were unable to parse the content for this site.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/RobotsAlert.vue:12
	// Translators: 1 - "View robots.txt" link.
	__( 'We\'re adding rules to your robots.txt file to block certain bots from crawling your website. This can help improve website performance and protect sensitive information. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review.vue:69
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'We\'re sorry to hear you aren\'t enjoying %1$s. We would love a chance to improve. Could you take a minute and let us know what we can do better?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Score.vue:34
	// Reference: /src/vue/composables/SeoSiteScore.js:16
	// Reference: /src/vue/composables/TruSeoScore.js:8
	// Translators: 1 - HTML Line break tag.
	__( 'We\'ve got some%1$swork to do!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1517
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:105
	// Reference: /src/vue/standalone/post-settings/composables/schema.js:132
	__( 'Web Page', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/ToolsSettings.js:15
	// Reference: /src/vue/pages/settings/router/paths.js:40
	__( 'Webmaster Tools', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:98
	__( 'Webmaster Tools Verification', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1247
	__( 'Website', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:124
	__( 'Website Name', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/BusinessInfo.vue:35
	__( 'Website URL', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:29
	__( 'Website URL:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1244
	__( 'Websites', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/opening-hours/Blur.vue:33
	// Reference: /src/vue/standalone/blocks/opening-hours/OpeningHoursSidebar.vue:31
	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:55
	__( 'Wednesday', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1548
	__( 'Week', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:29
	// Reference: /src/vue/plugins/constants.js:20
	__( 'weekly', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/partials/Advanced/EmailSummary.vue:66
	// Reference: /src/vue/plugins/constants.js:1478
	__( 'Weekly', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/router/paths.js:17
	__( 'Welcome', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/GettingStarted.vue:39
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'Welcome to %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:57
	// Translators: 1 - The plugin name ("All in One SEO"), 2 - The plugin name ("All in One SEO").
	__( 'Welcome to %1$s, the original SEO plugin for WordPress. At %2$s, we build software that helps you rank your website in search results and gain organic traffic.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Welcome.vue:35
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'Welcome to the %1$s Setup Wizard!', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/metadescriptionLength.js:47
	// Reference: /src/app/tru-seo/analyzer/analysis/titleLength.js:53
	// Reference: /src/app/tru-seo/analyzer/analysis/transitionWords.js:63
	__( 'Well done!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:89
	__( 'what\'s this?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:102
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:186
	// Translators: 1 - The default value.
	__( 'Whether the compact author bio should be output or not. Defaults to %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:57
	// Translators: 1 - The default value.
	__( 'Whether the labels should be shown or not. Defaults to %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:63
	__( 'Whether the publication date of posts should be shown.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/HtmlSitemap.vue:67
	__( 'Whether the regular sitemap or compact date archive sitemap is output.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:150
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:234
	// Translators: 1 - The default value.
	__( 'Whether to display the "Reviewed By" label or not. Defaults to %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:118
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:202
	// Translators: 1 - The default value.
	__( 'Whether to display the "Written By" label or not. Defaults to %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:126
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:210
	// Translators: 1 - The default value.
	__( 'Whether to display the author image or not. Defaults to %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:134
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:166
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:218
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:250
	// Translators: 1 - The default value.
	__( 'Whether to display the popup when someone hovers over the name or not. Defaults to %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:158
	// Reference: /src/vue/pages/search-appearance/views/partials/eeat/EeatBlur.vue:242
	// Translators: 1 - The default value.
	__( 'Whether to display the reviewer image or not. Defaults to %1$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:17
	// Translators: 1 - The plugin short name ("AIOSEO").
	__( 'Whether your business has multiple locations, or just one, %1$s makes it easy to configure and display relevant information about your local business. You can use the custom-built tools below, or you can use the Locations custom post type (multiple locations only) to generate relevant and necessary information for search engines or for your customers.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Category.vue:63
	__( 'Which category best describes your website?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/Features.vue:48
	__( 'Which SEO features do you want to enable?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/faq/index.js:24
	__( 'Who should use AIOSEO?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ui-element-slider/Index.vue:50
	__( 'Widget', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ui-element-slider/Index.vue:56
	__( 'Widgets', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:79
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:74
	// Reference: /src/vue/standalone/blocks/location-map/LocationMapSidebar.vue:33
	// Reference: /src/vue/standalone/post-settings/views/Facebook.vue:79
	__( 'Width', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:108
	__( 'Will be overriden if the Facebook author URL is present in the individual User Profile.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/local-business-seo/views/OpeningHours.vue:36
	__( 'Will default opening hours set globally', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/dashboard/Index.vue:73
	// Reference: /src/vue/pages/search-statistics/views/lite/dashboard/Blur.vue:54
	__( 'Winning / Losing', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/SeoRevisions.vue:30
	__( 'With our powerful revisions feature for all your SEO data, never lose the exact SEO title or description (and more!) that helped you rank higher in search results and restore it back in a single click.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:93
	__( 'WooCommerce Integration', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:96
	__( 'WooCommerce Products only', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:100
	__( 'WooCommerce Products, Product Categories, Product Tags, Product Attributes + WooCommerce smart tags (price, brand, etc.)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:55
	__( 'Word balance', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:13
	__( 'Word Balance', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:58
	// Reference: /src/vue/standalone/headline-analyzer/components/WordCount.vue:11
	__( 'Word Count', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/SystemStatus.vue:45
	__( 'WordPress', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1445
	__( 'WordPress Filter', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:134
	__( 'WordPress sites usually insert the page or post title as an H1 tag (although custom themes can change this behavior).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1439
	__( 'WordPress User Roles', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/partials/keyword/Examples.vue:21
	// Reference: /src/vue/standalone/writing-assistant/views/partials/summary/WordCount.vue:10
	// Reference: /src/vue/standalone/writing-assistant/views/report/Overview.vue:34
	__( 'words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Competitors.vue:52
	__( 'Words', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/lite/image-seo/Blur.vue:51
	__( 'Words to Strip', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:78
	__( 'Would you like to purchase and install the following features now?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/lite/AccessControl.vue:27
	__( 'WP Roles (Editor, Author)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:109
	__( 'Write a meta description for your page. Use your target keywords (in a natural way) and write with human readers in mind. Summarize the content - describe the topics your article discusses.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/faq/lite/sidebar.js:76
	__( 'Write a question...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/faq/lite/sidebar.js:19
	__( 'Write an answer...', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/Main.vue:34
	__( 'Write your post title to see the analyzer data. This Headline Analyzer tool enables you to write irresistible SEO headlines that drive traffic, shares, and rank better in search results.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/router/paths.js:78
	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:44
	__( 'Writing Assistant', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:62
	__( 'Writing compelling titles is both a science and an art. There are automated tools that can analyze your title against known metrics for readability and click-worthiness. You also need to understand the psychology of your target audience.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/SocialPostsModal.vue:112
	__( 'X (Twitter Post)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/router/paths.js:35
	// Reference: /src/vue/standalone/post-settings/views/Social.vue:44
	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:88
	__( 'X (Twitter)', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:64
	__( 'X (Twitter) Card Settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:74
	__( 'X (Twitter) Preview', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:82
	__( 'X Card Type', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:75
	__( 'X cards by default will use the data defined below. If no data is set, X will instead pick up the data set on the Facebook tab.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:81
	__( 'X Description', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:79
	__( 'X Image', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/Twitter.vue:80
	__( 'X Title', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/lite/LiteVsPro.vue:126
	__( 'XML Sitemap', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:162
	__( 'Yandex Verification Code', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:157
	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:166
	__( 'Yandex Webmaster Tools', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:22
	__( 'yearly', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/plugins/constants.js:1480
	__( 'Yearly', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/utils/date.js:32
	// Translators: A number will be prepended to this string, e.g. "2 years ago".
	__( 'years ago', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/query-arg-monitor/Table.vue:63
	// Reference: /src/vue/plugins/constants.js:44
	__( 'Yes', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:77
	__( 'Yes, count me in', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/Wizard.js:186
	__( 'Yes, count me in!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ResetSettings.vue:54
	__( 'Yes, I have a backup and want to reset the settings', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/notifications/Review.vue:38
	// Reference: /src/vue/components/common/notifications/Review2.vue:32
	__( 'Yes, I love it!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/link-assistant/Links.js:47
	__( 'Yes, I want to delete all links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/link-assistant/Links.js:46
	__( 'Yes, I want to delete these links', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:51
	__( 'Yes, I want to delete this backup', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/link-assistant/Links.js:45
	__( 'Yes, I want to delete this link', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/DisconnectModal.vue:29
	// Reference: /src/vue/pages/search-statistics/views/partials/DisconnectModal.vue:23
	// Reference: /src/vue/standalone/writing-assistant/views/partials/authenticate/DisconnectModal.vue:29
	__( 'Yes, I want to disconnect', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:52
	__( 'Yes, I want to restore this backup', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:89
	// Reference: /src/vue/pages/feature-manager/views/FeatureManager.vue:92
	__( 'Yes, process this network change', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/local-seo/views/lite/import/Import.vue:38
	__( 'Yoast SEO', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:94
	__( 'You are getting bugfixes and security updates, but not major features.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:92
	__( 'You are getting the latest features, bugfixes, and security updates as they are released.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/isInternalLink.js:16
	__( 'You are linking to other resources on your website which is great.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/subheadingsDistribution.js:65
	__( 'You are not using any subheadings, although your text is rather long. Try and add some subheadings.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/subheadingsDistribution.js:84
	__( 'You are not using any subheadings, but your text is short enough and probably doesn\'t need them.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/contentHasAssets.js:51
	__( 'You are not using rich media like images or videos.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/GlobalSettings.vue:80
	// Translators: 1 - Opening HTML link tag, 2 - Closing HTML link tag.
	__( 'You are using a static home page which is found under Pages. You can %1$sedit your home page settings%2$s directly to change the title and description.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/social-networks/views/Facebook.vue:133
	// Reference: /src/vue/pages/social-networks/views/Twitter.vue:93
	// Translators: 1 - Opening HTML link tag, 2 - Closing HTML link tag.
	__( 'You are using a static home page which is found under Pages. You can %1$sedit your home page settings%2$s directly to change the title, meta description and image.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/paragraphLength.js:28
	__( 'You are using short paragraphs.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:96
	__( 'You can add additional social profile URLs here, separated by a new line.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/faq/lite/sidebar.js:64
	__( 'You can choose to hide this block on the front-end of your site so that visitors won\'t see it but search engines will still see it.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/blocks/breadcrumbs/index.js:109
	__( 'You can customize your breadcrumb trail under ', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/seo-preview/views/SeoInspector.vue:47
	__( 'You can edit the "Focus Keyword" and view information about "Page Analysis" on the admin side.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:398
	__( 'You can get an even greater boost in speed with a content delivery network service. These services host a copy of your content on multiple servers spread out across the globe. A user\'s request is handled by the edge server that\'s closest to their physical location, so the content arrives incredibly fast.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/AddAdditionalPage.vue:111
	// Translators: 1 - Opening HTML strong tag, 2 - Closing HTML strong tag.
	__( 'You can import additional page URL\'s to your sitemap using a CSV file. The following 4 columns are required: %1$sPage URL, Priority, Frequency, Date Modified.%2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:255
	__( 'You can manually create a robots.txt file and upload it to your site\'s web root. A simpler option is to use a plugin for your CMS platform.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:214
	// Translators: 1 - Learn more link.
	__( 'You can manually set an API key here, but if left blank a new one will be auto-generated. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/page-builders/elementor/introduction.js:22
	// Translators: 1 - The plugin name ("All in One SEO"), 2 - The Elementor plugin name ("Elementor").
	__( 'You can now manage your SEO settings inside of %1$s via %2$s before you publish your post!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:378
	__( 'You can reduce CSS repetition with HTML class and ID attributes. Often the same rules will be repeated across many page elements, embedded in each tag\'s "style" attribute. You can extract them into a single "style" tag and use classes and ID\'s to target each element.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/Main.vue:39
	// Translators: 1 - "upgrade to Pro" link, 2 - "purchase PAYG credits" link.
	__( 'You can try out our AI features for free, enjoy! To unlock additional AI credits, %1$s or %2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:127
	__( 'You can use these quicklinks to quickly access our settings pages to adjust your site\'s SEO settings.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/AdditionalPages.vue:75
	__( 'You can use this section to add any URLs to your sitemap which aren\'t a part of your WordPress installation. For example, if you have a contact form that you would like to be included on your sitemap you can enter the information manually.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/addon/Activate.vue:50
	// Reference: /src/vue/components/common/core/addon/Update.vue:53
	// Reference: /src/vue/pages/tools/views/WpCode.vue:40
	__( 'You currently don\'t have permission to activate this addon. Please ask a site administrator to activate first.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/FeatureCard.vue:79
	__( 'You currently don\'t have permission to update this addon. Please ask a site administrator to update.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/TopKeywords.vue:26
	// Reference: /src/vue/pages/search-statistics/views/partials/WinningLosingKeywords.vue:26
	__( 'You don\'t have any keywords yet.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:171
	// Translators: 1 - The amount of remaining notifications.
	__( 'You have %1$s more notifications', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/Overview.vue:150
	// Translators: 1 - HTML opening link tag, 2 - The number of posts (e.g. "1 post", "2 posts"), 3 - HTML closing link tag.
	__( 'You have %1$s%2$s without a Focus Keyword%3$s. Adding one can help you optimize your content for your target keyword.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/dashboard/views/Main.vue:167
	__( 'You have 1 more notification', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:140
	__( 'You have exceeded the limit for requests. Please try again later.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:112
	// Translators: 1 - Semrush.
	__( 'You have exceeded the number of requests allowed by your %1$s plan.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/notifications/App.vue:9
	__( 'You have new notifications!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/NotificationCards.vue:32
	__( 'You have no new notifications.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/GenerateReport.vue:26
	__( 'You have no reports left.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/tools/views/partials/BackupSettings.vue:55
	__( 'You have no saved backups.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:406
	__( 'You have no visible plugins!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/Schema.vue:40
	__( 'You have not added any schema yet. You can add any schema graphs you like via the Schema Generator below.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/License.js:11
	__( 'You have not yet added a valid license key.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/general/AdditionalKeyphrases.vue:49
	// Reference: /src/vue/standalone/post-settings/views/partials/general/FocusKeyphrase.vue:122
	// Translators: 1 - Number of maximum keywords.
	__( 'You have reached the maximum of %1$s additional keywords.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:134
	// Reference: /src/vue/standalone/headline-analyzer/components/CharacterCount.vue:64
	// Reference: /src/vue/standalone/headline-analyzer/components/CharacterCount.vue:81
	__( 'You have space to add more keywords and power words to boost your rankings and click-through rate.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Overview.vue:37
	// Reference: /src/vue/standalone/writing-assistant/views/report/YourContent.vue:17
	__( 'You must first add content before the score can be determined.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/Cta.vue:16
	__( 'You Ran Out of Trial Credits!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:235
	__( 'You should use HTTP redirections (301 permanant redirects) to pass PageRank from the "wrong" URLs to the standard (canonical) ones. That way, your content will still benefit from backlinks if someone makes a mistake and uses the wrong URL.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:134
	__( 'You should write as if your readers are selfish people with short attention spans (because that describes a large percentage of the world\'s population). Readers visit websites for selfish reasons - they\'re not there to make you happy.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/Advanced.vue:96
	__( 'You will need to manually update everything.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:80
	__( 'You won\'t have access to this functionality until the extensions have been purchased and installed.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SeoSetup.vue:39
	__( 'You\'re almost there! Once you complete the SEO setup your site will be optimized to rank in search engine results!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WritingAssistant.vue:52
	__( 'You\'re connected to SEOBoost!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:217
	__( 'You\'re good to go!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/LicenseKey.vue:77
	// Reference: /src/vue/standalone/setup-wizard/views/LicenseKey.vue:92
	// Translators: 1 - The plugin name ("All in One SEO").
	__( 'You\'re using %1$s - no license needed. Enjoy!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/core/UpgradeBar.vue:47
	// Translators: 1 - The plugin name ("All in One SEO"), 2 - "upgrading to Pro".
	__( 'You\'re using %1$s. To unlock more features, consider %2$s', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/passiveVoice.js:39
	__( 'You\'re using enough active voice. That\'s great!', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInIntroduction.js:39
	// Translators: 1 - Focus Keyword or Keyword.
	__( 'Your %1$s appears in the first paragraph. Well done!', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInIntroduction.js:51
	// Translators: 1 - Focus Keyword or Keyword.
	__( 'Your %1$s does not appear in the first paragraph. Make sure the topic is clear immediately.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/TitleDescription.vue:104
	// Translators: 1 - The plural name of the content type (e.g. "Posts" or "Categories").
	__( 'Your %1$s will be noindexed and excluded from the sitemap so that search engines ignore them. You can still control how their page title looks like below.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/BuyOrConnectButtons.vue:45
	__( 'Your account is connected at the network level of your WordPress multisite.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/FirstReport.vue:15
	__( 'Your Account is Connected to SEOBoost! 🎉', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/ai/BuyOrConnectButtons.vue:44
	__( 'Your account is connected!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:251
	__( 'Your Bluesky URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-statistics/views/partials/AuthenticationAlert.vue:20
	__( 'Your connection with Google Search Console has expired or is invalid. Please check that your site is verified in Google Search Console and try to reconnect. If the problem persists, please contact our support team.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/writing-assistant/views/report/Overview.vue:31
	// Reference: /src/vue/standalone/writing-assistant/views/report/YourContent.vue:16
	__( 'Your Content', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/contentHasAssets.js:43
	__( 'Your content contains images and/or video(s).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:70
	__( 'Your Email Address', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/SmartRecommendations.vue:71
	__( 'Your email is needed so you can receive SEO recommendations. This email will also be used to connect your site with our SEO API.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:121
	__( 'Your Facebook URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInSubHeadings.js:98
	__( 'Your H2 and H3 subheadings reflects the topic of your copy. Good job!', 'all-in-one-seo-pack' ),

	// Reference: /src/app/tru-seo/analyzer/analysis/keyphraseInSubHeadings.js:89
	__( 'Your H2 or H3 subheading reflects the topic of your copy. Good job!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:163
	// Reference: /src/vue/standalone/headline-analyzer/components/WordCount.vue:61
	__( 'Your headline doesn’t use enough words. You have more space to add keywords and power words to improve your SEO and get more engagement.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:214
	// Reference: /src/vue/standalone/headline-analyzer/components/Sentiment.vue:16
	__( 'Your headline has a negative sentiment.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:198
	__( 'Your headline has a neutral sentiment.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:206
	// Reference: /src/vue/standalone/headline-analyzer/components/Sentiment.vue:14
	__( 'Your headline has a positive sentiment.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:171
	// Reference: /src/vue/standalone/headline-analyzer/components/WordCount.vue:63
	__( 'Your headline has the right amount of words. Headlines are more likely to be clicked on in search results if they have about 6 words.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:187
	// Reference: /src/vue/standalone/headline-analyzer/components/WordCount.vue:65
	__( 'Your headline has too many words. Long headlines will get cut off in search results and won’t get as many clicks.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:47
	__( 'Your headline would be more likely to get clicks if it had more common words.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/headline/Result.vue:77
	// Reference: /src/vue/standalone/headline-analyzer/components/WordBalance.vue:56
	__( 'Your headline would be more likely to get clicks if it had more uncommon words.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:141
	__( 'Your Instagram URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/search-appearance/views/partials/crawl-cleanup/SearchCleanup.vue:31
	__( 'Your internal site search can create lots of confusing URLs for search engines, and can even be used as a way for SEO spammers to attack your site. Most sites will benefit from experimenting with these protections and optimizations, even if you don\'t have a search feature in your theme.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/License.js:16
	__( 'Your license has been disabled.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/License.js:13
	__( 'Your license has expired.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/License.js:19
	__( 'Your license key is invalid.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/settings/LicenseKey.vue:46
	__( 'Your license key provides access to updates and addons.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:161
	__( 'Your LinkedIn URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:86
	// Translators: 1 - The length of the meta description as a number.
	__( 'Your meta description is %1$d characters long, which is too long.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:76
	// Translators: 1 - The length of the meta description as a number.
	__( 'Your meta description is only %1$d characters long, which is too short.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:96
	// Translators: 1 - The length of the meta description as a number.
	__( 'Your meta description is set and is %1$d characters long.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:231
	__( 'Your MySpace URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/Name.vue:8
	// Reference: /src/vue/pages/local-seo/views/lite/locations/Blur.vue:21
	__( 'Your name or company name.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Analyze.vue:47
	__( 'Your Overall Site Score', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:213
	__( 'Your page contains a noindex header or meta tag.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:215
	__( 'Your page does not contain any noindex header or meta tag.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:182
	__( 'Your page has a correct number of internal and external links.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:199
	__( 'Your page is using the canonical link tag.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:341
	// Translators: 1 - The total number of page requests.
	__( 'Your page makes %1$d requests.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:171
	__( 'Your Pinterest URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaDescriptionModal.vue:48
	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/MetaTitleModal.vue:48
	__( 'Your post is too short to generate AI content. Please add more content. For the best results, we recommend adding at least 200 words.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/partials/ai-content/Main.vue:36
	__( 'Your post is too short to generate AI content. Please add some more content. For the best results, we recommend adding at least 200 words.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/publish-panel/PrePublish.vue:217
	__( 'Your post needs improvement!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/user-profile-tab/main.js:27
	__( 'Your profile has unsaved changes. If you leave this page, you will lose your changes.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:394
	__( 'Your response time is under 0.2 seconds.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:243
	__( 'Your robots.txt file is missing or unavailable.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/RssContent.vue:38
	// Translators: 1 - Opening link tag, 2 - Closing link tag.
	__( 'Your RSS feed has been disabled. Disabling the global RSS feed is NOT recommended. This will prevent users from subscribing to your content and can hurt your SEO rankings. You can re-enable the global RSS feed in the %1$scrawl content settings%2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:39
	// Translators: 1 - The length of the SEO title as a number.
	__( 'Your SEO title is %1$d characters long, which is too long.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:29
	// Translators: 1 - The length of the SEO title as a number.
	__( 'Your SEO title is only %1$d characters long, which is too short.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:49
	// Translators: 1 - The length of the SEO title as a number.
	__( 'Your SEO title is set and is %1$d characters long.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:302
	__( 'Your server is not using "expires" headers for your images.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:304
	__( 'Your server is using "expires" headers for your images.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/ResetSettings.vue:46
	__( 'Your settings have been reset successfully!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:247
	__( 'Your site has a robots.txt file which includes one or more "disallow" directives.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:249
	__( 'Your site has a robots.txt file.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/partials/SearchConsole.vue:35
	// Reference: /src/vue/pages/sitemaps/views/partials/SearchConsoleInline.vue:36
	__( 'Your site is connected directly to Google Search Console and your sitemaps are in sync.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/SeoSiteScore.js:55
	// Translators: 1 - The plugin short name ('AIOSEO').
	__( 'Your site is currently not connected to %1$s. In order to analyze your site, you must first connect to our server. Please connect to %1$s and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/site-score/Dashboard.vue:70
	// Reference: /src/vue/pages/seo-analysis/views/AnalyzeCompetitorSite.vue:78
	// Translators: 1 - The plugin short name ('AIOSEO').
	__( 'Your site is not connected. Please connect to %1$s, then try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:461
	__( 'Your site is not using a secure transfer protocol (https).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:463
	__( 'Your site is using a secure transfer protocol (https).', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:112
	__( 'Your site was removed from Google Search Console.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/setup-wizard/views/AdditionalInformation.vue:62
	__( 'Your Social Profiles', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:211
	__( 'Your SoundCloud URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/redirects/Redirect.js:56
	__( 'Your source is a protected path and cannot be redirected.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/redirects/Redirect.js:49
	__( 'Your source is the same as a target and this will create a loop.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/sitemaps/views/GeneralSitemap.vue:90
	__( 'Your static sitemap is currently being regenerated. This may take some time based on the size of your site. This may also cause the sitemap content to look outdated.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:207
	// Translators: 1 - Adds a html tag with an option like: <code>^</code>.
	__( 'Your target URL contains the invalid character(s) %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:190
	__( 'Your target URL is not valid.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:196
	// Translators: 1 - Adds a html tag with an option like: <code>^</code>, 2 - Adds a html tag with an option like: <code>^</code>.
	__( 'Your target URL should be an absolute URL like %1$s or start with a slash %2$s.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/standalone/post-settings/views/lite/partials-schema/CtaModal.vue:81
	__( 'Your Templates', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/classes/SiteAnalysis.js:427
	__( 'Your theme is not visible!', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:261
	__( 'Your Threads URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:151
	__( 'Your TikTok URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:191
	__( 'Your Tumblr URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/add-redirection/Index.vue:222
	// Translators: 1 - Domain URL, 2 - Domain URL.
	__( 'Your URL appears to contain a domain inside the path: %1$s. Did you mean to use %2$s instead?', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/composables/redirects/Redirect.js:26
	__( 'Your URL is invalid.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/base/Input.vue:133
	__( 'Your URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/alert/UnfilteredHtml.vue:23
	// Reference: /src/vue/pages/settings/views/RssContent.vue:50
	// Reference: /src/vue/pages/settings/views/WebmasterTools.vue:101
	// Translators: 1 - Learn more link.
	__( 'Your user account role does not have access to edit this field. %1$s', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:94
	__( 'Your Username:', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:221
	__( 'Your Wikipedia URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:241
	__( 'Your WordPress URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:131
	__( 'Your X (Twitter) URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:201
	__( 'Your Yelp URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/common/core/SocialProfiles.vue:181
	__( 'Your YouTube URL is invalid. Please check the format and try again.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/pages/about/views/AboutUs.vue:69
	__( 'Yup, we know a thing or two about building awesome products that customers love.', 'all-in-one-seo-pack' ),

	// Reference: /src/vue/components/lite/local-business/business/Address.vue:11
	__( 'Zip Code', 'all-in-one-seo-pack' ),
];