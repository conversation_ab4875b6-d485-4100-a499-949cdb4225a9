# Copyright (C) 2025 Ultimate Member
# This file is distributed under the GPLv3.
msgid ""
msgstr ""
"Project-Id-Version: Ultimate Member 2.10.5\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/ultimate-member\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-06-25T13:24:33+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.12.0\n"
"X-Domain: ultimate-member\n"

#. Plugin Name of the plugin
#. Author of the plugin
#: ultimate-member.php
#: includes/admin/class-users-columns.php:322
#: includes/admin/core/class-admin-menu.php:183
#: includes/core/class-user.php:1056
msgid "Ultimate Member"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: ultimate-member.php
msgid "http://ultimatemember.com/"
msgstr ""

#. Description of the plugin
#: ultimate-member.php
msgid "The easiest way to create powerful online communities and beautiful user profiles with WordPress"
msgstr ""

#. translators: %1$s - Plugin name, %1$s - Plugin Version
#: includes/action-scheduler/class-init.php:66
#, php-format
msgid "<strong>%1$s %2$s</strong> The file needed to enable the Action Scheduler is missing. The plugin will continue to function as it did before, but without the new benefits offered by the Action Scheduler."
msgstr ""

#: includes/action-scheduler/class-init.php:79
#: includes/admin/class-site-health.php:51
#: includes/admin/core/class-admin-columns.php:161
#: includes/admin/core/class-admin-columns.php:194
#: includes/admin/core/class-admin-settings.php:1258
#: includes/admin/core/class-admin-settings.php:1366
#: includes/admin/core/list-tables/roles-list-table.php:435
#: includes/admin/core/list-tables/roles-list-table.php:443
#: includes/admin/templates/form/login_customize.php:27
#: includes/admin/templates/form/login_customize.php:75
#: includes/admin/templates/form/login_customize.php:94
#: includes/admin/templates/form/login_customize.php:105
#: includes/admin/templates/form/profile_customize.php:42
#: includes/admin/templates/form/profile_customize.php:108
#: includes/admin/templates/form/profile_customize.php:127
#: includes/admin/templates/form/profile_customize.php:162
#: includes/admin/templates/form/profile_customize.php:183
#: includes/admin/templates/form/profile_customize.php:194
#: includes/admin/templates/form/profile_customize.php:205
#: includes/admin/templates/form/profile_customize.php:216
#: includes/admin/templates/form/register_customize.php:33
#: includes/admin/templates/form/register_customize.php:89
#: includes/admin/templates/form/register_gdpr.php:31
#: includes/admin/templates/role/profile.php:56
#: includes/core/class-builtin.php:1347
#: includes/core/class-builtin.php:1366
#: includes/core/class-builtin.php:1384
#: includes/core/class-builtin.php:1399
#: includes/core/class-builtin.php:1402
#: includes/core/um-actions-account.php:349
msgid "No"
msgstr ""

#: includes/action-scheduler/class-init.php:94
#: includes/admin/class-site-health.php:1469
msgid "Email sending by Action Scheduler"
msgstr ""

#: includes/action-scheduler/class-init.php:95
msgid "Enable Action Scheduler for Ultimate Member emails sending"
msgstr ""

#: includes/action-scheduler/class-init.php:96
msgid "Check this box if you want to use the Action Scheduler for Ultimate Member emails sending. By enabling it, sending system emails will be scheduled to run at optimal times, which can help reduce the load on your server"
msgstr ""

#: includes/admin/class-actions-listener.php:65
#: includes/admin/class-actions-listener.php:90
#: includes/admin/class-actions-listener.php:114
#: includes/admin/class-actions-listener.php:138
#: includes/admin/class-actions-listener.php:162
#: includes/admin/class-actions-listener.php:186
msgid "Invalid user ID"
msgstr ""

#: includes/admin/class-admin-functions.php:26
#: includes/class-functions.php:46
msgid "Wrong Nonce"
msgstr ""

#: includes/admin/class-admin.php:844
#: includes/admin/templates/directory/sorting.php:68
#: includes/core/class-member-directory.php:455
msgid "Other (Custom Field)"
msgstr ""

#: includes/admin/class-admin.php:1807
#: includes/admin/class-secure.php:96
#: includes/admin/class-secure.php:133
msgid "Security check"
msgstr ""

#: includes/admin/class-admin.php:1811
msgid "Wrong ID"
msgstr ""

#. translators: %s - Form title
#: includes/admin/class-admin.php:1819
#, php-format
msgid "Duplicate of %s"
msgstr ""

#: includes/admin/class-admin.php:1879
msgid "Docs"
msgstr ""

#: includes/admin/class-admin.php:1880
#: includes/admin/core/class-admin-menu.php:195
msgid "Settings"
msgstr ""

#: includes/admin/class-admin.php:1903
#: includes/admin/core/class-admin-notices.php:1123
#: includes/admin/core/class-admin-settings.php:2563
#: includes/ajax/class-secure.php:34
msgid "Security Check"
msgstr ""

#: includes/admin/class-admin.php:1965
#: includes/admin/class-admin.php:1968
msgid "Form updated."
msgstr ""

#: includes/admin/class-admin.php:1966
msgid "Custom field updated."
msgstr ""

#: includes/admin/class-admin.php:1967
msgid "Custom field deleted."
msgstr ""

#: includes/admin/class-admin.php:1969
msgid "Form restored to revision."
msgstr ""

#: includes/admin/class-admin.php:1970
msgid "Form created."
msgstr ""

#: includes/admin/class-admin.php:1971
msgid "Form saved."
msgstr ""

#: includes/admin/class-admin.php:1972
msgid "Form submitted."
msgstr ""

#: includes/admin/class-admin.php:1973
msgid "Form scheduled."
msgstr ""

#: includes/admin/class-admin.php:1974
msgid "Form draft updated."
msgstr ""

#: includes/admin/class-enqueue.php:115
#: includes/admin/core/class-admin-settings.php:1082
#: includes/admin/core/class-admin-settings.php:2090
msgid "General"
msgstr ""

#: includes/admin/class-enqueue.php:119
#: includes/admin/core/class-admin-settings.php:1205
#: includes/class-config.php:316
#: includes/class-config.php:319
#: includes/class-config.php:391
#: includes/class-config.php:394
#: includes/core/class-builtin.php:820
#: includes/core/class-builtin.php:823
#: includes/core/class-builtin.php:1408
#: includes/core/class-builtin.php:1411
#: includes/core/um-actions-account.php:690
#: includes/core/um-actions-account.php:694
#: includes/core/um-actions-account.php:780
#: includes/core/um-actions-account.php:784
msgid "Password"
msgstr ""

#: includes/admin/class-enqueue.php:123
#: includes/admin/core/class-admin-metabox.php:2415
#: includes/core/class-account.php:101
msgid "Privacy"
msgstr ""

#: includes/admin/class-enqueue.php:127
#: includes/core/class-account.php:110
msgid "Notifications"
msgstr ""

#: includes/admin/class-enqueue.php:131
#: includes/admin/core/class-admin-builder.php:564
#: includes/admin/core/list-tables/roles-list-table.php:390
#: includes/admin/core/list-tables/roles-list-table.php:474
msgid "Delete"
msgstr ""

#: includes/admin/class-enqueue.php:543
msgid "Ultimate Member Blocks"
msgstr ""

#: includes/admin/class-secure.php:138
msgid "Invalid user."
msgstr ""

#: includes/admin/class-secure.php:196
msgid "Scan Now"
msgstr ""

#: includes/admin/class-secure.php:198
msgid "Last scan:"
msgstr ""

#: includes/admin/class-secure.php:202
#: includes/admin/class-secure.php:339
msgid "ago"
msgstr ""

#: includes/admin/class-secure.php:204
msgid "Not Completed."
msgstr ""

#: includes/admin/class-secure.php:207
msgid "Not Scanned yet."
msgstr ""

#: includes/admin/class-secure.php:221
#: includes/admin/class-site-health.php:1497
msgid "Banned Administrative Capabilities"
msgstr ""

#. translators: %s are disabled default capabilities that are enabled by default.
#: includes/admin/class-secure.php:223
#, php-format
msgid "All the above are default Administrator & Super Admin capabilities. When someone tries to inject capabilities to the Account, Profile & Register forms submission, it will be flagged with this option. The %s capabilities are locked to ensure no users will be created with these capabilities."
msgstr ""

#: includes/admin/class-secure.php:228
msgid "Scanner"
msgstr ""

#: includes/admin/class-secure.php:230
msgid "Scan your site to check for vulnerabilities prior to Ultimate Member version 2.6.7 and get recommendations to secure your site."
msgstr ""

#: includes/admin/class-secure.php:235
#: includes/admin/class-site-health.php:1501
msgid "Lock All Register Forms"
msgstr ""

#: includes/admin/class-secure.php:236
msgid "Lock Forms"
msgstr ""

#: includes/admin/class-secure.php:237
msgid "This prevents all users from registering with Ultimate Member on your site."
msgstr ""

#: includes/admin/class-secure.php:242
#: includes/admin/class-site-health.php:1505
msgid "Display Login form notice to reset passwords"
msgstr ""

#: includes/admin/class-secure.php:243
msgid "Enable Login form notice"
msgstr ""

#: includes/admin/class-secure.php:244
msgid "Enforces users to reset their passwords (one-time) and prevent from entering old password."
msgstr ""

#: includes/admin/class-secure.php:256
msgid "Expire All Users Sessions"
msgstr ""

#. translators: %d is the users count.
#: includes/admin/class-secure.php:258
msgid "Are you sure that you want to make all users sessions expired?"
msgstr ""

#. translators: %d is the users count.
#: includes/admin/class-secure.php:258
#, php-format
msgid "Logout Users (%d)"
msgstr ""

#: includes/admin/class-secure.php:259
msgid "This will log out all users on your site and forces them to reset passwords <br/>when <strong>\"Display Login form notice to reset passwords\" is enabled/checked.</strong>"
msgstr ""

#: includes/admin/class-secure.php:269
msgid "Administrative capabilities ban"
msgstr ""

#: includes/admin/class-secure.php:270
msgid "Enable ban for administrative capabilities"
msgstr ""

#: includes/admin/class-secure.php:271
msgid " When someone tries to inject capabilities to the Account, Profile & Register forms submission, it will be banned."
msgstr ""

#: includes/admin/class-secure.php:276
#: includes/admin/class-site-health.php:1517
msgid "Notify Administrators"
msgstr ""

#: includes/admin/class-secure.php:277
msgid "Enable notification"
msgstr ""

#: includes/admin/class-secure.php:278
msgid "When enabled, All administrators will be notified when someone has suspicious activities in the Account, Profile & Register forms."
msgstr ""

#: includes/admin/class-secure.php:285
#: includes/admin/class-site-health.php:1522
msgid "Send Immediately"
msgstr ""

#: includes/admin/class-secure.php:286
#: includes/admin/class-site-health.php:1523
msgid "Hourly"
msgstr ""

#: includes/admin/class-secure.php:287
#: includes/admin/class-site-health.php:1524
msgid "Daily"
msgstr ""

#: includes/admin/class-secure.php:289
#: includes/admin/class-site-health.php:1528
msgid "Notification Schedule"
msgstr ""

#: includes/admin/class-secure.php:295
msgid "Allowed hosts for safe redirect (one host per line)"
msgstr ""

#: includes/admin/class-secure.php:296
msgid "Extend allowed hosts for frontend pages redirects."
msgstr ""

#: includes/admin/class-secure.php:306
msgid "Security"
msgstr ""

#: includes/admin/class-secure.php:307
msgid "This feature scans for suspicious registered accounts, bans the usage of administrative capabilities to site subscribers/members, allows the website administrators to force all users to reset their passwords, preventing users from logging-in using their old passwords that may have been exposed."
msgstr ""

#: includes/admin/class-secure.php:334
msgid "Blocked Due to Suspicious Activity"
msgstr ""

#: includes/admin/class-secure.php:337
msgid "Are you sure that you want to restore this account after getting flagged for suspicious activity?"
msgstr ""

#: includes/admin/class-secure.php:337
msgid "Restore Account"
msgstr ""

#: includes/admin/class-site-health.php:50
#: includes/admin/core/class-admin-columns.php:161
#: includes/admin/core/class-admin-columns.php:194
#: includes/admin/core/class-admin-settings.php:1259
#: includes/admin/core/class-admin-settings.php:1367
#: includes/admin/core/list-tables/roles-list-table.php:435
#: includes/admin/core/list-tables/roles-list-table.php:443
#: includes/admin/core/packages/2.1.3-beta3/functions.php:81
#: includes/admin/core/packages/2.1.3-beta3/functions.php:82
#: includes/admin/templates/form/login_customize.php:28
#: includes/admin/templates/form/login_customize.php:76
#: includes/admin/templates/form/login_customize.php:95
#: includes/admin/templates/form/login_customize.php:106
#: includes/admin/templates/form/profile_customize.php:43
#: includes/admin/templates/form/profile_customize.php:109
#: includes/admin/templates/form/profile_customize.php:128
#: includes/admin/templates/form/profile_customize.php:163
#: includes/admin/templates/form/profile_customize.php:184
#: includes/admin/templates/form/profile_customize.php:195
#: includes/admin/templates/form/profile_customize.php:206
#: includes/admin/templates/form/profile_customize.php:217
#: includes/admin/templates/form/register_customize.php:34
#: includes/admin/templates/form/register_customize.php:90
#: includes/admin/templates/form/register_gdpr.php:32
#: includes/admin/templates/role/profile.php:57
#: includes/core/class-builtin.php:1348
#: includes/core/class-builtin.php:1367
#: includes/core/class-builtin.php:1385
#: includes/core/class-builtin.php:1401
#: includes/core/class-user.php:533
#: includes/core/class-user.php:534
#: includes/core/um-actions-account.php:344
msgid "Yes"
msgstr ""

#: includes/admin/class-site-health.php:52
#: blocks-src/um-account/src/index.js:14
#: includes/blocks/um-account/src/index.js:1
msgid "All"
msgstr ""

#: includes/admin/class-site-health.php:53
#: includes/admin/class-site-health.php:603
#: includes/admin/class-site-health.php:650
#: includes/admin/class-site-health.php:1796
#: includes/admin/class-site-health.php:2198
#: includes/admin/core/class-admin-columns.php:109
#: includes/admin/core/class-admin-columns.php:128
#: includes/admin/core/class-admin-settings.php:1103
#: includes/admin/core/class-admin-settings.php:1168
#: includes/admin/templates/form/login_settings.php:19
#: includes/admin/templates/form/register_customize.php:9
#: includes/admin/templates/form/register_customize.php:42
#: includes/admin/templates/role/profile.php:55
msgid "Default"
msgstr ""

#: includes/admin/class-site-health.php:54
msgid "No predefined page"
msgstr ""

#: includes/admin/class-site-health.php:55
msgid "Empty"
msgstr ""

#: includes/admin/class-site-health.php:74
msgid "Are the Ultimate Member templates out of date?"
msgstr ""

#: includes/admin/class-site-health.php:83
msgid "Are the icons in Ultimate Member Forms and Settings out of date?"
msgstr ""

#: includes/admin/class-site-health.php:94
msgid "Are the banned custom fields?"
msgstr ""

#: includes/admin/class-site-health.php:112
msgid "You have the most recent version of custom Ultimate Member templates"
msgstr ""

#: includes/admin/class-site-health.php:120
msgid "Your custom Ultimate Member templates that are situated in the theme have the most recent version and are ready to use."
msgstr ""

#: includes/admin/class-site-health.php:127
msgid "Your custom templates are out of date"
msgstr ""

#: includes/admin/class-site-health.php:132
msgid "Your custom Ultimate Member templates that are situated in the theme are out of date and may break the website's functionality."
msgstr ""

#: includes/admin/class-site-health.php:137
msgid "Check status and update"
msgstr ""

#: includes/admin/class-site-health.php:194
msgid "Your fields' icons in the Ultimate Member Forms are out of date."
msgstr ""

#: includes/admin/class-site-health.php:200
msgid "Related to Ultimate Member Forms: "
msgstr ""

#: includes/admin/class-site-health.php:222
#: includes/admin/class-site-health.php:379
msgid "Edit form fields and update"
msgstr ""

#: includes/admin/class-site-health.php:242
msgid "As soon as legacy icons will be removed old icons may break the website's functionality."
msgstr ""

#: includes/admin/class-site-health.php:260
msgid "You have the most recent version of icons in Ultimate Member forms and settings"
msgstr ""

#: includes/admin/class-site-health.php:268
msgid "Your fields in the Ultimate Member Forms and settings have the most recent version and are ready to use."
msgstr ""

#: includes/admin/class-site-health.php:277
msgid "Some field icons and (or) Ultimate Member settings icons are out of date"
msgstr ""

#: includes/admin/class-site-health.php:336
#: includes/admin/class-site-health.php:342
msgid "Unknown title"
msgstr ""

#: includes/admin/class-site-health.php:352
msgid "Please note that some fields in your Ultimate Member Forms are currently on a restricted list that disallows their use. This is particularly related to the Ultimate Member Forms and their fields below."
msgstr ""

#. translators: %1$s is the field title, %2$s is the field metakey
#: includes/admin/class-site-health.php:361
#, php-format
msgid "%1$s (<code>%2$s</code>)"
msgstr ""

#. translators: %1$s is the form link, %2$s is the form title, %3$s is the form ID
#: includes/admin/class-site-health.php:366
#, php-format
msgid "Fields in <a href=\"%1$s\" target=\"_blank\">%2$s (#ID: %3$s)</a>:"
msgstr ""

#: includes/admin/class-site-health.php:399
msgid "The using meta keys from restricted list in Ultimate Member Forms may break the website's functionality and is unsecure."
msgstr ""

#: includes/admin/class-site-health.php:417
msgid "You have correct Ultimate Member fields"
msgstr ""

#: includes/admin/class-site-health.php:425
msgid "Your all custom Ultimate Member fields are correct."
msgstr ""

#: includes/admin/class-site-health.php:433
msgid "Some field from Ultimate Member forms has banned meta key"
msgstr ""

#: includes/admin/class-site-health.php:466
msgid "Row: "
msgstr ""

#: includes/admin/class-site-health.php:466
msgid "Field: "
msgstr ""

#: includes/admin/class-site-health.php:496
msgid "Ultimate Member Settings"
msgstr ""

#: includes/admin/class-site-health.php:497
msgid "This debug information for your Ultimate Member installation can assist you in getting support."
msgstr ""

#. translators: %1$s is a predefined page title; %2$d is a predefined page ID; %3$s is a predefined page permalink.
#: includes/admin/class-site-health.php:578
#, php-format
msgid "%1$s (ID#%2$d) | %3$s"
msgstr ""

#: includes/admin/class-site-health.php:583
msgid "General > Pages"
msgstr ""

#: includes/admin/class-site-health.php:587
#: includes/admin/core/class-admin-settings.php:1085
msgid "Pages"
msgstr ""

#: includes/admin/class-site-health.php:598
msgid "General > Users"
msgstr ""

#: includes/admin/class-site-health.php:602
#: includes/admin/core/class-admin-settings.php:1100
msgid "Registration Default Role"
msgstr ""

#: includes/admin/class-site-health.php:606
#: includes/admin/core/class-admin-settings.php:1110
msgid "Profile Permalink Base"
msgstr ""

#: includes/admin/class-site-health.php:613
#: includes/admin/core/class-admin-settings.php:1119
msgid "Profile Permalink Base Custom Meta Key"
msgstr ""

#: includes/admin/class-site-health.php:619
#: includes/admin/core/class-admin-settings.php:1128
msgid "User Display Name"
msgstr ""

#: includes/admin/class-site-health.php:625
#: includes/admin/core/class-admin-settings.php:1136
msgid "Display Name Custom Field(s)"
msgstr ""

#: includes/admin/class-site-health.php:634
msgid "Hide author pages (enable author page redirect to user profile)"
msgstr ""

#: includes/admin/class-site-health.php:638
#: includes/admin/core/class-admin-settings.php:1152
msgid "Enable Members Directory"
msgstr ""

#: includes/admin/class-site-health.php:642
msgid "Use Gravatars?"
msgstr ""

#: includes/admin/class-site-health.php:651
#: includes/admin/core/class-admin-settings.php:1169
msgid "404 ( File Not Found response )"
msgstr ""

#: includes/admin/class-site-health.php:652
#: includes/admin/core/class-admin-settings.php:1170
msgid "Mystery Man"
msgstr ""

#: includes/admin/class-site-health.php:653
#: includes/admin/core/class-admin-settings.php:1171
msgid "Identicon"
msgstr ""

#: includes/admin/class-site-health.php:654
#: includes/admin/core/class-admin-settings.php:1172
msgid "Monsterid"
msgstr ""

#: includes/admin/class-site-health.php:655
#: includes/admin/core/class-admin-settings.php:1173
msgid "Wavatar"
msgstr ""

#: includes/admin/class-site-health.php:656
#: includes/admin/core/class-admin-settings.php:1174
msgid "Retro"
msgstr ""

#: includes/admin/class-site-health.php:657
#: includes/admin/core/class-admin-settings.php:1175
msgid "Blank ( a transparent PNG image )"
msgstr ""

#: includes/admin/class-site-health.php:661
#: includes/admin/core/class-admin-settings.php:1165
msgid "Use Gravatar builtin image"
msgstr ""

#: includes/admin/class-site-health.php:666
msgid "Replace Gravatar's Default avatar (Set Default plugin avatar as Gravatar's Default avatar)"
msgstr ""

#: includes/admin/class-site-health.php:676
msgid "Ignore the \"User Role > Registration Options\" if this user is added from the wp-admin dashboard"
msgstr ""

#: includes/admin/class-site-health.php:680
msgid "Delete user comments (enable deleting user comments after deleting a user)"
msgstr ""

#: includes/admin/class-site-health.php:684
msgid "Toggle Password Visibility (enable password show/hide icon on password field)"
msgstr ""

#: includes/admin/class-site-health.php:688
#: includes/admin/core/class-admin-settings.php:1218
#: includes/ajax/class-secure.php:421
msgid "Require Strong Passwords"
msgstr ""

#: includes/admin/class-site-health.php:699
#: includes/admin/core/class-admin-settings.php:1225
msgid "Password minimum length"
msgstr ""

#: includes/admin/class-site-health.php:703
#: includes/admin/core/class-admin-settings.php:1233
msgid "Password maximum length"
msgstr ""

#: includes/admin/class-site-health.php:714
#: includes/admin/core/class-admin-settings.php:1241
msgid "Email activation link expiration (days)"
msgstr ""

#: includes/admin/class-site-health.php:715
msgid "Not expired"
msgstr ""

#: includes/admin/class-site-health.php:718
#: includes/admin/class-site-health.php:1806
#: includes/admin/core/class-admin-settings.php:1255
#: includes/admin/templates/role/profile.php:52
msgid "Avoid indexing profile by search engines"
msgstr ""

#: includes/admin/class-site-health.php:722
#: includes/admin/core/class-admin-settings.php:1265
msgid "User Profile Title"
msgstr ""

#: includes/admin/class-site-health.php:726
#: includes/admin/core/class-admin-settings.php:1272
msgid "User Profile Dynamic Meta Description"
msgstr ""

#: includes/admin/class-site-health.php:735
msgid "General > Account"
msgstr ""

#: includes/admin/class-site-health.php:739
msgid "Display First & Last name fields (enable to display First & Last name fields)"
msgstr ""

#: includes/admin/class-site-health.php:743
msgid "Allow users to change email (enable changing email via the account page)"
msgstr ""

#: includes/admin/class-site-health.php:747
msgid "Require password to update account (enable required password)"
msgstr ""

#: includes/admin/class-site-health.php:751
#: includes/admin/core/class-admin-settings.php:1335
msgid "Password Account Tab"
msgstr ""

#: includes/admin/class-site-health.php:755
#: includes/admin/core/class-admin-settings.php:1348
msgid "Privacy Account Tab"
msgstr ""

#: includes/admin/class-site-health.php:766
#: includes/admin/core/class-admin-settings.php:1299
msgid "Disable First & Last name field editing"
msgstr ""

#: includes/admin/class-site-health.php:770
#: includes/admin/core/class-admin-settings.php:1307
msgid "Require First & Last Name"
msgstr ""

#: includes/admin/class-site-health.php:779
#: includes/admin/core/class-admin-settings.php:1355
msgid "Allow users to hide their profiles from directory"
msgstr ""

#: includes/admin/class-site-health.php:785
#: includes/admin/core/class-admin-settings.php:1363
msgid "Hide profiles from directory by default"
msgstr ""

#: includes/admin/class-site-health.php:793
#: includes/admin/core/class-admin-settings.php:1381
msgid "Notifications Account Tab"
msgstr ""

#: includes/admin/class-site-health.php:802
#: includes/admin/core/class-admin-settings.php:1394
msgid "Delete Account Tab"
msgstr ""

#: includes/admin/class-site-health.php:811
#: includes/admin/core/class-admin-settings.php:1401
#: includes/admin/core/class-admin-settings.php:1411
msgid "Account Deletion Text"
msgstr ""

#: includes/admin/class-site-health.php:816
msgid "Account Deletion without password Text"
msgstr ""

#: includes/admin/class-site-health.php:839
msgid "General > Uploads"
msgstr ""

#: includes/admin/class-site-health.php:843
#: includes/admin/core/class-admin-settings.php:1432
msgid "Change image orientation"
msgstr ""

#: includes/admin/class-site-health.php:847
#: includes/admin/core/class-admin-settings.php:1440
msgid "Image Quality"
msgstr ""

#: includes/admin/class-site-health.php:851
#: includes/admin/core/class-admin-settings.php:1447
msgid "Image Upload Maximum Width (px)"
msgstr ""

#: includes/admin/class-site-health.php:855
#: includes/admin/core/class-admin-settings.php:1460
msgid "Profile Photo Maximum File Size (bytes)"
msgstr ""

#: includes/admin/class-site-health.php:859
#: includes/admin/core/class-admin-settings.php:1467
msgid "Profile Photo Thumbnail Sizes (px)"
msgstr ""

#: includes/admin/class-site-health.php:863
#: includes/admin/core/class-admin-settings.php:1483
msgid "Cover Photo Maximum File Size (bytes)"
msgstr ""

#: includes/admin/class-site-health.php:867
#: includes/admin/core/class-admin-settings.php:1490
msgid "Cover Photo Minimum Width (px)"
msgstr ""

#: includes/admin/class-site-health.php:871
#: includes/admin/core/class-admin-settings.php:1497
msgid "Cover Photo Thumbnail Sizes (px)"
msgstr ""

#: includes/admin/class-site-health.php:907
msgid "Access > Restriction Content"
msgstr ""

#: includes/admin/class-site-health.php:911
#: includes/admin/core/class-admin-settings.php:540
msgid "Global Site Access"
msgstr ""

#: includes/admin/class-site-health.php:912
#: includes/admin/core/class-admin-settings.php:543
msgid "Site accessible to Everyone"
msgstr ""

#: includes/admin/class-site-health.php:912
#: includes/admin/core/class-admin-settings.php:544
msgid "Site accessible to Logged In Users"
msgstr ""

#: includes/admin/class-site-health.php:923
#: includes/admin/core/class-admin-settings.php:551
msgid "Custom Redirect URL"
msgstr ""

#: includes/admin/class-site-health.php:927
#: includes/admin/core/class-admin-settings.php:558
msgid "Exclude the following URLs"
msgstr ""

#: includes/admin/class-site-health.php:931
#: includes/admin/core/class-admin-settings.php:567
msgid "Allow Homepage to be accessible"
msgstr ""

#: includes/admin/class-site-health.php:935
#: includes/admin/core/class-admin-settings.php:574
msgid "Allow Category pages to be accessible"
msgstr ""

#: includes/admin/class-site-health.php:941
#: includes/admin/core/class-admin-settings.php:581
msgid "Restricted Post Title"
msgstr ""

#: includes/admin/class-site-health.php:946
#: includes/admin/core/class-admin-settings.php:588
msgid "Restricted Access Post Title"
msgstr ""

#: includes/admin/class-site-health.php:952
#: includes/admin/core/class-admin-settings.php:595
msgid "Restricted Access Message"
msgstr ""

#: includes/admin/class-site-health.php:956
msgid "Restricted Gutenberg Blocks (enable the \"Content Restriction\" settings for the Gutenberg Blocks)"
msgstr ""

#: includes/admin/class-site-health.php:961
#: includes/admin/core/class-admin-settings.php:645
msgid "Restricted Access Block Message"
msgstr ""

#: includes/admin/class-site-health.php:966
#: includes/admin/core/class-admin-settings.php:676
msgid "Enable the \"Content Restriction\" settings for post types"
msgstr ""

#: includes/admin/class-site-health.php:970
#: includes/admin/core/class-admin-settings.php:686
msgid "Enable the \"Content Restriction\" settings for taxonomies"
msgstr ""

#: includes/admin/class-site-health.php:980
msgid "Access > Other"
msgstr ""

#: includes/admin/class-site-health.php:984
#: includes/admin/core/class-admin-settings.php:1528
msgid "Enable the Reset Password Limit?"
msgstr ""

#: includes/admin/class-site-health.php:990
msgid "Password Limit (maximum reset password limit)"
msgstr ""

#: includes/admin/class-site-health.php:995
msgid "Change Password request limit "
msgstr ""

#: includes/admin/class-site-health.php:999
#: includes/admin/core/class-admin-settings.php:1550
msgid "Only approved user Reset Password"
msgstr ""

#: includes/admin/class-site-health.php:1003
msgid "Blocked Email Addresses"
msgstr ""

#: includes/admin/class-site-health.php:1007
msgid "Blacklist Words"
msgstr ""

#: includes/admin/class-site-health.php:1025
#: includes/admin/core/class-admin-settings.php:1579
msgid "Emails"
msgstr ""

#: includes/admin/class-site-health.php:1029
#: includes/admin/core/class-admin-settings.php:1588
msgid "Admin Email Address"
msgstr ""

#: includes/admin/class-site-health.php:1033
#: includes/admin/core/class-admin-settings.php:1594
msgid "Mail appears from"
msgstr ""

#: includes/admin/class-site-health.php:1037
#: includes/admin/core/class-admin-settings.php:1600
msgid "Mail appears from address"
msgstr ""

#: includes/admin/class-site-health.php:1041
msgid "Use HTML for Emails?"
msgstr ""

#. translators: %s is email template title.
#: includes/admin/class-site-health.php:1050
#, php-format
msgid "Email \"%s\" Enabled"
msgstr ""

#. translators: %s is email template title.
#: includes/admin/class-site-health.php:1057
#, php-format
msgid "\"%s\" Subject"
msgstr ""

#. translators: %s is email template title.
#: includes/admin/class-site-health.php:1063
#, php-format
msgid "Template \"%s\" in theme?"
msgstr ""

#: includes/admin/class-site-health.php:1082
#: includes/admin/class-site-health.php:2097
#: includes/admin/core/class-admin-settings.php:1842
#: includes/admin/core/class-admin-settings.php:1958
#: includes/admin/core/class-admin-settings.php:2068
#: includes/admin/templates/form/login_customize.php:54
#: includes/admin/templates/form/profile_customize.php:87
#: includes/admin/templates/form/register_customize.php:68
msgid "Show inside text field"
msgstr ""

#: includes/admin/class-site-health.php:1083
#: includes/admin/class-site-health.php:2098
#: includes/admin/core/class-admin-settings.php:1843
#: includes/admin/core/class-admin-settings.php:1959
#: includes/admin/core/class-admin-settings.php:2069
#: includes/admin/templates/form/login_customize.php:55
#: includes/admin/templates/form/profile_customize.php:88
#: includes/admin/templates/form/register_customize.php:69
msgid "Show with label"
msgstr ""

#: includes/admin/class-site-health.php:1084
#: includes/admin/class-site-health.php:2099
#: includes/admin/core/class-admin-settings.php:1844
#: includes/admin/core/class-admin-settings.php:1960
#: includes/admin/core/class-admin-settings.php:2070
#: includes/admin/templates/form/login_customize.php:56
#: includes/admin/templates/form/profile_customize.php:89
#: includes/admin/templates/form/register_customize.php:70
msgid "Turn off"
msgstr ""

#: includes/admin/class-site-health.php:1087
#: includes/admin/core/class-admin-settings.php:1799
msgid "Bottom of Icon"
msgstr ""

#: includes/admin/class-site-health.php:1088
#: includes/admin/core/class-admin-settings.php:1800
msgid "Left of Icon (right for RTL)"
msgstr ""

#: includes/admin/class-site-health.php:1093
#: includes/admin/class-site-health.php:1303
#: includes/admin/class-site-health.php:1362
msgid "No template name"
msgstr ""

#: includes/admin/class-site-health.php:1102
msgid "Appearance > Profile"
msgstr ""

#: includes/admin/class-site-health.php:1106
#: includes/admin/core/class-admin-settings.php:1634
msgid "Profile Default Template"
msgstr ""

#. translators: %1$s - profile template name, %2$s - profile template filename
#. translators: %1$s - register template name, %2$s - register template filename
#. translators: %1$s - login template name, %2$s - login template filename
#: includes/admin/class-site-health.php:1108
#: includes/admin/class-site-health.php:1322
#: includes/admin/class-site-health.php:1375
#, php-format
msgid "%1$s (filename: %2$s.php)"
msgstr ""

#: includes/admin/class-site-health.php:1111
#: includes/admin/core/class-admin-settings.php:1643
msgid "Profile Maximum Width"
msgstr ""

#: includes/admin/class-site-health.php:1115
#: includes/admin/core/class-admin-settings.php:1651
msgid "Profile Area Maximum Width"
msgstr ""

#: includes/admin/class-site-health.php:1119
#: includes/admin/core/class-admin-settings.php:1665
msgid "Default Profile Photo"
msgstr ""

#: includes/admin/class-site-health.php:1123
#: includes/admin/class-site-health.php:2346
#: includes/admin/core/class-admin-settings.php:1676
#: includes/admin/templates/form/profile_customize.php:157
msgid "Disable Profile Photo Upload"
msgstr ""

#: includes/admin/class-site-health.php:1127
#: includes/admin/class-site-health.php:2354
#: includes/admin/core/class-admin-settings.php:1683
#: includes/admin/templates/form/profile_customize.php:169
msgid "Profile Photo Size"
msgstr ""

#: includes/admin/class-site-health.php:1131
#: includes/admin/core/class-admin-settings.php:1700
msgid "Default Cover Photo"
msgstr ""

#: includes/admin/class-site-health.php:1135
#: includes/admin/core/class-admin-settings.php:1708
msgid "Profile Cover Photos"
msgstr ""

#: includes/admin/class-site-health.php:1142
#: includes/admin/core/class-admin-settings.php:1716
msgid "Profile Cover Size"
msgstr ""

#: includes/admin/class-site-health.php:1146
#: includes/admin/core/class-admin-settings.php:1726
msgid "Profile Cover Ratio"
msgstr ""

#: includes/admin/class-site-health.php:1155
#: includes/admin/core/class-admin-settings.php:1747
msgid "Profile Header Meta Text Icon"
msgstr ""

#: includes/admin/class-site-health.php:1159
#: includes/admin/core/class-admin-settings.php:1756
msgid "Show display name in profile header"
msgstr ""

#: includes/admin/class-site-health.php:1163
#: includes/admin/core/class-admin-settings.php:1764
msgid "Show social links in profile header"
msgstr ""

#: includes/admin/class-site-health.php:1167
#: includes/admin/core/class-admin-settings.php:1772
msgid "Show user description in profile header"
msgstr ""

#: includes/admin/class-site-health.php:1175
#: includes/admin/core/class-admin-settings.php:1779
msgid "User description maximum chars"
msgstr ""

#: includes/admin/class-site-health.php:1184
#: includes/admin/core/class-admin-settings.php:1789
msgid "Enable HTML support for user description"
msgstr ""

#: includes/admin/class-site-health.php:1188
#: includes/admin/core/class-admin-settings.php:1795
msgid "Profile Header Menu Position"
msgstr ""

#: includes/admin/class-site-health.php:1192
#: includes/admin/core/class-admin-settings.php:1813
msgid "Profile Primary Button Text"
msgstr ""

#: includes/admin/class-site-health.php:1196
#: includes/admin/core/class-admin-settings.php:1821
msgid "Profile Secondary Button"
msgstr ""

#: includes/admin/class-site-health.php:1204
msgid "Profile Secondary Button Text "
msgstr ""

#: includes/admin/class-site-health.php:1213
#: includes/admin/core/class-admin-settings.php:1838
msgid "Profile Field Icons"
msgstr ""

#: includes/admin/class-site-health.php:1217
#: includes/admin/core/class-admin-settings.php:1852
msgid "Show a custom message if profile is empty"
msgstr ""

#: includes/admin/class-site-health.php:1225
#: includes/admin/core/class-admin-settings.php:1860
msgid "Show the emoticon"
msgstr ""

#: includes/admin/class-site-health.php:1234
msgid "Appearance > Profile Menu"
msgstr ""

#: includes/admin/class-site-health.php:1239
#: includes/admin/core/class-admin-settings.php:359
msgid "Enable profile menu"
msgstr ""

#. translators: %s Profile Tab Title
#. translators: %s: Tab title
#: includes/admin/class-site-health.php:1256
#: includes/admin/core/class-admin-settings.php:388
#: includes/admin/core/class-admin-settings.php:406
#, php-format
msgid "%s Tab"
msgstr ""

#. translators: %s Profile Tab Title
#. translators: %s: Tab title
#: includes/admin/class-site-health.php:1265
#: includes/admin/core/class-admin-settings.php:415
#, php-format
msgid "Who can see %s Tab?"
msgstr ""

#: includes/admin/class-site-health.php:1291
#: includes/admin/core/class-admin-settings.php:456
msgid "Profile menu default tab"
msgstr ""

#: includes/admin/class-site-health.php:1295
#: includes/admin/core/class-admin-settings.php:474
msgid "Enable menu icons in desktop view"
msgstr ""

#: includes/admin/class-site-health.php:1307
#: includes/admin/core/class-admin-settings.php:1905
#: includes/admin/core/class-admin-settings.php:1999
msgid "Centered"
msgstr ""

#: includes/admin/class-site-health.php:1308
#: includes/admin/core/class-admin-settings.php:1906
#: includes/admin/core/class-admin-settings.php:2000
msgid "Left aligned"
msgstr ""

#: includes/admin/class-site-health.php:1309
#: includes/admin/core/class-admin-settings.php:1907
#: includes/admin/core/class-admin-settings.php:2001
msgid "Right aligned"
msgstr ""

#: includes/admin/class-site-health.php:1316
msgid "Appearance > Registration Form"
msgstr ""

#: includes/admin/class-site-health.php:1320
#: includes/admin/core/class-admin-settings.php:1884
msgid "Registration Default Template"
msgstr ""

#: includes/admin/class-site-health.php:1325
#: includes/admin/core/class-admin-settings.php:1893
msgid "Registration Maximum Width"
msgstr ""

#: includes/admin/class-site-health.php:1329
#: includes/admin/core/class-admin-settings.php:1901
msgid "Registration Shortcode Alignment"
msgstr ""

#: includes/admin/class-site-health.php:1333
msgid "Registration Primary Button Text "
msgstr ""

#: includes/admin/class-site-health.php:1337
#: includes/admin/core/class-admin-settings.php:1928
msgid "Registration Secondary Button"
msgstr ""

#: includes/admin/class-site-health.php:1345
#: includes/admin/core/class-admin-settings.php:1936
msgid "Registration Secondary Button Text"
msgstr ""

#: includes/admin/class-site-health.php:1349
#: includes/admin/core/class-admin-settings.php:1945
msgid "Registration Secondary Button URL"
msgstr ""

#: includes/admin/class-site-health.php:1355
#: includes/admin/core/class-admin-settings.php:1954
msgid "Registration Field Icons"
msgstr ""

#: includes/admin/class-site-health.php:1369
msgid "Appearance > Login Form"
msgstr ""

#: includes/admin/class-site-health.php:1373
#: includes/admin/core/class-admin-settings.php:1978
msgid "Login Default Template"
msgstr ""

#: includes/admin/class-site-health.php:1378
#: includes/admin/core/class-admin-settings.php:1987
msgid "Login Maximum Width"
msgstr ""

#: includes/admin/class-site-health.php:1382
#: includes/admin/core/class-admin-settings.php:1995
msgid "Login Shortcode Alignment"
msgstr ""

#: includes/admin/class-site-health.php:1386
#: includes/admin/core/class-admin-settings.php:2014
msgid "Login Primary Button Text"
msgstr ""

#: includes/admin/class-site-health.php:1390
#: includes/admin/core/class-admin-settings.php:2022
msgid "Login Secondary Button"
msgstr ""

#: includes/admin/class-site-health.php:1398
#: includes/admin/core/class-admin-settings.php:2030
msgid "Login Secondary Button Text"
msgstr ""

#: includes/admin/class-site-health.php:1402
#: includes/admin/core/class-admin-settings.php:2039
msgid "Login Secondary Button URL"
msgstr ""

#: includes/admin/class-site-health.php:1411
#: includes/admin/core/class-admin-settings.php:2048
msgid "Login Forgot Password Link"
msgstr ""

#: includes/admin/class-site-health.php:1415
#: includes/admin/core/class-admin-settings.php:2057
msgid "Show \"Remember Me\" checkbox"
msgstr ""

#: includes/admin/class-site-health.php:1419
#: includes/admin/core/class-admin-settings.php:2064
msgid "Login Field Icons"
msgstr ""

#: includes/admin/class-site-health.php:1438
msgid "Advanced > General"
msgstr ""

#: includes/admin/class-site-health.php:1442
msgid "Required fields' asterisk (Show an asterisk for required fields)"
msgstr ""

#: includes/admin/class-site-health.php:1446
msgid "Disable Cache User Profile"
msgstr ""

#: includes/admin/class-site-health.php:1450
#: includes/admin/core/class-admin-settings.php:2110
msgid "Remove Data on Uninstall?"
msgstr ""

#: includes/admin/class-site-health.php:1457
msgid "Advanced > Features"
msgstr ""

#: includes/admin/class-site-health.php:1461
#: includes/admin/core/class-admin-settings.php:2143
msgid "Enable Gutenberg Blocks"
msgstr ""

#: includes/admin/class-site-health.php:1465
#: includes/admin/core/class-admin-settings.php:715
msgid "Enable custom table for usermeta"
msgstr ""

#: includes/admin/class-site-health.php:1473
msgid "Disable pre-queries for restriction content logic (advanced)"
msgstr ""

#: includes/admin/class-site-health.php:1493
msgid "Advanced > Security"
msgstr ""

#: includes/admin/class-site-health.php:1509
msgid "Administrative capabilities ban (enable ban for administrative capabilities)"
msgstr ""

#: includes/admin/class-site-health.php:1538
msgid "Allowed hosts for safe redirect"
msgstr ""

#: includes/admin/class-site-health.php:1544
msgid "Advanced > Developers"
msgstr ""

#: includes/admin/class-site-health.php:1548
msgid "Allowed Choice Callbacks"
msgstr ""

#: includes/admin/class-site-health.php:1552
msgid "REST API version"
msgstr ""

#: includes/admin/class-site-health.php:1556
msgid "Allow external link redirect confirm (enable JS.confirm for external links)"
msgstr ""

#: includes/admin/class-site-health.php:1573
#: includes/admin/core/class-admin-settings.php:2084
msgid "Licenses"
msgstr ""

#: includes/admin/class-site-health.php:1629
msgid "Ultimate Member User Roles"
msgstr ""

#: includes/admin/class-site-health.php:1630
msgid "This debug information about user roles."
msgstr ""

#: includes/admin/class-site-health.php:1633
msgid "User Roles (priority)"
msgstr ""

#: includes/admin/class-site-health.php:1637
msgid "WordPress Default New User Role"
msgstr ""

#: includes/admin/class-site-health.php:1658
#: includes/admin/templates/role/admin-permissions.php:17
msgid "Can access wp-admin?"
msgstr ""

#: includes/admin/class-site-health.php:1667
#: includes/admin/templates/role/admin-permissions.php:24
msgid "Force hiding adminbar in frontend?"
msgstr ""

#: includes/admin/class-site-health.php:1676
#: includes/admin/templates/role/admin-permissions.php:31
msgid "Can edit other member accounts?"
msgstr ""

#: includes/admin/class-site-health.php:1695
#: includes/admin/templates/role/admin-permissions.php:38
msgid "Can edit these user roles only"
msgstr ""

#: includes/admin/class-site-health.php:1704
#: includes/admin/templates/role/admin-permissions.php:48
msgid "Can delete other member accounts?"
msgstr ""

#: includes/admin/class-site-health.php:1723
#: includes/admin/templates/role/admin-permissions.php:55
msgid "Can delete these user roles only"
msgstr ""

#: includes/admin/class-site-health.php:1732
#: includes/admin/templates/role/general.php:14
msgid "Can edit their profile?"
msgstr ""

#: includes/admin/class-site-health.php:1741
#: includes/admin/templates/role/general.php:21
msgid "Can delete their account?"
msgstr ""

#: includes/admin/class-site-health.php:1750
#: includes/admin/templates/role/profile.php:17
msgid "Can view other member profiles?"
msgstr ""

#: includes/admin/class-site-health.php:1769
#: includes/admin/templates/role/profile.php:24
msgid "Can view these user roles only"
msgstr ""

#: includes/admin/class-site-health.php:1778
#: includes/admin/templates/role/profile.php:35
msgid "Can make their profile private?"
msgstr ""

#: includes/admin/class-site-health.php:1787
#: includes/admin/templates/role/profile.php:43
msgid "Can view/access private profiles?"
msgstr ""

#: includes/admin/class-site-health.php:1801
msgid "No such option"
msgstr ""

#: includes/admin/class-site-health.php:1810
#: includes/admin/templates/role/home.php:15
msgid "Can view default homepage?"
msgstr ""

#: includes/admin/class-site-health.php:1818
#: includes/admin/templates/role/home.php:22
msgid "Custom Homepage Redirect"
msgstr ""

#: includes/admin/class-site-health.php:1825
#: includes/admin/templates/role/register.php:25
msgid "Auto Approve"
msgstr ""

#: includes/admin/class-site-health.php:1826
#: includes/admin/templates/role/register.php:26
msgid "Require Email Activation"
msgstr ""

#: includes/admin/class-site-health.php:1827
#: includes/admin/templates/role/register.php:27
msgid "Require Admin Review"
msgstr ""

#: includes/admin/class-site-health.php:1833
#: includes/admin/templates/role/register.php:21
msgid "Registration Status"
msgstr ""

#: includes/admin/class-site-health.php:1841
#: includes/admin/class-site-health.php:1947
#: includes/admin/class-site-health.php:2199
#: includes/admin/templates/form/login_settings.php:20
#: includes/admin/templates/role/login.php:19
#: includes/admin/templates/role/register.php:37
msgid "Redirect to profile"
msgstr ""

#: includes/admin/class-site-health.php:1842
#: includes/admin/class-site-health.php:1865
#: includes/admin/class-site-health.php:1915
#: includes/admin/class-site-health.php:1948
#: includes/admin/class-site-health.php:2200
#: includes/admin/templates/form/login_settings.php:21
#: includes/admin/templates/role/login.php:20
#: includes/admin/templates/role/register.php:38
#: includes/admin/templates/role/register.php:57
#: includes/admin/templates/role/register.php:99
msgid "Redirect to URL"
msgstr ""

#: includes/admin/class-site-health.php:1848
msgid "Auto-approve action"
msgstr ""

#: includes/admin/class-site-health.php:1857
#: includes/admin/class-site-health.php:1898
#: includes/admin/class-site-health.php:1939
#: includes/admin/class-site-health.php:1964
#: includes/admin/class-site-health.php:1989
#: includes/admin/class-site-health.php:2010
#: includes/admin/class-site-health.php:2270
#: includes/admin/templates/form/login_settings.php:29
#: includes/admin/templates/role/delete.php:26
#: includes/admin/templates/role/login.php:28
#: includes/admin/templates/role/logout.php:26
#: includes/admin/templates/role/register.php:45
#: includes/admin/templates/role/register.php:71
#: includes/admin/templates/role/register.php:113
msgid "Set Custom Redirect URL"
msgstr ""

#: includes/admin/class-site-health.php:1864
#: includes/admin/class-site-health.php:1914
#: includes/admin/templates/role/register.php:56
#: includes/admin/templates/role/register.php:98
#: assets/js/admin/block-restrictions.js:196
msgid "Show custom message"
msgstr ""

#: includes/admin/class-site-health.php:1871
#: includes/admin/templates/role/register.php:78
msgid "Login user after validating the activation link?"
msgstr ""

#: includes/admin/class-site-health.php:1880
#: includes/admin/class-site-health.php:1921
#: includes/admin/templates/role/register.php:33
#: includes/admin/templates/role/register.php:52
#: includes/admin/templates/role/register.php:94
msgid "Action to be taken after registration"
msgstr ""

#: includes/admin/class-site-health.php:1890
#: includes/admin/class-site-health.php:1931
#: includes/admin/templates/role/register.php:64
#: includes/admin/templates/role/register.php:106
msgid "Personalize the custom message"
msgstr ""

#: includes/admin/class-site-health.php:1907
#: includes/admin/templates/role/register.php:86
msgid "URL redirect after email activation"
msgstr ""

#: includes/admin/class-site-health.php:1949
#: includes/admin/class-site-health.php:2201
#: includes/admin/templates/form/login_settings.php:22
#: includes/admin/templates/role/login.php:21
msgid "Refresh active page"
msgstr ""

#: includes/admin/class-site-health.php:1950
#: includes/admin/class-site-health.php:2202
#: includes/admin/templates/form/login_settings.php:23
#: includes/admin/templates/role/login.php:22
msgid "Redirect to WordPress Admin"
msgstr ""

#: includes/admin/class-site-health.php:1956
#: includes/admin/templates/role/login.php:15
msgid "Action to be taken after login"
msgstr ""

#: includes/admin/class-site-health.php:1972
#: includes/admin/templates/role/delete.php:19
#: includes/admin/templates/role/logout.php:19
msgid "Go to Homepage"
msgstr ""

#: includes/admin/class-site-health.php:1973
#: includes/admin/templates/role/delete.php:20
#: includes/admin/templates/role/logout.php:20
msgid "Go to Custom URL"
msgstr ""

#: includes/admin/class-site-health.php:1981
#: includes/admin/templates/role/logout.php:15
msgid "Action to be taken after logout"
msgstr ""

#: includes/admin/class-site-health.php:2002
#: includes/admin/templates/role/delete.php:15
msgid "Action to be taken after account is deleted"
msgstr ""

#: includes/admin/class-site-health.php:2020
#: includes/admin/core/class-admin-metabox.php:997
msgid "WP Capabilities"
msgstr ""

#: includes/admin/class-site-health.php:2050
msgid " role settings"
msgstr ""

#: includes/admin/class-site-health.php:2051
msgid "This debug information about user role."
msgstr ""

#: includes/admin/class-site-health.php:2083
msgid "Ultimate Member Forms"
msgstr ""

#: includes/admin/class-site-health.php:2084
msgid "This debug information for your Ultimate Member forms."
msgstr ""

#: includes/admin/class-site-health.php:2087
msgid "UM Forms"
msgstr ""

#: includes/admin/class-site-health.php:2088
msgid "No Ultimate Member Forms"
msgstr ""

#: includes/admin/class-site-health.php:2107
#: includes/admin/class-site-health.php:2482
#: includes/admin/core/class-admin-columns.php:110
#: includes/admin/core/class-admin-columns.php:129
#: includes/admin/core/class-admin-metabox.php:864
#: includes/admin/core/class-admin-metabox.php:1031
msgid "Shortcode"
msgstr ""

#: includes/admin/class-site-health.php:2111
#: includes/admin/core/class-admin-columns.php:108
msgid "Type"
msgstr ""

#: includes/admin/class-site-health.php:2120
#: includes/admin/class-site-health.php:2210
#: includes/admin/class-site-health.php:2278
#: includes/admin/templates/form/login_customize.php:23
#: includes/admin/templates/form/profile_customize.php:38
#: includes/admin/templates/form/register_customize.php:29
msgid "Apply custom settings to this form"
msgstr ""

#: includes/admin/class-site-health.php:2128
msgid "User registration role"
msgstr ""

#: includes/admin/class-site-health.php:2132
#: includes/admin/class-site-health.php:2218
#: includes/admin/class-site-health.php:2290
#: includes/admin/class-site-health.php:2486
#: includes/admin/core/class-admin-settings.php:1627
#: includes/admin/core/class-admin-settings.php:1878
#: includes/admin/core/class-admin-settings.php:1972
#: includes/admin/core/list-tables/version-template-list-table.php:179
#: includes/admin/core/list-tables/version-template-list-table.php:209
#: includes/admin/templates/directory/appearance.php:17
#: includes/admin/templates/form/login_customize.php:34
#: includes/admin/templates/form/profile_customize.php:59
#: includes/admin/templates/form/register_customize.php:48
msgid "Template"
msgstr ""

#: includes/admin/class-site-health.php:2136
#: includes/admin/class-site-health.php:2222
#: includes/admin/class-site-health.php:2294
#: includes/admin/templates/form/login_customize.php:42
#: includes/admin/templates/form/profile_customize.php:67
#: includes/admin/templates/form/register_customize.php:56
msgid "Max. Width (px)"
msgstr ""

#: includes/admin/class-site-health.php:2140
#: includes/admin/class-site-health.php:2226
#: includes/admin/class-site-health.php:2302
#: includes/admin/templates/form/login_customize.php:50
#: includes/admin/templates/form/profile_customize.php:83
#: includes/admin/templates/form/register_customize.php:64
msgid "Field Icons"
msgstr ""

#: includes/admin/class-site-health.php:2144
#: includes/admin/class-site-health.php:2230
#: includes/admin/class-site-health.php:2306
#: includes/admin/templates/form/login_customize.php:63
#: includes/admin/templates/form/profile_customize.php:96
#: includes/admin/templates/form/register_customize.php:77
msgid "Primary Button Text"
msgstr ""

#: includes/admin/class-site-health.php:2148
#: includes/admin/class-site-health.php:2234
#: includes/admin/class-site-health.php:2310
#: includes/admin/templates/form/login_customize.php:71
#: includes/admin/templates/form/profile_customize.php:104
#: includes/admin/templates/form/register_customize.php:85
msgid "Show Secondary Button"
msgstr ""

#: includes/admin/class-site-health.php:2156
#: includes/admin/class-site-health.php:2242
#: includes/admin/class-site-health.php:2318
#: includes/admin/templates/form/login_customize.php:82
#: includes/admin/templates/form/profile_customize.php:115
#: includes/admin/templates/form/register_customize.php:96
msgid "Secondary Button Text"
msgstr ""

#: includes/admin/class-site-health.php:2165
msgid "Enable privacy policy agreement"
msgstr ""

#: includes/admin/class-site-health.php:2175
msgid "Privacy policy content"
msgstr ""

#: includes/admin/class-site-health.php:2179
#: includes/admin/templates/form/register_gdpr.php:46
msgid "Toggle Show text"
msgstr ""

#: includes/admin/class-site-health.php:2183
#: includes/admin/templates/form/register_gdpr.php:54
msgid "Toggle Hide text"
msgstr ""

#: includes/admin/class-site-health.php:2187
#: includes/admin/templates/form/register_gdpr.php:62
msgid "Checkbox agreement description"
msgstr ""

#: includes/admin/class-site-health.php:2191
#: includes/admin/templates/form/register_gdpr.php:70
msgid "Error Text"
msgstr ""

#: includes/admin/class-site-health.php:2250
#: includes/admin/templates/form/login_customize.php:90
msgid "Show Forgot Password Link?"
msgstr ""

#: includes/admin/class-site-health.php:2254
#: includes/admin/templates/form/login_customize.php:101
msgid "Show \"Remember Me\"?"
msgstr ""

#: includes/admin/class-site-health.php:2262
#: includes/admin/templates/form/login_settings.php:15
msgid "Redirection after Login"
msgstr ""

#: includes/admin/class-site-health.php:2286
#: includes/admin/templates/form/profile_customize.php:50
msgid "Make this profile form role-specific"
msgstr ""

#: includes/admin/class-site-health.php:2298
#: includes/admin/templates/form/profile_customize.php:75
msgid "Profile Area Max. Width (px)"
msgstr ""

#: includes/admin/class-site-health.php:2326
#: includes/admin/core/class-admin-settings.php:1709
#: includes/admin/templates/form/profile_customize.php:123
msgid "Enable Cover Photos"
msgstr ""

#: includes/admin/class-site-health.php:2334
#: includes/admin/templates/form/profile_customize.php:134
msgid "Cover Photo Size"
msgstr ""

#: includes/admin/class-site-health.php:2338
#: includes/admin/templates/form/profile_customize.php:143
msgid "Cover photo ratio"
msgstr ""

#: includes/admin/class-site-health.php:2358
#: includes/admin/templates/form/profile_customize.php:178
msgid "Make Profile Photo Required"
msgstr ""

#: includes/admin/class-site-health.php:2366
#: includes/admin/templates/form/profile_customize.php:190
msgid "Show display name in profile header?"
msgstr ""

#: includes/admin/class-site-health.php:2370
#: includes/admin/templates/form/profile_customize.php:201
msgid "Show social links in profile header?"
msgstr ""

#: includes/admin/class-site-health.php:2374
#: includes/admin/templates/form/profile_customize.php:212
msgid "Show user description in profile header?"
msgstr ""

#: includes/admin/class-site-health.php:2384
#: includes/admin/templates/form/profile_settings.php:25
msgid "Field(s) to show in user meta"
msgstr ""

#. translators: %s is the form title.
#: includes/admin/class-site-health.php:2428
#, php-format
msgid " - Form \"%s\" settings"
msgstr ""

#: includes/admin/class-site-health.php:2429
msgid "This debug information for your Ultimate Member form."
msgstr ""

#: includes/admin/class-site-health.php:2460
msgid "Ultimate Member Directories"
msgstr ""

#: includes/admin/class-site-health.php:2461
msgid "This debug information about Ultimate Member directories."
msgstr ""

#: includes/admin/class-site-health.php:2464
msgid "Member directories"
msgstr ""

#: includes/admin/class-site-health.php:2465
msgid "No member directories"
msgstr ""

#: includes/admin/class-site-health.php:2490
msgid "View types"
msgstr ""

#: includes/admin/class-site-health.php:2494
#: includes/admin/templates/directory/general.php:57
msgid "Default view type"
msgstr ""

#: includes/admin/class-site-health.php:2532
msgid "User Roles to display"
msgstr ""

#: includes/admin/class-site-health.php:2536
#: includes/admin/templates/directory/general.php:75
msgid "Only show members who have uploaded a profile photo"
msgstr ""

#: includes/admin/class-site-health.php:2540
#: includes/admin/templates/directory/general.php:82
msgid "Only show members who have uploaded a cover photo"
msgstr ""

#: includes/admin/class-site-health.php:2544
msgid "Only show specific users"
msgstr ""

#: includes/admin/class-site-health.php:2548
msgid "Exclude specific users"
msgstr ""

#: includes/admin/class-site-health.php:2581
#: includes/admin/templates/directory/sorting.php:14
msgid "Default sort users by"
msgstr ""

#: includes/admin/class-site-health.php:2589
#: includes/admin/core/class-admin-forms.php:1790
#: includes/admin/core/class-admin-forms.php:1819
#: includes/admin/templates/directory/sorting.php:22
#: assets/js/admin/forms.js:355
msgid "Meta key"
msgstr ""

#: includes/admin/class-site-health.php:2593
#: includes/admin/core/class-admin-forms.php:1791
#: includes/admin/templates/directory/sorting.php:30
#: assets/js/admin/forms.js:356
msgid "Data type"
msgstr ""

#: includes/admin/class-site-health.php:2597
#: includes/admin/core/class-admin-forms.php:1794
#: includes/admin/templates/directory/sorting.php:39
#: assets/js/admin/forms.js:359
msgid "Order"
msgstr ""

#: includes/admin/class-site-health.php:2598
msgid "Ascending"
msgstr ""

#: includes/admin/class-site-health.php:2598
msgid "Descending"
msgstr ""

#: includes/admin/class-site-health.php:2601
#: includes/admin/templates/directory/sorting.php:51
msgid "Label of custom sort"
msgstr ""

#: includes/admin/class-site-health.php:2609
#: includes/admin/templates/directory/sorting.php:59
msgid "Enable custom sorting"
msgstr ""

#: includes/admin/class-site-health.php:2621
#: includes/admin/class-site-health.php:2629
msgid "Field(s) to enable in sorting"
msgstr ""

#: includes/admin/class-site-health.php:2640
#: includes/admin/templates/directory/search.php:29
msgid "Enable Search feature"
msgstr ""

#: includes/admin/class-site-health.php:2658
#: includes/admin/templates/directory/search.php:36
msgid "User Roles that can use search"
msgstr ""

#: includes/admin/class-site-health.php:2666
#: includes/admin/templates/directory/search.php:46
msgid "Exclude fields from search"
msgstr ""

#: includes/admin/class-site-health.php:2675
#: includes/admin/templates/directory/search.php:58
msgid "Fields to search by"
msgstr ""

#: includes/admin/class-site-health.php:2684
#: includes/admin/templates/directory/search.php:70
msgid "Enable Filters feature"
msgstr ""

#: includes/admin/class-site-health.php:2702
#: includes/admin/templates/directory/search.php:77
msgid "User Roles that can use filters"
msgstr ""

#: includes/admin/class-site-health.php:2716
msgid "Filter meta to enable"
msgstr ""

#: includes/admin/class-site-health.php:2724
#: includes/admin/templates/directory/search.php:98
msgid "Expand the filter bar by default"
msgstr ""

#: includes/admin/class-site-health.php:2732
#: includes/admin/templates/directory/search.php:106
msgid "Can filter bar be collapsed"
msgstr ""

#. translators: %1$s is the "From" value, %2$s is the "To" value.
#: includes/admin/class-site-health.php:2748
#, php-format
msgid "From %1$s; To %2$s"
msgstr ""

#: includes/admin/class-site-health.php:2757
#: includes/admin/templates/directory/search.php:114
msgid "Admin filtering"
msgstr ""

#: includes/admin/class-site-health.php:2765
#: includes/admin/templates/directory/profile.php:19
msgid "Enable Profile Photo"
msgstr ""

#: includes/admin/class-site-health.php:2769
#: includes/admin/templates/directory/profile.php:25
msgid "Enable Cover Photo"
msgstr ""

#: includes/admin/class-site-health.php:2773
#: includes/admin/templates/directory/profile.php:32
msgid "Show display name"
msgstr ""

#: includes/admin/class-site-health.php:2800
#: includes/admin/templates/directory/profile.php:38
msgid "Show tagline below profile name"
msgstr ""

#: includes/admin/class-site-health.php:2816
msgid "Field to display in tagline"
msgstr ""

#: includes/admin/class-site-health.php:2825
#: includes/admin/templates/directory/profile.php:55
msgid "Show extra user information below tagline?"
msgstr ""

#: includes/admin/class-site-health.php:2841
msgid "Field to display in extra user information section"
msgstr ""

#: includes/admin/class-site-health.php:2850
#: includes/admin/templates/directory/profile.php:72
msgid "Show social connect icons in extra user information section"
msgstr ""

#: includes/admin/class-site-health.php:2854
#: includes/admin/templates/directory/profile.php:79
msgid "Hide extra user information to the reveal section"
msgstr ""

#: includes/admin/class-site-health.php:2858
#: includes/admin/templates/directory/pagination.php:17
msgid "Show results only after search/filtration"
msgstr ""

#: includes/admin/class-site-health.php:2862
#: includes/admin/class-site-health.php:2870
#: includes/admin/templates/directory/pagination.php:25
msgid "Maximum number of profiles"
msgstr ""

#: includes/admin/class-site-health.php:2866
#: includes/admin/templates/directory/pagination.php:33
msgid "Number of profiles per page"
msgstr ""

#: includes/admin/class-site-health.php:2874
#: includes/admin/templates/directory/pagination.php:49
msgid "Results Text"
msgstr ""

#: includes/admin/class-site-health.php:2878
#: includes/admin/templates/directory/pagination.php:57
msgid "Single Result Text"
msgstr ""

#: includes/admin/class-site-health.php:2882
#: includes/admin/templates/directory/pagination.php:65
msgid "Custom text if no users were found"
msgstr ""

#. translators: %s is the member directory title.
#: includes/admin/class-site-health.php:2911
#, php-format
msgid " - Member directory \"%s\" settings"
msgstr ""

#: includes/admin/class-site-health.php:2912
msgid "This debug information for your Ultimate Member directory."
msgstr ""

#: includes/admin/class-users-columns.php:49
#: includes/admin/core/list-tables/version-template-list-table.php:212
msgid "Status"
msgstr ""

#: includes/admin/class-users-columns.php:88
msgid "Approve"
msgstr ""

#: includes/admin/class-users-columns.php:100
msgid "Are you sure you want to reject this user membership?"
msgstr ""

#: includes/admin/class-users-columns.php:100
msgid "Reject"
msgstr ""

#: includes/admin/class-users-columns.php:112
#: includes/admin/class-users-columns.php:281
msgid "Reactivate"
msgstr ""

#: includes/admin/class-users-columns.php:124
msgid "Put as pending"
msgstr ""

#: includes/admin/class-users-columns.php:136
#: includes/frontend/class-users.php:46
msgid "Send activation email"
msgstr ""

#: includes/admin/class-users-columns.php:138
#: includes/frontend/class-users.php:48
msgid "Resend activation email"
msgstr ""

#: includes/admin/class-users-columns.php:153
msgid "Are you sure you want to deactivate this user?"
msgstr ""

#: includes/admin/class-users-columns.php:153
#: includes/admin/class-users-columns.php:280
msgid "Deactivate"
msgstr ""

#: includes/admin/class-users-columns.php:237
#: templates/account.php:61
#: templates/account.php:102
msgid "View profile"
msgstr ""

#: includes/admin/class-users-columns.php:243
msgid "Info"
msgstr ""

#: includes/admin/class-users-columns.php:276
#: includes/frontend/class-users.php:34
msgid "Approve Membership"
msgstr ""

#: includes/admin/class-users-columns.php:277
#: includes/frontend/class-users.php:37
msgid "Reject Membership"
msgstr ""

#: includes/admin/class-users-columns.php:278
#: includes/frontend/class-users.php:43
msgid "Put as Pending Review"
msgstr ""

#: includes/admin/class-users-columns.php:279
msgid "Resend Activation E-mail"
msgstr ""

#: includes/admin/class-users-columns.php:361
#: includes/admin/class-users-columns.php:363
msgid "All Statuses"
msgstr ""

#: includes/admin/class-users-columns.php:373
msgid "Filter"
msgstr ""

#: includes/admin/class-users-columns.php:406
msgid "You do not have enough permissions to do that."
msgstr ""

#: includes/admin/core/class-admin-builder.php:244
#: includes/admin/core/class-admin-builder.php:246
msgid "Here you can setup conditional logic to show/hide this field based on specific fields value or conditions"
msgstr ""

#: includes/admin/core/class-admin-builder.php:263
msgid "Use the condition operator `equals to` or `not equals` if the parent field has a single option."
msgstr ""

#: includes/admin/core/class-admin-builder.php:264
msgid "Use the condition operator `greater than` or `less than` if the parent field is a number."
msgstr ""

#: includes/admin/core/class-admin-builder.php:265
msgid "Use the condition operator `contains` if the parent field has multiple options."
msgstr ""

#: includes/admin/core/class-admin-builder.php:268
msgid "Add new rule"
msgstr ""

#: includes/admin/core/class-admin-builder.php:269
msgid "Reset all rules"
msgstr ""

#: includes/admin/core/class-admin-builder.php:323
#: includes/admin/core/class-admin-builder.php:597
#: includes/admin/core/class-admin-builder.php:731
#: includes/admin/core/class-admin-dragdrop.php:41
#: includes/admin/core/class-admin-menu.php:142
msgid "Please login as administrator"
msgstr ""

#: includes/admin/core/class-admin-builder.php:425
#: includes/admin/core/class-admin-builder.php:479
#: includes/admin/core/class-admin-dragdrop.php:181
msgid "Add Row"
msgstr ""

#: includes/admin/core/class-admin-builder.php:426
#: includes/admin/core/class-admin-builder.php:480
#: includes/admin/core/class-admin-dragdrop.php:182
msgid "Edit Row"
msgstr ""

#: includes/admin/core/class-admin-builder.php:483
#: includes/admin/core/class-admin-builder.php:521
#: includes/admin/core/class-admin-dragdrop.php:184
#: includes/admin/core/class-admin-dragdrop.php:200
#: includes/admin/core/class-admin-dragdrop.php:225
msgid "Delete Row"
msgstr ""

#: includes/admin/core/class-admin-builder.php:540
msgid "Invalid field type"
msgstr ""

#: includes/admin/core/class-admin-builder.php:552
msgid "(no title)"
msgstr ""

#: includes/admin/core/class-admin-builder.php:556
msgid "This is not permitted for security reasons. For your safety, please remove it."
msgstr ""

#: includes/admin/core/class-admin-builder.php:559
#: includes/admin/core/list-tables/roles-list-table.php:387
msgid "Edit"
msgstr ""

#: includes/admin/core/class-admin-builder.php:560
#: includes/admin/core/class-admin-columns.php:71
msgid "Duplicate"
msgstr ""

#: includes/admin/core/class-admin-builder.php:562
msgid "Delete Group"
msgstr ""

#: includes/admin/core/class-admin-builder.php:736
msgid "Wrong dynamic-content attribute."
msgstr ""

#: includes/admin/core/class-admin-builder.php:827
#: includes/admin/core/class-admin-builder.php:828
msgid "Search Icons..."
msgstr ""

#: includes/admin/core/class-admin-builder.php:846
msgid "Setup New Field"
msgstr ""

#: includes/admin/core/class-admin-builder.php:861
msgid "Predefined Fields"
msgstr ""

#: includes/admin/core/class-admin-builder.php:878
#: includes/core/class-builtin.php:1606
msgid "None"
msgstr ""

#: includes/admin/core/class-admin-builder.php:883
msgid "Custom Fields"
msgstr ""

#. translators: %s is a field metakey.
#: includes/admin/core/class-admin-builder.php:893
#, php-format
msgid "Meta Key - %s"
msgstr ""

#: includes/admin/core/class-admin-builder.php:904
msgid "You did not create any custom fields."
msgstr ""

#: includes/admin/core/class-admin-builder.php:918
msgid "This field is not setup correctly for this form."
msgstr ""

#: includes/admin/core/class-admin-builder.php:938
#: includes/admin/core/class-admin-builder.php:1007
msgid "This field type is not setup correctly."
msgstr ""

#: includes/admin/core/class-admin-builder.php:1175
#: includes/core/class-builtin.php:211
#: includes/core/class-form.php:146
#: includes/core/class-form.php:154
#: includes/core/class-form.php:161
#: includes/core/class-form.php:243
#: includes/core/class-form.php:633
#: includes/core/class-password.php:497
#: includes/core/um-actions-profile.php:551
msgid "This is not possible for security reasons."
msgstr ""

#: includes/admin/core/class-admin-builder.php:1192
msgid "This is not possible for security reasons. Don't use internal PHP functions."
msgstr ""

#: includes/admin/core/class-admin-columns.php:106
#: includes/admin/core/class-admin-columns.php:126
#: includes/admin/core/class-admin-metabox.php:2284
#: includes/admin/templates/role/role-edit.php:246
#: includes/widgets/class-um-search-widget.php:94
msgid "Title"
msgstr ""

#: includes/admin/core/class-admin-columns.php:107
#: includes/admin/core/class-admin-columns.php:127
msgid "ID"
msgstr ""

#: includes/admin/core/class-admin-columns.php:111
#: includes/admin/core/class-admin-columns.php:130
msgid "Date"
msgstr ""

#: includes/admin/core/class-admin-dragdrop.php:45
#: includes/admin/core/class-admin-dragdrop.php:50
msgid "Invalid form ID."
msgstr ""

#: includes/admin/core/class-admin-forms.php:348
#: includes/core/class-fields.php:748
msgid "Required"
msgstr ""

#: includes/admin/core/class-admin-forms.php:563
#: includes/admin/core/class-admin-forms.php:572
#: includes/admin/core/class-admin-metabox.php:1600
#: includes/admin/core/class-admin-metabox.php:1601
#: includes/admin/core/class-admin-metabox.php:1610
#: includes/admin/core/class-admin-metabox.php:1611
msgid "Select Icon"
msgstr ""

#: includes/admin/core/class-admin-forms.php:575
#: includes/admin/core/class-admin-metabox.php:1614
msgid "The selected icon is using an outdated version. Please select the icon above to use latest version."
msgstr ""

#: includes/admin/core/class-admin-forms.php:654
msgid "Select Users"
msgstr ""

#: includes/admin/core/class-admin-forms.php:1034
#: includes/admin/core/class-admin-upgrade.php:274
msgid "Run"
msgstr ""

#: includes/admin/core/class-admin-forms.php:1169
msgid "Create Default"
msgstr ""

#: includes/admin/core/class-admin-forms.php:1246
#: includes/admin/core/class-admin-forms.php:1265
#: includes/admin/core/class-admin-forms.php:1403
#: includes/admin/core/class-admin-forms.php:1412
#: includes/admin/core/class-admin-forms.php:1659
#: includes/admin/core/class-admin-forms.php:1673
#: includes/admin/core/class-admin-forms.php:1789
#: includes/admin/core/class-admin-forms.php:1818
#: assets/js/admin/forms.js:353
#: assets/js/admin/forms.js:427
#: assets/js/admin/forms.js:716
#: assets/js/admin/forms.js:749
msgid "Remove"
msgstr ""

#: includes/admin/core/class-admin-forms.php:1459
msgid "Select media"
msgstr ""

#: includes/admin/core/class-admin-forms.php:1489
#: assets/js/admin/forms.js:797
msgid "Select"
msgstr ""

#: includes/admin/core/class-admin-forms.php:1490
msgid "Clear"
msgstr ""

#: includes/admin/core/class-admin-forms.php:1795
#: includes/admin/templates/directory/sorting.php:43
#: assets/js/admin/forms.js:360
msgid "ASC"
msgstr ""

#: includes/admin/core/class-admin-forms.php:1796
#: includes/admin/templates/directory/sorting.php:44
#: assets/js/admin/forms.js:361
msgid "DESC"
msgstr ""

#: includes/admin/core/class-admin-forms.php:1798
#: includes/admin/core/class-admin-forms.php:1820
#: includes/admin/core/class-admin-metabox.php:2376
#: assets/js/admin/forms.js:363
msgid "Label"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:42
msgid "Privacy Policy"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:57
msgid "Account Status"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:58
msgid "Submitted data on Registration"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:59
msgid "Registration Form ID"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:60
msgid "Registration Timestamp"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:61
msgid "Registration Request"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:62
msgid "Registration Nonce"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:63
msgid "Registration HTTP referer"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:64
msgid "Community Role"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:65
msgid "Profile Slug \"Username\""
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:66
msgid "Profile Slug \"First and Last Name with '.'\""
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:67
msgid "Profile Slug \"First and Last Name with '-'\""
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:68
msgid "Profile Slug \"First and Last Name with '+'\""
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:69
msgid "Profile Slug \"User ID\""
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:70
msgid "Last Login date"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:73
msgid "Private Content Post ID"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:76
msgid "Verified Account"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:79
msgid "Terms&Conditions Agreement"
msgstr ""

#: includes/admin/core/class-admin-gdpr.php:82
msgid "Privacy Policy Agreement"
msgstr ""

#. translators: %s: metadata name.
#: includes/admin/core/class-admin-gdpr.php:303
#, php-format
msgid "Your %s was unable to be removed at this time."
msgstr ""

#: includes/admin/core/class-admin-menu.php:100
msgid "Thanks :)"
msgstr ""

#. translators: %s: Review link.
#: includes/admin/core/class-admin-menu.php:108
#, php-format
msgid "If you like Ultimate Member please consider leaving a %s review. It will help us to grow the plugin and make it more popular. Thank you."
msgstr ""

#: includes/admin/core/class-admin-menu.php:187
msgid "Dashboard"
msgstr ""

#: includes/admin/core/class-admin-menu.php:197
#: includes/admin/templates/gdpr.php:7
#: includes/common/class-cpt.php:32
msgid "Forms"
msgstr ""

#: includes/admin/core/class-admin-menu.php:199
#: includes/admin/core/list-tables/roles-list-table.php:547
msgid "User Roles"
msgstr ""

#: includes/admin/core/class-admin-menu.php:202
#: includes/common/class-cpt.php:64
msgid "Member Directories"
msgstr ""

#: includes/admin/core/class-admin-menu.php:244
#: includes/admin/core/class-admin-settings.php:2081
msgid "Extensions"
msgstr ""

#: includes/admin/core/class-admin-menu.php:257
msgid "Users Overview"
msgstr ""

#: includes/admin/core/class-admin-menu.php:259
msgid "Purge Temp Files"
msgstr ""

#: includes/admin/core/class-admin-menu.php:261
msgid "User Cache"
msgstr ""

#: includes/admin/core/class-admin-menu.php:266
msgid "Upgrade's Manual Request"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:113
msgid "e.g. Member Directory"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:115
msgid "e.g. New Registration Form"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:287
#: assets/js/admin/block-restrictions.js:104
msgid "Ultimate Member: Content Restriction"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:441
#: includes/admin/core/class-admin-metabox.php:592
msgid "Restrict access to this term and its posts?"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:442
#: includes/admin/core/class-admin-metabox.php:593
msgid "Activate content restriction for this term and its posts. Affects only posts that do not have individual Restriction Content settings"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:448
#: includes/admin/core/class-admin-metabox.php:599
msgid "Who can access this term and its posts?"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:451
#: includes/admin/core/class-admin-metabox.php:602
#: includes/admin/core/class-admin-metabox.php:2405
#: includes/admin/core/class-admin-navmenu.php:75
#: includes/admin/core/class-admin-navmenu.php:129
#: includes/admin/core/class-admin-navmenu.php:206
#: includes/admin/templates/access/restrict_content.php:65
#: includes/core/class-builtin.php:784
#: assets/js/admin/block-restrictions.js:136
msgid "Everyone"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:452
#: includes/admin/core/class-admin-metabox.php:603
#: includes/admin/templates/access/restrict_content.php:66
#: assets/js/admin/block-restrictions.js:144
msgid "Logged out users"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:453
#: includes/admin/core/class-admin-metabox.php:604
#: includes/admin/templates/access/restrict_content.php:67
#: assets/js/admin/block-restrictions.js:140
msgid "Logged in users"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:460
#: includes/admin/core/class-admin-metabox.php:611
msgid "Select which roles can access this term and its posts"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:461
#: includes/admin/core/class-admin-metabox.php:612
msgid "Leave empty if you want to display a term for all logged in users"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:469
#: includes/admin/core/class-admin-metabox.php:621
#: includes/admin/templates/access/restrict_content.php:84
msgid "Hide from queries"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:469
#: includes/admin/core/class-admin-metabox.php:621
msgid "Would you like to display a 404 page for users who do not have access to this term on the term's archive page and terms' posts single pages?"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:470
#: includes/admin/core/class-admin-metabox.php:622
#: includes/admin/templates/access/restrict_content.php:85
msgid "Exclude only from WP queries results"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:470
#: includes/admin/core/class-admin-metabox.php:622
msgid "Recommended to be enabled. Restricted term's archive page and all terms' posts will be hidden by exclusion from WP Query. The safest and most effective method that hides post and its comments from all requests, RSS feeds, etc. on your site"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:477
#: includes/admin/core/class-admin-metabox.php:629
msgid "What happens when users without access try to view the term's post?"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:478
#: includes/admin/core/class-admin-metabox.php:630
msgid "Action when users without access tries to view the term's post"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:481
#: includes/admin/core/class-admin-metabox.php:633
#: includes/admin/templates/access/restrict_content.php:96
msgid "Show access restricted message"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:482
#: includes/admin/core/class-admin-metabox.php:634
#: includes/admin/templates/access/restrict_content.php:97
msgid "Redirect user"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:489
#: includes/admin/core/class-admin-metabox.php:641
#: includes/admin/templates/access/restrict_content.php:104
msgid "Restricted access message type"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:490
#: includes/admin/core/class-admin-metabox.php:642
msgid "Would you like to use the global default message or apply a custom message to this term's post?"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:493
#: includes/admin/core/class-admin-metabox.php:645
#: includes/admin/templates/access/restrict_content.php:108
msgid "Global default message"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:494
#: includes/admin/core/class-admin-metabox.php:646
#: includes/admin/templates/access/restrict_content.php:109
msgid "Custom message"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:501
#: includes/admin/core/class-admin-metabox.php:653
#: includes/admin/templates/access/restrict_content.php:116
#: assets/js/admin/block-restrictions.js:215
msgid "Custom restricted access message"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:502
#: includes/admin/core/class-admin-metabox.php:654
#: includes/admin/templates/access/restrict_content.php:117
msgid "You may replace global restricted access message here"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:509
#: includes/admin/core/class-admin-metabox.php:661
#: includes/admin/templates/access/restrict_content.php:124
msgid "Where should users be redirected to?"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:510
#: includes/admin/core/class-admin-metabox.php:662
msgid "Select redirect to page when user hasn't access to the term's post"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:514
#: includes/admin/core/class-admin-metabox.php:665
#: includes/admin/templates/access/restrict_content.php:128
msgid "Login page"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:515
#: includes/admin/core/class-admin-metabox.php:666
#: includes/admin/templates/access/restrict_content.php:129
msgid "Custom URL"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:521
#: includes/admin/core/class-admin-metabox.php:673
#: includes/admin/templates/access/restrict_content.php:136
msgid "Redirect URL"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:522
#: includes/admin/core/class-admin-metabox.php:674
#: includes/admin/templates/access/restrict_content.php:137
msgid "Set full URL where do you want to redirect the user"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:858
#: includes/admin/core/class-admin-metabox.php:948
#: includes/admin/core/class-admin-metabox.php:1022
msgid "Publish"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:859
msgid "General Options"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:860
msgid "Sorting"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:861
msgid "Profile Card"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:862
msgid "Search Options"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:863
msgid "Results &amp; Pagination"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:865
msgid "Styling: General"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:878
msgid "Administrative Permissions"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:886
msgid "General Permissions"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:894
msgid "Profile Access"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:905
msgid "Homepage Options"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:916
msgid "Registration Options"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:924
msgid "Login Options"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:932
msgid "Logout Options"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:940
msgid "Delete Options"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1027
msgid "Select Form Type"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1028
#: includes/admin/core/class-admin-settings.php:2163
msgid "Form Builder"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1033
#: includes/admin/core/class-admin-metabox.php:1054
#: includes/admin/core/class-admin-metabox.php:1076
msgid "Customize this form"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1055
msgid "User Meta"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1077
msgid "Options"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1112
msgid "Delete Permanently"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1114
msgid "Move to Trash"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1128
#: includes/admin/core/class-admin-metabox.php:1129
msgid "Create"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1132
#: includes/admin/core/class-admin-metabox.php:1133
#: includes/admin/templates/modal/forms/dynamic_edit_field.php:15
#: includes/admin/templates/modal/forms/dynamic_edit_row.php:15
msgid "Update"
msgstr ""

#. translators: %s: Directory id.
#: includes/admin/core/class-admin-metabox.php:1179
#, php-format
msgid "Directory #%s"
msgstr ""

#. translators: %s: Form id.
#: includes/admin/core/class-admin-metabox.php:1289
#, php-format
msgid "Form #%s"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1421
msgid "Visibility"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1421
msgid "Select where this field should appear. This option should only be changed on the profile form and allows you to show a field in one mode only (edit or view) or in both modes."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1423
msgid "View everywhere"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1424
msgid "Edit mode only"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1425
msgid "View mode only"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1452
msgid "If"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1517
msgid "Value"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1529
msgid "Validate"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1529
msgid "Does this field require a special validation"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1530
msgid "Select a validation type..."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1574
msgid "Custom Action"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1574
msgid "If you want to apply your custom validation, you can use action hooks to add custom validation. Please refer to documentation for further details."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1599
#: includes/admin/core/class-admin-metabox.php:1609
msgid "Icon"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1599
#: includes/admin/core/class-admin-metabox.php:1609
msgid "Select an icon to appear in the field. Leave blank if you do not want an icon to show in the field."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1627
msgid "CSS Class"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1627
msgid "Specify a custom CSS class to be applied to this element"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1637
msgid "Thickness (in pixels)"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1637
msgid "This is the width in pixels, e.g. 4 or 2, etc"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1647
msgid "Optional Text"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1647
msgid "Optional text to include with the divider"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1657
msgid "Padding"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1657
msgid "Set padding for this section"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1667
msgid "Margin"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1667
msgid "Set margin for this section"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1677
msgid "Border"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1677
msgid "Set border for this section"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1687
#: includes/admin/core/class-admin-metabox.php:1858
msgid "Style"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1687
msgid "Choose the border style"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1689
#: includes/admin/core/class-admin-metabox.php:1860
msgid "Solid"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1690
#: includes/admin/core/class-admin-metabox.php:1861
msgid "Dotted"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1691
#: includes/admin/core/class-admin-metabox.php:1862
msgid "Dashed"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1692
#: includes/admin/core/class-admin-metabox.php:1863
msgid "Double"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1702
msgid "Border Radius"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1702
msgid "Rounded corners can be applied by setting a pixels value here. e.g. 5px"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1712
msgid "Border Color"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1712
msgid "Give a color to this border"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1722
msgid "Enable Row Heading"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1722
msgid "Whether to enable a heading for this row"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1732
msgid "Heading Text"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1732
msgid "Enter the row heading text here"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1742
msgid "Background Color"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1742
msgid "This will be the background of entire section"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1752
msgid "Heading Background Color"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1752
msgid "This will be the background of the heading section"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1762
msgid "Heading Text Color"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1762
msgid "This will be the text color of heading part only"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1772
msgid "Text Color"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1772
msgid "This will be the text color of entire section"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1782
msgid "Icon Color"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1782
msgid "This will be the color of selected icon. By default It will be the same color as heading text color"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1792
msgid "Color"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1792
msgid "Select a color for this divider"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1802
msgid "URL Alt Text"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1802
msgid "Entering custom text here will replace the url with a text link"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1812
msgid "Link Target"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1812
msgid "Choose whether to open this link in same window or in a new window"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1814
msgid "Open in new window"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1815
msgid "Same window"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1825
msgid "SEO Follow"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1825
msgid "Whether to follow or nofollow this link by search engines"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1827
msgid "Follow"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1828
msgid "No-Follow"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1838
msgid "Force strong password?"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1838
msgid "Turn on to force users to create a strong password (A combination of one lowercase letter, one uppercase letter, and one number). If turned on this option is only applied to register forms and not to login forms."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1848
msgid "Automatically add a confirm password field?"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1848
msgid "Turn on to add a confirm password field. If turned on the confirm password field will only show on register forms and not on login forms."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1858
msgid "This is the line-style of divider"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1874
msgid "Time Intervals (in minutes)"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1874
msgid "Choose the minutes interval between each time in the time picker."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1875
msgid "e.g. 30, 60, 120"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1887
msgid "Date User-Friendly Format"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1887
msgid "The display format of the date which is visible to user."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1898
msgid "Time Format"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1898
msgid "Choose the displayed time-format for this field"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1900
#: includes/admin/core/class-admin-metabox.php:1901
msgid "( 12-hr format )"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1902
msgid "( 24-hr format )"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1913
msgid "Use custom Date format"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1913
msgid "This option overrides \"Date User-Friendly Format\" option. See https://www.php.net/manual/en/function.date.php"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1923
msgid "Displayed Date Format"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1923
msgid "Whether you wish to show the date in full or only show the years e.g. 25 Years"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1925
msgid "Show full date"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1926
msgid "Show years only"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1942
msgid "Disable specific weekdays"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1942
msgid "Disable specific week days from being available for selection in this date picker"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1944
msgid "Sunday"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1945
msgid "Monday"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1946
msgid "Tuesday"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1947
msgid "Wednesday"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1948
msgid "Thursday"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1949
msgid "Friday"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1950
msgid "Saturday"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1960
msgid "Number of Years to pick from"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1960
msgid "Number of years available for the date selection. Default to last 50 years"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1970
msgid "Years Selection"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1970
msgid "This decides which years should be shown relative to today date"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1972
msgid "Equal years before / after today"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1973
msgid "Past years only"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1974
msgid "Future years only"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1984
msgid "Date Range Start"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1984
msgid "Set the minimum date/day in range in the format YYYY/MM/DD"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1985
#: includes/admin/core/class-admin-metabox.php:1995
msgid "YYYY/MM/DD"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1994
msgid "Date Range End"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:1994
msgid "Set the maximum date/day in range in the format YYYY/MM/DD"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2004
msgid "Set Date Range"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2004
msgid "Whether to show a specific number of years or specify a date range to be available for the date picker."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2006
msgid "Fixed Number of Years"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2007
msgid "Specific Date Range"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2020
msgid "Enter Shortcode"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2020
msgid "Enter the shortcode in the following textarea and it will be displayed on the fields"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2021
msgid "e.g. [my_custom_shortcode]"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2030
msgid "Content Editor"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2030
msgid "Edit the content of this field here"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2043
msgid "Crop Feature"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2043
msgid "Enable/disable crop feature for this image upload and define ratio"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2045
msgid "Turn Off (Default)"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2046
msgid "Crop and force 1:1 ratio"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2047
msgid "Crop and force user-defined ratio"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2064
msgid "Allowed Image Types"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2064
#: includes/admin/core/class-admin-metabox.php:2082
msgid "Select the image types that you want to allow to be uploaded via this field."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2082
msgid "Allowed File Types"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2099
msgid "Drag &amp; Drop Photo"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2102
msgid "Drag &amp; Drop File"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2107
#: includes/admin/core/class-admin-metabox.php:2127
msgid "Upload Box Text"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2107
msgid "This is the headline that appears in the upload box for this field"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2117
msgid "Additional Instructions Text"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2117
msgid "If you need to add information or secondary line below the headline of upload box, enter it here"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2127
msgid "The text that appears on the button. e.g. Upload"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2128
#: includes/core/class-fields.php:1993
#: includes/core/class-fields.php:2034
#: includes/core/class-fields.php:2995
#: includes/core/class-fields.php:3098
msgid "Upload"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2137
msgid "Maximum Size in bytes"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2137
msgid "The maximum size for image that can be uploaded through this field. Leave empty for unlimited size."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2147
msgid "Textarea Height"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2147
msgid "The height of textarea in pixels. Default is 100 pixels"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2157
msgid "Spacing"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2157
msgid "This is the required spacing in pixels. e.g. 20px"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2167
msgid "Allow multiple selections"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2167
msgid "Enable/disable multiple selections for this field"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2177
msgid "Maximum number of selections"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2177
msgid "Enter a number here to force a maximum number of selections by user for this field"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2187
msgid "Minimum number of selections"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2187
msgid "Enter a number here to force a minimum number of selections by user for this field"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2197
msgid "Maximum number of entries"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2197
msgid "This is the max number of entries the user can add via field group."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2207
msgid "Maximum allowed words"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2207
msgid "If you want to enable a maximum number of words to be input in this textarea. Leave empty to disable this setting"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2217
msgid "Minimum Number"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2217
msgid "Minimum number that can be entered in this field"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2227
msgid "Maximum Number"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2227
msgid "Maximum number that can be entered in this field"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2237
msgid "Minimum length"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2237
msgid "If you want to enable a minimum number of characters to be input in this field. Leave empty to disable this setting"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2247
msgid "Maximum length"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2247
msgid "If you want to enable a maximum number of characters to be input in this field. Leave empty to disable this setting"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2257
msgid "Does this textarea accept HTML?"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2257
msgid "Turn on/off HTML tags for this textarea"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2274
msgid "Edit Choices"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2274
msgid "Enter one choice per line. This will represent the available choices or selections available for user."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2284
msgid "This is the title of the field for your reference in the backend. The title will not appear on the front-end of your website."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2295
msgid "Unique ID"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2309
#: includes/admin/core/class-admin-metabox.php:2315
msgid "Meta Key"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2309
msgid "The meta key cannot be changed for duplicated fields or when editing an existing field. If you require a different meta key please create a new field."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2315
msgid "A meta key is required to store the entered info in this field in the database. The meta key should be unique to this field and be written in lowercase with an underscore ( _ ) separating words e.g country_list or job_title"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2328
msgid "Help Text"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2328
msgid "This is the text that appears in a tooltip when a user hovers over the info icon. Help text is useful for providing users with more information about what they should enter in the field. Leave blank if no help text is needed for field."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2340
msgid "Default Text"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2340
msgid "Text to display by default in this field"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2346
msgid "Default Date"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2346
#: includes/admin/core/class-admin-metabox.php:2352
msgid "You may use all PHP compatible date formats such as: 2020-02-02, 02/02/2020, yesterday, today, tomorrow, next monday, first day of next month, +3 day"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2352
msgid "Default Time"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2358
msgid "Default Rating"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2358
msgid "If you wish the rating field to be prefilled with a number of stars, enter it here."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2364
msgid "Default Value"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2364
msgid "This option allows you to pre-fill the field with a default value prior to the user entering a value in the field. Leave blank to have no default value"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2376
msgid "The field label is the text that appears above the field on your front-end form. Leave blank to not show a label above field."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2386
msgid "Confirm password field label"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2386
msgid "This label is the text that appears above the confirm password field. Leave blank to show default label."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2396
msgid "Placeholder"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2396
msgid "This is the text that appears within the field e.g please enter your email address. Leave blank to not show any placeholder text."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2406
#: includes/class-config.php:133
#: includes/class-config.php:941
#: includes/class-config.php:1004
msgid "Members"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2407
msgid "Only visible to profile owner and users who can edit other member accounts"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2408
msgid "Only visible to profile owner and specific roles"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2409
msgid "Only specific member roles"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2415
msgid "Field privacy allows you to select who can view this field on the front-end. The site admin can view all fields regardless of the option set here."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2438
msgid "Select member roles"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2438
msgid "Select the member roles that can view this field on the front-end."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2464
msgid "Is this field required?"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2464
msgid "This option allows you to set whether the field must be filled in before the form can be processed."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2490
msgid "Can user edit this field?"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2490
msgid "This option allows you to set whether or not the user can edit the information in this field. The site admin can edit all fields regardless of the option set here."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2502
msgid "Rating System"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2502
msgid "Choose whether you want a 5-stars or 10-stars ratings based here."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2504
msgid "5  stars rating system"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2505
msgid "10 stars rating system"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2515
msgid "Choices Callback"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2515
msgid "Add a callback source to retrieve choices."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2525
msgid "Parent Option"
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2525
msgid "Dynamically populates the option based from selected parent option."
msgstr ""

#: includes/admin/core/class-admin-metabox.php:2527
msgid "No Selected"
msgstr ""

#: includes/admin/core/class-admin-navmenu.php:38
msgid "Display Mode"
msgstr ""

#: includes/admin/core/class-admin-navmenu.php:39
msgid "By Role"
msgstr ""

#: includes/admin/core/class-admin-navmenu.php:69
#: includes/admin/core/class-admin-navmenu.php:122
#: includes/admin/core/class-admin-navmenu.php:198
msgid "Ultimate Member Menu Settings"
msgstr ""

#: includes/admin/core/class-admin-navmenu.php:73
#: includes/admin/core/class-admin-navmenu.php:126
#: includes/admin/core/class-admin-navmenu.php:202
msgid "Who can see this menu link?"
msgstr ""

#: includes/admin/core/class-admin-navmenu.php:76
#: includes/admin/core/class-admin-navmenu.php:130
#: includes/admin/core/class-admin-navmenu.php:209
msgid "Logged Out Users"
msgstr ""

#: includes/admin/core/class-admin-navmenu.php:77
#: includes/admin/core/class-admin-navmenu.php:131
#: includes/admin/core/class-admin-navmenu.php:212
msgid "Logged In Users"
msgstr ""

#: includes/admin/core/class-admin-navmenu.php:82
#: includes/admin/core/class-admin-navmenu.php:218
msgid "Select the member roles that can see this link"
msgstr ""

#. translators: %s: Setting link.
#: includes/admin/core/class-admin-notices.php:246
#, php-format
msgid "The <strong>\"Membership - Anyone can register\"</strong> option on the general settings <a href=\"%s\">page</a> is enabled. This means users can register via the standard WordPress wp-login.php page. If you do not want users to be able to register via this page and only register via the Ultimate Member registration form, you should deactivate this option. You can dismiss this notice if you wish to keep the wp-login.php registration page open."
msgstr ""

#. translators: %1$d: Background update for users is complete; %2$d: Total users for background update.
#: includes/admin/core/class-admin-notices.php:285
#, php-format
msgid "Background process is running: Setting user statuses %1$d/%2$d."
msgstr ""

#: includes/admin/core/class-admin-notices.php:301
msgid "<strong>All Access Pass</strong> – Get access to all Ultimate Member extensions at a significant discount with our All Access Pass."
msgstr ""

#: includes/admin/core/class-admin-notices.php:305
msgid "View Pricing"
msgstr ""

#. translators: %1$s is a plugin name; %2$s is a plugin version; %3$s is a plugin name; %4$s is a doc link.
#: includes/admin/core/class-admin-notices.php:402
#, php-format
msgid "<strong>%1$s %2$s</strong> requires 2.0 extensions. You have pre 2.0 extensions installed on your site. <br /> Please update %3$s extensions to latest versions. For more info see this <a href=\"%4$s\" target=\"_blank\">doc</a>."
msgstr ""

#. translators: %s: Plugin name.
#: includes/admin/core/class-admin-notices.php:433
#, php-format
msgid "%s needs to create several pages (User Profiles, Account, Registration, Login, Password Reset, Logout, Member Directory) to function correctly."
msgstr ""

#: includes/admin/core/class-admin-notices.php:438
msgid "Create Pages"
msgstr ""

#: includes/admin/core/class-admin-notices.php:440
msgid "No thanks"
msgstr ""

#: includes/admin/core/class-admin-notices.php:467
msgid "Ultimate Member Setup Error: User page can not be a child page."
msgstr ""

#: includes/admin/core/class-admin-notices.php:481
msgid "Ultimate Member Setup Error: Account page can not be a child page."
msgstr ""

#. translators: %s: query args.
#: includes/admin/core/class-admin-notices.php:500
msgid "Exif is not enabled on your server. Mobile photo uploads will not be rotated correctly until you enable the exif extension."
msgstr ""

#: includes/admin/core/class-admin-notices.php:518
msgid "Your temp uploads directory is now clean."
msgstr ""

#: includes/admin/core/class-admin-notices.php:521
msgid "Your user cache is now removed."
msgstr ""

#: includes/admin/core/class-admin-notices.php:524
msgid "Your user statuses cache is now removed."
msgstr ""

#: includes/admin/core/class-admin-notices.php:527
msgid "You have the latest updates."
msgstr ""

#: includes/admin/core/class-admin-notices.php:530
msgid "Try again later. You can run this action once daily."
msgstr ""

#: includes/admin/core/class-admin-notices.php:533
msgid "The form has been duplicated successfully."
msgstr ""

#: includes/admin/core/class-admin-notices.php:536
msgid "Settings have been saved successfully."
msgstr ""

#: includes/admin/core/class-admin-notices.php:539
msgid "Users have been updated."
msgstr ""

#: includes/admin/core/class-admin-notices.php:542
msgid "All users sessions have been successfully destroyed."
msgstr ""

#: includes/admin/core/class-admin-notices.php:545
msgid "Account has been successfully restored."
msgstr ""

#: includes/admin/core/class-admin-notices.php:550
#, php-format
msgid "<strong>%s</strong> user has been approved."
msgid_plural "<strong>%s</strong> users have been approved."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/core/class-admin-notices.php:555
#, php-format
msgid "<strong>%s</strong> user has been reactivated."
msgid_plural "<strong>%s</strong> users have been reactivated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/core/class-admin-notices.php:560
#, php-format
msgid "<strong>%s</strong> user has been rejected."
msgid_plural "<strong>%s</strong> users have been rejected."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/core/class-admin-notices.php:565
#, php-format
msgid "<strong>%s</strong> user has been deactivated."
msgid_plural "<strong>%s</strong> users have been deactivated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/core/class-admin-notices.php:570
#, php-format
msgid "<strong>%s</strong> user has been set as pending admin review."
msgid_plural "<strong>%s</strong> users have been set as pending admin review."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/core/class-admin-notices.php:575
#, php-format
msgid "Activation email for <strong>%s</strong> user has been sent."
msgid_plural "Activation emails for <strong>%s</strong> users have been sent."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: Plugin name.
#: includes/admin/core/class-admin-notices.php:644
#, php-format
msgid "You have installed <strong>%s</strong> with wrong folder name. Correct folder name is <strong>\"ultimate-member\"</strong>."
msgstr ""

#. translators: %1$s is an inactive license number; %2$s is a plugin name.
#: includes/admin/core/class-admin-notices.php:679
#, php-format
msgid "There are %1$s inactive %2$s license keys for this site. This site is not authorized to get plugin updates. You can activate this site on <a href=\"https://ultimatemember.com/\">www.ultimatemember.com</a>."
msgstr ""

#. translators: %1$s is an invalid license; %2$s is a plugin name; %3$s is a license link.
#: includes/admin/core/class-admin-notices.php:699
#, php-format
msgid "You have %1$s invalid or expired license keys for %2$s. Please go to the <a href=\"%3$s\">Licenses page</a> to correct this issue."
msgstr ""

#: includes/admin/core/class-admin-notices.php:716
msgid "version needs to be updated to work correctly."
msgstr ""

#: includes/admin/core/class-admin-notices.php:718
msgid "It is necessary to update the structure of the database and options that are associated with current version"
msgstr ""

#: includes/admin/core/class-admin-notices.php:720
msgid "Please run the upgrade process on this "
msgstr ""

#: includes/admin/core/class-admin-notices.php:720
msgid "page"
msgstr ""

#: includes/admin/core/class-admin-notices.php:724
msgid "Visit Upgrade Page"
msgstr ""

#: includes/admin/core/class-admin-notices.php:745
msgid "Settings successfully upgraded"
msgstr ""

#. translators: %1$s is a plugin name title; %2$s is a plugin version.
#: includes/admin/core/class-admin-notices.php:755
#, php-format
msgid "<strong>%1$s %2$s</strong> Successfully Upgraded"
msgstr ""

#. translators: %s: plugin name.
#: includes/admin/core/class-admin-notices.php:785
#, php-format
msgid "Hey there! It's been one month since you installed %s. How have you found the plugin so far?"
msgstr ""

#: includes/admin/core/class-admin-notices.php:789
msgid "I love it!"
msgstr ""

#: includes/admin/core/class-admin-notices.php:790
msgid "It's good but could be better"
msgstr ""

#: includes/admin/core/class-admin-notices.php:791
msgid "I don't like the plugin"
msgstr ""

#: includes/admin/core/class-admin-notices.php:800
msgid "Leave Review"
msgstr ""

#: includes/admin/core/class-admin-notices.php:809
#: includes/admin/core/class-admin-notices.php:818
msgid "Provide Feedback"
msgstr ""

#. translators: %1$s is a plugin name; %2$s is a #.
#: includes/admin/core/class-admin-notices.php:848
#, php-format
msgid "<strong>%1$s</strong> future plans! Detailed future list is <a href=\"%2$s\" target=\"_blank\">here</a>"
msgstr ""

#. translators: %s child-theme article link.
#: includes/admin/core/class-admin-notices.php:880
#, php-format
msgid "We recommend using a <a href=\"%s\">child-theme</a> for Ultimate Member customization. Unlike official theme repositories, child themes don't have dependencies that could lead to your custom files being overwritten during a theme upgrade.<br />Without a child theme, your customization files may be deleted after every theme update."
msgstr ""

#: includes/admin/core/class-admin-notices.php:903
msgid "Your Register forms are now locked. You can unlock them in Ultimate Member > Settings > Advanced > Security > Lock All Register Forms."
msgstr ""

#: includes/admin/core/class-admin-notices.php:922
msgid "Mandatory password changes has been enabled. You can disable them in Ultimate Member > Settings > Advanced > Security > Display Login form notice to reset passwords."
msgstr ""

#: includes/admin/core/class-admin-notices.php:941
msgid "Ban for administrative capabilities is enabled. You can disable them in Ultimate Member > Settings > Advanced > Security > Enable ban for administrative capabilities."
msgstr ""

#: includes/admin/core/class-admin-notices.php:974
msgid "The role selected in WordPress native \"Settings > New User Default Role\" setting has Administrative capabilities."
msgstr ""

#: includes/admin/core/class-admin-notices.php:1000
msgid "The role selected in \"Ultimate Member > Settings > Appearance > Registration Form > Registration Default Role\" setting has Administrative capabilities."
msgstr ""

#. translators: %s are link(s) to the forms.
#: includes/admin/core/class-admin-notices.php:1068
#, php-format
msgid "Register forms have Administrative roles, we recommend that you assign a non-admin roles to secure the forms. %s"
msgstr ""

#: includes/admin/core/class-admin-notices.php:1087
msgid "Wrong Data"
msgstr ""

#: includes/admin/core/class-admin-settings.php:80
msgid "Wrong callback"
msgstr ""

#: includes/admin/core/class-admin-settings.php:189
#: includes/admin/core/packages/2.1.3-beta3/functions.php:24
#: includes/admin/core/packages/2.3.0/functions.php:89
#: includes/admin/core/packages/2.8.0/functions.php:32
msgid "Wrong data"
msgstr ""

#. translators: %1$s is a metadata from name; %2$s is a metadata to.
#: includes/admin/core/class-admin-settings.php:234
#, php-format
msgid "Metadata from %1$s to %2$s was upgraded successfully..."
msgstr ""

#: includes/admin/core/class-admin-settings.php:268
#, php-format
msgid "%1$s (ID: %2$s)"
msgstr ""

#: includes/admin/core/class-admin-settings.php:288
#: includes/admin/core/class-admin-settings.php:321
msgid "<strong>Warning:</strong> Account page and User page must be separate pages."
msgstr ""

#: includes/admin/core/class-admin-settings.php:290
msgid "<strong>Warning:</strong> Account page must contain shortcode <code>[ultimatemember_account]</code>."
msgstr ""

#: includes/admin/core/class-admin-settings.php:292
msgid "<strong>Warning:</strong> Account page and WooCommerce \"My account\" page should be separate pages."
msgstr ""

#: includes/admin/core/class-admin-settings.php:297
#: includes/admin/core/class-admin-settings.php:306
msgid "<strong>Warning:</strong> Login page and Logout page must be separate pages."
msgstr ""

#: includes/admin/core/class-admin-settings.php:299
msgid "<strong>Warning:</strong> Login page must contain a login form shortcode. You can get existing shortcode or create a new one <a href=\"edit.php?post_type=um_form\" target=\"_blank\">here</a>."
msgstr ""

#: includes/admin/core/class-admin-settings.php:304
msgid "<strong>Warning:</strong> Home page and Logout page must be separate pages."
msgstr ""

#: includes/admin/core/class-admin-settings.php:311
msgid "<strong>Warning:</strong> Password Reset page must contain shortcode <code>[ultimatemember_password]</code>."
msgstr ""

#: includes/admin/core/class-admin-settings.php:316
msgid "<strong>Warning:</strong> Register page must contain a registration form shortcode. You can get existing shortcode or create a new one <a href=\"edit.php?post_type=um_form\" target=\"_blank\">here</a>."
msgstr ""

#: includes/admin/core/class-admin-settings.php:323
msgid "<strong>Warning:</strong> User page must contain a profile form shortcode. You can get existing shortcode or create a new one <a href=\"edit.php?post_type=um_form\" target=\"_blank\">here</a>."
msgstr ""

#: includes/admin/core/class-admin-settings.php:328
msgid "<strong>Warning:</strong> Members page must contain a profile form shortcode. You can get existing shortcode or create a new one <a href=\"edit.php?post_type=um_directory\" target=\"_blank\">here</a>."
msgstr ""

#. translators: %s: Page title
#: includes/admin/core/class-admin-settings.php:341
#, php-format
msgid "%s page"
msgstr ""

#: includes/admin/core/class-admin-settings.php:344
msgid "Choose a page..."
msgstr ""

#: includes/admin/core/class-admin-settings.php:358
msgid "Profile menu"
msgstr ""

#. translators: %s: Tab title
#: includes/admin/core/class-admin-settings.php:390
#: includes/admin/core/class-admin-settings.php:407
#, php-format
msgid "Enable %s Tab"
msgstr ""

#: includes/admin/core/class-admin-settings.php:416
msgid "Select which users can view this tab."
msgstr ""

#: includes/admin/core/class-admin-settings.php:425
msgid "Allowed roles"
msgstr ""

#: includes/admin/core/class-admin-settings.php:426
msgid "Select the the user roles allowed to view this tab."
msgstr ""

#: includes/admin/core/class-admin-settings.php:428
msgid "Choose user roles..."
msgstr ""

#: includes/admin/core/class-admin-settings.php:457
msgid "This will be the default tab on user profile page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:473
msgid "Menu icons in desktop view"
msgstr ""

#: includes/admin/core/class-admin-settings.php:475
msgid "\"Desktop view\" means the profile block's width lower than 800px."
msgstr ""

#: includes/admin/core/class-admin-settings.php:541
msgid "Globally control the access of your site, you can have separate restrict options per post/page by editing the desired item."
msgstr ""

#: includes/admin/core/class-admin-settings.php:552
msgid "A logged out user will be redirected to this url If he is not permitted to access the site."
msgstr ""

#: includes/admin/core/class-admin-settings.php:559
msgid "Here you can exclude URLs beside the redirect URI to be accessible to everyone."
msgstr ""

#: includes/admin/core/class-admin-settings.php:560
msgid "Add New URL"
msgstr ""

#: includes/admin/core/class-admin-settings.php:568
msgid "Accessible Homepage"
msgstr ""

#: includes/admin/core/class-admin-settings.php:575
msgid "Accessible Category pages"
msgstr ""

#: includes/admin/core/class-admin-settings.php:582
msgid "Replace the restricted Post Title"
msgstr ""

#: includes/admin/core/class-admin-settings.php:583
msgid "Allow to replace the restricted post title to users that do not have permission to view the content."
msgstr ""

#: includes/admin/core/class-admin-settings.php:589
msgid "If enabled, the text entered below will replace the title of the post/page/CPT for users who do not have permission to view the restricted content. Please see this doc for more information on this."
msgstr ""

#: includes/admin/core/class-admin-settings.php:596
msgid "This is the message shown to users that do not have permission to view the content."
msgstr ""

#: includes/admin/core/class-admin-settings.php:638
msgid "Restricted Gutenberg Blocks"
msgstr ""

#: includes/admin/core/class-admin-settings.php:639
msgid "Enable the \"Content Restriction\" settings for the Gutenberg Blocks"
msgstr ""

#: includes/admin/core/class-admin-settings.php:640
msgid "If enabled then allows to set the blocks restriction settings in wp-admin."
msgstr ""

#: includes/admin/core/class-admin-settings.php:646
msgid "This is the message shown to users that do not have permission to view the block's content."
msgstr ""

#: includes/admin/core/class-admin-settings.php:677
msgid "Check post types for which you plan to use the \"Content Restriction\" settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:687
msgid "Check taxonomies for which you plan to use the \"Content Restriction\" settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:714
msgid "Custom usermeta table"
msgstr ""

#: includes/admin/core/class-admin-settings.php:716
msgid "Check this box if you would like to enable the use of a custom table for user metadata. Improved performance for member directory searches."
msgstr ""

#: includes/admin/core/class-admin-settings.php:721
msgid "We recommend creating a backup of your site before running the update process. Do not exit the page before the update process has complete."
msgstr ""

#: includes/admin/core/class-admin-settings.php:722
msgid "After clicking the <strong>\"Run\"</strong> button, the update process will start. All information will be displayed in the field below."
msgstr ""

#: includes/admin/core/class-admin-settings.php:723
#: includes/admin/core/class-admin-upgrade.php:270
msgid "If the update was successful, you will see a corresponding message. Otherwise, contact technical support if the update failed."
msgstr ""

#. translators: %s: Link to UM Docs
#: includes/admin/core/class-admin-settings.php:1087
#, php-format
msgid "This section enables you to assign a page to one of the core elements necessary for the plugin's proper function. The plugin automatically creates and configures the required pages upon installation.<br />You only need to use this tab if you accidentally deleted pages that were automatically created during the initial plugin activation. <a href=\"%s\" target=\"_blank\">Learn more about manually creating pages</a>."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1091
#: includes/admin/core/class-admin-settings.php:1094
#: includes/admin/templates/dashboard/users.php:14
msgid "Users"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1095
msgid "General users settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1101
msgid "This will be the role assigned to users registering through Ultimate Member registration forms. By default, this setting will follow the core WordPress setting \"New User Default Role\" unless you specify a different role."
msgstr ""

#. translators: %s: Profile page URL
#: includes/admin/core/class-admin-settings.php:1112
#, php-format
msgid "Here you can control the permalink structure of the user profile URL globally e.g. %s<strong>username</strong>/."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1114
#: includes/admin/core/class-admin-settings.php:1131
msgid "Select..."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1120
msgid "Specify the custom field meta key that you want to use as profile permalink base. Meta value should be unique."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1129
msgid "This is the name that will be displayed for users on the front end of your site. Default setting uses first/last name as display name if it exists."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1137
msgid "Specify the custom field meta key or custom fields seperated by comma that you want to use to display users name on the frontend of your site."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1144
msgid "Hide author pages"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1145
msgid "Enable author page redirect to user profile"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1146
msgid "If enabled, author pages will automatically redirect to the user's profile page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1151
msgid "Members Directory"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1153
msgid "Control whether to enable or disable member directories on this site."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1158
msgid "Use Gravatar"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1159
msgid "Enable Gravatar"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1160
msgid "Do you want to use Gravatar instead of the default plugin profile photo (If the user did not upload a custom profile photo/avatar)?"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1166
msgid "Gravatar has a number of built in options which you can also use as defaults."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1183
msgid "Replace Gravatar's Default avatar"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1184
msgid "Set Default plugin avatar as Gravatar's Default avatar"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1185
msgid "Do you want to use the plugin default avatar instead of the gravatar default photo (If the user did not upload a custom profile photo/avatar)."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1191
msgid "Ignore the \"User Role > Registration Options\""
msgstr ""

#: includes/admin/core/class-admin-settings.php:1192
msgid "Automatically approve users from the wp-admin dashboard"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1193
msgid "Ignore registration settings and automatically approve the user if this user is added from the wp-admin dashboard."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1198
msgid "Delete user comments"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1199
msgid "Enable deleting user comments after deleting a user"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1200
msgid "Do you want to automatically delete a user's comments when they delete their account or are removed from the admin dashboard?"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1206
msgid "Password & Security settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1211
msgid "Toggle Password Visibility"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1212
msgid "Enable password show/hide icon on password field"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1213
msgid "Enable users to view their inputted password before submitting the form."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1219
msgid "Enable strong passwords"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1220
msgid "Enable this option to apply strong password rules to all password fields (user registration, password reset and password change)."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1226
msgid "Enter the minimum number of characters a user must use for their password. The default minimum characters is 8."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1234
msgid "Enter the maximum number of characters a user can use for their password. The default maximum characters is 30."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1242
msgid "For user registrations requiring email confirmation via a link, how long should the activation link remain active before expiring? If this field is left blank, the activation link will not expire."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1248
msgid "SEO"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1249
msgid "SEO settings for the user profiles."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1256
msgid "Hides the profile page for robots. This setting can be overridden by individual role settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1266
msgid "This is the title that is displayed on a specific user profile."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1273
msgid "This will be used in the meta description that is available for search-engines."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1283
#: includes/class-config.php:135
#: includes/class-config.php:947
#: includes/class-config.php:1012
#: includes/core/class-account.php:89
msgid "Account"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1286
msgid "Main account tab"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1287
msgid "Allows you to control the fields on the main tab of Account page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1292
msgid "Display First & Last name fields"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1293
msgid "Enable to display First & Last name fields"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1294
msgid "If enabled, the First & Last name fields will be shown on the account page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1300
msgid "Enable to prevent First & Last name editing by users"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1301
msgid "If enabled, this feature will prevent users from changing their first and last names on the account page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1308
msgid "First and last name fields are required"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1309
msgid "If enabled, users will not be allowed to remove their first or last names when updating their account information."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1315
msgid "Allow users to change email"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1316
msgid "Enable changing email via the account page"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1317
msgid "If disabled, users will not be allowed to change their email address on the account page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1322
msgid "Require password to update account"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1323
msgid "Enable required password"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1324
msgid "If enabled, users will need to enter their password when updating their information via the account page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1329
msgid "Change password tab"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1330
msgid "Enables you to toggle the change password tab on the account page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1336
msgid "Display password change account tab"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1337
msgid "Enable or disable the \"Password\" tab on the account page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1342
msgid "Privacy tab"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1343
msgid "Enables you to toggle the privacy tab on the account page. Disable this tab to prevent users from altering their privacy settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1349
msgid "Display privacy account tab"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1350
msgid "Enable or disable the \"Privacy\" tab on the account page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1356
msgid "Enable users ability to alter their profile visibility in member directories"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1357
msgid "If enabled, this will allow users to change their profile visibility in the member directory from the account page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1364
msgid "Set the default value for the \"Hide my profile from directory\" option."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1375
msgid "Notifications tab"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1376
msgid "Enables you to toggle the notifications tab on the account page. Disable this tab to prevent users from altering their notifications settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1382
msgid "Display notifications account tab"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1383
msgid "Enable or disable the \"Notifications\" tab on the account page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1388
msgid "Delete tab"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1389
msgid "Enables you to enable or disable the \"Delete Account\" tab on the account page. Disable this tab if you wish to prevent users from being able to delete their own accounts."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1395
msgid "Display delete account tab"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1396
msgid "Enable/disable the Delete account tab in account page."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1402
msgid "This is the custom text that will be displayed to users before they delete their account from your website when their password is required to confirm account deletion."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1412
msgid "This is the custom text that will be displayed to users before they delete their account from your website when no password is required to confirm account deletion."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1423
#: includes/admin/core/class-admin-settings.php:1426
msgid "Uploads"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1427
msgid "This page allows you to manage user upload options, enabling you to optimize photos for your site."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1433
msgid "Enable getting image orientation from EXIF data"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1434
msgid "Rotate image to and use orientation by the camera EXIF data."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1441
msgid "Quality is used to determine quality of image uploads, and ranges from 0 (worst quality, smaller file) to 100 (best quality, biggest file). The default range is 60."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1448
msgid "Any image upload above this width will be resized to this limit automatically."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1453
#: includes/admin/core/class-admin-settings.php:1659
#: includes/core/um-actions-profile.php:815
#: includes/core/um-actions-profile.php:833
msgid "Profile photo"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1454
msgid "Allows you to control the profile photos sizes, thumbnails, etc."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1461
msgid "Sets a maximum size for the uploaded photo."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1468
msgid "Here you can define which thumbnail sizes will be created for each profile photo upload."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1470
#: includes/admin/core/class-admin-settings.php:1500
msgid "Add New Size"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1476
#: includes/admin/core/class-admin-settings.php:1692
msgid "Cover photo"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1477
msgid "Allows you to control the cover photos sizes, thumbnails, etc."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1484
msgid "Sets a maximum size for the uploaded cover."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1491
msgid "This will be the minimum width for cover photo uploads."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1498
msgid "Here you can define which thumbnail sizes will be created for each cover photo upload."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1510
msgid "Access"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1513
msgid "Restriction Content"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1514
msgid "Provides  settings for controlling access to your site."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1518
msgid "Other"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1521
msgid "Reset Password"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1522
msgid "Allows to manage reset password settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1527
msgid "Password reset limit"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1529
msgid "If enabled, this sets a limit on the number of password resets a user can do."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1534
msgid "Enter password reset limit"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1535
msgid "Set the maximum reset password limit. If reached the maximum limit, user will be locked from using this."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1543
msgid "Change Password request limit"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1544
msgid "Enable limit for changing password"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1545
msgid "This option adds rate limit when submitting the change password form in the Account page. Users are only allowed to submit 1 request per 30 minutes to prevent from any brute-force attacks or password guessing with the form."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1551
msgid "Enable reset password only for approved users"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1552
msgid "This option makes possible to reset password only for approved user. Is used to prevent from any spam email attacks from not approved users."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1557
msgid "Blocked data when sign up"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1558
msgid "Allows to manage blocked data of signed up user."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1563
msgid "Blocked Email Addresses (Enter one email per line)"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1564
msgid "This will block the specified email addresses from being able to sign up or sign in to your site. To block an entire domain, use something like `*@domain.com`."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1569
msgid "Blacklist Words (Enter one word per line)"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1570
msgid "This option lets you specify blacklist of words to prevent anyone from signing up with such a word as their username."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1582
msgid "Email sender options"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1583
msgid "How the sender appears in outgoing Ultimate Member emails."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1589
#: includes/admin/core/class-admin-settings.php:1601
msgid "e.g. <EMAIL>."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1595
msgid "e.g. Site Name."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1606
msgid "Email template"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1607
msgid "Section to customize email templates settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1612
msgid "Content type"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1613
msgid "Enable HTML for Emails"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1614
msgid "If you plan use emails with HTML, please make sure that this option is enabled. Otherwise, HTML will be displayed as plain text."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1621
msgid "Appearance"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1624
#: includes/core/class-form.php:1078
msgid "Profile"
msgstr ""

#. translators: %s: Link to UM docs
#: includes/admin/core/class-admin-settings.php:1629
#, php-format
msgid "This section allows you to customize the user profile template and size. <a href=\"%s\" target=\"_blank\">Learn more about custom profile template creation</a>."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1635
msgid "This will be the default template to output profile."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1645
#: includes/admin/core/class-admin-settings.php:1895
#: includes/admin/core/class-admin-settings.php:1989
msgid "The maximum width this shortcode can take from the page width."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1653
msgid "The maximum width of the profile area inside profile (below profile header)."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1660
#: includes/admin/core/class-admin-settings.php:1693
msgid "This section allows you to customize the profile photo component on the user profile."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1666
msgid "You can change the default profile picture globally here. Please make sure that the photo is 300x300px."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1667
msgid "Select Default Profile Photo"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1675
msgid "Profile Photo Upload"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1677
msgid "Switch on/off the profile photo uploader."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1686
msgid "The global default of profile photo size. This can be overridden by individual form settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1701
msgid "You can change the default cover photo globally here. Please make sure that the default cover is large enough and respects the ratio you are using for cover photos."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1702
msgid "Select Default Cover Photo"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1711
msgid "Switch on/off the profile cover photos."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1719
msgid "The global default width of cover photo size. This can be overridden by individual form settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1727
msgid "Choose global ratio for cover photos of profiles."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1741
msgid "Header"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1742
msgid "This section allows you to customize the user profile header component."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1748
msgid "Show icons in Profile Header Meta"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1750
msgid "Display field icons for related user meta fields in header or not."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1755
msgid "Display name in profile header"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1758
msgid "Switch on/off the user name on profile header."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1763
msgid "Social links in profile header"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1766
msgid "Switch on/off the social links on profile header."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1771
msgid "User description in profile header"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1774
msgid "Switch on/off the user description on profile header."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1781
msgid "Maximum number of characters to allow in user description field in header."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1788
msgid "HTML support for user description"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1790
msgid "Switch on/off to enable/disable support for HTML tags on user description."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1797
msgid "For incompatible themes, please make the menu open from left instead of bottom by default."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1807
#: includes/admin/core/class-admin-settings.php:1914
#: includes/admin/core/class-admin-settings.php:2008
msgid "Buttons & Fields"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1808
msgid "This section allows you to customize the user profile buttons and fields layout."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1815
msgid "The text that is used for updating profile button."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1822
msgid "Show Profile Secondary Button"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1824
#: includes/admin/core/class-admin-settings.php:1931
#: includes/admin/core/class-admin-settings.php:2025
msgid "Switch on/off the secondary button display in the form."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1829
msgid "Profile Secondary Button Text"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1831
msgid "The text that is used for cancelling update profile button."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1839
msgid "This is applicable for edit mode only."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1851
msgid "Custom message on empty profile"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1854
msgid "Switch on/off the custom message that appears when the profile is empty."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1859
msgid "Custom message emoticon"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1862
msgid "Switch on/off the emoticon (sad face) that appears above the message."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1870
msgid "Profile Menu"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1871
msgid "This section allows you to customize the user profiles menus on your site."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1875
#: includes/admin/templates/form/mode.php:16
msgid "Registration Form"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1879
msgid "This section allows you to customize the user registration template and size."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1885
msgid "This will be the default template to output registration."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1902
#: includes/admin/core/class-admin-settings.php:1996
msgid "The shortcode is centered by default unless you specify otherwise here."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1915
msgid "This section allows you to customize the user registration buttons and fields layout."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1920
msgid "Registration Primary Button Text"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1922
#: includes/admin/core/class-admin-settings.php:2016
msgid "The text that is used for primary button text."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1929
msgid "Show Registration Secondary Button"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1938
#: includes/admin/core/class-admin-settings.php:2032
msgid "The text that is used for the secondary button text."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1947
#: includes/admin/core/class-admin-settings.php:2041
msgid "You can replace default link for this button by entering custom URL."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1955
msgid "This controls the display of field icons in the registration form."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1969
#: includes/admin/templates/form/mode.php:24
msgid "Login Form"
msgstr ""

#: includes/admin/core/class-admin-settings.php:1973
msgid "This section allows you to customize the user login template and size."
msgstr ""

#: includes/admin/core/class-admin-settings.php:1979
msgid "This will be the default template to output login."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2009
msgid "This section allows you to customize the user login buttons and fields layout."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2023
msgid "Show Login Secondary Button"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2049
msgid "Show Login Forgot Password Link"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2051
msgid "Switch on/off the forgot password link in login form."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2056
msgid "\"Remember Me\" checkbox"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2059
msgid "Allow users to choose If they want to stay signed in even after closing the browser. If you do not show this option, the default will be to not remember login session."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2065
msgid "This controls the display of field icons in the login form."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2087
msgid "Advanced"
msgstr ""

#. translators: %s: Link to UM docs
#: includes/admin/core/class-admin-settings.php:2092
#, php-format
msgid "Advanced settings section is designed to help you fine-tune your website or add extra features. <a href=\"%s\" target=\"_blank\">Learn more about advanced settings section</a>."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2097
msgid "Required fields' asterisk"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2098
msgid "Show an asterisk for required fields"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2103
msgid "Cache User Profile"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2104
msgid "Disable user data cache"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2105
msgid "Check this box if you would like to disable Ultimate Member user's cache."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2111
msgid "Enable flushing data"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2112
msgid "Check this box if you would like Ultimate Member to completely remove all of its data when the plugin/extensions are deleted."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2117
#: includes/admin/core/class-admin-settings.php:2120
msgid "Override templates"
msgstr ""

#. translators: %s: Link to the docs article.
#: includes/admin/core/class-admin-settings.php:2122
#, php-format
msgid "Each time we release an update, you'll find a list of changes made to the template files. <a href=\"%1$s\" target=\"_blank\">Learn more about overriding templates</a>.<br />You can easily check the status of the latest templates to see if they are up-to-date or need updating. <a href=\"%2$s\" target=\"_blank\">Learn more about fixing outdated templates</a>."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2133
#: includes/admin/core/class-admin-settings.php:2136
msgid "Features"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2137
msgid "Start using new features that are being progressively rolled out to improve the users management experience."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2142
msgid "Gutenberg Blocks"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2144
msgid "Check this box if you would like to use Ultimate Member blocks in Gutenberg editor. Important some themes have the conflicts with Gutenberg editor."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2150
msgid "Experimental features"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2151
msgid "These features are either experimental or incomplete, enable them at your own risk!"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2156
msgid "Design scheme"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2157
msgid "Enable new UI (for developers only)"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2158
msgid "Check this box if you would like to enable new UI."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2164
msgid "Enable new Form Builder (for developers only)"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2165
msgid "Check this box if you would like to enable new Form Builder."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2170
msgid "Legacy fonticons"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2171
msgid "Enable legacy fonticons"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2172
msgid "Check this box if you would like to enable legacy Ultimate Member fonticons used outdated versions of FontAwesome and Ionicons libraries."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2177
msgid "Legacy features"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2178
msgid "These features are related to the legacy logic or functionality. Please enable them only for the backward compatibility."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2185
msgid "Restriction content pre-queries"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2186
msgid "Disable pre-queries for restriction content logic"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2187
msgid "Please enable this option only in the cases when you have big or unnecessary queries on your site with active restriction logic. If you want to exclude posts only from the results queries instead of pre_get_posts and fully-hidden post logic also please enable this option. It activates the restriction content logic until 2.2.x version without latest security enhancements."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2194
#: includes/admin/core/class-admin-settings.php:2197
msgid "Developers"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2198
msgid "This section is designed to modify settings that are tailored for developers. If you are not a developer, please be cautious when changing these settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2203
msgid "Allowed Choice Callbacks (Enter one PHP function per line)"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2204
msgid "This option lets you specify the choice callback functions to prevent anyone from using 3rd-party functions that may put your site at risk."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2209
msgid "REST API Version"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2210
msgid "This controls the REST API version, we recommend to use the last version."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2212
msgid "1.0 version"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2213
msgid "2.0 version"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2219
msgid "Redirect"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2220
msgid "Allows to manage redirect settings."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2225
msgid "Allow external link redirect confirm"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2226
msgid "Enable JS.confirm for external links"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2227
msgid "Using JS.confirm alert when you go to an external link."
msgstr ""

#: includes/admin/core/class-admin-settings.php:2236
msgid "System info"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2363
msgid "Ultimate Member - Settings"
msgstr ""

#: includes/admin/core/class-admin-settings.php:2428
msgid "Save Changes"
msgstr ""

#: includes/admin/core/class-admin-settings.php:3028
msgid "Back"
msgstr ""

#: includes/admin/core/class-admin-settings.php:3040
msgid "Enable/Disable"
msgstr ""

#: includes/admin/core/class-admin-settings.php:3041
msgid "Enable this email notification"
msgstr ""

#: includes/admin/core/class-admin-settings.php:3046
msgid "Subject"
msgstr ""

#: includes/admin/core/class-admin-settings.php:3052
msgid "Email Content"
msgstr ""

#. translators: %1$s is an error code; %2$s is an error message.
#: includes/admin/core/class-admin-settings.php:3120
#, php-format
msgid "code: %1$s, message: %2$s;"
msgstr ""

#. translators: %1$s is an error data; %2$s is a support link.
#. translators: %1$s is an error; %2$s is a support link.
#: includes/admin/core/class-admin-settings.php:3126
#: includes/admin/core/class-admin-settings.php:3200
#: includes/admin/core/class-admin-settings.php:3209
#, php-format
msgid "There was an error with this license key: %1$s. Please <a href=\"%2$s\">contact our support team</a>."
msgstr ""

#. translators: %1$s is an expiry date; %2$s is a renewal link.
#. translators: %1$s is a expiry date; %2$s is a renew link.
#: includes/admin/core/class-admin-settings.php:3137
#: includes/admin/core/class-admin-settings.php:3232
#, php-format
msgid "Your license key expired on %1$s. Please <a href=\"%2$s\" target=\"_blank\">renew your license key</a>."
msgstr ""

#. translators: %s: support link name.
#: includes/admin/core/class-admin-settings.php:3148
#: includes/admin/core/class-admin-settings.php:3243
#, php-format
msgid "Your license key has been disabled. Please <a href=\"%s\" target=\"_blank\">contact support</a> for more information."
msgstr ""

#. translators: %s: account page.
#: includes/admin/core/class-admin-settings.php:3158
#: includes/admin/core/class-admin-settings.php:3253
#, php-format
msgid "Invalid license. Please <a href=\"%s\" target=\"_blank\">visit your account page</a> and verify it."
msgstr ""

#. translators: %1$s is a item name title; %2$s is a account page.
#: includes/admin/core/class-admin-settings.php:3169
#: includes/admin/core/class-admin-settings.php:3264
#, php-format
msgid "Your %1$s is not active for this URL. Please <a href=\"%2$s\" target=\"_blank\">visit your account page</a> to manage your license key URLs."
msgstr ""

#. translators: %s: item name.
#: includes/admin/core/class-admin-settings.php:3179
#: includes/admin/core/class-admin-settings.php:3274
#, php-format
msgid "This appears to be an invalid license key for %s."
msgstr ""

#. translators: %s: account link.
#: includes/admin/core/class-admin-settings.php:3186
#: includes/admin/core/class-admin-settings.php:3281
#, php-format
msgid "Your license key has reached its activation limit. <a href=\"%s\">View possible upgrades</a> now."
msgstr ""

#: includes/admin/core/class-admin-settings.php:3192
#: includes/admin/core/class-admin-settings.php:3287
msgid "The key you entered belongs to a bundle, please use the product specific license key."
msgstr ""

#: includes/admin/core/class-admin-settings.php:3198
#: includes/admin/core/class-admin-settings.php:3207
#: includes/admin/core/class-admin-settings.php:3218
msgid "unknown_error"
msgstr ""

#. translators: %1$s is an error; %2$s is a error data; %3$s is a support link.
#: includes/admin/core/class-admin-settings.php:3221
#, php-format
msgid "There was an error with this license key: %1$s%2$s. Please <a href=\"%3$s\">contact our support team</a>."
msgstr ""

#: includes/admin/core/class-admin-settings.php:3300
msgid "License key never expires."
msgstr ""

#. translators: %1$s is an expiry date; %2$s is a renewal link.
#: includes/admin/core/class-admin-settings.php:3308
#, php-format
msgid "Your license key expires soon! It expires on %1$s. <a href=\"%2$s\" target=\"_blank\">Renew your license key</a>."
msgstr ""

#. translators: %s: expiry date.
#: includes/admin/core/class-admin-settings.php:3319
#, php-format
msgid "Your license key expires on %s."
msgstr ""

#. translators: %s: item name.
#: includes/admin/core/class-admin-settings.php:3335
#, php-format
msgid "To receive updates, please enter your valid %s license key."
msgstr ""

#: includes/admin/core/class-admin-settings.php:3361
#: includes/admin/core/class-admin-settings.php:3370
msgid "Clear License"
msgstr ""

#: includes/admin/core/class-admin-settings.php:3365
msgid "Activate"
msgstr ""

#: includes/admin/core/class-admin-settings.php:3369
msgid "Re-Activate"
msgstr ""

#: includes/admin/core/class-admin-settings.php:3418
msgid "Re-check templates"
msgstr ""

#. translators: %s: Last checking templates time.
#: includes/admin/core/class-admin-settings.php:3422
#, php-format
msgid "Last update: %s. You could re-check changes manually."
msgstr ""

#. translators: %s: new version.
#: includes/admin/core/class-admin-upgrade.php:139
#, php-format
msgid "%s is a major update, and we highly recommend creating a full backup of your site before updating."
msgstr ""

#: includes/admin/core/class-admin-upgrade.php:246
msgid "Upgrade"
msgstr ""

#: includes/admin/core/class-admin-upgrade.php:254
msgid "empty"
msgstr ""

#. translators: %s: plugin name.
#: includes/admin/core/class-admin-upgrade.php:260
#, php-format
msgid "%s - Upgrade Process"
msgstr ""

#. translators: %1$s is a plugin version; %2$s is a last version upgrade.
#: includes/admin/core/class-admin-upgrade.php:266
#, php-format
msgid "You have installed <strong>%1$s</strong> version. Your latest DB version is <strong>%2$s</strong>. We recommend creating a backup of your site before running the update process. Do not exit the page before the update process has complete."
msgstr ""

#: includes/admin/core/class-admin-upgrade.php:269
msgid "After clicking the <strong>\"Run\"</strong> button, the update process will start. All information will be displayed in the <strong>\"Upgrade Log\"</strong> field."
msgstr ""

#: includes/admin/core/class-admin-upgrade.php:271
msgid "Upgrade Log"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:70
#: includes/admin/core/list-tables/roles-list-table.php:221
#: includes/admin/core/list-tables/version-template-list-table.php:30
msgid "item"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:71
#: includes/admin/core/list-tables/roles-list-table.php:222
#: includes/admin/core/list-tables/version-template-list-table.php:31
msgid "items"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:75
#: includes/admin/core/list-tables/roles-list-table.php:226
#: includes/admin/core/list-tables/version-template-list-table.php:36
msgid "not found."
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:234
msgid "Enabled"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:234
msgid "Disabled"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:257
msgid "Member"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:268
msgid "Manage"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:291
msgid "Email Notification"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:292
msgid "Email Notifications"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:321
msgid "Email"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:322
msgid "Recipient(s)"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:335
msgid "Email notifications"
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:337
msgid "Email notifications sent from Ultimate Member are listed below. Click on an email to configure it."
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:339
msgid "Emails should be sent from an email using your website's domain name. We highly recommend using a SMTP service email delivery."
msgstr ""

#: includes/admin/core/list-tables/emails-list-table.php:340
#, php-format
msgid "Please see this <a href=\"%s\" target=\"_blank\">doc</a> for more information."
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:26
#: includes/admin/core/list-tables/roles-list-table.php:117
#: includes/admin/core/list-tables/roles-list-table.php:466
#: includes/core/class-member-directory.php:465
msgid "Roles"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:390
msgid "Are you sure you want to delete this role?"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:395
msgid "Are you sure you want to reset UM role meta?"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:395
msgid "Reset UM Role meta"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:465
msgid "Role"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:478
msgid "Role Title"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:479
msgid "Role ID"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:480
msgid "No.of Members"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:481
msgid "UM Custom Role"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:482
msgid "WP-Admin Access"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:483
msgid "Priority"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:549
#: includes/admin/core/packages/2.1.17-alpha/functions.php:39
#: includes/admin/templates/directory/search.php:50
#: includes/admin/templates/directory/search.php:62
#: includes/admin/templates/role/role-edit.php:205
#: includes/common/class-cpt.php:34
#: includes/common/class-cpt.php:66
msgid "Add New"
msgstr ""

#: includes/admin/core/list-tables/roles-list-table.php:556
msgid "User Role <strong>Deleted</strong> Successfully."
msgstr ""

#: includes/admin/core/list-tables/version-template-list-table.php:130
msgid "Core path - "
msgstr ""

#: includes/admin/core/list-tables/version-template-list-table.php:132
msgid "Theme path - "
msgstr ""

#: includes/admin/core/list-tables/version-template-list-table.php:180
msgid "Templates"
msgstr ""

#: includes/admin/core/list-tables/version-template-list-table.php:210
msgid "Core version"
msgstr ""

#: includes/admin/core/list-tables/version-template-list-table.php:211
msgid "Theme version"
msgstr ""

#: includes/admin/core/packages/2.0-beta1/functions.php:8
#: includes/admin/core/packages/2.0.10/functions.php:8
msgid "Styles was upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.0-beta1/functions.php:21
msgid "User Roles was upgraded successfully"
msgstr ""

#. translators: %1$s is a from; %2$s is a to.
#: includes/admin/core/packages/2.0-beta1/functions.php:90
#, php-format
msgid "Users from %1$s to %2$s was upgraded successfully..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/functions.php:114
msgid "Settings was upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.0-beta1/functions.php:124
msgid "Menus settings was upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.0-beta1/functions.php:134
msgid "Mailchimp Lists was upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.0-beta1/functions.php:144
msgid "Social login forms was upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.0-beta1/functions.php:154
msgid "UM Custom Posts was upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.0-beta1/functions.php:171
msgid "Forums are ready for upgrade"
msgstr ""

#. translators: %1$s is a from; %2$s is a to.
#: includes/admin/core/packages/2.0-beta1/functions.php:220
#, php-format
msgid "Forums from %1$s to %2$s was upgraded successfully..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/functions.php:238
msgid "Woocommerce Products are ready for upgrade"
msgstr ""

#. translators: %1$s is a from; %2$s is a to.
#: includes/admin/core/packages/2.0-beta1/functions.php:308
#, php-format
msgid "Woocommerce Products from %1$s to %2$s was upgraded successfully..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/functions.php:326
msgid "Email Templates was upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:13
#: includes/admin/core/packages/2.0.10/init.php:6
msgid "Upgrade Styles..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:41
msgid "Upgrade Roles..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:55
msgid "Upgrade Users..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:79
msgid "Getting "
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:79
#: includes/admin/core/packages/2.0-beta1/init.php:91
#: includes/admin/core/packages/2.1.3-beta3/init.php:22
msgid " users..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:91
#: includes/admin/core/packages/2.1.3-beta3/init.php:22
#: includes/admin/core/packages/2.3.0/init.php:54
#: includes/admin/core/packages/2.8.0/init.php:25
#: assets/js/admin/forms.js:235
msgid "There are "
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:92
msgid "Start users upgrading..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:153
msgid "Upgrade Content Restriction Settings..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:181
msgid "Upgrade Settings..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:209
msgid "Upgrade Menu Items..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:237
msgid "Upgrade Mailchimp Lists..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:265
msgid "Upgrade Social Login Forms..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:293
msgid "Upgrade UM Custom Post Types..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:321
msgid "Upgrade bbPress Forums..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:322
msgid "Get bbPress Forums count..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:387
msgid "Upgrade Woocommerce Products..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:388
msgid "Get all Products..."
msgstr ""

#: includes/admin/core/packages/2.0-beta1/init.php:454
msgid "Upgrade Email Templates..."
msgstr ""

#: includes/admin/core/packages/2.0.10/functions.php:21
msgid "Users cache was cleared successfully"
msgstr ""

#: includes/admin/core/packages/2.0.10/init.php:34
msgid "Clear Users Cache..."
msgstr ""

#: includes/admin/core/packages/2.0.24/functions.php:11
msgid "Temporary dir was purged successfully"
msgstr ""

#: includes/admin/core/packages/2.0.24/init.php:6
msgid "Purge temp files dir..."
msgstr ""

#: includes/admin/core/packages/2.0.44/functions.php:13
msgid "Field was upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.0.44/init.php:7
msgid "Upgrade predefined metafields..."
msgstr ""

#: includes/admin/core/packages/2.0.54/functions.php:13
msgid "Roles was upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.0.54/init.php:7
msgid "Upgrade user roles..."
msgstr ""

#: includes/admin/core/packages/2.1.0-beta1/functions.php:11
msgid "Usermeta was upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.1.0-beta1/functions.php:24
msgid "Member directories were upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.1.0-beta1/init.php:7
#: includes/admin/core/packages/2.1.3-beta3/init.php:10
#: includes/admin/core/packages/2.8.0/init.php:13
msgid "Upgrade user metadata..."
msgstr ""

#: includes/admin/core/packages/2.1.0-beta1/init.php:35
msgid "Upgrade Member Directories..."
msgstr ""

#. translators: %1$s is a from; %2$s is a to.
#: includes/admin/core/packages/2.1.3-beta3/functions.php:125
#, php-format
msgid "Metadata from %1$s to %2$s users were upgraded successfully..."
msgstr ""

#: includes/admin/core/packages/2.1.3-beta3/functions.php:153
msgid "Usermeta table was upgraded successfully"
msgstr ""

#: includes/admin/core/packages/2.1.3-beta3/init.php:23
#: includes/admin/core/packages/2.3.0/init.php:55
#: includes/admin/core/packages/2.8.0/init.php:26
#: assets/js/admin/forms.js:236
msgid "Start metadata upgrading..."
msgstr ""

#: includes/admin/core/packages/2.1.3-beta3/init.php:75
msgid "Create additional metadata table..."
msgstr ""

#: includes/admin/core/packages/2.1.5/init.php:6
msgid "Upgrade form fields..."
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:30
msgctxt "Post Type General Name"
msgid "Profile Tabs"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:31
msgctxt "Post Type Singular Name"
msgid "Profile tab"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:32
#: includes/admin/core/packages/2.1.17-alpha/functions.php:33
#: includes/admin/core/packages/2.1.17-alpha/functions.php:50
msgid "Profile Tabs"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:34
msgid "Item Archives"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:35
msgid "Item Attributes"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:36
msgid "Parent Item:"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:37
msgid "All Items"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:38
msgid "Add New Item"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:40
msgid "New Item"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:41
msgid "Edit Item"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:42
msgid "Update Item"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:43
msgid "View Item"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:44
msgid "View Items"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:45
msgid "Search Item"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:46
msgid "Not found"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:136
msgid "Profile tabs have been updated successfully"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/functions.php:138
#: includes/admin/core/packages/2.3.0/functions.php:60
#: includes/admin/core/packages/2.3.0/functions.php:94
#: includes/admin/core/packages/2.6.0/functions.php:63
#: includes/admin/core/packages/2.8.0/functions.php:131
msgid "Database has been updated successfully"
msgstr ""

#: includes/admin/core/packages/2.1.17-alpha/init.php:6
msgid "Upgrade profile tabs..."
msgstr ""

#: includes/admin/core/packages/2.3.0/functions.php:58
msgid "SkypeID fields have been updated successfully"
msgstr ""

#. translators: %1$s is a from; %2$s is a to.
#: includes/admin/core/packages/2.3.0/functions.php:133
#, php-format
msgid "Metadata from %1$s to %2$s row were upgraded successfully..."
msgstr ""

#: includes/admin/core/packages/2.3.0/functions.php:152
msgid "Reset Password options have been updated successfully."
msgstr ""

#: includes/admin/core/packages/2.3.0/init.php:10
msgid "Upgrade SkypeID fields in UM Forms and generally in predefined fields..."
msgstr ""

#: includes/admin/core/packages/2.3.0/init.php:42
msgid "Upgrade SkypeID fields metadata for users..."
msgstr ""

#: includes/admin/core/packages/2.3.0/init.php:54
#: assets/js/admin/forms.js:235
msgid " metadata rows..."
msgstr ""

#: includes/admin/core/packages/2.3.0/init.php:108
msgid "Upgrade the \"Require strong password\" options..."
msgstr ""

#: includes/admin/core/packages/2.4.0/functions.php:49
msgid "Custom callback functions whitelisted for 2.4.0 version."
msgstr ""

#: includes/admin/core/packages/2.4.0/init.php:6
msgid "Added custom callback functions for the UM Forms custom fields to the whitelist setting..."
msgstr ""

#: includes/admin/core/packages/2.5.0/functions.php:43
msgid "Phone Number and Mobile Number fields have been successfully updated."
msgstr ""

#: includes/admin/core/packages/2.5.0/init.php:6
msgid "Updated phone number fields in the UM Forms fields..."
msgstr ""

#: includes/admin/core/packages/2.6.0/functions.php:61
msgid "Social URLs fields have been successfully updated."
msgstr ""

#: includes/admin/core/packages/2.6.0/init.php:6
msgid "Updated social URLs fields in the UM Forms fields..."
msgstr ""

#. translators: %1$s is a from; %2$s is a to.
#: includes/admin/core/packages/2.8.0/functions.php:107
#, php-format
msgid "Metadata from %1$s to %2$s were upgraded successfully..."
msgstr ""

#: includes/admin/core/packages/2.8.0/init.php:25
msgid " metarows..."
msgstr ""

#: includes/admin/core/packages/2.8.0/init.php:78
msgid "Update options table..."
msgstr ""

#: includes/admin/templates/access/restrict_content.php:55
msgid "Restrict access to this post?"
msgstr ""

#: includes/admin/templates/access/restrict_content.php:56
msgid "Activate individual content restriction settings for this post. Then ignore all terms restriction content settings if they exists."
msgstr ""

#: includes/admin/templates/access/restrict_content.php:62
msgid "Who can access this post?"
msgstr ""

#: includes/admin/templates/access/restrict_content.php:74
msgid "Select which roles can access this post"
msgstr ""

#: includes/admin/templates/access/restrict_content.php:75
msgid "Leave empty if you want to display a post for all logged in users"
msgstr ""

#: includes/admin/templates/access/restrict_content.php:84
msgid "Would you like to display a 404 page for users who do not have access to this page?"
msgstr ""

#: includes/admin/templates/access/restrict_content.php:85
msgid "Recommended to be enabled. Restricted post will be hidden by exclusion from WP Query. The safest and most effective method that hides post and its comments from all requests, RSS feeds, etc. on your site"
msgstr ""

#: includes/admin/templates/access/restrict_content.php:92
msgid "What happens when users without access try to view the post?"
msgstr ""

#: includes/admin/templates/access/restrict_content.php:93
msgid "Action when users without access tries to view the post"
msgstr ""

#: includes/admin/templates/access/restrict_content.php:105
msgid "Would you like to use the global default message or apply a custom message to this post?"
msgstr ""

#: includes/admin/templates/access/restrict_content.php:125
msgid "Select redirect to page when user hasn't access to post"
msgstr ""

#: includes/admin/templates/dashboard/cache.php:29
msgid "Run this task from time to time to keep your DB clean."
msgstr ""

#. translators: %s: users number.
#: includes/admin/templates/dashboard/cache.php:35
#, php-format
msgid "Clear cache of %s users"
msgstr ""

#: includes/admin/templates/dashboard/cache.php:39
msgid "Clear user statuses cache"
msgstr ""

#. translators: %s: temp folder size.
#: includes/admin/templates/dashboard/purge.php:18
#, php-format
msgid "You can free up <span class=\"red\">%s MB</span> by purging your temp upload directory."
msgstr ""

#: includes/admin/templates/dashboard/purge.php:24
msgid "Purge Temp"
msgstr ""

#: includes/admin/templates/dashboard/purge.php:31
msgid "Your temp uploads directory is <span class=\"ok\">clean</span>. There is nothing to purge."
msgstr ""

#: includes/admin/templates/dashboard/upgrade-request.php:14
msgid "Run this task from time to time if you have issues with WP Cron and need to get UM extension updates."
msgstr ""

#: includes/admin/templates/dashboard/upgrade-request.php:17
msgid "Get latest versions"
msgstr ""

#: includes/admin/templates/dashboard/users.php:24
msgid "Pending Review"
msgstr ""

#: includes/admin/templates/dashboard/users.php:36
#: includes/common/class-users.php:105
msgid "Approved"
msgstr ""

#: includes/admin/templates/dashboard/users.php:46
msgid "Awaiting Email Confirmation"
msgstr ""

#: includes/admin/templates/dashboard/users.php:58
msgid "Rejected"
msgstr ""

#: includes/admin/templates/dashboard/users.php:68
msgid "Inactive"
msgstr ""

#: includes/admin/templates/directory/general.php:47
msgid "View type(s)"
msgstr ""

#: includes/admin/templates/directory/general.php:48
msgid "View type a specific parameter in the directory"
msgstr ""

#: includes/admin/templates/directory/general.php:58
msgid "Default directory view type"
msgstr ""

#: includes/admin/templates/directory/general.php:66
msgid "User Roles to Display"
msgstr ""

#: includes/admin/templates/directory/general.php:67
msgid "If you do not want to show all members, select only user roles to appear in this directory"
msgstr ""

#: includes/admin/templates/directory/general.php:76
msgid "If 'Use Gravatars' as profile photo is enabled, this option is ignored"
msgstr ""

#: includes/admin/templates/directory/general.php:88
msgid "Only show specific users (Enter one username per line)"
msgstr ""

#: includes/admin/templates/directory/general.php:94
msgid "Exclude specific users (Enter one username per line)"
msgstr ""

#: includes/admin/templates/directory/pagination.php:18
msgid "If turned on, member results will only appear after search/filter is performed"
msgstr ""

#: includes/admin/templates/directory/pagination.php:26
msgid "Use this setting to control the maximum number of profiles to appear in this directory. Leave blank to disable this limit"
msgstr ""

#: includes/admin/templates/directory/pagination.php:34
msgid "Number of profiles to appear on page for standard users"
msgstr ""

#: includes/admin/templates/directory/pagination.php:41
msgid "Number of profiles per page (for Mobiles & Tablets)"
msgstr ""

#: includes/admin/templates/directory/pagination.php:42
msgid "Number of profiles to appear on page for mobile users"
msgstr ""

#: includes/admin/templates/directory/pagination.php:50
msgid "Customize the search result text . e.g. Found 3,000 Members. Leave this blank to not show result text"
msgstr ""

#: includes/admin/templates/directory/pagination.php:51
#: includes/admin/templates/directory/pagination.php:59
msgid "You could use {total_users} placeholder for getting users count"
msgstr ""

#: includes/admin/templates/directory/pagination.php:58
msgid "Same as above but in case of 1 user found only"
msgstr ""

#: includes/admin/templates/directory/pagination.php:66
msgid "This is the text that is displayed if no users are found during a search"
msgstr ""

#: includes/admin/templates/directory/profile.php:26
msgid "If turned on, the users cover photo will appear in the directory"
msgstr ""

#: includes/admin/templates/directory/profile.php:44
msgid "Choose field(s) to display in tagline"
msgstr ""

#: includes/admin/templates/directory/profile.php:47
#: includes/admin/templates/directory/profile.php:63
#: includes/admin/templates/directory/search.php:91
msgid "Add New Custom Field"
msgstr ""

#: includes/admin/templates/directory/profile.php:61
msgid "Choose field(s) to display in extra user information section"
msgstr ""

#: includes/admin/templates/directory/profile.php:80
msgid "If not checked always shown"
msgstr ""

#: includes/admin/templates/directory/search.php:30
msgid "If turned on, users will be able to search members in this directory"
msgstr ""

#: includes/admin/templates/directory/search.php:37
msgid "If you want to allow specific user roles to be able to search only"
msgstr ""

#: includes/admin/templates/directory/search.php:53
msgid "Choose fields to exclude them from search. This option will delete all included fields."
msgstr ""

#: includes/admin/templates/directory/search.php:65
msgid "Choose fields to only include them in the search. This option will delete all excluded fields."
msgstr ""

#: includes/admin/templates/directory/search.php:71
msgid "If turned on, users will be able to filter members in this directory"
msgstr ""

#: includes/admin/templates/directory/search.php:78
msgid "If you want to allow specific user roles to be able to filter only"
msgstr ""

#: includes/admin/templates/directory/search.php:87
msgid "Choose filter(s) meta to enable"
msgstr ""

#: includes/admin/templates/directory/search.php:99
msgid "If turned on, filters bar will be visible after a page loading"
msgstr ""

#: includes/admin/templates/directory/search.php:107
msgid "If turned on, filters bar can be collapsed after a page loading"
msgstr ""

#: includes/admin/templates/directory/search.php:115
msgid "Limit which users appear in the member directory e.g only display users from USA"
msgstr ""

#: includes/admin/templates/directory/search.php:118
msgid "Add New Filter"
msgstr ""

#: includes/admin/templates/directory/sorting.php:15
msgid "Default sorting users by a specific parameter in the directory"
msgstr ""

#: includes/admin/templates/directory/sorting.php:23
msgid "To sort by a custom field, enter the meta key of field here"
msgstr ""

#: includes/admin/templates/directory/sorting.php:31
msgid "To correct sort by a custom field, choose a data type"
msgstr ""

#: includes/admin/templates/directory/sorting.php:40
msgid "To correct sort by a custom field, choose an order"
msgstr ""

#: includes/admin/templates/directory/sorting.php:52
msgid "To sort by a custom field, enter the label of sorting here"
msgstr ""

#: includes/admin/templates/directory/sorting.php:60
msgid "Whether to provide an ability to change the sorting on the directory page"
msgstr ""

#: includes/admin/templates/directory/sorting.php:66
msgid "Choose field(s) to enable in sorting"
msgstr ""

#: includes/admin/templates/directory/sorting.php:69
#: includes/admin/templates/form/profile_settings.php:29
msgid "Add New Field"
msgstr ""

#: includes/admin/templates/extensions.php:242
msgid "Premium"
msgstr ""

#: includes/admin/templates/extensions.php:247
msgid "Free"
msgstr ""

#: includes/admin/templates/extensions.php:277
msgid "Get this Add on"
msgstr ""

#: includes/admin/templates/extensions.php:280
msgid "More Details"
msgstr ""

#: includes/admin/templates/form/builder.php:16
msgid "Live Preview Screen"
msgstr ""

#: includes/admin/templates/form/builder.php:21
msgid "Live Preview Mobile"
msgstr ""

#: includes/admin/templates/form/builder.php:33
msgid "Add Master Row"
msgstr ""

#: includes/admin/templates/form/login_customize.php:24
#: includes/admin/templates/form/profile_customize.php:39
#: includes/admin/templates/form/register_customize.php:30
msgid "Switch to yes if you want to customize this form settings, styling &amp; appearance"
msgstr ""

#: includes/admin/templates/form/login_customize.php:43
#: includes/admin/templates/form/profile_customize.php:68
#: includes/admin/templates/form/register_customize.php:57
msgid "The maximum width of shortcode in pixels e.g. 600px"
msgstr ""

#: includes/admin/templates/form/login_customize.php:51
#: includes/admin/templates/form/profile_customize.php:84
#: includes/admin/templates/form/register_customize.php:65
msgid "Whether to show field icons and where to show them relative to the field"
msgstr ""

#: includes/admin/templates/form/login_customize.php:64
#: includes/admin/templates/form/login_customize.php:83
#: includes/admin/templates/form/profile_customize.php:97
#: includes/admin/templates/form/profile_customize.php:116
#: includes/admin/templates/form/register_customize.php:78
#: includes/admin/templates/form/register_customize.php:97
msgid "Customize the button text"
msgstr ""

#: includes/admin/templates/form/login_settings.php:16
msgid "Change this If you want to override role redirection settings after login only."
msgstr ""

#: includes/admin/templates/form/mode.php:11
msgid "Note: "
msgstr ""

#: includes/admin/templates/form/mode.php:11
msgid "Form type cannot be changed for the default forms."
msgstr ""

#: includes/admin/templates/form/mode.php:20
msgid "Profile Form"
msgstr ""

#: includes/admin/templates/form/profile_customize.php:51
msgid "Please note if you make a profile form specific to a role then you must make sure that every other role is assigned a profile form"
msgstr ""

#: includes/admin/templates/form/profile_customize.php:76
msgid "The maximum width of the profile area inside profile (below profile header)"
msgstr ""

#: includes/admin/templates/form/profile_customize.php:135
#: includes/admin/templates/form/profile_customize.php:170
msgid "Set the profile photo size in pixels here"
msgstr ""

#: includes/admin/templates/form/profile_customize.php:144
msgid "The shortcode is centered by default unless you specify otherwise here"
msgstr ""

#: includes/admin/templates/form/profile_customize.php:158
msgid "Switch on/off the profile photo uploader"
msgstr ""

#: includes/admin/templates/form/profile_customize.php:179
msgid "Require user to update a profile photo when updating their profile"
msgstr ""

#: includes/admin/templates/form/profile_settings.php:26
msgid "Fields selected here will appear in the profile header area below the user's display name"
msgstr ""

#: includes/admin/templates/form/register_customize.php:40
msgid "Assign role to form"
msgstr ""

#: includes/admin/templates/form/register_gdpr.php:8
msgid "Select page"
msgstr ""

#: includes/admin/templates/form/register_gdpr.php:28
msgid "Enable on this form"
msgstr ""

#: includes/admin/templates/form/register_gdpr.php:38
msgid "Content"
msgstr ""

#: includes/admin/templates/form/register_gdpr.php:47
#: includes/admin/templates/form/register_gdpr.php:48
#: templates/gdpr-register.php:32
#: templates/gdpr-register.php:34
msgid "Show privacy policy"
msgstr ""

#: includes/admin/templates/form/register_gdpr.php:55
#: includes/admin/templates/form/register_gdpr.php:56
#: templates/gdpr-register.php:33
msgid "Hide privacy policy"
msgstr ""

#: includes/admin/templates/form/register_gdpr.php:63
#: includes/admin/templates/form/register_gdpr.php:64
#: templates/gdpr-register.php:35
msgid "Please confirm that you agree to our privacy policy"
msgstr ""

#: includes/admin/templates/form/register_gdpr.php:71
#: includes/admin/templates/form/register_gdpr.php:72
#: templates/gdpr-register.php:38
msgid "Please confirm your acceptance of our privacy policy"
msgstr ""

#: includes/admin/templates/gdpr.php:4
msgid "What personal data we collect and why we collect it"
msgstr ""

#. translators: %s: plugin name.
#: includes/admin/templates/gdpr.php:12
#, php-format
msgid "%s provides you with forms for user registration, login and profiles."
msgstr ""

#: includes/admin/templates/gdpr.php:16
msgid "Via these forms you are collecting personal data from your users."
msgstr ""

#: includes/admin/templates/gdpr.php:19
msgid "You should include in your privacy policy what personal data is captured when someone submits/fills in one of the forms, why you collect it and what you do with this data and how long you keep it."
msgstr ""

#: includes/admin/templates/gdpr.php:23
msgid "How long we retain your data"
msgstr ""

#: includes/admin/templates/gdpr.php:26
msgid "Registered user information is retained in your website’s database indefinitely."
msgstr ""

#: includes/admin/templates/gdpr.php:29
msgid "Data can be exported or removed upon users request via the existing WordPress data exporter or eraser."
msgstr ""

#: includes/admin/templates/gdpr.php:32
msgid "If syncing data to a 3rd party service (e.g Mailchimp via our MailChimp extension), data is retained there until unsubscribed or deleted."
msgstr ""

#: includes/admin/templates/gdpr.php:36
msgid "Where we send your data"
msgstr ""

#. translators: %s: plugin name.
#: includes/admin/templates/gdpr.php:41
#, php-format
msgid "%s does not send any user data outside of your site by default."
msgstr ""

#: includes/admin/templates/gdpr.php:45
msgid "If you have extended the functionality of the plugin (e.g sending registered user data to MailChimp via our MailChimp extension, this user info may be passed to these external services. These services may be located abroad and outwith the EU."
msgstr ""

#: includes/admin/templates/modal/dynamic_registration_preview.php:7
msgid "Review Registration Details"
msgstr ""

#: includes/admin/templates/modal/forms/dynamic_edit_field.php:9
msgid "Edit Field"
msgstr ""

#: includes/admin/templates/modal/forms/dynamic_edit_field.php:18
#: includes/admin/templates/modal/forms/dynamic_edit_row.php:18
#: includes/admin/templates/modal/forms/dynamic_new_divider.php:18
#: includes/admin/templates/modal/forms/dynamic_new_field.php:18
#: includes/admin/templates/modal/forms/dynamic_new_group.php:18
#: includes/admin/templates/modal/forms/fonticons.php:26
#: includes/admin/templates/role/publish.php:33
#: includes/class-config.php:231
#: includes/core/class-fields.php:3080
#: includes/core/class-fields.php:3205
#: includes/core/um-actions-profile.php:900
#: includes/core/um-actions-profile.php:909
#: includes/core/um-actions-profile.php:1088
#: includes/core/um-actions-profile.php:1121
#: includes/core/um-actions-profile.php:1513
#: includes/core/um-actions-profile.php:1520
msgid "Cancel"
msgstr ""

#: includes/admin/templates/modal/forms/dynamic_edit_row.php:9
msgid "Edit Row Settings"
msgstr ""

#: includes/admin/templates/modal/forms/dynamic_form_preview.php:8
msgid "Live Form Preview"
msgstr ""

#: includes/admin/templates/modal/forms/dynamic_form_preview.php:14
msgid "Continue editing"
msgstr ""

#: includes/admin/templates/modal/forms/dynamic_new_divider.php:9
msgid "Add a New Divider"
msgstr ""

#: includes/admin/templates/modal/forms/dynamic_new_divider.php:15
#: includes/admin/templates/modal/forms/dynamic_new_field.php:15
#: includes/admin/templates/modal/forms/dynamic_new_group.php:15
msgid "Add"
msgstr ""

#: includes/admin/templates/modal/forms/dynamic_new_field.php:9
msgid "Add a New Field"
msgstr ""

#: includes/admin/templates/modal/forms/dynamic_new_group.php:9
msgid "Add a New Field Group"
msgstr ""

#: includes/admin/templates/modal/forms/fields.php:7
msgid "Fields Manager"
msgstr ""

#. translators: %s: icons nubber.
#: includes/admin/templates/modal/forms/fonticons.php:17
#, php-format
msgid "Choose from %s available icons"
msgstr ""

#: includes/admin/templates/modal/forms/fonticons.php:25
msgid "Finish"
msgstr ""

#: includes/admin/templates/role/admin-permissions.php:18
msgid "The core admin role must always have access to wp-admin / WordPress backend"
msgstr ""

#: includes/admin/templates/role/admin-permissions.php:25
msgid "Mark this option if you need to hide the adminbar on frontend for this role"
msgstr ""

#: includes/admin/templates/role/admin-permissions.php:32
msgid "Allow this role to edit accounts of other members"
msgstr ""

#: includes/admin/templates/role/admin-permissions.php:39
msgid "Multiple selections of which roles this role can edit, none selected to allow this role to edit all member roles."
msgstr ""

#: includes/admin/templates/role/admin-permissions.php:49
msgid "Allow this role to delete other user accounts."
msgstr ""

#: includes/admin/templates/role/admin-permissions.php:56
msgid "Multiple selections of which roles this role can delete, none selected to allow this role to delete all member roles"
msgstr ""

#: includes/admin/templates/role/delete.php:16
msgid "Select what happens when a user with this role deletes their own account"
msgstr ""

#: includes/admin/templates/role/delete.php:27
msgid "Set a url to redirect this user role to after they delete account"
msgstr ""

#: includes/admin/templates/role/general.php:15
msgid "Can this role edit his own profile?"
msgstr ""

#: includes/admin/templates/role/general.php:22
msgid "Allow this role to delete their account and end their membership on your site"
msgstr ""

#: includes/admin/templates/role/home.php:16
msgid "Allow this user role to view your site's homepage"
msgstr ""

#: includes/admin/templates/role/home.php:23
msgid "Set a url to redirect this user role to if they try to view your site's homepage"
msgstr ""

#: includes/admin/templates/role/login.php:16
msgid "Select what happens when a user with this role logins to your site"
msgstr ""

#: includes/admin/templates/role/login.php:29
msgid "Set a url to redirect this user role to after they login with their account"
msgstr ""

#: includes/admin/templates/role/logout.php:16
msgid "Select what happens when a user with this role logouts of your site"
msgstr ""

#: includes/admin/templates/role/logout.php:27
msgid "Set a url to redirect this user role to after they logout from site"
msgstr ""

#: includes/admin/templates/role/profile.php:18
msgid "Can this role view all member profiles?"
msgstr ""

#: includes/admin/templates/role/profile.php:25
msgid "Multiple selections of which roles this role can view, none selected to allow this role to view all member roles."
msgstr ""

#: includes/admin/templates/role/profile.php:36
msgid "Can this role make their profile private?"
msgstr ""

#: includes/admin/templates/role/profile.php:44
msgid "Can this role view private profiles?"
msgstr ""

#: includes/admin/templates/role/profile.php:53
msgid "Hides the profile page for robots. The default value depends on the General > Users setting."
msgstr ""

#: includes/admin/templates/role/publish.php:19
msgid "Role Priority"
msgstr ""

#: includes/admin/templates/role/publish.php:20
msgid "The higher the number, the higher the priority"
msgstr ""

#: includes/admin/templates/role/publish.php:32
msgid "Update Role"
msgstr ""

#: includes/admin/templates/role/publish.php:32
msgid "Create Role"
msgstr ""

#: includes/admin/templates/role/publish.php:40
msgid "Learn more about role priorities"
msgstr ""

#: includes/admin/templates/role/register.php:22
msgid "Select the status you would like this user role to have after they register on your site"
msgstr ""

#: includes/admin/templates/role/register.php:34
#: includes/admin/templates/role/register.php:53
#: includes/admin/templates/role/register.php:95
msgid "Select what action is taken after a person registers on your site. Depending on the status you can redirect them to their profile, a custom url or show a custom message"
msgstr ""

#: includes/admin/templates/role/register.php:65
msgid "Thank you for registering. Before you can login we need you to activate your account by clicking the activation link in the email we just sent you."
msgstr ""

#: includes/admin/templates/role/register.php:79
msgid "Login the user after validating the activation link"
msgstr ""

#: includes/admin/templates/role/register.php:87
msgid "If you want users to go to a specific page other than login page after email activation, enter the URL here."
msgstr ""

#: includes/admin/templates/role/register.php:107
msgid "Thank you for applying for membership to our site. We will review your details and send you an email letting you know whether your application has been successful or not."
msgstr ""

#: includes/admin/templates/role/role-edit.php:74
#: includes/admin/templates/role/role-edit.php:78
msgid "Security Issue"
msgstr ""

#: includes/admin/templates/role/role-edit.php:91
msgid "Title is empty!"
msgstr ""

#: includes/admin/templates/role/role-edit.php:122
msgid "Role already exists!"
msgstr ""

#: includes/admin/templates/role/role-edit.php:193
msgid "Add New Role"
msgstr ""

#: includes/admin/templates/role/role-edit.php:195
msgid "Edit Role"
msgstr ""

#: includes/admin/templates/role/role-edit.php:216
msgid "User Role <strong>Added</strong> Successfully."
msgstr ""

#: includes/admin/templates/role/role-edit.php:219
msgid "User Role <strong>Updated</strong> Successfully."
msgstr ""

#: includes/admin/templates/role/role-edit.php:247
msgid "Enter Title Here"
msgstr ""

#: includes/admin/templates/role/wp-capabilities.php:39
msgid "Uncheck All"
msgstr ""

#: includes/admin/templates/role/wp-capabilities.php:41
#: includes/admin/templates/role/wp-capabilities.php:49
msgid "Check All"
msgstr ""

#: includes/ajax/class-pages.php:56
#, php-format
msgid "%s (ID: %s)"
msgstr ""

#. translators: %s capability name
#: includes/ajax/class-secure.php:87
#, php-format
msgid "<strong>`%s`</strong> <span style=\"color:green\">is safe.</span>"
msgstr ""

#. translators: %1$s capability name, has affected %2$d user account
#: includes/ajax/class-secure.php:91
#, php-format
msgid "<strong>`%1$s`</strong> <span style=\"color:red\">has affected %2$d user account.</span>"
msgid_plural "<strong>`%1$s`</strong> <span style=\"color:red\">has affected %2$d user accounts.</span>"
msgstr[0] ""
msgstr[1] ""

#: includes/ajax/class-secure.php:251
msgid "Scan Complete."
msgstr ""

#: includes/ajax/class-secure.php:258
msgid "Suspicious Accounts Detected!"
msgstr ""

#. translators: %s suspicious account
#: includes/ajax/class-secure.php:260
#, php-format
msgid "We have found <strong style=\"color:red;\">%s suspicious account</strong> created on your site via Ultimate Member Forms."
msgid_plural "We have found <strong style=\"color:red;\">%s suspicious accounts</strong> created on your site via Ultimate Member Forms."
msgstr[0] ""
msgstr[1] ""

#: includes/ajax/class-secure.php:261
msgid "We've temporarily disabled the suspicious account(s) for you to <strong>take actions</strong>."
msgstr ""

#. translators: %s suspicious account
#: includes/ajax/class-secure.php:272
#, php-format
msgid "Also, We've found <strong style=\"color:red;\">%s account</strong>"
msgid_plural "Also, We've found <strong style=\"color:red;\">%s accounts</strong>"
msgstr[0] ""
msgstr[1] ""

#. translators: %s account creation date
#: includes/ajax/class-secure.php:274
#, php-format
msgid "created on %s when the suspicious account was created."
msgid_plural "created on %s when the suspicious accounts were created."
msgstr[0] ""
msgstr[1] ""

#: includes/ajax/class-secure.php:277
msgid "Suspicious Accounts"
msgstr ""

#: includes/ajax/class-secure.php:278
msgid "No suspicious accounts found"
msgstr ""

#: includes/ajax/class-secure.php:281
msgid "PLEASE READ OUR <strong>RECOMMENDATIONS</strong> BELOW: "
msgstr ""

#: includes/ajax/class-secure.php:283
msgid "<strong style=\"color:red\">WARNING:</strong> Ensure that you've created a full backup of your site as your restoration point before changing anything on your site with our recommendations."
msgstr ""

#: includes/ajax/class-secure.php:286
msgid "1. Please temporarily lock all your active Register forms."
msgstr ""

#: includes/ajax/class-secure.php:287
msgid "Click here to lock them now."
msgstr ""

#: includes/ajax/class-secure.php:288
msgid "You can unblock the Register forms later. Just go to Ultimate Member > Settings > Advanced > Security and uncheck the option \"Lock All Register Forms\"."
msgstr ""

#: includes/ajax/class-secure.php:302
msgid "2. Review all suspicious accounts and delete them completely."
msgstr ""

#: includes/ajax/class-secure.php:303
msgid "Click here to review accounts."
msgstr ""

#: includes/ajax/class-secure.php:308
msgid "3. If accounts are suspicious to you, please destroy all user sessions to logout active users on your site."
msgstr ""

#: includes/ajax/class-secure.php:309
msgid "Click here to Destroy Sessions now"
msgstr ""

#: includes/ajax/class-secure.php:312
msgid "4. Run a complete scan on your site using third-party Security plugins such as"
msgstr ""

#: includes/ajax/class-secure.php:313
msgid "WPScan/Jetpack Protect or WordFence Security"
msgstr ""

#: includes/ajax/class-secure.php:319
msgid "5. Force users to Reset their Passwords."
msgstr ""

#: includes/ajax/class-secure.php:320
msgid "Click here to enable this option"
msgstr ""

#: includes/ajax/class-secure.php:321
msgid "When this option is enabled, users will be asked to reset their passwords(one-time) on the next login in the UM Login form."
msgstr ""

#: includes/ajax/class-secure.php:324
msgid "6. Once your site is secured, please create or enable Daily Backups of your server/site. You can contact your hosting provider to assist you on this matter."
msgstr ""

#: includes/ajax/class-secure.php:327
msgid "👇 MORE RECOMMENDATIONS BELOW."
msgstr ""

#: includes/ajax/class-secure.php:330
msgid "Review & Resolve Issues with Site Health Check tool"
msgstr ""

#: includes/ajax/class-secure.php:331
msgid "Site Health is a tool in WordPress that helps you monitor how your site is doing. It shows critical information about your WordPress configuration and items that require your attention."
msgstr ""

#. translators: %d issue in the Site Health status
#: includes/ajax/class-secure.php:334
#, php-format
msgid "There's %d issue in the Site Health status"
msgid_plural "There are %d issues in the Site Health status"
msgstr[0] ""
msgstr[1] ""

#: includes/ajax/class-secure.php:335
msgid "Review Site Health Status"
msgstr ""

#: includes/ajax/class-secure.php:337
msgid "There are no issues found in the Site Health status"
msgstr ""

#: includes/ajax/class-secure.php:340
msgid "Default WP Register Form"
msgstr ""

#: includes/ajax/class-secure.php:342
msgid "The default WordPress Register form is enabled. If you're getting Spam User Registrations, we recommend that you enable a Challenge-Response plugin such as our <a href=\"https://wordpress.org/plugins/um-recaptcha/\" target=\"_blank\">Ultimate Member - Google reCAPTCHA</a> extension."
msgstr ""

#: includes/ajax/class-secure.php:345
msgid "The default WordPress Register form is disabled."
msgstr ""

#: includes/ajax/class-secure.php:348
msgid "Secure Register Forms"
msgstr ""

#: includes/ajax/class-secure.php:349
msgid "We've removed the assignment of administrative roles for Register forms due to vulnerabilities in previous versions of the plugin. If your Register forms still have Administrative roles, we recommend that you assign a non-admin roles to secure the forms."
msgstr ""

#: includes/ajax/class-secure.php:363
msgid "contains <strong>administrative role</strong>"
msgstr ""

#: includes/ajax/class-secure.php:372
msgid "is <strong>secured</strong>"
msgstr ""

#: includes/ajax/class-secure.php:379
msgid "Block Disposable Email Addresses/Domains"
msgstr ""

#: includes/ajax/class-secure.php:381
msgid "You are not blocking email addresses or disposable email domains that are mostly used for Spam Account Registrations. You can get the list of disposable email domains with our basic extension <a href=\"https://docs.ultimatemember.com/article/1870-block-disposable-email-domains\" target=\"_blank\">Block Disposable Email Domains</a>."
msgstr ""

#: includes/ajax/class-secure.php:384
msgid "Blocked Emails option is already set."
msgstr ""

#: includes/ajax/class-secure.php:388
msgid "Manage User Roles & Capabilities"
msgstr ""

#. translators: %d users count
#: includes/ajax/class-secure.php:405
#, php-format
msgid "We have found %d user account"
msgid_plural "We have found %d user accounts"
msgstr[0] ""
msgstr[1] ""

#. translators: %d count of banned capabilities
#: includes/ajax/class-secure.php:407
#, php-format
msgid " affected by %d capability selected in the Banned Administrative Capabilities."
msgid_plural " affected by one of the %d capabilities selected in the Banned Administrative Capabilities."
msgstr[0] ""
msgstr[1] ""

#: includes/ajax/class-secure.php:411
msgid "The flagged capabilities are related to the following roles: "
msgstr ""

#. translators: %s is the role settings URL
#: includes/ajax/class-secure.php:413
#, php-format
msgid "The affected user accounts will be flagged as suspicious when they update their Profile/Account. If you are not using these capabilities, you may remove them from the roles in the <a target=\"_blank\" href=\"%s\">User Role settings</a>."
msgstr ""

#. translators: %s is the plugins page URL
#: includes/ajax/class-secure.php:415
#, php-format
msgid "If the roles are not created via Ultimate Member > User Roles, you can use a <a href=\"%s\" target=\"_blank\">third-party plugin</a> to modify the role capability."
msgstr ""

#: includes/ajax/class-secure.php:416
msgid "We strongly recommend that you never assign roles with the same capabilities as your administrators for your members/users and that may allow them to access the admin-side features and functionalities of your WordPress site."
msgstr ""

#: includes/ajax/class-secure.php:418
msgid "Roles & Capabilities are all secured. No users are using the same capabilities as your administrators."
msgstr ""

#: includes/ajax/class-secure.php:423
msgid "We recommend that you enable and require \"Strong Password\" feature for all the Register, Reset Password & Account forms."
msgstr ""

#: includes/ajax/class-secure.php:424
msgid "Click here to enable."
msgstr ""

#: includes/ajax/class-secure.php:426
msgid "Your forms are already configured to require of using strong passwords."
msgstr ""

#: includes/ajax/class-secure.php:429
msgid "Secure Site's Connection"
msgstr ""

#: includes/ajax/class-secure.php:431
msgid "Your site cannot provide a secure connection. Please contact your hosting provider to enable SSL certifications on your server."
msgstr ""

#: includes/ajax/class-secure.php:433
msgid "Your site provides a secure connection with SSL."
msgstr ""

#: includes/ajax/class-secure.php:436
msgid "Install Challenge-Response plugin to Login & Register Forms"
msgstr ""

#: includes/ajax/class-secure.php:438
msgid "We recommend that you install and enable <a href=\"https://wordpress.org/plugins/um-recaptcha/\" target=\"_blank\">Ultimate Member - Google reCAPTCHA</a> to your Reset Password, Login & Register forms."
msgstr ""

#: includes/ajax/class-secure.php:441
msgid "Ultimate Member - Google reCAPTCHA is active."
msgstr ""

#. translators: %1$s is UM form edit link, %2$s is the UM form title
#: includes/ajax/class-secure.php:448
#, php-format
msgid "- Register: <a target=\"_blank\" href=\"%1$s\">%2$s</a> reCAPTCHA is <strong>enabled</strong>"
msgstr ""

#. translators: %1$s is UM form edit link, %2$s is the UM form title
#: includes/ajax/class-secure.php:451
#, php-format
msgid "- Register: <a target=\"_blank\" href=\"%1$s\">%2$s</a> reCAPTCHA is <strong>disabled</strong>"
msgstr ""

#. translators: %1$s is UM form edit link, %2$s is the UM form title
#: includes/ajax/class-secure.php:458
#, php-format
msgid "- Login: <a target=\"_blank\" href=\"%1$s\">%2$s</a> reCAPTCHA is <strong>enabled</strong>"
msgstr ""

#. translators: %1$s is UM form edit link, %2$s is the UM form title
#: includes/ajax/class-secure.php:461
#, php-format
msgid "- Login: <a target=\"_blank\" href=\"%1$s\">%2$s</a> reCAPTCHA is <strong>disabled</strong>"
msgstr ""

#: includes/ajax/class-secure.php:468
msgid "- Reset Password Form's Google reCAPTCHA is <strong>enabled</strong>"
msgstr ""

#: includes/ajax/class-secure.php:470
msgid "- Reset Password Form's Google reCAPTCHA is <strong>disabled</strong>"
msgstr ""

#: includes/ajax/class-secure.php:473
msgid "Ultimate Member - Google reCAPTCHA is installed but not activated."
msgstr ""

#: includes/ajax/class-secure.php:481
msgid "Keep Themes & Plugins up to date."
msgstr ""

#: includes/ajax/class-secure.php:482
msgid "It is important that you update your themes/plugins if the theme/plugin creators update is aimed at fixing security, bug and vulnerability issues. It is not a good idea to ignore available updates as this may give hackers an advantage when trying to access your website."
msgstr ""

#. translators: %d count of plugins for update
#: includes/ajax/class-secure.php:486
#, php-format
msgid "There's %d plugin that requires an update."
msgid_plural "There are %d plugins that require updates"
msgstr[0] ""
msgstr[1] ""

#. translators: %d count of plugins for update
#: includes/ajax/class-secure.php:486
msgid "Update Plugins Now"
msgstr ""

#: includes/ajax/class-secure.php:491
msgid "Plugins are up to date."
msgstr ""

#. translators: %d count of themes for update
#: includes/ajax/class-secure.php:496
#, php-format
msgid "There's %d theme that requires an update."
msgid_plural "There are %d themes that require updates"
msgstr[0] ""
msgstr[1] ""

#. translators: %d count of themes for update
#: includes/ajax/class-secure.php:496
msgid "Update Themes Now"
msgstr ""

#: includes/ajax/class-secure.php:501
msgid "Themes are up to date."
msgstr ""

#: includes/ajax/class-secure.php:505
msgid "There's a new version of WordPress."
msgstr ""

#: includes/ajax/class-secure.php:505
msgid "Update WordPress Now"
msgstr ""

#: includes/ajax/class-secure.php:507
msgid "You're using the latest version of WordPress"
msgstr ""

#: includes/ajax/class-secure.php:510
msgid "That's all. If you have any recommendation on how to secure your site or have questions, please contact us on our <a href=\"https://ultimatemember.com/feedback/\" target=\"_blank\">feedback page</a>."
msgstr ""

#: includes/class-config.php:130
#: includes/class-config.php:932
#: includes/class-config.php:992
msgid "User"
msgstr ""

#: includes/class-config.php:131
#: includes/class-config.php:241
#: includes/class-config.php:249
#: includes/class-config.php:935
#: includes/class-config.php:996
#: includes/core/class-form.php:1075
msgid "Login"
msgstr ""

#: includes/class-config.php:132
#: includes/class-config.php:239
#: includes/class-config.php:253
#: includes/class-config.php:938
#: includes/class-config.php:1000
#: includes/core/class-form.php:1081
msgid "Register"
msgstr ""

#: includes/class-config.php:134
#: includes/class-config.php:944
#: includes/class-config.php:1008
#: includes/core/class-member-directory.php:2518
#: includes/core/um-actions-profile.php:1519
#: includes/core/um-actions-user.php:19
msgid "Logout"
msgstr ""

#: includes/class-config.php:136
#: includes/class-config.php:950
#: includes/class-config.php:1016
msgid "Password Reset"
msgstr ""

#: includes/class-config.php:181
#: includes/class-config.php:258
msgid "{total_users} Members"
msgstr ""

#: includes/class-config.php:182
#: includes/class-config.php:259
msgid "{total_users} Member"
msgstr ""

#: includes/class-config.php:183
msgid "We are sorry. We cannot find any users who match your search criteria."
msgstr ""

#: includes/class-config.php:229
msgid "Update Profile"
msgstr ""

#: includes/class-config.php:284
#: includes/class-config.php:287
#: includes/class-config.php:883
#: includes/class-config.php:896
#: includes/core/class-builtin.php:796
#: includes/core/class-builtin.php:799
#: includes/core/class-member-directory.php:401
msgid "Username"
msgstr ""

#: includes/class-config.php:301
#: includes/class-config.php:304
msgid "E-mail Address"
msgstr ""

#: includes/class-config.php:334
#: includes/class-config.php:337
#: includes/core/class-builtin.php:835
#: includes/core/class-builtin.php:838
#: includes/core/class-member-directory.php:469
msgid "First Name"
msgstr ""

#: includes/class-config.php:348
#: includes/class-config.php:351
#: includes/core/class-builtin.php:845
#: includes/core/class-builtin.php:848
#: includes/core/class-member-directory.php:470
msgid "Last Name"
msgstr ""

#: includes/class-config.php:376
#: includes/class-config.php:379
msgid "Username or E-mail"
msgstr ""

#: includes/class-config.php:437
msgid "Account Welcome Email"
msgstr ""

#: includes/class-config.php:448
msgid "Whether to send the user an email when his account is automatically approved"
msgstr ""

#: includes/class-config.php:454
msgid "Account Activation Email"
msgstr ""

#: includes/class-config.php:462
msgid "Whether to send the user an email when his account needs email activation"
msgstr ""

#: includes/class-config.php:467
msgid "Your account is pending review"
msgstr ""

#: includes/class-config.php:475
msgid "Whether to send the user an email when his account needs admin review"
msgstr ""

#: includes/class-config.php:480
msgid "Account Approved Email"
msgstr ""

#: includes/class-config.php:492
msgid "Whether to send the user an email when his account is approved"
msgstr ""

#: includes/class-config.php:497
msgid "Account Rejected Email"
msgstr ""

#: includes/class-config.php:504
msgid "Whether to send the user an email when his account is rejected"
msgstr ""

#: includes/class-config.php:509
msgid "Account Deactivated Email"
msgstr ""

#: includes/class-config.php:516
msgid "Whether to send the user an email when his account is deactivated"
msgstr ""

#: includes/class-config.php:522
msgid "Account Deleted Email"
msgstr ""

#: includes/class-config.php:529
msgid "Whether to send the user an email when his account is deleted"
msgstr ""

#: includes/class-config.php:535
msgid "Password Reset Email"
msgstr ""

#: includes/class-config.php:543
msgid "Whether to send an email when users changed their password (Recommended, please keep on)"
msgstr ""

#: includes/class-config.php:549
msgid "Password Changed Email"
msgstr ""

#: includes/class-config.php:556
msgid "Whether to send the user an email when he requests to reset password (Recommended, please keep on)"
msgstr ""

#: includes/class-config.php:562
msgid "Account Updated Email"
msgstr ""

#: includes/class-config.php:569
msgid "Whether to send the user an email when he updated their account"
msgstr ""

#: includes/class-config.php:575
msgid "New User Notification"
msgstr ""

#: includes/class-config.php:581
msgid "Whether to receive notification when a new user account is created"
msgstr ""

#: includes/class-config.php:587
msgid "Account Needs Review Notification"
msgstr ""

#: includes/class-config.php:594
msgid "Whether to receive notification when an account needs admin review"
msgstr ""

#: includes/class-config.php:599
msgid "Account Deletion Notification"
msgstr ""

#: includes/class-config.php:602
msgid "Whether to receive notification when an account is deleted"
msgstr ""

#: includes/class-config.php:607
msgid "Security: Suspicious Account Activity"
msgstr ""

#: includes/class-config.php:608
msgid "[{site_name}] Suspicious Account Activity"
msgstr ""

#: includes/class-config.php:610
msgid "Whether to receive notification when suspicious account activity is detected."
msgstr ""

#: includes/class-config.php:670
msgid "Are you sure you want to delete your account? This will erase all of your account data from the site. To delete your account enter your password below."
msgstr ""

#: includes/class-config.php:671
msgid "Are you sure you want to delete your account? This will erase all of your account data from the site. To delete your account, click on the button below."
msgstr ""

#: includes/class-config.php:687
msgid "Restricted content"
msgstr ""

#: includes/class-config.php:884
msgid "First and Last Name with '.'"
msgstr ""

#: includes/class-config.php:885
msgid "First and Last Name with '-'"
msgstr ""

#: includes/class-config.php:886
msgid "First and Last Name with '+'"
msgstr ""

#: includes/class-config.php:887
msgid "User ID"
msgstr ""

#: includes/class-config.php:888
msgid "Unique hash string"
msgstr ""

#: includes/class-config.php:889
msgid "Custom usermeta"
msgstr ""

#: includes/class-config.php:894
msgid "Default WP Display Name"
msgstr ""

#: includes/class-config.php:895
#: includes/core/class-builtin.php:855
#: includes/core/class-builtin.php:858
#: includes/core/class-member-directory.php:402
#: includes/core/class-member-directory.php:471
msgid "Nickname"
msgstr ""

#: includes/class-config.php:897
msgid "First name & last name"
msgstr ""

#: includes/class-config.php:898
msgid "Last name & first name"
msgstr ""

#: includes/class-config.php:899
msgid "First name & first initial of last name"
msgstr ""

#: includes/class-config.php:900
msgid "First initial of first name & last name"
msgstr ""

#: includes/class-config.php:901
msgid "First name only"
msgstr ""

#: includes/class-config.php:902
msgid "Custom field(s)"
msgstr ""

#. translators: %1$s is an extension name; %2$s is a plugin name; %3$s is a required version.
#: includes/class-dependencies.php:202
#, php-format
msgid "When new UI is enabled this version of <strong>\"%1$s\"</strong> requires the core <strong>%2$s</strong> plugin to be <strong>%3$s</strong> or higher."
msgstr ""

#. translators: %s: plugin name.
#: includes/class-dependencies.php:205
#, php-format
msgid "Please update <strong>%s</strong> to the latest version or disable new UI."
msgstr ""

#. translators: %1$s is an extension name; %2$s is a plugin name; %3$s is a required version.
#: includes/class-dependencies.php:208
#, php-format
msgid "This version of <strong>\"%1$s\"</strong> requires the core <strong>%2$s</strong> plugin to be <strong>%3$s</strong> or higher."
msgstr ""

#. translators: %s: plugin name.
#: includes/class-dependencies.php:211
#, php-format
msgid "Please update <strong>%s</strong> to the latest version."
msgstr ""

#. translators: %1$s is a plugin name; %2$s is an extension name; %3$s is an extension version.
#: includes/class-dependencies.php:218
#, php-format
msgid "Sorry, but this version of <strong>%1$s</strong> does not work with extension <strong>\"%2$s\" %3$s</strong> version."
msgstr ""

#. translators: %s: extension name.
#: includes/class-dependencies.php:221
#, php-format
msgid "Please update extension <strong>\"%s\"</strong> to the latest version."
msgstr ""

#. translators: %1$s is an extension name; %2$s is an extension version.
#: includes/class-dependencies.php:239
#, php-format
msgid "Please check <strong>\"%1$s\" %2$s</strong> extension's folder name."
msgstr ""

#. translators: %s: extension name.
#: includes/class-dependencies.php:242
#, php-format
msgid "Correct folder name is <strong>\"%s\"</strong>"
msgstr ""

#. translators: %s: extension name.
#: includes/class-extensions.php:102
#, php-format
msgid "%s License Key"
msgstr ""

#: includes/class-init.php:167
#: includes/class-init.php:176
msgid "Cheatin&#8217; huh?"
msgstr ""

#: includes/common/class-cpt.php:33
#: includes/um-short-functions.php:664
#: includes/um-short-functions.php:702
msgid "Form"
msgstr ""

#: includes/common/class-cpt.php:35
msgid "Add New Form"
msgstr ""

#: includes/common/class-cpt.php:36
msgid "Edit Form"
msgstr ""

#: includes/common/class-cpt.php:37
msgid "You did not create any forms yet"
msgstr ""

#: includes/common/class-cpt.php:38
#: includes/common/class-cpt.php:70
msgid "Nothing found in Trash"
msgstr ""

#: includes/common/class-cpt.php:39
msgid "Search Forms"
msgstr ""

#: includes/common/class-cpt.php:65
msgid "Member Directory"
msgstr ""

#: includes/common/class-cpt.php:67
msgid "Add New Member Directory"
msgstr ""

#: includes/common/class-cpt.php:68
msgid "Edit Member Directory"
msgstr ""

#: includes/common/class-cpt.php:69
msgid "You did not create any member directories yet"
msgstr ""

#: includes/common/class-cpt.php:71
msgid "Search Member Directories"
msgstr ""

#: includes/common/class-secure.php:214
msgid "Ultimate Member Scanner"
msgstr ""

#: includes/common/class-theme.php:214
msgid "Theme version up to date"
msgstr ""

#: includes/common/class-theme.php:218
msgid "Theme version is empty"
msgstr ""

#: includes/common/class-theme.php:221
msgid "Theme version is out of date"
msgstr ""

#: includes/common/class-users.php:106
msgid "Pending administrator review"
msgstr ""

#: includes/common/class-users.php:107
msgid "Waiting email confirmation"
msgstr ""

#: includes/common/class-users.php:108
msgid "Membership inactive"
msgstr ""

#: includes/common/class-users.php:109
msgid "Membership rejected"
msgstr ""

#: includes/common/class-users.php:198
msgid "Undefined"
msgstr ""

#: includes/common/class-users.php:774
#: includes/common/class-users.php:892
#: includes/core/class-mail.php:633
msgid "Your set password"
msgstr ""

#: includes/common/class-users.php:784
#: includes/common/class-users.php:898
msgid "Login to our site"
msgstr ""

#: includes/common/class-users.php:787
#: includes/common/class-users.php:901
msgid "Set your password"
msgstr ""

#: includes/core/class-account.php:90
msgid "Update Account"
msgstr ""

#: includes/core/class-account.php:95
msgid "Change Password"
msgstr ""

#: includes/core/class-account.php:96
msgid "Update Password"
msgstr ""

#: includes/core/class-account.php:102
msgid "Update Privacy"
msgstr ""

#: includes/core/class-account.php:111
msgid "Update Notifications"
msgstr ""

#: includes/core/class-account.php:118
#: includes/core/class-account.php:119
#: includes/core/class-builtin.php:1391
#: includes/core/class-builtin.php:1394
msgid "Delete Account"
msgstr ""

#: includes/core/class-blocks.php:183
#: includes/core/class-blocks.php:226
msgid "This block cannot be used on this page"
msgstr ""

#: includes/core/class-builtin.php:163
msgid "Please provide a meta key"
msgstr ""

#: includes/core/class-builtin.php:166
msgid "Your meta key is a reserved core field and cannot be used"
msgstr ""

#: includes/core/class-builtin.php:169
msgid "Your meta key is a predefined reserved key and cannot be used"
msgstr ""

#: includes/core/class-builtin.php:172
msgid "Your meta key already exists in your fields list"
msgstr ""

#: includes/core/class-builtin.php:175
msgid "Your meta key contains illegal characters. Please correct it."
msgstr ""

#: includes/core/class-builtin.php:190
msgid "Your meta key can not be used"
msgstr ""

#: includes/core/class-builtin.php:226
msgid "Please provide a date range beginning"
msgstr ""

#: includes/core/class-builtin.php:229
msgid "Please enter a valid start date in the date range"
msgstr ""

#: includes/core/class-builtin.php:246
msgid "Please provide a date range end"
msgstr ""

#: includes/core/class-builtin.php:249
msgid "Please enter a valid end date in the date range"
msgstr ""

#: includes/core/class-builtin.php:252
msgid "The end of date range must be greater than the start of date range"
msgstr ""

#: includes/core/class-builtin.php:291
#: includes/core/class-builtin.php:307
#: includes/core/class-builtin.php:323
#: includes/core/class-builtin.php:339
#: includes/core/class-builtin.php:355
#: includes/core/class-builtin.php:375
#: includes/core/class-builtin.php:395
#: includes/core/class-builtin.php:415
#: includes/core/class-builtin.php:435
#: includes/core/class-builtin.php:451
#: includes/core/class-builtin.php:467
#: includes/core/class-builtin.php:487
#: includes/core/class-builtin.php:507
#: includes/core/class-builtin.php:533
#: includes/core/class-builtin.php:549
#: includes/core/class-builtin.php:566
#: includes/core/class-builtin.php:579
#: includes/core/class-builtin.php:596
#: includes/core/class-builtin.php:609
#: includes/core/class-builtin.php:622
#: includes/core/class-builtin.php:638
#: includes/core/class-builtin.php:654
#: includes/core/class-builtin.php:670
#: includes/core/class-builtin.php:685
#: includes/core/class-builtin.php:700
msgid "You must provide a title"
msgstr ""

#: includes/core/class-builtin.php:300
msgid "Telephone"
msgstr ""

#: includes/core/class-builtin.php:316
msgid "Number"
msgstr ""

#: includes/core/class-builtin.php:362
#: includes/core/class-builtin.php:382
#: includes/core/class-builtin.php:402
#: includes/core/class-builtin.php:422
msgid "You have not added any choices yet."
msgstr ""

#: includes/core/class-builtin.php:474
#: includes/core/class-builtin.php:494
msgid "Please enter a valid size"
msgstr ""

#: includes/core/class-builtin.php:514
msgid "Number of years is not valid"
msgstr ""

#: includes/core/class-builtin.php:583
msgid "You must add a shortcode to the content area"
msgstr ""

#: includes/core/class-builtin.php:678
#: includes/core/class-builtin.php:1627
msgid "Spotify URL"
msgstr ""

#: includes/core/class-builtin.php:693
msgid "oEmbed"
msgstr ""

#: includes/core/class-builtin.php:785
#: includes/core/class-user.php:1717
msgid "Only me"
msgstr ""

#: includes/core/class-builtin.php:809
#: includes/core/class-builtin.php:812
#: includes/core/class-builtin.php:1308
msgid "Username or Email"
msgstr ""

#: includes/core/class-builtin.php:831
msgid "Confirm Password"
msgstr ""

#: includes/core/class-builtin.php:865
#: includes/core/class-builtin.php:868
#: includes/core/class-builtin.php:1624
msgid "Website URL"
msgstr ""

#: includes/core/class-builtin.php:876
#: includes/core/class-builtin.php:879
msgid "Registration Date"
msgstr ""

#: includes/core/class-builtin.php:887
#: includes/core/class-builtin.php:890
#: includes/core/class-member-directory.php:467
msgid "Last Login"
msgstr ""

#: includes/core/class-builtin.php:898
#: includes/core/class-builtin.php:901
msgid "Email Address"
msgstr ""

#: includes/core/class-builtin.php:909
#: includes/core/class-builtin.php:912
#: includes/core/class-member-directory.php:472
msgid "Secondary Email Address"
msgstr ""

#: includes/core/class-builtin.php:921
#: includes/core/class-builtin.php:924
#: includes/core/class-member-directory.php:473
msgid "Biography"
msgstr ""

#: includes/core/class-builtin.php:930
msgid "Enter a bit about yourself..."
msgstr ""

#: includes/core/class-builtin.php:934
#: includes/core/class-builtin.php:937
msgid "Birth Date"
msgstr ""

#: includes/core/class-builtin.php:948
#: includes/core/class-builtin.php:951
#: includes/core/class-member-directory.php:463
msgid "Gender"
msgstr ""

#: includes/core/class-builtin.php:956
msgid "Male"
msgstr ""

#: includes/core/class-builtin.php:957
msgid "Female"
msgstr ""

#: includes/core/class-builtin.php:962
#: includes/core/class-builtin.php:965
#: includes/core/class-member-directory.php:462
msgid "Country"
msgstr ""

#: includes/core/class-builtin.php:966
msgid "Choose a Country"
msgstr ""

#: includes/core/class-builtin.php:974
#: includes/core/class-builtin.php:977
msgid "Facebook"
msgstr ""

#: includes/core/class-builtin.php:992
msgid "X (formerly Twitter)"
msgstr ""

#: includes/core/class-builtin.php:995
msgid "X"
msgstr ""

#: includes/core/class-builtin.php:1010
#: includes/core/class-builtin.php:1013
msgid "LinkedIn"
msgstr ""

#: includes/core/class-builtin.php:1028
#: includes/core/class-builtin.php:1031
msgid "Instagram"
msgstr ""

#: includes/core/class-builtin.php:1046
#: includes/core/class-builtin.php:1049
#: includes/core/class-builtin.php:1616
msgid "Skype ID"
msgstr ""

#: includes/core/class-builtin.php:1057
msgid "Join chat"
msgstr ""

#: includes/core/class-builtin.php:1061
#: includes/core/class-builtin.php:1064
msgid "Viber number"
msgstr ""

#: includes/core/class-builtin.php:1075
#: includes/core/class-builtin.php:1078
msgid "WhatsApp number"
msgstr ""

#: includes/core/class-builtin.php:1089
#: includes/core/class-builtin.php:1092
msgid "Telegram"
msgstr ""

#: includes/core/class-builtin.php:1107
#: includes/core/class-builtin.php:1116
msgid "Discord"
msgstr ""

#: includes/core/class-builtin.php:1110
#: includes/core/class-builtin.php:1629
msgid "Discord ID"
msgstr ""

#: includes/core/class-builtin.php:1125
#: includes/core/class-builtin.php:1128
msgid "TikTok"
msgstr ""

#: includes/core/class-builtin.php:1143
#: includes/core/class-builtin.php:1146
msgid "Twitch"
msgstr ""

#: includes/core/class-builtin.php:1161
#: includes/core/class-builtin.php:1164
msgid "Reddit"
msgstr ""

#: includes/core/class-builtin.php:1179
#: includes/core/class-builtin.php:1182
#: includes/core/class-builtin.php:1190
msgid "YouTube"
msgstr ""

#: includes/core/class-builtin.php:1200
#: includes/core/class-builtin.php:1203
msgid "SoundCloud"
msgstr ""

#: includes/core/class-builtin.php:1218
msgid "Roles (Dropdown)"
msgstr ""

#: includes/core/class-builtin.php:1221
#: includes/core/class-builtin.php:1233
msgid "Account Type"
msgstr ""

#: includes/core/class-builtin.php:1222
msgid "Choose account type"
msgstr ""

#: includes/core/class-builtin.php:1230
msgid "Roles (Radio)"
msgstr ""

#: includes/core/class-builtin.php:1241
#: includes/core/class-member-directory.php:464
msgid "Languages"
msgstr ""

#: includes/core/class-builtin.php:1244
msgid "Languages Spoken"
msgstr ""

#: includes/core/class-builtin.php:1245
msgid "Select languages"
msgstr ""

#: includes/core/class-builtin.php:1253
#: includes/core/class-builtin.php:1256
#: includes/core/class-builtin.php:1615
#: includes/core/class-member-directory.php:474
msgid "Phone Number"
msgstr ""

#: includes/core/class-builtin.php:1265
#: includes/core/class-builtin.php:1268
#: includes/core/class-member-directory.php:475
msgid "Mobile Number"
msgstr ""

#: includes/core/class-builtin.php:1279
msgid "Profile Photo"
msgstr ""

#: includes/core/class-builtin.php:1282
msgid "Change your profile photo"
msgstr ""

#: includes/core/class-builtin.php:1283
msgid "Upload your photo here"
msgstr ""

#: includes/core/class-builtin.php:1293
msgid "Cover Photo"
msgstr ""

#: includes/core/class-builtin.php:1296
#: includes/core/um-actions-profile.php:978
msgid "Change your cover photo"
msgstr ""

#: includes/core/class-builtin.php:1297
msgid "Upload profile cover here"
msgstr ""

#: includes/core/class-builtin.php:1311
msgid "Enter your username or email"
msgstr ""

#: includes/core/class-builtin.php:1321
#: includes/core/class-builtin.php:1324
msgid "Profile Privacy"
msgstr ""

#: includes/core/class-builtin.php:1325
msgid "Who can see your public profile?"
msgstr ""

#: includes/core/class-builtin.php:1337
#: includes/core/class-builtin.php:1340
msgid "Avoid indexing my profile by search engines"
msgstr ""

#: includes/core/class-builtin.php:1341
msgid "Hide my profile for robots?"
msgstr ""

#: includes/core/class-builtin.php:1356
#: includes/core/class-builtin.php:1359
msgid "Hide my profile from directory"
msgstr ""

#: includes/core/class-builtin.php:1360
msgid "Here you can hide yourself from appearing in public directory"
msgstr ""

#: includes/core/class-builtin.php:1374
#: includes/core/class-builtin.php:1377
msgid "Show my last login?"
msgstr ""

#: includes/core/class-builtin.php:1378
msgid "Here you can hide last login field on profile page and card in member directory"
msgstr ""

#: includes/core/class-builtin.php:1395
msgid "If you confirm, everything related to your profile will be deleted permanently from the site"
msgstr ""

#: includes/core/class-builtin.php:1607
msgid "Alphabetic value only"
msgstr ""

#: includes/core/class-builtin.php:1608
msgid "Alpha-numeric value"
msgstr ""

#: includes/core/class-builtin.php:1609
msgid "English letters only"
msgstr ""

#: includes/core/class-builtin.php:1610
msgid "Facebook URL"
msgstr ""

#: includes/core/class-builtin.php:1611
msgid "Instagram URL"
msgstr ""

#: includes/core/class-builtin.php:1612
msgid "LinkedIn URL"
msgstr ""

#: includes/core/class-builtin.php:1613
msgid "Lowercase only"
msgstr ""

#: includes/core/class-builtin.php:1614
msgid "Numeric value only"
msgstr ""

#: includes/core/class-builtin.php:1617
msgid "SoundCloud Profile"
msgstr ""

#: includes/core/class-builtin.php:1618
msgid "X (formerly Twitter) URL"
msgstr ""

#: includes/core/class-builtin.php:1619
msgid "Email( Not Unique )"
msgstr ""

#: includes/core/class-builtin.php:1620
msgid "Unique Email"
msgstr ""

#: includes/core/class-builtin.php:1621
msgid "Unique Metakey value"
msgstr ""

#: includes/core/class-builtin.php:1622
msgid "Unique Username"
msgstr ""

#: includes/core/class-builtin.php:1623
msgid "Unique Username/Email"
msgstr ""

#: includes/core/class-builtin.php:1625
msgid "YouTube Profile"
msgstr ""

#: includes/core/class-builtin.php:1626
msgid "YouTube Video"
msgstr ""

#: includes/core/class-builtin.php:1628
msgid "Telegram URL"
msgstr ""

#: includes/core/class-builtin.php:1630
msgid "TikTok URL"
msgstr ""

#: includes/core/class-builtin.php:1631
msgid "Twitch URL"
msgstr ""

#: includes/core/class-builtin.php:1632
msgid "Reddit URL"
msgstr ""

#: includes/core/class-builtin.php:1633
msgid "Custom Validation"
msgstr ""

#: includes/core/class-builtin.php:1672
msgid "Afar"
msgstr ""

#: includes/core/class-builtin.php:1673
msgid "Abkhazian"
msgstr ""

#: includes/core/class-builtin.php:1674
msgid "Avestan"
msgstr ""

#: includes/core/class-builtin.php:1675
msgid "Afrikaans"
msgstr ""

#: includes/core/class-builtin.php:1676
msgid "Akan"
msgstr ""

#: includes/core/class-builtin.php:1677
msgid "Amharic"
msgstr ""

#: includes/core/class-builtin.php:1678
msgid "Aragonese"
msgstr ""

#: includes/core/class-builtin.php:1679
msgid "Arabic"
msgstr ""

#: includes/core/class-builtin.php:1680
msgid "Assamese"
msgstr ""

#: includes/core/class-builtin.php:1681
msgid "Avaric"
msgstr ""

#: includes/core/class-builtin.php:1682
msgid "Aymara"
msgstr ""

#: includes/core/class-builtin.php:1683
msgid "Azerbaijani"
msgstr ""

#: includes/core/class-builtin.php:1684
msgid "Bashkir"
msgstr ""

#: includes/core/class-builtin.php:1685
msgid "Belarusian"
msgstr ""

#: includes/core/class-builtin.php:1686
msgid "Bulgarian"
msgstr ""

#: includes/core/class-builtin.php:1687
msgid "Bihari"
msgstr ""

#: includes/core/class-builtin.php:1688
msgid "Bislama"
msgstr ""

#: includes/core/class-builtin.php:1689
msgid "Bambara"
msgstr ""

#: includes/core/class-builtin.php:1690
msgid "Bengali"
msgstr ""

#: includes/core/class-builtin.php:1691
msgid "Tibetan"
msgstr ""

#: includes/core/class-builtin.php:1692
msgid "Breton"
msgstr ""

#: includes/core/class-builtin.php:1693
msgid "Bosnian"
msgstr ""

#: includes/core/class-builtin.php:1694
msgid "Catalan"
msgstr ""

#: includes/core/class-builtin.php:1695
msgid "Chechen"
msgstr ""

#: includes/core/class-builtin.php:1696
msgid "Chamorro"
msgstr ""

#: includes/core/class-builtin.php:1697
msgid "Corsican"
msgstr ""

#: includes/core/class-builtin.php:1698
msgid "Cree"
msgstr ""

#: includes/core/class-builtin.php:1699
msgid "Czech"
msgstr ""

#: includes/core/class-builtin.php:1700
msgid "Church Slavic"
msgstr ""

#: includes/core/class-builtin.php:1701
msgid "Chuvash"
msgstr ""

#: includes/core/class-builtin.php:1702
msgid "Welsh"
msgstr ""

#: includes/core/class-builtin.php:1703
msgid "Danish"
msgstr ""

#: includes/core/class-builtin.php:1704
msgid "German"
msgstr ""

#: includes/core/class-builtin.php:1705
msgid "Divehi"
msgstr ""

#: includes/core/class-builtin.php:1706
msgid "Dzongkha"
msgstr ""

#: includes/core/class-builtin.php:1707
msgid "Ewe"
msgstr ""

#: includes/core/class-builtin.php:1708
msgid "Greek"
msgstr ""

#: includes/core/class-builtin.php:1709
msgid "English"
msgstr ""

#: includes/core/class-builtin.php:1710
msgid "Esperanto"
msgstr ""

#: includes/core/class-builtin.php:1711
msgid "Spanish"
msgstr ""

#: includes/core/class-builtin.php:1712
msgid "Estonian"
msgstr ""

#: includes/core/class-builtin.php:1713
msgid "Basque"
msgstr ""

#: includes/core/class-builtin.php:1714
msgid "Persian"
msgstr ""

#: includes/core/class-builtin.php:1715
msgid "Fulah"
msgstr ""

#: includes/core/class-builtin.php:1716
msgid "Finnish"
msgstr ""

#: includes/core/class-builtin.php:1717
msgid "Fijian"
msgstr ""

#: includes/core/class-builtin.php:1718
msgid "Faroese"
msgstr ""

#: includes/core/class-builtin.php:1719
msgid "French"
msgstr ""

#: includes/core/class-builtin.php:1720
msgid "Western Frisian"
msgstr ""

#: includes/core/class-builtin.php:1721
msgid "Irish"
msgstr ""

#: includes/core/class-builtin.php:1722
msgid "Scottish Gaelic"
msgstr ""

#: includes/core/class-builtin.php:1723
msgid "Galician"
msgstr ""

#: includes/core/class-builtin.php:1724
msgid "Guarani"
msgstr ""

#: includes/core/class-builtin.php:1725
msgid "Gujarati"
msgstr ""

#: includes/core/class-builtin.php:1726
msgid "Manx"
msgstr ""

#: includes/core/class-builtin.php:1727
msgid "Hausa"
msgstr ""

#: includes/core/class-builtin.php:1728
msgid "Hebrew"
msgstr ""

#: includes/core/class-builtin.php:1729
msgid "Hindi"
msgstr ""

#: includes/core/class-builtin.php:1730
msgid "Hiri Motu"
msgstr ""

#: includes/core/class-builtin.php:1731
msgid "Croatian"
msgstr ""

#: includes/core/class-builtin.php:1732
msgid "Haitian"
msgstr ""

#: includes/core/class-builtin.php:1733
msgid "Hungarian"
msgstr ""

#: includes/core/class-builtin.php:1734
msgid "Armenian"
msgstr ""

#: includes/core/class-builtin.php:1735
msgid "Herero"
msgstr ""

#: includes/core/class-builtin.php:1736
msgid "Interlingua (International Auxiliary Language Association)"
msgstr ""

#: includes/core/class-builtin.php:1737
msgid "Indonesian"
msgstr ""

#: includes/core/class-builtin.php:1738
msgid "Interlingue"
msgstr ""

#: includes/core/class-builtin.php:1739
msgid "Igbo"
msgstr ""

#: includes/core/class-builtin.php:1740
msgid "Sichuan Yi"
msgstr ""

#: includes/core/class-builtin.php:1741
msgid "Inupiaq"
msgstr ""

#: includes/core/class-builtin.php:1742
msgid "Ido"
msgstr ""

#: includes/core/class-builtin.php:1743
msgid "Icelandic"
msgstr ""

#: includes/core/class-builtin.php:1744
msgid "Italian"
msgstr ""

#: includes/core/class-builtin.php:1745
msgid "Inuktitut"
msgstr ""

#: includes/core/class-builtin.php:1746
msgid "Japanese"
msgstr ""

#: includes/core/class-builtin.php:1747
msgid "Javanese"
msgstr ""

#: includes/core/class-builtin.php:1748
msgid "Kartuli"
msgstr ""

#: includes/core/class-builtin.php:1749
msgid "Kongo"
msgstr ""

#: includes/core/class-builtin.php:1750
msgid "Kikuyu"
msgstr ""

#: includes/core/class-builtin.php:1751
msgid "Kwanyama"
msgstr ""

#: includes/core/class-builtin.php:1752
msgid "Kazakh"
msgstr ""

#: includes/core/class-builtin.php:1753
msgid "Kalaallisut"
msgstr ""

#: includes/core/class-builtin.php:1754
msgid "Khmer"
msgstr ""

#: includes/core/class-builtin.php:1755
msgid "Kannada"
msgstr ""

#: includes/core/class-builtin.php:1756
msgid "Korean"
msgstr ""

#: includes/core/class-builtin.php:1757
msgid "Kanuri"
msgstr ""

#: includes/core/class-builtin.php:1758
msgid "Kashmiri"
msgstr ""

#: includes/core/class-builtin.php:1759
msgid "Kurdish"
msgstr ""

#: includes/core/class-builtin.php:1760
msgid "Komi"
msgstr ""

#: includes/core/class-builtin.php:1761
msgid "Cornish"
msgstr ""

#: includes/core/class-builtin.php:1762
msgid "Kirghiz"
msgstr ""

#: includes/core/class-builtin.php:1763
msgid "Latin"
msgstr ""

#: includes/core/class-builtin.php:1764
msgid "Luxembourgish"
msgstr ""

#: includes/core/class-builtin.php:1765
msgid "Ganda"
msgstr ""

#: includes/core/class-builtin.php:1766
msgid "Limburgish"
msgstr ""

#: includes/core/class-builtin.php:1767
msgid "Lingala"
msgstr ""

#: includes/core/class-builtin.php:1768
msgid "Lao"
msgstr ""

#: includes/core/class-builtin.php:1769
msgid "Lithuanian"
msgstr ""

#: includes/core/class-builtin.php:1770
msgid "Luba-Katanga"
msgstr ""

#: includes/core/class-builtin.php:1771
msgid "Latvian"
msgstr ""

#: includes/core/class-builtin.php:1772
msgid "Malagasy"
msgstr ""

#: includes/core/class-builtin.php:1773
msgid "Marshallese"
msgstr ""

#: includes/core/class-builtin.php:1774
msgid "Maori"
msgstr ""

#: includes/core/class-builtin.php:1775
msgid "Macedonian"
msgstr ""

#: includes/core/class-builtin.php:1776
msgid "Malayalam"
msgstr ""

#: includes/core/class-builtin.php:1777
msgid "Mongolian"
msgstr ""

#: includes/core/class-builtin.php:1778
msgid "Marathi"
msgstr ""

#: includes/core/class-builtin.php:1779
msgid "Malay"
msgstr ""

#: includes/core/class-builtin.php:1780
msgid "Maltese"
msgstr ""

#: includes/core/class-builtin.php:1781
msgid "Burmese"
msgstr ""

#: includes/core/class-builtin.php:1782
#: includes/core/class-builtin.php:2013
msgid "Nauru"
msgstr ""

#: includes/core/class-builtin.php:1783
msgid "Norwegian Bokmal"
msgstr ""

#: includes/core/class-builtin.php:1784
msgid "North Ndebele"
msgstr ""

#: includes/core/class-builtin.php:1785
msgid "Nepali"
msgstr ""

#: includes/core/class-builtin.php:1786
msgid "Ndonga"
msgstr ""

#: includes/core/class-builtin.php:1787
msgid "Dutch"
msgstr ""

#: includes/core/class-builtin.php:1788
msgid "Norwegian Nynorsk"
msgstr ""

#: includes/core/class-builtin.php:1789
msgid "Norwegian"
msgstr ""

#: includes/core/class-builtin.php:1790
msgid "South Ndebele"
msgstr ""

#: includes/core/class-builtin.php:1791
msgid "Navajo"
msgstr ""

#: includes/core/class-builtin.php:1792
msgid "Chichewa"
msgstr ""

#: includes/core/class-builtin.php:1793
msgid "Occitan"
msgstr ""

#: includes/core/class-builtin.php:1794
msgid "Ojibwa"
msgstr ""

#: includes/core/class-builtin.php:1795
msgid "Oromo"
msgstr ""

#: includes/core/class-builtin.php:1796
msgid "Oriya"
msgstr ""

#: includes/core/class-builtin.php:1797
msgid "Ossetian"
msgstr ""

#: includes/core/class-builtin.php:1798
msgid "Panjabi"
msgstr ""

#: includes/core/class-builtin.php:1799
msgid "Pali"
msgstr ""

#: includes/core/class-builtin.php:1800
msgid "Polish"
msgstr ""

#: includes/core/class-builtin.php:1801
msgid "Pashto"
msgstr ""

#: includes/core/class-builtin.php:1802
msgid "Portuguese"
msgstr ""

#: includes/core/class-builtin.php:1803
msgid "Quechua"
msgstr ""

#: includes/core/class-builtin.php:1804
msgid "Raeto-Romance"
msgstr ""

#: includes/core/class-builtin.php:1805
msgid "Kirundi"
msgstr ""

#: includes/core/class-builtin.php:1806
msgid "Romanian"
msgstr ""

#: includes/core/class-builtin.php:1807
msgid "Russian"
msgstr ""

#: includes/core/class-builtin.php:1808
msgid "Kinyarwanda"
msgstr ""

#: includes/core/class-builtin.php:1809
msgid "Sanskrit"
msgstr ""

#: includes/core/class-builtin.php:1810
msgid "Sardinian"
msgstr ""

#: includes/core/class-builtin.php:1811
msgid "Sindhi"
msgstr ""

#: includes/core/class-builtin.php:1812
msgid "Northern Sami"
msgstr ""

#: includes/core/class-builtin.php:1813
msgid "Sango"
msgstr ""

#: includes/core/class-builtin.php:1814
msgid "Sinhala"
msgstr ""

#: includes/core/class-builtin.php:1815
msgid "Slovak"
msgstr ""

#: includes/core/class-builtin.php:1816
msgid "Slovenian"
msgstr ""

#: includes/core/class-builtin.php:1817
msgid "Samoan"
msgstr ""

#: includes/core/class-builtin.php:1818
msgid "Shona"
msgstr ""

#: includes/core/class-builtin.php:1819
msgid "Somali"
msgstr ""

#: includes/core/class-builtin.php:1820
msgid "Albanian"
msgstr ""

#: includes/core/class-builtin.php:1821
msgid "Serbian"
msgstr ""

#: includes/core/class-builtin.php:1822
msgid "Swati"
msgstr ""

#: includes/core/class-builtin.php:1823
msgid "Southern Sotho"
msgstr ""

#: includes/core/class-builtin.php:1824
msgid "Sundanese"
msgstr ""

#: includes/core/class-builtin.php:1825
msgid "Swedish"
msgstr ""

#: includes/core/class-builtin.php:1826
msgid "Swahili"
msgstr ""

#: includes/core/class-builtin.php:1827
msgid "Tamil"
msgstr ""

#: includes/core/class-builtin.php:1828
msgid "Telugu"
msgstr ""

#: includes/core/class-builtin.php:1829
msgid "Tajik"
msgstr ""

#: includes/core/class-builtin.php:1830
msgid "Thai"
msgstr ""

#: includes/core/class-builtin.php:1831
msgid "Tigrinya"
msgstr ""

#: includes/core/class-builtin.php:1832
msgid "Turkmen"
msgstr ""

#: includes/core/class-builtin.php:1833
msgid "Tagalog"
msgstr ""

#: includes/core/class-builtin.php:1834
msgid "Tswana"
msgstr ""

#: includes/core/class-builtin.php:1835
#: includes/core/class-builtin.php:2083
msgid "Tonga"
msgstr ""

#: includes/core/class-builtin.php:1836
msgid "Turkish"
msgstr ""

#: includes/core/class-builtin.php:1837
msgid "Tsonga"
msgstr ""

#: includes/core/class-builtin.php:1838
msgid "Tatar"
msgstr ""

#: includes/core/class-builtin.php:1839
msgid "Twi"
msgstr ""

#: includes/core/class-builtin.php:1840
msgid "Tahitian"
msgstr ""

#: includes/core/class-builtin.php:1841
msgid "Uighur"
msgstr ""

#: includes/core/class-builtin.php:1842
msgid "Ukrainian"
msgstr ""

#: includes/core/class-builtin.php:1843
msgid "Urdu"
msgstr ""

#: includes/core/class-builtin.php:1844
msgid "Uzbek"
msgstr ""

#: includes/core/class-builtin.php:1845
msgid "Venda"
msgstr ""

#: includes/core/class-builtin.php:1846
msgid "Vietnamese"
msgstr ""

#: includes/core/class-builtin.php:1847
msgid "Volapuk"
msgstr ""

#: includes/core/class-builtin.php:1848
msgid "Walloon"
msgstr ""

#: includes/core/class-builtin.php:1849
msgid "Wolof"
msgstr ""

#: includes/core/class-builtin.php:1850
msgid "Xhosa"
msgstr ""

#: includes/core/class-builtin.php:1851
msgid "Yiddish"
msgstr ""

#: includes/core/class-builtin.php:1852
msgid "Yoruba"
msgstr ""

#: includes/core/class-builtin.php:1853
msgid "Zhuang"
msgstr ""

#: includes/core/class-builtin.php:1854
msgid "Chinese"
msgstr ""

#: includes/core/class-builtin.php:1855
msgid "Zulu"
msgstr ""

#: includes/core/class-builtin.php:1861
msgid "Afghanistan"
msgstr ""

#: includes/core/class-builtin.php:1862
msgid "Åland Islands"
msgstr ""

#: includes/core/class-builtin.php:1863
msgid "Albania"
msgstr ""

#: includes/core/class-builtin.php:1864
msgid "Algeria"
msgstr ""

#: includes/core/class-builtin.php:1865
msgid "American Samoa"
msgstr ""

#: includes/core/class-builtin.php:1866
msgid "Andorra"
msgstr ""

#: includes/core/class-builtin.php:1867
msgid "Angola"
msgstr ""

#: includes/core/class-builtin.php:1868
msgid "Anguilla"
msgstr ""

#: includes/core/class-builtin.php:1869
msgid "Antarctica"
msgstr ""

#: includes/core/class-builtin.php:1870
msgid "Antigua and Barbuda"
msgstr ""

#: includes/core/class-builtin.php:1871
msgid "Argentina"
msgstr ""

#: includes/core/class-builtin.php:1872
msgid "Armenia"
msgstr ""

#: includes/core/class-builtin.php:1873
msgid "Aruba"
msgstr ""

#: includes/core/class-builtin.php:1874
msgid "Australia"
msgstr ""

#: includes/core/class-builtin.php:1875
msgid "Austria"
msgstr ""

#: includes/core/class-builtin.php:1876
msgid "Azerbaijan"
msgstr ""

#: includes/core/class-builtin.php:1877
msgid "Bahamas"
msgstr ""

#: includes/core/class-builtin.php:1878
msgid "Bahrain"
msgstr ""

#: includes/core/class-builtin.php:1879
msgid "Bangladesh"
msgstr ""

#: includes/core/class-builtin.php:1880
msgid "Barbados"
msgstr ""

#: includes/core/class-builtin.php:1881
msgid "Belarus"
msgstr ""

#: includes/core/class-builtin.php:1882
msgid "Belgium"
msgstr ""

#: includes/core/class-builtin.php:1883
msgid "Belize"
msgstr ""

#: includes/core/class-builtin.php:1884
msgid "Benin"
msgstr ""

#: includes/core/class-builtin.php:1885
msgid "Bermuda"
msgstr ""

#: includes/core/class-builtin.php:1886
msgid "Bhutan"
msgstr ""

#: includes/core/class-builtin.php:1887
msgid "Bolivia, Plurinational State of"
msgstr ""

#: includes/core/class-builtin.php:1888
msgid "Bosnia and Herzegovina"
msgstr ""

#: includes/core/class-builtin.php:1889
msgid "Botswana"
msgstr ""

#: includes/core/class-builtin.php:1890
msgid "Bouvet Island"
msgstr ""

#: includes/core/class-builtin.php:1891
msgid "Brazil"
msgstr ""

#: includes/core/class-builtin.php:1892
msgid "British Indian Ocean Territory"
msgstr ""

#: includes/core/class-builtin.php:1893
msgid "Brunei Darussalam"
msgstr ""

#: includes/core/class-builtin.php:1894
msgid "Bulgaria"
msgstr ""

#: includes/core/class-builtin.php:1895
msgid "Burkina Faso"
msgstr ""

#: includes/core/class-builtin.php:1896
msgid "Burundi"
msgstr ""

#: includes/core/class-builtin.php:1897
msgid "Cambodia"
msgstr ""

#: includes/core/class-builtin.php:1898
msgid "Cameroon"
msgstr ""

#: includes/core/class-builtin.php:1899
msgid "Canada"
msgstr ""

#: includes/core/class-builtin.php:1900
msgid "Cape Verde"
msgstr ""

#: includes/core/class-builtin.php:1901
msgid "Cayman Islands"
msgstr ""

#: includes/core/class-builtin.php:1902
msgid "Central African Republic"
msgstr ""

#: includes/core/class-builtin.php:1903
msgid "Chad"
msgstr ""

#: includes/core/class-builtin.php:1904
msgid "Chile"
msgstr ""

#: includes/core/class-builtin.php:1905
msgid "China"
msgstr ""

#: includes/core/class-builtin.php:1906
msgid "Christmas Island"
msgstr ""

#: includes/core/class-builtin.php:1907
msgid "Cocos (Keeling) Islands"
msgstr ""

#: includes/core/class-builtin.php:1908
msgid "Colombia"
msgstr ""

#: includes/core/class-builtin.php:1909
msgid "Comoros"
msgstr ""

#: includes/core/class-builtin.php:1910
msgid "Congo"
msgstr ""

#: includes/core/class-builtin.php:1911
msgid "Congo, the Democratic Republic of the"
msgstr ""

#: includes/core/class-builtin.php:1912
msgid "Cook Islands"
msgstr ""

#: includes/core/class-builtin.php:1913
msgid "Costa Rica"
msgstr ""

#: includes/core/class-builtin.php:1914
msgid "Côte d'Ivoire"
msgstr ""

#: includes/core/class-builtin.php:1915
msgid "Croatia"
msgstr ""

#: includes/core/class-builtin.php:1916
msgid "Cuba"
msgstr ""

#: includes/core/class-builtin.php:1917
msgid "Cyprus"
msgstr ""

#: includes/core/class-builtin.php:1918
msgid "Czech Republic"
msgstr ""

#: includes/core/class-builtin.php:1919
msgid "Denmark"
msgstr ""

#: includes/core/class-builtin.php:1920
msgid "Djibouti"
msgstr ""

#: includes/core/class-builtin.php:1921
msgid "Dominica"
msgstr ""

#: includes/core/class-builtin.php:1922
msgid "Dominican Republic"
msgstr ""

#: includes/core/class-builtin.php:1923
msgid "Ecuador"
msgstr ""

#: includes/core/class-builtin.php:1924
msgid "Egypt"
msgstr ""

#: includes/core/class-builtin.php:1925
msgid "El Salvador"
msgstr ""

#: includes/core/class-builtin.php:1926
msgid "Equatorial Guinea"
msgstr ""

#: includes/core/class-builtin.php:1927
msgid "Eritrea"
msgstr ""

#: includes/core/class-builtin.php:1928
msgid "Estonia"
msgstr ""

#: includes/core/class-builtin.php:1929
msgid "Ethiopia"
msgstr ""

#: includes/core/class-builtin.php:1930
msgid "Falkland Islands (Malvinas)"
msgstr ""

#: includes/core/class-builtin.php:1931
msgid "Faroe Islands"
msgstr ""

#: includes/core/class-builtin.php:1932
msgid "Fiji"
msgstr ""

#: includes/core/class-builtin.php:1933
msgid "Finland"
msgstr ""

#: includes/core/class-builtin.php:1934
msgid "France"
msgstr ""

#: includes/core/class-builtin.php:1935
msgid "French Guiana"
msgstr ""

#: includes/core/class-builtin.php:1936
msgid "French Polynesia"
msgstr ""

#: includes/core/class-builtin.php:1937
msgid "French Southern Territories"
msgstr ""

#: includes/core/class-builtin.php:1938
msgid "Gabon"
msgstr ""

#: includes/core/class-builtin.php:1939
msgid "Gambia"
msgstr ""

#: includes/core/class-builtin.php:1940
msgid "Sakartvelo"
msgstr ""

#: includes/core/class-builtin.php:1941
msgid "Germany"
msgstr ""

#: includes/core/class-builtin.php:1942
msgid "Ghana"
msgstr ""

#: includes/core/class-builtin.php:1943
msgid "Gibraltar"
msgstr ""

#: includes/core/class-builtin.php:1944
msgid "Greece"
msgstr ""

#: includes/core/class-builtin.php:1945
msgid "Greenland"
msgstr ""

#: includes/core/class-builtin.php:1946
msgid "Grenada"
msgstr ""

#: includes/core/class-builtin.php:1947
msgid "Guadeloupe"
msgstr ""

#: includes/core/class-builtin.php:1948
msgid "Guam"
msgstr ""

#: includes/core/class-builtin.php:1949
msgid "Guatemala"
msgstr ""

#: includes/core/class-builtin.php:1950
msgid "Guernsey"
msgstr ""

#: includes/core/class-builtin.php:1951
msgid "Guinea"
msgstr ""

#: includes/core/class-builtin.php:1952
msgid "Guinea-Bissau"
msgstr ""

#: includes/core/class-builtin.php:1953
msgid "Guyana"
msgstr ""

#: includes/core/class-builtin.php:1954
msgid "Haiti"
msgstr ""

#: includes/core/class-builtin.php:1955
msgid "Heard Island and McDonald Islands"
msgstr ""

#: includes/core/class-builtin.php:1956
msgid "Holy See (Vatican City State)"
msgstr ""

#: includes/core/class-builtin.php:1957
msgid "Honduras"
msgstr ""

#: includes/core/class-builtin.php:1958
msgid "Hong Kong"
msgstr ""

#: includes/core/class-builtin.php:1959
msgid "Hungary"
msgstr ""

#: includes/core/class-builtin.php:1960
msgid "Iceland"
msgstr ""

#: includes/core/class-builtin.php:1961
msgid "India"
msgstr ""

#: includes/core/class-builtin.php:1962
msgid "Indonesia"
msgstr ""

#: includes/core/class-builtin.php:1963
msgid "Iran, Islamic Republic of"
msgstr ""

#: includes/core/class-builtin.php:1964
msgid "Iraq"
msgstr ""

#: includes/core/class-builtin.php:1965
msgid "Ireland"
msgstr ""

#: includes/core/class-builtin.php:1966
msgid "Isle of Man"
msgstr ""

#: includes/core/class-builtin.php:1967
msgid "Israel"
msgstr ""

#: includes/core/class-builtin.php:1968
msgid "Italy"
msgstr ""

#: includes/core/class-builtin.php:1969
msgid "Jamaica"
msgstr ""

#: includes/core/class-builtin.php:1970
msgid "Japan"
msgstr ""

#: includes/core/class-builtin.php:1971
msgid "Jersey"
msgstr ""

#: includes/core/class-builtin.php:1972
msgid "Jordan"
msgstr ""

#: includes/core/class-builtin.php:1973
msgid "Kazakhstan"
msgstr ""

#: includes/core/class-builtin.php:1974
msgid "Kenya"
msgstr ""

#: includes/core/class-builtin.php:1975
msgid "Kiribati"
msgstr ""

#: includes/core/class-builtin.php:1976
msgid "Korea, Democratic People's Republic of"
msgstr ""

#: includes/core/class-builtin.php:1977
msgid "Korea, Republic of"
msgstr ""

#: includes/core/class-builtin.php:1978
msgid "Kuwait"
msgstr ""

#: includes/core/class-builtin.php:1979
msgid "Kyrgyzstan"
msgstr ""

#: includes/core/class-builtin.php:1980
msgid "Lao People's Democratic Republic"
msgstr ""

#: includes/core/class-builtin.php:1981
msgid "Latvia"
msgstr ""

#: includes/core/class-builtin.php:1982
msgid "Lebanon"
msgstr ""

#: includes/core/class-builtin.php:1983
msgid "Lesotho"
msgstr ""

#: includes/core/class-builtin.php:1984
msgid "Liberia"
msgstr ""

#: includes/core/class-builtin.php:1985
msgid "Libyan Arab Jamahiriya"
msgstr ""

#: includes/core/class-builtin.php:1986
msgid "Liechtenstein"
msgstr ""

#: includes/core/class-builtin.php:1987
msgid "Lithuania"
msgstr ""

#: includes/core/class-builtin.php:1988
msgid "Luxembourg"
msgstr ""

#: includes/core/class-builtin.php:1989
msgid "Macao"
msgstr ""

#: includes/core/class-builtin.php:1990
msgid "North Macedonia"
msgstr ""

#: includes/core/class-builtin.php:1991
msgid "Madagascar"
msgstr ""

#: includes/core/class-builtin.php:1992
msgid "Malawi"
msgstr ""

#: includes/core/class-builtin.php:1993
msgid "Malaysia"
msgstr ""

#: includes/core/class-builtin.php:1994
msgid "Maldives"
msgstr ""

#: includes/core/class-builtin.php:1995
msgid "Mali"
msgstr ""

#: includes/core/class-builtin.php:1996
msgid "Malta"
msgstr ""

#: includes/core/class-builtin.php:1997
msgid "Marshall Islands"
msgstr ""

#: includes/core/class-builtin.php:1998
msgid "Martinique"
msgstr ""

#: includes/core/class-builtin.php:1999
msgid "Mauritania"
msgstr ""

#: includes/core/class-builtin.php:2000
msgid "Mauritius"
msgstr ""

#: includes/core/class-builtin.php:2001
msgid "Mayotte"
msgstr ""

#: includes/core/class-builtin.php:2002
msgid "Mexico"
msgstr ""

#: includes/core/class-builtin.php:2003
msgid "Micronesia, Federated States of"
msgstr ""

#: includes/core/class-builtin.php:2004
msgid "Moldova, Republic of"
msgstr ""

#: includes/core/class-builtin.php:2005
msgid "Monaco"
msgstr ""

#: includes/core/class-builtin.php:2006
msgid "Mongolia"
msgstr ""

#: includes/core/class-builtin.php:2007
msgid "Montenegro"
msgstr ""

#: includes/core/class-builtin.php:2008
msgid "Montserrat"
msgstr ""

#: includes/core/class-builtin.php:2009
msgid "Morocco"
msgstr ""

#: includes/core/class-builtin.php:2010
msgid "Mozambique"
msgstr ""

#: includes/core/class-builtin.php:2011
msgid "Myanmar"
msgstr ""

#: includes/core/class-builtin.php:2012
msgid "Namibia"
msgstr ""

#: includes/core/class-builtin.php:2014
msgid "Nepal"
msgstr ""

#: includes/core/class-builtin.php:2015
msgid "Netherlands"
msgstr ""

#: includes/core/class-builtin.php:2016
msgid "Netherlands Antilles"
msgstr ""

#: includes/core/class-builtin.php:2017
msgid "New Caledonia"
msgstr ""

#: includes/core/class-builtin.php:2018
msgid "New Zealand"
msgstr ""

#: includes/core/class-builtin.php:2019
msgid "Nicaragua"
msgstr ""

#: includes/core/class-builtin.php:2020
msgid "Niger"
msgstr ""

#: includes/core/class-builtin.php:2021
msgid "Nigeria"
msgstr ""

#: includes/core/class-builtin.php:2022
msgid "Niue"
msgstr ""

#: includes/core/class-builtin.php:2023
msgid "Norfolk Island"
msgstr ""

#: includes/core/class-builtin.php:2024
msgid "Northern Mariana Islands"
msgstr ""

#: includes/core/class-builtin.php:2025
msgid "Norway"
msgstr ""

#: includes/core/class-builtin.php:2026
msgid "Oman"
msgstr ""

#: includes/core/class-builtin.php:2027
msgid "Pakistan"
msgstr ""

#: includes/core/class-builtin.php:2028
msgid "Palau"
msgstr ""

#: includes/core/class-builtin.php:2029
msgid "Palestine"
msgstr ""

#: includes/core/class-builtin.php:2030
msgid "Panama"
msgstr ""

#: includes/core/class-builtin.php:2031
msgid "Papua New Guinea"
msgstr ""

#: includes/core/class-builtin.php:2032
msgid "Paraguay"
msgstr ""

#: includes/core/class-builtin.php:2033
msgid "Peru"
msgstr ""

#: includes/core/class-builtin.php:2034
msgid "Philippines"
msgstr ""

#: includes/core/class-builtin.php:2035
msgid "Pitcairn"
msgstr ""

#: includes/core/class-builtin.php:2036
msgid "Poland"
msgstr ""

#: includes/core/class-builtin.php:2037
msgid "Portugal"
msgstr ""

#: includes/core/class-builtin.php:2038
msgid "Puerto Rico"
msgstr ""

#: includes/core/class-builtin.php:2039
msgid "Qatar"
msgstr ""

#: includes/core/class-builtin.php:2040
msgid "Réunion"
msgstr ""

#: includes/core/class-builtin.php:2041
msgid "Romania"
msgstr ""

#: includes/core/class-builtin.php:2042
msgid "Russian Federation"
msgstr ""

#: includes/core/class-builtin.php:2043
msgid "Rwanda"
msgstr ""

#: includes/core/class-builtin.php:2044
msgid "Saint Barthélemy"
msgstr ""

#: includes/core/class-builtin.php:2045
msgid "Saint Helena"
msgstr ""

#: includes/core/class-builtin.php:2046
msgid "Saint Kitts and Nevis"
msgstr ""

#: includes/core/class-builtin.php:2047
msgid "Saint Lucia"
msgstr ""

#: includes/core/class-builtin.php:2048
msgid "Saint Martin (French part)"
msgstr ""

#: includes/core/class-builtin.php:2049
msgid "Saint Pierre and Miquelon"
msgstr ""

#: includes/core/class-builtin.php:2050
msgid "Saint Vincent and the Grenadines"
msgstr ""

#: includes/core/class-builtin.php:2051
msgid "Samoa"
msgstr ""

#: includes/core/class-builtin.php:2052
msgid "San Marino"
msgstr ""

#: includes/core/class-builtin.php:2053
msgid "Sao Tome and Principe"
msgstr ""

#: includes/core/class-builtin.php:2054
msgid "Saudi Arabia"
msgstr ""

#: includes/core/class-builtin.php:2055
msgid "Senegal"
msgstr ""

#: includes/core/class-builtin.php:2056
msgid "Serbia"
msgstr ""

#: includes/core/class-builtin.php:2057
msgid "Seychelles"
msgstr ""

#: includes/core/class-builtin.php:2058
msgid "Sierra Leone"
msgstr ""

#: includes/core/class-builtin.php:2059
msgid "Singapore"
msgstr ""

#: includes/core/class-builtin.php:2060
msgid "Slovakia"
msgstr ""

#: includes/core/class-builtin.php:2061
msgid "Slovenia"
msgstr ""

#: includes/core/class-builtin.php:2062
msgid "Solomon Islands"
msgstr ""

#: includes/core/class-builtin.php:2063
msgid "Somalia"
msgstr ""

#: includes/core/class-builtin.php:2064
msgid "South Africa"
msgstr ""

#: includes/core/class-builtin.php:2065
msgid "South Georgia and the South Sandwich Islands"
msgstr ""

#: includes/core/class-builtin.php:2066
msgid "South Sudan"
msgstr ""

#: includes/core/class-builtin.php:2067
msgid "Spain"
msgstr ""

#: includes/core/class-builtin.php:2068
msgid "Sri Lanka"
msgstr ""

#: includes/core/class-builtin.php:2069
msgid "Sudan"
msgstr ""

#: includes/core/class-builtin.php:2070
msgid "Suriname"
msgstr ""

#: includes/core/class-builtin.php:2071
msgid "Svalbard and Jan Mayen"
msgstr ""

#: includes/core/class-builtin.php:2072
msgid "Eswatini"
msgstr ""

#: includes/core/class-builtin.php:2073
msgid "Sweden"
msgstr ""

#: includes/core/class-builtin.php:2074
msgid "Switzerland"
msgstr ""

#: includes/core/class-builtin.php:2075
msgid "Syrian Arab Republic"
msgstr ""

#: includes/core/class-builtin.php:2076
msgid "Taiwan, Province of China"
msgstr ""

#: includes/core/class-builtin.php:2077
msgid "Tajikistan"
msgstr ""

#: includes/core/class-builtin.php:2078
msgid "Tanzania, United Republic of"
msgstr ""

#: includes/core/class-builtin.php:2079
msgid "Thailand"
msgstr ""

#: includes/core/class-builtin.php:2080
msgid "Timor-Leste"
msgstr ""

#: includes/core/class-builtin.php:2081
msgid "Togo"
msgstr ""

#: includes/core/class-builtin.php:2082
msgid "Tokelau"
msgstr ""

#: includes/core/class-builtin.php:2084
msgid "Trinidad and Tobago"
msgstr ""

#: includes/core/class-builtin.php:2085
msgid "Tunisia"
msgstr ""

#: includes/core/class-builtin.php:2086
msgid "Türkiye"
msgstr ""

#: includes/core/class-builtin.php:2087
msgid "Turkmenistan"
msgstr ""

#: includes/core/class-builtin.php:2088
msgid "Turks and Caicos Islands"
msgstr ""

#: includes/core/class-builtin.php:2089
msgid "Tuvalu"
msgstr ""

#: includes/core/class-builtin.php:2090
msgid "Uganda"
msgstr ""

#: includes/core/class-builtin.php:2091
msgid "Ukraine"
msgstr ""

#: includes/core/class-builtin.php:2092
msgid "United Arab Emirates"
msgstr ""

#: includes/core/class-builtin.php:2093
msgid "United Kingdom"
msgstr ""

#: includes/core/class-builtin.php:2094
msgid "United States"
msgstr ""

#: includes/core/class-builtin.php:2095
msgid "United States Minor Outlying Islands"
msgstr ""

#: includes/core/class-builtin.php:2096
msgid "Uruguay"
msgstr ""

#: includes/core/class-builtin.php:2097
msgid "Uzbekistan"
msgstr ""

#: includes/core/class-builtin.php:2098
msgid "Vanuatu"
msgstr ""

#: includes/core/class-builtin.php:2099
msgid "Venezuela, Bolivarian Republic of"
msgstr ""

#: includes/core/class-builtin.php:2100
msgid "Viet Nam"
msgstr ""

#: includes/core/class-builtin.php:2101
msgid "Virgin Islands, British"
msgstr ""

#: includes/core/class-builtin.php:2102
msgid "Virgin Islands, U.S."
msgstr ""

#: includes/core/class-builtin.php:2103
msgid "Wallis and Futuna"
msgstr ""

#: includes/core/class-builtin.php:2104
msgid "Western Sahara"
msgstr ""

#: includes/core/class-builtin.php:2105
msgid "Yemen"
msgstr ""

#: includes/core/class-builtin.php:2106
msgid "Zambia"
msgstr ""

#: includes/core/class-builtin.php:2107
msgid "Zimbabwe"
msgstr ""

#: includes/core/class-cron.php:58
msgid "Once Weekly"
msgstr ""

#: includes/core/class-date-time.php:47
msgid "just now"
msgstr ""

#. translators: %s: min time.
#: includes/core/class-date-time.php:57
#, php-format
msgid "%s min"
msgid_plural "%s mins"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: hours.
#: includes/core/class-date-time.php:67
#, php-format
msgid "%s hr"
msgid_plural "%s hrs"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: time.
#: includes/core/class-date-time.php:78
#, php-format
msgid "Yesterday at %s"
msgstr ""

#. translators: %1$s is a date; %2$s is a time.
#: includes/core/class-date-time.php:81
#: includes/core/class-date-time.php:86
#: includes/core/class-date-time.php:91
#: includes/core/class-date-time.php:96
#, php-format
msgid "%1$s at %2$s"
msgstr ""

#: includes/core/class-date-time.php:148
msgid "Less than 1 year old"
msgstr ""

#. translators: %s: age.
#: includes/core/class-date-time.php:152
#, php-format
msgid "%s year old"
msgid_plural "%s years old"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: The message after registration process based on a role data and user status after registration
#. translators: %s: Restricted blog page message.
#. translators: %s: Restricted taxonomy message.
#: includes/core/class-fields.php:1599
#: includes/core/um-actions-account.php:547
#: templates/message.php:23
#: templates/restricted-blog.php:41
#: templates/restricted-taxonomy.php:59
#, php-format
msgid "%s"
msgstr ""

#: includes/core/class-fields.php:1618
msgid "Custom Field"
msgstr ""

#: includes/core/class-fields.php:1982
msgid "Please upload a valid image!"
msgstr ""

#: includes/core/class-fields.php:1996
msgid "Sorry this is not a valid image."
msgstr ""

#: includes/core/class-fields.php:1999
msgid "This image is too large!"
msgstr ""

#: includes/core/class-fields.php:2002
msgid "This image is too small!"
msgstr ""

#: includes/core/class-fields.php:2005
msgid "You can only upload one image"
msgstr ""

#: includes/core/class-fields.php:2037
msgid "Sorry this is not a valid file."
msgstr ""

#: includes/core/class-fields.php:2040
msgid "This file is too large!"
msgstr ""

#: includes/core/class-fields.php:2043
msgid "This file is too small!"
msgstr ""

#: includes/core/class-fields.php:2046
msgid "You can only upload one file"
msgstr ""

#: includes/core/class-fields.php:2583
msgid "Current Password"
msgstr ""

#: includes/core/class-fields.php:2623
msgid "New Password"
msgstr ""

#. translators: %s: label.
#. translators: %s: placeholder.
#: includes/core/class-fields.php:2674
#: includes/core/class-fields.php:2692
#: includes/core/class-fields.php:2695
#, php-format
msgid "Confirm %s"
msgstr ""

#: includes/core/class-fields.php:3008
msgid "Upload Photo"
msgstr ""

#: includes/core/class-fields.php:3035
#: includes/core/class-fields.php:3079
#: includes/core/um-actions-profile.php:1086
#: includes/core/um-actions-profile.php:1119
msgid "Change photo"
msgstr ""

#: includes/core/class-fields.php:3079
#: includes/core/class-fields.php:3204
msgid "Processing..."
msgstr ""

#: includes/core/class-fields.php:3079
msgid "Apply"
msgstr ""

#: includes/core/class-fields.php:3107
msgid "Upload File"
msgstr ""

#: includes/core/class-fields.php:3152
#: includes/core/um-filters-fields.php:373
msgid "This file has been removed."
msgstr ""

#: includes/core/class-fields.php:3156
#: includes/core/class-fields.php:3204
msgid "Change file"
msgstr ""

#: includes/core/class-fields.php:3204
msgid "Save"
msgstr ""

#. translators: %s: edit user link.
#: includes/core/class-fields.php:4726
#, php-format
msgid "Your profile is looking a little empty. Why not <a href=\"%s\">add</a> some information!"
msgstr ""

#: includes/core/class-fields.php:4728
msgid "This user has not added any information to their profile yet."
msgstr ""

#: includes/core/class-fields.php:4993
msgid "Please login as administrator."
msgstr ""

#: includes/core/class-fields.php:4997
msgid "Invalid action."
msgstr ""

#: includes/core/class-files.php:270
msgid "Wrong path"
msgstr ""

#: includes/core/class-files.php:274
msgid "Wrong mode"
msgstr ""

#: includes/core/class-files.php:294
#: includes/core/class-files.php:329
#: includes/core/class-files.php:514
#: includes/core/class-files.php:683
#: includes/core/class-files.php:688
#: includes/core/class-files.php:699
msgid "You have no permission to edit this user"
msgstr ""

#: includes/core/class-files.php:308
msgid "You have no permission to delete this file"
msgstr ""

#: includes/core/class-files.php:319
msgid "Invalid parameters"
msgstr ""

#: includes/core/class-files.php:324
msgid "Invalid coordinates"
msgstr ""

#: includes/core/class-files.php:333
#: includes/core/class-files.php:519
msgid "Please login to edit this user"
msgstr ""

#: includes/core/class-files.php:343
#: includes/core/class-files.php:530
msgid "You have no permission to edit user profile"
msgstr ""

#: includes/core/class-files.php:347
#: includes/core/class-files.php:535
#: includes/core/class-files.php:704
msgid "User has to be empty on registration"
msgstr ""

#: includes/core/class-files.php:353
#: includes/core/class-files.php:542
#: includes/core/class-files.php:711
msgid "Invalid form ID"
msgstr ""

#: includes/core/class-files.php:357
#: includes/core/class-files.php:547
#: includes/core/class-files.php:716
msgid "Invalid form post type"
msgstr ""

#: includes/core/class-files.php:362
#: includes/core/class-files.php:553
#: includes/core/class-files.php:722
msgid "Invalid form status"
msgstr ""

#: includes/core/class-files.php:367
#: includes/core/class-files.php:559
#: includes/core/class-files.php:728
msgid "Invalid form type"
msgstr ""

#: includes/core/class-files.php:376
#: includes/core/class-files.php:383
#: includes/core/class-files.php:386
#: includes/core/class-files.php:569
#: includes/core/class-files.php:577
#: includes/core/class-files.php:581
#: includes/core/class-files.php:738
#: includes/core/class-files.php:746
#: includes/core/class-files.php:750
msgid "You have no permission to edit this user through this form"
msgstr ""

#: includes/core/class-files.php:393
#: includes/core/class-files.php:587
#: includes/core/class-files.php:758
msgid "Invalid form fields"
msgstr ""

#: includes/core/class-files.php:399
#: includes/core/class-files.php:594
#: includes/core/class-files.php:764
msgid "Invalid field metakey"
msgstr ""

#: includes/core/class-files.php:404
msgid "This field doesn't support image crop"
msgstr ""

#: includes/core/class-files.php:412
#: includes/core/class-files.php:417
#: includes/core/class-files.php:421
#: includes/core/class-files.php:604
#: includes/core/class-files.php:610
#: includes/core/class-files.php:615
#: includes/core/class-files.php:769
msgid "You have no permission to edit this field"
msgstr ""

#: includes/core/class-files.php:428
msgid "Invalid file ownership"
msgstr ""

#: includes/core/class-files.php:454
msgid "Invalid image key"
msgstr ""

#: includes/core/class-files.php:508
#: includes/core/class-files.php:676
msgid "Invalid nonce"
msgstr ""

#: includes/core/class-files.php:636
#: includes/core/class-files.php:793
msgid "A theme or plugin compatibility issue"
msgstr ""

#: includes/core/class-files.php:1534
msgid "Original size"
msgstr ""

#: includes/core/class-form.php:84
msgid "Invalid hook"
msgstr ""

#: includes/core/class-form.php:91
msgid "You can not edit this user."
msgstr ""

#: includes/core/class-form.php:128
#: includes/core/class-form.php:137
msgid "Wrong callback."
msgstr ""

#: includes/core/class-form.php:602
#: includes/core/class-password.php:411
#: includes/core/class-password.php:491
msgid "Hello, spam bot!"
msgstr ""

#: includes/core/class-gdpr.php:51
msgid "Please agree privacy policy."
msgstr ""

#: includes/core/class-gdpr.php:90
#: includes/um-short-functions.php:667
msgid "GDPR Applied"
msgstr ""

#: includes/core/class-member-directory-meta.php:603
#: includes/core/class-member-directory-meta.php:608
#: includes/core/class-member-directory.php:2806
#: includes/core/class-member-directory.php:2812
msgid "Wrong member directory data"
msgstr ""

#: includes/core/class-member-directory.php:393
msgid "Grid"
msgstr ""

#: includes/core/class-member-directory.php:394
msgid "List"
msgstr ""

#: includes/core/class-member-directory.php:399
msgid "New users first"
msgstr ""

#: includes/core/class-member-directory.php:400
msgid "Old users first"
msgstr ""

#: includes/core/class-member-directory.php:403
msgid "First name"
msgstr ""

#: includes/core/class-member-directory.php:404
msgid "Last name"
msgstr ""

#: includes/core/class-member-directory.php:405
msgid "Display name"
msgstr ""

#: includes/core/class-member-directory.php:406
msgid "Last & First name"
msgstr ""

#: includes/core/class-member-directory.php:407
msgid "Last login"
msgstr ""

#: includes/core/class-member-directory.php:413
msgid "CHAR"
msgstr ""

#: includes/core/class-member-directory.php:414
msgid "NUMERIC"
msgstr ""

#: includes/core/class-member-directory.php:415
msgid "BINARY"
msgstr ""

#: includes/core/class-member-directory.php:416
msgid "DATE"
msgstr ""

#: includes/core/class-member-directory.php:417
msgid "DATETIME"
msgstr ""

#: includes/core/class-member-directory.php:418
msgid "DECIMAL"
msgstr ""

#: includes/core/class-member-directory.php:419
msgid "SIGNED"
msgstr ""

#: includes/core/class-member-directory.php:420
msgid "TIME"
msgstr ""

#: includes/core/class-member-directory.php:421
msgid "UNSIGNED"
msgstr ""

#. translators: %s: title.
#: includes/core/class-member-directory.php:434
#: includes/core/class-member-directory.php:444
#, php-format
msgid "%s DESC"
msgstr ""

#. translators: %s: title.
#: includes/core/class-member-directory.php:446
#, php-format
msgid "%s ASC"
msgstr ""

#: includes/core/class-member-directory.php:454
#: templates/members.php:102
msgid "Random"
msgstr ""

#: includes/core/class-member-directory.php:466
#: includes/core/um-filters-fields.php:1006
#: includes/core/um-filters-fields.php:1023
msgid "Age"
msgstr ""

#: includes/core/class-member-directory.php:468
msgid "User Registered"
msgstr ""

#. translators: %s: Datetime filter label.
#. translators: %s: Timepicker filter label.
#: includes/core/class-member-directory.php:918
#: includes/core/class-member-directory.php:963
#, php-format
msgid "%s From"
msgstr ""

#. translators: %s: Datetime filter label.
#. translators: %s: Timepicker filter label.
#: includes/core/class-member-directory.php:924
#: includes/core/class-member-directory.php:970
#, php-format
msgid "%s To"
msgstr ""

#: includes/core/class-member-directory.php:1068
#: includes/core/class-member-directory.php:1069
msgid " stars"
msgstr ""

#: includes/core/class-member-directory.php:1079
msgid "<strong>Age:</strong>&nbsp;{value} years old"
msgstr ""

#: includes/core/class-member-directory.php:1080
msgid "<strong>Age:</strong>&nbsp;{min_range} - {max_range} years old"
msgstr ""

#: includes/core/class-member-directory.php:2479
#: includes/core/class-member-directory.php:2507
#: includes/core/um-actions-profile.php:1486
#: includes/core/um-actions-profile.php:1517
msgid "Edit Profile"
msgstr ""

#: includes/core/class-member-directory.php:2513
#: includes/core/um-actions-profile.php:1518
msgid "My Account"
msgstr ""

#: includes/core/class-password.php:422
msgid "Please provide your username or email"
msgstr ""

#: includes/core/class-password.php:440
msgid "You have reached the limit for requesting password change for this user already. Contact support if you cannot open the email"
msgstr ""

#: includes/core/class-password.php:508
msgid "Unable to change password because of password change limit. Please try again later."
msgstr ""

#: includes/core/class-password.php:514
msgid "You must enter a new password"
msgstr ""

#: includes/core/class-password.php:528
#: includes/core/um-actions-account.php:67
#: includes/core/um-actions-register.php:342
msgid "Passwords may not contain the character \"\\\"."
msgstr ""

#. translators: %s: min length.
#: includes/core/class-password.php:553
#: includes/core/um-actions-account.php:104
#, php-format
msgid "Your password must contain at least %d characters"
msgstr ""

#. translators: %s: max length.
#: includes/core/class-password.php:558
#: includes/core/um-actions-account.php:108
#, php-format
msgid "Your password must contain less than %d characters"
msgstr ""

#: includes/core/class-password.php:562
#: includes/core/um-actions-account.php:112
#: includes/core/um-actions-form.php:638
msgid "Your password cannot contain the part of your username"
msgstr ""

#: includes/core/class-password.php:566
#: includes/core/um-actions-account.php:116
#: includes/core/um-actions-form.php:642
msgid "Your password cannot contain the part of your email address"
msgstr ""

#: includes/core/class-password.php:570
#: includes/core/um-actions-account.php:120
#: includes/core/um-actions-form.php:646
msgid "Your password must contain at least one lowercase letter, one capital letter and one number"
msgstr ""

#: includes/core/class-password.php:575
msgid "You must confirm your new password"
msgstr ""

#: includes/core/class-password.php:579
#: includes/core/um-actions-form.php:658
msgid "Your passwords do not match"
msgstr ""

#: includes/core/class-profile.php:67
#: includes/core/class-profile.php:87
msgid "Invalid data"
msgstr ""

#: includes/core/class-profile.php:73
#: includes/core/class-profile.php:93
msgid "You can not edit this user"
msgstr ""

#: includes/core/class-profile.php:126
msgid "Anyone"
msgstr ""

#: includes/core/class-profile.php:127
msgid "Guests only"
msgstr ""

#: includes/core/class-profile.php:128
msgid "Members only"
msgstr ""

#: includes/core/class-profile.php:129
msgid "Only the owner"
msgstr ""

#: includes/core/class-profile.php:130
msgid "Only specific roles"
msgstr ""

#: includes/core/class-profile.php:131
msgid "Owner and specific roles"
msgstr ""

#: includes/core/class-profile.php:144
msgid "About"
msgstr ""

#: includes/core/class-profile.php:148
msgid "Posts"
msgstr ""

#: includes/core/class-profile.php:152
msgid "Comments"
msgstr ""

#: includes/core/class-query.php:40
msgid "Invalid hook."
msgstr ""

#: includes/core/class-query.php:210
msgid "The \"unassigned\" $status has been removed. Use `UM()->setup()->set_default_user_status()` for setting up default user account status."
msgstr ""

#: includes/core/class-shortcodes.php:406
msgid "This content has been restricted to logged-in users only. Please <a href=\"{login_referrer}\">log in</a> to view this content."
msgstr ""

#: includes/core/class-shortcodes.php:493
#: includes/core/class-shortcodes.php:514
msgid "Go to profile"
msgstr ""

#: includes/core/class-shortcodes.php:852
msgid "You are already registered."
msgstr ""

#: includes/core/class-shortcodes.php:1111
msgid "Default Template"
msgstr ""

#: includes/core/class-uploader.php:700
msgid "Your image is invalid!"
msgstr ""

#: includes/core/class-uploader.php:742
msgid "This media type is not recognized."
msgstr ""

#: includes/core/class-uploader.php:790
msgid "Your image is invalid or too large!"
msgstr ""

#. translators: %s: min widdth.
#: includes/core/class-uploader.php:797
#, php-format
msgid "Your photo is too small. It must be at least %spx wide."
msgstr ""

#. translators: %s: min height.
#: includes/core/class-uploader.php:800
#, php-format
msgid "Your photo is too small. It must be at least %spx high."
msgstr ""

#: includes/core/class-uploader.php:853
msgid "This file type is not recognized."
msgstr ""

#. translators: %s is the file src.
#: includes/core/class-uploader.php:1014
#: includes/core/class-uploader.php:1041
#: includes/core/class-uploader.php:1089
#: includes/core/class-uploader.php:1123
#, php-format
msgid "Unable to crop image file: %s"
msgstr ""

#: includes/core/class-user.php:1118
msgid "Ultimate Member Role"
msgstr ""

#: includes/core/class-user.php:1121
msgid "&mdash; No role for Ultimate Member &mdash;"
msgstr ""

#: includes/core/rest/class-api-v1.php:222
#: includes/core/rest/class-api-v1.php:279
#: includes/core/rest/class-api-v1.php:311
#: includes/core/rest/class-api-v2.php:204
#: includes/core/rest/class-api-v2.php:261
#: includes/core/rest/class-api-v2.php:293
msgid "You must provide a user ID"
msgstr ""

#: includes/core/rest/class-api-v1.php:227
#: includes/core/rest/class-api-v2.php:209
msgid "You need to provide data to update"
msgstr ""

#: includes/core/rest/class-api-v1.php:232
#: includes/core/rest/class-api-v2.php:214
msgid "You need to provide value to update"
msgstr ""

#: includes/core/rest/class-api-v1.php:246
#: includes/core/rest/class-api-v2.php:228
msgid "User status has been changed."
msgstr ""

#: includes/core/rest/class-api-v1.php:256
#: includes/core/rest/class-api-v2.php:238
msgid "User role has been changed."
msgstr ""

#: includes/core/rest/class-api-v1.php:260
#: includes/core/rest/class-api-v2.php:242
msgid "User meta has been changed."
msgstr ""

#: includes/core/rest/class-api-v1.php:287
#: includes/core/rest/class-api-v1.php:318
#: includes/core/rest/class-api-v2.php:269
#: includes/core/rest/class-api-v2.php:300
msgid "Invalid user specified"
msgstr ""

#: includes/core/rest/class-api-v1.php:294
#: includes/core/rest/class-api-v2.php:276
msgid "User has been successfully deleted."
msgstr ""

#: includes/core/rest/class-api.php:253
msgid "You must specify both a token and API key!"
msgstr ""

#: includes/core/rest/class-api.php:265
msgid "Your request could not be authenticated"
msgstr ""

#: includes/core/rest/class-api.php:277
msgid "Invalid API key"
msgstr ""

#: includes/core/rest/class-api.php:434
msgid "Invalid query!"
msgstr ""

#: includes/core/um-actions-account.php:21
msgid "Are you hacking? Please try again!"
msgstr ""

#: includes/core/um-actions-account.php:29
#: includes/core/um-actions-account.php:175
msgid "You must enter your password"
msgstr ""

#: includes/core/um-actions-account.php:32
#: includes/core/um-actions-account.php:75
#: includes/core/um-actions-account.php:79
#: includes/core/um-actions-account.php:178
msgid "This is not your password"
msgstr ""

#: includes/core/um-actions-account.php:56
msgid "Password is required"
msgstr ""

#: includes/core/um-actions-account.php:61
msgid "Password confirmation is required"
msgstr ""

#: includes/core/um-actions-account.php:86
msgid "Your new password does not match"
msgstr ""

#: includes/core/um-actions-account.php:150
msgid "You must provide your first name"
msgstr ""

#: includes/core/um-actions-account.php:154
msgid "You must provide your last name"
msgstr ""

#: includes/core/um-actions-account.php:160
#: includes/core/um-actions-form.php:927
#: includes/core/um-actions-form.php:941
msgid "You must provide your email"
msgstr ""

#: includes/core/um-actions-account.php:164
#: includes/core/um-actions-account.php:168
msgid "Please provide a valid email"
msgstr ""

#: includes/core/um-actions-account.php:562
msgid "Select what email notifications you want to receive"
msgstr ""

#: includes/core/um-actions-account.php:629
msgid "Download your data"
msgstr ""

#: includes/core/um-actions-account.php:631
msgid "You can request a file with the information that we believe is most relevant and useful to you."
msgstr ""

#: includes/core/um-actions-account.php:656
msgid "You could download your previous data:"
msgstr ""

#: includes/core/um-actions-account.php:657
msgid "Download Personal Data"
msgstr ""

#: includes/core/um-actions-account.php:658
msgid "You could send a new request for an export of personal your data."
msgstr ""

#: includes/core/um-actions-account.php:678
#: includes/core/um-actions-account.php:856
msgid "A confirmation email has been sent to your email. Click the link within the email to confirm your export request."
msgstr ""

#: includes/core/um-actions-account.php:680
msgid "The administrator has not yet approved downloading the data. Please expect an email with a link to your data."
msgstr ""

#: includes/core/um-actions-account.php:685
msgid "Enter your current password to confirm a new export of your personal data."
msgstr ""

#: includes/core/um-actions-account.php:697
#: includes/core/um-actions-account.php:787
msgid "You must enter a password"
msgstr ""

#: includes/core/um-actions-account.php:705
msgid "To export of your personal data, click the button below."
msgstr ""

#: includes/core/um-actions-account.php:712
msgid "Request data"
msgstr ""

#: includes/core/um-actions-account.php:721
msgid "Erase of your data"
msgstr ""

#: includes/core/um-actions-account.php:723
msgid "You can request erasing of the data that we have about you."
msgstr ""

#: includes/core/um-actions-account.php:747
msgid "Your personal data has been deleted."
msgstr ""

#: includes/core/um-actions-account.php:748
msgid "You could send a new request for deleting your personal data."
msgstr ""

#: includes/core/um-actions-account.php:768
#: includes/core/um-actions-account.php:858
msgid "A confirmation email has been sent to your email. Click the link within the email to confirm your deletion request."
msgstr ""

#: includes/core/um-actions-account.php:770
msgid "The administrator has not yet approved deleting your data. Please expect an email with a link to your data."
msgstr ""

#: includes/core/um-actions-account.php:775
msgid "Enter your current password to confirm the erasure of your personal data."
msgstr ""

#: includes/core/um-actions-account.php:795
msgid "Require erasure of your personal data, click on the button below."
msgstr ""

#: includes/core/um-actions-account.php:802
msgid "Request data erase"
msgstr ""

#: includes/core/um-actions-account.php:816
#: includes/core/um-actions-account.php:848
msgid "Wrong request."
msgstr ""

#: includes/core/um-actions-account.php:828
#: includes/core/um-actions-account.php:835
msgid "The password you entered is incorrect."
msgstr ""

#: includes/core/um-actions-form.php:98
msgid "You are not allowed to use this word as your username."
msgstr ""

#: includes/core/um-actions-form.php:405
msgid "Profile Photo is required."
msgstr ""

#. translators: %s: title.
#: includes/core/um-actions-form.php:509
#: includes/core/um-actions-form.php:511
#, php-format
msgid "%s - wrong conditions."
msgstr ""

#. translators: %s: title.
#: includes/core/um-actions-form.php:521
#: includes/core/um-actions-form.php:526
#: includes/core/um-actions-form.php:531
#: includes/core/um-actions-form.php:536
#, php-format
msgid "%s is required."
msgstr ""

#: includes/core/um-actions-form.php:541
msgid "Please specify account type."
msgstr ""

#: includes/core/um-actions-form.php:571
msgid "This field is required"
msgstr ""

#. translators: %s: title.
#: includes/core/um-actions-form.php:574
#, php-format
msgid "%s is required"
msgstr ""

#. translators: %s: max words.
#: includes/core/um-actions-form.php:593
#, php-format
msgid "You are only allowed to enter a maximum of %s words"
msgstr ""

#. translators: %s: min chars.
#: includes/core/um-actions-form.php:601
#, php-format
msgid "This field must contain at least %s characters"
msgstr ""

#. translators: %1$s is a label; %2$s is a min chars.
#: includes/core/um-actions-form.php:604
#, php-format
msgid "Your %1$s must contain at least %2$s characters"
msgstr ""

#. translators: %s: max chars.
#: includes/core/um-actions-form.php:620
#, php-format
msgid "This field must contain less than %s characters"
msgstr ""

#. translators: %1$s is a label; %2$s is a max chars.
#: includes/core/um-actions-form.php:623
#, php-format
msgid "Your %1$s must contain less than %2$s characters"
msgstr ""

#: includes/core/um-actions-form.php:631
msgid "You can not use HTML tags here"
msgstr ""

#: includes/core/um-actions-form.php:652
#: includes/core/um-actions-form.php:655
msgid "Please confirm your password"
msgstr ""

#. translators: %s: min selections.
#: includes/core/um-actions-form.php:666
#, php-format
msgid "Please select at least %s choices"
msgstr ""

#. translators: %s: max selections.
#: includes/core/um-actions-form.php:673
#, php-format
msgid "You can only select up to %s choices"
msgstr ""

#. translators: %s: min limit.
#: includes/core/um-actions-form.php:680
#, php-format
msgid "Minimum number limit is %s"
msgstr ""

#. translators: %s: max limit.
#: includes/core/um-actions-form.php:687
#, php-format
msgid "Maximum number limit is %s"
msgstr ""

#. translators: %s: max chars.
#: includes/core/um-actions-form.php:747
#: includes/core/um-actions-form.php:1073
#, php-format
msgid "Your user description must contain less than %s characters"
msgstr ""

#: includes/core/um-actions-form.php:785
msgid "Please enter numbers only in this field"
msgstr ""

#: includes/core/um-actions-form.php:791
msgid "Please enter a valid phone number"
msgstr ""

#. translators: %s: label.
#: includes/core/um-actions-form.php:798
#: includes/core/um-actions-form.php:819
#: includes/core/um-actions-form.php:826
#: includes/core/um-actions-form.php:833
#: includes/core/um-actions-form.php:840
#: includes/core/um-actions-form.php:854
#, php-format
msgid "Please enter a valid %s username or profile URL"
msgstr ""

#. translators: %s: label.
#: includes/core/um-actions-form.php:805
#: includes/core/um-actions-form.php:812
#, php-format
msgid "Please enter a valid %s URL"
msgstr ""

#. translators: %s: label.
#: includes/core/um-actions-form.php:847
#: includes/core/um-actions-form.php:867
#: includes/core/um-actions-form.php:874
#: includes/core/um-actions-form.php:881
#, php-format
msgid "Please enter a valid %s profile URL"
msgstr ""

#: includes/core/um-actions-form.php:860
msgid "Please enter a valid Discord ID"
msgstr ""

#: includes/core/um-actions-form.php:887
msgid "Please enter a valid URL"
msgstr ""

#: includes/core/um-actions-form.php:893
msgid "You must provide a username"
msgstr ""

#: includes/core/um-actions-form.php:895
#: includes/core/um-actions-form.php:907
msgid "The username you entered is incorrect"
msgstr ""

#: includes/core/um-actions-form.php:897
msgid "Username cannot be an email"
msgstr ""

#: includes/core/um-actions-form.php:899
#: includes/core/um-actions-form.php:911
msgid "Your username contains invalid characters"
msgstr ""

#: includes/core/um-actions-form.php:905
msgid "You must provide a username or email"
msgstr ""

#: includes/core/um-actions-form.php:909
#: includes/core/um-actions-form.php:929
#: includes/core/um-actions-form.php:931
#: includes/core/um-actions-form.php:933
#: includes/core/um-actions-form.php:943
#: includes/core/um-actions-form.php:960
msgid "The email you entered is incorrect"
msgstr ""

#: includes/core/um-actions-form.php:935
msgid "Your email contains invalid characters"
msgstr ""

#: includes/core/um-actions-form.php:945
msgid "The secondary email cannot be the same as primary"
msgstr ""

#: includes/core/um-actions-form.php:970
msgid "This is not a valid email"
msgstr ""

#: includes/core/um-actions-form.php:991
msgid "You must provide a unique value"
msgstr ""

#: includes/core/um-actions-form.php:999
msgid "You must provide alphabetic letters"
msgstr ""

#: includes/core/um-actions-form.php:1007
msgid "You must provide alphabetic letters or numbers"
msgstr ""

#: includes/core/um-actions-form.php:1015
msgid "You must provide lowercase letters."
msgstr ""

#: includes/core/um-actions-form.php:1023
msgid "You must provide English letters."
msgstr ""

#: includes/core/um-actions-login.php:15
msgid "Please enter your username or email"
msgstr ""

#: includes/core/um-actions-login.php:19
msgid "Please enter your username"
msgstr ""

#: includes/core/um-actions-login.php:23
msgid "Please enter your email"
msgstr ""

#: includes/core/um-actions-login.php:47
msgid "Please enter your password"
msgstr ""

#: includes/core/um-actions-login.php:54
#: includes/core/um-actions-login.php:69
#: includes/core/um-actions-login.php:78
msgid "Password is incorrect. Please try again."
msgstr ""

#: includes/core/um-actions-login.php:204
msgid "This action has been prevented for security measures."
msgstr ""

#: includes/core/um-actions-login.php:441
msgid "Keep me signed in"
msgstr ""

#: includes/core/um-actions-login.php:485
msgid "Forgot your password?"
msgstr ""

#: includes/core/um-actions-misc.php:110
msgid "Your account was updated successfully."
msgstr ""

#: includes/core/um-actions-misc.php:113
msgid "You have successfully changed your password."
msgstr ""

#: includes/core/um-actions-misc.php:116
msgid "Your account is now active! You can login."
msgstr ""

#: includes/core/um-actions-misc.php:149
msgid "An error has been encountered"
msgstr ""

#: includes/core/um-actions-misc.php:153
msgid "Registration is currently disabled"
msgstr ""

#: includes/core/um-actions-misc.php:156
#: includes/core/um-filters-login.php:18
msgid "This email address has been blocked."
msgstr ""

#: includes/core/um-actions-misc.php:159
msgid "We do not accept registrations from that domain."
msgstr ""

#: includes/core/um-actions-misc.php:162
#: includes/core/um-filters-login.php:21
msgid "Your IP address has been blocked."
msgstr ""

#: includes/core/um-actions-misc.php:165
#: includes/core/um-filters-login.php:74
msgid "Your account has been disabled."
msgstr ""

#: includes/core/um-actions-misc.php:168
#: includes/core/um-filters-login.php:77
msgid "Your account has not been approved yet."
msgstr ""

#: includes/core/um-actions-misc.php:171
#: includes/core/um-filters-login.php:80
msgid "Your account is awaiting email verification."
msgstr ""

#: includes/core/um-actions-misc.php:174
#: includes/core/um-filters-login.php:83
msgid "Your membership request has been rejected."
msgstr ""

#: includes/core/um-actions-misc.php:177
msgid "An error has been encountered. Probably page was cached. Please try again."
msgstr ""

#: includes/core/um-actions-misc.php:180
msgid "This activation link is expired or have already been used."
msgstr ""

#: includes/core/um-actions-misc.php:183
msgid "This activation link is expired."
msgstr ""

#: includes/core/um-actions-profile.php:203
msgid "You are not allowed to edit this user."
msgstr ""

#. translators: %s: title.
#: includes/core/um-actions-profile.php:363
#, php-format
msgid "Your choosed %s"
msgstr ""

#: includes/core/um-actions-profile.php:894
#: includes/core/um-actions-profile.php:895
#: includes/core/um-actions-profile.php:908
#: includes/core/um-actions-profile.php:968
#: assets/js/um-profile.js:73
msgid "Upload a cover photo"
msgstr ""

#: includes/core/um-actions-profile.php:894
#: includes/core/um-actions-profile.php:895
msgid "Change cover photo"
msgstr ""

#: includes/core/um-actions-profile.php:899
msgid "Remove cover photo"
msgstr ""

#: includes/core/um-actions-profile.php:1086
#: includes/core/um-actions-profile.php:1119
msgid "Upload photo"
msgstr ""

#: includes/core/um-actions-profile.php:1087
#: includes/core/um-actions-profile.php:1120
msgid "Remove photo"
msgstr ""

#: includes/core/um-actions-profile.php:1321
msgid "Tell us a bit about yourself..."
msgstr ""

#. translators: %s: profile status.
#: includes/core/um-actions-profile.php:1341
#, php-format
msgid "This user account status is %s"
msgstr ""

#: includes/core/um-actions-user.php:14
msgid "Your account"
msgstr ""

#: includes/core/um-filters-fields.php:51
msgid "Invalid SoundCloud track ID"
msgstr ""

#: includes/core/um-filters-fields.php:108
msgid "Invalid Spotify URL"
msgstr ""

#. translators: %s: date.
#: includes/core/um-filters-fields.php:225
#, php-format
msgid "Joined %s"
msgstr ""

#: includes/core/um-filters-fields.php:407
msgid "Untitled photo"
msgstr ""

#. translators: %s: link.
#: includes/core/um-filters-fields.php:526
#, php-format
msgid "This link leads to a 3rd-party website. Make sure the link is safe and you really want to go to this website: '%s'"
msgstr ""

#: includes/core/um-filters-profile.php:135
#: includes/core/um-filters-profile.php:156
msgid "max"
msgstr ""

#: includes/frontend/class-actions-listener.php:52
#: includes/frontend/class-user-profile.php:53
msgid "Super administrators can not be modified."
msgstr ""

#: includes/frontend/class-actions-listener.php:60
#: includes/frontend/class-actions-listener.php:76
#: includes/frontend/class-actions-listener.php:92
#: includes/frontend/class-actions-listener.php:108
#: includes/frontend/class-actions-listener.php:124
#: includes/frontend/class-actions-listener.php:140
#: includes/frontend/class-actions-listener.php:160
#: includes/frontend/class-actions-listener.php:169
msgid "The link you followed has expired."
msgstr ""

#: includes/frontend/class-actions-listener.php:64
#: includes/frontend/class-actions-listener.php:80
#: includes/frontend/class-actions-listener.php:96
#: includes/frontend/class-actions-listener.php:112
#: includes/frontend/class-actions-listener.php:128
#: includes/frontend/class-actions-listener.php:144
msgid "You do not have permission to edit this user."
msgstr ""

#: includes/frontend/class-actions-listener.php:69
#: includes/frontend/class-actions-listener.php:85
#: includes/frontend/class-actions-listener.php:101
#: includes/frontend/class-actions-listener.php:117
#: includes/frontend/class-actions-listener.php:133
#: includes/frontend/class-actions-listener.php:149
msgid "Something went wrong."
msgstr ""

#: includes/frontend/class-actions-listener.php:173
msgid "You do not have permission to delete this user."
msgstr ""

#. translators: One-time change requires you to reset your password
#: includes/frontend/class-secure.php:81
#, php-format
msgid "<strong>Important:</strong> Your password has expired. This (one-time) change requires you to reset your password. Please <a href=\"%s\">click here</a> to reset your password via Email."
msgstr ""

#: includes/frontend/class-secure.php:112
msgid "<strong>Important:</strong> Our website is currently under maintenance. Please check back soon."
msgstr ""

#: includes/frontend/class-secure.php:180
#: includes/frontend/class-secure.php:181
msgid "Your new password cannot be same as old password."
msgstr ""

#: includes/frontend/class-users.php:40
msgid "Reactivate this account"
msgstr ""

#: includes/frontend/class-users.php:53
msgid "Deactivate this account"
msgstr ""

#: includes/frontend/class-users.php:58
msgid "Delete this user"
msgstr ""

#: includes/frontend/class-users.php:62
msgid "Login as this user"
msgstr ""

#: includes/um-deprecated-functions.php:676
msgid "date submitted"
msgstr ""

#: includes/um-deprecated-functions.php:682
#: includes/um-short-functions.php:852
#: includes/um-short-functions.php:913
msgid "(empty)"
msgstr ""

#: includes/um-short-functions.php:662
msgid "User registered date"
msgstr ""

#. translators: %1$s is a form title; %2$s is a form ID.
#: includes/um-short-functions.php:835
#, php-format
msgid "%1$s - Form ID#: %2$s"
msgstr ""

#: includes/widgets/class-um-search-widget.php:27
msgid "Ultimate Member - Search"
msgstr ""

#: includes/widgets/class-um-search-widget.php:30
msgid "Shows the search member form."
msgstr ""

#: includes/widgets/class-um-search-widget.php:81
msgid "Search Users"
msgstr ""

#: templates/members-grid.php:80
msgid "Edit profile"
msgstr ""

#: templates/members-pagination.php:19
msgid "Jump to page:"
msgstr ""

#: templates/members-pagination.php:22
msgid "of"
msgstr ""

#: templates/members-pagination.php:28
msgid "First page"
msgstr ""

#: templates/members-pagination.php:29
msgid "Previous page"
msgstr ""

#: templates/members-pagination.php:35
msgid "Next page"
msgstr ""

#: templates/members-pagination.php:36
msgid "Last page"
msgstr ""

#: templates/members.php:255
msgid "Search:"
msgstr ""

#: templates/members.php:256
#: templates/members.php:258
msgid "Search"
msgstr ""

#. translators: %s: title.
#: templates/members.php:282
#, php-format
msgid "Change to %s"
msgstr ""

#: templates/members.php:306
msgid "Sort by:"
msgstr ""

#: templates/members.php:327
msgid "More filters"
msgstr ""

#: templates/members.php:352
msgid "Remove filter"
msgstr ""

#: templates/members.php:379
msgid "Remove all filters"
msgstr ""

#: templates/members.php:379
msgid "Clear all"
msgstr ""

#: templates/modal/view-photo.php:15
msgid "Close view photo modal"
msgstr ""

#: templates/password-change.php:62
msgid "Set password"
msgstr ""

#: templates/password-change.php:64
msgid "Change password"
msgstr ""

#: templates/password-reset.php:28
msgid "If an account matching the provided details exists, we will send a password reset link. Please check your inbox."
msgstr ""

#: templates/password-reset.php:36
msgid "You have successfully changed password."
msgstr ""

#: templates/password-reset.php:73
msgid "Your password reset link has expired. Please request a new link below."
msgstr ""

#: templates/password-reset.php:75
msgid "Your password reset link appears to be invalid. Please request a new link below."
msgstr ""

#: templates/password-reset.php:87
msgid "To reset your password, please enter your email address or username below."
msgstr ""

#: templates/password-reset.php:147
msgid "Reset password"
msgstr ""

#. translators: %1$s is a link; %2$s is a title.
#: templates/profile/comments-single.php:31
#, php-format
msgid "On <a href=\"%1$s\">%2$s</a>"
msgstr ""

#: templates/profile/comments.php:40
msgid "load more comments"
msgstr ""

#: templates/profile/comments.php:52
msgid "You have not made any comments."
msgstr ""

#: templates/profile/comments.php:54
msgid "This user has not made any comments."
msgstr ""

#. translators: %s: human time diff.
#: templates/profile/posts-single.php:45
#, php-format
msgid "%s ago"
msgstr ""

#. translators: %s: categories list.
#: templates/profile/posts-single.php:53
#, php-format
msgid "in: %s"
msgstr ""

#: templates/profile/posts-single.php:61
msgid "no comments"
msgstr ""

#. translators: %s: comments number.
#: templates/profile/posts-single.php:64
#, php-format
msgid "%s comment"
msgid_plural "%s comments"
msgstr[0] ""
msgstr[1] ""

#: templates/profile/posts.php:38
msgid "load more posts"
msgstr ""

#: templates/profile/posts.php:50
msgid "You have not created any posts."
msgstr ""

#: templates/profile/posts.php:52
msgid "This user has not created any posts."
msgstr ""

#: assets/js/admin/block-restrictions.js:110
msgid "Restrict access?"
msgstr ""

#: assets/js/admin/block-restrictions.js:132
msgid "Who can access this block?"
msgstr ""

#: assets/js/admin/block-restrictions.js:171
msgid "What roles can access this block?"
msgstr ""

#: assets/js/admin/block-restrictions.js:184
msgid "Restriction action"
msgstr ""

#: assets/js/admin/block-restrictions.js:188
msgid "Hide block"
msgstr ""

#: assets/js/admin/block-restrictions.js:192
msgid "Show global default message"
msgstr ""

#: assets/js/admin/builder.js:711
msgid "This will permanently delete this custom field from a database and from all forms on your site. Are you sure?"
msgstr ""

#: assets/js/admin/forms.js:159
msgid "Wrong AJAX response..."
msgstr ""

#: assets/js/admin/forms.js:160
#: assets/js/admin/forms.js:166
msgid "Your upgrade was crashed, please contact with support"
msgstr ""

#: assets/js/admin/forms.js:165
msgid "Something went wrong with AJAX request..."
msgstr ""

#: assets/js/admin/forms.js:191
msgid "Upgrade Process Started..."
msgstr ""

#: assets/js/admin/forms.js:223
msgid "Getting metadata"
msgstr ""

#: assets/js/admin/security.js:27
msgid "Scanning site.."
msgstr ""

#: assets/js/admin/security.js:33
msgid "You can start the scan now but you must save the settings to apply the selected capabilities after the scan is complete."
msgstr ""

#: assets/js/admin/settings.js:38
msgid "Are sure, maybe some settings not saved"
msgstr ""

#: assets/js/um-modal.js:114
msgid "UM Warning: No field associated with image uploader."
msgstr ""

#: assets/js/um-profile.js:133
msgid "Are you sure that you want to delete this user?"
msgstr ""

#: assets/libs/raty/um-raty.js:612
msgid "Cancel this rating!"
msgstr ""

#: assets/libs/raty/um-raty.js:623
msgid "Not rated yet!"
msgstr ""

#: blocks-src/um-account/src/index.js:36
#: includes/blocks/um-account/src/index.js:1
msgid "Account Tab"
msgstr ""

#: blocks-src/um-account/src/index.js:38
#: includes/blocks/um-account/src/index.js:1
msgid "Select Tab"
msgstr ""

#: blocks-src/um-forms/src/index.js:21
#: blocks-src/um-member-directories/src/index.js:23
#: blocks-src/um-member-directories/src/index.js:42
#: includes/blocks/um-forms/src/index.js:1
#: includes/blocks/um-member-directories/src/index.js:1
msgid "Loading..."
msgstr ""

#: blocks-src/um-forms/src/index.js:24
#: includes/blocks/um-forms/src/index.js:1
msgid "No forms found."
msgstr ""

#: blocks-src/um-forms/src/index.js:26
#: includes/blocks/um-forms/src/index.js:1
msgid "Select Form"
msgstr ""

#: blocks-src/um-forms/src/index.js:37
#: blocks-src/um-forms/src/index.js:39
#: includes/blocks/um-forms/src/index.js:1
msgid "Select Forms"
msgstr ""

#: blocks-src/um-member-directories/src/index.js:26
#: includes/blocks/um-member-directories/src/index.js:1
msgid "No posts found."
msgstr ""

#: blocks-src/um-member-directories/src/index.js:28
#: includes/blocks/um-member-directories/src/index.js:1
msgid "Select Directory"
msgstr ""

#: blocks-src/um-member-directories/src/index.js:51
#: blocks-src/um-member-directories/src/index.js:53
#: includes/blocks/um-member-directories/src/index.js:1
msgid "Select Directories"
msgstr ""

#: blocks-src/um-account/block.json
#: includes/blocks/um-account/block.json
msgctxt "block title"
msgid "Account"
msgstr ""

#: blocks-src/um-account/block.json
#: includes/blocks/um-account/block.json
msgctxt "block description"
msgid "Displaying the account page of the current user"
msgstr ""

#: blocks-src/um-forms/block.json
#: includes/blocks/um-forms/block.json
msgctxt "block title"
msgid "Form"
msgstr ""

#: blocks-src/um-forms/block.json
#: includes/blocks/um-forms/block.json
msgctxt "block description"
msgid "Choose display form"
msgstr ""

#: blocks-src/um-member-directories/block.json
#: includes/blocks/um-member-directories/block.json
msgctxt "block title"
msgid "Member Directory"
msgstr ""

#: blocks-src/um-member-directories/block.json
#: includes/blocks/um-member-directories/block.json
msgctxt "block description"
msgid "Choose display directory"
msgstr ""

#: blocks-src/um-password-reset/block.json
#: includes/blocks/um-password-reset/block.json
msgctxt "block title"
msgid "Password Reset"
msgstr ""

#: blocks-src/um-password-reset/block.json
#: includes/blocks/um-password-reset/block.json
msgctxt "block description"
msgid "Displaying the password reset form"
msgstr ""
