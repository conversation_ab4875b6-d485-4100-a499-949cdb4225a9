function updraft_send_command(t,e,r,n){for(var a in default_options={json_parse:!0,alert_on_error:!0,action:"updraft_ajax",nonce:updraft_credentialtest_nonce,nonce_key:"nonce",timeout:null,async:!0,type:"POST"},void 0===n&&(n={}),default_options)n.hasOwnProperty(a)||(n[a]=default_options[a]);var o={action:n.action,subaction:t};if(o[n.nonce_key]=n.nonce,"object"==typeof e)for(var d in e)o[d]=e[d];else o.action_data=e;t={type:n.type,url:ajaxurl,data:o,success:function(e,t){if(n.json_parse){try{var a=ud_parse_json(e)}catch(t){return"function"==typeof n.error_callback?n.error_callback(e,t,502,a):(console.log(t),console.log(e),void(n.alert_on_error&&("string"==typeof e&&e.match(/security\scheck\s?/i)&&(e+=" ("+updraftlion.expired_tokens+" "+updraftlion.reload_page+")"),alert(updraftlion.unexpectedresponse+" "+e))))}if(a.hasOwnProperty("fatal_error"))return"function"==typeof n.error_callback?n.error_callback(e,t,500,a):(console.error(a.fatal_error_message),n.alert_on_error&&alert(a.fatal_error_message),!1);"function"==typeof r&&r(a,t,e)}else"function"==typeof r&&r(e,t)},error:function(t,e,a){"function"==typeof n.error_callback?n.error_callback(t,e,a):(console.log("updraft_send_command: error: "+e+" ("+a+")"),console.log(t))},dataType:"text",async:n.async};null!=n.timeout&&(t.timeout=n.timeout),jQuery.ajax(t)}function updraft_delete(t,e,a){jQuery("#updraft_delete_timestamp").val(t),jQuery("#updraft_delete_nonce").val(e),a?jQuery("#updraft-delete-remote-section, #updraft_delete_remote").prop("disabled",!1).show():jQuery("#updraft-delete-remote-section, #updraft_delete_remote").hide().attr("disabled","disabled"),(-1<t.indexOf(",")?(jQuery("#updraft_delete_question_singular").hide(),jQuery("#updraft_delete_question_plural")):(jQuery("#updraft_delete_question_plural").hide(),jQuery("#updraft_delete_question_singular"))).show(),jQuery("#updraft-delete-modal").dialog("open")}function updraft_remote_storage_tab_activation(t){jQuery(".updraftplusmethod").not(".error").hide(),jQuery(".remote-tab").data("active",!1),jQuery(".remote-tab").removeClass("nav-tab-active"),jQuery(".updraftplusmethod."+t).show(),jQuery(".remote-tab-"+t).data("active",!0),jQuery(".remote-tab-"+t).addClass("nav-tab-active")}function updraft_scroll_to_remote_storage_config(){var t=window.location.hash.match(/#remote-storage-([A-Za-z]+)/);t&&updraftlion.remote_storage_methods[t[1]]&&(jQuery(".updraft_servicecheckbox").hasClass("multi")&&updraft_remote_storage_tab_activation(t[1]),document.getElementById("remote-storage-"+t[1]).scrollIntoView())}function updraft_setup_remote_storage_config_link(){jQuery(".updraftplus-remote-storage-link").on("click",function(t){"settings"==updraftlion.tab&&(t.preventDefault(),updraft_open_main_tab("settings"),window.location.href=jQuery(this).attr("href"),updraft_scroll_to_remote_storage_config())})}function set_email_report_storage_interface(t){jQuery("#cb_not_email_storage_label").css("display",!0===t?"none":"inline"),jQuery("#cb_email_storage_label").css("display",!0===t?"inline":"none"),!0===t?jQuery("#updraft-navtab-settings-content #updraft_report_row_no_addon input#updraft_email").on("click",function(t){return!1}):jQuery("#updraft-navtab-settings-content #updraft_report_row_no_addon input#updraft_email").prop("onclick",null).off("click"),jQuery("#updraft-navtab-settings-content #updraft_report_row_no_addon input#updraft_email").is(":checked")||jQuery("#updraft-navtab-settings-content #updraft_report_row_no_addon input#updraft_email").prop("checked",t),jQuery("#updraft-navtab-settings-content #updraft_report_row_no_addon input#updraft_email").prop("disabled",t);var e=jQuery("#updraft-navtab-settings-content #updraft_report_row_no_addon input#updraft_email").val();jQuery('#updraft-navtab-settings-content #updraft_report_row_no_addon label.email_report input[type="hidden"]').remove(),!0===t&&jQuery("#updraft-navtab-settings-content #updraft_report_row_no_addon label.email_report input#updraft_email").after('<input type="hidden" name="updraft_email" value="'+e+'">')}function updraft_check_overduecrons(){updraft_send_command("check_overdue_crons",null,function(t){if(t&&t.hasOwnProperty("m")&&Array.isArray(t.m))for(var e in t.m)jQuery("#updraft-insert-admin-warning").append(t.m[e])},{alert_on_error:!1})}function updraft_remote_storage_tabs_setup(){var t,r=0,n=jQuery(".updraft_servicecheckbox:checked"),e=(jQuery(n).each(function(t,e){var a=jQuery(e).val();jQuery(".error.updraftplusmethod."+a).show(),"updraft_servicecheckbox_none"!=jQuery(e).attr("id")&&r++,jQuery(".remote-tab-"+a).show(),t==jQuery(n).length-1&&updraft_remote_storage_tab_activation(a)}),0<r?(jQuery(".updraftplusmethod.none").hide(),jQuery("#remote_storage_tabs").show()):jQuery("#remote_storage_tabs").hide(),jQuery(document).on("keyup",function(t){32!==t.keyCode&&13!==t.keyCode||jQuery(document.activeElement).is("input.labelauty + label")&&(t=jQuery(document.activeElement).attr("for"))&&jQuery("#"+t).trigger("change")}),jQuery(".updraft_servicecheckbox").on("change",function(){var t=jQuery(this).attr("id");"updraft_servicecheckbox_"==t.substring(0,24)&&null!=(t=t.substring(24))&&""!=t&&(jQuery(this).is(":checked")?(r++,jQuery(".error.updraftplusmethod."+t).show(),jQuery(".remote-tab-"+t).fadeIn(),updraft_remote_storage_tab_activation(t),jQuery("#updraft-navtab-settings-content #updraft_report_row_no_addon").length&&"email"===t&&set_email_report_storage_interface(!0)):(r--,jQuery(".error.updraftplusmethod."+t).hide(),jQuery(".remote-tab-"+t).hide(),1==jQuery(".remote-tab-"+t).data("active")&&updraft_remote_storage_tab_activation(jQuery(".remote-tab:visible").last().attr("name")),jQuery("#updraft-navtab-settings-content #updraft_report_row_no_addon").length&&"email"===t&&set_email_report_storage_interface(!1))),r<=0?(jQuery(".updraftplusmethod.none").fadeIn(),jQuery("#remote_storage_tabs").hide()):(jQuery(".updraftplusmethod.none").hide(),jQuery("#remote_storage_tabs").show())}),jQuery(".updraft_servicecheckbox:not(.multi)").on("change",function(){set_email_report_storage_interface(!1);var t=jQuery(this).attr("value");jQuery(this).is(":not(:checked)")?(jQuery(".updraftplusmethod."+t).hide(),jQuery(".updraftplusmethod.none").fadeIn()):(jQuery(".updraft_servicecheckbox").not(this).prop("checked",!1),"email"===t&&set_email_report_storage_interface(!0))}),jQuery(".updraft_servicecheckbox"));"function"==typeof e.labelauty&&(e.labelauty(),e=jQuery("label[for=updraft_servicecheckbox_updraftvault]"),t=jQuery('<div class="udp-info"><span class="info-trigger">?</span><div class="info-content-wrapper"><div class="info-content">'+updraftlion.updraftvault_info+"</div></div></div>"),e.append(t))}function updraft_remote_storage_test(t,a,e){var n,e=e?(n=jQuery("#updraft-"+t+"-test-"+e),".updraftplusmethod."+t+"-"+e):(n=jQuery("#updraft-"+t+"-test"),".updraftplusmethod."+t),o=n.data("method_label"),d=(n.html(updraftlion.testing_settings.replace("%s",o)),{method:t});jQuery("#updraft-navtab-settings-content "+e+" input[data-updraft_settings_test], #updraft-navtab-settings-content .expertmode input[data-updraft_settings_test]").each(function(t,e){var a,r=jQuery(e).data("updraft_settings_test"),n=jQuery(e).attr("type");r&&(n||(console.log("UpdraftPlus: settings test input item with no type found"),console.log(e),n="text"),a=null,"checkbox"==n?a=jQuery(e).is(":checked")?1:0:"text"==n||"password"==n||"hidden"==n?a=jQuery(e).val():(console.log("UpdraftPlus: settings test input item with unrecognised type ("+n+") found"),console.log(e)),d[r]=a)}),jQuery("#updraft-navtab-settings-content "+e+" textarea[data-updraft_settings_test], #updraft-navtab-settings-content "+e+" select[data-updraft_settings_test]").each(function(t,e){var a=jQuery(e).data("updraft_settings_test");d[a]=jQuery(e).val()}),updraft_send_command("test_storage_settings",d,function(t,e){n.html(updraftlion.test_settings.replace("%s",o)),void 0!==(a=void 0!==a&&0!=a?a.call(this,t,e,d):a)&&!1===a&&(t.output=t.output.replaceAll("&quot;",'"'),alert(updraftlion.settings_test_result.replace("%s",o)+" "+t.output),t.hasOwnProperty("data"))&&console.log(t.data)},{error_callback:function(t,e,a,r){n.html(updraftlion.test_settings.replace("%s",o)),void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),alert(r.fatal_error_message)):(r="updraft_send_command: error: "+e+" ("+a+")",console.log(r),alert(r),console.log(t))}})}function backupnow_whichfiles_checked(a){return jQuery('#backupnow_includefiles_moreoptions input[type="checkbox"]').each(function(t){var e;jQuery(this).is(":checked")&&"updraft_include_"==(e=jQuery(this).attr("name")).substring(0,16)&&(e=e.substring(16),""!=a&&(a+=","),a+=e)}),a}function backupnow_whichtables_checked(t){var e=!1;return jQuery("#backupnow_database_moreoptions .updraft_db_entity").each(function(t){(!jQuery(this).is(":checked")||jQuery(this).is(":checked")&&jQuery(this).data("non_wp_table"))&&(e=!0)}),t=jQuery("input[name^='updraft_include_tables_']").serializeArray(),!e||t}function updraft_deleteallselected(){var a="",r="",n=0;jQuery("#updraft-navtab-backups-content .updraft_existing_backups .updraft_existing_backups_row.backuprowselected").each(function(t){0;var e=jQuery(this).data("nonce"),e=(r&&(r+=","),r+=e,jQuery(this).data("key"));a&&(a+=","),a+=e,jQuery(this).find(".updraftplus-remove").data("hasremote")&&n++}),updraft_delete(a,r,n)}function updraft_open_main_tab(e){updraftlion.main_tabs_keys.forEach(function(t){e==t?(jQuery("#updraft-navtab-"+t+"-content").show(),jQuery("#updraft-navtab-"+t).addClass("nav-tab-active")):(jQuery("#updraft-navtab-"+t+"-content").hide(),jQuery("#updraft-navtab-"+t).removeClass("nav-tab-active")),updraft_console_focussed_tab=e})}function updraft_openrestorepanel(t){updraft_historytimertoggle(t),updraft_open_main_tab("backups")}function updraft_delete_old_dirs(){return!0}function updraft_initiate_restore(t){jQuery('#updraft-navtab-backups-content .updraft_existing_backups .restore-button button[data-backup_timestamp="'+t+'"]').trigger("click")}function updraft_restore_setoptions(n){jQuery('input[name="updraft_restore[]"]').each(function(t,e){var a=jQuery(e).val(),r=new RegExp("/"+a+"=([0-9,]+)"),r=n.match(r);r?(jQuery(e).prop("disabled",!1).data("howmany",r[1]).parent().show(),"db"==a&&0,jQuery(e).is(":checked")&&jQuery("#updraft_restorer_"+a+"options").show()):jQuery(e).attr("disabled","disabled").parent().hide()});n.match(/dbcrypted=1/)?(jQuery("#updraft_restore_db").data("encrypted",1),jQuery(".updraft_restore_crypteddb").show()):(jQuery("#updraft_restore_db").data("encrypted",0),jQuery(".updraft_restore_crypteddb").hide()),jQuery("#updraft_restore_db").trigger("change");var t=n.match(/meta_foreign=([12])/);t?jQuery("#updraft_restore_meta_foreign").val(t[1]):jQuery("#updraft_restore_meta_foreign").val("0")}function updraft_backup_dialog_open(t){t=void 0===t?"new":t,0==jQuery("#updraftplus_incremental_backup_link").data("incremental")&&"incremental"==t?(jQuery("#updraft-backupnow-modal .incremental-free-only").show(),t="new"):jQuery("#updraft-backupnow-modal .incremental-backups-only, #updraft-backupnow-modal .incremental-free-only").hide(),jQuery("#backupnow_includefiles_moreoptions").hide(),updraft_settings_form_changed&&!window.confirm(updraftlion.unsavedsettingsbackup)||(jQuery("#backupnow_label").val(""),"incremental"==t?(update_file_entities_checkboxes(!0,impossible_increment_entities),jQuery("#backupnow_includedb").prop("checked",!1),jQuery("#backupnow_includefiles").prop("checked",!0),jQuery("#backupnow_includefiles_label").text(updraftlion.files_incremental_backup),jQuery("#updraft-backupnow-modal .new-backups-only").hide(),jQuery("#updraft-backupnow-modal .incremental-backups-only").show()):(update_file_entities_checkboxes(!1,impossible_increment_entities),jQuery("#backupnow_includedb").prop("checked",!0),jQuery("#backupnow_includefiles_label").text(updraftlion.files_new_backup),jQuery("#updraft-backupnow-modal .new-backups-only").show(),jQuery("#updraft-backupnow-modal .incremental-backups-only").hide()),jQuery("#updraft-backupnow-modal").data("backup-type",t),jQuery("#updraft-backupnow-modal").dialog("open"))}function update_file_entities_checkboxes(t,e){t?jQuery(e).each(function(t,e){jQuery("#backupnow_files_updraft_include_"+e).prop("checked",!1),jQuery("#backupnow_files_updraft_include_"+e).prop("disabled",!0)}):jQuery('#backupnow_includefiles_moreoptions input[type="checkbox"]').each(function(t){var e=jQuery(this).attr("name");"updraft_include_"==e.substring(0,16)&&(e=e.substring(16),void 0===jQuery("#backupnow_files_updraft_include_"+e).data("force_disabled")?(jQuery("#backupnow_files_updraft_include_"+e).prop("disabled",!1),jQuery("#updraft_include_"+e).is(":checked")&&jQuery("#backupnow_files_updraft_include_"+e).prop("checked",!0)):(jQuery("#backupnow_files_updraft_include_"+e).prop("disabled",!0),jQuery("#backupnow_files_updraft_include_"+e).prop("checked",!1)))})}var impossible_increment_entities,updraft_poplog_log_nonce,onlythesefileentities=backupnow_whichfiles_checked(""),updraft_restore_stage=(""==onlythesefileentities?jQuery("#backupnow_includefiles_moreoptions").show():jQuery("#backupnow_includefiles_moreoptions").hide(),1),lastlog_lastmessage="",lastlog_lastdata="",lastlog_jobs="",updraft_activejobs_nextupdate=(new Date).getTime()+1e3,updraft_page_is_visible=1,updraft_console_focussed_tab=updraftlion.tab,php_max_input_vars=0,skipped_db_scan=0,updraft_settings_form_changed=!1,save_button_added=!1;function load_save_button(){updraft_settings_form_changed&&!save_button_added&&(save_button_added=!0,jQuery("#updraft-navtab-settings-content").prepend('<input style="position:fixed;top:46px; right:20px;z-index: 999999;" type="button" class="button-primary" id="updraftplus-floating-settings-save" value="'+updraftlion.save_changes+'">'),jQuery("#updraft-navtab-settings-content").one("click","#updraftplus-floating-settings-save",function(){jQuery("#updraftplus-settings-save").trigger("click"),jQuery("#updraftplus-floating-settings-save").remove(),save_button_added=!1}),jQuery("#updraftplus-settings-save").one("click",function(){jQuery("#updraftplus-floating-settings-save").remove(),save_button_added=!1}))}function updraft_check_page_visibility(t){"hidden"==document.visibilityState?updraft_page_is_visible=0:(updraft_page_is_visible=1)!==t&&jQuery("#updraft-navtab-backups-content").length&&updraft_activejobs_update(!0)}window.onbeforeunload=function(t){if(updraft_settings_form_changed)return updraftlion.unsavedsettings},void 0!==document.hidden&&document.addEventListener("visibilitychange",function(){updraft_check_page_visibility(0)},!1),updraft_check_page_visibility(1);var updraft_activejobs_update_timer,temporary_clone_timeout,updraft_poplog_log_pointer=0,updraft_poplog_lastscroll=-1,updraft_last_forced_jobid=-1,updraft_last_forced_resumption=-1,updraft_last_forced_when=-1,updraft_backupnow_nonce="",updraft_activejobslist_backupnownonce_only=0,updraft_inpage_hasbegun=0,updraft_aborted_jobs=[],updraft_clone_jobs=[],updraft_backups_selection={};function setup_migrate_tabs(){jQuery("#updraft_migrate .updraft_migrate_widget_module_content").each(function(t,e){var a=jQuery(e).find("h3").first().html(),r=jQuery(".updraft_migrate_intro");jQuery('<button class="button button-primary button-hero" />').html(a).appendTo(r).on("click",function(t){t.preventDefault(),jQuery(e).show(),r.hide()})})}function updraft_backupnow_inpage_go(t,e,a,r,n,o,d){r=void 0===r?0:r,n=void 0===n?0:n,o=void 0===o?0:o,d=void 0===d?updraftlion.automaticbackupbeforeupdate:d,updraft_console_focussed_tab="backups",updraft_inpage_success_callback=t,updraft_activejobs_update_timer=setInterval(function(){updraft_activejobs_update(!1)},1250);t=jQuery("#updraft-backupnow-inpage-modal").length;t&&jQuery("#updraft-backupnow-inpage-modal").dialog("option","buttons",{}),jQuery("#updraft_inpage_prebackup").hide(),t&&jQuery("#updraft-backupnow-inpage-modal").dialog("open"),jQuery("#updraft_inpage_backup").show(),updraft_activejobslist_backupnownonce_only=1,updraft_inpage_hasbegun=0,updraft_backupnow_go(r,n,o,e,a,d,"")}function updraft_get_downloaders(){var a="";return jQuery(".ud_downloadstatus .updraftplus_downloader, #ud_downloadstatus2 .updraftplus_downloader, #ud_downloadstatus3 .updraftplus_downloader").each(function(t,e){e=jQuery(e).data("downloaderfor");"object"==typeof e&&(""!=a&&(a+=":"),a=a+e.base+","+e.nonce+","+e.what+","+e.index)}),a}function updraft_poll_get_parameters(){var t={downloaders:updraft_get_downloaders()};try{jQuery("#updraft-poplog").dialog("isOpen")&&(t.log_fetch=1,t.log_nonce=updraft_poplog_log_nonce,t.log_pointer=updraft_poplog_log_pointer)}catch(t){console.log(t)}return updraft_activejobslist_backupnownonce_only&&void 0!==updraft_backupnow_nonce&&""!=updraft_backupnow_nonce&&(t.thisjobonly=updraft_backupnow_nonce),0!==jQuery("#updraftplus_ajax_restore_job_id").length&&(t.updraft_credentialtest_nonce=updraft_credentialtest_nonce),t}(n=>{updraft_backups_selection.toggle=function(t){n(t).is(".backuprowselected")?this.deselect(t):this.select(t)},updraft_backups_selection.select=function(t){n(t).addClass("backuprowselected"),n(t).find(".backup-select input").prop("checked",!0),this.checkSelectionStatus()},updraft_backups_selection.deselect=function(t){n(t).removeClass("backuprowselected"),n(t).find(".backup-select input").prop("checked",!1),this.checkSelectionStatus()},updraft_backups_selection.selectAll=function(){n("#updraft-navtab-backups-content .updraft_existing_backups .updraft_existing_backups_row").each(function(t,e){updraft_backups_selection.select(e)})},updraft_backups_selection.deselectAll=function(){n("#updraft-navtab-backups-content .updraft_existing_backups .updraft_existing_backups_row").each(function(t,e){updraft_backups_selection.deselect(e)})},updraft_backups_selection.checkSelectionStatus=function(){var t=n("#updraft-navtab-backups-content .updraft_existing_backups .updraft_existing_backups_row").length,e=n("#updraft-navtab-backups-content .updraft_existing_backups .updraft_existing_backups_row.backuprowselected").length;0<e?(n("#ud_massactions").addClass("active"),n(".js--deselect-all-backups, .js--delete-selected-backups").prop("disabled",!1)):(n("#ud_massactions").removeClass("active"),n(".js--deselect-all-backups, .js--delete-selected-backups").prop("disabled",!0)),t===e?n("#cb-select-all").prop("checked",!0):n("#cb-select-all").prop("checked",!1),t?n("#ud_massactions").show():n("#ud_massactions").hide()},updraft_backups_selection.selectAllInBetween=function(t){var e=this.firstMultipleSelectionIndex,a=t.rowIndex-1;this.firstMultipleSelectionIndex>t.rowIndex-1&&(e=t.rowIndex-1,a=this.firstMultipleSelectionIndex);for(var r=e;r<=a;r++)this.select(n("#updraft-navtab-backups-content .updraft_existing_backups .updraft_existing_backups_row").eq(r))},updraft_backups_selection.highlight_backup_rows=function(){void 0!==updraft_backups_selection.firstMultipleSelectionIndex&&(n(this).hasClass("range-selection")||n(this).hasClass("backuprowselected")||n(this).addClass("range-selection"),n(this).siblings().removeClass("range-selection"),updraft_backups_selection.firstMultipleSelectionIndex+1>this.rowIndex?n(this).nextUntil(".updraft_existing_backups_row.range-selection-start").addClass("range-selection"):updraft_backups_selection.firstMultipleSelectionIndex+1<this.rowIndex&&n(this).prevUntil(".updraft_existing_backups_row.range-selection-start").addClass("range-selection"))},updraft_backups_selection.unregister_highlight_mode=function(){void 0!==updraft_backups_selection.firstMultipleSelectionIndex&&(delete updraft_backups_selection.firstMultipleSelectionIndex,n("#updraft-navtab-backups-content .updraft_existing_backups .updraft_existing_backups_row").removeClass("range-selection range-selection-start"),n("#updraft-navtab-backups-content").off("mouseenter",".updraft_existing_backups .updraft_existing_backups_row",this.highlight_backup_rows),n("#updraft-navtab-backups-content").off("mouseleave",".updraft_existing_backups .updraft_existing_backups_row",this.highlight_backup_rows),n(document).off("mouseleave",this.unregister_highlight_mode))},updraft_backups_selection.register_highlight_mode=function(){n(document).on("mouseleave",updraft_backups_selection.unregister_highlight_mode),n("#updraft-navtab-backups-content").on("mouseenter",".updraft_existing_backups .updraft_existing_backups_row",updraft_backups_selection.highlight_backup_rows),n("#updraft-navtab-backups-content").on("mouseleave",".updraft_existing_backups .updraft_existing_backups_row",updraft_backups_selection.highlight_backup_rows)}})(jQuery);var updraftplus_activejobs_list_fatal_error_alert=!0;function updraft_activejobs_update(t){jQuery;var r,e=(new Date).getTime();0==t&&e<updraft_activejobs_nextupdate||(updraft_activejobs_nextupdate=e+5500,updraft_send_command("activejobs_list",r=updraft_poll_get_parameters(),function(t,e,a){updraft_process_status_check(t,a,r)},{type:"GET",error_callback:function(t,e,a,r){return void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),!0===updraftplus_activejobs_list_fatal_error_alert&&(updraftplus_activejobs_list_fatal_error_alert=!1,alert(this.alert_done+" "+r.fatal_error_message))):(console.error(e==a?a:a+" ("+e+")"),console.log(t)),!1}}))}function updraft_show_success_modal(t){"string"==typeof t&&(t={message:t});t=jQuery.extend({icon:"yes",close:updraftlion.close,message:"",classes:"success"},t);jQuery.blockUI({css:{width:"300px",border:"none","border-radius":"10px",left:"calc(50% - 150px)"},message:'<div class="updraft_success_popup '+t.classes+'"><span class="dashicons dashicons-'+t.icon+'"></span><div class="updraft_success_popup--message">'+t.message+'</div><button class="button updraft-close-overlay"><span class="dashicons dashicons-no-alt"></span>'+t.close+"</button></div>"}),setTimeout(jQuery.unblockUI,5e3),jQuery(".blockUI .updraft-close-overlay").on("click",function(){jQuery.unblockUI()})}function updraft_popuplog(t){var e=updraftlion.loading_log_file;t&&(e+=" (log."+t+".txt)"),jQuery("#updraft-poplog").dialog("option","title",e),jQuery("#updraft-poplog-content").html("<em>"+e+" ...</em> "),jQuery("#updraft-poplog").dialog("open"),updraft_send_command("get_log",t,function(t){updraft_poplog_log_pointer=t.pointer;var e="?page=updraftplus&action=downloadlog&force_download=1&updraftplus_backup_nonce="+(updraft_poplog_log_nonce=t.nonce),a=(jQuery("#updraft-poplog-content").html(t.log),{});a[updraftlion.downloadlogfile]=function(){window.location.href=e},a[updraftlion.close]=function(){jQuery(this).dialog("close")},jQuery("#updraft-poplog").dialog("option","buttons",a),jQuery("#updraft-poplog").dialog("option","title","log."+t.nonce+".txt"),updraft_poplog_lastscroll=-1},{type:"GET",timeout:6e4,error_callback:function(t,e,a,r){void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),jQuery("#updraft-poplog-content").append(r.fatal_error_message)):(r=e==a?a:a+" ("+e+")",jQuery("#updraft-poplog-content").append(r),console.log(t))}})}function updraft_showlastbackup(){updraft_send_command("get_fragment","last_backup_html",function(t){response=t.output,lastbackup_laststatus==response?setTimeout(function(){updraft_showlastbackup()},7e3):jQuery("#updraft_last_backup").html(response),lastbackup_laststatus=response},{type:"GET"})}var updraft_historytimer=0,calculated_diskspace=0,updraft_historytimer_notbefore=0,updraft_history_lastchecksum=!1;function updraft_historytimertoggle(t){updraft_historytimer&&1!=t?(clearTimeout(updraft_historytimer),updraft_historytimer=0):(updraft_updatehistory(0,0),updraft_historytimer=setInterval(function(){updraft_updatehistory(0,0)},3e4),calculated_diskspace||(updraftplus_diskspace(),calculated_diskspace=1))}function updraft_updatehistory(t,e,a,r){if("undefined"==typeof updraft_restore_screen||!updraft_restore_screen){void 0===a&&(a=jQuery("#updraft_debug_mode").is(":checked")?1:0);var n=Math.round((new Date).getTime()/1e3);if(1==t||1==e)updraft_historytimer_notbefore=n+30;else if(n<updraft_historytimer_notbefore&&void 0===r)return void console.log("Update history skipped: "+n.toString()+" < "+updraft_historytimer_notbefore.toString());void 0===r&&(r=0),1==t&&(1==e?(updraft_history_lastchecksum=!1,jQuery("#updraft-navtab-backups-content .updraft_existing_backups").html('<p style="text-align:center;"><em>'+updraftlion.rescanningremote+"</em></p>")):(updraft_history_lastchecksum=!1,jQuery("#updraft-navtab-backups-content .updraft_existing_backups").html('<p style="text-align:center;"><em>'+updraftlion.rescanning+"</em></p>"))),updraft_send_command("rescan",{operation:e?"remotescan":!!t&&"rescan",debug:a,backup_count:r},function(t){if(t.hasOwnProperty("logs_exist")&&t.logs_exist&&jQuery("#updraft_lastlogmessagerow .updraft-log-link").show(),t.hasOwnProperty("migrate_tab")&&t.migrate_tab&&(jQuery("#updraft-navtab-migrate").hasClass("nav-tab-active")||(jQuery("#updraft_migrate_tab_alt").html(""),jQuery("#updraft_migrate").replaceWith(jQuery(t.migrate_tab).find("#updraft_migrate")),setup_migrate_tabs())),t.hasOwnProperty("web_server_disk_space")&&(""==t.web_server_disk_space?(console.log("UpdraftPlus: web_server_disk_space is empty"),jQuery("#updraft-navtab-backups-content .updraft-server-disk-space").length&&jQuery("#updraft-navtab-backups-content .updraft-server-disk-space").slideUp("slow",function(){jQuery(this).remove()})):jQuery("#updraft-navtab-backups-content .updraft-server-disk-space").length?jQuery("#updraft-navtab-backups-content  .updraft-server-disk-space").replaceWith(t.web_server_disk_space):jQuery("#updraft-navtab-backups-content .updraft-disk-space-actions").prepend(t.web_server_disk_space)),update_backupnow_modal(t),t.hasOwnProperty("backupnow_file_entities")&&(impossible_increment_entities=t.backupnow_file_entities),null!=t.n&&jQuery("#updraft-existing-backups-heading").html(t.n),null!=t.t){if(null!=t.cksum){if(t.cksum==updraft_history_lastchecksum)return;updraft_history_lastchecksum=t.cksum}jQuery("#updraft-navtab-backups-content .updraft_existing_backups").html(t.t),updraft_backups_selection.checkSelectionStatus(),t.data&&console.log(t.data)}})}}function update_backupnow_modal(t){t.hasOwnProperty("modal_afterfileoptions")&&jQuery(".backupnow_modal_afterfileoptions").html(t.modal_afterfileoptions)}function updraft_exclude_entity_update(t){var e=[];jQuery("#updraft_include_"+t+"_exclude_container .updraft_exclude_entity_wrapper .updraft_exclude_entity_field").each(function(){var t=jQuery(this).data("val").toString().trim();""!=t&&e.push(t)}),jQuery("#updraft_include_"+t+"_exclude").val(e.join(","))}function updraft_is_unique_exclude_rule(t,e){return existing_exclude_rules_str=jQuery("#updraft_include_"+e+"_exclude").val(),existing_exclude_rules=existing_exclude_rules_str.split(","),!(-1<jQuery.inArray(t,existing_exclude_rules)&&(alert(updraftlion.duplicate_exclude_rule_error_msg),1))}var updraft_interval_week_val=!1,updraft_interval_month_val=!1;function updraft_intervals_monthly_or_not(t,e){var a="#updraft-navtab-settings-content #"+t,r="monthly"==e,n=!1;10<jQuery(a+" option").length&&(n=!0),(r||n)&&(r&&n?"monthly"==e&&(jQuery(".updraft_monthly_extra_words_"+t).remove(),jQuery(a).before('<span class="updraft_monthly_extra_words_'+t+'">'+updraftlion.day+" </span>").after('<span class="updraft_monthly_extra_words_'+t+'"> '+updraftlion.inthemonth+" </span>")):(jQuery(".updraft_monthly_extra_words_"+t).remove(),(r?(updraft_interval_week_val=jQuery(a+" option:selected").val(),jQuery(a).html(updraftlion.mdayselector).before('<span class="updraft_monthly_extra_words_'+t+'">'+updraftlion.day+" </span>").after('<span class="updraft_monthly_extra_words_'+t+'"> '+updraftlion.inthemonth+" </span>"),n=!1===updraft_interval_month_val?1:updraft_interval_month_val,n-=1,jQuery(a+" option").eq(n)):(updraft_interval_month_val=jQuery(a+" option:selected").val(),jQuery(a).html(updraftlion.dayselector),e=!1===updraft_interval_week_val?1:updraft_interval_week_val,jQuery(a+" option").eq(e))).prop("selected",!0)))}function updraft_check_same_times(){var t=0,e=jQuery("#updraft-navtab-settings-content .updraft_interval").val(),a=("manual"==e?jQuery("#updraft-navtab-settings-content .updraft_files_timings").hide():jQuery("#updraft-navtab-settings-content .updraft_files_timings").show(),"weekly"==e||"fortnightly"==e||"monthly"==e?(updraft_intervals_monthly_or_not("updraft_startday_files",e),jQuery("#updraft-navtab-settings-content #updraft_startday_files").show()):(jQuery(".updraft_monthly_extra_words_updraft_startday_files").remove(),jQuery("#updraft-navtab-settings-content #updraft_startday_files").hide()),jQuery("#updraft-navtab-settings-content .updraft_interval_database").val());"manual"==a&&(t=1,jQuery("#updraft-navtab-settings-content .updraft_db_timings").hide()),"weekly"==a||"fortnightly"==a||"monthly"==a?(updraft_intervals_monthly_or_not("updraft_startday_db",a),jQuery("#updraft-navtab-settings-content #updraft_startday_db").show()):(jQuery(".updraft_monthly_extra_words_updraft_startday_db").remove(),jQuery("#updraft-navtab-settings-content #updraft_startday_db").hide()),a==e?(jQuery("#updraft-navtab-settings-content .updraft_db_timings").hide(),0==t?jQuery("#updraft-navtab-settings-content .updraft_same_schedules_message").show():jQuery("#updraft-navtab-settings-content .updraft_same_schedules_message").hide()):(jQuery("#updraft-navtab-settings-content .updraft_same_schedules_message").hide(),0==t&&jQuery("#updraft-navtab-settings-content .updraft_db_timings").show())}function updraft_activejobs_delete(a){updraft_aborted_jobs[a]=1,jQuery("#updraft-jobid-"+a).closest(".updraft_row").addClass("deleting"),updraft_send_command("activejobs_delete",a,function(t){var e=jQuery("#updraft-jobid-"+a).closest(".updraft_row");e.addClass("deleting"),"Y"==t.ok?(jQuery("#updraft-jobid-"+a).html(t.m),e.remove(),jQuery("#updraft-backupnow-inpage-modal").dialog("isOpen")&&jQuery("#updraft-backupnow-inpage-modal").dialog("close"),updraft_show_success_modal({message:updraft_active_job_is_clone(a)?updraftlion.clone_backup_aborted:updraftlion.backup_aborted,icon:"no-alt",classes:"warning"})):"N"==t.ok?(e.removeClass("deleting"),alert(t.m)):(e.removeClass("deleting"),alert(updraftlion.unexpectedresponse),console.log(t))})}function updraftplus_diskspace_entity(e){jQuery("#updraft_diskspaceused_"+e).html("<em>"+updraftlion.calculating+"</em>"),updraft_send_command("get_fragment",{fragment:"disk_usage",data:e},function(t){jQuery("#updraft_diskspaceused_"+e).html(t.output)},{type:"GET"})}function updraft_active_job_is_clone(e){return updraft_clone_jobs.filter(function(t){return t==e}).length}function updraft_iframe_modal(t,e){jQuery("#updraft-iframe-modal-innards").html('<iframe width="100%" height="430px" src="'+ajaxurl+"?action=updraft_ajax&subaction="+t+"&nonce="+updraft_credentialtest_nonce+'"></iframe>'),jQuery("#updraft-iframe-modal").dialog({title:e,resizeOnWindowResize:!0,scrollWithViewport:!0,resizeAccordingToViewport:!0,useContentSize:!1,open:function(t,e){jQuery(this).dialog("option","width",780),jQuery(this).dialog("option","minHeight",260),500<jQuery(window).height()?jQuery(this).dialog("option","height",500):jQuery(this).dialog("option","height",jQuery(window).height()-30)}}).dialog("open")}function updraft_html_modal(t,e,a,r){jQuery("#updraft-iframe-modal-innards").html(t);t={};a<450&&(t[updraftlion.close]=function(){jQuery(this).dialog("close")}),jQuery("#updraft-iframe-modal").dialog({title:e,buttons:t,resizeOnWindowResize:!0,scrollWithViewport:!0,resizeAccordingToViewport:!0,useContentSize:!1,open:function(t,e){jQuery(this).dialog("option","width",a),jQuery(this).dialog("option","minHeight",260),jQuery(window).height()>r?jQuery(this).dialog("option","height",r):jQuery(this).dialog("option","height",jQuery(window).height()-30)}}).dialog("open")}function updraftplus_diskspace(){jQuery("#updraft-navtab-backups-content .updraft_diskspaceused").html("<em>"+updraftlion.calculating+"</em>"),updraft_send_command("get_fragment",{fragment:"disk_usage",data:"updraft"},function(t){jQuery("#updraft-navtab-backups-content .updraft_diskspaceused").html(t.output)},{type:"GET"})}"undefined"!=typeof updraft_siteurl&&setInterval(function(){jQuery.get(updraft_siteurl+"/wp-cron.php")},21e4);lastlog_lastmessage="";function updraftplus_deletefromserver(t,e,a){updraft_send_command("updraft_download_backup",{stage:"delete",timestamp:t,type:e,findex:a=a||0},null,{action:"updraft_download_backup",nonce:updraft_download_nonce,nonce_key:"_wpnonce"})}function updraftplus_downloadstage2(t,e,a){location.href=ajaxurl+"?_wpnonce="+updraft_download_nonce+"&timestamp="+t+"&type="+e+"&stage=2&findex="+a+"&action=updraft_download_backup"}function updraftplus_show_contents(t,e,a){updraft_html_modal('<div id="updraft_zip_files_container" class="hidden-in-updraftcentral" style="clear:left;"><div id="updraft_zip_info_container" class="updraft_jstree_info_container"><p><span id="updraft_zip_path_text">'+updraftlion.zip_file_contents_info+'</span> - <span id="updraft_zip_size_text"></span></p>'+updraftlion.browse_download_link+'</div><div id="updraft_zip_files_jstree_container"><input type="search" id="zip_files_jstree_search" name="zip_files_jstree_search" placeholder="'+updraftlion.search+'"><div id="updraft_zip_files_jstree" class="updraft_jstree"></div></div></div>',updraftlion.zip_file_contents,780,500),zip_files_jstree("zipbrowser",t,e,a)}function zip_files_jstree(a,r,n,o){jQuery("#updraft_zip_files_jstree").jstree({core:{multiple:!1,data:function(t,e){updraft_send_command("get_jstree_directory_nodes",{entity:a,node:t,timestamp:r,type:n,findex:o},function(t){t.hasOwnProperty("error")?alert(t.error):e.call(this,t.nodes)},{error_callback:function(t,e,a,r){void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),jQuery("#updraft_zip_files_jstree").html('<p style="color:red; margin: 5px;">'+r.fatal_error_message+"</p>"),alert(r.fatal_error_message)):(r="updraft_send_command: error: "+e+" ("+a+")",jQuery("#updraft_zip_files_jstree").html('<p style="color:red; margin: 5px;">'+r+"</p>"),console.log(r),alert(r),console.log(t))}})},error:function(t){alert(t),console.log(t)}},search:{show_only_matches:!0},plugins:["search","sort"]}),jQuery("#updraft_zip_files_jstree").on("ready.jstree",function(t,e){jQuery("#updraft-iframe-modal").dialog("option","title",updraftlion.zip_file_contents+": "+e.instance.get_node("#").children[0])});var t=!1;jQuery("#zip_files_jstree_search").on("keyup",function(){t&&clearTimeout(t),t=setTimeout(function(){var t=jQuery("#zip_files_jstree_search").val();jQuery("#updraft_zip_files_jstree").jstree(!0).search(t)},250)}),jQuery("#updraft_zip_files_jstree").on("changed.jstree",function(t,e){jQuery("#updraft_zip_path_text").text(e.node.li_attr.path),e.node.li_attr.size?(jQuery("#updraft_zip_size_text").text(e.node.li_attr.size),jQuery("#updraft_zip_download_item").show()):(jQuery("#updraft_zip_size_text").text(""),jQuery("#updraft_zip_download_item").hide())}),jQuery("#updraft_zip_download_item").on("click",function(t){t.preventDefault(),updraft_send_command("get_zipfile_download",{path:jQuery("#updraft_zip_path_text").text(),timestamp:r,type:n,findex:o},function(t){t.hasOwnProperty("error")?alert(t.error):t.hasOwnProperty("path")?location.href=ajaxurl+"?_wpnonce="+updraft_download_nonce+"&timestamp="+r+"&type="+n+"&stage=2&findex="+o+"&filepath="+t.path+"&action=updraft_download_backup":alert(updraftlion.download_timeout)},{error_callback:function(t,e,a,r){void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),alert(r.fatal_error_message)):(r="updraft_send_command: error: "+e+" ("+a+")",console.log(r),alert(r),console.log(t))}})})}function remove_updraft_downloader(t,e){jQuery(t).closest(".updraftplus_downloader").fadeOut().remove(),0==jQuery(".updraftplus_downloader_container_"+e+" .updraftplus_downloader").length&&jQuery(".updraftplus_downloader_container_"+e).remove()}function updraft_downloader(t,e,a,r,n,o,d){"string"!=typeof n&&(n=n.toString()),jQuery(".ud_downloadstatus").show();var n=n.split(","),o=o||e,s=jQuery("#updraft-navtab-backups-content .uddownloadform_"+a+"_"+e+"_"+n[0]).data("wp_nonce").toString();jQuery(".updraftplus_downloader_container_"+a).length||(jQuery(r).append('<div class="updraftplus_downloader_container_'+a+' postbox"></div>'),jQuery(".updraftplus_downloader_container_"+a).append('<strong style="clear:left; padding: 8px; margin-top: 4px;">'+updraftlion.download+" "+a+" ("+o+"):</strong>"));for(var u=0;u<n.length;u++){var i=t+e+"_"+a+"_"+n[u],p="."+i,l=parseInt(n[u]),l=(l++,0==n[u]?"":" ("+l+")");jQuery(p).length||(jQuery(".updraftplus_downloader_container_"+a).append('<div style="clear:left; padding: 8px; margin-top: 4px;" class="'+i+' updraftplus_downloader"><button onclick="remove_updraft_downloader(this, \''+a+'\');" type="button" style="float:right; margin-bottom: 8px;" class="ud_downloadstatus__close" aria-label="Close"><span class="dashicons dashicons-no-alt"></span></button><strong>'+a+l+'</strong>:<div class="raw">'+updraftlion.begunlooking+'</div><div class="file '+i+'_st"><div class="dlfileprogress" style="width: 0;"></div></div></div>'),jQuery(p).data("downloaderfor",{base:t,nonce:e,what:a,index:n[u]}),setTimeout(function(){updraft_activejobs_update(!0)},1500)),jQuery(p).data("lasttimebegan",(new Date).getTime())}return updraft_send_command("updraft_download_backup",{type:a,timestamp:e,findex:n},null,{action:"updraft_download_backup",nonce_key:"_wpnonce",nonce:s,timeout:1e4,async:d=!!d}),!1}function ud_parse_json(e,a){if(!(a=void 0!==a))try{return JSON.parse(e)}catch(t){console.log("UpdraftPlus: Exception when trying to parse JSON (1) - will attempt to fix/re-parse based upon first/last curly brackets"),console.log(e)}var r=e.indexOf("{"),n=e.lastIndexOf("}");if(-1<r&&-1<n){var t=e.slice(r,n+1);try{var o=JSON.parse(t);return a||console.log("UpdraftPlus: JSON re-parse successful"),a?{parsed:o,json_start_pos:r,json_last_pos:n+1}:o}catch(t){console.log("UpdraftPlus: Exception when trying to parse JSON (2) - will attempt to fix/re-parse based upon bracket counting");for(var d=r,s=0,u="",i=!1;(0<s||d==r)&&d<=n;){var p=e.charAt(d);i||"{"!=p?i||"}"!=p?'"'==p&&"\\"!=u&&(i=!i):s--:s++,u=p,d++}console.log("Started at cursor="+r+", ended at cursor="+d+" with result following:"),console.log(e.substring(r,d));try{o=JSON.parse(e.substring(r,d));return console.log("UpdraftPlus: JSON re-parse successful"),a?{parsed:o,json_start_pos:r,json_last_pos:d}:o}catch(t){throw t}}}throw"UpdraftPlus: could not parse the JSON"}function updraft_restorer_checkstage2(t){0<jQuery("#ud_downloadstatus2 .file").length?t&&alert(updraftlion.stilldownloading):(jQuery(".updraft-restore--next-step").prop("disabled",!0),jQuery("#updraft-restore-modal-stage2a").html('<span class="dashicons dashicons-update rotate"></span> '+updraftlion.preparing_backup_files),updraft_send_command("restore_alldownloaded",{timestamp:jQuery("#updraft_restore_timestamp").val(),restoreopts:jQuery("#updraft_restore_form").serialize()},function(e,t,a){var r=null;jQuery("#updraft_restorer_restore_options").val(""),jQuery(".updraft-restore--next-step").prop("disabled",!1);try{if(null==e)jQuery("#updraft-restore-modal-stage2a").html(updraftlion.emptyresponse);else{var n=e.m;if(""!=e.w&&(n=n+'<div class="udp-notice notice-warning"><p><span class="dashicons dashicons-warning"></span> <strong>'+updraftlion.warnings+"</strong></p>"+e.w+"</div>"),""!=e.e?n=n+'<div class="udp-notice notice-error"><p><span class="dashicons dashicons-dismiss"></span> <strong>'+updraftlion.errors+"</strong></p>"+e.e+"</div>":updraft_restore_stage=3,e.hasOwnProperty("i")){try{(r=ud_parse_json(e.i)).hasOwnProperty("addui")&&(console.log("Further UI options are being displayed"),n+='<div id="updraft_restoreoptions_ui">'+r.addui+"</div>","object"==typeof JSON)&&"function"==typeof JSON.stringify&&(delete r.addui,e.i=JSON.stringify(r)),r.hasOwnProperty("php_max_input_vars")&&(php_max_input_vars=parseInt(r.php_max_input_vars)),r.hasOwnProperty("skipped_db_scan")&&(skipped_db_scan=parseInt(r.skipped_db_scan))}catch(t){console.log(t),console.log(e)}jQuery("#updraft_restorer_backup_info").val(e.i)}else jQuery("#updraft_restorer_backup_info").val();jQuery("#updraft-restore-modal-stage2a").html(n),jQuery(".updraft-restore--next-step").text(updraftlion.restore),0<jQuery("#updraft-restore-modal-stage2a .updraft_select2").length&&jQuery("#updraft-restore-modal-stage2a .updraft_select2").select2()}}catch(t){console.log(a),console.log(t),jQuery("#updraft-restore-modal-stage2a").text(updraftlion.jsonnotunderstood+" "+updraftlion.errordata+": "+a).html()}},{error_callback:function(t,e,a,r){void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),jQuery("#updraft-restore-modal-stage2a").html('<p style="color: red;">'+r.fatal_error_message+"</p>"),alert(r.fatal_error_message)):(r="updraft_send_command: error: "+e+" ("+a+")",jQuery("#updraft-restore-modal-stage2a").html('<p style="color: red;">'+r+"</p>"),console.log(r),alert(r),console.log(t))}}))}function updraft_downloader_status(t,e,a,r){}function updraft_downloader_status_update(t,n){var o=0;return jQuery(t).each(function(t,e){var a,r;""!=e.base&&(a="."+(e.base+e.timestamp+"_"+e.what+"_"+e.findex),null!=e.e?(jQuery(a+" .raw").html("<strong>"+updraftlion.error+"</strong> "+e.e),console.log(e)):null!=e.p?(jQuery(a+"_st .dlfileprogress").width(e.p+"%"),null!=e.a&&0<e.a&&(r=(new Date).getTime()-jQuery(a).data("lasttimebegan"),90<e.a)&&6e4<r&&(console.log(e.timestamp+" "+e.what+" "+e.findex+": restarting download: file_age="+e.a+", sincelastrestart_ms="+r),jQuery(a).data("lasttimebegan",(new Date).getTime()),r=jQuery("#updraft-navtab-backups-content .uddownloadform_"+e.what+"_"+e.timestamp+"_"+e.findex),updraft_send_command("updraft_download_backup",{type:e.what,timestamp:e.timestamp,findex:e.findex},null,{action:"updraft_download_backup",nonce_key:"_wpnonce",nonce:r.data("wp_nonce").toString(),timeout:1e4}),jQuery(a).data("lasttimebegan",(new Date).getTime())),null!=e.m&&(100<=e.p&&"udrestoredlstatus_"==e.base?(jQuery(a+" .raw").html(e.m),jQuery(a).fadeOut("slow",function(){remove_updraft_downloader(this,e.what),updraft_restorer_checkstage2(0)})):100<=e.p&&"udclonedlstatus_"==e.base?(jQuery(a+" .raw").html(e.m),jQuery(a).fadeOut("slow",function(){remove_updraft_downloader(this,e.what)})):e.p<100||"uddlstatus_"!=e.base?jQuery(a+" .raw").html(e.m):(r=updraftlion.fileready+" (size: "+convert_numeric_size_to_text(e.s)+"). "+updraftlion.actions+':\t\t\t\t<button class="button" type="button" onclick="updraftplus_downloadstage2(\''+e.timestamp+"', '"+e.what+"', '"+e.findex+"')\">"+updraftlion.downloadtocomputer+'</button> \t\t\t\t<button class="button" id="uddownloaddelete_'+e.timestamp+"_"+e.what+'" type="button" onclick="updraftplus_deletefromserver(\''+e.timestamp+"', '"+e.what+"', '"+e.findex+"')\">"+updraftlion.deletefromserver+"</button>",e.hasOwnProperty("can_show_contents")&&e.can_show_contents&&(r+=' <button class="button" type="button" onclick="updraftplus_show_contents(\''+e.timestamp+"', '"+e.what+"', '"+e.findex+"')\">"+updraftlion.browse_contents+"</button>"),jQuery(a+" .raw").html(r),jQuery(a+"_st").remove()))):null!=e.m?jQuery(a+" .raw").html(e.m):(jQuery(a+" .raw").html(updraftlion.jsonnotunderstood+" ("+n+")"),o=1))}),o}function convert_numeric_size_to_text(t){return t<1024?t+" b":t<1048576?(t/1024).toFixed(2)+" KB":t<1073741824?(t/1048576).toFixed(2)+" MB":(t/1073741824).toFixed(2)+" GB"}function updraft_backupnow_go(t,e,a,r,n,o,d,s){var u,t={backupnow_nodb:t,backupnow_nofiles:e,backupnow_nocloud:a,backupnow_label:o,extradata:n};""!=r&&(t.onlythisfileentity=r),""!=d&&(t.onlythesetableentities=d),""!=s&&(t.only_these_cloud_services=s),t.always_keep=void 0!==n.always_keep?n.always_keep:0,delete n.always_keep,t.incremental=void 0!==n.incremental?n.incremental:0,delete n.incremental,t.db_anon_all=void 0!==n.db_anon&&void 0!==n.db_anon.all?n.db_anon.all:0,t.db_anon_non_staff=void 0!==n.db_anon&&void 0!==n.db_anon.non_staff?n.db_anon.non_staff:0,t.db_anon_wc_orders=void 0!==n.db_anon&&void 0!==n.db_anon.wc_orders?n.db_anon.wc_orders:0,void 0!==n.db_anon&&(delete n.db_anon.all,delete n.db_anon.non_staff,delete n.db_anon.wc_orders),jQuery(".updraft_requeststart").length||((u=jQuery('<div class="updraft_requeststart" />').html('<span class="spinner"></span>'+updraftlion.requeststart)).data("remove",!1),setTimeout(function(){u.data("remove",!0)},3e3),setTimeout(function(){u.remove()},75e3),jQuery("#updraft_activejobsrow").before(u)),updraft_activejobslist_backupnownonce_only=1,updraft_send_command("backupnow",t,function(t){t.hasOwnProperty("error")?(jQuery(".updraft_requeststart").remove(),alert(t.error)):(jQuery("#updraft_backup_started").html(t.m),t.hasOwnProperty("nonce")&&(updraft_backupnow_nonce=t.nonce,console.log("UpdraftPlus: ID of started job: "+updraft_backupnow_nonce)),setTimeout(function(){updraft_activejobs_update(!0)},500))})}function updraft_process_status_check(t,e,a){if(t.hasOwnProperty("fatal_error"))console.error(t.fatal_error_message),!0===updraftplus_activejobs_list_fatal_error_alert&&(updraftplus_activejobs_list_fatal_error_alert=!1,alert(this.alert_done+" "+t.fatal_error_message));else try{t.hasOwnProperty("l")&&(t.l?(jQuery("#updraft_lastlogmessagerow").show(),jQuery("#updraft_lastlogcontainer").html(t.l)):(jQuery("#updraft_lastlogmessagerow").hide(),jQuery("#updraft_lastlogcontainer").html("("+updraftlion.nothing_yet_logged+")"))),updraftlion.hasOwnProperty("hosting_restriction")&&updraftlion.hosting_restriction instanceof Array&&(updraftlion.hosting_restriction.length=0,t.hasOwnProperty("hosting_restriction"))&&(t.hosting_restriction&&t.hosting_restriction.includes("only_one_backup_per_month")&&updraftlion.hosting_restriction.push("only_one_backup_per_month"),t.hosting_restriction)&&t.hosting_restriction.includes("only_one_incremental_per_day")&&updraftlion.hosting_restriction.push("only_one_incremental_per_day"),jQuery("#updraft-wrap #updraft-navtab-settings-content").is(":hidden")||t.hasOwnProperty("automatic_updates")&&jQuery('input[name="updraft_auto_updates"]').prop("checked",t.automatic_updates);var r,n,o=-1,d=jQuery(".updraft_requeststart"),s=(t.j&&d.length&&d.data("remove")&&d.remove(),jQuery(t.j)),u=(s.find(".updraft_jobtimings").each(function(t,e){var a,e=jQuery(e);e.data("jobid")&&(a=e.data("jobid"),e=e.closest(".updraft_row"),updraft_aborted_jobs[a])&&e.hide()}),jQuery("#updraft_activejobsrow").html(s),s.find('.job-id[data-isclone="1"]'));0<u.length&&(0==jQuery(".updraftclone_action_box .updraftclone_network_info").length&&0<jQuery("#updraft_activejobsrow .job-id .updraft_clone_url").length&&updraft_send_command("get_clone_network_info",{clone_url:jQuery("#updraft_activejobsrow .job-id .updraft_clone_url").data("clone_url")},function(t){t.hasOwnProperty("html")&&jQuery(".updraftclone_action_box").html(t.html)}),jQuery("#updraft_clone_activejobsrow").empty(),u.each(function(t,e){jQuery(e).closest(".updraft_row").appendTo(jQuery("#updraft_clone_activejobsrow"))})),jQuery("#updraft_activejobs .updraft_jobtimings").each(function(t,e){var a,r,n,e=jQuery(e);e.data("lastactivity")&&e.data("jobid")&&(a=e.data("jobid"),r=e.data("lastactivity"),(-1==o||r<o)&&(o=r),n=e.data("nextresumptionafter"),e=e.data("nextresumption"),timenow=(new Date).getTime(),50<r)&&0<e&&n<-30&&timenow>updraft_last_forced_when+1e5&&(updraft_last_forced_jobid!=a||e!=updraft_last_forced_resumption)&&(updraft_last_forced_resumption=e,updraft_last_forced_jobid=a,updraft_last_forced_when=timenow,console.log("UpdraftPlus: force resumption: job_id="+a+", resumption="+e),updraft_send_command("forcescheduledresumption",{resumption:e,job_id:a},function(t){console.log(t)},{json_parse:!1,alert_on_error:!1}))}),timenow=(new Date).getTime(),updraft_activejobs_nextupdate=timenow+18e4,1==updraft_page_is_visible&&"backups"==updraft_console_focussed_tab&&(updraft_activejobs_nextupdate=-1<o?o<5?timenow+1750:timenow+5e3:lastlog_lastdata==e?timenow+7500:timenow+1750),0<u.length&&(updraft_activejobs_nextupdate=timenow+6e3),lastlog_lastdata=e,null!=t.j&&""!=t.j?(jQuery("#updraft_activejobsrow").show(),0<u.length&&jQuery("#updraft_clone_activejobsrow").show(),a.hasOwnProperty("thisjobonly")&&!updraft_inpage_hasbegun&&jQuery("#updraft-jobid-"+a.thisjobonly).length?(updraft_inpage_hasbegun=1,console.log("UpdraftPlus: the start of the requested backup job has been detected")):!updraft_inpage_hasbegun&&updraft_activejobslist_backupnownonce_only&&jQuery(".updraft_jobtimings.isautobackup").length&&(autobackup_nonce=jQuery(".updraft_jobtimings.isautobackup").first().data("jobid"))&&(updraft_inpage_hasbegun=1,updraft_backupnow_nonce=autobackup_nonce,a.thisjobonly=autobackup_nonce,console.log("UpdraftPlus: the start of the requested backup job has been detected; id: "+autobackup_nonce)),1==updraft_inpage_hasbegun&&jQuery("#updraft-jobid-"+a.thisjobonly+".updraft_finished").length&&(updraft_inpage_hasbegun=2,console.log("UpdraftPlus: the end of the requested backup job has been detected"),updraft_activejobs_update_timer&&clearInterval(updraft_activejobs_update_timer),"undefined"!=typeof updraft_inpage_success_callback&&""!=updraft_inpage_success_callback?updraft_inpage_success_callback.call(!1):jQuery("#updraft-backupnow-inpage-modal").dialog("close")),""==lastlog_jobs&&setTimeout(function(){jQuery("#updraft_backup_started").slideUp()},3500),a.hasOwnProperty("thisjobonly")&&updraft_backupnow_nonce&&a.thisjobonly===updraft_backupnow_nonce&&(jQuery(".updraft_requeststart").remove(),jQuery("#updraft-jobid-"+updraft_backupnow_nonce).is(".updraft_finished"))&&(updraft_activejobslist_backupnownonce_only=0,updraft_aborted_jobs[updraft_backupnow_nonce]?updraft_aborted_jobs=updraft_aborted_jobs.filter(function(t,e){return t!=updraft_backupnow_nonce}):updraft_active_job_is_clone(updraft_backupnow_nonce)?(updraft_show_success_modal(updraftlion.clone_backup_complete),updraft_clone_jobs=updraft_clone_jobs.filter(function(t){return t!=updraft_backupnow_nonce})):updraft_show_success_modal(updraftlion.backup_complete),updraft_activejobs_update(!(updraft_backupnow_nonce="")))):jQuery("#updraft_activejobsrow").is(":hidden")||("undefined"!=typeof lastbackup_laststatus&&updraft_showlastbackup(),updraft_updatehistory(0,0),jQuery("#updraft_activejobsrow").hide()),lastlog_jobs=t.j,null!=t.ds&&""!=t.ds&&updraft_downloader_status_update(t.ds,e),null!=t.u&&""!=t.u&&jQuery("#updraft-poplog").dialog("isOpen")&&(r=t.u).nonce==updraft_poplog_log_nonce&&(updraft_poplog_log_pointer=r.pointer,null!=r.log)&&""!=r.log&&(n=jQuery("#updraft-poplog").scrollTop(),jQuery("#updraft-poplog-content").append(r.log),updraft_poplog_lastscroll!=n&&-1!=updraft_poplog_lastscroll||(jQuery("#updraft-poplog").scrollTop(jQuery("#updraft-poplog-content").prop("scrollHeight")),updraft_poplog_lastscroll=jQuery("#updraft-poplog").scrollTop()))}catch(t){console.log(updraftlion.unexpectedresponse+" "+e),console.log(t)}}function updraft_js_tree(t){var o=this;o.remote_storage=t,this.hide_jstree=function(t){t.find('[id^="updraft_'+o.remote_storage+'_folder"]').removeAttr("readonly"),t.find(".updraft_"+o.remote_storage+"_select_folder").show(),t.find(".updraft_"+o.remote_storage+"_container").hide(),t.find(".updraft_jstree_cancel").hide(),t.find(".updraft_jstree_confirm").hide()},this.get_structured_folder=function(t){for(var e=t.node.parents.length,a=t.node.id,r=jQuery("#"+a),n=t.node.text+"/",o=0;o<=e-1;o++)null!=(r=r.parent().parent()).children()[1]&&(n=r.children()[1].text+"/"+n);return n=0<n.length?n.substring(0,n.length-1):n},this.list_folders=function(a,r,n){r.find('[id^="updraft_'+o.remote_storage+'_folder"]').prop("readOnly",!0),r.find('[id^="updraft_'+o.remote_storage+'_folder"]').attr("previous-value",r.find('[id^="updraft_'+o.remote_storage+'_folder"]').val()),r.find(".updraft_"+o.remote_storage+"_select_folder").hide(),r.find(".updraft_"+o.remote_storage+"_container").show(),r.find(".updraft_jstree_cancel").show(),r.find(".updraft_jstree_confirm").show(),r.find(".updraft_"+o.remote_storage+"_jstree_container").jstree({core:{multiple:!1,data:function(t,e){updraft_send_command("get_jstree_directory_nodes",{entity:a,node:t,instance_id:n},function(t){t.hasOwnProperty("error")?jQuery(".updraft_include_"+o.remote_storage+"_paths_error").text(t.error):(jQuery(".updraft_include_"+o.remote_storage+"_paths_error").text(""),e.call(this,t.nodes))})}},plugins:["sort","types"],sort:function(t,e){return a1=this.get_node(t),b1=this.get_node(e),a1.icon==b1.icon?a1.text>b1.text?1:-1:a1.icon<b1.icon?1:-1}}),r.find(".updraft_"+o.remote_storage+"_jstree_container").on("changed.jstree",function(t,e){r.find('[id^="updraft_'+o.remote_storage+'_folder"]').val(o.get_structured_folder(e))}),r.find(".updraft_"+o.remote_storage+"_jstree_confirm, .updraft_"+o.remote_storage+"_jstree_cancel").on("click",function(t){t.preventDefault(),jQuery(this).hasClass("updraft_"+o.remote_storage+"_jstree_cancel")&&r.find('[id^="updraft_'+o.remote_storage+'_folder"]').val(r.find('[id^="updraft_'+o.remote_storage+'_folder"]').attr("previous-value")),o.hide_jstree(r)})},this.init=function(){jQuery(function(){jQuery(".updraft_"+o.remote_storage+"_select_folder").on("click",function(t){t.preventDefault();var t=jQuery(this).closest("tr"),e=t.find('[id^="updraft_'+o.remote_storage+'_folder_"]').attr("id").replace("updraft_"+o.remote_storage+"_folder_","");o.list_folders(o.remote_storage,t,e)})})}}function initialize_remote_storage_select2_elements(t){for(var e=jQuery(t).find("select.select2-storage-config"),a=0;a<e.length;a++)jQuery(e[a]).select2({tags:!0}),"dreamobjects"===jQuery(e[a]).data("storage-id")&&"endpoint"===jQuery(e[a]).data("field-id")&&(jQuery(e[a]).on("change",function(t){validate_dreamobjects_endpoint(t.target)}),validate_dreamobjects_endpoint(e[a]))}function validate_dreamobjects_endpoint(t){var e=t.value.trim();updraftlion.dreamobject_endpoints.includes(e)||new RegExp(updraftlion.dreamobject_endpoint_regex,"i").test(e)?t.classList.remove("updraft-input--invalid"):t.classList.add("updraft-input--invalid")}jQuery(document).ajaxError(function(t,e,a,r){var n,o,d;null!=r&&""!=r&&null!=e.responseText&&""!=e.responseText&&(console.log("Error caught by UpdraftPlus ajaxError handler (follows) for "+a.url),console.log(r),0==a.url.search(ajaxurl))&&(0<=a.url.search("subaction=downloadstatus")?(e=a.url.match(/timestamp=\d+/),o=a.url.match(/type=[a-z]+/),n=a.url.match(/findex=\d+/),d=a.url.match(/base=[a-z_]+/),n=n instanceof Array?parseInt(n[0].substr(7)):0,o=o instanceof Array?o[0].substr(5):"",d=d instanceof Array?d[0].substr(5):"",e=e instanceof Array?parseInt(e[0].substr(10)):0,""!=d&&""!=o&&0<e&&jQuery("."+(d+e+"_"+o+"_"+n)+" .raw").html("<strong>"+updraftlion.error+"</strong> "+updraftlion.servererrorcode)):0<=a.url.search("subaction=restore_alldownloaded")&&jQuery("#updraft-restore-modal-stage2a").append("<br><strong>"+updraftlion.error+"</strong> "+updraftlion.servererrorcode+": "+r))}),jQuery(function(h){var e;h(document).on("udp/checkout/done",function(t,e){e.hasOwnProperty("product")&&"updraftpremium"===e.product&&"complete"===e.status&&(h(".premium-upgrade-purchase-success").show(),h(".updraft_feat_table").closest("section").hide(),h(".updraft_premium_cta__action").hide())}),h(".expertmode .advanced_settings_container .advanced_tools_button").on("click",function(){var t;t=h(this).attr("id"),h('.expertmode .advanced_settings_container .advanced_tools:not(".'+t+'")').hide(),h(".expertmode .advanced_settings_container .advanced_tools."+t).fadeIn("slow"),h(".expertmode .advanced_settings_container .advanced_tools_button:not(#"+t+")").removeClass("active"),h(".expertmode .advanced_settings_container .advanced_tools_button#"+t).addClass("active")}),jQuery.ui&&jQuery.ui.dialog&&jQuery.ui.dialog.prototype._allowInteraction&&(e=jQuery.ui.dialog.prototype._allowInteraction,jQuery.ui.dialog.prototype._allowInteraction=function(t){return!!jQuery(t.target).closest(".select2-dropdown").length||e.apply(this,arguments)}),h("#updraft-navtab-settings-content #remote-storage-holder").on("change keyup paste",".updraft_webdav_settings",function(){var t,e,a,r,n,o,d,s=h(this).attr("id");"string"==typeof s&&(s=s.match(/updraft_webdav_(.+)_(s-[^_]+)$/i))&&s[1]&&s[2]&&(t=s[2],e="","host"===(s=s[1].toLowerCase())?(h(".webdav-"+t+" .updraft_webdav_host_error").hide(),0<=h(this).val().indexOf("@")?h(this).val(h(this).val().replaceAll("@",encodeURIComponent("@"))):0<=h(this).val().indexOf("/")&&(h(this).val(h(this).val().replaceAll("/","")),h(".webdav-"+t+" .updraft_webdav_host_error").show())):"path"===s&&0==h(this).val().indexOf("/")&&h(this).val(h(this).val().replace(/^[\/]+/,"")),s=h("#updraft_webdav_webdav_"+t).val(),a=h("#updraft_webdav_host_"+t).val()||"",r=h("#updraft_webdav_user_"+t).val()||"",n=h("#updraft_webdav_pass_"+t).val()||"",o=h("#updraft_webdav_port_"+t).val()||"",d=h("#updraft_webdav_path_"+t).val()||"",n&&(e=n.replace(/./gi,"*")),r&&n&&(r+=":"),(r||n)&&a&&(e+="@",n+="@"),a&&o&&(a+=":"),d=d&&"/"+d,h("#updraft_webdav_url_"+t).val(s+r+n+a+o+d),h("#updraft_webdav_masked_url_"+t).val(s+r+e+a+o+d))}),h("div.ud-phpseclib-notice").on("click","button.notice-dismiss",function(t){t.stopImmediatePropagation(),updraft_send_command("dismiss_phpseclib_notice",null,function(t,e,a){t.hasOwnProperty("success")&&1===t.success||(console.log(t),alert(updraftlion.unexpectedresponse+" "+a))})}),h("#updraft-navtab-backups-content").on("click",".js--delete-selected-backups",function(t){t.preventDefault(),updraft_deleteallselected()}),h("#updraft-navtab-backups-content").on("click",".updraft_existing_backups .backup-select input",function(t){updraft_backups_selection.toggle(h(this).closest(".updraft_existing_backups_row"))}),h("#updraft-navtab-backups-content").on("click","#cb-select-all",function(t){h(this).is(":checked")?updraft_backups_selection.selectAll():updraft_backups_selection.deselectAll()}),h("#updraft-wrap").on("click","[id^=updraftplus_manual_authorisation_submit_]",function(t){t.preventDefault();var e,t=h(this).data("method"),a=h("#updraftplus_manual_authentication_data_"+t).val();h("#updraftplus_manual_authentication_error_"+t).text(),h("#updraft-wrap #updraftplus_manual_authorisation_template_"+t+" .updraftplus_spinner.spinner").addClass("visible"),h("#updraftplus_manual_authorisation_submit_"+t).prop("disabled",!0),updraft_send_command("manual_remote_storage_authentication",{method:e=t,auth_data:a},function(t){h("#updraft-wrap #updraftplus_manual_authorisation_template_"+e+" .updraftplus_spinner.spinner").removeClass("visible"),t.hasOwnProperty("result")&&"success"===t.result?(h("#updraft-wrap .updraftplus-top-menu").before(t.data),h("#updraft-wrap #updraftplus_manual_authorisation_template_"+e).parent().remove(),h("#updraft-wrap .updraft_authenticate_"+e).remove()):t.hasOwnProperty("result")&&"error"===t.result&&(h("#updraftplus_manual_authentication_error_"+e).text(t.data),h("#updraftplus_manual_authorisation_submit_"+e).prop("disabled",!1))})}),h("#updraft-navtab-backups-content").on("click",".js--select-all-backups",function(t){updraft_backups_selection.selectAll()}),h("#updraft-navtab-backups-content").on("click",".js--deselect-all-backups",function(t){updraft_backups_selection.deselectAll()}),h("#updraft-navtab-backups-content").on("click",".updraft_existing_backups .updraft_existing_backups_row",function(t){(t.ctrlKey||t.metaKey)&&(t.shiftKey?(void 0===updraft_backups_selection.firstMultipleSelectionIndex?(h(document).on("keyup.MultipleSelection",function(t){updraft_backups_selection.unregister_highlight_mode(),h(document).off(".MultipleSelection")}),updraft_backups_selection.select(this),h(this).addClass("range-selection-start"),updraft_backups_selection.register_highlight_mode()):(updraft_backups_selection.selectAllInBetween(this),jQuery("#updraft-navtab-backups-content .updraft_existing_backups .updraft_existing_backups_row").removeClass("range-selection")),updraft_backups_selection.firstMultipleSelectionIndex=this.rowIndex-1):updraft_backups_selection.toggle(this))}),updraft_backups_selection.checkSelectionStatus(),h("#updraft-navtab-addons-content .wrap").on("click",".updraftplus_com_login .ud_connectsubmit",function(t){t.preventDefault();var t=h("#updraft-navtab-addons-content .wrap .updraftplus_com_login #updraftplus-addons_options_email").val(),e=h("#updraft-navtab-addons-content .wrap .updraftplus_com_login #updraftplus-addons_options_password").val(),a=h("#updraft-navtab-addons-content .wrap .updraftplus_com_login #updraftplus-addons_options_auto_updates").is(":checked")?1:0,r=h("#updraft-navtab-addons-content .wrap .updraftplus_com_login #updraftplus-addons_options_auto_udc_connect").is(":checked")?1:0;n.submit({email:t,password:e,auto_update:a,auto_udc_connect:r})}),h("#updraft-navtab-addons-content .wrap").on("keydown",".updraftplus_com_login input",function(t){var e,a,r;13==t.which&&(t.preventDefault(),t=h("#updraft-navtab-addons-content .wrap .updraftplus_com_login #updraftplus-addons_options_email").val(),e=h("#updraft-navtab-addons-content .wrap .updraftplus_com_login #updraftplus-addons_options_password").val(),a=h("#updraft-navtab-addons-content .wrap .updraftplus_com_login #updraftplus-addons_options_auto_updates").is(":checked")?1:0,r=h("#updraft-navtab-addons-content .wrap .updraftplus_com_login #updraftplus-addons_options_auto_udc_connect").is(":checked")?1:0,n.submit({email:t,password:e,auto_update:a,auto_udc_connect:r}))}),h("#updraft-navtab-migrate-content").on("click",".updraftclone_show_step_1",function(t){h(".updraftplus-clone").addClass("opened"),h(".updraftclone_show_step_1").hide(),h(".updraft_migrate_widget_temporary_clone_stage1").show(),h(".updraft_migrate_widget_temporary_clone_stage0").hide()}),h("#updraft-navtab-migrate-content").on("click",".updraft_migrate_widget_temporary_clone_show_stage0",function(t){t.preventDefault(),h(".updraft_migrate_widget_temporary_clone_stage0").toggle()}),setup_migrate_tabs(),h("#updraft-navtab-migrate-content").on("click",".updraft_migrate_widget_module_content .close",function(t){h(".updraft_migrate_intro").show(),h(this).closest(".updraft_migrate_widget_module_content").hide()}),h("#updraft-navtab-migrate-content").on("click","#updraft_migrate_tab_alt .close",function(t){t.preventDefault(),h(".updraft_migrate_intro").show(),h("#updraft_migrate_tab_alt").html("").hide()}),h("#updraft-navtab-migrate-content").on("click",".updraft_migrate_add_site--trigger",function(t){t.preventDefault(),h(".updraft_migrate_add_site").toggle()}),h("#updraft-navtab-migrate-content").on("click",".updraft_migrate_widget_module_content .updraftplus_com_login .ud_connectsubmit",function(t){t.preventDefault();var t=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login #temporary_clone_options_email").val(),e=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login #temporary_clone_options_password").val(),a=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login #temporary_clone_options_two_factor_code").val(),r=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login .temporary_clone_terms_and_conditions").is(":checked")?1:0;t&&e?o({form_data:{email:t,password:e,two_factor_code:a,consent:r}}):h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login_status").html("<b>"+updraftlion.error+"</b> "+updraftlion.username_password_required).show()}),h("#updraft-navtab-migrate-content").on("keydown",".updraft_migrate_widget_module_content .updraftplus_com_login input",function(t){var e,a,r;13==t.which&&(t.preventDefault(),t=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login #temporary_clone_options_email").val(),e=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login #temporary_clone_options_password").val(),a=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login #temporary_clone_options_two_factor_code").val(),r=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login .temporary_clone_terms_and_conditions").is(":checked")?1:0,t&&e?o({form_data:{email:t,password:e,two_factor_code:a,consent:r}}):h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login_status").html("<b>"+updraftlion.error+"</b> "+updraftlion.username_password_required).show())}),h("#updraft-navtab-migrate-content").on("click",".updraft_migrate_widget_module_content .updraftplus_com_key .ud_key_connectsubmit",function(t){t.preventDefault();var t=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_key #temporary_clone_options_key").val(),e=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_key .temporary_clone_terms_and_conditions").is(":checked")?1:0;t?a({form_data:{clone_key:t,consent:e}}):h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_key_status").html("<b>"+updraftlion.error+"</b> "+updraftlion.clone_key_required).show()}),h("#updraft-navtab-migrate-content").on("keydown",".updraft_migrate_widget_module_content .updraftplus_com_key input",function(t){var e;13==t.which&&(t.preventDefault(),t=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_key #temporary_clone_options_key").val(),e=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_key .temporary_clone_terms_and_conditions").is(":checked")?1:0,t?a({form_data:{clone_key:t,consent:e}}):h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_key_status").html("<b>"+updraftlion.error+"</b> "+updraftlion.clone_key_required).show())}),h("#updraft-navtab-migrate-content").on("change",".updraft_migrate_widget_module_content #updraftplus_clone_php_options",function(){var t=h(this).data("php_version");h(this).val()<t?h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_clone_status").html(updraftlion.clone_version_warning):h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_clone_status").html("")}),h("#updraft-navtab-migrate-content").on("change",".updraft_migrate_widget_module_content #updraftplus_clone_wp_options",function(){var t=h(this).data("wp_version");h(this).val()<t?h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_clone_status").html(updraftlion.clone_version_warning):h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_clone_status").html("")}),h("#updraft-navtab-migrate-content").on("change",".updraft_migrate_widget_module_content #updraftplus_clone_backup_options",function(){h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_package_options > option").each(function(){var t=h(this).val();"starter"==t&&h('#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_package_options  option[value="'+t+'"]').prop("selected",!0),h('#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_package_options  option[value="'+t+'"]').prop("disabled",!1)});var a,t=h(this).find("option:selected");"current"!=h(t).data("nonce")&&"wp_only"!=h(t).data("nonce")&&(a=h(t).data("size"),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_package_options > option").each(function(){var t=h(this).data("size"),e=h(this).val();if(!(t<=a))return h('#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_package_options  option[value="'+e+'"]').prop("selected",!0),!1;h('#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_package_options  option[value="'+e+'"]').prop("disabled",!0)}))}),h("#updraft-navtab-migrate-content").on("click",".updraft_migrate_widget_module_content #updraft_migrate_createclone",function(t){t.preventDefault(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraft_migrate_createclone").prop("disabled",!0),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_clone_status").html(""),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_spinner.spinner").addClass("visible");var t=h(this).data("clone_id"),e=h(this).data("secret_token"),a=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_php_options").val(),r=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_wp_options").val(),n=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_region_options").val(),o=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_package_options").val(),d=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_updraftclone_branch").val(),s=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_updraftplus_branch").val(),u=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_clone_admin_login_options").is(":checked"),i=h("#updraftplus_clone_use_queue").is(":checked")?1:0,p=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_backupnow_db_anon_all").is(":checked")?1:0,l=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_backupnow_db_anon_non_staff").is(":checked")?1:0,_=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_backupnow_db_anon_wc_order_data").is(":checked")?1:0,c="current",f="current",g=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_backup_options").length,m=h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraftplus_clone_backup_options").find("option:selected"),g=(0!==g&&void 0!==m&&(c=m.data("nonce"),f=m.data("timestamp")),{form_data:{clone_id:t,secret_token:e,install_info:{php_version:a,wp_version:r,region:n,package:o,admin_only:u,updraftclone_branch:void 0===d?"":d,updraftplus_branch:void 0===s?"":s,use_queue:void 0===i?1:i}}}),m={db_anon_all:p,db_anon_non_staff:l,db_anon_wc_orders:_,clone_region:n};"wp_only"===c&&(g.form_data.install_info.wp_only=1),function t(r,n,o,d){var s="";"current"!=n&&updraft_send_command("whichdownloadsneeded",{updraftplus_clone:!0,timestamp:n},function(t){if(t.hasOwnProperty("downloads")&&(console.log("UpdraftPlus: items which still require downloading follow"),s=t.downloads,console.log(s)),0!=s.length)for(var e=0;e<s.length;e++)updraft_downloader("udclonedlstatus_",n,s[e][0],"#ud_downloadstatus3",s[e][1],"",!1)},{alert_on_error:!1,error_callback:function(t,e,a,r){void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_clone_status").html('<p style="color:red;">'+r.fatal_error_message+"</p>")):(r="updraft_send_command: error: "+e+" ("+a+")",h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_clone_status").html('<p style="color:red; margin: 5px;">'+r+"</p>"),console.log(r),console.log(t))}});setTimeout(function(){var e,a;0!=s.length?t(r,n,o,d):(e=r.form_data.clone_id,a=r.form_data.secret_token,updraft_send_command("process_updraftplus_clone_create",r,function(t){try{h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraft_migrate_createclone").prop("disabled",!1),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_spinner.spinner").removeClass("visible"),t.hasOwnProperty("status")&&"error"==t.status?h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_clone_status").html(updraftlion.error+" "+t.message).show():"success"===t.status&&(h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage2").hide(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage3").show(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage3").html(t.html),temporary_clone_timeout&&clearTimeout(temporary_clone_timeout),t.hasOwnProperty("secret_token")&&(a=t.secret_token),"wp_only"===o?(jQuery("#updraft_clone_progress .updraftplus_spinner.spinner").addClass("visible"),y(e,a)):(jQuery("#updraft_clone_progress .updraftplus_spinner.spinner").addClass("visible"),b(e,a,t.url,t.key,o,n,d)))}catch(t){h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraft_migrate_createclone").prop("disabled",!1),console.log("Error when processing the response of process_updraftplus_clone_create (as follows)"),console.log(t)}}))},5e3)}(g,f,c,m)});var n={};function o(t){h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login_status").html("").hide(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login .updraftplus_spinner.spinner").addClass("visible"),updraft_send_command("process_updraftplus_clone_login",t,function(t){try{h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login .updraftplus_spinner.spinner").removeClass("visible"),t.hasOwnProperty("status")&&"error"==t.status?(h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login_status").html(t.message).show(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage1 .tfa_fields").hide(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage1 .non_tfa_fields").show(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_login #temporary_clone_options_two_factor_code").val("")):(t.hasOwnProperty("tfa_enabled")&&1==t.tfa_enabled&&(h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage1 .non_tfa_fields").hide(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage1 .tfa_fields").show(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage1 input#temporary_clone_options_two_factor_code").trigger("focus")),"authenticated"===t.status&&(h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage1").hide(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage1 .non_tfa_fields").show(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage1 .tfa_fields").hide(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage1 input#temporary_clone_options_two_factor_code").val(""),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage2").show(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage2").html(t.html),t.hasOwnProperty("clone_info"))&&t.clone_info.hasOwnProperty("expires_after")&&r(t.clone_info.expires_after))}catch(t){console.log(t)}})}function a(t){h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_key_status").html("").hide(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_key .updraftplus_spinner.spinner").addClass("visible"),updraft_send_command("process_updraftplus_clone_login",t,function(t){try{h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_key .updraftplus_spinner.spinner").removeClass("visible"),t.hasOwnProperty("status")&&"error"==t.status?h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_com_key_status").html(t.message).show():"authenticated"===t.status&&(h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage1").hide(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage2").show(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage2").html(t.html),t.hasOwnProperty("clone_info"))&&t.clone_info.hasOwnProperty("expires_after")&&r(t.clone_info.expires_after)}catch(t){console.log(t)}})}function r(t){temporary_clone_timeout=setTimeout(function(){h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage2").hide(),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage2").html(""),h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraft_migrate_widget_temporary_clone_stage1").show()},1e3*t)}function b(t,e,a,r,n,o,d){t={updraftplus_clone_backup:1,backupnow_nodb:0,backupnow_nofiles:0,backupnow_nocloud:0,backupnow_label:"UpdraftClone",extradata:"",onlythisfileentity:"plugins,themes,uploads,others",clone_id:t,secret_token:e,clone_url:a,key:r,backup_nonce:n,backup_timestamp:o,db_anon_all:d.db_anon_all,db_anon_non_staff:d.db_anon_non_staff,db_anon_wc_orders:d.db_anon_wc_orders,clone_region:d.clone_region};updraft_activejobslist_backupnownonce_only=1,updraft_send_command("backupnow",t,function(t){jQuery("#updraft_clone_progress .updraftplus_spinner.spinner").removeClass("visible"),jQuery("#updraft_backup_started").html(t.m),t.hasOwnProperty("nonce")&&(updraft_backupnow_nonce=t.nonce,updraft_clone_jobs.push(updraft_backupnow_nonce),updraft_inpage_success_callback=function(){jQuery("#updraft_clone_activejobsrow").hide(),updraft_aborted_jobs[updraft_backupnow_nonce]?jQuery("#updraft_clone_progress").html(updraftlion.clone_backup_aborted):jQuery("#updraft_clone_progress").html(updraftlion.clone_backup_complete)},console.log("UpdraftPlus: ID of started job: "+updraft_backupnow_nonce)),updraft_activejobs_update(!0)})}function y(e,a){var t={clone_id:e,secret_token:a};setTimeout(function(){updraft_send_command("process_updraftplus_clone_poll",t,function(t){if(t.hasOwnProperty("status")){if("error"==t.status)return void h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_clone_status").html(updraftlion.error+" "+t.message).show();if("success"===t.status&&t.hasOwnProperty("data")&&t.data.hasOwnProperty("wordpress_credentials"))return h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content .updraftplus_spinner.spinner").removeClass("visible"),void h("#updraft-navtab-migrate-content .updraft_migrate_widget_module_content #updraft_clone_progress").append("<br>WordPress "+updraftlion.credentials+":<br>"+updraftlion.username+": "+t.data.wordpress_credentials.username+"<br>"+updraftlion.password+": "+t.data.wordpress_credentials.password)}else console.log(t);y(e,a)})},6e4)}function d(t){var e=Handlebars.compile(updraftlion.remote_storage_templates[t]),a={},r=(Object.assign(a,updraftlion.remote_storage_options[t].template_properties,updraftlion.remote_storage_options[t].default),updraftlion.remote_storage_methods[t]),r=(a.instance_id="s-"+(t=>{for(var e="",a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r=0;r<t;r++)e+=a.charAt(Math.floor(Math.random()*a.length));return e})(32),a.instance_enabled=1,a.instance_label=r+" ("+(jQuery("."+t+"_updraft_remote_storage_border").length+1)+")",a.instance_conditional_logic={type:"",rules:[],day_of_the_week_options:updraftlion.conditional_logic.day_of_the_week_options,logic_options:updraftlion.conditional_logic.logic_options,operand_options:updraftlion.conditional_logic.operand_options,operator_options:updraftlion.conditional_logic.operator_options},e(a));jQuery(r).hide().insertAfter(jQuery("."+t+"_add_instance_container").first()).show("slow",function(){initialize_remote_storage_select2_elements(this)})}n.set_status=function(t){h("#updraft-navtab-addons-content .wrap").find(".updraftplus_spinner.spinner").text(t)},n.show_loader=function(){h("#updraft-navtab-addons-content .wrap").find(".updraftplus_spinner.spinner").addClass("visible"),h("#updraft-navtab-addons-content .wrap").find(".ud_connectsubmit").prop("disabled","disabled")},n.hide_loader=function(){h("#updraft-navtab-addons-content .wrap").find(".updraftplus_spinner.spinner").removeClass("visible").text(updraftlion.processing),h("#updraft-navtab-addons-content .wrap").find(".ud_connectsubmit").prop("disabled",!1)},n.submit=function(e){if(h("#updraft-navtab-addons-content .wrap .updraftplus_com_login_status").html("").hide(),this.stage)switch(this.stage){case"connect_udc":case"connect_udc_TFA":var t=h("#updraftplus-addons_options_email").val(),a=h("#updraftplus-addons_options_password").val();this.login_data.email=t,this.login_data.password=a,this.connect_udc();break;case"create_key":this.create_key();break;default:this.stage=null,n.submit()}else this.set_status(updraftlion.connecting),this.show_loader(),updraft_send_command("updraftplus_com_login_submit",{data:e},function(t){t.hasOwnProperty("success")?h("#updraftplus-addons_options_auto_udc_connect").is(":checked")?(this.login_data={email:e.email,password:e.password,i_consent:1,two_factor_code:""},n.create_key()):(n.hide_loader(),h("#updraft-navtab-addons-content .wrap .updraftplus_com_login").trigger("submit")):t.hasOwnProperty("error")&&(n.hide_loader(),h("#updraft-navtab-addons-content .wrap .updraftplus_com_login_status").html(t.message).show())}.bind(this))},n.create_key=function(){this.stage="create_key",this.set_status(updraftlion.udc_cloud_connected),this.show_loader();updraft_send_command("updraftcentral_create_key",{where_send:"__updraftpluscom",key_description:"",key_size:null,mothership_firewalled:0},function(t){try{var e=ud_parse_json(t);e.hasOwnProperty("error")?console.log(e):e.hasOwnProperty("bundle")?(console.log("bundle",e.bundle),this.login_data.key=e.bundle,this.stage="connect_udc",n.connect_udc()):(e.hasOwnProperty("r")?(h("#updraft-navtab-addons-content .wrap .updraftplus_com_login_status").html(updraftlion.trouble_connecting).show(),alert(e.r)):(h("#updraft-navtab-addons-content .wrap .updraftplus_com_login_status").html(updraftlion.trouble_connecting).show(),console.log(e)),n.hide_loader())}catch(t){console.log(t),n.hide_loader()}}.bind(this),{json_parse:!1})},n.connect_udc=function(){var a=h("#updraft-navtab-addons-content .wrap"),t=(n.set_status(updraftlion.udc_cloud_key_created),n.show_loader(),"connect_udc_TFA"==this.stage&&(this.login_data.two_factor_code=a.find("input#updraftplus-addons_options_two_factor_code").val(),n.set_status(updraftlion.checking_tfa_code)),{form_data:this.login_data});t.form_data.addons_options_connect=1,updraft_send_command("process_updraftcentral_login",t,function(t){try{var e=ud_parse_json(t);if(e.hasOwnProperty("error")){if("incorrect_password"===e.code&&(a.find(".tfa_fields").hide(),a.find(".non_tfa_fields").show(),a.find("input#updraftplus-addons_options_two_factor_code").val(""),a.find("input#updraftplus-addons_options_password").val("").trigger("focus")),"no_key_found"===e.code&&(this.stage="create_key"),"no_licences_available"!==e.code)return h("#updraft-navtab-addons-content .wrap .updraftplus_com_login_status").html(e.message).show(),h("#updraft-navtab-addons-content .wrap .updraftplus_com_login_status").find("a").attr("target","_blank"),console.log(e),void n.hide_loader();h("#updraft-navtab-addons-content .wrap .updraftplus_com_login_status").html(updraftlion.login_udc_no_licences_short).show(),e.status="authenticated",a.find('input[name="_wp_http_referer"]').val(function(t,e){return e+"&udc_connect=0"})}e.hasOwnProperty("tfa_enabled")&&1==e.tfa_enabled&&(h("#updraft-navtab-addons-content .wrap .updraftplus_com_login_status").html("").hide(),a.find(".non_tfa_fields").hide(),a.find(".tfa_fields").show(),a.find("input#updraftplus-addons_options_two_factor_code").trigger("focus"),this.stage="connect_udc_TFA"),"authenticated"===e.status&&(a.find(".non_tfa_fields").hide(),a.find(".tfa_fields").hide(),a.find(".updraft-after-form-table").hide(),this.stage=null,h("#updraft-navtab-addons-content .wrap .updraftplus_com_login_status").html(updraftlion.login_successful_short).show().addClass("success"),setTimeout(function(){h("#updraft-navtab-addons-content .wrap form.updraftplus_com_login").trigger("submit")},1e3))}catch(t){console.log(t)}n.hide_loader()}.bind(this),{json_parse:!1})},h("#updraft-navtab-settings-content #remote-storage-holder").on("click",".updraftplusmethod a.updraft_add_instance",function(t){t.preventDefault(),updraft_settings_form_changed=!0,load_save_button(),d(h(this).data("method"))}),h("#updraft-navtab-settings-content #remote-storage-holder").on("click",".updraftplusmethod a.updraft_delete_instance",function(t){t.preventDefault(),updraft_settings_form_changed=!0,load_save_button();var t=h(this).data("method"),e=h(this).data("instance_id");1===h("."+t+"_updraft_remote_storage_border").length&&d(t),h("."+t+"-"+e).hide("slow",function(){h(this).remove()})}),h("#updraft-navtab-settings-content #remote-storage-holder").on("click",".updraftplusmethod .updraft_edit_label_instance",function(t){h(this).find("span").hide(),h(this).attr("contentEditable",!0).trigger("focus")}),h("#updraft-navtab-settings-content #remote-storage-holder").on("keyup",".updraftplusmethod .updraft_edit_label_instance",function(t){var e=jQuery(this).data("method"),a=jQuery(this).data("instance_id"),r=jQuery(this).text();h("#updraft_"+e+"_instance_label_"+a).val(r)}),h("#updraft-navtab-settings-content #remote-storage-holder").on("blur",".updraftplusmethod .updraft_edit_label_instance",function(t){h(this).attr("contentEditable",!1),h(this).find("span").show()}),h("#updraft-navtab-settings-content #remote-storage-holder").on("keypress",".updraftplusmethod .updraft_edit_label_instance",function(t){13===t.which&&(h(this).attr("contentEditable",!1),h(this).find("span").show(),h(this).trigger("blur"))}),jQuery("#updraft-navtab-settings-content #remote-storage-holder").on("change","input[class='updraft_instance_toggle']",function(){updraft_settings_form_changed=!0,load_save_button(),jQuery(this).is(":checked")?jQuery(this).siblings("label").html(updraftlion.instance_enabled):jQuery(this).siblings("label").html(updraftlion.instance_disabled)}),jQuery("#updraft-navtab-settings-content #remote-storage-holder").on("change","select[class='logic_type']",function(){updraft_settings_form_changed=!0,load_save_button(),""!==this.value?(jQuery("div.logic",jQuery(this).parents("tr.updraftplusmethod")).show(),jQuery(this).parents("tr.updraftplusmethod").find("div.logic ul.rules > li").each(function(){jQuery(this).find("select").each(function(){jQuery(this).prop("disabled",!1)})})):(jQuery(this).parents("tr.updraftplusmethod").find("div.logic ul.rules > li").each(function(){jQuery(this).find("select").each(function(){jQuery(this).prop("disabled",!0)})}),jQuery(this).parents("tr.updraftplusmethod").find("div.logic").hide())}),jQuery("#updraft-navtab-settings-content #remote-storage-holder").on("change","select[class='conditional_logic_operand']",function(){if(updraft_settings_form_changed=!0,load_save_button(),jQuery(this).parent().find("select:nth(2)").empty(),"day_of_the_week"===jQuery(this).val())for(var t=0;t<updraftlion.conditional_logic.day_of_the_week_options.length;t++)jQuery(this).parent().find("select:nth(2)").append(jQuery('<option value="'+updraftlion.conditional_logic.day_of_the_week_options[t].index+'"></option>').text(updraftlion.conditional_logic.day_of_the_week_options[t].value));else if("day_of_the_month"===jQuery(this).val())for(t=1;t<=31;t++)jQuery(this).parent().find("select:nth(2)").append(jQuery('<option value="'+t+'"></option>').text(t))}),jQuery("#updraft-navtab-settings-content #remote-storage-holder").on("click","div.conditional_remote_backup ul.rules li span",function(){updraft_settings_form_changed=!0,load_save_button();var t=jQuery(this).parents("ul.rules");jQuery(this).hasClass("remove-rule")&&jQuery(this).parent().slideUp(function(){jQuery(this).remove(),jQuery(t).find("> li").length<2&&jQuery("li:nth(0) span.remove-rule",t).remove()})}),jQuery("#updraft-navtab-settings-content #remote-storage-holder").on("click","div.conditional_remote_backup input.add-new-rule",function(){var t=jQuery(this).parent().find("ul.rules");jQuery(t).find("> li").length<2&&jQuery(t).find("li:nth(0)").append('<span class="remove-rule"><svg viewbox="0 0 25 25"><line x1="6.5" y1="18.5" x2="18.5" y2="6.5" fill="none" stroke="#FF6347" stroke-width="3" vector-effect="non-scaling-stroke" ></line><line y1="6.5" x1="6.5" y2="18.5" x2="18.5" fill="none" stroke="#FF6347" stroke-width="3" vector-effect="non-scaling-stroke" ></line></svg></span>'),$cloned_item=jQuery(t).find("> li").last().clone(),jQuery($cloned_item).find("> select").each(function(){jQuery(this).prop("name",jQuery(this).prop("name").replace(/\[instance_conditional_logic\]\[rules\]\[[0-9]+\]/gi,"[instance_conditional_logic][rules]["+jQuery(t).data("rules")+"]"))}),jQuery(t).append($cloned_item),jQuery(t).data("rules",parseInt(jQuery(t).data("rules"))+1),jQuery($cloned_item).find('select[name*="[operand]"]').trigger("change")}),jQuery("#updraft-navtab-settings-content #remote-storage-holder").on("click",".updraftplusmethod button.updraft-test-button",function(){var r=jQuery(this).data("method"),n=jQuery(this).data("instance_id");updraft_remote_storage_test(r,function(t,e,a){return"sftp"==r&&(a.hasOwnProperty("scp")&&a.scp?alert(updraftlion.settings_test_result.replace("%s","SCP")+" "+t.output):alert(updraftlion.settings_test_result.replace("%s","SFTP")+" "+t.output),t.hasOwnProperty("data")&&t.data&&t.data.hasOwnProperty("valid_md5_fingerprint")&&t.data.valid_md5_fingerprint&&h("#updraft_sftp_fingerprint_"+n).val(t.data.valid_md5_fingerprint),!0)},n)}),h("#updraft-navtab-settings-content select.updraft_interval, #updraft-navtab-settings-content select.updraft_interval_database").on("change",function(){updraft_check_same_times()}),h("#backupnow_includefiles_showmoreoptions").on("click",function(t){t.preventDefault(),h("#backupnow_includefiles_moreoptions").toggle()}),h("#backupnow_database_showmoreoptions").on("click",function(t){t.preventDefault(),h("#backupnow_database_moreoptions").toggle()}),h("#updraft-navtab-migrate-content").on("click","#backupnow_database_showmoreoptions",function(t){t.preventDefault(),h("#updraft-navtab-migrate-content #backupnow_database_moreoptions").toggle()}),h("#backupnow_db_anon_all").on("click",function(t){h("#backupnow_db_anon_non_staff").prop("checked")&&h("#backupnow_db_anon_non_staff").prop("checked",!1)}),h("#backupnow_db_anon_non_staff").on("click",function(t){h("#backupnow_db_anon_all").prop("checked")&&h("#backupnow_db_anon_all").prop("checked",!1)}),h("#updraft-navtab-migrate-content").on("click","#updraftplus_migration_backupnow_db_anon_all",function(){h("#updraftplus_migration_backupnow_db_anon_non_staff").prop("checked")&&h("#updraftplus_migration_backupnow_db_anon_non_staff").prop("checked",!1)}),h("#updraft-navtab-migrate-content").on("click","#updraftplus_migration_backupnow_db_anon_non_staff",function(){h("#updraftplus_migration_backupnow_db_anon_all").prop("checked")&&h("#updraftplus_migration_backupnow_db_anon_all").prop("checked",!1)}),h("#updraft-navtab-migrate-content").on("click","#updraftplus_clone_backupnow_db_anon_all",function(){h("#updraftplus_clone_backupnow_db_anon_non_staff").prop("checked")&&h("#updraftplus_clone_backupnow_db_anon_non_staff").prop("checked",!1)}),h("#updraft-navtab-migrate-content").on("click","#updraftplus_clone_backupnow_db_anon_non_staff",function(){h("#updraftplus_clone_backupnow_db_anon_all").prop("checked")&&h("#updraftplus_clone_backupnow_db_anon_all").prop("checked",!1)}),h("#updraft-backupnow-modal").on("click","#backupnow_includecloud_showmoreoptions",function(t){t.preventDefault(),h("#backupnow_includecloud_moreoptions").toggle()}),h("#updraft-navtab-backups-content").on("click","a.updraft_diskspaceused_update",function(t){t.preventDefault(),updraftplus_diskspace()}),h(".advanced_settings_content a.updraft_diskspaceused_update").on("click",function(t){t.preventDefault(),jQuery(".advanced_settings_content .updraft_diskspaceused").html("<em>"+updraftlion.calculating+"</em>"),updraft_send_command("get_fragment",{fragment:"disk_usage",data:"updraft"},function(t){jQuery(".advanced_settings_content .updraft_diskspaceused").html(t.output)},{type:"GET"})}),h("#updraft-navtab-backups-content a.updraft_uploader_toggle").on("click",function(t){t.preventDefault(),h("#updraft-plupload-modal").slideToggle()}),h("#updraft-navtab-backups-content a.updraft_rescan_local").on("click",function(t){t.preventDefault(),updraft_updatehistory(1,0)}),h("#updraft-navtab-backups-content a.updraft_rescan_remote").on("click",function(t){t.preventDefault(),confirm(updraftlion.remote_scan_warning)&&updraft_updatehistory(1,1)}),h("#updraftplus-remote-rescan-debug").on("click",function(t){t.preventDefault(),updraft_updatehistory(1,1,1)}),jQuery("#updraft_reset_sid").on("click",function(t){t.preventDefault(),updraft_send_command("reset_site_id",null,function(t){jQuery("#updraft_show_sid").html(t)},{json_parse:!1})}),jQuery("#updraft-navtab-settings-content").on("input","form input:not('.udignorechange'), form textarea:not('.udignorechange')",function(t){updraft_settings_form_changed=!0,load_save_button()}),jQuery("#updraft-navtab-settings-content").on("change","form select",function(t){updraft_settings_form_changed=!0,load_save_button()}),jQuery("#updraft-navtab-settings-content").on("click","form input[type='submit']",function(t){updraft_settings_form_changed=!1});var s=180,t=(jQuery(".updraft-bigbutton").each(function(t,e){e=jQuery(e).width();s<e&&(s=e)}),180<s&&jQuery(".updraft-bigbutton").width(s),jQuery("#updraft-navtab-backups-content").length&&setInterval(function(){updraft_activejobs_update(!1)},1250),setTimeout(function(){jQuery("#setting-error-settings_updated").slideUp()},5e3),jQuery("#updraft_restore_db").on("change",function(){jQuery("#updraft_restore_db").is(":checked")&&1==jQuery(this).data("encrypted")?jQuery("#updraft_restorer_dboptions").slideDown():jQuery("#updraft_restorer_dboptions").slideUp()}),updraft_check_same_times(),{}),t=(t[updraftlion.close]=function(){jQuery(this).dialog("close")},jQuery("#updraft-message-modal").dialog({autoOpen:!1,resizeOnWindowResize:!0,scrollWithViewport:!0,resizeAccordingToViewport:!0,useContentSize:!1,open:function(t,e){h(this).dialog("option","width",520),h(this).dialog("option","minHeight",260),360<h(window).height()?h(this).dialog("option","height",360):h(this).dialog("option","height",h(window).height()-30)},modal:!0,buttons:t}),{});t[updraftlion.deletebutton]=function(){!function n(t,e,a,r,o,d){jQuery("#updraft-delete-modal").dialog("close");var s=t;var u=e;var i=a;var p=r;var l="";t=jQuery("#updraft_delete_form").serializeArray();var _={};h.each(t,function(){void 0!==_[this.name]?(_[this.name].push||(_[this.name]=[_[this.name]]),_[this.name].push(this.value||"")):_[this.name]=this.value||""});_.delete_remote?jQuery("#updraft-delete-waitwarning").find(".updraft-deleting-remote").show():jQuery("#updraft-delete-waitwarning").find(".updraft-deleting-remote").hide();jQuery("#updraft-delete-waitwarning").slideDown().addClass("active");_.remote_delete_limit=updraftlion.remote_delete_limit;_.processed_instance_ids=o;_.is_continuation=d;delete _.action;delete _.subaction;delete _.nonce;updraft_send_command("deleteset",_,function(t){if(t.hasOwnProperty("result")&&null!=t.result){if("error"==t.result)jQuery("#updraft-delete-waitwarning").slideUp(),alert(updraftlion.error+" "+t.message);else if("continue"==t.result){s=s+t.backup_local+t.backup_remote,u+=t.backup_local,i+=t.backup_remote,p+=t.backup_sets;for(var e=t.deleted_timestamps.split(","),a=0;a<e.length;a++){var r=e[a];jQuery("#updraft-navtab-backups-content .updraft_existing_backups_row_"+r).slideUp().remove()}jQuery("#updraft_delete_timestamp").val(t.timestamps),jQuery("#updraft-deleted-files-total").text(s+" "+updraftlion.remote_files_deleted),n(s,u,i,p,t.processed_instance_ids,!0)}else if("success"==t.result){setTimeout(function(){jQuery("#updraft-deleted-files-total").text(""),jQuery("#updraft-delete-waitwarning").slideUp()},500),update_backupnow_modal(t),t.hasOwnProperty("backupnow_file_entities")&&(impossible_increment_entities=t.backupnow_file_entities),t.hasOwnProperty("count_backups")&&jQuery("#updraft-existing-backups-heading").html(updraftlion.existing_backups+' <span class="updraft_existing_backups_count">'+t.count_backups+"</span>");for(e=t.deleted_timestamps.split(","),a=0;a<e.length;a++){r=e[a];jQuery("#updraft-navtab-backups-content .updraft_existing_backups_row_"+r).slideUp().remove()}updraft_backups_selection.checkSelectionStatus(),updraft_history_lastchecksum=!1,u+=t.backup_local,i+=t.backup_remote,p+=t.backup_sets,""!=t.error_messages&&(l=updraftlion.delete_error_log_prompt),setTimeout(function(){alert(t.set_message+" "+p+"\n"+t.local_message+" "+u+"\n"+t.remote_message+" "+i+"\n\n"+t.error_messages+"\n"+l)},900)}}else jQuery("#updraft-delete-waitwarning").slideUp()})}(0,0,0,0,[],!1)},t[updraftlion.cancel]=function(){jQuery(this).dialog("close")},jQuery("#updraft-delete-modal").dialog({autoOpen:!1,resizeOnWindowResize:!0,scrollWithViewport:!0,resizeAccordingToViewport:!0,useContentSize:!1,open:function(t,e){h(this).css("minHeight",83)},modal:!0,buttons:t});var u,i={initialized:!1,init:function(){this.initialized||(this.initialized=!0,h(".updraft-restore--cancel").on("click",function(t){t.preventDefault(),jQuery("#ud_downloadstatus2").html(""),this.close()}.bind(this)),this.default_next_text=h(".updraft-restore--next-step").eq(0).text(),h(".updraft-restore--next-step").on("click",function(t){t.preventDefault(),this.process_next_action()}.bind(this)))},close:function(){h(".updraft_restore_container").hide(),h("body").removeClass("updraft-modal-is-opened")},open:function(){this.init(),h("#updraft-restore-modal-stage1").show(),h("#updraft-restore-modal-stage2").hide(),h("#updraft-restore-modal-stage2a").html(""),h(".updraft-restore--next-step").text(this.default_next_text),h(".updraft-restore--stages li").removeClass("active").first().addClass("active"),h(".updraft_restore_container").show(),h("body").addClass("updraft-modal-is-opened")},process_next_action:function(){var r=0,n=0,o=0,d=0,s=0,u=[],i=0,p=h("#updraft_restore_meta_foreign").val();if(h('input[name="updraft_restore[]"]').each(function(t,e){var a;h(e).is(":checked")&&!h(e).is(":disabled")&&(r=1,a=h(e).data("howmany"),"more"==(e=h(e).val())&&(n=1),"db"==e&&(o=1),"plugins"==e&&(d=1),"themes"==e&&(s=1),(1==p||2==p&&"db"!=e)&&("wpcore"!=e&&(a=h("#updraft_restore_form #updraft_restore_wpcore").data("howmany")),e="wpcore"),"wpcore"==e&&0!=i||(u.push([e,a]),"wpcore"==e&&(i=1)))}),1==r){if(1==updraft_restore_stage)h(".updraft-restore--stages li").removeClass("active").eq(1).addClass("active"),h("#updraft-restore-modal-stage1").slideUp("slow",function(){h("#updraft-restore-modal-stage2").show(100,function(){updraft_restore_stage=2;var a=h(".updraft_restore_date").first().text(),r=u,n=h("#updraft_restore_timestamp").val();try{h(".updraft-restore--next-step").prop("disabled",!0),h("#updraft-restore-modal-stage2a").html('<span class="dashicons dashicons-update rotate"></span> '+updraftlion.maybe_downloading_entities),updraft_send_command("whichdownloadsneeded",{downloads:u,timestamp:n},function(t){if(h(".updraft-restore--next-step").prop("disabled",!1),t.hasOwnProperty("downloads")&&(console.log("UpdraftPlus: items which still require downloading follow"),r=t.downloads,console.log(r)),0==r.length)updraft_restorer_checkstage2(0);else for(var e=0;e<r.length;e++)updraft_downloader("udrestoredlstatus_",n,r[e][0],"#ud_downloadstatus2",r[e][1],a,!0)},{alert_on_error:!1,error_callback:function(t,e,a,r){void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),h("#updraft-restore-modal-stage2a").html('<p style="color:red;">'+r.fatal_error_message+"</p>")):(r="updraft_send_command: error: "+e+" ("+a+")",h("#updraft-restore-modal-stage2a").html('<p style="color:red; margin: 5px;">'+r+"</p>"),console.log(r),console.log(t))}})}catch(t){console.log("UpdraftPlus: error (follows) when looking for items needing downloading"),console.log(t),alert(updraftlion.jsonnotunderstood)}})});else if(2==updraft_restore_stage)updraft_restorer_checkstage2(1);else if(3==updraft_restore_stage){var l=1;if(jQuery(".updraft-restore--next-step, .updraft-restore--cancel").prop("disabled",!0),h("#updraft_restoreoptions_ui input.required").each(function(t){var e,a;0!=l&&(""==(e=h(this).val())?(alert(updraftlion.pleasefillinrequired),jQuery(".updraft-restore--next-step, .updraft-restore--cancel").prop("disabled",!1),l=0):""!=h(this).attr("pattern")&&(a=h(this).attr("pattern"),new RegExp(a,"g").test(e)||(alert(h(this).data("invalidpattern")),jQuery(".updraft-restore--next-step, .updraft-restore--cancel").prop("disabled",!1),l=0)))}),1!=o||(r=0,jQuery('input[name="updraft_restore_tables_options[]"').each(function(t,e){jQuery(e).is(":checked")&&!jQuery(e).is(":disabled")&&(r=1)}),0!=r)||skipped_db_scan){if(1==d&&(r=0,jQuery(".updraftplus_restore_plugins_options_container").length||(r=1),jQuery('input[name="updraft_restore_plugins_options[]"').each(function(t,e){jQuery(e).is(":checked")&&!jQuery(e).is(":disabled")&&(r=1)}),0==r))alert(updraftlion.youdidnotselectany),jQuery(".updraft-restore--next-step, .updraft-restore--cancel").prop("disabled",!1);else if(1==s&&(r=0,jQuery(".updraftplus_restore_themes_options_container").length||(r=1),jQuery('input[name="updraft_restore_themes_options[]"').each(function(t,e){jQuery(e).is(":checked")&&!jQuery(e).is(":disabled")&&(r=1)}),0==r))alert(updraftlion.youdidnotselectany),jQuery(".updraft-restore--next-step, .updraft-restore--cancel").prop("disabled",!1);else if(1==n&&(r=0,jQuery('input[name="updraft_include_more_index[]"').each(function(t,e){jQuery(e).is(":checked")&&!jQuery(e).is(":disabled")&&(r=1,""==jQuery("#updraft_include_more_path_restore"+t).val())&&alert(updraftlion.emptyrestorepath)}),0==r))alert(updraftlion.youdidnotselectany),jQuery(".updraft-restore--next-step, .updraft-restore--cancel").prop("disabled",!1);else if(l){var _=h("#updraft_restoreoptions_ui select, #updraft_restoreoptions_ui input").serialize();if(jQuery.each(["tables","plugins","themes"],function(t,a){jQuery.each(jQuery('input[name="updraft_restore_'+a+"_options[]").filter(function(t){return!1===jQuery(this).prop("checked")}),function(t,e){_+="&"+jQuery(e).attr("name")+"=udp-skip-"+a+"-"+jQuery(e).val()})}),console.log("Restore options: "+_),void 0!==php_max_input_vars){var t,e=_.split("&").length,a='<div class="udp-notice notice-warning"><p><span class="dashicons dashicons-warning"></span> <strong>'+updraftlion.warnings+'</strong></p><ul id="updraft_restore_warnings">';if(!php_max_input_vars&&1e3<=e)console.log("Restore options: "+e+" PHP max input vars not detected; using default: 1000");else if(php_max_input_vars&&php_max_input_vars<=e)return t="<li>"+updraftlion.php_max_input_vars_detected_warning+"</li>",1!=jQuery("#updraft-restore-modal-stage2a .notice-warning").length?(a=a+t+"</ul></div>",jQuery("#updraft_restoreoptions_ui").prepend(a)):jQuery("#updraft-restore-modal-stage2a #updraft_restore_warnings").append(t),console.log("Restore options: "+e+" PHP max input vars: "+php_max_input_vars),jQuery(".updraft-restore--next-step, .updraft-restore--cancel").prop("disabled",!1),void(php_max_input_vars=void 0)}h("#updraft_restorer_restore_options").val(_),h("#updraft-restore-modal-stage2a").html(updraftlion.restore_proceeding),h("#updraft_restore_form").trigger("submit"),updraft_restore_stage=4}}else alert(updraftlion.youdidnotselectany),jQuery(".updraft-restore--next-step, .updraft-restore--cancel").prop("disabled",!1)}}else alert(updraftlion.youdidnotselectany)}},p=h(".updraft_restore_main--activity").width(),t=(jQuery("#activity-full-log").on("click",function(){var t="1460px"==h(".updraft_restore_main").css("max-width")?"860px":"1460px",e=h(".updraft_restore_main--activity").width()==p?"100%":p+"px",a="600px"==h(".updraft_restore_main--activity").css("min-height")?"0px":"600px",r=h("#activity-full-log").attr("title")==updraftlion.restoreactivitylogscreenexit?updraftlion.restoreactivitylogfullscreen:updraftlion.restoreactivitylogscreenexit;h("#activity-full-log").toggleClass("dashicons-fullscreen-exit-alt"),h("#activity-full-log").attr("title",r),h(".updraft_restore_main--components").toggle("fast"),h(".updraft_restore_main--header").toggle("fast"),h(".updraft_restore_main--activity").animate({minHeight:a,width:e}),h(".updraft_restore_main").animate({maxWidth:t})}),jQuery("#updraft-iframe-modal").dialog({autoOpen:!1,height:500,width:780,modal:!0}),jQuery("#updraft-backupnow-inpage-modal").dialog({autoOpen:!1,modal:!0,resizeOnWindowResize:!0,scrollWithViewport:!0,resizeAccordingToViewport:!0,useContentSize:!1,open:function(t,e){h(this).dialog("option","width",580),h(this).dialog("option","minHeight",261),h(this).dialog("option","height",380)}}),{});t[updraftlion.backupnow]=function(){var t,e,a=jQuery("#backupnow_includedb").is(":checked")?0:1,r=jQuery("#backupnow_includefiles").is(":checked")?0:1,n=jQuery("#backupnow_includecloud").is(":checked")?0:1,o=jQuery("#backupnow_db_anon_all").is(":checked")?1:0,d=jQuery("#backupnow_db_anon_non_staff").is(":checked")?1:0,s=jQuery("#backupnow_db_anon_wc_order_data").is(":checked")?1:0,u=backupnow_whichtables_checked(""),i=jQuery("#always_keep").is(":checked")?1:0,p="incremental"==jQuery("#updraft-backupnow-modal").data("backup-type")?1:0;updraftlion.hosting_restriction.includes("only_one_backup_per_month")&&!p?alert(updraftlion.hosting_restriction_one_backup_permonth):updraftlion.hosting_restriction.includes("only_one_incremental_per_day")&&p?alert(updraftlion.hosting_restriction_one_incremental_perday):""==u&&0==a?(alert(updraftlion.notableschosen),jQuery("#backupnow_database_moreoptions").show()):("boolean"==typeof u&&(u=null),""==(t=backupnow_whichfiles_checked(""))&&0==r?(alert(updraftlion.nofileschosen),jQuery("#backupnow_includefiles_moreoptions").show()):""==(e=jQuery("input[name^='updraft_include_remote_service_']").serializeArray())&&0==n?(alert(updraftlion.nocloudserviceschosen),jQuery("#backupnow_includecloud_moreoptions").show()):("boolean"==typeof e&&(e=null),a&&r?alert(updraftlion.excludedeverything):(jQuery(this).dialog("close"),setTimeout(function(){jQuery("#updraft_lastlogmessagerow").fadeOut("slow",function(){jQuery(this).fadeIn("slow")})},1700),updraft_backupnow_go(a,r,n,t,{always_keep:i,incremental:p,db_anon:{all:o,non_staff:d,wc_orders:s}},jQuery("#backupnow_label").val(),u,e))))},t[updraftlion.cancel]=function(){jQuery(this).dialog("close")},jQuery("#updraft-backupnow-modal").dialog({autoOpen:!1,resizeOnWindowResize:!0,scrollWithViewport:!0,resizeAccordingToViewport:!0,useContentSize:!1,open:function(t,e){h(this).dialog("option","width",610),h(this).dialog("option","minHeight",300),h(this).dialog("option","height",472)},modal:!0,buttons:t,create:function(){h(this).closest(".ui-dialog").find(".ui-dialog-buttonpane .ui-button").first().addClass("js-tour-backup-now-button")}}),jQuery("#updraft-poplog").dialog({autoOpen:!1,modal:!0,resizeOnWindowResize:!0,scrollWithViewport:!0,resizeAccordingToViewport:!0,useContentSize:!1,open:function(t,e){h(this).dialog("option","width",860),h(this).dialog("option","minHeight",260),600<h(window).height()?h(this).dialog("option","height",600):h(this).dialog("option","height",h(window).height()-50)}}),jQuery("#updraft-navtab-settings-content .enableexpertmode").on("click",function(){return jQuery("#updraft-navtab-settings-content .expertmode").fadeIn(),jQuery("#updraft-navtab-settings-content .enableexpertmode").off("click"),!1}),jQuery("#updraft-navtab-settings-content .backupdirrow").on("click","a.updraft_backup_dir_reset",function(){return jQuery("#updraft_dir").val("updraft"),!1}),jQuery("#updraft-navtab-settings-content .updraft_include_entity").on("click",function(){var t,e=jQuery(this).data("toggle_exclude_field");e&&(e=e,t=!1,jQuery("#updraft-navtab-settings-content #updraft_include_"+e).is(":checked")?t?jQuery("#updraft-navtab-settings-content #updraft_include_"+e+"_exclude_container").show():jQuery("#updraft-navtab-settings-content #updraft_include_"+e+"_exclude_container").slideDown():t?jQuery("#updraft-navtab-settings-content #updraft_include_"+e+"_exclude").hide():jQuery("#updraft-navtab-settings-content #updraft_include_"+e+"_exclude_container").slideUp())}),jQuery(".updraft_exclude_entity_container").on("click",".updraft_exclude_entity_delete",function(t){t.preventDefault(),confirm(updraftlion.exclude_rule_remove_conformation_msg)&&(t=jQuery(this).data("include-backup-file"),jQuery.when(jQuery(this).closest(".updraft_exclude_entity_wrapper").remove()).then(updraft_exclude_entity_update(t)))}),jQuery(".updraft_exclude_entity_container").on("click",".updraft_exclude_entity_edit",function(t){t.preventDefault();var t=jQuery(this).hide().closest(".updraft_exclude_entity_wrapper"),e=t.find("input"),a=(e.prop("readonly",!1).trigger("focus"),e.val());e.val(""),e.val(a),t.find(".updraft_exclude_entity_update").addClass("is-active").show()}),jQuery(".updraft_exclude_entity_container").on("click",".updraft_exclude_entity_update",function(t){t.preventDefault();var e=jQuery(this).closest(".updraft_exclude_entity_wrapper"),a=jQuery(this).data("include-backup-file"),t=e.find("input").val().trim(),r=!1;(r=t==e.find("input").data("val")||updraft_is_unique_exclude_rule(t,a)?!0:r)&&(jQuery(this).hide().removeClass("is-active"),jQuery.when(e.find("input").prop("readonly","readonly").data("val",t)).then(function(){e.find(".updraft_exclude_entity_edit").show(),updraft_exclude_entity_update(a)}))}),jQuery("#updraft_exclude_modal").dialog({autoOpen:!1,modal:!0,resizeOnWindowResize:!0,scrollWithViewport:!0,resizeAccordingToViewport:!0,useContentSize:!1,open:function(t,e){h(this).parent().trigger("focus"),h(this).dialog("option","width",520),h(this).dialog("option","minHeight",260),579<h(window).height()?h(this).css("height","auto"):h(window).height()<580&&410<h(window).height()?(h(this).dialog("option","height",410),h(this).css("height","auto")):h(this).dialog("option","height",h(window).height()-20)}}),jQuery("#updraft_include_others_exclude_container, #updraft_include_uploads_exclude_container, .updraft_exclude_container").on("click","a.updraft_add_exclude_item",function(t){t.preventDefault();t=jQuery(this).data("include-backup-file");jQuery("#updraft_exclude_modal_for").val(t),jQuery("#updraft_exclude_modal_path").val(jQuery(this).data("path")),"uploads"==t&&jQuery("#updraft-exclude-file-dir-prefix").html(jQuery("#updraft-exclude-upload-base-dir").val()),jQuery(".updraft-exclude-modal-reset").trigger("click"),jQuery("#updraft_exclude_modal").dialog("open")}),jQuery(".updraft-exclude-link").on("click",function(t){t.preventDefault();t=jQuery(this).data("panel");"file-dir"==t?jQuery("#updraft_exclude_files_folders_jstree").jstree({core:{multiple:!1,data:function(t,e){updraft_send_command("get_jstree_directory_nodes",{entity:"filebrowser",node:t,path:jQuery("#updraft_exclude_modal_path").val(),findex:0,skip_root_node:!0},function(t){t.hasOwnProperty("error")?alert(t.error):e.call(this,t.nodes)},{error_callback:function(t,e,a,r){void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),jQuery("#updraft_zip_files_jstree").html('<p style="color:red; margin: 5px;">'+r.fatal_error_message+"</p>"),alert(r.fatal_error_message)):(r="updraft_send_command: error: "+e+" ("+a+")",jQuery("#updraft_zip_files_jstree").html('<p style="color:red; margin: 5px;">'+r+"</p>"),console.log(r),alert(r),console.log(t))}})},error:function(t){alert(t),console.log(t)}},search:{show_only_matches:!0},plugins:["sort"]}):"contain-clause"==t&&jQuery("#updraft_exclude_files_folders_wildcards_jstree").jstree({core:{multiple:!1,data:function(t,e){updraft_send_command("get_jstree_directory_nodes",{entity:"filebrowser",directories_only:1,node:t,path:jQuery("#updraft_exclude_modal_path").val(),findex:0,skip_root_node:0},function(t){t.hasOwnProperty("error")?alert(t.error):e.call(this,t.nodes)},{error_callback:function(t,e,a,r){void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),jQuery("#updraft_zip_files_jstree").html('<p style="color:red; margin: 5px;">'+r.fatal_error_message+"</p>"),alert(r.fatal_error_message)):(r="updraft_send_command: error: "+e+" ("+a+")",jQuery("#updraft_zip_files_jstree").html('<p style="color:red; margin: 5px;">'+r+"</p>"),console.log(r),alert(r),console.log(t))}})},error:function(t){alert(t),console.log(t)}},search:{show_only_matches:!0},plugins:["sort"]}),jQuery("#updraft_exclude_modal_main").slideUp(),jQuery(".updraft-exclude-panel").hide(),jQuery(".updraft-exclude-panel[data-panel="+t+"]").slideDown()}),jQuery(".updraft-exclude-modal-reset").on("click",function(t){t.preventDefault(),jQuery("#updraft_exclude_files_folders_jstree").jstree("destroy"),jQuery("#updraft_exclude_files_folders_wildcards_jstree").jstree("destroy"),jQuery("#updraft_exclude_extension_field").val(""),jQuery("#updraft_exclude_prefix_field").val(""),jQuery(".updraft-exclude-panel").slideUp(),jQuery("#updraft_exclude_modal_main").slideDown()}),jQuery(".updraft-exclude-submit").on("click",function(){var t,e="";switch(jQuery(this).data("panel")){case"file-dir":if(0==(t=jQuery("#updraft_exclude_files_folders_jstree").jstree("get_selected")).length)return void alert(updraftlion.exclude_select_file_or_folder_msg);var a=t[0],r=jQuery("#updraft_exclude_modal_path").val(),e=a="/"==(a="/"==(a=a.substr(0,r.length)==r?a.substr(r.length,a.length):a).charAt(0)?a.substr(1):a).charAt(a.length-1)?a.substr(0,a.length-1):a;break;case"extension":var n=jQuery("#updraft_exclude_extension_field").val();if(""==n)return void alert(updraftlion.exclude_type_ext_msg);if(!n.match(/^[0-9a-zA-Z]+$/))return void alert(updraftlion.exclude_ext_error_msg);e="ext:"+n;break;case"begin-with":n=jQuery("#updraft_exclude_prefix_field").val();if(""==n)return void alert(updraftlion.exclude_type_prefix_msg);if(!n.match(/^\s*[a-z-_\d,\s]+\s*$/i))return void alert(updraftlion.exclude_prefix_error_msg);e="prefix:"+n;break;case"contain-clause":if(0==(t=jQuery("#updraft_exclude_files_folders_wildcards_jstree").jstree("get_selected")).length)return void alert(updraftlion.exclude_select_folder_wildcards_msg);var n=jQuery(this).parents("div.updraft-exclude-panel").find("div.clause-input-container input").val(),o=(jQuery(this).parents("div.updraft-exclude-panel").find("div.clause-input-container input").val(""),jQuery(this).parents("div.updraft-exclude-panel").find("div.clause-input-container select").val());if(""==n)return void alert(updraftlion.exclude_contain_error_msg);jQuery(this).parents("div.updraft-exclude-panel").find("div.clause-input-container select option").eq(0).prop("selected",!0);a=t[0],r=jQuery("#updraft_exclude_modal_path").val();""!==(e=a="/"==(a="/"==(a=a.substr(0,r.length)==r?a.substr(r.length,a.length):a).charAt(0)?a.substr(1):a).charAt(a.length-1)?a.substr(0,a.length-1):a)&&(e+="/"),n=n.replace(/\*/g,"\\*"),"beginning"===o?e+=n+"*":"middle"===o?e+="*"+n+"*":"end"===o&&(e+="*"+n);break;default:return}var d,s=jQuery("#updraft_exclude_modal_for").val();updraft_is_unique_exclude_rule(e,s)&&(d='<div class="updraft_exclude_entity_wrapper"><input type="text" class="updraft_exclude_entity_field updraft_include_'+s+'_exclude_entity" name="updraft_include_'+s+'_exclude_entity[]" value="'+e+'" data-val="'+e+'" data-include-backup-file="'+s+'" readonly="readonly"><a href="#" class="updraft_exclude_entity_edit dashicons dashicons-edit" data-include-backup-file="'+s+'"></a><a href="#" class="updraft_exclude_entity_update dashicons dashicons-yes" data-include-backup-file="'+s+'" style="display: none;"></a><a href="#" class="updraft_exclude_entity_delete dashicons dashicons-no" data-include-backup-file="'+s+'"></a></div>',jQuery('.updraft_exclude_entity_container[data-include-backup-file="'+s+'"]').append(d),updraft_exclude_entity_update(s),jQuery("#updraft_exclude_modal").dialog("close"))}),jQuery("#updraft-navtab-settings-content .updraft-service").on("change",function(){var t=jQuery(this).val();jQuery("#updraft-navtab-settings-content .updraftplusmethod").hide(),jQuery("#updraft-navtab-settings-content ."+t).show()}),jQuery("#updraft-navtab-settings-content a.updraft_show_decryption_widget").on("click",function(t){t.preventDefault(),jQuery("#updraftplus_db_decrypt").val(jQuery("#updraft_encryptionphrase").val()),jQuery("#updraft-manualdecrypt-modal").slideToggle()}),jQuery("#updraftplus-phpinfo").on("click",function(t){t.preventDefault(),updraft_iframe_modal("phpinfo",updraftlion.phpinfo)}),jQuery("#updraftplus-rawbackuphistory").on("click",function(t){t.preventDefault(),updraft_iframe_modal("rawbackuphistory",updraftlion.raw)}),jQuery("#updraft-navtab-status").on("click",function(t){t.preventDefault(),updraft_open_main_tab("status"),updraft_page_is_visible=1,updraft_console_focussed_tab="status",updraft_activejobs_update(!0)}),jQuery("#updraft-navtab-expert").on("click",function(t){t.preventDefault(),updraft_open_main_tab("expert"),updraft_page_is_visible=1}),jQuery("#updraft-navtab-settings, #updraft-navtab-settings2, #updraft_backupnow_gotosettings").on("click",function(t){t.preventDefault(),jQuery(this).parents(".updraftmessage").remove(),jQuery("#updraft-backupnow-modal").dialog("close"),updraft_open_main_tab("settings"),updraft_page_is_visible=1}),jQuery("#updraft-navtab-addons").on("click",function(t){t.preventDefault(),jQuery(this).addClass("b#nav-tab-active"),updraft_open_main_tab("addons"),updraft_page_is_visible=1}),jQuery("#updraft-navtab-backups").on("click",function(t){t.preventDefault(),updraft_console_focussed_tab="backups",updraft_historytimertoggle(1),updraft_open_main_tab("backups")}),jQuery("#updraft-navtab-migrate").on("click",function(t){t.preventDefault(),jQuery("#updraft_migrate_tab_alt").html("").hide(),updraft_open_main_tab("migrate"),updraft_page_is_visible=1,jQuery("#updraft_migrate .updraft_migrate_widget_module_content").is(":visible")||jQuery(".updraft_migrate_intro").show()}),"migrate"==updraftlion.tab&&jQuery("#updraft-navtab-migrate").trigger("click"),updraft_send_command("ping",null,function(t,e){"success"==e&&"pong"!=t&&0<=t.indexOf("pong")&&(jQuery("#updraft-navtab-backups-content .ud-whitespace-warning").show(),console.log("UpdraftPlus: Extra output warning: response (which should be just (string)'pong') follows."),console.log(t))},{json_parse:!1,type:"GET"});try{"undefined"!=typeof updraft_plupload_config&&((u=new plupload.Uploader(updraft_plupload_config)).bind("Init",function(t){var e=jQuery("#plupload-upload-ui");t.features.dragdrop?(e.addClass("drag-drop"),jQuery("#drag-drop-area").on("dragover.wp-uploader",function(){e.addClass("drag-over")}).on("dragleave.wp-uploader, drop.wp-uploader",function(){e.removeClass("drag-over")})):(e.removeClass("drag-drop"),jQuery("#drag-drop-area").off(".wp-uploader"))}),u.init(),u.bind("FilesAdded",function(t,e){h("#updraft-plupload-modal").is(":hidden")&&h("#updraft-plupload-modal").slideToggle(),plupload.each(e,function(t){if(!/^backup_([\-0-9]{15})_.*_([0-9a-f]{12})-[\-a-z]+([0-9]+?)?(\.(zip|gz|gz\.crypt))?$/i.test(t.name)&&!/^log\.([0-9a-f]{12})\.txt$/.test(t.name)){for(var e=!1,a=0;a<updraft_accept_archivename.length;a++)updraft_accept_archivename[a].test(t.name)&&(e=!0);if(!e)return/\.(zip|tar|tar\.gz|tar\.bz2)$/i.test(t.name)||/\.sql(\.gz)?$/i.test(t.name)?(jQuery("#updraft-message-modal-innards").html("<p><strong>"+t.name+"</strong></p> "+updraftlion.notarchive2),jQuery("#updraft-message-modal").dialog("open")):alert(t.name+": "+updraftlion.notarchive),void u.removeFile(t)}jQuery("#filelist").append('<div class="file" id="'+t.id+'"><b>'+t.name+"</b> (<span>"+plupload.formatSize(0)+"</span>/"+plupload.formatSize(t.size)+') <div class="fileprogress"></div></div>')}),t.refresh(),t.start()}),u.bind("UploadProgress",function(t,e){jQuery("#"+e.id+" .fileprogress").width(e.percent+"%"),jQuery("#"+e.id+" span").html(plupload.formatSize(parseInt(e.size*e.percent/100))),e.size==e.loaded&&(jQuery("#"+e.id).html('<div class="file" id="'+e.id+'"><b>'+e.name+"</b> (<span>"+plupload.formatSize(parseInt(e.size*e.percent/100))+"</span>/"+plupload.formatSize(e.size)+") - "+updraftlion.complete+"</div>"),jQuery("#"+e.id+" .fileprogress").width(e.percent+"%"))}),u.bind("Error",function(t,e){console.log(e),a="-200"==e.code?"\n"+updraftlion.makesure2:updraftlion.makesure;var a,r=updraftlion.uploaderr+" (code "+e.code+") : "+e.message;e.hasOwnProperty("status")&&e.status&&(r+=" ("+updraftlion.http_code+" "+e.status+")"),e.hasOwnProperty("response")&&(console.log("UpdraftPlus: plupload error: "+e.response),e.response.length<100)&&(r+=" "+updraftlion.error+" "+e.response+"\n"),r+=" "+a,alert(r)}),u.bind("FileUploaded",function(t,e,a){if("200"==a.status)try{(resp=ud_parse_json(a.response)).e?alert(updraftlion.uploaderror+" "+resp.e):resp.dm?(alert(resp.dm),updraft_updatehistory(1,0)):resp.m?updraft_updatehistory(1,0):alert("Unknown server response: "+a.response)}catch(t){console.log(a),alert(updraftlion.jsonnotunderstood)}else alert("Unknown server response status: "+a.code),console.log(a)}))}catch(t){console.log(t)}function l(t){(params={uri:jQuery("#updraftplus_httpget_uri").val()}).curl=t,updraft_send_command("httpget",params,function(t){t.e&&alert(t.e),t.r?jQuery("#updraftplus_httpget_results").html("<pre>"+t.r+"</pre>"):console.log(t)},{type:"GET"})}function _(t,e,a){updraft_restore_setoptions(t),e.toString().match(/^[0-9]+$/i)&&jQuery("#updraft_restore_timestamp").val(e),jQuery(".updraft_restore_date").text(a),updraft_restore_stage=1,i.open(),updraft_activejobs_update(!0)}function c(t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");t=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(window.location.href);return null==t?"":decodeURIComponent(t[1].replace(/\+/g," "))}jQuery("#updraftplus_httpget_go").on("click",function(t){t.preventDefault(),l(0)}),jQuery("#updraftplus_httpget_gocurl").on("click",function(t){t.preventDefault(),l(1)}),jQuery("#updraftplus_callwpaction_go").on("click",function(t){t.preventDefault(),updraft_send_command("call_wordpress_action",params={wpaction:jQuery("#updraftplus_callwpaction").val()},function(t){t.e?alert(t.e):t.s||(t.r?jQuery("#updraftplus_callwpaction_results").html(t.r):(console.log(t),alert(updraftlion.jsonnotunderstood)))})}),jQuery("#updraft_activejobs_table, #updraft-navtab-migrate-content").on("click",".updraft_jobinfo_delete",function(t){t.preventDefault();t=jQuery(this).data("jobid");t?(h(this).addClass("disabled"),updraft_activejobs_delete(t)):console.log("UpdraftPlus: A stop job link was clicked, but the Job ID could not be found")}),jQuery("#updraft_activejobs_table, #updraft-navtab-backups-content .updraft_existing_backups, #updraft-backupnow-inpage-modal, #updraft-navtab-migrate-content").on("click",".updraft-log-link",function(t){t.preventDefault();var t=jQuery(this).data("fileid"),e=jQuery(this).data("jobid");t?updraft_popuplog(t):e?updraft_popuplog(e):console.log("UpdraftPlus: A log link was clicked, but the Job ID could not be found")}),jQuery("#updraft-navtab-backups-content .updraft_existing_backups").on("click","button.choose-components-button",function(t){_(jQuery(this).data("entities"),jQuery(this).data("backup_timestamp"),jQuery(this).data("showdata"))}),"initiate_restore"==c("udaction")&&_(c("entities"),c("backup_timestamp"),c("showdata"));t={};function f(){var t=jQuery(".db-search").val().toLowerCase();jQuery(".db-size-content tr").filter(function(){jQuery(this).toggle(-1<jQuery(this).text().toLowerCase().indexOf(t))})}t[updraftlion.uploadbutton]=function(){var t=jQuery("#updraft_upload_timestamp").val(),e=jQuery("#updraft_upload_nonce").val(),a="",r=!1;jQuery(".updraft_remote_storage_destination").each(function(t){jQuery(this).is(":checked")&&(r=!0)}),r?(a=jQuery("input[name^='updraft_remote_storage_destination_']").serializeArray(),jQuery(this).dialog("close"),alert(updraftlion.local_upload_started),updraft_send_command("upload_local_backup",{use_nonce:e,use_timestamp:t,services:a})):jQuery("#updraft-upload-modal-error").html(updraftlion.local_upload_error)},t[updraftlion.cancel]=function(){jQuery(this).dialog("close")},jQuery("#updraft-upload-modal").dialog({autoOpen:!1,modal:!0,resizeOnWindowResize:!0,scrollWithViewport:!0,resizeAccordingToViewport:!0,useContentSize:!1,open:function(t,e){h(this).parent().trigger("focus"),h(this).dialog("option","width",308),460<jQuery(window).height()?h(this).dialog("option","height",318):250<jQuery(window).height()&&jQuery(window).height()<461?h(this).dialog("option","height",460):h(this).dialog("option","height",jQuery(window).height()-20)},buttons:t}),jQuery("#updraft-navtab-backups-content .updraft_existing_backups").on("click","button.updraft-upload-link",function(t){t.preventDefault();var a,t=jQuery(this).data("nonce").toString(),e=jQuery(this).data("key").toString(),r=jQuery(this).data("services").toString();t?(e=e,t=t,r=r,jQuery("#updraft_upload_timestamp").val(e),jQuery("#updraft_upload_nonce").val(t),a=r.split(","),jQuery(".updraft_remote_storage_destination").each(function(t){var e=jQuery(this).val();-1==jQuery.inArray(e,a)&&(jQuery(this).prop("checked",!1),jQuery(this).prop("disabled",!0),e=h(this).prop("labels"),jQuery(e).find("span").show())}),jQuery("#updraft-upload-modal").dialog("open")):console.log("UpdraftPlus: A upload link was clicked, but the Job ID could not be found")}),jQuery("#updraft-navtab-backups-content .updraft_existing_backups").on("click",".updraft-load-more-backups",function(t){t.preventDefault(),updraft_updatehistory(0,0,0,parseInt(jQuery("#updraft-navtab-backups-content .updraft_existing_backups .updraft_existing_backups_row").length)+parseInt(updraftlion.existing_backups_limit))}),jQuery("#updraft-navtab-backups-content .updraft_existing_backups").on("click",".updraft-load-all-backups",function(t){t.preventDefault(),updraft_updatehistory(0,0,0,9999999)}),jQuery("#updraft-navtab-backups-content .updraft_existing_backups").on("click",".updraft-delete-link",function(t){t.preventDefault();var t=jQuery(this).data("hasremote"),e=jQuery(this).data("nonce").toString(),a=jQuery(this).data("key").toString();e?updraft_delete(a,e,t):console.log("UpdraftPlus: A delete link was clicked, but the Job ID could not be found")}),jQuery("#updraft-navtab-backups-content .updraft_existing_backups").on("click","button.updraft_download_button",function(t){t.preventDefault();updraft_downloader("uddlstatus_",jQuery(this).data("backup_timestamp"),jQuery(this).data("what"),".ud_downloadstatus",jQuery(this).data("set_contents"),jQuery(this).data("prettydate"),!0)}),jQuery("#updraft-navtab-backups-content .updraft_existing_backups").on("dblclick",".updraft_existingbackup_date",function(t){t.preventDefault();t=jQuery(this).data("nonce").toString();updraft_send_command("rawbackup_history",{timestamp:jQuery(this).data("timestamp").toString(),nonce:t},function(t){var e;t.hasOwnProperty("rawbackup")?((e=document.createElement("textarea")).innerHTML=t.rawbackup,updraft_html_modal(e.value,updraftlion.raw,780,500)):updraft_html_modal(updraftlion.jsonnotunderstood,updraftlion.raw,780,500)},{type:"POST"}),updraft_html_modal('<div style="margin:auto;text-align:center;margin-top:150px;"><img src="'+updraftlion.ud_url+'/images/udlogo-rotating.gif" /> <br>'+updraftlion.loading+"</div>",updraftlion.raw,780,500)}),jQuery("#backupnow_database_moreoptions").on("click","div.backupnow-db-tables > a",function(t){t.preventDefault(),jQuery("> input",jQuery(this).parents("div.backupnow-db-tables")).prop("checked",!1),jQuery(this).hasClass("backupnow-select-all-table")?jQuery("> input",jQuery(this).parents("div.backupnow-db-tables")).prop("checked",!0):jQuery(this).hasClass("backupnow-select-all-this-site")&&jQuery("> input",jQuery(this).parents("div.backupnow-db-tables")).not("[data-non_wp_table]").prop("checked",!0)}),jQuery("#updraft-restore-modal").on("click",".updraft_restore_select_all_themes",function(t){t.preventDefault(),jQuery(".updraft_restore_themes_options").prop("checked",!0)}),jQuery("#updraft-restore-modal").on("click",".updraft_restore_deselect_all_themes",function(t){t.preventDefault(),jQuery(".updraft_restore_themes_options").prop("checked",!1)}),jQuery("#updraft-restore-modal").on("click",".updraft_restore_select_all_plugins",function(t){t.preventDefault(),jQuery(".updraft_restore_plugins_options").prop("checked",!0)}),jQuery("#updraft-restore-modal").on("click",".updraft_restore_deselect_all_plugins",function(t){t.preventDefault(),jQuery(".updraft_restore_plugins_options").prop("checked",!1)}),jQuery(".updraftmessage.admin-warning-litespeed").on("click",".notice-dismiss",function(t){t.preventDefault(),updraft_send_command("dismiss_admin_warning_litespeed")}),jQuery(".updraftmessage.admin-warning-pclzip").on("click",".notice-dismiss",function(t){t.preventDefault(),updraft_send_command("dismiss_admin_warning_pclzip")}),jQuery("#db_size.advanced_tools_button, .db-size-refresh").on("click",function(t){t.preventDefault();var e=jQuery(".advanced_settings_content .advanced_tools.db_size .total-size"),a=jQuery(".advanced_settings_content .advanced_tools.db_size tbody.db-size-content");jQuery(this).hasClass("advanced_tools_button")&&""!=a.html()||(a.html(""),updraft_send_command("db_size",1,function(t){e.html(t.size),a.html(t.html),f()}))}),jQuery(".db-search").on("input",function(){f()}),jQuery(".db-search-clear").on("click",function(t){t.preventDefault(),jQuery(".db-search").val(""),f()}),jQuery(".updraft_restore_main").on("click","button#updraft_restore_abort",function(t){t.preventDefault(),jQuery("#updraft_restore_continue_action").val("updraft_restore_abort"),jQuery(this).parent("form").trigger("submit")}),jQuery("#cron_events.advanced_tools_button").on("click",function(t){t.preventDefault();var r=jQuery(".advanced_settings_content .advanced_tools.cron_events tbody");r.html(""),updraft_send_command("get_cron_events",1,function(t){h.each(t,function(t,e){var a="<td>";e.overdue&&(a='<td style="border-left:4px solid #DB6A03;">'),r.append(h("<tr>").append(h(a).text(e.hook),h("<td>").text(e.name))),e.overdue?r.find("tr:last").append('<td><span></span><br><span class="dashicons dashicons-warning" aria-hidden="true" style="color:#DB6A03"></span> <span></span></td>'):r.find("tr:last").append("<td><span></span><br><span></span></td>"),r.find("tr:last td:last span").not(".dashicons").first().text(e.time),r.find("tr:last td:last span").last().text(e.interval)})})})}),jQuery(function(n){var o="#updraft-navtab-settings-content ";n(o+"#remote-storage-holder").on("click",".updraftvault_backtostart",function(t){t.preventDefault(),n(o+"#updraftvault_settings_showoptions").slideUp(),n(o+"#updraftvault_settings_connect").slideUp(),n(o+"#updraftvault_settings_connected").slideUp(),n(o+"#updraftvault_settings_default").slideDown()}),n(o).on("keypress","#updraftvault_settings_connect input",function(t){if(13==t.which)return n(o+"#updraftvault_connect_go").trigger("click"),!1}),n(o+"#remote-storage-holder").on("click","#updraftvault_recountquota",function(t){t.preventDefault(),n(o+"#updraftvault_recountquota").html(updraftlion.counting);try{updraft_send_command("vault_recountquota",{instance_id:n("#updraftvault_settings_connect").data("instance_id")},function(t){n(o+"#updraftvault_recountquota").html(updraftlion.updatequotacount),t.hasOwnProperty("html")&&(n(o+"#updraftvault_settings_connected").html(t.html),t.hasOwnProperty("connected"))&&(t.connected?(n(o+"#updraftvault_settings_default").hide(),n(o+"#updraftvault_settings_connected")):(n(o+"#updraftvault_settings_connected").hide(),n(o+"#updraftvault_settings_default"))).show()},{error_callback:function(t,e,a,r){n(o+"#updraftvault_recountquota").html(updraftlion.updatequotacount),void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),alert(r.fatal_error_message)):(r="updraft_send_command: error: "+e+" ("+a+")",console.log(r),alert(r),console.log(t))}})}catch(t){n(o+"#updraftvault_recountquota").html(updraftlion.updatequotacount),console.log(t)}}),n(o+"#remote-storage-holder").on("click","#updraftvault_disconnect",function(t){t.preventDefault(),n(o+"#updraftvault_disconnect").html(updraftlion.disconnecting);try{updraft_send_command("vault_disconnect",{immediate_echo:!0,instance_id:n("#updraftvault_settings_connect").data("instance_id")},function(t){n(o+"#updraftvault_disconnect").html(updraftlion.disconnect),t.hasOwnProperty("html")&&(n(o+"#updraftvault_settings_connected").html(t.html).slideUp(),n(o+"#updraftvault_settings_default").slideDown())},{error_callback:function(t,e,a,r){n(o+"#updraftvault_disconnect").html(updraftlion.disconnect),void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),alert(r.fatal_error_message)):(r="updraft_send_command: error: "+e+" ("+a+")",console.log(r),alert(r),console.log(t))}})}catch(t){n(o+"#updraftvault_disconnect").html(updraftlion.disconnect),console.log(t)}}),n(o+"#remote-storage-holder").on("click","#updraftvault_connect",function(t){t.preventDefault(),n(o+"#updraftvault_settings_default").slideUp(),n(o+"#updraftvault_settings_connect").slideDown()}),n(o+"#remote-storage-holder").on("click","#updraftvault_showoptions",function(t){t.preventDefault(),n(o+"#updraftvault_settings_default").slideUp(),n(o+"#updraftvault_settings_showoptions").slideDown()}),n("#remote-storage-holder").on("keyup",".updraftplus_onedrive_folder_input",function(t){var e=n(this).val(),a=n(this).closest("td");0==e.indexOf("https:")||0==e.indexOf("http:")?a.find(".onedrive_folder_error").length||a.append('<div class="onedrive_folder_error">'+updraftlion.onedrive_folder_url_warning+"</div>"):a.find(".onedrive_folder_error").slideUp("slow",function(){a.find(".onedrive_folder_error").remove()})}),n(o+"#remote-storage-holder").on("click","#updraftvault_connect_go",function(t){return n(o+"#updraftvault_connect_go").html(updraftlion.connecting),updraft_send_command("vault_connect",{email:n("#updraftvault_email").val(),pass:n("#updraftvault_pass").val(),instance_id:n("#updraftvault_settings_connect").data("instance_id")},function(t,e,a){n(o+"#updraftvault_connect_go").html(updraftlion.connect),t.hasOwnProperty("e")?(updraft_html_modal('<h4 style="margin-top:0px; padding-top:0px;">'+updraftlion.errornocolon+"</h4><p>"+t.e+"</p>",updraftlion.disconnect,400,250),t.hasOwnProperty("code")&&"no_quota"==t.code&&(n(o+"#updraftvault_settings_connect").slideUp(),n(o+"#updraftvault_settings_default").slideDown())):t.hasOwnProperty("connected")&&t.connected&&t.hasOwnProperty("html")?(n(o+"#updraftvault_settings_connect").slideUp(),n(o+"#updraftvault_settings_connected").html(t.html).slideDown()):(console.log(t),alert(updraftlion.unexpectedresponse+" "+a))},{error_callback:function(t,e,a,r){n(o+"#updraftvault_connect_go").html(updraftlion.connect),void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),alert(r.fatal_error_message)):(r="updraft_send_command: error: "+e+" ("+a+")",console.log(r),alert(r),console.log(t))}}),!1}),n("#updraft-iframe-modal").on("change","#always_keep_this_backup",function(){var e=n(this).data("backup_key");updraft_send_command("always_keep_this_backup",{backup_key:e,always_keep:n(this).is(":checked")?1:0},function(t){t.hasOwnProperty("rawbackup")&&(jQuery("#updraft-iframe-modal").dialog("close"),jQuery(".updraft_existing_backups_row_"+e+" .updraft_existingbackup_date").data("rawbackup",t.rawbackup),updraft_html_modal(jQuery(".updraft_existing_backups_row_"+e+" .updraft_existingbackup_date").data("rawbackup"),updraftlion.raw,780,500))})})}),jQuery(function(t){try{"undefined"!=typeof updraft_plupload_config2&&((a=new plupload.Uploader(updraft_plupload_config2)).bind("Init",function(t){var e=jQuery("#plupload-upload-ui2");t.features.dragdrop?(e.addClass("drag-drop"),jQuery("#drag-drop-area2").on("dragover.wp-uploader",function(){e.addClass("drag-over")}).on("dragleave.wp-uploader, drop.wp-uploader",function(){e.removeClass("drag-over")})):(e.removeClass("drag-drop"),jQuery("#drag-drop-area2").off(".wp-uploader"))}),a.init(),a.bind("FilesAdded",function(t,e){plupload.each(e,function(t){/^backup_([\-0-9]{15})_.*_([0-9a-f]{12})-db([0-9]+)?\.(gz\.crypt)$/i.test(t.name)?jQuery("#filelist2").append('<div class="file" id="'+t.id+'"><b>'+t.name+"</b> (<span>"+plupload.formatSize(0)+"</span>/"+plupload.formatSize(t.size)+') <div class="fileprogress"></div></div>'):(alert(t.name+": "+updraftlion.notdba),a.removeFile(t))}),t.refresh(),t.start()}),a.bind("UploadProgress",function(t,e){jQuery("#"+e.id+" .fileprogress").width(e.percent+"%"),jQuery("#"+e.id+" span").html(plupload.formatSize(parseInt(e.size*e.percent/100)))}),a.bind("Error",function(t,e){err_makesure="-200"==e.code?"\n"+updraftlion.makesure2:updraftlion.makesure,alert(updraftlion.uploaderr+" (code "+e.code+") : "+e.message+" "+err_makesure)}),a.bind("FileUploaded",function(t,e,a){"200"==a.status?"ERROR:"==a.response.substring(0,6)?alert(updraftlion.uploaderror+" "+a.response.substring(6)):"OK:"==a.response.substring(0,3)?(bkey=a.response.substring(3),jQuery("#"+e.id+" .fileprogress").hide(),jQuery("#"+e.id).append(updraftlion.uploaded+' <a href="?page=updraftplus&action=downloadfile&updraftplus_file='+bkey+"&decrypt_key="+encodeURIComponent(jQuery("#updraftplus_db_decrypt").val())+'">'+updraftlion.followlink+"</a> "+updraftlion.thiskey+" "+jQuery("#updraftplus_db_decrypt").val().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"))):alert(updraftlion.unknownresp+" "+a.response):alert(updraftlion.ukrespstatus+" "+a.code)}))}catch(t){console.log(t)}var a;if(jQuery("#updraft-hidethis").remove(),Handlebars.registerHelper("ifeq",function(t,e,a){return(t="string"!=typeof t&&null!=t?t.toString():t)===(e="string"!=typeof e&&null!=e?e.toString():e)?a.fn(this):a.inverse(this)}),Handlebars.registerHelper("maskPassword",function(t){return t.replace(/./gi,"*")}),Handlebars.registerHelper("encodeURIComponent",function(t){return encodeURIComponent(t)}),Handlebars.registerHelper("ifCond",function(t,e,a,r){switch(e){case"==":return t==a?r.fn(this):r.inverse(this);case"===":return t===a?r.fn(this):r.inverse(this);case"!=":return t!=a?r.fn(this):r.inverse(this);case"!==":return t!==a?r.fn(this):r.inverse(this);case"<":return t<a?r.fn(this):r.inverse(this);case"<=":return t<=a?r.fn(this):r.inverse(this);case">":return a<t?r.fn(this):r.inverse(this);case">=":return a<=t?r.fn(this):r.inverse(this);case"&&":return t&&a?r.fn(this):r.inverse(this);case"||":return t||a?r.fn(this):r.inverse(this);case"typeof":return t===typeof a?r.fn(this):r.inverse(this);case"not_typeof":return t!==typeof a?r.fn(this):r.inverse(this);default:return r.inverse(this)}}),Handlebars.registerHelper("for",function(t,e,a,r){for(var n="",o=t;o<e;o+=a)n+=r.fn(o);return n}),Handlebars.registerHelper("set_var",function(t,e,a){a.data.root||(a.data.root={}),a.data.root[t]=e}),Handlebars.registerHelper("get_length",function(t){return void 0!==t&&!1==t instanceof Array?Object.keys(t).length:!0==t instanceof Array?t.length:0}),Handlebars.registerHelper("get_template_css_classes",function(t,e){var a=e.data.root.css_class+" "+e.data.root.method_id;return t&&e.data.root.is_multi_options_feature_supported&&(e.data.root.is_config_templates_feature_supported?a+=" "+e.data.root.method_id+"-"+e.data.root.instance_id:a+=" "+e.data.root.method_id+"-"+e.data.root._instance_id),a}),Handlebars.registerHelper("get_template_input_attribute_value",function(t,e,a){var r=a.data.root.is_config_templates_feature_supported?a.data.root.instance_id:a.data.root._instance_id,n=ename="",a=a.data.root.method_id;try{e=JSON.parse(e)}catch(t){}if(void 0!==e&&Array===e.constructor)for(var o=0;o<e.length;o++)n+="_"+e[o],ename+="["+e[o]+"]";else n="_"+e,ename="["+e+"]";return"id"===t?"updraft_"+a+n+"_"+r:"name"===t?"updraft_"+a+"[settings]["+r+"]"+ename:""}),Handlebars.registerHelper("get_template_test_button_html",function(t,e){var a=e.data.root.is_config_templates_feature_supported?Handlebars.escapeExpression(e.data.root.instance_id):Handlebars.escapeExpression(e.data.root._instance_id),r=Handlebars.escapeExpression(Handlebars.helpers.get_template_css_classes.apply(this,[!0,e])),n=Handlebars.escapeExpression(e.data.root.input_test_label),e=Handlebars.escapeExpression(e.data.root.method_id);return'\t\t<tr class="'+r+'"> \t\t\t<th></th> \t\t\t<td> \t\t\t\t<p> \t\t\t\t\t<button id="updraft-'+e+"-test-"+a+'" type="button" class="button-primary updraft-test-button updraft-'+e+"-test-"+a+'" data-instance_id="'+a+'" data-method="'+e+'" data-method_label="'+t+'">'+n+"</button> \t\t\t\t</p> \t\t\t</td> \t\t</tr>"}),t("#remote-storage-holder").length){var e,r="",n=["default","template_properties"];for(e in updraftlion.remote_storage_templates)if(void 0!==updraftlion.remote_storage_options[e]&&n.length<Object.keys(updraftlion.remote_storage_options[e]).length){var o,d=Handlebars.compile(updraftlion.remote_storage_templates[e]);for(o in updraftlion.remote_storage_partial_templates[e])Handlebars.registerPartial(o,Handlebars.compile(updraftlion.remote_storage_partial_templates[e][o]));var s,u,i,p=!0,l=1;for(s in updraftlion.remote_storage_options[e])-1<n.indexOf(s)||(u={},Object.assign(u,updraftlion.remote_storage_options[e].template_properties,updraftlion.remote_storage_options[e][s]),void 0===u.instance_conditional_logic&&(u.instance_conditional_logic={type:"",rules:[]}),u.instance_conditional_logic.day_of_the_week_options=updraftlion.conditional_logic.day_of_the_week_options,u.instance_conditional_logic.logic_options=updraftlion.conditional_logic.logic_options,u.instance_conditional_logic.operand_options=updraftlion.conditional_logic.operand_options,u.instance_conditional_logic.operator_options=updraftlion.conditional_logic.operator_options,u.first_instance=p,void 0===u.instance_enabled&&(u.instance_enabled=1),void 0!==u.instance_label&&""!=u.instance_label||(i=updraftlion.remote_storage_methods[e],u.instance_label=i+(1==l?"":" ("+l+")")),r+=d(u),p=!1,l++)}else r+=updraftlion.remote_storage_templates[e];t("#remote-storage-holder").append(r).ready(function(){t(".updraftplusmethod").not(".none").hide(),updraft_remote_storage_tabs_setup(),updraft_setup_remote_storage_config_link(),updraft_scroll_to_remote_storage_config(),t("#remote-storage-holder .updraftplus_onedrive_folder_input").trigger("keyup"),initialize_remote_storage_select2_elements(jQuery("#remote-storage-holder"))})}}),jQuery(function(o){function a(t){var a="";return"object"==(t=void 0===t?"string":t)?a=o("#updraft-navtab-settings-content form input[name!='action'][name!='option_page'][name!='_wpnonce'][name!='_wp_http_referer'], #updraft-navtab-settings-content form textarea, #updraft-navtab-settings-content form select, #updraft-navtab-settings-content form input[type=checkbox]").serializeJSON({checkboxUncheckedValue:"0",useIntKeysAsArrayIndex:!0}):(a=o("#updraft-navtab-settings-content form input[name!='action'], #updraft-navtab-settings-content form textarea, #updraft-navtab-settings-content form select").serialize(),o.each(o("#updraft-navtab-settings-content form input[type=checkbox]").filter(function(t){return 0==o(this).prop("checked")}),function(t,e){a+="&"+o(e).attr("name")+"=0"})),a}function r(t,e){try{t.messages;var a=t.backup_dir.writable,r=t.backup_dir.message,n=t.backup_dir.button_title}catch(t){return console.log(t),console.log(e),alert(updraftlion.jsonnotunderstood),o.unblockUI(),{}}if(t.hasOwnProperty("changed"))for(prop in console.log("UpdraftPlus: savesettings: some values were changed after being filtered"),console.log(t.changed),t.changed)if("object"==typeof t.changed[prop])for(innerprop in t.changed[prop])o("[name='"+innerprop+"']").is(":checkbox")||o("[name='"+prop+"["+innerprop+"]']").val(t.changed[prop][innerprop]);else o("[name='"+prop+"']").is(":checkbox")||o("[name='"+prop+"']").val(t.changed[prop]);return o("#updraft_writable_mess").html(r),0==a?(o("#updraft-backupnow-button").attr("disabled","disabled"),o("#updraft-backupnow-button").attr("title",n),o(".backupdirrow").css("display","table-row")):(o("#updraft-backupnow-button").prop("disabled",!1),o("#updraft-backupnow-button").removeAttr("title")),t.hasOwnProperty("updraft_include_more_path")&&o("#backupnow_includefiles_moreoptions").html(t.updraft_include_more_path),t.hasOwnProperty("backup_now_message")&&o("#backupnow_remote_container").html(t.backup_now_message),o(".updraftmessage").remove(),o("#updraft_backup_started").before(t.messages),updraft_setup_remote_storage_config_link(),console.log(t),o("#updraft-next-files-backup-inner").html(t.files_scheduled),o("#updraft-next-database-backup-inner").html(t.database_scheduled),t}function n(){var t,e=!1;jQuery("#updraft-authenticate-modal-innards").html(""),jQuery("div[class*=updraft_authenticate_] a.updraft_authlink").each(function(){var t=jQuery(this).data("pretext");void 0===t&&(t=""),jQuery("#updraft-authenticate-modal-innards").append(t+'<p><a class="'+jQuery(this).attr("class")+'" href="'+jQuery(this).attr("href")+'">'+jQuery(this).html()+"</a></p>"),e=!0}),e&&((t={})[updraftlion.cancel]=function(){jQuery(this).dialog("close")},jQuery("#updraft-authenticate-modal").dialog({autoOpen:!0,modal:!0,resizable:!1,draggable:!1,resizeOnWindowResize:!0,scrollWithViewport:!0,resizeAccordingToViewport:!0,useContentSize:!1,open:function(t,e){o(this).dialog("option","width",860),o(this).dialog("option","height",260)},buttons:t}).dialog("open"))}(new Image).src=updraftlion.ud_url+"/images/notices/updraft_logo.png",o("#updraft-navtab-settings-content input.updraft_include_entity").on("change",function(t){var e=o(this).attr("id"),a=o(this).is(":checked");o("#backupnow_files_"+e).prop("checked",a)}),o("#updraftplus-settings-save").on("click",function(t){t.preventDefault(),o.blockUI({css:{width:"300px",border:"none","border-radius":"10px",left:"calc(50% - 150px)",padding:"20px"},message:'<div style="margin: 8px; font-size:150%;" class="updraft_saving_popup"><img src="'+updraftlion.ud_url+'/images/notices/updraft_logo.png" height="80" width="80" style="padding-bottom:10px;"><br>'+updraftlion.saving+"</div>"}),updraft_send_command("savesettings",{settings:a("string"),updraftplus_version:updraftlion.updraftplus_version},function(t,e,a){r(t,a),o("#updraft-wrap .fade").delay(6e3).fadeOut(2e3),window.updraft_main_tour&&!window.updraft_main_tour.canceled?(window.updraft_main_tour.show("settings_saved"),n()):o("html, body").animate({scrollTop:o("#updraft-wrap").offset().top},1e3,function(){n()}),o.unblockUI()},{action:"updraft_savesettings",error_callback:function(t,e,a,r){o.unblockUI(),void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),alert(r.fatal_error_message)):(r="updraft_send_command: error: "+e+" ("+a+")",console.log(r),alert(r),console.log(t))},nonce:updraftplus_settings_nonce})}),o("#updraftplus-settings-export").on("click",function(){var t,e;updraft_settings_form_changed&&alert(updraftlion.unsaved_settings_export),t=a("object"),e=new Date,t=JSON.stringify({version:"1.12.40",epoch_date:e.getTime(),local_date:e.toLocaleString(),network_site_url:updraftlion.network_site_url,data:t}),(e=document.body.appendChild(document.createElement("a"))).setAttribute("download",updraftlion.export_settings_file_name),e.setAttribute("style","display:none;"),e.setAttribute("href","data:text/json;charset=UTF-8,"+encodeURIComponent(t)),e.click()}),o("#updraftplus-settings-import").on("click",function(){o.blockUI({css:{width:"300px",border:"none","border-radius":"10px",left:"calc(50% - 150px)",padding:"20px"},message:'<div style="margin: 8px; font-size:150%;" class="updraft_saving_popup"><img src="'+updraftlion.ud_url+'/images/notices/updraft_logo.png" height="80" width="80" style="padding-bottom:10px;"><br>'+updraftlion.importing+"</div>"});var t,e=document.getElementById("import_settings");0==e.files.length?(alert(updraftlion.import_select_file),o.unblockUI()):(e=e.files[0],(t=new FileReader).onload=function(){var t,e=this.result;try{t=ud_parse_json(e)}catch(t){return void(o.unblockUI(),jQuery("#import_settings").val(""),console.log(e),console.log(t),alert(updraftlion.import_invalid_json_file))}window.confirm(updraftlion.importing_data_from+" "+t.network_site_url+"\n"+updraftlion.exported_on+" "+t.local_date+"\n"+updraftlion.continue_import)?updraft_send_command("importsettings",{settings:JSON.stringify(t.data),updraftplus_version:updraftlion.updraftplus_version},function(t,e,a){t=r(t);!t.hasOwnProperty("saved")||t.saved?(updraft_settings_form_changed=!1,location.replace(updraftlion.updraft_settings_url)):(o.unblockUI(),t.hasOwnProperty("error_message")&&t.error_message&&alert(t.error_message))},{action:"updraft_importsettings",nonce:updraftplus_settings_nonce,error_callback:function(t,e,a,r){o.unblockUI(),void 0!==r&&r.hasOwnProperty("fatal_error")?(console.error(r.fatal_error_message),alert(r.fatal_error_message)):(r="updraft_send_command: error: "+e+" ("+a+")",console.log(r),console.log(t),alert(r))}}):o.unblockUI()},t.readAsText(e))}),o(".udp-replace-with-iframe--js").on("click",function(t){t.preventDefault();t=o(this).prop("href");o('<iframe width="356" height="200" allowfullscreen webkitallowfullscreen mozallowfullscreen>').attr("src",t).insertAfter(o(this)),o(this).remove()})}),jQuery(function(d){jQuery("#updraft-restore-modal").on("change","#updraft_restorer_charset",function(t){var e;d("#updraft_restorer_charset").length&&d("#updraft_restorer_collate").length&&d("#collate_change_on_charset_selection_data").length&&(e=d("#updraft_restorer_charset").val(),d("#updraft_restorer_collate option").show(),d("#updraft_restorer_collate option[data-charset!="+e+"]").hide(),updraft_send_command("collate_change_on_charset_selection",{collate_change_on_charset_selection_data:d("#collate_change_on_charset_selection_data").val(),updraft_restorer_charset:e,updraft_restorer_collate:d("#updraft_restorer_collate").val()},function(t){t.hasOwnProperty("is_action_required")&&1==t.is_action_required&&t.hasOwnProperty("similar_type_collate")&&d("#updraft_restorer_collate").val(t.similar_type_collate)}))}),jQuery("#updraft-restore-modal").on("click","#updraftplus_restore_tables_showmoreoptions",function(t){t.preventDefault(),jQuery(".updraftplus_restore_tables_options_container").toggle()}),jQuery("#updraft-restore-modal").on("click","#updraftplus_restore_plugins_showmoreoptions",function(t){t.preventDefault(),jQuery(".updraftplus_restore_plugins_options_container").toggle()}),jQuery("#updraft-restore-modal").on("click","#updraftplus_restore_themes_showmoreoptions",function(t){t.preventDefault(),jQuery(".updraftplus_restore_themes_options_container").toggle()}),jQuery("#updraft-restore-modal").on("click",".updraft-select-all-tables",function(t){t.preventDefault(),jQuery(".updraft_restore_tables_options").prop("checked",!0)}),jQuery("#updraft-restore-modal").on("click",".updraft-deselect-all-tables",function(t){t.preventDefault(),jQuery(".updraft_restore_tables_options").prop("checked",!1)});var a=null;function s(t){d(t).find(".updraftplus_spinner.spinner").addClass("visible")}function u(t){d(t).find(".updraftplus_spinner.spinner").removeClass("visible")}function i(a,r){s(r),updraft_send_command("process_updraftcentral_registration",a,function(t){u(r);try{var e;(a=ud_parse_json(t)).hasOwnProperty("error")?(e=a.message,-1!==d.inArray(a.code,["existing_user_email","email_exists"])&&(e=a.message+" "+updraftlion.perhaps_login),d(r).find(".updraftcentral_cloud_notices").html(e).addClass("updraftcentral_cloud_error"),d(r).find(".updraftcentral_cloud_notices a").attr("target","_blank"),console.log(a)):"registered"===a.status&&(d(r).find(".updraftcentral_cloud_form_container").hide(),d(r).find(".updraftcentral-subheading").hide(),d(r).find(".updraftcentral_cloud_notices").removeClass("updraftcentral_cloud_error"),p(r,a,updraftlion.registration_successful))}catch(t){console.log(t)}},{json_parse:!1})}function p(e,t,a){var r=d(e).find("form#updraftcentral_cloud_redirect_form");r.attr("action",t.redirect_url),r.attr("target","_blank"),void 0!==t.redirect_token&&r.append('<input type="hidden" name="redirect_token" value="'+t.redirect_token+'">'),t.hasOwnProperty("keys_table")&&t.keys_table&&d("#updraftcentral_keys_content").html(t.keys_table),d(".updraftplus-addons-connect-to-udc").remove(),$redirect_lnk='<a href="'+updraftlion.current_clean_url+'" class="updraftcentral_cloud_redirect_link">'+updraftlion.updraftcentral_cloud+"</a>",$close_lnk='<a href="'+updraftlion.current_clean_url+'" class="updraftcentral_cloud_close_link">'+updraftlion.close_wizard+"</a>",d(e).find(".updraftcentral_cloud_notices").html(a.replace("%s",$redirect_lnk)+" "+$close_lnk+"<br/><br/>"+updraftlion.control_udc_connections),d(e).find(".updraftcentral_cloud_notices .updraftcentral_cloud_redirect_link").off("click").on("click",function(t){t.preventDefault(),r.trigger("submit"),d(e).find(".updraftcentral_cloud_notices .updraftcentral_cloud_close_link").trigger("click")}),d(e).find(".updraftcentral_cloud_notices .updraftcentral_cloud_close_link").off("click").on("click",function(t){t.preventDefault(),d(e).dialog("close"),d("#updraftcentral_cloud_connect_container").hide()})}function e(n,o){!function(t,e,a,r){var n,o;"function"==typeof a&&((n=d(r).find("#updraftcentral_cloud_form").find('.form_hidden_fields input[name="key"]')).length&&""!==n.val()?a.apply(this,[n.val()]):(o={where_send:"__updraftpluscom",key_description:"",key_size:t,mothership_firewalled:e},s(r),updraft_send_command("updraftcentral_create_key",o,function(t){u(r);try{(o=ud_parse_json(t)).hasOwnProperty("error")?console.log(o):o.hasOwnProperty("bundle")?a.apply(this,[o.bundle]):o.hasOwnProperty("r")?(d(r).find(".updraftcentral_cloud_notices").html(updraftlion.trouble_connecting).addClass("updraftcentral_cloud_info"),alert(o.r)):console.log(o)}catch(t){console.log(t)}},{json_parse:!1})))}(d(n).find("#updraft_central_keysize").val(),d(n).find("#updraft_central_firewalled").is(":checked")?1:0,function(t){var e=d(n).find("#updraftcentral_cloud_form");0===e.find('.form_hidden_fields input[name="key"]').length&&e.find(".form_hidden_fields").append('<input type="hidden" name="key" value="'+t+'">');var a,r,t={form_data:e.find("input").serialize()};void 0!==o&&o?i(t,n):(a=t,s(r=n),updraft_send_command("process_updraftcentral_login",a,function(t){u(r);try{if((data=ud_parse_json(t)).hasOwnProperty("error")){if("incorrect_password"===data.code&&(d(r).find(".updraftcentral_cloud_form_container .tfa_fields").hide(),d(r).find(".updraftcentral_cloud_form_container .non_tfa_fields").show(),d(r).find("input#two_factor_code").val(""),d(r).find("input#password").val("").trigger("focus")),"email_not_registered"!==data.code)return d(r).find(".updraftcentral_cloud_notices").html(data.message).addClass("updraftcentral_cloud_error"),d(r).find(".updraftcentral_cloud_notices a").attr("target","_blank"),void console.log(data);i(a,r)}data.hasOwnProperty("tfa_enabled")&&1==data.tfa_enabled&&(d(r).find(".updraftcentral_cloud_notices").html("").removeClass("updraftcentral_cloud_error"),d(r).find(".updraftcentral_cloud_form_container .non_tfa_fields").hide(),d(r).find(".updraftcentral_cloud_form_container .tfa_fields").show(),d(r).find("input#two_factor_code").trigger("focus")),"authenticated"===data.status&&(d(r).find(".updraftcentral_cloud_form_container").hide(),d(r).find(".updraftcentral_cloud_notices").removeClass("updraftcentral_cloud_error"),p(r,data,updraftlion.login_successful))}catch(t){console.log(t)}},{json_parse:!1}))},n)}jQuery("#updraft-restore-modal").on("click",".updraft_restore_tables_options",function(t){var e;a=(a&&t.shiftKey&&(t=jQuery(".updraft_restore_tables_options").index(this),e=jQuery(".updraft_restore_tables_options").index(a),jQuery(".updraft_restore_tables_options").slice(Math.min(t,e),Math.max(t,e)+1).prop("checked",a.checked)),this)}),d("#updraft-wrap #btn_cloud_connect").on("click",function(){var t,e;(e=d("#updraftcentral_cloud_login_form")).length&&(updraft_html_modal(e.html(),updraftlion.updraftcentral_cloud,520,400),void 0!==(t=(e=modal.find(".updraftcentral-data-consent")).find("input").attr("name")))&&t&&(e.find("input").attr("id",t),e.find("label").attr("for",t))}),d("#updraft-wrap a#self_hosted_connect").on("click",function(t){t.preventDefault(),d("h2.nav-tab-wrapper > a#updraft-navtab-expert").trigger("click"),d("div.advanced_settings_menu > #updraft_central").trigger("click")}),d("#updraft-iframe-modal").on("click","#updraftcentral_cloud_login",function(t){t.preventDefault();t=d(this).closest("#updraft-iframe-modal");(t=>{var e=d(t).find("#updraftcentral_cloud_form"),a=e.find("input#email").val(),r=e.find("input#password").val();if(d(t).find(".updraftcentral_cloud_notices").html("").removeClass("updraftcentral_cloud_error updraftcentral_cloud_info"),e.find('.updraftcentral-data-consent > input[name="i_consent"]').is(":checked"))if(0===a.length||0===r.length)d(t).find(".updraftcentral_cloud_notices").html(updraftlion.username_password_required).addClass("updraftcentral_cloud_error");else{if(null!==a.match(/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,})+$/))return 1;d(t).find(".updraftcentral_cloud_notices").html(updraftlion.valid_email_required).addClass("updraftcentral_cloud_error")}else d(t).find(".updraftcentral_cloud_notices").html(updraftlion.data_consent_required).addClass("updraftcentral_cloud_error")})(t)&&e(t)});var r={};d(document).on("heartbeat-send",function(t,e){r=updraft_poll_get_parameters(),e.updraftplus=r}),d(document).on("heartbeat-tick",function(t,e){var a;null!==e&&e.hasOwnProperty("updraftplus")&&null!=e.updraftplus&&(updraft_process_status_check(a=e.updraftplus,JSON.stringify(a),r),e.updraftplus.hasOwnProperty("time_now"))&&jQuery("body.settings_page_updraftplus #updraft-navtab-backups-content .updraft_time_now_wrapper .updraft_time_now").empty().html(e.updraftplus.time_now)})});