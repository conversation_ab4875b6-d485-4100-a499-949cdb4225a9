# Translation of UpdraftPlus in Thai
# This file is distributed under the same license as the UpdraftPlus package.
msgid ""
msgstr ""
"PO-Revision-Date: +0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: th\n"
"Project-Id-Version: UpdraftPlus\n"

#: src/addons/googlecloud.php:518
msgid "%s suthorization failed"
msgstr ""

#: src/includes/Dropbox2/OAuth/Consumer/ConsumerAbstract.php:129
msgid "Turning off any debugging settings may also help."
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:14
msgid "It's free to use or try up to 5 sites."
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:14
msgid "If you have a few sites, it'll save hours."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:40
msgid "See our documentation on how to carry out a normal migration here"
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:40
msgid "Temporary clones of WordPress subdomain multisite installations are not yet supported."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:33
msgid "UpdraftClone does the work."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:33
msgid "Press the buttons..."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:27
msgid "Rather than test things on your live site, you can UpdraftClone it, and then throw away your clone when done."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:27
msgid "A temporary clone is an instant copy of this website, running on our servers."
msgstr ""

#: src/templates/wp-admin/settings/take-backup.php:6
msgid "You either need to activate it within your browser, or to use a JavaScript-capable browser."
msgstr ""

#: src/templates/wp-admin/settings/take-backup.php:6
msgid "This admin interface uses JavaScript heavily."
msgstr ""

#: src/templates/wp-admin/settings/tab-backups.php:80
msgid "Drop your backup files"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:346,
#: src/templates/wp-admin/settings/tab-addons.php:347
msgid "Keeps your WordPress site up to date and bug free."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:334,
#: src/templates/wp-admin/settings/tab-addons.php:335
msgid "A comprehensive and easy to use security plugin and site scanning service."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:330,
#: src/templates/wp-admin/settings/tab-addons.php:330,
#: src/templates/wp-admin/settings/tab-addons.php:336,
#: src/templates/wp-admin/settings/tab-addons.php:336,
#: src/templates/wp-admin/settings/tab-addons.php:342,
#: src/templates/wp-admin/settings/tab-addons.php:342,
#: src/templates/wp-admin/settings/tab-addons.php:348,
#: src/templates/wp-admin/settings/tab-addons.php:348
msgid "Demo in WP Playground"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:328,
#: src/templates/wp-admin/settings/tab-addons.php:329
msgid "It cleans the database, compresses images and caches pages for ultimate speed."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:328,
#: src/templates/wp-admin/settings/tab-addons.php:329
msgid "Makes your site fast and efficient."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:89
msgid "UpdraftPlus free includes Dropbox, Google Drive, Amazon S3, Rackspace and more."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:89
msgid "To avoid server-wide risks, always backup to remote cloud storage."
msgstr ""

#: src/templates/wp-admin/settings/migrator-no-migrator.php:13
msgid "After using it once, you'll have saved the purchase price compared to the time needed to copy a site by hand."
msgstr ""

#: src/templates/wp-admin/settings/migrator-no-migrator.php:13
msgid "Then, try out our \"Migrator\" add-on which can perform a direct site-to-site migration."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:362
msgid "Note that some cloud storage providers do not allow this (e.g. Dropbox), so with those providers this setting will have no effect."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:362
msgid "Choosing this option lowers your security by stopping UpdraftPlus from using SSL for authentication and encrypted transport at all, where possible."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:357
msgid "It means that UpdraftPlus will be using SSL only for encryption of traffic, and not for authentication."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:357
msgid "Choosing this option lowers your security by stopping UpdraftPlus from verifying the identity of encrypted sites that it connects to (e.g. Dropbox, Google Drive)."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:352
msgid "However, if you get an SSL error, then choosing this option (which causes UpdraftPlus to use your web server's collection instead) may help."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:352
msgid "We keep these up to date."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:352
msgid "By default UpdraftPlus uses its own store of SSL certificates to verify the identity of remote sites (i.e. to make sure it is talking to the real Dropbox, Amazon S3, etc., and not an attacker)."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:345
msgid "It is relative to your content directory (which by default is called wp-content)."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:345
msgid "This directory must be writable by your web server."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:345
msgid "This is where UpdraftPlus will write the zip files it creates initially."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:326
msgid "Be careful to leave some margin if your web-server has a hard size limit (e.g. the 2 GB / 2048 MB limit on some 32-bit servers/file systems)."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:326
msgid "The default value is %s megabytes."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:326
msgid "UpdraftPlus will split up backup archives when they exceed this file size."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:135
msgid "This is not recommended (unless you plan to manually copy them to your computer), as losing the web-server would mean losing both your website and the backups in one event."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:135
msgid "If you choose no remote storage, then the backups remain on the web-server."
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:27,
#: src/templates/wp-admin/settings/tab-backups.php:27
msgid "This can corrupt backups that you download from here."
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:27,
#: src/templates/wp-admin/settings/tab-backups.php:27
msgid "Your WordPress installation has a problem with outputting extra whitespace."
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:60
msgid "You are recommended to turn safe_mode off, or to restore only one entity at a time"
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:60
msgid "This makes time-outs much more likely."
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:35
msgid "Designed to optimize your store, enhance user experience  and increase revenue!"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:35
msgid "Quality add-ons for WooCommerce."
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:35
msgid "WP Overnight"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:31
msgid "You don’t need to be an SEO expert to use this plugin."
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:31
msgid "Save time and boost SEO!"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:31
msgid "Automate the building of internal links on your WordPress website."
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:31
msgid "Internal Link Juicer"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:27
msgid " Comprehensive, cost-effective, 5* rated and easy to use."
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:27
msgid "Secure your WordPress website with AIOS."
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:27
msgid "Still on the fence?"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:27
msgid "All-In-One Security (AIOS)"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:24
msgid "Cache your site, clean the database and compress images."
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:24
msgid "Speed up and optimize your WordPress website."
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:18
msgid " Upgrade for automatic backups before updates, incremental backups, more remote storage locations, premium support and"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:10
msgid "Protect your WordPress investment with premium features, or check out our other 5* rated  plugins below:"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:12
msgid "All 5* rated and actively installed on millions of WordPress websites:"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:12
msgid "If you like UpdraftPlus, you'll love our other plugins."
msgstr ""

#: src/templates/wp-admin/advanced/wipe-settings.php:6
msgid "You can also do this before deactivating/deinstalling UpdraftPlus if you wish."
msgstr ""

#: src/templates/wp-admin/advanced/wipe-settings.php:6
msgid "You will then need to enter all your settings again."
msgstr ""

#: src/templates/wp-admin/advanced/tools-menu.php:28
msgid "Database size"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:66
msgid "Database maximum packet size:"
msgstr ""

#: src/templates/wp-admin/advanced/export-settings.php:12
msgid "This tool will replace all your saved settings."
msgstr ""

#: src/templates/wp-admin/advanced/export-settings.php:12
msgid "You can also import previously-exported settings."
msgstr ""

#: src/templates/wp-admin/advanced/export-settings.php:7
msgid "This tool will export what is currently in the settings tab."
msgstr ""

#: src/templates/wp-admin/advanced/export-settings.php:7
msgid "Here, you can export your UpdraftPlus settings (%s), either for using on another site, or to keep as a backup."
msgstr ""

#: src/templates/wp-admin/advanced/db-size.php:35
msgid "Type"
msgstr ""

#: src/templates/wp-admin/advanced/db-size.php:34
msgid "Index size"
msgstr ""

#: src/templates/wp-admin/advanced/db-size.php:33
msgid "Data size"
msgstr ""

#: src/templates/wp-admin/advanced/db-size.php:32
msgid "Records"
msgstr ""

#: src/templates/wp-admin/advanced/db-size.php:31
msgid "Table name"
msgstr ""

#: src/templates/wp-admin/advanced/db-size.php:25
msgid "Refresh"
msgstr ""

#: src/templates/wp-admin/advanced/db-size.php:24
msgid "Clear"
msgstr ""

#: src/templates/wp-admin/advanced/db-size.php:19
msgid "Reducing your database size with WP-Optimize helps to maintain a fast, efficient, and user-friendly website."
msgstr ""

#: src/templates/wp-admin/advanced/db-size.php:10
msgid "Total Size"
msgstr ""

#: src/templates/wp-admin/advanced/db-size.php:6
msgid "Search for table"
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:371
msgid "So far %s data archives totalling %s have been received"
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:371
msgid "The sending of the site data has begun."
msgstr ""

#: src/includes/updraftclone/temporary-clone-dash-notice.php:47
msgid "You can shut this clone down at the following link:"
msgstr ""

#: src/includes/updraftclone/temporary-clone-dash-notice.php:47
msgid "Each time your clone renews (weekly) it costs %s."
msgstr ""

#: src/central/modules/updates.php:715
msgid "If installing, then proceed with caution by first doing a backup."
msgstr ""

#: src/central/modules/updates.php:715
msgid "This % does not provide information to allow determining whether the latest version is compatible with your WordPress or PHP installation."
msgstr ""

#: src/central/modules/updates.php:706
msgid "The latest update for this %s has not been tested with the WordPress version installed on the remote site and may have compatibility issues when used."
msgstr ""

#: src/central/modules/updates.php:696
msgid "The minimum PHP version supported by this %s is %s."
msgstr ""

#: src/central/modules/updates.php:696
msgid "The latest update for this %s is not compatible with the PHP version installed on the remote site."
msgstr ""

#: src/central/modules/updates.php:686
msgid "The minimum WordPress version supported by this %s is %s."
msgstr ""

#: src/central/modules/updates.php:686
msgid "The latest update for this %s is not compatible with the WordPress version installed on the remote site."
msgstr ""

#: src/udaddons/updraftplus-addons.php:1056
msgid "No response data was received."
msgstr ""

#: src/udaddons/updraftplus-addons.php:332
msgid "You will no longer receive updates to UpdraftPlus."
msgstr ""

#: src/udaddons/updraftplus-addons.php:332
msgid "Your paid access to UpdraftPlus updates for this site has expired."
msgstr ""

#: src/udaddons/options.php:536
msgid "Follow this link to activate this licence"
msgstr ""

#: src/udaddons/options.php:536
msgid "%s available to claim on this site."
msgstr ""

#: src/udaddons/options.php:339
msgid "You can now use your purchase!"
msgstr ""

#: src/udaddons/options.php:339
msgid "The claim and installation was successful."
msgstr ""

#: src/udaddons/options.php:338
msgid "Response was:"
msgstr ""

#: src/udaddons/options.php:338
msgid "An unknown response was received."
msgstr ""

#: src/udaddons/options.php:259
msgid "This ensures %s can connect and update."
msgstr ""

#: src/udaddons/options.php:259
msgid "Please list %s in the %s constant."
msgstr ""

#: src/udaddons/options.php:254
msgid "Please make sure that %s is not set to \"true\" in your wp-config file - this ensures UpdraftPlus can connect and update."
msgstr ""

#: src/udaddons/options.php:250
msgid "This is incompatible with WordPress's updates mechanism; you will not be able to receive updates."
msgstr ""

#: src/udaddons/options.php:250
msgid "You have installed this plugin in your plugins folder (%s) with a non-default name %s which is different to %s."
msgstr ""

#: src/udaddons/options.php:143
msgid "To fix this problem, contact your web hosting company."
msgstr ""

#: src/udaddons/options.php:143
msgid "You can try it, but don't be surprised if it does not work."
msgstr ""

#: src/udaddons/options.php:143
msgid "Your web server's version of PHP is too old (%s) - UpdraftPlus expects at least %s."
msgstr ""

#: src/methods/updraftvault.php:943
msgid "An unknown error occurred while connecting to Vault."
msgstr ""

#: src/methods/updraftvault.php:477
msgid "Your web server's PHP installation does not include a <strong>required</strong> (for %s) module (%s)."
msgstr ""

#: src/methods/updraftvault.php:331
msgid "If you do not wish this to happen, then you should renew as soon as possible."
msgstr ""

#: src/methods/updraftvault.php:331
msgid "In a few days' time, your stored data will be permanently removed."
msgstr ""

#: src/methods/updraftvault.php:331
msgid "You have an UpdraftPlus Vault subscription that has not been renewed, and the grace period has expired."
msgstr ""

#: src/methods/updraftvault.php:328
msgid "Please renew as soon as possible!"
msgstr ""

#: src/methods/updraftvault.php:328
msgid "You are within the few days of grace period before it will be suspended, and you will lose your quota and access to data stored within it."
msgstr ""

#: src/methods/updraftvault.php:328
msgid "You have an UpdraftPlus Vault subscription with overdue payment."
msgstr ""

#: src/methods/updraftvault.php:325
msgid "You should renew immediately to avoid losing the 12 months of free storage allowance that you get for being a current UpdraftPlus Premium customer."
msgstr ""

#: src/methods/updraftvault.php:325
msgid "Your UpdraftPlus Premium purchase is over a year ago."
msgstr ""

#: src/methods/updraftvault.php:257
msgid "Please try again after a few minutes."
msgstr ""

#: src/methods/updraftvault.php:257, src/methods/updraftvault.php:941
msgid "An error occurred while fetching your Vault credentials."
msgstr ""

#: src/methods/s3generic.php:212
msgid "SigV2"
msgstr ""

#: src/methods/s3generic.php:211
msgid "SigV4"
msgstr ""

#: src/methods/s3generic.php:209
msgid "Read more about signature version"
msgstr ""

#: src/methods/s3generic.php:208
msgid "Signature version"
msgstr ""

#: src/methods/s3.php:826, src/methods/s3.php:871
msgid "Error: Failed to download %s."
msgstr ""

#: src/methods/s3.php:542, src/methods/s3.php:736, src/methods/s3.php:846,
#: src/methods/s3.php:826, src/methods/s3.php:871
msgid "Check your permissions and credentials."
msgstr ""

#: src/methods/s3.php:542, src/methods/s3.php:736, src/methods/s3.php:846
msgid "Error: Failed to access bucket %s."
msgstr ""

#: src/methods/openstack2.php:247
msgid "Authentication URI"
msgstr ""

#: src/methods/openstack2.php:244
msgid "Get your access credentials from your OpenStack Swift provider, and then pick a container name to use for storage."
msgstr ""

#: src/methods/googledrive.php:1515
msgid "%s does not allow authorisation of sites hosted on direct IP addresses."
msgstr ""

#: src/methods/googledrive.php:1512
msgid "Account holder's name"
msgstr ""

#: src/methods/googledrive.php:859
msgid "%s for %s"
msgstr ""

#: src/methods/googledrive.php:423
msgid "Please create a new Google Drive project and reconnect with UpdraftPlus."
msgstr ""

#: src/methods/googledrive.php:423
msgid "The client has been deleted from the Google Drive API console."
msgstr ""

#: src/methods/email.php:30
msgid "If so, you should switch to using a different remote storage method."
msgstr ""

#: src/methods/email.php:30
msgid "This backup archive is %s MB in size - the attempt to send this via email is likely to fail (few email servers allow attachments of this size)."
msgstr ""

#: src/methods/dropbox.php:612
msgid "Your Dropbox App Secret"
msgstr ""

#: src/methods/dropbox.php:611
msgid "Your Dropbox App Key"
msgstr ""

#: src/methods/dropbox.php:528
msgid "You are not authenticated with %s"
msgstr ""

#: src/methods/dropbox.php:482
msgid "You are not authenticated with %s (whilst deleting)"
msgstr ""

#: src/methods/dropbox.php:194, src/methods/dropbox.php:211
msgid "You are not authenticated with Dropbox"
msgstr ""

#: src/methods/dreamobjects.php:192, src/methods/s3.php:1049,
#: src/methods/s3.php:1089, src/methods/s3generic.php:200
msgid "Examples: mybucket, mybucket/mypath"
msgstr ""

#: src/methods/dreamobjects.php:192, src/methods/s3.php:1049,
#: src/methods/s3.php:1089, src/methods/s3generic.php:200
msgid "Enter only a bucket name or a bucket and path."
msgstr ""

#: src/methods/dreamobjects.php:183, src/methods/s3.php:936,
#: src/methods/s3.php:1079
msgid "This bucket will be created for you if it does not already exist."
msgstr ""

#: src/methods/dreamobjects.php:183, src/methods/s3.php:936,
#: src/methods/s3.php:1079
msgid "Get your access key and secret key from your <a href=\"%s\">%s console</a>, then pick a (globally unique - all %s users) bucket name (letters and numbers) (and optionally a path) to use for storage."
msgstr ""

#: src/methods/cloudfiles.php:455, src/methods/dreamobjects.php:181,
#: src/methods/dreamobjects.php:182, src/methods/openstack-base.php:577,
#: src/methods/s3.php:926, src/methods/s3.php:930
msgid "Your web server's PHP installation does not included a required module (%s)."
msgstr ""

#: src/methods/cloudfiles-new.php:284, src/methods/cloudfiles.php:461,
#: src/methods/openstack2.php:244
msgid "This container will be created for you if it does not already exist."
msgstr ""

#: src/methods/cloudfiles-new.php:284
msgid "Get your API key <a href=\"%s\" target=\"_blank\">from your Rackspace Cloud console</a> (<a href=\"%s\" target=\"_blank\">read instructions here</a>), then pick a container name to use for storage."
msgstr ""

#: src/methods/cloudfiles-new.php:281, src/methods/cloudfiles-new.php:282,
#: src/methods/cloudfiles.php:455, src/methods/dreamobjects.php:181,
#: src/methods/openstack-base.php:577, src/methods/openstack2.php:242,
#: src/methods/s3.php:930, src/methods/s3.php:1081,
#: src/methods/s3generic.php:193
msgid "UpdraftPlus's %s module <strong>requires</strong> %s."
msgstr ""

#: src/methods/cloudfiles-new.php:281, src/methods/cloudfiles-new.php:282,
#: src/methods/openstack2.php:242, src/methods/s3.php:1080,
#: src/methods/s3.php:1081, src/methods/s3generic.php:192,
#: src/methods/s3generic.php:193
msgid "Your web server's PHP installation does not include a required module (%s)."
msgstr ""

#: src/methods/backup-module.php:89
msgid "Select existing folder"
msgstr ""

#: src/methods/addon-not-yet-present.php:126
msgid "Your PHP version: %s."
msgstr ""

#: src/includes/updraftplus-tour.php:196
msgid "You are now all set to use UpdraftPlus!"
msgstr ""

#: src/includes/updraftplus-tour.php:130, src/includes/updraftplus-tour.php:182
msgid "If not, your backups remain on the same server as your site."
msgstr ""

#: src/includes/updraftplus-tour.php:130, src/includes/updraftplus-tour.php:182
msgid "Now select a remote storage destination to protect against server-wide threats."
msgstr ""

#: src/includes/updraftplus-notices.php:275
msgid "30 September"
msgstr ""

#: src/includes/updraftplus-notices.php:275
msgid "Be quick, offer ends %s."
msgstr ""

#: src/includes/updraftplus-notices.php:275
msgid "Visit any of our websites and <b>use code %s</b> at checkout to get <b>20%% off all our plugins</b>."
msgstr ""

#: src/includes/updraftplus-notices.php:262
msgid "31 July"
msgstr ""

#: src/includes/updraftplus-notices.php:261
msgid "Summer Sale"
msgstr ""

#: src/includes/updraftplus-notices.php:249
msgid "31 May"
msgstr ""

#: src/includes/updraftplus-notices.php:248
msgid "Spring Sale"
msgstr ""

#: src/includes/updraftplus-notices.php:236
msgid "28 January"
msgstr ""

#: src/includes/updraftplus-notices.php:235
msgid "New Year Sale"
msgstr ""

#: src/includes/updraftplus-notices.php:223
msgid "3 December"
msgstr ""

#: src/includes/updraftplus-notices.php:223,
#: src/includes/updraftplus-notices.php:236,
#: src/includes/updraftplus-notices.php:249,
#: src/includes/updraftplus-notices.php:262
msgid "Offer ends %s"
msgstr ""

#: src/includes/updraftplus-notices.php:223,
#: src/includes/updraftplus-notices.php:236,
#: src/includes/updraftplus-notices.php:249,
#: src/includes/updraftplus-notices.php:262
msgid "Save 20%% with code %s."
msgstr ""

#: src/includes/updraftplus-notices.php:222
msgid "Black Friday Sale"
msgstr ""

#: src/includes/updraftplus-notices.php:122
msgid "Or if you have any issues or questions please leave us a support message"
msgstr ""

#: src/includes/updraftplus-notices.php:122
msgid "If you like us, please consider leaving a positive review to spread the word."
msgstr ""

#: src/includes/updraftplus-notices.php:122
msgid "Hey - We noticed UpdraftPlus has kept your site safe for a while."
msgstr ""

#: src/includes/updraftplus-notices.php:93
msgid "Includes find-and-replace tool for database references."
msgstr ""

#: src/includes/updraftplus-notices.php:93
msgid "Copy your site to another domain directly."
msgstr ""

#: src/includes/updraftplus-notices.php:60
msgid "Plus many more options."
msgstr ""

#: src/includes/updraftplus-notices.php:60
msgid "Enhanced storage options for Dropbox, Google Drive and S3."
msgstr ""

#: src/includes/updraftplus-notices.php:29
msgid "Backup incremental changes instead of full backups (saving server resources), clone or migrate your site with ease, get more remote storage locations, premium support and more."
msgstr ""

#: src/includes/updraftplus-notices.php:28
msgid "Backup, migrate and restore with Premium."
msgstr ""

#: src/includes/updraftcentral.php:21, src/includes/updraftplus-clone.php:21
msgid "Please try again later."
msgstr ""

#: src/includes/updraftcentral.php:21, src/includes/updraftplus-clone.php:21
msgid "The server might be busy or you have lost your connection to the internet at the time of the request."
msgstr ""

#: src/includes/updraftcentral.php:21, src/includes/updraftplus-clone.php:21
msgid "An error has occurred while processing your request."
msgstr ""

#: src/includes/S3.php:1927
msgid "Please ask your webserver support how to upgrade your PHP and cURL library versions to use non-obsolete TLS versions."
msgstr ""

#: src/includes/S3.php:1927
msgid "Your PHP installation failed a TLS v1.2 connection test, which is the minimum version required by Amazon."
msgstr ""

#: src/includes/S3.php:1927
msgid "Connecting to Amazon S3 failed."
msgstr ""

#: src/includes/migrator-lite.php:334
msgid "If that is not yet set up, then you should set it up, or use below search and replace so that the non-https links are automatically replaced."
msgstr ""

#: src/includes/migrator-lite.php:334
msgid "As long as your web hosting allows http (i.e. non-SSL access) or will forward requests to https (which is almost always the case), this is no problem."
msgstr ""

#: src/includes/migrator-lite.php:323
msgid "Otherwise, you will want to use below search and replace to search/replace the site address so that the site can be visited without https."
msgstr ""

#: src/includes/migrator-lite.php:323
msgid "This restoration will work if you still have an SSL certificate (i.e. can use https) to access the site."
msgstr ""

#: src/includes/class-wpadmin-commands.php:586
msgid "This is normally caused by file permissions."
msgstr ""

#: src/includes/class-wpadmin-commands.php:261
msgid "Now press Restore again to proceed."
msgstr ""

#: src/includes/class-wpadmin-commands.php:263
msgid "If all is well, then now press Restore again to proceed."
msgstr ""

#: src/includes/class-wpadmin-commands.php:236
msgid "Now press Restore to proceed."
msgstr ""

#: src/includes/class-wpadmin-commands.php:236,
#: src/includes/class-wpadmin-commands.php:261
msgid "The backup archive files have been successfully processed."
msgstr ""

#: src/includes/class-wpadmin-commands.php:238,
#: src/includes/class-wpadmin-commands.php:263
msgid "Otherwise, cancel and correct any problems first."
msgstr ""

#: src/includes/class-wpadmin-commands.php:238
msgid "If all is well, then press Restore to proceed."
msgstr ""

#: src/includes/class-wpadmin-commands.php:238,
#: src/includes/class-wpadmin-commands.php:263
msgid "The backup archive files have been processed, but with some warnings."
msgstr ""

#: src/includes/class-wpadmin-commands.php:240,
#: src/includes/class-wpadmin-commands.php:265
msgid "You will need to cancel and correct any problems before retrying."
msgstr ""

#: src/includes/class-wpadmin-commands.php:240,
#: src/includes/class-wpadmin-commands.php:265
msgid "The backup archive files have been processed, but with some errors."
msgstr ""

#: src/includes/class-wpadmin-commands.php:130
msgid "If you have a lot of data to import, and if the restore operation times out, then you will need to ask your web hosting company for ways to raise this limit (or attempt the restoration piece-by-piece)."
msgstr ""

#: src/includes/class-wpadmin-commands.php:130
msgid "The PHP setup on this webserver allows only %s seconds for PHP to run, and does not allow this limit to be raised."
msgstr ""

#: src/includes/class-updraft-dashboard-news-offer.php:103
msgid "No, please don't."
msgstr ""

#: src/includes/class-updraft-dashboard-news-offer.php:103
msgid "Yes, show me the news."
msgstr ""

#: src/includes/class-updraft-dashboard-news-offer.php:102
msgid "Do you want to see official news from this plugin in this Events and News section?"
msgstr ""

#: src/includes/class-storage-methods-interface.php:392
msgid "To perform any restoration using UpdraftPlus, you will need to obtain a copy of this file and place it inside UpdraftPlus's working folder"
msgstr ""

#: src/includes/class-storage-methods-interface.php:392
msgid "The remote storage method in use (%s) does not allow us to retrieve files."
msgstr ""

#: src/includes/class-storage-methods-interface.php:392
msgid "The backup archive for this file could not be found."
msgstr ""

#: src/includes/class-remote-send.php:671
msgid "This action cannot be undone."
msgstr ""

#: src/includes/class-remote-send.php:671
msgid "You are about to permanently delete the list of existing sites."
msgstr ""

#: src/includes/class-remote-send.php:565
msgid "It is deprecated, causes encryption to malfunction, and should be turned off."
msgstr ""

#: src/includes/class-remote-send.php:565
msgid "The setting %s is turned on in your PHP settings."
msgstr ""

#: src/includes/class-remote-send.php:425
msgid "If you are sending from an external network, it is likely that a firewall will be blocking this."
msgstr ""

#: src/includes/class-remote-send.php:425
msgid "The site URL you are sending to (%s) looks like a local development website."
msgstr ""

#. translators: Deleted long text.
#: src/includes/class-manipulation-functions.php:509
msgid "This content was deleted in order to anonymize it."
msgstr ""

#. translators: Deleted text.
#: src/includes/class-manipulation-functions.php:505
msgid "[deleted]"
msgstr ""

#: src/includes/class-http-error-descriptions.php:79
msgid "Intended for use by intercepting proxies used to control access to the network (e.g., \"captive portals\" used to require agreement to Terms of Service before granting full Internet access via a Wi-Fi hotspot)."
msgstr ""

#: src/includes/class-http-error-descriptions.php:79
msgid "The client needs to authenticate to gain network access"
msgstr ""

#: src/includes/class-http-error-descriptions.php:79
msgid "Network Authentication Required"
msgstr ""

#: src/includes/class-http-error-descriptions.php:78
msgid "Further extensions to the request are required for the server to fulfil it."
msgstr ""

#: src/includes/class-http-error-descriptions.php:78
msgid "Not Extended"
msgstr ""

#: src/includes/class-http-error-descriptions.php:77
msgid "The server detected an infinite loop while processing the request."
msgstr ""

#: src/includes/class-http-error-descriptions.php:77
msgid "Loop Detected"
msgstr ""

#: src/includes/class-http-error-descriptions.php:76
msgid "The server is unable to store the representation needed to complete the request."
msgstr ""

#: src/includes/class-http-error-descriptions.php:76
msgid "Insufficient Storage"
msgstr ""

#: src/includes/class-http-error-descriptions.php:75
msgid "Transparent content negotiation for the request results in a circular reference."
msgstr ""

#: src/includes/class-http-error-descriptions.php:75
msgid "Variant Also Negotiates"
msgstr ""

#: src/includes/class-http-error-descriptions.php:74
msgid "The server does not support the HTTP protocol version used in the request."
msgstr ""

#: src/includes/class-http-error-descriptions.php:74
msgid "HTTP Version Not Supported"
msgstr ""

#: src/includes/class-http-error-descriptions.php:73
msgid "The server was acting as a gateway or proxy and did not receive a timely response from the upstream server."
msgstr ""

#: src/includes/class-http-error-descriptions.php:73
msgid "Gateway Timeout"
msgstr ""

#: src/includes/class-http-error-descriptions.php:72
msgid "Generally, this is a temporary state."
msgstr ""

#: src/includes/class-http-error-descriptions.php:72
msgid "The server cannot handle the request (because it is overloaded or down for maintenance)"
msgstr ""

#: src/includes/class-http-error-descriptions.php:71
msgid "The server was acting as a gateway or proxy and received an invalid response from the upstream server."
msgstr ""

#: src/includes/class-http-error-descriptions.php:71
msgid "Bad Gateway"
msgstr ""

#: src/includes/class-http-error-descriptions.php:70
msgid "Usually this implies future availability (e.g., a new feature of a web-service API)."
msgstr ""

#: src/includes/class-http-error-descriptions.php:70
msgid "The server either does not recognize the request method, or it lacks the ability to fulfil the request"
msgstr ""

#: src/includes/class-http-error-descriptions.php:70
msgid "Not Implemented"
msgstr ""

#: src/includes/class-http-error-descriptions.php:69
msgid "A generic error message, given when an unexpected condition was encountered and no more specific message is suitable."
msgstr ""

#: src/includes/class-http-error-descriptions.php:69
msgid "Internal Server Error"
msgstr ""

#: src/includes/class-http-error-descriptions.php:63
msgid "A server operator has received a legal demand to deny access to a resource or to a set of resources that includes the requested resource."
msgstr ""

#: src/includes/class-http-error-descriptions.php:63
msgid "Unavailable For Legal Reasons"
msgstr ""

#: src/includes/class-http-error-descriptions.php:62
msgid "The server is unwilling to process the request because either an individual header field, or all the header fields collectively, are too large."
msgstr ""

#: src/includes/class-http-error-descriptions.php:62
msgid "Request Header Fields Too Large"
msgstr ""

#: src/includes/class-http-error-descriptions.php:61
msgid "Intended for use with rate-limiting schemes."
msgstr ""

#: src/includes/class-http-error-descriptions.php:61
msgid "The user has sent too many requests in a given amount of time"
msgstr ""

#: src/includes/class-http-error-descriptions.php:61
msgid "Too Many Requests"
msgstr ""

#: src/includes/class-http-error-descriptions.php:60
msgid "Intended to prevent the 'lost update' problem, where a client GETs a resource's state, modifies it, and PUTs it back to the server, when meanwhile a third party has modified the state on the server, leading to a conflict."
msgstr ""

#: src/includes/class-http-error-descriptions.php:60
msgid "The origin server requires the request to be conditional"
msgstr ""

#: src/includes/class-http-error-descriptions.php:60
msgid "Precondition Required"
msgstr ""

#: src/includes/class-http-error-descriptions.php:59
msgid "The client should switch to a different protocol such as TLS/1.3, given in the Upgrade header field."
msgstr ""

#: src/includes/class-http-error-descriptions.php:59
msgid "Upgrade Required"
msgstr ""

#: src/includes/class-http-error-descriptions.php:58
msgid "Indicates that the server is unwilling to risk processing a request that might be replayed."
msgstr ""

#: src/includes/class-http-error-descriptions.php:58
msgid "Too Early"
msgstr ""

#: src/includes/class-http-error-descriptions.php:57
msgid "The request failed because it depended on another request and that request failed (e.g., a PROPPATCH)."
msgstr ""

#: src/includes/class-http-error-descriptions.php:57
msgid "Failed Dependency"
msgstr ""

#: src/includes/class-http-error-descriptions.php:56
msgid "The resource that is being accessed is locked."
msgstr ""

#: src/includes/class-http-error-descriptions.php:56
msgid "Locked"
msgstr ""

#: src/includes/class-http-error-descriptions.php:55
msgid "The request was well-formed but was unable to be followed due to semantic errors."
msgstr ""

#: src/includes/class-http-error-descriptions.php:55
msgid "Unprocessable Entity"
msgstr ""

#: src/includes/class-http-error-descriptions.php:54
msgid "The request was directed at a server that is not able to produce a response (for example because of connection reuse)."
msgstr ""

#: src/includes/class-http-error-descriptions.php:54
msgid "Misdirected Request"
msgstr ""

#: src/includes/class-http-error-descriptions.php:53
msgid "The server cannot meet the requirements of the Expect request-header field."
msgstr ""

#: src/includes/class-http-error-descriptions.php:53
msgid "Expectation Failed"
msgstr ""

#: src/includes/class-http-error-descriptions.php:52
msgid "For example, if the client asked for a part of the file that lies beyond the end of the file."
msgstr ""

#: src/includes/class-http-error-descriptions.php:52
msgid "The client has asked for a portion of the file (byte serving), but the server cannot supply that portion"
msgstr ""

#: src/includes/class-http-error-descriptions.php:52
msgid "Range Not Satisfiable"
msgstr ""

#: src/includes/class-http-error-descriptions.php:51
msgid "For example, the client uploads an image as image/svg+xml, but the server requires that images use a different format."
msgstr ""

#: src/includes/class-http-error-descriptions.php:51
msgid "The request entity has a media type which the server or resource does not support"
msgstr ""

#: src/includes/class-http-error-descriptions.php:51
msgid "Unsupported Media Type"
msgstr ""

#: src/includes/class-http-error-descriptions.php:50
msgid "Often the result of too much data being encoded as a query-string of a GET request, in which it should be converted to a POST request."
msgstr ""

#: src/includes/class-http-error-descriptions.php:50
msgid "The URI provided was too long for the server to process"
msgstr ""

#: src/includes/class-http-error-descriptions.php:50
msgid "URI Too Long"
msgstr ""

#: src/includes/class-http-error-descriptions.php:49
msgid "Previously called \"Request Entity Too Large\"."
msgstr ""

#: src/includes/class-http-error-descriptions.php:49
msgid "The request is larger than the server is willing or able to process"
msgstr ""

#: src/includes/class-http-error-descriptions.php:49
msgid "Payload Too Large"
msgstr ""

#: src/includes/class-http-error-descriptions.php:48
msgid "The server does not meet one of the preconditions that the requester put on the request header fields."
msgstr ""

#: src/includes/class-http-error-descriptions.php:48
msgid "Precondition Failed"
msgstr ""

#: src/includes/class-http-error-descriptions.php:47
msgid "The request did not specify the length of its content, which is required by the requested resource."
msgstr ""

#: src/includes/class-http-error-descriptions.php:47
msgid "Length Required"
msgstr ""

#: src/includes/class-http-error-descriptions.php:46
msgid "Indicates that the resource requested is no longer available and will not be available again."
msgstr ""

#: src/includes/class-http-error-descriptions.php:46
msgid "Gone"
msgstr ""

#: src/includes/class-http-error-descriptions.php:45
msgid "Indicates that the request could not be processed because of conflict in the current state of the resource, such as an edit conflict between multiple simultaneous updates."
msgstr ""

#: src/includes/class-http-error-descriptions.php:45
msgid "Conflict"
msgstr ""

#: src/includes/class-http-error-descriptions.php:44
msgid "The client MAY repeat the request without modifications at any later time."
msgstr ""

#: src/includes/class-http-error-descriptions.php:44
msgid "The server timed out waiting for the request"
msgstr ""

#: src/includes/class-http-error-descriptions.php:44
msgid "Request Timeout"
msgstr ""

#: src/includes/class-http-error-descriptions.php:43
msgid "The client must first authenticate itself with the proxy."
msgstr ""

#: src/includes/class-http-error-descriptions.php:43
msgid "Proxy Authentication Required"
msgstr ""

#: src/includes/class-http-error-descriptions.php:42
msgid "The requested resource is capable of generating only content not acceptable according to the Accept headers sent in the request."
msgstr ""

#: src/includes/class-http-error-descriptions.php:42
msgid "Not Acceptable"
msgstr ""

#: src/includes/class-http-error-descriptions.php:41
msgid "A request method is not supported for the requested resource; for example, a GET request on a form that requires data to be presented via POST, or a PUT request on a read-only resource."
msgstr ""

#: src/includes/class-http-error-descriptions.php:41
msgid "Method Not Allowed"
msgstr ""

#: src/includes/class-http-error-descriptions.php:40
msgid "Subsequent requests by the client are permissible."
msgstr ""

#: src/includes/class-http-error-descriptions.php:40
msgid "The requested resource could not be found but may be available in the future"
msgstr ""

#: src/includes/class-http-error-descriptions.php:40
msgid "Not Found."
msgstr ""

#: src/includes/class-http-error-descriptions.php:39
msgid "This may be due to the user not having the necessary permissions for a resource or needing an account of some sort, or attempting a prohibited action (e.g. creating a duplicate record where only one is allowed)."
msgstr ""

#: src/includes/class-http-error-descriptions.php:39
msgid "The request contained valid data and was understood by the server, but the server is refusing action."
msgstr ""

#: src/includes/class-http-error-descriptions.php:39
msgid "Forbidden."
msgstr ""

#: src/includes/class-http-error-descriptions.php:38
msgid "Authentication is required and has failed or has not yet been provided."
msgstr ""

#: src/includes/class-http-error-descriptions.php:38
msgid "Unauthorized."
msgstr ""

#: src/includes/class-http-error-descriptions.php:37
msgid "The server cannot or will not process the request due to an apparent client error (e.g., malformed request syntax, size too large, invalid request message framing, or deceptive request routing)."
msgstr ""

#: src/includes/class-http-error-descriptions.php:37
msgid "Bad Request."
msgstr ""

#: src/includes/class-http-error-descriptions.php:31
msgid "Permanent Redirect."
msgstr ""

#: src/includes/class-http-error-descriptions.php:30
msgid "In this case, the request should be repeated with another URI; however, future requests should still use the original URI."
msgstr ""

#: src/includes/class-http-error-descriptions.php:30
msgid "Temporary Redirect."
msgstr ""

#: src/includes/class-http-error-descriptions.php:29
msgid "The requested resource is available only through a proxy, the address for which is provided in the response."
msgstr ""

#: src/includes/class-http-error-descriptions.php:29
msgid "Use Proxy."
msgstr ""

#: src/includes/class-http-error-descriptions.php:28
msgid "Indicates that the resource has not been modified since the version specified by the request headers If-Modified-Since or If-None-Match."
msgstr ""

#: src/includes/class-http-error-descriptions.php:28
msgid "Not Modified."
msgstr ""

#: src/includes/class-http-error-descriptions.php:27
msgid "When received in response to a POST (or PUT/DELETE), the client should presume that the server has received the data and should issue a new GET request to the given URI"
msgstr ""

#: src/includes/class-http-error-descriptions.php:27
msgid "The response to the request can be found under another URI using the GET method."
msgstr ""

#: src/includes/class-http-error-descriptions.php:27
msgid "See Other."
msgstr ""

#: src/includes/class-http-error-descriptions.php:26
msgid "Tells the client to look at (browse to) another URL."
msgstr ""

#: src/includes/class-http-error-descriptions.php:26
msgid "Found (Previously \"Moved temporarily\")."
msgstr ""

#: src/includes/class-http-error-descriptions.php:25,
#: src/includes/class-http-error-descriptions.php:31
msgid "This and all future requests should be directed to the given URI."
msgstr ""

#: src/includes/class-http-error-descriptions.php:25
msgid "Moved Permanently."
msgstr ""

#: src/includes/class-http-error-descriptions.php:24
msgid "Indicates multiple options for the resource from which the client may choose (via agent-driven content negotiation)."
msgstr ""

#: src/includes/class-http-error-descriptions.php:24
msgid "Multiple Choices."
msgstr ""

#: src/includes/class-filesystem-functions.php:678
msgid "Could not copy files."
msgstr ""

#: src/includes/class-database-utility.php:726
msgid "Follow this link to install the WP-Optimize plugin."
msgstr ""

#: src/includes/class-database-utility.php:722
msgid "Follow this link to activate the WP-Optimize plugin."
msgstr ""

#: src/includes/class-database-utility.php:722
msgid "WP-Optimize is installed but currently inactive."
msgstr ""

#: src/includes/class-commands.php:1142
msgid "The creation of your clone should now begin, and your WordPress username and password will be displayed below when ready."
msgstr ""

#: src/includes/class-commands.php:1142
msgid "No backup will be started."
msgstr ""

#: src/includes/class-commands.php:1144, src/includes/class-commands.php:1142
msgid "If the clone fails to boot, then the token will be released within an hour."
msgstr ""

#: src/includes/class-commands.php:1144, src/includes/class-commands.php:1142
msgid "N.B. You will be charged one token once the clone is ready."
msgstr ""

#: src/includes/class-commands.php:954
msgid "Backup:"
msgstr ""

#: src/includes/class-commands.php:967
msgid "You have no local backups to send."
msgstr ""

#: src/central/bootstrap.php:757
msgid "Follow this link to read about how to set browser permission"
msgstr ""

#: src/central/bootstrap.php:757
msgid "Your web browser prevented the copy operation."
msgstr ""

#: src/central/bootstrap.php:755
msgid "Copy to clipboard"
msgstr ""

#: src/addons/wp-cli.php:1232
msgid "The creation of your data for creating the clone should now begin, and you will receive an email when it completes."
msgstr ""

#: src/addons/wp-cli.php:1232
msgid "The UpdraftClone boot process has started."
msgstr ""

#: src/addons/wp-cli.php:1206
msgid "You will receive an email when it completes."
msgstr ""

#: src/addons/wp-cli.php:1206
msgid "The UpdraftClone boot process for an empty WordPress install has begun."
msgstr ""

#: src/addons/wp-cli.php:1202
msgid "Failed to boot clone:"
msgstr ""

#: src/addons/wp-cli.php:1150
msgid "Failed to connect to UpdraftPlus.Com:"
msgstr ""

#: src/addons/wp-cli.php:1133
msgid "You can create a clone key at %s"
msgstr ""

#: src/addons/wp-cli.php:1133
msgid "No clone key provided."
msgstr ""

#: src/addons/wp-cli.php:1056
msgid "Please make sure these two parameters are set."
msgstr ""

#: src/addons/wp-cli.php:1056
msgid "An email and password are required to connect to UpdraftPlus.com."
msgstr ""

#: src/addons/wp-cli.php:897
msgid "Get the \"%s\" addon: %s"
msgstr ""

#: src/addons/wp-cli.php:897
msgid "The %s is working with \"%s\" addon."
msgstr ""

#: src/addons/wp-cli.php:897
msgid "You have given the %s option."
msgstr ""

#: src/addons/wp-cli.php:211
msgid "You can see the last log message by running the following command: \"%s\""
msgstr ""

#: src/addons/wp-cli.php:211
msgid "Backup has been started successfully."
msgstr ""

#: src/addons/webdav.php:1125
msgid "WebDAV server returned 501; probably does not support Content-Range (chunks)"
msgstr ""

#: src/addons/webdav.php:1114
msgid "WebDAV server returned 400; probably does not support Content-Range (chunks)"
msgstr ""

#: src/addons/webdav.php:1119, src/addons/webdav.php:1131
msgid "Unexpected HTTP response code (%s): %s"
msgstr ""

#: src/addons/webdav.php:496
msgid "We successfully accessed the directory, and were able to create files within it"
msgstr ""

#: src/addons/webdav.php:438
msgid "Split uploads into chunks"
msgstr ""

#: src/addons/webdav.php:428
msgid "If you do not know the details, then you will need to ask your WebDAV provider."
msgstr ""

#: src/addons/webdav.php:428
msgid "This WebDAV URL is generated by filling in the options below."
msgstr ""

#: src/addons/sftp.php:713
msgid "Failed: We are unable to match the fingerprint."
msgstr ""

#: src/addons/sftp.php:619, src/addons/sftp.php:620
msgid "MITM attacks"
msgstr ""

#: src/addons/sftp.php:608
msgid "Thus, if using SCP then you will need to ensure that your webserver allows PHP processes to run long enough to upload your largest backup file."
msgstr ""

#: src/addons/sftp.php:608
msgid "Resuming partial uploads is supported for SFTP, but not for SCP."
msgstr ""

#: src/addons/sftp.php:606
msgid "Using a fingerprint is not essential, but you are not secure against %s if you do not use one"
msgstr ""

#: src/addons/sftp.php:606
msgid "MD5 (128-bit) fingerprint, in hex format - should have the same length and general appearance as this (colons optional): 73:51:43:b1:b5:fc:8b:b7:0a:3a:a9:b1:0f:69:73:a8."
msgstr ""

#: src/addons/sftp.php:426
msgid "You need to provide the unencrypted key (see: https://updraftplus.com/faqs/why-must-i-use-a-non-encrypted-sftp-key/)."
msgstr ""

#: src/addons/sftp.php:426
msgid "The key provided is encrypted."
msgstr ""

#: src/addons/sftp.php:61
msgid "Explicit encryption is used by default."
msgstr ""

#: src/addons/sftp.php:61
msgid "If you find this happening, then go into the \"Expert Options\" (below) and turn off SSL there."
msgstr ""

#: src/addons/sftp.php:61
msgid "Some servers advertise encrypted FTP as available, but then time-out (after a long time) when you attempt to use it."
msgstr ""

#: src/addons/sftp.php:61
msgid "The 'Test FTP Login' button will tell you what type of connection is in use."
msgstr ""

#: src/addons/sftp.php:61
msgid "Encrypted FTP is available, and will be automatically tried first (before falling back to non-encrypted if it is not successful), unless you disable it using the expert options."
msgstr ""

#: src/addons/s3-enhanced.php:285
msgid "Please check your access credentials, and if those are correct then try another bucket name (as another AWS user may already have taken your name)."
msgstr ""

#: src/addons/s3-enhanced.php:285, src/methods/s3.php:1436
msgid "Failure: We could not successfully access or create such a bucket."
msgstr ""

#: src/addons/reporting.php:243
msgid "Instead, they provide information that you might find useful, or that may indicate the source of a problem if the backup did not succeed."
msgstr ""

#: src/addons/reporting.php:243
msgid "Note that warning messages are advisory - the backup process does not stop for them."
msgstr ""

#: src/addons/pcloud.php:724, src/methods/dropbox.php:944,
#: src/methods/dropbox.php:915
msgid "though part of the returned information was not as expected - whether this indicates a real problem cannot be determined"
msgstr ""

#: src/addons/pcloud.php:383
msgid "You are not authenticated with pCloud"
msgstr ""

#: src/addons/pcloud.php:297
msgid "Listing the files failed:"
msgstr ""

#: src/addons/pcloud.php:256
msgid "Chunked upload failed"
msgstr ""

#: src/addons/onedrive.php:1329, src/addons/pcloud.php:550,
#: src/methods/dropbox.php:606
msgid "(You are already authenticated)."
msgstr ""

#: src/addons/onedrive.php:1315
msgid "As such, you must use the main %s %s App to authenticate with your account."
msgstr ""

#: src/addons/onedrive.php:1315
msgid "This site uses a URL which is either non-HTTPS, or is localhost or 127.0.0.1 URL."
msgstr ""

#: src/addons/onedrive.php:1121, src/addons/pcloud.php:653,
#: src/methods/googledrive.php:490,
#: src/includes/Dropbox2/OAuth/Consumer/ConsumerAbstract.php:129
msgid "The %s authentication could not go ahead, because something else on your site is breaking it."
msgstr ""

#: src/addons/morefiles.php:907
msgid "Restore all %s not listed below"
msgstr ""

#: src/addons/morefiles.php:905
msgid "This option will ensure all %s files not found will be restored."
msgstr ""

#: src/addons/morefiles.php:905
msgid "The amount of %1$s files scanned is near or over the php_max_input_vars value so some %1$s files maybe truncated."
msgstr ""

#: src/addons/morefiles.php:897
msgid "Files not chosen will not be replaced."
msgstr ""

#: src/addons/morefiles.php:897
msgid "If you do not want to restore all your %s files, then de-select the unwanted ones here."
msgstr ""

#: src/addons/morefiles.php:834
msgid "Please select where you want these backups to be restored to."
msgstr ""

#: src/addons/morefiles.php:834
msgid "The original filesystem location for some of the following items was not found."
msgstr ""

#: src/addons/morefiles.php:221
msgid "Zip error: (%s)"
msgstr ""

#: src/addons/morefiles.php:221
msgid "Unable to read any files from the zip (%s) - could not pre-scan it to check its integrity."
msgstr ""

#: src/addons/moredatabase.php:411
msgid "Encryption aborted."
msgstr ""

#: src/addons/moredatabase.php:411
msgid "Encryption error occurred when encrypting database."
msgstr ""

#: src/addons/moredatabase.php:342
msgid "This is also the key used to decrypt backups from this admin interface (so if you change it, then automatic decryption will not work until you change it back)."
msgstr ""

#: src/addons/moredatabase.php:342
msgid "<strong>Do make a separate record of it and do not lose it, or all your backups <em>will</em> be useless.</strong>"
msgstr ""

#: src/addons/moredatabase.php:342
msgid "If you enter text here, it is used to encrypt database backups (Rijndael)."
msgstr ""

#: src/addons/migrator.php:551
msgid "When you are shown the key, then press the 'Migrate' button on the other (sending) site, and copy-and-paste the key over there (in the 'Send a backup to another site' section)."
msgstr ""

#: src/addons/migrator.php:551
msgid "To allow another site to send a backup to this site, create a key below."
msgstr ""

#: src/addons/googlecloud.php:1320
msgid "<strong>(You are already authenticated,</strong> though you can authenticate again to refresh your access if you've had a problem)."
msgstr ""

#: src/addons/googlecloud.php:1308
msgid "Bucket names have to be globally unique."
msgstr ""

#: src/addons/googlecloud.php:1284, src/methods/googledrive.php:1515
msgid "You will need to change your site's address (%s) before you can use %s for storage."
msgstr ""

#: src/addons/googlecloud.php:1284
msgid "%s does not allow authorization of sites hosted on direct IP addresses."
msgstr ""

#: src/addons/googlecloud.php:1288, src/methods/googledrive.php:1494
msgid "The description below is sufficient for more expert users."
msgstr ""

#: src/addons/googlecloud.php:876
msgid "Please complete all fields in %s settings and save the settings."
msgstr ""

#: src/addons/googlecloud.php:876
msgid "But no %s settings were found."
msgstr ""

#: src/addons/googlecloud.php:879, src/addons/googlecloud.php:874
msgid "Please enter a bucket name in the %s settings and save settings."
msgstr ""

#: src/addons/googlecloud.php:879, src/addons/googlecloud.php:874
msgid "But no bucket was defined, so backups may not complete."
msgstr ""

#: src/addons/googlecloud.php:510, src/methods/googledrive.php:590
msgid "Finally, if that does not work, then use expert mode to wipe all your settings, create a new Google client ID/secret, and start again."
msgstr ""

#: src/addons/googlecloud.php:510, src/methods/googledrive.php:590
msgid "Re-check it, then follow the link to authenticate again."
msgstr ""

#: src/addons/googlecloud.php:510, src/methods/googledrive.php:590
msgid "This often means that you entered your client secret wrongly, or that you have not yet re-authenticated (below) since correcting it."
msgstr ""

#: src/addons/googlecloud.php:510, src/methods/googledrive.php:590
msgid "No refresh token was received from Google."
msgstr ""

#: src/addons/googlecloud.php:464, src/addons/onedrive.php:1121,
#: src/addons/pcloud.php:653, src/methods/googledrive.php:490
msgid "Turning off any debugging settings may also help)."
msgstr ""

#: src/addons/googlecloud.php:464, src/addons/onedrive.php:1121,
#: src/addons/pcloud.php:653, src/methods/googledrive.php:490,
#: src/includes/Dropbox2/OAuth/Consumer/ConsumerAbstract.php:129
msgid "Specifically, you are looking for the component that sends output (most likely PHP warnings/errors) before the page begins."
msgstr ""

#: src/addons/googlecloud.php:464, src/addons/onedrive.php:1121,
#: src/addons/pcloud.php:653, src/methods/googledrive.php:490,
#: src/includes/Dropbox2/OAuth/Consumer/ConsumerAbstract.php:129
msgid "Try disabling your other plugins and switching to a default theme."
msgstr ""

#: src/addons/googlecloud.php:464
msgid "Authentication could not go ahead, because something else on your site is breaking it."
msgstr ""

#: src/addons/backblaze.php:847
msgid "Error: unable to set object lock for file: %s"
msgstr ""

#: src/addons/backblaze.php:791
msgid "Spaces are not allowed."
msgstr ""

#: src/addons/backblaze.php:791
msgid "There are limits upon which path-names are valid."
msgstr ""

#: src/addons/backblaze.php:789
msgid "A file which is locked cannot be deleted by any means until the lock time duration has expired."
msgstr ""

#: src/addons/backblaze.php:788
msgid "Read more about the Backblaze Object Lock"
msgstr ""

#: src/addons/backblaze.php:787
msgid "0 days means no lock is applied."
msgstr ""

#: src/addons/backblaze.php:787
msgid "Use this to protect your data from hackers or for regulatory compliance reasons."
msgstr ""

#: src/addons/backblaze.php:787
msgid "Object lock is a Backblaze B2 feature that prevents data from being changed or deleted for a given number of days."
msgstr ""

#: src/addons/backblaze.php:786
msgid "Object lock duration (days)"
msgstr ""

#: src/addons/backblaze.php:580, src/methods/s3.php:1436
msgid "Please check your access credentials, and if those are correct then try another bucket name (as another %s user may already have taken your name)."
msgstr ""

#: src/addons/backblaze.php:580
msgid "Failure: We could not successfully access or create such a bucket"
msgstr ""

#: src/addons/backblaze.php:118
msgid "Error: unable to set object lock for bucket: %s"
msgstr ""

#: src/addons/azure.php:691, src/addons/onedrive.php:1314,
#: src/methods/dreamobjects.php:187
msgid "For more detailed instructions, follow this link."
msgstr ""

#: src/addons/anonymisation.php:48, src/addons/anonymisation.php:71,
#: src/addons/anonymisation.php:101
msgid "Anonymize WooCommerce order data"
msgstr ""

#: src/updraftplus.php:226
msgid "Most likely, WordPress malfunctioned when copying the plugin files."
msgstr ""

#: src/updraftplus.php:226
msgid "You do not have UpdraftPlus completely installed - please de-install and install it again."
msgstr ""

#: src/updraftplus.php:97
msgid "Most likely, WordPress did not correctly unpack the plugin when installing it."
msgstr ""

#: src/updraftplus.php:97
msgid "The expected file %s is missing from your UpdraftPlus installation."
msgstr ""

#: src/restorer.php:3101
msgid "UpdraftPlus plugin slug:"
msgstr ""

#: src/restorer.php:2911
msgid "We will attempt to restore by simply emptying the tables; this should work as long as a) you are restoring from a WordPress version with the same database structure, and b) Your imported database does not contain any tables which are not already present on the importing site."
msgstr ""

#: src/restorer.php:2911
msgid "Your database user does not have permission to create tables."
msgstr ""

#: src/restorer.php:2964
msgid "We will attempt to restore by simply emptying the tables; this should work as long as you are restoring from a WordPress version with the same database structure (%s)"
msgstr ""

#: src/restorer.php:2964
msgid "Your database user does not have permission to drop tables."
msgstr ""

#: src/restorer.php:2762
msgid "If these happen, then you will need to manually restore the file via phpMyAdmin or another method."
msgstr ""

#: src/restorer.php:2762
msgid "Timeouts are much more likely."
msgstr ""

#: src/restorer.php:2762
msgid "Warning: PHP safe_mode is active on your server."
msgstr ""

#: src/restorer.php:854
msgid "Check your file permissions."
msgstr ""

#: src/restorer.php:854
msgid "Could not move the files into place."
msgstr ""

#: src/restorer.php:853
msgid "Check your wp-content/upgrade folder."
msgstr ""

#: src/restorer.php:853
msgid "Could not move new files into place."
msgstr ""

#: src/restorer.php:841
msgid "It must be restored manually."
msgstr ""

#: src/restorer.php:841
msgid "UpdraftPlus is not able to directly restore this kind of entity."
msgstr ""

#: src/restorer.php:508
msgid "If you had anything necessary in your WordPress directory then you will need to re-add it manually from the zip file."
msgstr ""

#: src/restorer.php:508
msgid "Skipping restoration of WordPress core when importing a single site into a multisite installation."
msgstr ""

#: src/class-updraftplus.php:6318
msgid "Future releases of UpdraftPlus will require a more recent PHP version to use these features; we recommend that you speak to your web hosting company about updating to version %s or higher."
msgstr ""

#: src/class-updraftplus.php:6318
msgid "Your site is running on PHP version %s and has feature(s) currently enabled (%s) which are deprecated upon this PHP version."
msgstr ""

#: src/class-updraftplus.php:5511
msgid "UpdraftPlus was unable to find any tables when scanning the database backup; it maybe corrupt."
msgstr ""

#: src/class-updraftplus.php:5525
msgid "Deselect All"
msgstr ""

#: src/class-updraftplus.php:5525
msgid "Select All"
msgstr ""

#: src/class-updraftplus.php:5520
msgid "The amount of database tables scanned is near or over the php_max_input_vars value so some tables maybe truncated."
msgstr ""

#: src/class-updraftplus.php:5519, src/class-updraftplus.php:5520
msgid "This option will ensure all tables not found will be backed up."
msgstr ""

#: src/class-updraftplus.php:5519
msgid "The database scan was taking too long and consequently the list of all tables in the database could not be completed."
msgstr ""

#: src/class-updraftplus.php:5476
msgid "This database backup has the following non-core WordPress database tables excluded: %s"
msgstr ""

#: src/class-updraftplus.php:5154
msgid "This is not expected to be a problem for restoring the site, as long as visits to the former address still reach the site."
msgstr ""

#: src/class-updraftplus.php:5154
msgid "The website address in the backup set (%s) is slightly different from that of the site now (%s)."
msgstr ""

#: src/class-updraftplus.php:5191, src/class-updraftplus.php:5173
msgid "You need the Migrator add-on in order to make this work."
msgstr ""

#: src/class-updraftplus.php:5191, src/class-updraftplus.php:5173
msgid "This backup set is from a different site (%s) - this is not a restoration, but a migration."
msgstr ""

#: src/class-updraftplus.php:5201
msgid "There are no guarantees that WordPress can handle this."
msgstr ""

#: src/class-updraftplus.php:5201
msgid "You are importing from a newer version of WordPress (%s) into an older one (%s)."
msgstr ""

#: src/class-updraftplus.php:5236
msgid "Only the first site of the network will be accessible."
msgstr ""

#: src/class-updraftplus.php:5236
msgid "Your backup is of a WordPress multisite install; but this site is not."
msgstr ""

#: src/class-updraftplus.php:5061, src/restorer.php:1179,
#: src/includes/class-updraftplus-encryption.php:354
msgid "The most likely cause is that you used the wrong key."
msgstr ""

#: src/class-updraftplus.php:5049, src/restorer.php:1166,
#: src/includes/class-updraftplus-encryption.php:336
msgid "The database file is encrypted, but you have no encryption key entered."
msgstr ""

#: src/class-updraftplus.php:5051
msgid "The database file is encrypted."
msgstr ""

#: src/class-updraftplus.php:5051, src/class-updraftplus.php:5049,
#: src/class-updraftplus.php:5061, src/restorer.php:1166,
#: src/restorer.php:1179, src/includes/class-updraftplus-encryption.php:354,
#: src/includes/class-updraftplus-encryption.php:336
msgid "Decryption failed."
msgstr ""

#: src/class-updraftplus.php:4549
msgid "Backup probably failed."
msgstr ""

#: src/class-updraftplus.php:4549
msgid "Could not save backup history because we have no backup array."
msgstr ""

#: src/class-updraftplus.php:3812
msgid "Warnings encountered (note: this is for information; the backup has completed successfully)"
msgstr ""

#: src/class-updraftplus.php:3667
msgid "The backup succeeded and is now complete"
msgstr ""

#: src/class-updraftplus.php:3313
msgid "Please check the backup directory and ensure it is writable so that backups may continue."
msgstr ""

#: src/class-updraftplus.php:3313
msgid "UpdraftPlus is unable to perform backups as your backup directory is not writable or the disk space is full."
msgstr ""

#: src/class-updraftplus.php:3312
msgid "Backup aborted - check your UpdraftPlus settings."
msgstr ""

#: src/class-updraftplus.php:3312
msgid "Could not create files in the backup directory."
msgstr ""

#: src/class-updraftplus.php:2091
msgid "Theme"
msgstr ""

#: src/class-updraftplus.php:2090
msgid "Plugin"
msgstr ""

#: src/class-updraftplus.php:698
msgid "Check back in a minute."
msgstr ""

#: src/class-updraftplus.php:698
msgid "Briefly unavailable for scheduled maintenance."
msgstr ""

#: src/class-updraftplus.php:245
msgid "So, go to the \"Plugins\" page, de-activate and de-install UpdraftPlus, and then try again."
msgstr ""

#: src/class-updraftplus.php:245
msgid "That is safe - all your settings and backups will be retained."
msgstr ""

#: src/class-updraftplus.php:245
msgid "WordPress will only allow you to install your new version after first de-installing the existing one."
msgstr ""

#: src/class-updraftplus.php:245
msgid "A version of UpdraftPlus is already installed."
msgstr ""

#: src/backup.php:4387
msgid "your web hosting account is full; please see: %s"
msgstr ""

#: src/backup.php:3163
msgid "Possible causes include that the link points to an invalid or inaccessible location."
msgstr ""

#: src/backup.php:3163
msgid "%s: unfollowable link - could not be followed to back up (readlink=%s)."
msgstr ""

#: src/backup.php:2051
msgid "To be able to backup the routines, you must be the user named as the routine DEFINER(s), have the SHOW_ROUTINE privilege (for MySQL 8.0.20+ users), have the SELECT privilege at the global level, or have the CREATE ROUTINE, ALTER ROUTINE, or EXECUTE privilege granted at a scope that includes the routines."
msgstr ""

#: src/backup.php:2051
msgid "Your WordPress database user doesn't have sufficient privileges to read these stored routines."
msgstr ""

#: src/backup.php:2051
msgid "Dumping routines: "
msgstr ""

#: src/backup.php:1901, src/backup.php:1899
msgid "The database backup has failed"
msgstr ""

#: src/backup.php:365
msgid "Consult the log file for more information."
msgstr ""

#: src/backup.php:365
msgid "Could not create %s zip."
msgstr ""

#: src/admin.php:6360
msgid "Thus, we recommend you choose a full backup when performing a manual backup and to use that option when creating a scheduled backup."
msgstr ""

#: src/admin.php:6360
msgid "%s permits UpdraftPlus to perform only one backup per month."
msgstr ""

#: src/admin.php:5672
msgid "Please refresh the settings page and try again"
msgstr ""

#: src/admin.php:5672
msgid "Your settings failed to save."
msgstr ""

#: src/admin.php:5631
msgid "Please reload the settings page before trying to save settings."
msgstr ""

#: src/admin.php:5631
msgid "UpdraftPlus seems to have been updated to version (%s), which is different to the version running when this settings page was loaded."
msgstr ""

#: src/admin.php:5244
msgid "Timestamp:"
msgstr ""

#: src/admin.php:5163,
#: src/templates/wp-admin/settings/delete-and-restore-modals.php:98
msgid "Restoration"
msgstr ""

#: src/admin.php:5162,
#: src/templates/wp-admin/settings/delete-and-restore-modals.php:97
msgid "Verifications"
msgstr ""

#: src/admin.php:5135
msgid "Do not close this page until it reports itself as having finished."
msgstr ""

#: src/admin.php:5135
msgid "The restore operation has begun (%s)."
msgstr ""

#: src/admin.php:4417, src/addons/azure.php:689,
#: src/methods/dreamobjects.php:182, src/methods/s3.php:926,
#: src/methods/s3.php:1080, src/methods/s3generic.php:192,
#: src/methods/updraftvault.php:477
msgid "Please contact your web hosting provider's support and ask for them to enable it."
msgstr ""

#: src/admin.php:4417, src/addons/azure.php:689
msgid "Your web server's PHP installation does not included a <strong>required</strong> (for %s) module (%s)."
msgstr ""

#: src/admin.php:4424
msgid "Ask your web host to install Curl/SSL in order to gain the ability for encryption (via an add-on)."
msgstr ""

#: src/admin.php:4424
msgid "Communications with %s will be unencrypted."
msgstr ""

#: src/admin.php:4426, src/methods/cloudfiles-new.php:281,
#: src/methods/cloudfiles-new.php:282, src/methods/cloudfiles.php:455,
#: src/methods/dreamobjects.php:181, src/methods/openstack-base.php:577,
#: src/methods/openstack2.php:242, src/methods/s3.php:930,
#: src/methods/s3.php:1081, src/methods/s3generic.php:193
msgid "Please do not file any support requests; there is no alternative."
msgstr ""

#: src/admin.php:4426
msgid "%s <strong>requires</strong> Curl+https."
msgstr ""

#: src/admin.php:4426, src/methods/cloudfiles-new.php:281,
#: src/methods/cloudfiles-new.php:282, src/methods/cloudfiles.php:455,
#: src/methods/dreamobjects.php:181, src/methods/openstack-base.php:577,
#: src/methods/openstack2.php:242, src/methods/s3.php:930,
#: src/methods/s3.php:1081, src/methods/s3generic.php:193
msgid "Please contact your web hosting provider's support."
msgstr ""

#: src/admin.php:4426
msgid "We cannot access %s without this support."
msgstr ""

#: src/admin.php:4426, src/admin.php:4424
msgid "Your web server's PHP/Curl installation does not support https access."
msgstr ""

#: src/admin.php:4429
msgid "If you see any errors to do with encryption, then look in the 'Expert Settings' for more help."
msgstr ""

#: src/admin.php:4429
msgid "Good news: Your site's communications with %s can be encrypted."
msgstr ""

#: src/admin.php:4333, src/addons/morefiles.php:371
msgid "For entities at the top level, you can use a * at the start or end of the entry as a wildcard."
msgstr ""

#: src/admin.php:4364
msgid "none present"
msgstr ""

#: src/admin.php:4043
msgid "You should check with your hosting provider that this will not cause any problems"
msgstr ""

#: src/admin.php:4043
msgid "The folder was created, but we had to change its file permissions to 777 (world-writable) to be able to write to it."
msgstr ""

#: src/admin.php:3891
msgid "Old folders successfully removed."
msgstr ""

#: src/admin.php:3893
msgid "You may want to do this manually."
msgstr ""

#: src/admin.php:3893
msgid "Old folder removal failed for some reason."
msgstr ""

#: src/admin.php:3888
msgid "Remove old folders"
msgstr ""

#: src/admin.php:3878
msgid "Do not stop the backup simply because it seems to have remained in the same place for a while - that is normal."
msgstr ""

#: src/admin.php:3878
msgid "Note: the progress bar below is based on stages, NOT time."
msgstr ""

#: src/admin.php:3602
msgid "Delete old folders"
msgstr ""

#: src/admin.php:3596
msgid "You should press this button to delete them as soon as you have verified that the restoration worked."
msgstr ""

#: src/admin.php:3596
msgid "Your WordPress install has old folders from its state before you restored/migrated (technical information: these are suffixed with -old)."
msgstr ""

#: src/admin.php:3050
msgid "This plugin may struggle with a memory limit of less than 64 Mb  - especially if you have very large files uploaded (though on the other hand, many sites will be successful with a 32Mb limit - your experience may vary)."
msgstr ""

#: src/admin.php:3050
msgid "UpdraftPlus attempted to raise it but was unsuccessful."
msgstr ""

#: src/admin.php:3050
msgid "Your PHP memory limit (set by your web hosting company) is very low."
msgstr ""

#: src/admin.php:2993
msgid "You should rename the directory to wp-content/plugins/updraftplus to fix this problem."
msgstr ""

#: src/admin.php:2993
msgid "The UpdraftPlus directory in wp-content/plugins has white-space in it; WordPress does not like this."
msgstr ""

#: src/admin.php:2889
msgid "We really appreciate your support!"
msgstr ""

#: src/admin.php:2889
msgid "Enjoyed %s? Please leave us a %s rating on %s or %s"
msgstr ""

#: src/admin.php:2751
msgid "Perhaps you need to install an add-on?"
msgstr ""

#: src/admin.php:2751
msgid "This looks like a file created by UpdraftPlus, but this install does not know about this type of object: %s."
msgstr ""

#: src/admin.php:2465
msgid "Aborting this backup."
msgstr ""

#: src/admin.php:2458, src/includes/class-commands.php:992
msgid "OK."
msgstr ""

#: src/admin.php:1674
msgid "Therefore, please download the attachment from the original backup email and upload it using the \"Upload backup files\" facility in the \"Existing Backups\" tab."
msgstr ""

#: src/admin.php:1674
msgid "The email protocol does not allow a remote backup to be retrieved from an email that has been sent."
msgstr ""

#: src/admin.php:1491, src/admin.php:1498, src/addons/azure.php:61,
#: src/addons/onedrive.php:105
msgid "For more information, please see: %s"
msgstr ""

#: src/admin.php:1491, src/admin.php:1498, src/addons/azure.php:61,
#: src/addons/onedrive.php:105
msgid "You will need to migrate to the Global endpoint in your UpdraftPlus settings."
msgstr ""

#: src/admin.php:1491, src/admin.php:1498, src/addons/azure.php:61,
#: src/addons/onedrive.php:105
msgid "Due to the shutdown of the %1$s endpoint, support for %1$s will be ending soon."
msgstr ""

#: src/admin.php:1484
msgid "However, we were not able to register this site with %1$s, as there are no available %1$s licences on the account."
msgstr ""

#: src/admin.php:1484
msgid "Connection to your %s account was successful."
msgstr ""

#: src/admin.php:1477
msgid "You will need to switch to a different end-point and migrate your data before that date."
msgstr ""

#: src/admin.php:1477
msgid "The %s endpoint is scheduled to shut down on the 1st October 2018."
msgstr ""

#: src/admin.php:1456
msgid "Go here to complete your settings for %s."
msgstr ""

#: src/admin.php:1429
msgid "Unless this is a development site, this means that the scheduler in your WordPress install is not working properly."
msgstr ""

#: src/admin.php:1429
msgid "WordPress has a number (%d) of scheduled tasks which are overdue."
msgstr ""

#: src/admin.php:1402
msgid "Please try to make sure that the notice you are seeing is from UpdraftPlus before you raise a support request."
msgstr ""

#: src/admin.php:1402
msgid "You may see debugging notices on this page not just from UpdraftPlus, but from any other plugin installed."
msgstr ""

#: src/admin.php:1402
msgid "UpdraftPlus's debug mode is on."
msgstr ""

#: src/admin.php:1398
msgid "To get faster backups, ask your web hosting provider how to turn on the PHP zip module on your hosting."
msgstr ""

#: src/admin.php:1398
msgid "Consequently, UpdraftPlus will use a built-in zip module (PclZip); this is significantly slower."
msgstr ""

#: src/admin.php:1398
msgid "Neither the PHP zip module nor a zip executable are available on your webserver."
msgstr ""

#: src/admin.php:1390
msgid "It may work for you, but if it does not, then please be aware that no support is available until you upgrade WordPress."
msgstr ""

#: src/admin.php:1390
msgid "UpdraftPlus does not officially support versions of WordPress before %s."
msgstr ""

#: src/admin.php:1386
msgid "Contact your the operator of your server (e.g. your web hosting company) to resolve this issue."
msgstr ""

#: src/admin.php:1386
msgid "UpdraftPlus could well run out of space."
msgstr ""

#: src/admin.php:1386
msgid "You have less than %s of free disk space on the disk which UpdraftPlus is configured to use to create backups."
msgstr ""

#: src/admin.php:1380
msgid "No backups can run (even &quot;Backup Now&quot;) unless either you have set up a facility to call the scheduler manually, or until it is enabled."
msgstr ""

#: src/admin.php:1380
msgid "The scheduler is disabled in your WordPress install, via the DISABLE_WP_CRON setting."
msgstr ""

#: src/admin.php:1161
msgid "Therefore, please reload the page."
msgstr ""

#: src/admin.php:1160
msgid "This is usually caused by your dashboard page having been open a long time, and the included security tokens having since expired."
msgstr ""

#: src/admin.php:1158
msgid "Searching for backups..."
msgstr ""

#: src/admin.php:1157
msgid "Send a new backup"
msgstr ""

#: src/admin.php:1156
msgid "Send existing backup"
msgstr ""

#: src/admin.php:1154
msgid "You can send an existing local backup to the remote site or create a new backup"
msgstr ""

#: src/admin.php:1153
msgid "See the browser console log for more information."
msgstr ""

#: src/admin.php:1153
msgid "If you proceed with the restoration then some of the restore options will be lost and you may get unexpected results."
msgstr ""

#: src/admin.php:1109
msgid "This could mean that your .htaccess file has incorrect contents, is missing, or that your webserver is missing an equivalent mechanism."
msgstr ""

#: src/admin.php:1109
msgid "Attempts by the browser to access some pages have returned a \"not found (404)\" error."
msgstr ""

#: src/admin.php:1094
msgid "No UpdraftCentral licences were available."
msgstr ""

#: src/admin.php:1087
msgid "You may have a security module on your webserver blocking the restoration operation."
msgstr ""

#: src/admin.php:1087
msgid "HTML was detected in the response."
msgstr ""

#: src/admin.php:1072
msgid "Once the clone has finished deploying it, you will receive an email."
msgstr ""

#: src/admin.php:1072
msgid "The clone has been provisioned, and its data has been sent to it."
msgstr ""

#: src/admin.php:1071
msgid "This may fail if you have components that are incompatible with earlier versions."
msgstr ""

#: src/admin.php:1071
msgid "Warning: you have selected a lower version than your currently installed version."
msgstr ""

#: src/admin.php:1056
msgid "Adding site to UpdraftCentral Cloud."
msgstr ""

#: src/admin.php:1056
msgid "Key created."
msgstr ""

#: src/admin.php:1055
msgid "Requesting UpdraftCentral Key."
msgstr ""

#: src/admin.php:1055
msgid "Connected."
msgstr ""

#: src/admin.php:1037
msgid "It would be best to download the zip to your computer."
msgstr ""

#: src/admin.php:1037
msgid "This could be caused by a timeout."
msgstr ""

#: src/admin.php:1037
msgid "Unable to download file."
msgstr ""

#: src/admin.php:1033
msgid "Please check the following:"
msgstr ""

#: src/admin.php:1033
msgid "The file failed to upload."
msgstr ""

#: src/admin.php:1020
msgid "Please choose a valid UpdraftPlus export file."
msgstr ""

#: src/admin.php:1020
msgid "Error: The chosen file is corrupt."
msgstr ""

#: src/admin.php:967
msgid "Please select at least one, and then try again."
msgstr ""

#: src/admin.php:967
msgid "You did not select any components to restore."
msgstr ""

#: src/admin.php:950, src/admin.php:951
msgid "This file is not an UpdraftPlus backup archive (such files are .zip or .gz files which have a name like: backup_(time)_(site name)_(code)_(type).(zip|gz))."
msgstr ""

#: src/admin.php:940
msgid "File size"
msgstr ""

#: src/admin.php:921
msgid "Do not close your browser until it reports itself as having finished."
msgstr ""

#: src/admin.php:921
msgid "The restore operation has begun."
msgstr ""

#: src/admin.php:3742
msgid "file %d of %d"
msgstr ""

#: src/addons/googlecloud.php:626
msgid "Have not yet obtained an access token from Google - you need to authorise or re-authorise your connection to Google Cloud."
msgstr ""

#: src/addons/googlecloud.php:1319, src/addons/googlecloud.php:1372,
#: src/methods/googledrive.php:1507, src/methods/googledrive.php:1684
msgid "Sign in with %s"
msgstr ""

#: src/admin.php:3822
msgid "(after %ss)"
msgstr ""

#: src/admin.php:3822
msgid "next resumption: %d"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:113
msgid "Install debugging plugins:"
msgstr ""

#: src/restorer.php:3095
msgid "Old ABSPATH:"
msgstr ""

#: src/udaddons/options.php:112
msgid "An update is available for UpdraftPlus - please connect here to gain access to it."
msgstr ""

#: src/templates/wp-admin/settings/upload-backups-modal.php:17
msgid "already uploaded"
msgstr ""

#: src/templates/wp-admin/settings/take-backup.php:99
msgid "Please check out UpdraftPlus Premium."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:115
msgid "Anonymise personal data in your database backups."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:113,
#: src/templates/wp-admin/settings/tab-addons.php:114
msgid "Anonymisation functions"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:281
msgid "For more reporting features, use the Premium version"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:277
msgid "Your email backup and a report will be sent to"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:148
msgid "See also the Premium version from our shop."
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:93
msgid "Add exclusion rule"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:89
msgid "at the end of their names"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:88
msgid "anywhere in their names"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:87
msgid "at the beginning of their names"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:85
msgid "these characters"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:83
msgid "All files/directories containing "
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:76
msgid "Select the folder in which the files or sub-directories you would like to exclude are located"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:71
msgid "All files/directories containing the given characters in their names"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:18
msgid "Files/Directories containing the given characters in their names"
msgstr ""

#: src/templates/wp-admin/notices/horizontal-notice.php:25
msgid "Never"
msgstr ""

#: src/templates/wp-admin/notices/horizontal-notice.php:22
msgid "Maybe later"
msgstr ""

#: src/templates/wp-admin/notices/horizontal-notice.php:18
msgid "Ok, you deserve it"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:69
msgid "Current SQL mode:"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:42
msgid "UpdraftClone image:"
msgstr ""

#: src/options.php:229
msgid "WordPress Multisite is supported, with extra features, by UpdraftPlus Premium."
msgstr ""

#: src/methods/s3generic.php:206
msgid "Virtual-host style"
msgstr ""

#: src/methods/s3generic.php:205
msgid "Path style"
msgstr ""

#: src/methods/s3generic.php:203
msgid "Read more about bucket access style"
msgstr ""

#: src/methods/s3generic.php:202
msgid "Bucket access style"
msgstr ""

#: src/methods/s3.php:1397
msgid "Failure: No endpoint details were given."
msgstr ""

#: src/methods/ftp.php:120
msgid "If you want encryption (e.g. you are storing sensitive business data), then an add-on is available in the Premium version."
msgstr ""

#: src/methods/email.php:111
msgid "For more options, use Premium"
msgstr ""

#: src/methods/dropbox.php:604
msgid "If you backup several sites into the same Dropbox and want to organize with sub-folders, then %scheck out Premium%s"
msgstr ""

#: src/methods/backup-module.php:745
msgid "Complete manual authentication"
msgstr ""

#: src/methods/backup-module.php:743
msgid "%s authentication code:"
msgstr ""

#: src/methods/backup-module.php:742
msgid "To complete manual authentication, at the orange UpdraftPlus authentication screen select the \"Having problems authenticating?\" link, then copy and paste the code given here."
msgstr ""

#: src/methods/backup-module.php:741
msgid "If you are having problems authenticating with %s you can manually authorize here."
msgstr ""

#: src/methods/backup-module.php:740
msgid "%s authentication:"
msgstr ""

#: src/includes/updraftplus-notices.php:274
msgid "The UpdraftPlus Plugin Collection Sale"
msgstr ""

#: src/includes/updraftplus-notices.php:200
msgid "The WordPress subscription extension for WooCommerce store owners."
msgstr ""

#: src/includes/updraftplus-notices.php:199
msgid "by"
msgstr ""

#: src/includes/updraftplus-notices.php:122
msgid "Team Updraft"
msgstr ""

#: src/includes/updraftplus-notices.php:122
msgid "Thank you so much!"
msgstr ""

#: src/includes/updraftclone/temporary-clone-dash-notice.php:52
msgid "Warning: You have no clone tokens remaining and either no subscriptions or no subscription that will renew before the clone expiry date."
msgstr ""

#: src/includes/class-remote-send.php:728
msgid "The list of existing sites has been removed"
msgstr ""

#: src/includes/class-remote-send.php:724
msgid "There was an error while trying to remove the list of existing sites."
msgstr ""

#: src/includes/class-remote-send.php:672
msgid "Clear list of existing sites"
msgstr ""

#: src/includes/class-commands.php:806
msgid "Missing instance id:"
msgstr ""

#: src/includes/class-commands.php:795
msgid "Missing authentication data:"
msgstr ""

#: src/includes/class-commands.php:766
msgid "Manual authentication is not available for this remote storage method"
msgstr ""

#: src/includes/class-backup-history.php:133
msgid "Or, if they are in remote storage, you can connect that remote storage (in the \"Settings\" tab), save your settings, and use the \"Rescan remote storage\" link."
msgstr ""

#: src/includes/class-backup-history.php:133
msgid "If you have an existing backup that you wish to upload and restore from, then please use the \"Upload backup files\" link above."
msgstr ""

#: src/class-updraftplus.php:5522
msgid "Include all tables not listed below"
msgstr ""

#: src/class-updraftplus.php:5506
msgid "Therefore, affected tables on the current site which already exist will not be replaced by default, to avoid corrupting them (you can review this in the list of tables below)."
msgstr ""

#: src/class-updraftplus.php:5504
msgid "Therefore it is advised that you take a fresh backup on the source site, using a later version."
msgstr ""

#: src/class-updraftplus.php:5506, src/class-updraftplus.php:5504
msgid "This backup was created on a previous UpdraftPlus version (%s) which did not correctly backup tables with composite primary keys (such as the term_relationships table, which records tags and product attributes)."
msgstr ""

#: src/class-updraftplus.php:5456
msgid "This backup is of a site with an empty table prefix, which WordPress does not officially support; the results may be unreliable."
msgstr ""

#: src/class-updraftplus.php:4033
msgid "View log"
msgstr ""

#: src/class-updraftplus.php:4027
msgid "You can view the log by pressing the 'View log' button."
msgstr ""

#: src/class-updraftplus.php:3986
msgid "UpdraftPlus on %s"
msgstr ""

#: src/class-updraftplus.php:3294
msgid " Your hosting provider only allows you to take one incremental backup per day."
msgstr ""

#: src/backup.php:4042
msgid "two unsuccessful attempts were made to include it, and it will now be omitted from the backup"
msgstr ""

#: src/backup.php:4038
msgid "a second attempt is being made (upon further failure it will be skipped)"
msgstr ""

#: src/backup.php:1850
msgid "Failed to backup database table:"
msgstr ""

#: src/backup.php:1695
msgid "Failed to open directory for reading:"
msgstr ""

#: src/admin.php:6378
msgid "The download link is broken or the backup file is no longer available"
msgstr ""

#: src/admin.php:6376
msgid "The download link is broken, you may have clicked the link from untrusted source"
msgstr ""

#: src/admin.php:6361
msgid "Due to the restriction, some settings can be automatically adjusted, disabled or not available."
msgstr ""

#: src/admin.php:6359
msgid "Your website is hosted with %s (%s)."
msgstr ""

#: src/admin.php:6293
msgid "The following remote storage (%s) have only been partially configured, if you are having problems you can try to manually authorise at the UpdraftPlus settings page."
msgstr ""

#: src/admin.php:6287
msgid "The following remote storage (%s) have only been partially configured, manual authorization is not supported with this remote storage, please try again and if the problem persists contact support."
msgstr ""

#: src/admin.php:6151
msgid "more info"
msgstr ""

#: src/admin.php:5537
msgid "The following remote storage options are configured."
msgstr ""

#: src/admin.php:5534
msgid "No remote storage locations with valid options found."
msgstr ""

#: src/admin.php:5123
msgid "This may prevent the restore procedure from being able to proceed."
msgstr ""

#: src/admin.php:4330
msgid "(the asterisk character matches zero or more characters)"
msgstr ""

#: src/admin.php:1491, src/admin.php:1498
msgid "UpdraftPlus notice"
msgstr ""

#: src/admin.php:1299
msgid "Premium / Pro Support"
msgstr ""

#: src/admin.php:1153
msgid "The number of restore options that will be sent exceeds the configured maximum in your PHP configuration (max_input_vars)."
msgstr ""

#: src/admin.php:1148
msgid "is not"
msgstr ""

#: src/admin.php:1144
msgid "is"
msgstr ""

#: src/admin.php:1138
msgid "Day of the month"
msgstr ""

#: src/admin.php:1134
msgid "Day of the week"
msgstr ""

#: src/admin.php:1128
msgid "if all of the following conditions are matched:"
msgstr ""

#: src/admin.php:1124
msgid "if any of the following conditions are matched:"
msgstr ""

#: src/admin.php:1120
msgid "on every backup"
msgstr ""

#: src/admin.php:1114
msgid "Your hosting provider only allows you to take one incremental backup per day."
msgstr ""

#: src/admin.php:1114, src/class-updraftplus.php:3294
msgid "You have reached the daily limit for the number of incremental backups you can create at this time."
msgstr ""

#: src/admin.php:1113, src/admin.php:1114, src/class-updraftplus.php:3297,
#: src/class-updraftplus.php:3294
msgid "Please contact your hosting company (%s) if you require further support."
msgstr ""

#: src/admin.php:1113, src/class-updraftplus.php:3297
msgid "Your hosting provider only allows you to take one backup per month."
msgstr ""

#: src/admin.php:1113, src/class-updraftplus.php:3297
msgid "You have reached the monthly limit for the number of backups you can create at this time."
msgstr ""

#: src/admin.php:1102
msgid "Restoring stored routine: %s"
msgstr ""

#: src/admin.php:1082
msgid "Please enter part of the file name"
msgstr ""

#: src/admin.php:1013
msgid "Loading..."
msgstr ""

#: src/admin.php:969
msgid "Exit full-screen"
msgstr ""

#: src/admin.php:968, src/admin.php:5155
msgid "Full-screen"
msgstr ""

#: src/admin.php:920
msgid "You have chosen to send this backup to remote storage, but no remote storage locations have been selected"
msgstr ""

#: src/addons/wp-cli.php:1085
msgid "You successfully logged in to UpdraftPlus.Com and connected this plugin with your account"
msgstr ""

#: src/addons/wp-cli.php:1070
msgid "Please wait while connecting to UpdraftPlus.com ..."
msgstr ""

#: src/addons/wp-cli.php:1065
msgid "The attempt to open and read contents from the password file failed; please make sure the file is readable and is not being exclusively locked by another process"
msgstr ""

#: src/addons/wp-cli.php:1062
msgid "The password file you specified doesn't exist; please check the --password-file parameter."
msgstr ""

#: src/addons/wp-cli.php:1059
msgid "The email address provided appears to be invalid, please double-check your email address again and try again."
msgstr ""

#: src/addons/wp-cli.php:654
msgid "Failed to search and replace"
msgstr ""

#: src/addons/wp-cli.php:652
msgid "Search and replace successful"
msgstr ""

#: src/addons/wp-cli.php:650
msgid "Failed to search and replace:"
msgstr ""

#: src/addons/wp-cli.php:599
msgid "Failed to create UpdraftCentral key"
msgstr ""

#: src/addons/wp-cli.php:596
msgid "UpdraftCentral key created:"
msgstr ""

#: src/addons/s3-enhanced.php:439
msgid "China (Ningxia) (restricted)"
msgstr ""

#: src/addons/s3-enhanced.php:437
msgid "Asia Pacific (Hong Kong)"
msgstr ""

#: src/addons/s3-enhanced.php:436
msgid "Asia Pacific (Osaka-Local) (restricted)"
msgstr ""

#: src/addons/s3-enhanced.php:430
msgid "Africa (Cape Town)"
msgstr ""

#: src/addons/s3-enhanced.php:427
msgid "Europe (Milan)"
msgstr ""

#: src/addons/s3-enhanced.php:417
msgid "US East (N. Virginia) (default)"
msgstr ""

#: src/admin.php:911, src/addons/reporting.php:549
msgid "When email storage method is enabled, and an email address is entered, also send the backup"
msgstr ""

#: src/addons/reporting.php:462
msgid "Send reports"
msgstr ""

#: src/addons/morestorage.php:105
msgid "Send scheduled backups to this destination:"
msgstr ""

#: src/addons/moredatabase.php:461, src/addons/morefiles.php:902
msgid "Deselect all"
msgstr ""

#: src/addons/moredatabase.php:460
msgid "Select all (this site)"
msgstr ""

#: src/addons/googlecloud.php:1146
msgid "The specified bucket was not found."
msgstr ""

#: src/addons/anonymisation.php:44, src/addons/anonymisation.php:68,
#: src/addons/anonymisation.php:96,
#: src/templates/wp-admin/notices/horizontal-notice.php:70
msgid "Learn more"
msgstr ""

#: src/addons/anonymisation.php:44, src/addons/anonymisation.php:68,
#: src/addons/anonymisation.php:96
msgid "Anonymize personal data for all users except staff"
msgstr ""

#: src/addons/anonymisation.php:42, src/addons/anonymisation.php:66,
#: src/addons/anonymisation.php:91
msgid "Anonymize personal data for all users except the logged-in user"
msgstr ""

#: src/addons/anonymisation.php:39, src/addons/anonymisation.php:64
msgid "N.B. Anonymized information cannot be recovered; the original non-anonymized data will be absent from the backup."
msgstr ""

#: src/addons/anonymisation.php:39, src/addons/anonymisation.php:64
msgid "These options can anonymize personal data in your database backup."
msgstr ""

#: src/udaddons/options.php:543
msgid "Buy %s"
msgstr ""

#: src/udaddons/options.php:542
msgid "Get %s from the UpdraftPlus.com Store"
msgstr ""

#: src/udaddons/options.php:295
msgid "Go to your UpdraftCentral Cloud dashboard"
msgstr ""

#: src/udaddons/options.php:294
msgid "You successfully logged in to UpdraftPlus and connected this site to UpdraftCentral Cloud."
msgstr ""

#: src/templates/wp-admin/settings/take-backup.php:90
msgid "Perform a backup"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:324
msgid "More great plugins by the Updraft Team"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:128
msgid "Allows you to only backup changes to your files (such as a new image) that have been made to your site since the last backup."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:126,
#: src/templates/wp-admin/settings/tab-addons.php:127
msgid "Incremental backups"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:57
msgid "Follow this link to the installation instructions (particularly step 1)."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:56
msgid "You successfully purchased UpdraftPremium."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:39
msgid "Goes to updraftplus.com checkout page"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:38
msgid "Goes to the updraftplus.com checkout page"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:38
msgid "Get %s here"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:326
msgid "The higher the value, the more server resources are required to create the archive."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:88
msgid "to take incremental backups"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:76
msgid "Retain this many scheduled database backups"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:42
msgid "Retain this many scheduled file backups"
msgstr ""

#: src/templates/wp-admin/settings/file-backup-exclude.php:18
msgid "Add an exclusion rule for %s"
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:170
msgid "Use ctrl / cmd + press to select several items, or ctrl / cmd + shift + press to select all in between"
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:169
msgid "Deselect all backups"
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:168
msgid "Select all backups"
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:167
msgid "Delete selected backups"
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:158
msgid "Show all backups..."
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:158
msgid "Show more backups..."
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:63
msgid "Choose the components to restore:"
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:30
msgid "Restore files from"
msgstr ""

#: src/templates/wp-admin/settings/backupnow-modal.php:20,
#: src/templates/wp-admin/settings/backupnow-modal.php:67
msgid "Find out more about incremental backups here."
msgstr ""

#: src/templates/wp-admin/settings/backupnow-modal.php:17
msgid "Incremental backups are a feature of %s (upgrade by following this link)."
msgstr ""

#: src/templates/wp-admin/settings/backupnow-modal.php:13
msgid "Take an incremental backup"
msgstr ""

#: src/templates/wp-admin/settings/backupnow-modal.php:12
msgid "Take a new backup"
msgstr ""

#: src/restorer.php:4008
msgid "The Database connection has been closed and cannot be reopened."
msgstr ""

#: src/restorer.php:3661
msgid "Skipping table %s: already restored on a prior run; next table to restore: %s"
msgstr ""

#: src/restorer.php:3658
msgid "Skipping table %s: user has chosen not to restore this table"
msgstr ""

#: src/restorer.php:2557
msgid "Found and replaced existing table foreign key constraints as the table prefix has changed."
msgstr ""

#: src/restorer.php:2400
msgid "An error occurred while attempting to set a new value to the MySQL global log_bin_trust_function_creators variable %s"
msgstr ""

#: src/restorer.php:2393
msgid "An error occurred while attempting to retrieve the MySQL global log_bin_trust_function_creators variable %s"
msgstr ""

#: src/restorer.php:1255
msgid "The directory does not exist, and the attempt to create it failed"
msgstr ""

#: src/restorer.php:852
msgid "Could not delete old path."
msgstr ""

#: src/methods/updraftvault.php:527
msgid "Follow this link for help"
msgstr ""

#: src/methods/updraftvault.php:522
msgid "Please enter your %s password"
msgstr ""

#: src/methods/updraftvault.php:520
msgid "Please enter your %s email address"
msgstr ""

#: src/methods/updraftvault.php:491
msgid "Start Subscription"
msgstr ""

#: src/methods/updraftvault.php:492, src/methods/updraftvault.php:493,
#: src/methods/updraftvault.php:494
msgid "Start %s Subscription"
msgstr ""

#: src/methods/updraftvault.php:495
msgid "Start Trial"
msgstr ""

#: src/methods/updraftvault.php:496
msgid "Start %s Trial"
msgstr ""

#: src/methods/updraftvault.php:507, src/methods/updraftvault.php:508,
#: src/methods/updraftvault.php:509, src/methods/updraftvault.php:510
msgid "Start a %s UpdraftVault Subscription"
msgstr ""

#: src/methods/updraftvault.php:503
msgid "%s month %s trial"
msgstr ""

#: src/methods/updraftvault.php:498
msgid "with the option of"
msgstr ""

#: src/methods/updraftvault.php:486
msgid "Read more about %s here."
msgstr ""

#: src/methods/updraftvault.php:483
msgid "Connect to your %s account"
msgstr ""

#: src/methods/updraftvault.php:482
msgid "Already got space?"
msgstr ""

#: src/methods/updraftvault.php:480
msgid "Need to get space?"
msgstr ""

#: src/methods/s3.php:184, src/methods/s3.php:185, src/methods/s3.php:196,
#: src/methods/s3.php:197
msgid "Error: Failed to initialise"
msgstr ""

#: src/methods/ftp.php:184
msgid "upload failed"
msgstr ""

#: src/methods/ftp.php:157, src/methods/ftp.php:325
msgid "login failure"
msgstr ""

#: src/methods/cloudfiles.php:228, src/methods/cloudfiles.php:229
msgid "error - failed to upload file"
msgstr ""

#: src/methods/cloudfiles.php:221
msgid "error - failed to re-assemble chunks"
msgstr ""

#: src/methods/cloudfiles.php:113, src/methods/cloudfiles.php:358,
#: src/methods/cloudfiles.php:370
msgid "error - failed to create and access the container"
msgstr ""

#: src/methods/cloudfiles.php:105, src/methods/cloudfiles.php:109,
#: src/methods/cloudfiles.php:302, src/methods/cloudfiles.php:350,
#: src/methods/cloudfiles.php:354
msgid "authentication failed"
msgstr ""

#: src/includes/updraftplus-tour.php:189
msgid "Otherwise, you can try UpdraftVault for 1 month for only $1!"
msgstr ""

#: src/includes/updraftplus-tour.php:188
msgid "If you have a valid Premium license, you get 1GB of storage included."
msgstr ""

#: src/includes/updraftplus-tour.php:135
msgid "Try UpdraftVault for 1 month for only $1!"
msgstr ""

#: src/includes/updraftplus-notices.php:189
msgid "manages all your WordPress sites at once from one place"
msgstr ""

#: src/includes/updraftplus-notices.php:189
msgid "Many sites?"
msgstr ""

#: src/includes/updraftplus-notices.php:188
msgid "handles updates automatically as you want them"
msgstr ""

#: src/includes/updraftplus-notices.php:188
msgid "Save time"
msgstr ""

#: src/includes/updraftplus-notices.php:187
msgid "backs up automatically when you update plugins, themes or core"
msgstr ""

#: src/includes/updraftplus-notices.php:187
msgid "Be safe"
msgstr ""

#: src/includes/updraftplus-notices.php:186
msgid "Make updates easy with UpdraftPlus"
msgstr ""

#: src/includes/updraftplus-clone.php:107
msgid "Clone of %s"
msgstr ""

#: src/includes/updraftclone/temporary-clone-dash-notice.php:47
msgid "%d token"
msgid_plural "%d tokens"
msgstr[0] ""

#: src/includes/updraftclone/temporary-clone-dash-notice.php:36
msgid "%s from now"
msgstr ""

#: src/includes/class-wpadmin-commands.php:217
msgid "This backup set contains incremental backups of your files; please select the time you wish to restore your files to"
msgstr ""

#: src/includes/class-filesystem-functions.php:558
msgid "Unzip progress: %d out of %d files"
msgstr ""

#: src/includes/class-database-utility.php:675
msgid "An error occurred while attempting to retrieve the routine SQL/DDL statement (%s %s)"
msgstr ""

#: src/includes/class-database-utility.php:663
msgid "An error occurred while attempting to retrieve routine status (%s %s)"
msgstr ""

#: src/includes/class-database-utility.php:595
msgid "An error occurred while attempting to check the support of stored routines creation (%s %s)"
msgstr ""

#: src/class-updraftplus.php:5514
msgid "If you do not want to restore all your database tables, then choose some to exclude here."
msgstr ""

#: src/class-updraftplus.php:5209
msgid "You should only proceed if you have checked and are confident (or willing to risk) that your plugins/themes/etc are compatible with the new %s version."
msgstr ""

#: src/class-updraftplus.php:5209
msgid "This is older than the server which you are now restoring onto (version %s)."
msgstr ""

#: src/class-updraftplus.php:3781
msgid "Incomplete"
msgstr ""

#: src/class-updraftplus.php:2501
msgid "The backup is being aborted for a repeated failure to progress."
msgstr ""

#: src/class-updraftplus.php:698
msgid "Under Maintenance"
msgstr ""

#: src/admin.php:6304, src/admin.php:6302
msgid "You have requested saving to remote storage (%s), but without entering any settings for that storage."
msgstr ""

#: src/admin.php:6151
msgid "Clone package"
msgstr ""

#: src/admin.php:6138
msgid "An empty WordPress install"
msgstr ""

#: src/admin.php:6137
msgid "This current site"
msgstr ""

#: src/admin.php:6135
msgid "Clone:"
msgstr ""

#: src/admin.php:5161,
#: src/templates/wp-admin/settings/delete-and-restore-modals.php:96
msgid "Component selection"
msgstr ""

#: src/admin.php:5155
msgid "Activity log"
msgstr ""

#: src/admin.php:5148
msgid "Cleaning"
msgstr ""

#: src/admin.php:5139
msgid "Verifying"
msgstr ""

#: src/admin.php:5136
msgid "Restoration progress:"
msgstr ""

#: src/admin.php:5126,
#: src/templates/wp-admin/settings/delete-and-restore-modals.php:30
msgid "UpdraftPlus Restoration"
msgstr ""

#: src/admin.php:4607
msgid "(%d archive(s) in set, total %s)."
msgstr ""

#: src/admin.php:3429
msgid "Learn more about UpdraftCentral"
msgstr ""

#: src/admin.php:3429
msgid "Add this website to UpdraftCentral (remote, centralised control) - free for up to 5 sites."
msgstr ""

#: src/admin.php:2971, src/admin.php:3895, src/admin.php:5091,
#: src/admin.php:5080, src/admin.php:5068, src/admin.php:5334,
#: src/admin.php:6293, src/admin.php:6304
msgid "Return to UpdraftPlus configuration"
msgstr ""

#: src/admin.php:2465
msgid "No suitable backup set (that already contains a full backup of all the requested file component types) was found, to add increments to."
msgstr ""

#: src/includes/class-http-error-descriptions.php:72
msgid "Service Unavailable"
msgstr ""

#: src/admin.php:1110
msgid "Please check the error log for more details"
msgstr ""

#: src/admin.php:1109
msgid "Missing pages:"
msgstr ""

#: src/admin.php:1108
msgid "Restore error:"
msgstr ""

#: src/admin.php:1107
msgid "Attempts by the browser to contact the website failed."
msgstr ""

#: src/admin.php:1106
msgid "Preparing backup files"
msgstr ""

#: src/admin.php:1105
msgid "Downloading backup files if needed"
msgstr ""

#: src/admin.php:1104
msgid "Begun"
msgstr ""

#: src/admin.php:1103, src/admin.php:5149
msgid "Finished"
msgstr ""

#: src/admin.php:1100
msgid "Restoring %s1 files out of %s2"
msgstr ""

#: src/admin.php:1099
msgid "no recent activity; will offer resumption after: %d seconds"
msgstr ""

#: src/admin.php:1098
msgid "last activity: %d seconds ago"
msgstr ""

#: src/admin.php:1095
msgid "credentials"
msgstr ""

#: src/admin.php:1093
msgid "Try it - 1 month for $1!"
msgstr ""

#: src/admin.php:1089
msgid "Try UpdraftVault!"
msgstr ""

#: src/admin.php:1088
msgid "You have not selected a restore path for your chosen backups"
msgstr ""

#: src/admin.php:1086
msgid "File backup options"
msgstr ""

#: src/admin.php:1063
msgid "Verifying one-time password..."
msgstr ""

#: src/admin.php:1058
msgid "Login successful; reloading information."
msgstr ""

#: src/admin.php:938, src/admin.php:2294,
#: src/templates/wp-admin/settings/downloading-and-restoring.php:21,
#: src/templates/wp-admin/settings/tab-backups.php:21,
#: src/templates/wp-admin/settings/tab-backups.php:44
msgid "Existing backups"
msgstr ""

#: src/admin.php:664
msgid "Dismiss notice"
msgstr ""

#: src/admin.php:664
msgid "You can test running your site on a different PHP (or WordPress) version using UpdraftClone credits."
msgstr ""

#: src/admin.php:652
msgid "dismiss notice"
msgstr ""

#: src/admin.php:652
msgid "go here to learn more"
msgstr ""

#: src/admin.php:652
msgid "You can test upgrading your site on an instant copy using UpdraftClone credits"
msgstr ""

#: src/addons/wp-cli.php:1020
msgid "There are no incremental backup restore points available."
msgstr ""

#: src/addons/wp-cli.php:1015
msgid "Timestamp"
msgstr ""

#: src/addons/wp-cli.php:742
msgid "No valid components found, please select different components or a backup set with components that can be restored."
msgstr ""

#: src/addons/sftp.php:238, src/methods/cloudfiles.php:199,
#: src/methods/cloudfiles.php:157, src/methods/openstack-base.php:81
msgid "Error: Failed to upload"
msgstr ""

#: src/addons/s3-enhanced.php:429
msgid "Middle East (Bahrain)"
msgstr ""

#: src/addons/s3-enhanced.php:428
msgid "Europe (Stockholm)"
msgstr ""

#: src/addons/s3-enhanced.php:80
msgid "Read more about server-side encryption"
msgstr ""

#: src/addons/s3-enhanced.php:77
msgid "Intelligent Tiering"
msgstr ""

#: src/addons/morefiles.php:1010
msgid "Restore location found for:"
msgstr ""

#: src/addons/morefiles.php:1008
msgid "Restore location does not exist on the filesystem for:"
msgstr ""

#: src/addons/morefiles.php:836
msgid "Please select the more files backups that you wish to restore:"
msgstr ""

#: src/addons/moredatabase.php:267
msgid "If you enter a table prefix, then only tables that begin with this prefix will be backed up."
msgstr ""

#: src/addons/moredatabase.php:267, src/addons/moredatabase.php:267
msgid "Enter table prefix"
msgstr ""

#: src/addons/moredatabase.php:266
msgid "Enter database"
msgstr ""

#: src/addons/moredatabase.php:266
msgid "Enter database."
msgstr ""

#: src/addons/moredatabase.php:265
msgid "Enter password"
msgstr ""

#: src/addons/moredatabase.php:265
msgid "Enter password."
msgstr ""

#: src/addons/moredatabase.php:264
msgid "Enter username"
msgstr ""

#: src/addons/moredatabase.php:264
msgid "Enter username."
msgstr ""

#: src/addons/moredatabase.php:263
msgid "Enter host"
msgstr ""

#: src/addons/moredatabase.php:263
msgid "Enter host."
msgstr ""

#: src/addons/incremental.php:297
msgid "No incremental backup of your files is possible, as no suitable existing backup was found to add increments to."
msgstr ""

#: src/addons/incremental.php:295
msgid "N.B. No backup of your database will be taken in an incremental backup; if you want a database backup as well, then take that separately."
msgstr ""

#: src/addons/incremental.php:295
msgid "Files changed since the last backup will be added as a new increment in that backup set."
msgstr ""

#: src/addons/googlecloud.php:1315
msgid "Read more about bucket locations"
msgstr ""

#: src/addons/googlecloud.php:1311, src/addons/s3-enhanced.php:72
msgid "Read more about storage classes"
msgstr ""

#: src/addons/googlecloud.php:346, src/methods/googledrive.php:1459
msgid "download: failed: file not found"
msgstr ""

#: src/addons/googlecloud.php:223, src/addons/googlecloud.php:228,
#: src/methods/cloudfiles.php:140, src/methods/googledrive.php:1287,
#: src/methods/googledrive.php:1293
msgid "Error: Failed to open local file"
msgstr ""

#: src/addons/fixtime.php:570
msgid "Start time"
msgstr ""

#: src/addons/fixtime.php:552
msgid "Day to run backups"
msgstr ""

#: src/addons/fixtime.php:310
msgid "Add an additional file retention rule"
msgstr ""

#: src/addons/fixtime.php:305
msgid "Add an additional database retention rule"
msgstr ""

#: src/addons/backblaze.php:783
msgid "This is needed if, and only if, your application key was a bucket-specific application key (not a master key)"
msgstr ""

#: src/addons/backblaze.php:782
msgid "Bucket application key ID"
msgstr ""

#: src/addons/backblaze.php:778
msgid "Master Application Key ID"
msgstr ""

#: src/addons/backblaze.php:777
msgid "For help configuring %s, including screenshots, follow this link."
msgstr ""

#: src/addons/azure.php:706
msgid "Azure China"
msgstr ""

#: src/admin.php:6121
msgid "Clone region:"
msgstr ""

#: src/udaddons/updraftplus-addons.php:334,
#: src/udaddons/updraftplus-addons.php:348
msgid "go here"
msgstr ""

#: src/udaddons/updraftplus-addons.php:334,
#: src/udaddons/updraftplus-addons.php:348
msgid "If you have already renewed, then you need to allocate a licence to this site - %s"
msgstr ""

#: src/addons/onedrive.php:924
msgid "Authentication"
msgstr ""

#: src/admin.php:1051
msgid "You must select at least one remote storage destination to upload this backup set to."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:367
msgid "Read more about Easy Updates Manager"
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:73
msgid "You can find out more about clone keys here."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:62
msgid "Or, use an UpdraftClone key"
msgstr ""

#: src/restorer.php:3350
msgid "Found SET NAMES %s, but changing to %s as suggested by WPDB::determine_charset()."
msgstr ""

#: src/admin.php:1084
msgid "UpdraftClone key is required."
msgstr ""

#: src/admin.php:1073
msgid "The preparation of the clone data has been aborted."
msgstr ""

#: src/addons/azure.php:705
msgid "Azure Government"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:367
msgid "Ask WordPress to automatically update UpdraftPlus when it finds an available update."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:366
msgid "Automatic updates"
msgstr ""

#: src/restorer.php:2959, src/restorer.php:2908
msgid "Your database user does not have permission to drop tables"
msgstr ""

#: src/admin.php:3418
msgid "Ask WordPress to update UpdraftPlus automatically when an update is available"
msgstr ""

#: src/addons/googlecloud.php:1289
msgid "You must add the following as the authorized redirect URI (under \"More Options\") when asked"
msgstr ""

#: src/addons/googlecloud.php:62
msgid "Frankfurt"
msgstr ""

#: src/addons/googlecloud.php:61
msgid "London"
msgstr ""

#: src/addons/googlecloud.php:60
msgid "Belgium"
msgstr ""

#: src/addons/googlecloud.php:59
msgid "Sydney"
msgstr ""

#: src/addons/googlecloud.php:58
msgid "Singapore"
msgstr ""

#: src/addons/googlecloud.php:57
msgid "Tokyo"
msgstr ""

#: src/addons/googlecloud.php:56
msgid "Taiwan"
msgstr ""

#: src/addons/googlecloud.php:55
msgid "Oregon"
msgstr ""

#: src/addons/googlecloud.php:54
msgid "North Virginia"
msgstr ""

#: src/addons/googlecloud.php:53
msgid "South Carolina"
msgstr ""

#: src/addons/googlecloud.php:52
msgid "Iowa"
msgstr ""

#: src/templates/wp-admin/settings/file-backup-exclude.php:11
msgid "Confirm change"
msgstr ""

#: src/templates/wp-admin/settings/file-backup-exclude.php:18,
#: src/templates/wp-admin/settings/exclude-settings-modal/exclude-panel-submit.php:3
msgid "Add an exclusion rule"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:62
msgid "Type a file prefix"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:59,
#: src/templates/wp-admin/settings/exclude-modal.php:61
msgid "All files beginning with these characters"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:50
msgid "Type an extension like zip"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:31
msgid "Select a file/folder which you would like to exclude"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:15
msgid "All files beginning with given characters"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:12,
#: src/templates/wp-admin/settings/exclude-modal.php:47,
#: src/templates/wp-admin/settings/exclude-modal.php:49
msgid "All files with this extension"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:9,
#: src/templates/wp-admin/settings/exclude-modal.php:25
msgid "File/directory"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:6
msgid "Select a way to exclude files or directories from the backup"
msgstr ""

#: src/templates/wp-admin/settings/exclude-modal.php:2
msgid "Exclude files/directories"
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:420
msgid "To read FAQs/documentation about UpdraftClone, go here."
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:419
msgid "your UpdraftPlus.com account"
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:419
msgid "You can check the progress here or in %s"
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:419
msgid "Your UpdraftClone is still setting up."
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:376
msgid "%s archives remain"
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:376
msgid "The site data has all been received, and its import has begun."
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:367
msgid "WordPress installed; now awaiting the site data to be sent."
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:94
msgid "Clone ready"
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:86
msgid "Site data has been deployed"
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:84,
#: src/includes/updraftclone/temporary-clone-status.php:347
msgid "Deploying site data"
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:75
msgid "Site data received"
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:73,
#: src/includes/updraftclone/temporary-clone-status.php:344
msgid "Receiving site data"
msgstr ""

#: src/includes/updraftclone/temporary-clone-status.php:66,
#: src/includes/updraftclone/temporary-clone-status.php:341
msgid "WordPress installed"
msgstr ""

#: src/admin.php:6226
msgid "Your clone has started, network information is not yet available but will be displayed here and at your updraftplus.com account once it is ready."
msgstr ""

#: src/admin.php:4330
msgid "Exclude these from"
msgstr ""

#: src/admin.php:1083
msgid "The exclusion rule which you are trying to add already exists"
msgstr ""

#: src/admin.php:1081
msgid "Please enter a valid file name prefix"
msgstr ""

#: src/admin.php:1080
msgid "Please enter characters that begin the filename which you would like to exclude"
msgstr ""

#: src/admin.php:1079
msgid "Please enter a valid file extension"
msgstr ""

#: src/admin.php:1078
msgid "Please enter a file extension, like zip"
msgstr ""

#: src/admin.php:1076
msgid "Please select a file/folder which you would like to exclude"
msgstr ""

#: src/admin.php:1075
msgid "Are you sure you want to remove this exclusion rule?"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:122
msgid "log results to console"
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:46
msgid "To create a temporary clone you need credit in your account."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:27
msgid "Read FAQs here."
msgstr ""

#: src/methods/dropbox.php:351, src/methods/dropbox.php:336
msgid "failed to upload file to %s (see log file for more)"
msgstr ""

#: src/admin.php:6222
msgid "Dashboard:"
msgstr ""

#: src/admin.php:6221
msgid "Front page:"
msgstr ""

#: src/admin.php:6220
msgid "Your clone has started and will be available at the following URLs once it is ready."
msgstr ""

#: src/includes/class-commands.php:1093
msgid "manage"
msgstr ""

#: src/includes/class-commands.php:1093
msgid "Current clones"
msgstr ""

#: src/class-updraftplus.php:3679
msgid "Your clone will now deploy this data to re-create your site."
msgstr ""

#: src/addons/migrator.php:541
msgid "Site key"
msgstr ""

#: src/includes/class-remote-send.php:667
msgid "Add a site"
msgstr ""

#: src/admin.php:1159, src/addons/migrator.php:206,
#: src/addons/migrator.php:528, src/addons/migrator.php:548
msgid "back"
msgstr ""

#: src/addons/migrator.php:172
msgid "Read this article to see step-by-step how it's done."
msgstr ""

#: src/addons/migrator.php:166,
#: src/templates/wp-admin/settings/migrator-no-migrator.php:6
msgid "Migrate (create a copy of a site on hosting you control)"
msgstr ""

#: src/includes/updraftclone/temporary-clone-dash-notice.php:47
msgid "Manage your clones"
msgstr ""

#: src/methods/dreamobjects.php:28
msgid "Closing 1st October 2018"
msgstr ""

#: src/includes/updraftclone/temporary-clone-dash-notice.php:46
msgid "Your clone will renew on:"
msgstr ""

#: src/includes/updraftclone/temporary-clone-dash-notice.php:32
msgid "Unable to get renew date"
msgstr ""

#: src/admin.php:1029
msgid "The backup was aborted"
msgstr ""

#: src/addons/onedrive.php:1326
msgid "OneDrive Germany"
msgstr ""

#: src/addons/onedrive.php:1325
msgid "OneDrive International"
msgstr ""

#: src/addons/onedrive.php:1323
msgid "Account type"
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:61,
#: src/templates/wp-admin/settings/temporary-clone.php:81
msgid "I accept the UpdraftClone terms and conditions"
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:61
msgid "Not got an account? Get one by buying some tokens here."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:27,
#: src/templates/wp-admin/settings/temporary-clone.php:46,
#: src/templates/wp-admin/settings/temporary-clone.php:59
msgid "You can buy UpdraftClone tokens from our shop, here."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:59
msgid "To create a temporary clone you need: 1) credit in your account and 2) to connect to your account, below."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:37
msgid "If you want, test upgrading to a different PHP or WP version."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:37
msgid "Flexible"
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:36
msgid "Takes just the time needed to create a backup and send it."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:36
msgid "Fast"
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:35
msgid "One VPS (Virtual Private Server) per clone, shared with nobody."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:35
msgid "Secure"
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:34
msgid "Runs on capacity from a leading cloud computing provider."
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:34
msgid "Reliable"
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:33
msgid "Easy"
msgstr ""

#: src/templates/wp-admin/settings/temporary-clone.php:15,
#: src/templates/wp-admin/settings/temporary-clone.php:44
msgid "Create a temporary clone on our servers (UpdraftClone)"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:30
msgid "WooCommerce plugins"
msgstr ""

#: src/templates/wp-admin/advanced/wipe-settings.php:14
msgid "Reset tour"
msgstr ""

#: src/templates/wp-admin/advanced/wipe-settings.php:13
msgid "Press this button to take a tour of the plugin."
msgstr ""

#: src/includes/updraftplus-tour.php:256
msgid "Take Tour"
msgstr ""

#: src/includes/updraftplus-tour.php:206
msgid "Log in here to enable all the features you have access to."
msgstr ""

#: src/includes/updraftplus-tour.php:205
msgid "Connect to updraftplus.com"
msgstr ""

#: src/includes/updraftplus-tour.php:169
msgctxt "Translators: UpdraftVault is a product name and should not be translated."
msgid "To get started with UpdraftVault, select one of the options below:"
msgstr ""

#: src/includes/updraftplus-tour.php:165,
#: src/includes/updraftplus-tour.php:209, src/includes/updraftplus-tour.php:198
msgid "Finish"
msgstr ""

#: src/includes/updraftplus-tour.php:162
msgid "UpdraftPlus Premium has many more exciting features!"
msgstr ""

#: src/includes/updraftplus-tour.php:161
msgid "UpdraftPlus Premium and addons"
msgstr ""

#: src/includes/updraftplus-tour.php:159,
#: src/includes/updraftplus-tour.php:203, src/includes/updraftplus-tour.php:196
msgid "Thank you for taking the tour."
msgstr ""

#: src/includes/updraftplus-tour.php:154
msgid "Do you have a few more WordPress sites you want to backup? If yes you can save hours by controlling all your backups in one place from UpdraftCentral."
msgstr ""

#: src/includes/updraftplus-tour.php:153
msgid "Control all your backups in one place"
msgstr ""

#: src/includes/updraftplus-tour.php:148
msgid "Congratulations, your settings have successfully been saved."
msgstr ""

#: src/includes/updraftplus-tour.php:144
msgid "Press here to save your settings."
msgstr ""

#: src/includes/updraftplus-tour.php:143, src/includes/updraftplus-tour.php:147
msgid "Save"
msgstr ""

#: src/includes/updraftplus-tour.php:140
msgid "Look through the other settings here, making any changes you’d like."
msgstr ""

#: src/includes/updraftplus-tour.php:139
msgid "More settings"
msgstr ""

#: src/admin.php:1091, src/includes/updraftplus-tour.php:134,
#: src/includes/updraftplus-tour.php:162,
#: src/includes/updraftplus-tour.php:186,
#: src/templates/wp-admin/settings/temporary-clone.php:27
msgid "Find out more here."
msgstr ""

#: src/admin.php:1090, src/includes/updraftplus-tour.php:133,
#: src/includes/updraftplus-tour.php:185
msgid "UpdraftVault is our remote storage which works seamlessly with UpdraftPlus."
msgstr ""

#: src/includes/updraftplus-tour.php:126
msgid "Choose the schedule that you want your backups to run on."
msgstr ""

#: src/includes/updraftplus-tour.php:125
msgid "Choose your backup schedule"
msgstr ""

#: src/includes/updraftplus-tour.php:121
msgid "Congratulations! Your first backup is running."
msgstr ""

#: src/includes/updraftplus-tour.php:117, src/includes/updraftplus-tour.php:122
msgid "Go to settings"
msgstr ""

#: src/includes/updraftplus-tour.php:116, src/includes/updraftplus-tour.php:121
msgctxt "Translators: %s is a bold tag."
msgid "But to avoid server-wide threats backup regularly to remote cloud storage in %s settings %s"
msgstr ""

#: src/includes/updraftplus-tour.php:116
msgid "Press here to run a manual backup."
msgstr ""

#: src/includes/updraftplus-tour.php:115, src/includes/updraftplus-tour.php:120
msgid "Creating your first backup"
msgstr ""

#: src/includes/updraftplus-tour.php:112
msgid "Select what you want to backup"
msgstr ""

#: src/includes/updraftplus-tour.php:111
msgid "Manual backup options"
msgstr ""

#: src/includes/updraftplus-tour.php:108
msgctxt "updraftplus"
msgid "To make a simple backup to your server, press this button. Or to setup regular backups and remote storage, go to %s settings %s"
msgstr ""

#: src/includes/updraftplus-tour.php:107
msgid "Your first backup"
msgstr ""

#: src/includes/updraftplus-tour.php:103
msgid "Press here to start!"
msgstr ""

#: src/includes/updraftplus-tour.php:100
msgid "the world’s most trusted backup plugin!"
msgstr ""

#: src/includes/updraftplus-tour.php:100
msgid "Welcome to UpdraftPlus"
msgstr ""

#: src/includes/updraftplus-tour.php:99
msgid "UpdraftPlus settings"
msgstr ""

#: src/includes/updraftplus-tour.php:96
msgid "End tour"
msgstr ""

#: src/includes/updraftplus-tour.php:95
msgid "Skip this step"
msgstr ""

#: src/includes/updraftplus-tour.php:94
msgid "Back"
msgstr ""

#: src/includes/class-commands.php:1074
msgid "You can buy more temporary clone tokens here."
msgstr ""

#: src/admin.php:6174
msgid "Forbid non-administrators to login to WordPress on your clone"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:299
msgid "Premium / Find out more"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:29
msgid "Other great plugins"
msgstr ""

#: src/includes/class-commands.php:1144
msgid "The creation of your data for creating the clone should now begin."
msgstr ""

#: src/admin.php:6227, src/admin.php:6224
msgid "You can find your temporary clone information in your updraftplus.com account here."
msgstr ""

#: src/class-updraftplus.php:5415
msgid "Choose a default for each table"
msgstr ""

#: src/admin.php:3735
msgid "Sending files to remote site"
msgstr ""

#: src/admin.php:3729
msgid "Clone server being provisioned and booted (can take several minutes)"
msgstr ""

#: src/addons/migrator.php:210
msgid "To import a backup set, go to the \"Existing backups\" section in the \"Backup/Restore\" tab"
msgstr ""

#: src/admin.php:3210
msgid "Backup / Restore"
msgstr ""

#: src/admin.php:740, src/admin.php:5126
msgid "Backup"
msgstr ""

#: src/addons/wp-cli.php:413
msgid "Latest full backup found; identifier:"
msgstr ""

#: src/addons/wp-cli.php:412
msgid "No previous full backup found."
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:92
msgid "Remote storage: %s"
msgstr ""

#: src/addons/wp-cli.php:102
msgid "No previous backup found to add an increment to."
msgstr ""

#: src/restorer.php:3356
msgid "Requested character set (%s) is not present - changing to %s."
msgstr ""

#: src/includes/updraftclone/temporary-clone-user-notice.php:32
msgid "Allow only administrators to log in"
msgstr ""

#: src/includes/updraftclone/temporary-clone-user-notice.php:31
msgid "You can forbid non-admins logins to this cloned site by checking the checkbox below"
msgstr ""

#: src/includes/updraftclone/temporary-clone-user-notice.php:30
msgid "UpdraftPlus temporary clone user login settings:"
msgstr ""

#: src/includes/updraftclone/temporary-clone-dash-notice.php:44
msgid "Welcome to your UpdraftClone (temporary clone)"
msgstr ""

#: src/includes/updraftclone/temporary-clone-dash-notice.php:43
msgid "Refresh connection"
msgstr ""

#: src/addons/reporting.php:517
msgid "Log all messages to syslog"
msgstr ""

#: src/addons/sftp.php:717, src/addons/sftp.php:718
msgid "The server's RSA key %s fingerprint: %s."
msgstr ""

#: src/addons/sftp.php:614
msgid "RSA fingerprint"
msgstr ""

#: src/addons/sftp.php:156
msgid "Fingerprints don't match."
msgstr ""

#: src/templates/wp-admin/settings/migrator-no-migrator.php:17
msgid "More information here."
msgstr ""

#: src/admin.php:748, src/admin.php:3211
msgid "Migrate / Clone"
msgstr ""

#: src/admin.php:4467, src/templates/wp-admin/settings/backupnow-modal.php:60,
#: src/templates/wp-admin/settings/existing-backups-table.php:79,
#: src/templates/wp-admin/settings/existing-backups-table.php:76
msgid "Only allow this backup to be deleted manually (i.e. keep it even if retention limits are hit)."
msgstr ""

#: src/restorer.php:380
msgid "Your WordPress install has old directories from its state before you restored/migrated (technical information: these are suffixed with -old)."
msgstr ""

#: src/addons/wp-cli.php:850, src/addons/wp-cli.php:846
msgid "This is not an incremental backup"
msgstr ""

#: src/addons/wp-cli.php:762
msgid "Run this command to see the log file for this restoration (needed for any support requests)."
msgstr ""

#: src/admin.php:6156, src/admin.php:6200
msgid "(current version)"
msgstr ""

#: src/admin.php:4235
msgid "press here"
msgstr ""

#: src/addons/onedrive.php:1312, src/addons/pcloud.php:548,
#: src/methods/dropbox.php:601, src/methods/googledrive.php:1503
msgid "this privacy policy"
msgstr ""

#: src/addons/onedrive.php:1312, src/addons/pcloud.php:548,
#: src/methods/dropbox.php:601, src/methods/googledrive.php:1503
msgid "Please read %s for use of our %s authorization app (none of your backup data is sent to us)."
msgstr ""

#: src/addons/incremental.php:378
msgid "Tell me more"
msgstr ""

#: src/addons/incremental.php:366
msgid "And then add an incremental backup"
msgstr ""

#: src/updraftplus.php:126, src/addons/incremental.php:342
msgid "Every hour"
msgstr ""

#: src/includes/class-commands.php:1086
msgid "Create clone"
msgstr ""

#: src/includes/class-commands.php:1073, src/includes/class-commands.php:1130
msgid "Available temporary clone tokens:"
msgstr ""

#: src/admin.php:3342, src/includes/class-commands.php:1087,
#: src/includes/class-commands.php:1144, src/includes/class-commands.php:1142,
#: src/methods/backup-module.php:746,
#: src/templates/wp-admin/settings/temporary-clone.php:88,
#: src/templates/wp-admin/settings/updraftcentral-connect.php:71
msgid "Processing"
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:70
msgid "Connect to UpdraftCentral Cloud"
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:68
msgid "UpdraftPlus.Com account terms and policies"
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:68
msgid "I consent to %s"
msgstr ""

#: src/admin.php:3455,
#: src/templates/wp-admin/settings/updraftcentral-connect.php:56
msgid "One Time Password (check your OTP app to get this password)"
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:46
msgid "Login or register with this email address"
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:34
msgid "If not, then choose your details and a new account will be registered."
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:33
msgid "If you already have an updraftplus.com account, then enter the details below."
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:31
msgid "Add this website to your UpdraftCentral Cloud dashboard at updraftplus.com."
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:29
msgid "Login or register for UpdraftCentral Cloud"
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:20
msgid "Go here to connect it."
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:20
msgid "Or if you prefer to self-host, then you can get the self-hosted version here."
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:17
msgid "Connect this site to UpdraftCentral Cloud"
msgstr ""

#: src/templates/wp-admin/settings/updraftcentral-connect.php:12
msgid "Backup, update and manage all your WordPress sites from one dashboard"
msgstr ""

#: src/admin.php:6083
msgid "The file %s has a \"byte order mark\" (BOM) at its beginning."
msgid_plural "The files %s have a \"byte order mark\" (BOM) at their beginning."
msgstr[0] ""

#: src/admin.php:1069
msgid "For future control of all your UpdraftCentral connections, go to the \"Advanced Tools\" tab."
msgstr ""

#: src/admin.php:1068
msgid "You can also close this wizard."
msgstr ""

#: src/admin.php:1067
msgid "You need to read and accept the UpdraftCentral Cloud data and privacy policies before you can proceed."
msgstr ""

#: src/admin.php:1066
msgid "Please wait while you are redirected to UpdraftCentral Cloud."
msgstr ""

#: src/admin.php:1065
msgid "Please wait while the system generates and registers an encryption key for your website with UpdraftCentral Cloud."
msgstr ""

#: src/admin.php:1064
msgid "Perhaps you would want to login instead."
msgstr ""

#: src/admin.php:1062
msgid "Trouble connecting? Try using an alternative method in the advanced security options."
msgstr ""

#: src/admin.php:1061
msgid "An email is required and needs to be in a valid format."
msgstr ""

#: src/admin.php:1060
msgid "Both email and password fields are required."
msgstr ""

#: src/admin.php:1059
msgid "Registration successful."
msgstr ""

#: src/admin.php:1057, src/admin.php:1059
msgid "Please follow this link to open %s in a new window."
msgstr ""

#: src/admin.php:1057
msgid "Login successful."
msgstr ""

#: src/admin.php:1054,
#: src/templates/wp-admin/settings/updraftcentral-connect.php:9
msgid "UpdraftCentral Cloud"
msgstr ""

#: src/admin.php:591
msgid "Are you sure you want to dismiss all UpdraftPlus news forever?"
msgstr ""

#: src/admin.php:590
msgid "Dismiss all UpdraftPlus news"
msgstr ""

#: src/admin.php:589
msgid "UpdraftPlus News"
msgstr ""

#: src/addons/wp-cli.php:538
msgid "Migration key created:"
msgstr ""

#: src/addons/wp-cli.php:528, src/addons/wp-cli.php:570,
#: src/addons/wp-cli.php:630
msgid "Missing parameters"
msgstr ""

#: src/addons/azure.php:704
msgid "Azure Germany"
msgstr ""

#: src/addons/azure.php:703
msgid "Azure Global"
msgstr ""

#: src/addons/azure.php:701
msgid "Azure Account"
msgstr ""

#: src/admin.php:1053
msgid "Please specify the Microsoft OneDrive folder name, not the URL."
msgstr ""

#: src/templates/wp-admin/settings/upload-backups-modal.php:4
msgid "Select the remote storage destinations you want to upload this backup set to"
msgstr ""

#: src/templates/wp-admin/settings/upload-backups-modal.php:3
msgid "Upload backup"
msgstr ""

#: src/admin.php:4725
msgid "After pressing this button, you can select where to upload your backup from a list of your currently saved remote storage locations"
msgstr ""

#: src/admin.php:1052
msgid "(already uploaded)"
msgstr ""

#: src/admin.php:1050
msgid "Local backup upload has started; please check the log file to see the upload progress"
msgstr ""

#: src/admin.php:966, src/admin.php:4725
msgid "Upload"
msgstr ""

#: src/admin.php:914, src/addons/reporting.php:551
msgid "Only email the database backup"
msgstr ""

#: src/addons/reporting.php:551
msgid "Be aware that mail servers tend to have size limits; typically around %s MB; backups larger than any limits will likely not arrive as a result UpdraftPlus will only send Database backups to email."
msgstr ""

#: src/addons/reporting.php:551
msgid "Use this option to only send database backups when sending to email, and skip other components."
msgstr ""

#: src/addons/migrator.php:252
msgid "For incremental backups, you will be able to choose which increments to restore at a later stage."
msgstr ""

#: src/addons/incremental.php:93
msgid "Increments exist at: %s"
msgstr ""

#: src/addons/incremental.php:93, src/addons/incremental.php:91
msgid "(latest increment: %s)"
msgstr ""

#: src/addons/s3-enhanced.php:425
msgid "Europe (Paris)"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:206
msgid "WP-CLI commands to take, list and delete backups."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:205
msgid "WP-CLI support"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:204
msgid "WP CLI"
msgstr ""

#: src/addons/wp-cli.php:272
msgid "Invalid Job Id"
msgstr ""

#: src/addons/wp-cli.php:201
msgid "Recently started backup job id: %s"
msgstr ""

#: src/addons/wp-cli.php:92, src/addons/wp-cli.php:494,
#: src/addons/wp-cli.php:737, src/addons/wp-cli.php:826,
#: src/addons/wp-cli.php:849
msgid "The given value for the '%s' option is not valid"
msgstr ""

#: src/addons/migrator.php:537
msgid "So, to get the key for the remote site, open the 'Migrate Site' window on that site, and go to that section."
msgstr ""

#: src/addons/migrator.php:537
msgid "Keys for a site are created in the section \"receive a backup from a remote site\"."
msgstr ""

#: src/includes/class-remote-send.php:555
msgid "You must copy and paste this key on the sending site now - it cannot be shown again."
msgstr ""

#: src/templates/wp-admin/advanced/lock-admin.php:14
msgid "This functionality has been disabled by the site administrator."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:340,
#: src/templates/wp-admin/settings/tab-addons.php:341
msgid "Highly efficient way to manage, optimize, update and backup multiple websites from one place."
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:4
msgid "Thank you for installing UpdraftPlus!"
msgstr ""

#: src/includes/class-remote-send.php:684
msgid "No keys to allow remote sites to send backup data here have yet been created."
msgstr ""

#: src/restorer.php:860
msgid "Failed to read from the working directory."
msgstr ""

#: src/restorer.php:859
msgid "Failed to find a manifest file in the backup."
msgstr ""

#: src/restorer.php:858
msgid "Failed to read the manifest file from backup."
msgstr ""

#: src/addons/morefiles.php:82
msgid "(None configured)"
msgstr ""

#: src/addons/onedrive.php:1332, src/methods/backup-module.php:629,
#: src/methods/dropbox.php:609
msgid "Ensure you are logged into the correct account before continuing."
msgstr ""

#: src/admin.php:5724
msgid "Remote storage method and instance id are required for authentication."
msgstr ""

#: src/admin.php:5720
msgid "authentication error"
msgstr ""

#: src/options.php:48, src/addons/multisite.php:49
msgid "(Nothing has been logged yet)"
msgstr ""

#: src/includes/migrator-lite.php:345
msgid "you will want to use below search and replace site location in the database (migrate) to search/replace the site address."
msgstr ""

#: src/addons/morestorage.php:197
msgid "Add another %s account..."
msgstr ""

#: src/addons/morestorage.php:85
msgid "Delete these settings"
msgstr ""

#: src/admin.php:1049, src/addons/morestorage.php:83
msgid "Currently disabled"
msgstr ""

#: src/admin.php:1048, src/addons/morestorage.php:83
msgid "Currently enabled"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:48
msgid "If you have purchased from UpdraftPlus.Com, then follow this link to the installation instructions (particularly step 1)."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:48
msgid "You are currently using the free version of UpdraftPlus."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:38
msgid "Get it here"
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:90
msgid "remote site"
msgstr ""

#: src/addons/backblaze.php:558
msgid "Invalid bucket name"
msgstr ""

#: src/restorer.php:2601
msgid "Requested table collation (%1$s) is not present - changing to %2$s."
msgid_plural "Requested table collations (%1$s) are not present - changing to %2$s."
msgstr[0] ""

#: src/class-updraftplus.php:5392
msgid "Your chosen replacement collation"
msgstr ""

#: src/class-updraftplus.php:5369
msgid "You can choose another suitable collation instead and continue with the restoration (at your own risk)."
msgstr ""

#: src/class-updraftplus.php:5369
msgid "The database server that this WordPress site is running on doesn't support the collation (%s) used in the database which you are trying to import."
msgid_plural "The database server that this WordPress site is running on doesn't support multiple collations (%s) used in the database which you are trying to import."
msgstr[0] ""

#: src/includes/migrator-lite.php:371
msgid "Database restoration options:"
msgstr ""

#: src/includes/migrator-lite.php:312
msgid "This looks like a migration (the backup is from a site with a different address/URL, %s)."
msgstr ""

#: src/addons/azure.php:699
msgid "%s Prefix"
msgstr ""

#: src/addons/azure.php:696
msgid "%s Container"
msgstr ""

#: src/addons/azure.php:694
msgid "%s Key"
msgstr ""

#: src/addons/azure.php:692
msgid "%s Account Name"
msgstr ""

#: src/includes/migrator-lite.php:668
msgid "Your .htaccess has an old site reference on line number %s. You should remove it manually."
msgid_plural "Your .htaccess has an old site references on line numbers %s. You should remove them manually."
msgstr[0] ""

#: src/restorer.php:2542
msgid "Requested table character set (%s) is not present - changing to %s."
msgstr ""

#: src/class-updraftplus.php:5344
msgid "Your chosen character set to use instead:"
msgstr ""

#: src/class-updraftplus.php:5334
msgid "You can choose another suitable character set instead and continue with the restoration at your own risk."
msgstr ""

#: src/class-updraftplus.php:5334
msgid "The database server that this WordPress site is running on doesn't support the character set (%s) which you are trying to import."
msgid_plural "The database server that this WordPress site is running on doesn't support the character sets (%s) which you are trying to import."
msgstr[0] ""

#: src/includes/updraftplus-tour.php:93,
#: src/templates/wp-admin/settings/delete-and-restore-modals.php:100
msgid "Next"
msgstr ""

#: src/admin.php:1041
msgid "Please enter a valid URL e.g http://example.com"
msgstr ""

#: src/addons/backblaze.php:792
msgid "some/path"
msgstr ""

#: src/addons/backblaze.php:790
msgid "Bucket name"
msgstr ""

#: src/addons/backblaze.php:784
msgid "Backup path"
msgstr ""

#: src/addons/backblaze.php:780
msgid "Application key"
msgstr ""

#: src/addons/backblaze.php:779, src/addons/backblaze.php:779,
#: src/includes/updraftplus-notices.php:122
msgid "here"
msgstr ""

#: src/addons/backblaze.php:779
msgid "Get these settings from %s, or sign up %s."
msgstr ""

#: src/addons/backblaze.php:536
msgid "Account Key"
msgstr ""

#: src/addons/backblaze.php:535
msgid "Account ID"
msgstr ""

#: src/class-updraftplus.php:5159
msgid "This backup set is of this site, but at the time of the backup you were using %s, whereas the site now uses %s."
msgstr ""

#: src/methods/googledrive.php:1511
msgid "To de-authorize UpdraftPlus (all sites) from accessing your Google Drive, follow this link to your Google account settings."
msgstr ""

#: src/addons/googlecloud.php:1321, src/addons/onedrive.php:1330,
#: src/addons/pcloud.php:551, src/methods/backup-module.php:700,
#: src/methods/dropbox.php:608, src/methods/googledrive.php:1510
msgid "Follow this link to remove these settings for %s."
msgstr ""

#: src/backup.php:617, src/backup.php:2894, src/class-updraftplus.php:2565,
#: src/class-updraftplus.php:2632, src/restorer.php:713,
#: src/includes/class-search-replace.php:298,
#: src/includes/class-storage-methods-interface.php:387
msgid "A PHP fatal error (%s) has occurred: %s"
msgstr ""

#: src/backup.php:611, src/backup.php:2885, src/class-updraftplus.php:2556,
#: src/class-updraftplus.php:2625, src/restorer.php:701,
#: src/includes/class-search-replace.php:291,
#: src/includes/class-storage-methods-interface.php:378
msgid "A PHP exception (%s) has occurred: %s"
msgstr ""

#: src/addons/googlecloud.php:59
msgid "South-east Australia"
msgstr ""

#: src/addons/googlecloud.php:58
msgid "South-east Asia"
msgstr ""

#: src/addons/googlecloud.php:57
msgid "North-east Asia"
msgstr ""

#: src/templates/wp-admin/settings/take-backup.php:84
msgid "Remote storage authentication"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:178
msgid "Network and multisite"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:100
msgid "Migrator"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:230
msgid "Additional storage"
msgstr ""

#: src/includes/updraftplus-tour.php:129,
#: src/includes/updraftplus-tour.php:181,
#: src/templates/wp-admin/settings/tab-addons.php:87
msgid "Remote storage"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:192
msgid "Select Files"
msgstr ""

#: src/methods/cloudfiles.php:510
msgid "Cloud Files"
msgstr ""

#: src/methods/updraftvault.php:122,
#: src/templates/wp-admin/settings/tab-addons.php:295
msgid "UpdraftVault"
msgstr ""

#: src/includes/class-wpadmin-commands.php:637
msgid "archive"
msgstr ""

#: src/includes/class-wpadmin-commands.php:628
msgid "Extra database"
msgstr ""

#: src/admin.php:4606
msgid "Press here to download or browse"
msgstr ""

#: src/admin.php:1620, src/admin.php:1630
msgid "Error: invalid path"
msgstr ""

#: src/admin.php:1361
msgid "An error occurred when fetching storage module options: "
msgstr ""

#: src/admin.php:1038
msgid "Loading log file"
msgstr ""

#: src/admin.php:1036
msgid "Search"
msgstr ""

#: src/admin.php:1035
msgid "Select a file to view information about it"
msgstr ""

#: src/admin.php:1034
msgid "Browsing zip file"
msgstr ""

#: src/admin.php:1000
msgid "With UpdraftPlus Premium, you can directly download individual files from here."
msgstr ""

#: src/admin.php:944
msgid "Browse contents"
msgstr ""

#: src/restorer.php:3067
msgid "Skipped tables:"
msgstr ""

#: src/templates/wp-admin/settings/backupnow-modal.php:8
msgid "With UpdraftPlus Premium, you can choose to backup non-WordPress tables, backup only specified tables, and backup other databases too."
msgstr ""

#: src/templates/wp-admin/settings/backupnow-modal.php:8
msgid "All WordPress tables will be backed up."
msgstr ""

#: src/admin.php:1033
msgid "Further information may be found in the browser JavaScript console, and the server PHP error logs."
msgstr ""

#: src/admin.php:1033
msgid "That you are attempting to upload a zip file previously created by UpdraftPlus."
msgstr ""

#: src/admin.php:1033
msgid "The available memory on the server."
msgstr ""

#: src/admin.php:1033
msgid "Any settings in your .htaccess or web.config file that affects the maximum upload or post size."
msgstr ""

#: src/admin.php:1032
msgid "HTTP code:"
msgstr ""

#: src/admin.php:919, src/addons/wp-cli.php:111
msgid "You have chosen to backup a database, but no tables have been selected"
msgstr ""

#: src/addons/moredatabase.php:463
msgid "tables"
msgstr ""

#: src/addons/moredatabase.php:462
msgid "WordPress database"
msgstr ""

#: src/addons/moredatabase.php:427
msgid "You should backup all tables unless you are an expert in the internals of the WordPress database."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:329,
#: src/templates/wp-admin/settings/tab-addons.php:329,
#: src/templates/wp-admin/settings/tab-addons.php:335,
#: src/templates/wp-admin/settings/tab-addons.php:335,
#: src/templates/wp-admin/settings/tab-addons.php:341,
#: src/templates/wp-admin/settings/tab-addons.php:341,
#: src/templates/wp-admin/settings/tab-addons.php:347,
#: src/templates/wp-admin/settings/tab-addons.php:347
msgid "Find out more"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:298
msgid "UpdraftPlus has its own embedded storage option, providing a zero-hassle way to download, store and manage all your backups from one place."
msgstr ""

#: src/templates/wp-admin/advanced/wipe-settings.php:12
msgid "UpdraftPlus Tour"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:284
msgid "Lock access to UpdraftPlus via a password so you choose which admin users can access backups."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:271
msgid "Some backup plugins can't restore a backup, so Premium allows you to restore backups from other plugins."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:269,
#: src/templates/wp-admin/settings/tab-addons.php:270
msgid "Importer"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:258
msgid "Tidy things up for clients and remove all adverts for our other products."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:256,
#: src/templates/wp-admin/settings/tab-addons.php:257
msgid "No ads"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:245
msgid "Sophisticated reporting and emailing capabilities."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:219
msgid "Encrypt your sensitive databases (e.g. customer information or passwords); Backup external databases too."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:217,
#: src/templates/wp-admin/settings/tab-addons.php:218
msgid "More database options"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:193
msgid "Set exact times to create or delete backups."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:191,
#: src/templates/wp-admin/settings/tab-addons.php:192
msgid "Backup time and scheduling"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:180
msgid "Backup WordPress multisites (i.e, networks), securely."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:179
msgid "Network / multisite"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:167
msgid "Backup WordPress core and non-WP files and databases."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:154
msgid "Automatically backs up your website before any updates to plugins, themes and WordPress core."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:152,
#: src/templates/wp-admin/settings/tab-addons.php:153
msgid "Pre-update backups"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:141
msgid "Provides expert help and support from the developers whenever you need it."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:140
msgid "Fast, personal support"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:102
msgid "UpdraftPlus Migrator clones your WordPress site and moves it to a new domain directly and simply."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:101
msgid "Cloning and migration"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:232
msgid "Get enhanced versions of the free remote storage options (Dropbox, Google Drive & S3) and even more remote storage options like OneDrive, SFTP, Azure, WebDAV, Backblaze and more with UpdraftPlus Premium."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:231
msgid "Additional and enhanced remote storage locations"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:88
msgid "Backup to remote storage locations"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:82,
#: src/templates/wp-admin/settings/tab-addons.php:316
msgid "Upgrade now"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:79,
#: src/templates/wp-admin/settings/tab-addons.php:313
msgid "Installed"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:69
msgid "Free"
msgstr ""

#: src/admin.php:588
msgid "UpdraftPlus"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:216
msgid "Recommended: optimize your database with WP-Optimize."
msgstr ""

#: src/templates/wp-admin/notices/bottom-notice.php:31,
#: src/templates/wp-admin/notices/horizontal-notice.php:74,
#: src/templates/wp-admin/notices/report-plain.php:33,
#: src/templates/wp-admin/notices/report.php:30
msgid "Read more"
msgstr ""

#: src/includes/updraftplus-notices.php:210
msgid "After you've backed up your database, we recommend you install our WP-Optimize plugin to streamline it for better website performance."
msgstr ""

#: src/addons/morefiles.php:1057
msgid "Please choose a file or directory"
msgstr ""

#: src/addons/morefiles.php:1035, src/methods/backup-module.php:90
msgid "Confirm"
msgstr ""

#: src/addons/morefiles.php:1030, src/addons/morefiles.php:1030
msgid "Go up a directory"
msgstr ""

#: src/addons/morefiles.php:1027
msgid "Add directory..."
msgstr ""

#: src/addons/morefiles.php:334, src/addons/morefiles.php:1013,
#: src/addons/morefiles.php:1055,
#: src/templates/wp-admin/settings/file-backup-exclude.php:11
msgid "Edit"
msgstr ""

#: src/addons/morefiles.php:317
msgid "If using it, select a path from the directory tree below and then press confirm selection."
msgstr ""

#: src/addons/s3-enhanced.php:426
msgid "Europe (Frankfurt)"
msgstr ""

#: src/addons/s3-enhanced.php:424
msgid "Europe (London)"
msgstr ""

#: src/addons/s3-enhanced.php:423
msgid "Europe (Ireland)"
msgstr ""

#: src/includes/updraftplus-tour.php:151
msgid "UpdraftCentral"
msgstr ""

#: src/templates/wp-admin/notices/autobackup-notice.php:6,
#: src/templates/wp-admin/notices/horizontal-notice.php:38,
#: src/templates/wp-admin/notices/horizontal-notice.php:8
msgid "notice image"
msgstr ""

#: src/templates/wp-admin/notices/bottom-notice.php:29,
#: src/templates/wp-admin/notices/horizontal-notice.php:72,
#: src/templates/wp-admin/notices/report-plain.php:31,
#: src/templates/wp-admin/notices/report.php:28
msgid "Go there"
msgstr ""

#: src/templates/wp-admin/notices/bottom-notice.php:27,
#: src/templates/wp-admin/notices/horizontal-notice.php:68,
#: src/templates/wp-admin/notices/report-plain.php:29,
#: src/templates/wp-admin/notices/report.php:26
msgid "Sign up"
msgstr ""

#: src/templates/wp-admin/notices/bottom-notice.php:25,
#: src/templates/wp-admin/notices/horizontal-notice.php:66,
#: src/templates/wp-admin/notices/report-plain.php:27,
#: src/templates/wp-admin/notices/report.php:24
msgid "Get Premium"
msgstr ""

#: src/templates/wp-admin/notices/bottom-notice.php:23,
#: src/templates/wp-admin/notices/report-plain.php:25,
#: src/templates/wp-admin/notices/report.php:22
msgid "Review UpdraftPlus"
msgstr ""

#: src/templates/wp-admin/notices/bottom-notice.php:21,
#: src/templates/wp-admin/notices/horizontal-notice.php:64,
#: src/templates/wp-admin/notices/report-plain.php:23,
#: src/templates/wp-admin/notices/report.php:20
msgid "Get UpdraftCentral"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:109
msgid "Apache modules"
msgstr ""

#: src/includes/updraftplus-notices.php:166,
#: src/includes/updraftplus-notices.php:176
msgid "UpdraftPlus Blog - get up-to-date news and offers"
msgstr ""

#: src/includes/updraftplus-notices.php:155
msgid "UpdraftPlus Newsletter"
msgstr ""

#: src/includes/updraftplus-notices.php:114
msgid "Control all your WordPress installations from one place using UpdraftCentral remote site management!"
msgstr ""

#: src/includes/updraftplus-notices.php:113
msgid "Do you use UpdraftPlus on multiple sites?"
msgstr ""

#: src/includes/updraftplus-notices.php:104
msgid "UpdraftCentral is a highly efficient way to manage, update and backup multiple websites from one place."
msgstr ""

#: src/includes/updraftplus-notices.php:103
msgid "Introducing UpdraftCentral"
msgstr ""

#: src/includes/updraftplus-notices.php:92
msgid "easily migrate or clone your site in minutes"
msgstr ""

#: src/includes/updraftplus-notices.php:82
msgid "Add SFTP to send your data securely, lock settings and encrypt your database backups for extra security."
msgstr ""

#: src/includes/updraftplus-notices.php:81
msgid "secure your backups"
msgstr ""

#: src/includes/updraftplus-notices.php:71
msgid "Secure multisite installation, advanced reporting and much more."
msgstr ""

#: src/includes/updraftplus-notices.php:70
msgid "advanced options"
msgstr ""

#: src/includes/updraftplus-notices.php:59
msgid "enhanced remote storage options"
msgstr ""

#: src/includes/updraftplus-notices.php:49
msgid "The ultimately secure and convenient place to store your backups."
msgstr ""

#: src/includes/updraftplus-notices.php:48,
#: src/templates/wp-admin/settings/tab-addons.php:296
msgid "UpdraftVault storage"
msgstr ""

#: src/includes/updraftplus-notices.php:38
msgid "Enjoy professional, fast, and friendly help whenever you need it with Premium."
msgstr ""

#: src/includes/updraftplus-notices.php:36,
#: src/includes/updraftplus-notices.php:47,
#: src/includes/updraftplus-notices.php:58,
#: src/includes/updraftplus-notices.php:69,
#: src/includes/updraftplus-notices.php:80,
#: src/includes/updraftplus-notices.php:91
msgid "UpdraftPlus Premium:"
msgstr ""

#: src/addons/s3-enhanced.php:422
msgid "Canada (Central)"
msgstr ""

#: src/templates/wp-admin/advanced/tools-menu.php:24
msgid "Site size"
msgstr ""

#: src/templates/wp-admin/advanced/tools-menu.php:12,
#: src/templates/wp-admin/settings/tab-addons.php:282,
#: src/templates/wp-admin/settings/tab-addons.php:283
msgid "Lock settings"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:5,
#: src/templates/wp-admin/advanced/tools-menu.php:8
msgid "Site information"
msgstr ""

#: src/templates/wp-admin/advanced/search-replace.php:9
msgid "For the ability to migrate websites, upgrade to UpdraftPlus Premium."
msgstr ""

#: src/templates/wp-admin/advanced/export-settings.php:15
msgid "Import settings"
msgstr ""

#: src/templates/wp-admin/advanced/export-settings.php:9
msgid "Export settings"
msgstr ""

#: src/templates/wp-admin/advanced/export-settings.php:7
msgid "including any passwords"
msgstr ""

#: src/templates/wp-admin/advanced/export-settings.php:5,
#: src/templates/wp-admin/advanced/tools-menu.php:32
msgid "Export / import settings"
msgstr ""

#: src/restorer.php:2603
msgid "Processing table (%s)"
msgstr ""

#: src/restorer.php:3034
msgid "Backup of: %s"
msgstr ""

#: src/methods/dropbox.php:887
msgid "%s de-authentication"
msgstr ""

#: src/methods/dropbox.php:610
msgid "You must add the following as the authorised redirect URI in your Dropbox console (under \"API Settings\") when asked"
msgstr ""

#: src/backup.php:2113
msgid "If not, you will need to either remove data from this table, or contact your hosting company to request more resources."
msgstr ""

#: src/templates/wp-admin/settings/take-backup.php:85
msgid "You have selected a remote storage option which has an authorization step to complete:"
msgstr ""

#: src/admin.php:2232
msgid "Remote files deleted:"
msgstr ""

#: src/admin.php:2231
msgid "Local files deleted:"
msgstr ""

#: src/methods/backup-module.php:637
msgid "Follow this link to authorize access to your %s account (you will not be able to backup to %s without it)."
msgstr ""

#: src/admin.php:1031
msgid "remote files deleted"
msgstr ""

#: src/admin.php:1027
msgid "Complete"
msgstr ""

#: src/admin.php:1026
msgid "Do you want to carry out the import?"
msgstr ""

#: src/admin.php:1025
msgid "Which was exported on:"
msgstr ""

#: src/admin.php:1024
msgid "This will import data from:"
msgstr ""

#: src/admin.php:1023
msgid "Importing..."
msgstr ""

#: src/admin.php:1019
msgid "You have not yet selected a file to import."
msgstr ""

#: src/admin.php:1002
msgid "Your export file will be of your displayed settings, not your saved ones."
msgstr ""

#: src/admin.php:99
msgid "template not found"
msgstr ""

#: src/addons/s3-enhanced.php:418
msgid "US East (Ohio)"
msgstr ""

#: src/addons/onedrive.php:793
msgid "Account is not authorized (%s)."
msgstr ""

#: src/addons/onedrive.php:754, src/udaddons/updraftplus-addons.php:1040
msgid "Your IP address:"
msgstr ""

#: src/addons/onedrive.php:754, src/udaddons/updraftplus-addons.php:1040,
#: src/udaddons/updraftplus-addons.php:1053
msgid "To remove any block, please go here."
msgstr ""

#: src/addons/onedrive.php:741, src/udaddons/updraftplus-addons.php:1027
msgid "An error response was received; HTTP code:"
msgstr ""

#: src/includes/class-commands.php:444
msgid "%s add-on not found"
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:60
msgid "or to restore manually"
msgstr ""

#: src/admin.php:3008
msgid "To fix this problem go here."
msgstr ""

#: src/admin.php:3008
msgid "OptimizePress 2.0 encodes its contents, so search/replace does not work."
msgstr ""

#: src/admin.php:986
msgid "your PHP install lacks the openssl module; as a result, this can take minutes; if nothing has happened by then, then you should either try a smaller key size, or ask your web hosting company how to enable this PHP module on your setup."
msgstr ""

#: src/addons/webdav.php:437
msgid "Path"
msgstr ""

#: src/addons/webdav.php:436
msgid "Leave this blank to use the default (80 for webdav, 443 for webdavs)"
msgstr ""

#: src/addons/webdav.php:434
msgid "Enter any path in the field below."
msgstr ""

#: src/addons/webdav.php:434
msgid "A host name cannot contain a slash."
msgstr ""

#: src/addons/webdav.php:429
msgid "Protocol (SSL or not)"
msgstr ""

#: src/udaddons/updraftplus-addons.php:1056
msgid "This usually indicates a network connectivity issue (e.g. an outgoing firewall or overloaded network) between this site and UpdraftPlus.com."
msgstr ""

#: src/methods/s3.php:1440
msgid "The AWS access key looks to be wrong (valid %s access keys begin with \"AK\")"
msgstr ""

#: src/methods/s3.php:155
msgid "No settings were found - please go to the Settings tab and check your settings"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:115
msgid "Backup using %s?"
msgstr ""

#: src/addons/s3-enhanced.php:434
msgid "Asia Pacific (Mumbai)"
msgstr ""

#: src/addons/s3-enhanced.php:76
msgid "Standard (infrequent access)"
msgstr ""

#: src/templates/wp-admin/settings/header.php:25
msgid "FAQs"
msgstr ""

#: src/backup.php:3150
msgid "Failed to open directory (check the file permissions and ownership): %s"
msgstr ""

#: src/backup.php:3128
msgid "%s: unreadable file - could not be backed up (check the file permissions and ownership)"
msgstr ""

#: src/addons/migrator.php:563
msgid "Create key"
msgstr ""

#: src/addons/migrator.php:560
msgid "slower, strongest"
msgstr ""

#: src/addons/migrator.php:559
msgid "recommended"
msgstr ""

#: src/addons/migrator.php:559
msgid "%s bytes"
msgstr ""

#: src/addons/migrator.php:558
msgid "faster (possibility for slow PHP installs)"
msgstr ""

#: src/addons/migrator.php:558, src/addons/migrator.php:560
msgid "%s bits"
msgstr ""

#: src/addons/migrator.php:556
msgid "Encryption key size:"
msgstr ""

#: src/addons/migrator.php:554
msgid "Enter your chosen name"
msgstr ""

#: src/addons/migrator.php:553
msgid "Create a key: give this key a unique name (e.g. indicate the site it is for), then press \"Create key\":"
msgstr ""

#: src/methods/googledrive.php:767
msgid "Upload expected to fail: the %s limit for any single file is %s, whereas this file is %s GB (%d bytes)"
msgstr ""

#: src/methods/ftp.php:466
msgid "This is sometimes caused by a firewall - try turning off SSL in the expert settings, and testing again."
msgstr ""

#: src/methods/ftp.php:438
msgid "login"
msgstr ""

#: src/addons/reporting.php:549, src/addons/reporting.php:549,
#: src/addons/reporting.php:551, src/methods/email.php:114
msgid "Be aware that mail servers tend to have size limits; typically around %s MB; backups larger than any limits will likely not arrive."
msgstr ""

#: src/class-updraftplus.php:2049
msgid "Size: %s MB"
msgstr ""

#: src/class-updraftplus.php:5178, src/restorer.php:1882
msgid "You should enable %s to make any pretty permalinks (e.g. %s) work"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:104
msgid "(tap on an icon to select or unselect)"
msgstr ""

#: src/methods/updraftvault.php:499, src/methods/updraftvault.php:504,
#: src/methods/updraftvault.php:505, src/methods/updraftvault.php:506
msgid "%s per year"
msgstr ""

#: src/methods/updraftvault.php:497
msgid "or (annual discount)"
msgstr ""

#: src/methods/updraftvault.php:336
msgid "No Vault connection was found for this site (has it moved?); please disconnect and re-connect."
msgstr ""

#: src/class-updraftplus.php:663, src/class-updraftplus.php:745
msgid "The given file was not found, or could not be read."
msgstr ""

#: src/class-updraftplus.php:3660, src/class-updraftplus.php:3772
msgid "The backup was aborted by the user"
msgstr ""

#: src/admin.php:5667
msgid "Your settings have been saved."
msgstr ""

#: src/admin.php:4515
msgid "Total backup size:"
msgstr ""

#: src/admin.php:3878
msgid "stop"
msgstr ""

#: src/admin.php:1028, src/admin.php:3664
msgid "The backup has finished running"
msgstr ""

#: src/templates/wp-admin/advanced/tools-menu.php:36,
#: src/templates/wp-admin/advanced/wipe-settings.php:5,
#: src/templates/wp-admin/advanced/wipe-settings.php:10
msgid "Wipe settings"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:120
msgid "reset"
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:14
msgid "these backup sets"
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:11
msgid "this backup set"
msgstr ""

#: src/includes/class-filesystem-functions.php:112
msgid "calculate"
msgstr ""

#: src/admin.php:1001
msgid "You should save your changes to ensure that they are used for making your backup."
msgstr ""

#: src/admin.php:994
msgid "We requested to delete the file, but could not understand the server's response"
msgstr ""

#: src/admin.php:993
msgid "Please enter a valid URL"
msgstr ""

#: src/admin.php:976
msgid "Saving..."
msgstr ""

#: src/admin.php:934
msgid "Error: the server sent us a response which we did not understand."
msgstr ""

#: src/admin.php:926
msgid "Fetching..."
msgstr ""

#: src/addons/s3-enhanced.php:431
msgid "Asia Pacific (Seoul)"
msgstr ""

#: src/restorer.php:3057
msgid "Uploads URL:"
msgstr ""

#: src/addons/onedrive.php:91
msgid "The required %s PHP module is not installed - ask your web hosting company to enable it."
msgstr ""

#: src/class-updraftplus.php:5232, src/restorer.php:3075
msgid "To import an ordinary WordPress site into a multisite installation requires %s."
msgstr ""

#: src/class-updraftplus.php:5228
msgid "Please read this link for important information on this process."
msgstr ""

#: src/class-updraftplus.php:5228
msgid "It will be imported as a new site."
msgstr ""

#: src/admin.php:3294, src/templates/wp-admin/notices/autobackup-notice.php:18,
#: src/templates/wp-admin/notices/autobackup-notice.php:16,
#: src/templates/wp-admin/notices/horizontal-notice.php:50,
#: src/templates/wp-admin/notices/horizontal-notice.php:48
msgid "Dismiss"
msgstr ""

#: src/admin.php:1014
msgid "Please fill in the required information."
msgstr ""

#: src/addons/multisite.php:691
msgid "Read more..."
msgstr ""

#: src/addons/multisite.php:682
msgid "may include some site-wide data"
msgstr ""

#: src/addons/multisite.php:677
msgid "All sites"
msgstr ""

#: src/addons/multisite.php:673
msgid "Which site to restore"
msgstr ""

#: src/addons/migrator.php:434, src/addons/migrator.php:435
msgid "Error when creating new site at your chosen address:"
msgstr ""

#: src/addons/migrator.php:378
msgid "Required information for restoring this backup was not given (%s)"
msgstr ""

#: src/addons/migrator.php:339
msgid "Attribute imported content to user"
msgstr ""

#: src/addons/migrator.php:331, src/addons/migrator.php:329
msgid "You must use lower-case letters or numbers for the site path, only."
msgstr ""

#: src/addons/migrator.php:317
msgid "This feature is not compatible with %s"
msgstr ""

#: src/addons/migrator.php:317, src/addons/migrator.php:315
msgid "Importing a single site into a multisite install"
msgstr ""

#: src/addons/migrator.php:307
msgid "other content from wp-content"
msgstr ""

#: src/addons/migrator.php:304
msgid "WordPress core"
msgstr ""

#: src/addons/migrator.php:304, src/addons/migrator.php:307,
#: src/addons/migrator.php:310
msgid "You selected %s to be included in the restoration - this cannot / should not be done when importing a single site into a network."
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:118
msgid "Call WordPress action:"
msgstr ""

#: src/templates/wp-admin/settings/backupnow-modal.php:45
msgid "Your saved settings also affect what is backed up - e.g. files excluded."
msgstr ""

#: src/restorer.php:522
msgid "Skipping: this archive was already restored."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:141
msgid "File Options"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:94
msgid "Sending Your Backup To Remote Storage"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:58
msgid "Database backup schedule"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:22
msgid "Files backup schedule"
msgstr ""

#: src/templates/wp-admin/advanced/wipe-settings.php:6
msgid "This button will delete all UpdraftPlus settings and progress information for in-progress backups (but not any of your existing backups from your cloud storage)."
msgstr ""

#: src/admin.php:5537
msgid "Send this backup to remote storage"
msgstr ""

#: src/admin.php:5530
msgid "Check out UpdraftVault."
msgstr ""

#: src/admin.php:5530
msgid "Not got any remote storage?"
msgstr ""

#: src/admin.php:5530
msgid "settings"
msgstr ""

#: src/admin.php:5530
msgid "Backup won't be sent to any remote storage - none has been saved in the %s"
msgstr ""

#: src/admin.php:1085, src/templates/wp-admin/settings/backupnow-modal.php:40
msgid "Include your files in the backup"
msgstr ""

#: src/templates/wp-admin/settings/backupnow-modal.php:26
msgid "Include your database in the backup"
msgstr ""

#: src/admin.php:3293
msgid "Continue restoration"
msgstr ""

#: src/admin.php:3287
msgid "You have an unfinished restoration operation, begun %s ago."
msgstr ""

#: src/admin.php:3286
msgid "Unfinished restoration"
msgstr ""

#: src/admin.php:3283
msgid "%s minutes, %s seconds"
msgstr ""

#: src/admin.php:3111
msgid "Backup Contents And Schedule"
msgstr ""

#: src/admin.php:3214
msgid "Premium / Extensions"
msgstr ""

#: src/admin.php:5027, src/admin.php:5018
msgid "Sufficient information about the in-progress restoration operation could not be found."
msgstr ""

#: src/admin.php:999, src/addons/morefiles.php:86
msgctxt "(verb)"
msgid "Download"
msgstr ""

#: src/admin.php:918
msgid "You have chosen to backup files, but no file entities have been selected"
msgstr ""

#: src/admin.php:772
msgid "Extensions"
msgstr ""

#: src/admin.php:764, src/admin.php:3213
msgid "Advanced Tools"
msgstr ""

#: src/addons/googlecloud.php:1314
msgid "Bucket location"
msgstr ""

#: src/addons/googlecloud.php:1313
msgid "Note that Google do not support every storage class in every location - you should read their documentation to learn about current availability."
msgstr ""

#: src/addons/googlecloud.php:1313, src/addons/googlecloud.php:1316
msgid "This setting applies only when a new bucket is being created."
msgstr ""

#: src/addons/googlecloud.php:1307
msgid "You must use a bucket name that is unique, for all %s users."
msgstr ""

#: src/addons/googlecloud.php:1295
msgid "Do not confuse %s with %s - they are separate things."
msgstr ""

#: src/addons/googlecloud.php:362
msgid "You do not have access to this bucket"
msgstr ""

#: src/addons/googlecloud.php:60, src/addons/googlecloud.php:61,
#: src/addons/googlecloud.php:62
msgid "Western Europe"
msgstr ""

#: src/addons/googlecloud.php:56
msgid "Eastern Asia-Pacific"
msgstr ""

#: src/addons/googlecloud.php:55
msgid "Western United States"
msgstr ""

#: src/addons/googlecloud.php:53, src/addons/googlecloud.php:54
msgid "Eastern United States"
msgstr ""

#: src/addons/googlecloud.php:52
msgid "Central United States"
msgstr ""

#: src/addons/googlecloud.php:51
msgid "European Union"
msgstr ""

#: src/addons/googlecloud.php:50
msgid "Asia Pacific"
msgstr ""

#: src/addons/googlecloud.php:49, src/addons/googlecloud.php:50,
#: src/addons/googlecloud.php:51
msgid "multi-region location"
msgstr ""

#: src/addons/googlecloud.php:49
msgid "United States"
msgstr ""

#: src/addons/googlecloud.php:45
msgid "Nearline"
msgstr ""

#: src/addons/googlecloud.php:44
msgid "Durable reduced availability"
msgstr ""

#: src/addons/googlecloud.php:43, src/addons/s3-enhanced.php:75
msgid "Standard"
msgstr ""

#: src/addons/azure.php:700
msgid "container"
msgstr ""

#: src/addons/azure.php:700
msgid "You can enter the path of any %s virtual folder you wish to use here."
msgstr ""

#: src/addons/azure.php:699
msgid "optional"
msgstr ""

#: src/addons/azure.php:698
msgid "See Microsoft's guidelines on container naming by following this link."
msgstr ""

#: src/addons/azure.php:697
msgid "Enter the path of the %s you wish to use here."
msgstr ""

#: src/addons/azure.php:693
msgid "This is not your Azure login - see the instructions if needing more guidance."
msgstr ""

#: src/addons/azure.php:692, src/addons/azure.php:694,
#: src/addons/azure.php:696, src/addons/azure.php:699
msgid "Azure"
msgstr ""

#: src/addons/azure.php:690, src/methods/dreamobjects.php:186
msgid "Create Azure credentials in your Azure developer console."
msgstr ""

#: src/addons/azure.php:593
msgid "Could not create the container"
msgstr ""

#: src/addons/azure.php:447
msgid "Could not access container"
msgstr ""

#: src/class-updraftplus.php:3679
msgid "To complete your migration/clone, you should now log in to the remote site and restore the backup set."
msgstr ""

#: src/backup.php:1901
msgid "the options table was not found"
msgstr ""

#: src/backup.php:1899
msgid "no options or sitemeta table was found"
msgstr ""

#: src/backup.php:1657
msgid "The backup directory is not writable (or disk space is full) - the database backup is expected to shortly fail."
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:74
msgid "required for some remote storage providers"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:74
msgid "Not installed"
msgstr ""

#: src/addons/googlecloud.php:1310, src/addons/s3-enhanced.php:71
msgid "Storage class"
msgstr ""

#: src/addons/googlecloud.php:1309
msgid "See Google's guidelines on bucket naming by following this link."
msgstr ""

#: src/addons/googlecloud.php:1308
msgid "Enter the name of the %s bucket you wish to use here."
msgstr ""

#: src/addons/googlecloud.php:1306
msgid "Bucket"
msgstr ""

#: src/addons/googlecloud.php:1304
msgid "Otherwise, you can leave it blank."
msgstr ""

#: src/addons/googlecloud.php:1304
msgid "N.B. This is only needed if you have not already created the bucket, and you wish UpdraftPlus to create it for you."
msgstr ""

#: src/addons/googlecloud.php:1303
msgid "Enter the ID of the %s project you wish to use here."
msgstr ""

#: src/addons/googlecloud.php:1289
msgid "Follow this link to your Google API Console, and there activate the Storage API and create a Client ID in the API Access section."
msgstr ""

#: src/addons/googlecloud.php:1105
msgid "You must enter a project ID in order to be able to create a new bucket."
msgstr ""

#: src/addons/googlecloud.php:1302
msgid "Project ID"
msgstr ""

#: src/addons/googlecloud.php:949
msgid "You must save and authenticate before you can test your settings."
msgstr ""

#: src/addons/googlecloud.php:693
msgid "Have not yet obtained an access token from Google - you need to authorize or re-authorize your connection to Google Cloud."
msgstr ""

#: src/addons/googlecloud.php:309, src/addons/googlecloud.php:386,
#: src/addons/googlecloud.php:1094, src/addons/googlecloud.php:1144
msgid "You do not have access to this bucket."
msgstr ""

#: src/addons/googlecloud.php:309, src/addons/googlecloud.php:362,
#: src/addons/googlecloud.php:375, src/addons/googlecloud.php:373,
#: src/addons/googlecloud.php:386, src/addons/googlecloud.php:904,
#: src/addons/googlecloud.php:1094, src/addons/googlecloud.php:1146,
#: src/addons/googlecloud.php:1144, src/addons/googlecloud.php:1284,
#: src/addons/googlecloud.php:1284, src/addons/googlecloud.php:1298,
#: src/addons/googlecloud.php:1300, src/addons/googlecloud.php:1307
msgid "Google Cloud"
msgstr ""

#: src/addons/googlecloud.php:309, src/addons/googlecloud.php:362,
#: src/addons/googlecloud.php:386, src/addons/googlecloud.php:1094,
#: src/addons/googlecloud.php:1146, src/addons/googlecloud.php:1144
msgid "%s Service Exception."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:88
msgid "or to configure more complex schedules"
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:11,
#: src/templates/wp-admin/settings/delete-and-restore-modals.php:14
msgid "Are you sure that you wish to remove %s from UpdraftPlus?"
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:169
msgid "Deselect"
msgstr ""

#: src/addons/moredatabase.php:459, src/addons/morefiles.php:901,
#: src/templates/wp-admin/settings/existing-backups-table.php:168
msgid "Select all"
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:166
msgid "Actions upon selected backups"
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:56,
#: src/templates/wp-admin/settings/tab-backups.php:60
msgid "Press here to look inside your remote storage methods for any existing backup sets (from any site, if they are stored in the same folder)."
msgstr ""

#: src/admin.php:2230
msgid "Backup sets removed:"
msgstr ""

#: src/admin.php:1012
msgid "Processing..."
msgstr ""

#: src/admin.php:1010
msgid "For backups older than"
msgstr ""

#: src/admin.php:1009
msgid "week(s)"
msgstr ""

#: src/admin.php:1008
msgid "hour(s)"
msgstr ""

#: src/admin.php:1007
msgid "day(s)"
msgstr ""

#: src/admin.php:1006
msgid "in the month"
msgstr ""

#: src/admin.php:1005
msgid "day"
msgstr ""

#: src/addons/morestorage.php:32
msgid "(as many as you like)"
msgstr ""

#: src/addons/fixtime.php:305, src/addons/fixtime.php:310
msgid "Add an additional retention rule..."
msgstr ""

#: src/restorer.php:4035
msgid "This database needs to be deployed on MySQL version %s or later."
msgstr ""

#: src/restorer.php:4035
msgid "This problem is caused by trying to restore a database on a very old MySQL version that is incompatible with the source database."
msgstr ""

#: src/methods/updraftvault.php:922
msgid "You do not currently have any UpdraftVault quota"
msgstr ""

#: src/class-updraftplus.php:5309
msgid "You must upgrade MySQL to be able to use this database."
msgstr ""

#: src/class-updraftplus.php:5309
msgid "The database backup uses MySQL features not available in the old MySQL version (%s) that this site is running on."
msgstr ""

#: src/methods/updraftvault.php:526, src/methods/updraftvault.php:527
msgid "Don't know your email address, or forgotten your password?"
msgstr ""

#: src/methods/updraftvault.php:519
msgid "Enter your UpdraftPlus.Com email / password here to connect:"
msgstr ""

#: src/methods/updraftvault.php:487
msgid "Read the %s FAQs here."
msgstr ""

#: src/addons/s3-enhanced.php:82
msgid "Check this box to use Amazon's server-side encryption"
msgstr ""

#: src/addons/s3-enhanced.php:79
msgid "Server-side encryption"
msgstr ""

#: src/methods/updraftvault.php:930
msgid "If you have forgotten your password, then go here to change your password on updraftplus.com."
msgstr ""

#: src/admin.php:1456
msgid "%s has been chosen for remote storage, but you are not currently connected."
msgstr ""

#: src/methods/updraftvault.php:518
msgid "Payments can be made in US dollars, euros or GB pounds sterling, via card or PayPal."
msgstr ""

#: src/admin.php:982
msgid "Update quota count"
msgstr ""

#: src/admin.php:981
msgid "Counting..."
msgstr ""

#: src/admin.php:980
msgid "Disconnecting..."
msgstr ""

#: src/admin.php:978
msgid "Connecting..."
msgstr ""

#: src/methods/updraftvault.php:652, src/methods/updraftvault.php:755
msgid "Refresh current status"
msgstr ""

#: src/methods/updraftvault.php:650, src/methods/updraftvault.php:667,
#: src/methods/updraftvault.php:669, src/methods/updraftvault.php:755
msgid "Get more quota"
msgstr ""

#: src/methods/updraftvault.php:647, src/methods/updraftvault.php:664,
#: src/methods/updraftvault.php:719
msgid "Current use:"
msgstr ""

#: src/methods/updraftvault.php:632
msgid "You can get more quota here"
msgstr ""

#: src/methods/updraftvault.php:632
msgid "Error: you have insufficient storage quota available (%s) to upload this archive (%s) (%s)."
msgstr ""

#: src/admin.php:979, src/methods/updraftvault.php:533,
#: src/methods/updraftvault.php:615
msgid "Disconnect"
msgstr ""

#: src/methods/updraftvault.php:532, src/methods/updraftvault.php:601
msgid "Quota:"
msgstr ""

#: src/methods/updraftvault.php:531, src/methods/updraftvault.php:599
msgid "Vault owner"
msgstr ""

#: src/methods/updraftvault.php:530, src/methods/updraftvault.php:599
msgid "Well done - there's nothing more needed to set up."
msgstr ""

#: src/methods/updraftvault.php:530, src/methods/updraftvault.php:599
msgid "This site is <strong>connected</strong> to UpdraftVault."
msgstr ""

#: src/methods/updraftvault.php:534, src/methods/updraftvault.php:595
msgid "You are <strong>not connected</strong> to UpdraftVault."
msgstr ""

#: src/methods/updraftvault.php:529
msgid "Go here for help"
msgstr ""

#: src/methods/updraftvault.php:515,
#: src/templates/wp-admin/settings/exclude-settings-modal/exclude-panel-heading.php:4
msgid "Back..."
msgstr ""

#: src/methods/updraftvault.php:518
msgid "Subscriptions can be cancelled at any time."
msgstr ""

#: src/methods/updraftvault.php:500, src/methods/updraftvault.php:501,
#: src/methods/updraftvault.php:502
msgid "%s per quarter"
msgstr ""

#: src/methods/updraftvault.php:485
msgid "UpdraftVault is built on top of Amazon's world-leading data-centres, with redundant data storage to achieve 99.999999999% reliability."
msgstr ""

#: src/methods/updraftvault.php:481
msgid "Show the options"
msgstr ""

#: src/methods/updraftvault.php:479, src/methods/updraftvault.php:490
msgid "Press a button to get started."
msgstr ""

#: src/methods/updraftvault.php:479, src/methods/updraftvault.php:490
msgid "UpdraftVault brings you storage that is <strong>reliable, easy to use and a great price</strong>."
msgstr ""

#: src/methods/updraftvault.php:155
msgid "Updraft Vault"
msgstr ""

#: src/addons/azure.php:477, src/addons/backblaze.php:611,
#: src/addons/googlecloud.php:1034, src/methods/s3.php:1468
msgid "Delete failed:"
msgstr ""

#: src/backup.php:4383
msgid "The zip engine returned the message: %s."
msgstr ""

#: src/addons/s3-enhanced.php:456, src/addons/s3-enhanced.php:458
msgid "Without this permission, UpdraftPlus cannot delete backups - you should also set your 'retain' settings very high to prevent seeing deletion errors."
msgstr ""

#: src/addons/s3-enhanced.php:456, src/addons/s3-enhanced.php:456
msgid "Allow deletion"
msgstr ""

#: src/addons/s3-enhanced.php:452, src/addons/s3-enhanced.php:454
msgid "Without this permission, you cannot directly download or restore using UpdraftPlus, and will instead need to visit the AWS website."
msgstr ""

#: src/addons/s3-enhanced.php:452, src/addons/s3-enhanced.php:452
msgid "Allow download"
msgstr ""

#: src/includes/class-remote-send.php:429
msgid "If sending directly from site to site does not work for you, then there are three other methods - please try one of these instead."
msgstr ""

#: src/admin.php:988, src/includes/class-remote-send.php:411
msgid "You should check that the remote site is online, not firewalled, does not have security modules that may be blocking access, has UpdraftPlus version %s or later active and that the keys have been entered correctly."
msgstr ""

#: src/includes/class-remote-send.php:693
msgid "Existing keys"
msgstr ""

#: src/addons/migrator.php:568
msgid "Your new key:"
msgstr ""

#: src/includes/class-remote-send.php:555
msgid "Key created successfully."
msgstr ""

#: src/includes/class-remote-send.php:538
msgid "A key with this name already exists; you must use a unique name."
msgstr ""

#: src/includes/class-remote-send.php:463
msgid "Also send this backup to the active remote storage locations"
msgstr ""

#: src/includes/class-remote-send.php:359
msgid "site not found"
msgstr ""

#: src/includes/class-remote-send.php:326
msgid "Backup data will be sent to:"
msgstr ""

#: src/addons/migrator.php:207
msgid "Restore an existing backup set onto this site"
msgstr ""

#: src/addons/migrator.php:213
msgid "This site has no backups to restore from yet."
msgstr ""

#: src/addons/reporting.php:197
msgid "Backup made by %s"
msgstr ""

#: src/methods/addon-base-v2.php:222
msgid "This storage method does not allow downloading"
msgstr ""

#: src/admin.php:4657
msgid "(backup set imported from remote location)"
msgstr ""

#: src/addons/wp-cli.php:379,
#: src/templates/wp-admin/settings/existing-backups-table.php:105
msgid "Site"
msgstr ""

#: src/addons/wp-cli.php:377,
#: src/templates/wp-admin/settings/existing-backups-table.php:104
msgid "Backup sent to remote site - not available for download."
msgstr ""

#: src/includes/class-wpadmin-commands.php:134
msgid "You should make sure that this really is a backup set intended for use on this website, before you restore (rather than a backup set of an unrelated website)."
msgstr ""

#: src/includes/class-wpadmin-commands.php:134
msgid "This backup set was not known by UpdraftPlus to be created by the current WordPress installation, but was either found in remote storage, or was sent from a remote site."
msgstr ""

#: src/admin.php:995, src/includes/class-remote-send.php:441
msgid "Testing connection..."
msgstr ""

#: src/admin.php:992,
#: src/templates/wp-admin/settings/existing-backups-table.php:173
msgid "Deleting..."
msgstr ""

#: src/admin.php:991
msgid "key name"
msgstr ""

#: src/admin.php:989
msgid "Please give this key a name (e.g. indicate the site it is for):"
msgstr ""

#: src/admin.php:986
msgid "Creating..."
msgstr ""

#: src/addons/migrator.php:549
msgid "Receive a backup from a remote site"
msgstr ""

#: src/addons/migrator.php:541
msgid "Paste key here"
msgstr ""

#: src/addons/migrator.php:537
msgid "How do I get a site's key?"
msgstr ""

#: src/addons/migrator.php:537
msgid "To add a site as a destination for sending to, enter that site's key below."
msgstr ""

#: src/admin.php:1155, src/addons/migrator.php:529
msgid "Send a backup to another site"
msgstr ""

#: src/admin.php:996, src/includes/class-commands.php:964,
#: src/includes/class-remote-send.php:476,
#: src/includes/class-remote-send.php:662
msgid "Send"
msgstr ""

#: src/admin.php:987, src/includes/class-remote-send.php:656
msgid "Send to site:"
msgstr ""

#: src/includes/class-remote-send.php:654
msgid "No receiving sites have yet been added."
msgstr ""

#: src/includes/class-remote-send.php:632
msgid "It is for sending backups to the following site: "
msgstr ""

#: src/includes/class-remote-send.php:632
msgid "The key was successfully added."
msgstr ""

#: src/includes/class-remote-send.php:608
msgid "The entered key does not belong to a remote site (it belongs to this one)."
msgstr ""

#: src/includes/class-remote-send.php:603,
#: src/includes/class-remote-send.php:599,
#: src/includes/class-remote-send.php:597
msgid "The entered key was corrupt - please try again."
msgstr ""

#: src/includes/class-remote-send.php:595
msgid "The entered key was the wrong length - please try again."
msgstr ""

#: src/includes/class-remote-send.php:580
msgid "key"
msgstr ""

#: src/methods/ftp.php:129
msgid "Almost all FTP servers will want passive mode; but if you need active mode, then uncheck this."
msgstr ""

#: src/methods/ftp.php:128
msgid "Passive mode"
msgstr ""

#: src/methods/ftp.php:126
msgid "Remote path"
msgstr ""

#: src/methods/ftp.php:124
msgid "FTP password"
msgstr ""

#: src/methods/ftp.php:123
msgid "FTP login"
msgstr ""

#: src/methods/ftp.php:122
msgid "FTP server"
msgstr ""

#: src/addons/migrator.php:171
msgid "The UpdraftPlus Migrator modifies the restoration operation appropriately, to fit the backup data to the new site."
msgstr ""

#: src/addons/migrator.php:171
msgid "A \"migration\" is ultimately the same as a restoration - but using backup archives that you import from another site."
msgstr ""

#: src/admin.php:984, src/addons/migrator.php:541
msgid "Add site"
msgstr ""

#: src/admin.php:983
msgid "Adding..."
msgstr ""

#: src/udaddons/options.php:336
msgid "Claim not granted - perhaps you have already used this purchase somewhere else, or your paid period for downloading from updraftplus.com has expired?"
msgstr ""

#: src/restorer.php:4037
msgid "To use this backup, your database server needs to support the %s character set."
msgstr ""

#: src/udaddons/updraftplus-addons.php:1089
msgid "go here to change your password on updraftplus.com."
msgstr ""

#: src/udaddons/updraftplus-addons.php:1089
msgid "If you have forgotten your password "
msgstr ""

#: src/udaddons/updraftplus-addons.php:1088
msgid "Go here to re-enter your password."
msgstr ""

#: src/addons/migrator.php:248
msgid "After pressing this button, you will be given the option to choose which components you wish to migrate"
msgstr ""

#: src/admin.php:975, src/admin.php:1001, src/admin.php:1002
msgid "You have made changes to your settings, and not saved."
msgstr ""

#: src/addons/onedrive.php:1322
msgid "N.B. %s is not case-sensitive."
msgstr ""

#: src/addons/onedrive.php:1318
msgid "If OneDrive later shows you the message \"unauthorized_client\", then you did not enter a valid client ID here."
msgstr ""

#: src/addons/googlecloud.php:1288, src/includes/class-remote-send.php:429,
#: src/methods/googledrive.php:1494
msgid "For longer help, including screenshots, follow this link."
msgstr ""

#: src/addons/onedrive.php:1313
msgid "Create OneDrive credentials in your OneDrive developer console."
msgstr ""

#: src/addons/onedrive.php:1316
msgid "You must add the following as the authorized redirect URI in your OneDrive console (under \"API Settings\") when asked"
msgstr ""

#: src/addons/onedrive.php:1189, src/addons/onedrive.php:1187
msgid "authorization failed:"
msgstr ""

#: src/addons/onedrive.php:1013, src/addons/onedrive.php:1317,
#: src/addons/onedrive.php:1319
msgid "OneDrive"
msgstr ""

#: src/addons/onedrive.php:784
msgid "Please re-authorize the connection to your %s account."
msgstr ""

#: src/methods/email.php:110
msgid "configure it here"
msgstr ""

#: src/addons/onedrive.php:777, src/includes/updraftplus-login.php:55,
#: src/methods/updraftvault.php:895
msgid "To remove the block, please go here."
msgstr ""

#: src/addons/s3-enhanced.php:538
msgid "Do remember to save your settings."
msgstr ""

#: src/addons/s3-enhanced.php:538
msgid "You are now using a IAM user account to access your bucket."
msgstr ""

#: src/addons/s3-enhanced.php:450
msgid "S3 bucket"
msgstr ""

#: src/addons/s3-enhanced.php:440
msgid "China (Beijing) (restricted)"
msgstr ""

#: src/addons/s3-enhanced.php:438
msgid "South America (São Paulo)"
msgstr ""

#: src/addons/s3-enhanced.php:435
msgid "Asia Pacific (Tokyo)"
msgstr ""

#: src/addons/s3-enhanced.php:433
msgid "Asia Pacific (Sydney)"
msgstr ""

#: src/addons/s3-enhanced.php:432
msgid "Asia Pacific (Singapore)"
msgstr ""

#: src/addons/s3-enhanced.php:421
msgid "US Government West (restricted)"
msgstr ""

#: src/addons/s3-enhanced.php:420
msgid "US West (N. California)"
msgstr ""

#: src/addons/s3-enhanced.php:419
msgid "US West (Oregon)"
msgstr ""

#: src/addons/s3-enhanced.php:413
msgid "S3 storage region"
msgstr ""

#: src/addons/s3-enhanced.php:411
msgid "New IAM username"
msgstr ""

#: src/addons/s3-enhanced.php:410
msgid "Admin secret key"
msgstr ""

#: src/addons/s3-enhanced.php:409
msgid "Admin access key"
msgstr ""

#: src/addons/s3-enhanced.php:402
msgid "Then, these lower-powered access credentials can be used, instead of storing your administrative keys."
msgstr ""

#: src/addons/s3-enhanced.php:402
msgid "These will be used to create a new user and key pair with an IAM policy attached which will only allow it to access the indicated bucket."
msgstr ""

#: src/addons/s3-enhanced.php:402
msgid "Enter your administrative Amazon S3 access/secret keys (this needs to be a key pair with enough rights to create new users and buckets), and a new (unique) username for the new user and a bucket name."
msgstr ""

#: src/addons/s3-enhanced.php:488
msgid "Create new IAM user and S3 bucket"
msgstr ""

#: src/addons/s3-enhanced.php:388
msgid "Secret Key: %s"
msgstr ""

#: src/addons/s3-enhanced.php:388
msgid "Access Key: %s"
msgstr ""

#: src/addons/s3-enhanced.php:378
msgid "Failed to apply User Policy"
msgstr ""

#: src/addons/s3-enhanced.php:315, src/addons/s3-enhanced.php:319
msgid "Operation to create user Access Key failed"
msgstr ""

#: src/addons/s3-enhanced.php:296, src/addons/s3-enhanced.php:306
msgid "IAM operation failed (%s)"
msgstr ""

#: src/addons/s3-enhanced.php:302
msgid "Conflict: that user already exists"
msgstr ""

#: src/addons/s3-enhanced.php:240
msgid "AWS authentication failed"
msgstr ""

#: src/addons/s3-enhanced.php:233
msgid "Cannot create new AWS user, since an unknown AWS toolkit is being used."
msgstr ""

#: src/addons/s3-enhanced.php:204
msgid "You need to enter a bucket"
msgstr ""

#: src/addons/s3-enhanced.php:200
msgid "You need to enter a new IAM username"
msgstr ""

#: src/addons/s3-enhanced.php:196
msgid "You need to enter an admin secret key"
msgstr ""

#: src/addons/s3-enhanced.php:192
msgid "You need to enter an admin access key"
msgstr ""

#: src/addons/s3-enhanced.php:70
msgid "If you have an AWS admin user, then you can use this wizard to quickly create a new AWS (IAM) user with access to only this bucket (rather than your whole account)"
msgstr ""

#: src/addons/s3-enhanced.php:69, src/methods/s3.php:1031
msgid "To create a new IAM sub-user and access key that has access only to this bucket, upgrade to Premium."
msgstr ""

#: src/addons/onedrive.php:754, src/addons/onedrive.php:777,
#: src/includes/updraftplus-login.php:55, src/methods/updraftvault.php:895,
#: src/udaddons/updraftplus-addons.php:1040,
#: src/udaddons/updraftplus-addons.php:1053
msgid "This most likely means that you share a webserver with a hacked website that has been used in previous attacks."
msgstr ""

#: src/addons/onedrive.php:777, src/includes/updraftplus-login.php:55,
#: src/methods/updraftvault.php:895, src/udaddons/updraftplus-addons.php:1053
msgid "It appears that your web server's IP Address (%s) is blocked."
msgstr ""

#: src/addons/onedrive.php:777, src/includes/updraftplus-login.php:55,
#: src/methods/updraftvault.php:895, src/udaddons/updraftplus-addons.php:1053
msgid "UpdraftPlus.com has responded with 'Access Denied'."
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:40
msgid "Premium WooCommerce plugins"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:40
msgid "Free two-factor security plugin"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:39
msgid "More quality plugins"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:18,
#: src/templates/wp-admin/settings/tab-addons.php:72
msgid "UpdraftPlus Premium"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:2,
#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:43
msgid "Dismiss (for %s months)"
msgstr ""

#: src/addons/fixtime.php:447
msgid "(at same time as files backup)"
msgstr ""

#: src/admin.php:4190
msgid "No backup has been completed"
msgstr ""

#: src/templates/wp-admin/settings/header.php:21
msgid "Newsletter sign-up"
msgstr ""

#: src/includes/updraftplus-notices.php:156
msgid "Follow this link to sign up for the UpdraftPlus newsletter."
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:165,
#: src/templates/wp-admin/settings/tab-addons.php:166
msgid "Backup non-WordPress files and databases"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:27
msgid "Ask a pre-sales question"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:26
msgid "Pre-sales FAQs"
msgstr ""

#: src/templates/wp-admin/settings/tab-addons.php:25
msgid "Full feature list"
msgstr ""

#: src/addons/autobackup.php:1188
msgid "Backup (where relevant) plugins, themes and the WordPress database with UpdraftPlus before updating"
msgstr ""

#: src/methods/s3.php:186, src/methods/s3.php:198
msgid "%s Error: Failed to initialise"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:191
msgctxt "Uploader: Drop db.gz.crypt files here to upload them for decryption - or - Select Files"
msgid "or"
msgstr ""

#: src/addons/sftp.php:618
msgctxt "Do not translate BEGIN RSA PRIVATE KEY. PCKS1, XML, PEM and PuTTY are also technical acronyms which should not be translated."
msgid "PKCS1 (PEM header: BEGIN RSA PRIVATE KEY), XML and PuTTY format keys are accepted."
msgstr ""

#: src/methods/openstack2.php:125
msgctxt "\"tenant\" is a term used with OpenStack storage - Google for \"OpenStack tenant\" to get more help on its meaning"
msgid "tenant"
msgstr ""

#: src/methods/openstack2.php:248
msgctxt "Keystone and swauth are technical terms which cannot be translated"
msgid "This needs to be a v2 (Keystone) authentication URI; v1 (Swauth) is not supported."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:274,
#: src/templates/wp-admin/settings/form-contents.php:277
msgid "your site's admin address"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:274
msgid "Check this box to have a basic report sent to"
msgstr ""

#: src/admin.php:4220
msgctxt "i.e. Non-automatic"
msgid "Manual"
msgstr ""

#: src/restorer.php:3990
msgctxt "The user is being told the number of times an error has happened, e.g. An error (27) occurred"
msgid "An error (%s) occurred:"
msgstr ""

#: src/addons/lockadmin.php:198
msgid "Change Lock Settings"
msgstr ""

#: src/addons/morefiles.php:293
msgid "Any other file/directory on your server that you wish to backup"
msgstr ""

#: src/admin.php:3010
msgid "For even more features and personal support, check out "
msgstr ""

#: src/addons/moredatabase.php:61
msgid "Database decryption phrase"
msgstr ""

#: src/admin.php:974, src/addons/autobackup.php:161,
#: src/addons/autobackup.php:1131
msgid "Automatic backup before update"
msgstr ""

#: src/addons/autobackup.php:126
msgid "WordPress core (only)"
msgstr ""

#: src/addons/lockadmin.php:250
msgid "For unlocking support, please contact whoever manages UpdraftPlus for you."
msgstr ""

#: src/addons/lockadmin.php:241
msgid "To access the UpdraftPlus settings, please enter your unlock password"
msgstr ""

#: src/addons/lockadmin.php:238
msgid "Password incorrect"
msgstr ""

#: src/addons/lockadmin.php:226, src/addons/lockadmin.php:232
msgid "Unlock"
msgstr ""

#: src/addons/lockadmin.php:196
msgid "Otherwise, the default link will be shown."
msgstr ""

#: src/addons/lockadmin.php:196
msgid "Anyone seeing the lock screen will be shown this URL for support - enter a website address or an email address."
msgstr ""

#: src/addons/lockadmin.php:196
msgid "Support URL"
msgstr ""

#: src/addons/lockadmin.php:194
msgid "Require password again after"
msgstr ""

#: src/addons/lockadmin.php:185, src/addons/lockadmin.php:186
msgid "%s weeks"
msgstr ""

#: src/addons/lockadmin.php:184
msgid "1 week"
msgstr ""

#: src/addons/lockadmin.php:182, src/addons/lockadmin.php:183
msgid "%s hours"
msgstr ""

#: src/addons/lockadmin.php:181
msgid "1 hour"
msgstr ""

#: src/addons/lockadmin.php:170
msgid "Please make sure that you have made a note of the password!"
msgstr ""

#: src/addons/lockadmin.php:163,
#: src/templates/wp-admin/advanced/lock-admin.php:8
msgid "Lock access to the UpdraftPlus settings page"
msgstr ""

#: src/addons/lockadmin.php:136
msgid "The admin password has been changed."
msgstr ""

#: src/addons/lockadmin.php:134
msgid "An admin password has been set."
msgstr ""

#: src/addons/lockadmin.php:132
msgid "The admin password has now been removed."
msgstr ""

#: src/addons/morefiles.php:156
msgid "(learn more about this significant option)"
msgstr ""

#: src/udaddons/options.php:271
msgid "Note that after you have claimed your add-ons, you can remove your password (but not the email address) from the settings below, without affecting this site's access to updates."
msgstr ""

#: src/admin.php:3664, src/admin.php:4758
msgid "View Log"
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:18,
#: src/templates/wp-admin/settings/existing-backups-table.php:100
msgid "Backup data (click to download)"
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:17,
#: src/templates/wp-admin/settings/existing-backups-table.php:67
msgid "Backup date"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:42,
#: src/templates/wp-admin/settings/form-contents.php:76
msgid "and retain this many scheduled backups"
msgstr ""

#: src/admin.php:4160
msgid "incremental backup; base backup: %s"
msgstr ""

#: src/templates/wp-admin/advanced/lock-admin.php:19
msgid "For the ability to lock access to UpdraftPlus settings with a password, upgrade to UpdraftPlus Premium."
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:69,
#: src/templates/wp-admin/settings/tab-backups.php:71
msgid "Upload files into UpdraftPlus."
msgstr ""

#: src/admin.php:1366, src/includes/class-commands.php:505,
#: src/templates/wp-admin/settings/take-backup.php:13
msgid "The 'Backup Now' button is disabled as your backup directory is not writable (go to the 'Settings' tab and find the relevant option)."
msgstr ""

#: src/class-updraftplus.php:5217
msgid "Backup label:"
msgstr ""

#: src/admin.php:2539, src/addons/backblaze.php:240
msgid "Error: unexpected file read fail"
msgstr ""

#: src/backup.php:4389
msgid "check your log for more details."
msgstr ""

#: src/backup.php:4385
msgid "A zip error occurred"
msgstr ""

#: src/addons/reporting.php:92
msgid "Your label for this backup (optional)"
msgstr ""

#: src/methods/updraftvault.php:933, src/udaddons/updraftplus-addons.php:1092
msgid "You entered an email address that was not recognised by UpdraftPlus.Com"
msgstr ""

#: src/methods/updraftvault.php:930, src/udaddons/updraftplus-addons.php:1088
msgid "Your email address was valid, but your password was not recognised by UpdraftPlus.Com."
msgstr ""

#: src/methods/updraftvault.php:870, src/udaddons/updraftplus-addons.php:957
msgid "You need to supply both an email address and a password"
msgstr ""

#: src/class-updraftplus.php:5236
msgid "If you want to restore a multisite backup, you should first set up your WordPress installation as a multisite."
msgstr ""

#: src/includes/migrator-lite.php:986
msgid "already done"
msgstr ""

#: src/includes/migrator-lite.php:946
msgid "skipped (not in list)"
msgstr ""

#: src/includes/class-search-replace.php:99,
#: src/includes/migrator-lite.php:946, src/includes/migrator-lite.php:986
msgid "Search and replacing table:"
msgstr ""

#: src/includes/migrator-lite.php:293
msgid "Enter a comma-separated list; otherwise, leave blank for all tables."
msgstr ""

#: src/includes/migrator-lite.php:293
msgid "These tables only"
msgstr ""

#: src/includes/migrator-lite.php:292
msgid "Rows per batch"
msgstr ""

#: src/udaddons/options.php:122
msgid "You have not yet connected with your UpdraftPlus.Com account."
msgstr ""

#: src/udaddons/options.php:122, src/udaddons/options.php:120
msgid "You need to connect to receive future updates to UpdraftPlus."
msgstr ""

#: src/class-updraftplus.php:5209, src/class-updraftplus.php:5207
msgid "Any support requests to do with %s should be raised with your web hosting company."
msgstr ""

#: src/class-updraftplus.php:5207
msgid "You should only proceed if you cannot update the current server and are confident (or willing to risk) that your plugins/themes/etc are compatible with the older %s version."
msgstr ""

#: src/class-updraftplus.php:5207
msgid "This is significantly newer than the server which you are now restoring onto (version %s)."
msgstr ""

#: src/class-updraftplus.php:5209, src/class-updraftplus.php:5207
msgid "The site in this backup was running on a webserver with version %s of %s."
msgstr ""

#: src/includes/updraftplus-notices.php:146,
#: src/includes/updraftplus-notices.php:148
msgid "Facebook"
msgstr ""

#: src/includes/updraftplus-notices.php:144,
#: src/includes/updraftplus-notices.php:147,
#: src/templates/wp-admin/settings/header.php:16
msgid "Twitter"
msgstr ""

#: src/includes/updraftplus-notices.php:143
msgid "UpdraftPlus is on social media - check us out!"
msgstr ""

#: src/addons/wp-cli.php:979, src/includes/class-filesystem-functions.php:83
msgid "Why am I seeing this?"
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:55,
#: src/templates/wp-admin/settings/tab-backups.php:59
msgid "The location of this directory is set in the expert settings, in the Settings tab."
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:55,
#: src/templates/wp-admin/settings/tab-backups.php:59
msgid "Press here to look inside your UpdraftPlus directory (in your web hosting space) for any new backup sets that you have uploaded."
msgstr ""

#: src/admin.php:2458, src/admin.php:2481, src/includes/class-commands.php:992
msgid "Start backup"
msgstr ""

#: src/class-updraftplus.php:5178, src/restorer.php:1882
msgid "You are using the %s webserver, but do not seem to have the %s module loaded."
msgstr ""

#: src/admin.php:4048
msgid "You will need to consult with your web hosting provider to find out how to set permissions for a WordPress plugin to write to the directory."
msgstr ""

#: src/templates/wp-admin/advanced/advanced-tools.php:6
msgid "Unless you have a problem, you can completely ignore everything here."
msgstr ""

#: src/admin.php:2722
msgid "This file could not be uploaded"
msgstr ""

#: src/admin.php:2685
msgid "You will find more information about this in the Settings section."
msgstr ""

#: src/addons/importer.php:78
msgid "Supported backup plugins: %s"
msgstr ""

#: src/addons/importer.php:78
msgid "Was this a backup created by a different backup plugin? If so, then you might first need to rename it so that it can be recognized - please follow this link."
msgstr ""

#: src/addons/incremental.php:378
msgid "Tell me more about incremental backups"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:57
msgid "Memory limit"
msgstr ""

#: src/restorer.php:2800, src/includes/class-filesystem-functions.php:304
msgid "restoration"
msgstr ""

#: src/class-updraftplus.php:3767
msgid "Incremental"
msgstr ""

#: src/class-updraftplus.php:3767
msgid "Full backup"
msgstr ""

#: src/addons/autobackup.php:579, src/addons/autobackup.php:577
msgid "now proceeding with the updates..."
msgstr ""

#: src/addons/autobackup.php:579, src/addons/autobackup.php:577
msgid "(view log...)"
msgstr ""

#: src/addons/autobackup.php:579, src/addons/autobackup.php:577
msgid "Backup succeeded"
msgstr ""

#: src/updraftplus.php:122, src/addons/incremental.php:343,
#: src/addons/incremental.php:344, src/addons/incremental.php:345,
#: src/addons/incremental.php:346
msgid "Every %s hours"
msgstr ""

#: src/restorer.php:4382, src/restorer.php:4380,
#: src/includes/migrator-lite.php:629, src/includes/migrator-lite.php:627
msgid "search and replace"
msgstr ""

#: src/includes/migrator-lite.php:295
msgid "Go"
msgstr ""

#: src/includes/migrator-lite.php:284
msgid "A search/replace cannot be undone - are you sure you want to do this?"
msgstr ""

#: src/includes/migrator-lite.php:283
msgid "This can easily destroy your site; so, use it with care!"
msgstr ""

#: src/includes/migrator-lite.php:238, src/includes/migrator-lite.php:291
msgid "Replace with"
msgstr ""

#: src/includes/migrator-lite.php:237, src/includes/migrator-lite.php:290
msgid "Search for"
msgstr ""

#: src/includes/migrator-lite.php:236, src/includes/migrator-lite.php:282,
#: src/templates/wp-admin/advanced/search-replace.php:7,
#: src/templates/wp-admin/advanced/tools-menu.php:20
msgid "Search / replace database"
msgstr ""

#: src/includes/migrator-lite.php:242
msgid "search term"
msgstr ""

#: src/restorer.php:4049
msgid "Too many database errors have occurred - aborting"
msgstr ""

#: src/class-updraftplus.php:3839
msgid "read more at %s"
msgstr ""

#: src/class-updraftplus.php:3839
msgid "Email reports created by UpdraftPlus (free edition) bring you the latest UpdraftPlus.com news"
msgstr ""

#: src/methods/googledrive.php:1500
msgid "N.B. If you install UpdraftPlus on several WordPress sites, then you cannot re-use your project; you must create a new one from your Google API console for each site."
msgstr ""

#: src/includes/class-backup-history.php:133
msgid "You have not yet made any backups."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:153
msgid "Database Options"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:92
msgid "%s (%s used)"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:92
msgid "Free disk space in account:"
msgstr ""

#: src/admin.php:5638, src/templates/wp-admin/settings/take-backup.php:52
msgid "This button is disabled because your backup directory is not writable (see the settings)."
msgstr ""

#: src/admin.php:1371
msgid "To change any of the default settings of what is backed up, to configure scheduled backups, to send your backups to remote storage (recommended), and more, go to the settings tab."
msgstr ""

#: src/admin.php:1371
msgid "To make a backup, just press the Backup Now button."
msgstr ""

#: src/admin.php:1371
msgid "Welcome to UpdraftPlus!"
msgstr ""

#: src/addons/moredatabase.php:281
msgid "Testing..."
msgstr ""

#: src/addons/moredatabase.php:268
msgid "Test connection..."
msgstr ""

#: src/addons/moredatabase.php:267
msgid "Table prefix"
msgstr ""

#: src/addons/moredatabase.php:261
msgid "Backup external database"
msgstr ""

#: src/addons/moredatabase.php:201
msgid "Add an external database to backup..."
msgstr ""

#: src/addons/moredatabase.php:197
msgid "If your database includes extra tables that are not part of this WordPress site (you will know if this is the case), then activate this option to also back them up."
msgstr ""

#: src/addons/moredatabase.php:196
msgid "Backup non-WordPress tables contained in the same database as WordPress"
msgstr ""

#: src/addons/moredatabase.php:196
msgid "This option will cause tables stored in the MySQL database which do not belong to WordPress (identified by their lacking the configured WordPress prefix, %s) to also be backed up."
msgstr ""

#: src/addons/moredatabase.php:181
msgid "Connection failed."
msgstr ""

#: src/addons/moredatabase.php:179
msgid "Connection succeeded."
msgstr ""

#: src/addons/moredatabase.php:161
msgid "%s total table(s) found; %s with the indicated prefix."
msgstr ""

#: src/addons/moredatabase.php:155
msgid "%s table(s) found."
msgstr ""

#: src/addons/moredatabase.php:128
msgid "database connection attempt failed"
msgstr ""

#: src/addons/moredatabase.php:117
msgid "database name"
msgstr ""

#: src/addons/moredatabase.php:115
msgid "host"
msgstr ""

#: src/addons/moredatabase.php:113
msgid "user"
msgstr ""

#: src/class-updraftplus.php:2046
msgid "External database (%s)"
msgstr ""

#: src/methods/googledrive.php:1496
msgid "Follow this link to your Google API Console, and there activate the Drive API and create a Client ID in the API Access section."
msgstr ""

#: src/methods/googledrive.php:717
msgid "failed to access parent folder"
msgstr ""

#: src/addons/googlecloud.php:884, src/addons/onedrive.php:981,
#: src/addons/onedrive.php:992, src/methods/googledrive.php:638,
#: src/methods/googledrive.php:651
msgid "However, subsequent access attempts failed:"
msgstr ""

#: src/admin.php:4540, src/addons/wp-cli.php:440
msgid "External database"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:321
msgid "This will also cause debugging output from all plugins to be shown upon this screen - please do not be surprised to see these."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:234
msgid "Backup more databases"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:193
msgid "First, enter the decryption key"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:176
msgid "You can manually decrypt an encrypted database here."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:162
msgid "It can also backup external databases."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:162
msgid "Don't want to be spied on? UpdraftPlus Premium can encrypt your database backup."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:88
msgid "use UpdraftPlus Premium"
msgstr ""

#: src/includes/class-wpadmin-commands.php:146
msgid "Only the WordPress database can be restored; you will need to deal with the external database manually."
msgstr ""

#: src/restorer.php:3214, src/restorer.php:3947, src/restorer.php:4037,
#: src/restorer.php:4020
msgid "An error occurred on the first %s command - aborting run"
msgstr ""

#: src/backup.php:1593, src/addons/moredatabase.php:136
msgid "Connection failed: check your access details, that the database server is up, and that the network connection is not firewalled."
msgstr ""

#: src/backup.php:1593
msgid "database connection attempt failed."
msgstr ""

#: src/includes/migrator-lite.php:877
msgid "Warning: the database's home URL (%s) is different to what we expected (%s)"
msgstr ""

#: src/addons/google-enhanced.php:110, src/addons/google-enhanced.php:111,
#: src/addons/google-enhanced.php:128, src/addons/google-enhanced.php:134
msgid "In %s, path names are case sensitive."
msgstr ""

#: src/addons/azure.php:700, src/addons/google-enhanced.php:110,
#: src/addons/google-enhanced.php:128, src/addons/onedrive.php:1322
msgid "If you leave it blank, then the backup will be placed in the root of your %s"
msgstr ""

#: src/addons/google-enhanced.php:110, src/addons/google-enhanced.php:128,
#: src/addons/googlecloud.php:1308, src/addons/onedrive.php:1322
msgid "e.g. %s"
msgstr ""

#: src/addons/google-enhanced.php:110, src/addons/google-enhanced.php:128,
#: src/addons/onedrive.php:1322
msgid "Enter the path of the %s folder you wish to use here."
msgstr ""

#: src/methods/openstack2.php:257
msgid "Container"
msgstr ""

#: src/methods/openstack2.php:253
msgid "Leave this blank, and a default will be chosen."
msgstr ""

#: src/methods/openstack2.php:249
msgid "Tenant"
msgstr ""

#: src/admin.php:1109, src/admin.php:6083, src/restorer.php:392,
#: src/restorer.php:390, src/methods/openstack2.php:251,
#: src/templates/wp-admin/settings/downloading-and-restoring.php:27,
#: src/templates/wp-admin/settings/tab-backups.php:27,
#: src/templates/wp-admin/settings/updraftcentral-connect.php:14
msgid "Follow this link for more information"
msgstr ""

#: src/methods/openstack2.php:130
msgid "authentication URI"
msgstr ""

#: src/methods/addon-base-v2.php:239, src/methods/addon-base-v2.php:259
msgid "Failed to download %s"
msgstr ""

#: src/methods/addon-base-v2.php:253
msgid "Failed to download"
msgstr ""

#: src/methods/addon-base-v2.php:138
msgid "failed to list files"
msgstr ""

#: src/methods/addon-base-v2.php:100, src/methods/addon-base-v2.php:105
msgid "Failed to upload %s"
msgstr ""

#: src/addons/pcloud.php:710, src/methods/dropbox.php:910,
#: src/methods/dropbox.php:912
msgid "Success:"
msgstr ""

#: src/addons/googlecloud.php:1318, src/addons/onedrive.php:1333,
#: src/methods/googledrive.php:1506
msgid "<strong>After</strong> you have saved your settings (by clicking 'Save Changes' below), then come back here once and follow this link to complete authentication with %s."
msgstr ""

#: src/methods/dropbox.php:605
msgid "Dropbox"
msgstr ""

#: src/addons/onedrive.php:1328, src/addons/pcloud.php:549,
#: src/methods/dropbox.php:605
msgid "Authenticate with %s"
msgstr ""

#: src/methods/cloudfiles.php:415, src/methods/openstack-base.php:460
msgid "Error downloading remote file: Failed to download"
msgstr ""

#: src/methods/openstack-base.php:531, src/methods/openstack-base.php:536
msgid "Region: %s"
msgstr ""

#: src/methods/openstack-base.php:530
msgid "%s error - we accessed the container, but failed to create a file within it"
msgstr ""

#: src/methods/openstack-base.php:446
msgid "The %s object was not found"
msgstr ""

#: src/methods/openstack-base.php:56, src/methods/openstack-base.php:369,
#: src/methods/openstack-base.php:438
msgid "Could not access %s container"
msgstr ""

#: src/methods/openstack-base.php:48, src/methods/openstack-base.php:122,
#: src/methods/openstack-base.php:129, src/methods/openstack-base.php:361,
#: src/methods/openstack-base.php:426
msgid "%s error - failed to access the container"
msgstr ""

#: src/addons/googlecloud.php:1341, src/addons/onedrive.php:1355,
#: src/addons/pcloud.php:568, src/methods/dropbox.php:750
msgid "Account holder's name: %s."
msgstr ""

#: src/methods/googledrive.php:1502
msgid "To be able to set a custom folder name, use UpdraftPlus Premium."
msgstr ""

#: src/methods/googledrive.php:1501
msgid "It is an ID number internal to Google Drive"
msgstr ""

#: src/methods/googledrive.php:1501
msgid "<strong>This is NOT a folder name</strong>."
msgstr ""

#: src/addons/google-enhanced.php:126, src/methods/googledrive.php:1493
msgid "Folder"
msgstr ""

#: src/addons/onedrive.php:526
msgid "%s download: failed: file not found"
msgstr ""

#: src/addons/googlecloud.php:904, src/methods/googledrive.php:674
msgid "Name: %s."
msgstr ""

#: src/methods/googledrive.php:339
msgid "Google Drive list files: failed to access parent folder"
msgstr ""

#: src/methods/insufficient.php:121
msgid "Your %s version: %s."
msgstr ""

#: src/methods/addon-not-yet-present.php:127, src/methods/insufficient.php:120
msgid "You will need to ask your web hosting company to upgrade."
msgstr ""

#: src/methods/addon-not-yet-present.php:27, src/methods/insufficient.php:21
msgid "This remote storage method (%s) requires PHP %s or later."
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:118
msgid "Call"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:116,
#: src/templates/wp-admin/advanced/site-info.php:116
msgid "Fetch"
msgstr ""

#: src/addons/migrator.php:315,
#: src/templates/wp-admin/settings/downloading-and-restoring.php:72,
#: src/templates/wp-admin/settings/form-contents.php:183,
#: src/templates/wp-admin/settings/tab-backups.php:74
msgid "This feature requires %s version %s or later"
msgstr ""

#: src/restorer.php:857
msgid "Failed to unpack the archive"
msgstr ""

#: src/class-updraftplus.php:1638, src/methods/cloudfiles.php:428
msgid "Error - failed to download the file"
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:55,
#: src/templates/wp-admin/settings/tab-backups.php:59
msgid "Rescan local folder for new backup sets"
msgstr ""

#: src/udaddons/updraftplus-addons.php:325
msgid "You should update UpdraftPlus to make sure that you have a version that has been tested for compatibility."
msgstr ""

#: src/udaddons/updraftplus-addons.php:325
msgid "It has been tested up to version %s."
msgstr ""

#: src/udaddons/updraftplus-addons.php:325
msgid "The installed version of UpdraftPlus Backup/Restore has not been tested on your version of WordPress (%s)."
msgstr ""

#: src/addons/sftp.php:646
msgid "password/key"
msgstr ""

#: src/admin.php:990, src/addons/migrator.php:554, src/addons/sftp.php:613,
#: src/templates/wp-admin/settings/temporary-clone.php:68
msgid "Key"
msgstr ""

#: src/addons/sftp.php:616
msgid "Your login may be either password or key-based - you only need to enter one, not both."
msgstr ""

#: src/addons/sftp.php:428
msgid "The key provided was not in a valid format, or was corrupt."
msgstr ""

#: src/addons/sftp.php:124
msgid "SCP/SFTP password/key"
msgstr ""

#: src/admin.php:4584, src/addons/wp-cli.php:451
msgid "Files backup (created by %s)"
msgstr ""

#: src/admin.php:4584, src/addons/wp-cli.php:451
msgid "Files and database WordPress backup (created by %s)"
msgstr ""

#: src/admin.php:4578, src/addons/importer.php:278,
#: src/includes/class-backup-history.php:534
msgid "Backup created by: %s."
msgstr ""

#: src/admin.php:4538, src/addons/wp-cli.php:434
msgid "Database (created by %s)"
msgstr ""

#: src/admin.php:4532, src/admin.php:4580, src/addons/wp-cli.php:432
msgid "unknown source"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:122,
#: src/templates/wp-admin/settings/downloading-and-restoring.php:56,
#: src/templates/wp-admin/settings/tab-backups.php:60
msgid "Rescan remote storage"
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:51,
#: src/templates/wp-admin/settings/tab-backups.php:55
msgid "Upload backup files"
msgstr ""

#: src/admin.php:2737
msgid "This backup was created by %s, and can be imported."
msgstr ""

#: src/admin.php:1429
msgid "Read this page for a guide to possible causes and how to fix it."
msgstr ""

#: src/admin.php:951, src/includes/class-backup-history.php:541
msgid "If this is a backup created by a different backup plugin, then UpdraftPlus Premium may be able to help you."
msgstr ""

#: src/admin.php:950
msgid "However, UpdraftPlus archives are standard zip/SQL files - so if you are sure that your file has the right format, then you can rename it to match that pattern."
msgstr ""

#: src/includes/class-backup-history.php:541
msgid "This file does not appear to be an UpdraftPlus backup archive (such files are .zip or .gz files which have a name like: backup_(time)_(site name)_(code)_(type).(zip|gz))."
msgstr ""

#: src/admin.php:4581, src/restorer.php:2769,
#: src/includes/class-wpadmin-commands.php:159
msgid "Backup created by unknown source (%s) - cannot be restored."
msgstr ""

#: src/restorer.php:1742, src/restorer.php:1694
msgid "The WordPress content folder (wp-content) was not found in this zip file."
msgstr ""

#: src/restorer.php:1587
msgid "This version of UpdraftPlus does not know how to handle this type of foreign backup"
msgstr ""

#: src/methods/dropbox.php:406
msgid "%s returned an unexpected HTTP response: %s"
msgstr ""

#: src/addons/sftp.php:1180
msgid "The UpdraftPlus module for this file access method (%s) does not support listing files"
msgstr ""

#: src/addons/backblaze.php:665, src/methods/cloudfiles.php:244,
#: src/methods/dropbox.php:388, src/methods/openstack-base.php:117
msgid "No settings were found"
msgstr ""

#: src/includes/class-backup-history.php:729
msgid "One or more backups has been added from scanning remote storage; note that these backups will not be automatically deleted through the \"retain\" settings; if/when you wish to delete them then you must do so manually."
msgstr ""

#: src/admin.php:915
msgid "Rescanning remote and local storage for backup sets..."
msgstr ""

#: src/addons/googlecloud.php:1312, src/addons/s3-enhanced.php:73,
#: src/addons/s3-enhanced.php:81, src/methods/s3generic.php:203,
#: src/methods/s3generic.php:209
msgid "(Read more)"
msgstr ""

#: src/addons/reporting.php:518
msgid "Log all messages to syslog (only server admins are likely to want this)"
msgstr ""

#: src/addons/morefiles.php:433
msgid "No backup of location: there was nothing found to back up"
msgstr ""

#: src/addons/moredatabase.php:260, src/addons/morefiles.php:334,
#: src/addons/morefiles.php:1056, src/addons/reporting.php:543
msgid "Remove"
msgstr ""

#: src/methods/s3.php:940, src/methods/s3.php:1084
msgid "Other %s FAQs."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:321
msgid "Check this to receive more information and emails on the backup process - useful if something is going wrong."
msgstr ""

#: src/includes/class-filesystem-functions.php:304, src/methods/ftp.php:116
msgid "Your hosting company must enable these functions before %s can work."
msgstr ""

#: src/includes/class-filesystem-functions.php:304, src/methods/ftp.php:116
msgid "Your web server's PHP installation has these functions disabled: %s."
msgstr ""

#: src/methods/ftp.php:113
msgid "encrypted FTP (explicit encryption)"
msgstr ""

#: src/methods/ftp.php:112
msgid "encrypted FTP (implicit encryption)"
msgstr ""

#: src/methods/ftp.php:111
msgid "regular non-encrypted FTP"
msgstr ""

#: src/restorer.php:3041
msgid "Backup created by:"
msgstr ""

#: src/udaddons/options.php:536
msgid "Available to claim on this site"
msgstr ""

#: src/udaddons/updraftplus-addons.php:357
msgid "To maintain your access to support, please renew."
msgstr ""

#: src/udaddons/updraftplus-addons.php:357
msgid "Your paid access to UpdraftPlus support will soon expire."
msgstr ""

#: src/udaddons/updraftplus-addons.php:355
msgid "To regain your access, please renew."
msgstr ""

#: src/udaddons/updraftplus-addons.php:355
msgid "Your paid access to UpdraftPlus support has expired."
msgstr ""

#: src/udaddons/updraftplus-addons.php:346
msgid "Your paid access to UpdraftPlus updates for this site will soon expire."
msgstr ""

#: src/udaddons/updraftplus-addons.php:346,
#: src/udaddons/updraftplus-addons.php:344
msgid "To retain your access, and maintain access to updates (including future features and compatibility with future WordPress releases) and support, please renew."
msgstr ""

#: src/udaddons/updraftplus-addons.php:344
msgid "Your paid access to UpdraftPlus updates for %s of the %s add-ons on this site will soon expire."
msgstr ""

#: src/udaddons/updraftplus-addons.php:338
msgid "Your paid access to UpdraftPlus updates for %s add-ons on this site has expired."
msgstr ""

#: src/udaddons/updraftplus-addons.php:338,
#: src/udaddons/updraftplus-addons.php:332
msgid "To regain access to updates (including future features and compatibility with future WordPress releases) and support, please renew."
msgstr ""

#: src/udaddons/updraftplus-addons.php:294
msgid "Dismiss from main dashboard (for %s weeks)"
msgstr ""

#: src/includes/class-filesystem-functions.php:355
msgid "The attempt to undo the double-compression succeeded."
msgstr ""

#: src/includes/class-filesystem-functions.php:353,
#: src/includes/class-filesystem-functions.php:331
msgid "The attempt to undo the double-compression failed."
msgstr ""

#: src/includes/class-filesystem-functions.php:324
msgid "The database file appears to have been compressed twice - probably the website you downloaded it from had a mis-configured webserver."
msgstr ""

#: src/includes/class-wpadmin-commands.php:399
msgid "Constants"
msgstr ""

#: src/backup.php:1972
msgid "Failed to open database file for reading:"
msgstr ""

#: src/backup.php:1645
msgid "No database tables found"
msgstr ""

#: src/backup.php:1643
msgid "please wait for the rescheduled attempt"
msgstr ""

#: src/addons/onedrive.php:147
msgid "Account full: your %s account has only %d bytes left, but the file to be uploaded has %d bytes remaining (total size: %d bytes)"
msgstr ""

#: src/udaddons/updraftplus-addons.php:658,
#: src/udaddons/updraftplus-addons.php:564
msgid "Errors occurred:"
msgstr ""

#: src/admin.php:5152, src/addons/wp-cli.php:761
msgid "Follow this link to download the log file for this restoration (needed for any support requests)."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:362
msgid "See this FAQ also."
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:36
msgid "Retrieving (if necessary) and preparing backup files..."
msgstr ""

#: src/restorer.php:1579
msgid "Existing unremoved folders from a previous restore exist (please use the \"Delete old folders\" button to delete them before trying again): %s"
msgstr ""

#: src/admin.php:1375, src/class-updraftplus.php:1030
msgid "The amount of time allowed for WordPress plugins to run is very low (%s seconds) - you should increase it to avoid backup failures due to time-outs (consult your web hosting company for more help - it is the max_execution_time PHP setting; the recommended value is %s seconds or more)"
msgstr ""

#: src/includes/migrator-lite.php:209
msgid "Disabled this plugin: %s: re-activate it manually when you are ready."
msgstr ""

#: src/addons/sftp.php:946, src/addons/sftp.php:943,
#: src/includes/ftp.class.php:61, src/includes/ftp.class.php:58
msgid "The %s connection timed out; if you entered the server correctly, then this is usually caused by a firewall blocking the connection - you should check with your web hosting company."
msgstr ""

#: src/admin.php:1934, src/addons/moredatabase.php:168
msgid "Messages:"
msgstr ""

#: src/restorer.php:3821
msgid "An SQL line that is larger than the maximum packet size and cannot be split was found; this line will not be processed, but will be dropped: %s"
msgstr ""

#: src/restorer.php:1258
msgid "The directory does not exist"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:298
msgid "New User's Email Address"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:295
msgid "New User's Username"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:292
msgid "Admin API Key"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:289
msgid "Admin Username"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:284
msgid "US or UK Rackspace Account"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:276
msgid "Enter your Rackspace admin username/API key (so that Rackspace can authenticate your permission to create new users), and enter a new (unique) username and email address for the new user and a container name."
msgstr ""

#: src/addons/cloudfiles-enhanced.php:273
msgid "Create new API user and container"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:205
msgid "API Key: %s"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:205
msgid "Password: %s"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:205, src/addons/s3-enhanced.php:388
msgid "Username: %s"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:164,
#: src/addons/cloudfiles-enhanced.php:167,
#: src/addons/cloudfiles-enhanced.php:171,
#: src/addons/cloudfiles-enhanced.php:183,
#: src/addons/cloudfiles-enhanced.php:190,
#: src/addons/cloudfiles-enhanced.php:194
msgid "Cloud Files operation failed (%s)"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:162
msgid "Conflict: that user or email address already exists"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:94
msgid "You need to enter a valid new email address"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:90
msgid "You need to enter a container"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:87
msgid "You need to enter a new username"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:84
msgid "You need to enter an admin API key"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:81
msgid "You need to enter an admin username"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:67
msgid "Create a new API user with access to only this container (rather than your whole account)"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:305, src/methods/cloudfiles-new.php:129,
#: src/methods/cloudfiles-new.php:298, src/methods/cloudfiles.php:507
msgid "Cloud Files Container"
msgstr ""

#: src/methods/cloudfiles-new.php:124, src/methods/cloudfiles-new.php:296,
#: src/methods/cloudfiles.php:502
msgid "Cloud Files API Key"
msgstr ""

#: src/methods/cloudfiles-new.php:119, src/methods/cloudfiles-new.php:295
msgid "To create a new Rackspace API sub-user and API key that has access only to this Rackspace container, use Premium."
msgstr ""

#: src/methods/cloudfiles-new.php:116, src/methods/cloudfiles-new.php:294
msgid "Cloud Files Username"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:54, src/methods/cloudfiles-new.php:148
msgid "London (LON)"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:53, src/methods/cloudfiles-new.php:147
msgid "Hong Kong (HKG)"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:52, src/methods/cloudfiles-new.php:146
msgid "Northern Virginia (IAD)"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:51, src/methods/cloudfiles-new.php:145
msgid "Chicago (ORD)"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:50, src/methods/cloudfiles-new.php:144
msgid "Sydney (SYD)"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:49, src/methods/cloudfiles-new.php:143
msgid "Dallas (DFW) (default)"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:301, src/methods/cloudfiles-new.php:106,
#: src/methods/cloudfiles-new.php:293
msgid "Cloud Files Storage Region"
msgstr ""

#: src/methods/cloudfiles-new.php:99
msgid "Accounts created at rackspacecloud.com are US-accounts; accounts created at rackspace.co.uk are UK-based"
msgstr ""

#: src/methods/cloudfiles-new.php:97, src/methods/cloudfiles-new.php:287
msgid "US or UK-based Rackspace Account"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:285, src/methods/cloudfiles-new.php:97,
#: src/methods/cloudfiles-new.php:288
msgid "Accounts created at rackspacecloud.com are US accounts; accounts created at rackspace.co.uk are UK accounts."
msgstr ""

#: src/addons/cloudfiles-enhanced.php:160, src/addons/s3-enhanced.php:300,
#: src/methods/cloudfiles-new.php:37, src/methods/openstack-base.php:491,
#: src/methods/openstack-base.php:489, src/methods/openstack-base.php:512,
#: src/methods/openstack2.php:33
msgid "Authorisation failed (check your credentials)"
msgstr ""

#: src/addons/wp-cli.php:1082, src/includes/class-commands.php:908,
#: src/methods/updraftvault.php:844, src/udaddons/options.php:244
msgid "An unknown error occurred when trying to connect to UpdraftPlus.Com"
msgstr ""

#: src/admin.php:965
msgid "Create"
msgstr ""

#: src/admin.php:925
msgid "Trying..."
msgstr ""

#: src/admin.php:924
msgid "The new user's RackSpace console password is (this will not be shown again):"
msgstr ""

#: src/admin.php:935, src/restorer.php:386
msgid "Error data:"
msgstr ""

#: src/admin.php:5245
msgid "Backup does not exist in the backup history"
msgstr ""

#: src/restorer.php:3183
msgid "Split line to avoid exceeding maximum packet size"
msgstr ""

#: src/restorer.php:851
msgid "Could not move old files out of the way."
msgstr ""

#: src/restorer.php:847
msgid "Moving old data out of the way..."
msgstr ""

#: src/addons/reporting.php:503
msgid "Add another address..."
msgstr ""

#: src/addons/reporting.php:488
msgid "Enter addresses here to have a report sent to them when a backup job finishes."
msgstr ""

#: src/class-updraftplus.php:2054, src/class-updraftplus.php:2059
msgid "%s checksum: %s"
msgstr ""

#: src/class-updraftplus.php:1988, src/class-updraftplus.php:1986
msgid "files: %s"
msgstr ""

#: src/addons/reporting.php:414
msgid "Use the \"Reporting\" section to configure the email addresses to be used."
msgstr ""

#: src/addons/reporting.php:293
msgid "Debugging information"
msgstr ""

#: src/admin.php:4498, src/addons/reporting.php:248
msgid "Uploaded to:"
msgstr ""

#: src/addons/reporting.php:247
msgid "Time taken:"
msgstr ""

#: src/addons/reporting.php:238
msgid "Warnings"
msgstr ""

#: src/addons/reporting.php:223
msgid "Errors"
msgstr ""

#: src/addons/reporting.php:220
msgid "Errors / warnings:"
msgstr ""

#: src/addons/morefiles.php:144, src/addons/morefiles.php:145,
#: src/addons/reporting.php:209
msgid "Contains:"
msgstr ""

#: src/addons/reporting.php:208
msgid "Backup began:"
msgstr ""

#: src/addons/reporting.php:196
msgid "Backup Report"
msgstr ""

#: src/addons/reporting.php:191
msgid "%d hours, %d minutes, %d seconds"
msgstr ""

#: src/addons/reporting.php:175
msgid "%d errors, %d warnings"
msgstr ""

#: src/methods/dropbox.php:816, src/methods/dropbox.php:873
msgid "%s authentication"
msgstr ""

#: src/class-updraftplus.php:611, src/addons/onedrive.php:924,
#: src/addons/pcloud.php:705, src/methods/dropbox.php:816,
#: src/methods/dropbox.php:873, src/methods/dropbox.php:887,
#: src/methods/dropbox.php:907, src/methods/dropbox.php:1073
msgid "%s error: %s"
msgstr ""

#: src/addons/googlecloud.php:1294, src/methods/dropbox.php:599
msgid "%s logo"
msgstr ""

#: src/methods/dropbox.php:316
msgid "did not return the expected response - check your log file for more details"
msgstr ""

#: src/methods/s3.php:336
msgid "The required %s PHP module is not installed - ask your web hosting company to enable it"
msgstr ""

#: src/methods/email.php:110
msgid "Your site's admin email address (%s) will be used."
msgstr ""

#: src/admin.php:977, src/admin.php:3332, src/methods/updraftvault.php:484,
#: src/methods/updraftvault.php:525,
#: src/templates/wp-admin/settings/temporary-clone.php:87
msgid "Connect"
msgstr ""

#: src/class-updraftplus.php:5140
msgid "(version: %s)"
msgstr ""

#: src/admin.php:912
msgid "Be aware that mail servers tend to have size limits; typically around %s Mb; backups larger than any limits will likely not arrive."
msgstr ""

#: src/class-updraftplus.php:3874, src/addons/reporting.php:207
msgid "Latest status:"
msgstr ""

#: src/class-updraftplus.php:3873
msgid "Backup contains:"
msgstr ""

#: src/class-updraftplus.php:3830
msgid "Backed up: %s"
msgstr ""

#: src/class-updraftplus.php:3824, src/class-updraftplus.php:3999,
#: src/addons/reporting.php:290
msgid "The log file has been attached to this email."
msgstr ""

#: src/class-updraftplus.php:3784
msgid "Unknown/unexpected error - please raise a support request"
msgstr ""

#: src/class-updraftplus.php:3779
msgid "Database only (files were not part of this particular schedule)"
msgstr ""

#: src/class-updraftplus.php:3779
msgid "Database (files backup has not completed)"
msgstr ""

#: src/class-updraftplus.php:3776
msgid "Files only (database was not part of this particular schedule)"
msgstr ""

#: src/class-updraftplus.php:3776
msgid "Files (database backup has not completed)"
msgstr ""

#: src/admin.php:431, src/class-updraftplus.php:3774
msgid "Files and database"
msgstr ""

#: src/options.php:229
msgid "(This applies to all WordPress backup plugins unless they have been explicitly coded for multisite compatibility)."
msgstr ""

#: src/options.php:229
msgid "Without upgrading, UpdraftPlus allows <strong>every</strong> blog admin who can modify plugin settings to backup (and hence access the data, including passwords, from) and restore (including with customized modifications, e.g. changed passwords) <strong>the entire network</strong>."
msgstr ""

#: src/options.php:229
msgid "This is a WordPress multi-site (a.k.a. network) installation."
msgstr ""

#: src/options.php:229
msgid "UpdraftPlus warning:"
msgstr ""

#: src/udaddons/options.php:542, src/udaddons/options.php:542
msgid "(or connect using the form on this page if you have already purchased it)"
msgstr ""

#: src/udaddons/options.php:530
msgid "please  follow this link to update the plugin in order to activate it"
msgstr ""

#: src/udaddons/options.php:527
msgid "please follow this link to update the plugin in order to get it"
msgstr ""

#: src/udaddons/options.php:519, src/udaddons/options.php:517
msgid "latest"
msgstr ""

#: src/udaddons/options.php:515
msgid "Your version: %s"
msgstr ""

#: src/udaddons/options.php:513, src/udaddons/options.php:513
msgid "You've got it"
msgstr ""

#: src/udaddons/options.php:468
msgid "UpdraftPlus Support"
msgstr ""

#: src/udaddons/options.php:406
msgid "An update containing your addons is available for UpdraftPlus - please follow this link to get it."
msgstr ""

#: src/udaddons/options.php:395, src/udaddons/updraftplus-addons.php:384
msgid "UpdraftPlus Addons"
msgstr ""

#: src/udaddons/options.php:110
msgid "An update is available for UpdraftPlus - please follow this link to get it."
msgstr ""

#: src/methods/updraftvault.php:924, src/methods/updraftvault.php:947,
#: src/methods/updraftvault.php:950, src/udaddons/updraftplus-addons.php:1099
msgid "UpdraftPlus.Com returned a response, but we could not understand it"
msgstr ""

#: src/methods/updraftvault.php:936, src/udaddons/updraftplus-addons.php:1095
msgid "Your email address and password were not recognised by UpdraftPlus.Com"
msgstr ""

#: src/includes/updraftplus-login.php:57, src/methods/updraftvault.php:897,
#: src/udaddons/updraftplus-addons.php:1058
msgid "UpdraftPlus.Com returned a response which we could not understand (data: %s)"
msgstr ""

#: src/udaddons/updraftplus-addons.php:984
msgid "UpdraftPlus.Com responded, but we did not understand the response"
msgstr ""

#: src/udaddons/updraftplus-addons.php:982
msgid "We failed to successfully connect to UpdraftPlus.Com"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:244,
#: src/templates/wp-admin/settings/tab-addons.php:243,
#: src/templates/wp-admin/settings/tab-addons.php:244
msgid "Reporting"
msgstr ""

#: src/admin.php:5927
msgid "Options (raw)"
msgstr ""

#: src/admin.php:910, src/addons/reporting.php:547
msgid "Send a report only when there are warnings/errors"
msgstr ""

#: src/restorer.php:3052
msgid "Content URL:"
msgstr ""

#: src/restorer.php:851
msgid "You should check the file ownerships and permissions in your WordPress installation"
msgstr ""

#: src/backup.php:4376, src/class-updraftplus.php:1043
msgid "Your free space in your hosting account is very low - only %s Mb remain"
msgstr ""

#: src/class-updraftplus.php:1027
msgid "The amount of memory (RAM) allowed for PHP is very low (%s Mb) - you should increase it to avoid failures due to insufficient memory (consult your web hosting company for more help)"
msgstr ""

#: src/udaddons/options.php:562
msgid "Manage Addons"
msgstr ""

#: src/udaddons/options.php:543
msgid "Buy It"
msgstr ""

#: src/udaddons/options.php:542
msgid "Get it from the UpdraftPlus.Com Store"
msgstr ""

#: src/udaddons/options.php:538, src/udaddons/options.php:536
msgid "activate it on this site"
msgstr ""

#: src/udaddons/options.php:538
msgid "You have an inactive purchase"
msgstr ""

#: src/udaddons/options.php:530
msgid "Assigned to this site"
msgstr ""

#: src/udaddons/options.php:527
msgid "Available for this site (via your all-addons purchase)"
msgstr ""

#: src/udaddons/options.php:521
msgid "(apparently a pre-release or withdrawn release)"
msgstr ""

#: src/udaddons/options.php:470, src/udaddons/options.php:470
msgid "Go here"
msgstr ""

#: src/udaddons/options.php:470, src/udaddons/options.php:470
msgid "Need to get support?"
msgstr ""

#: src/udaddons/options.php:452
msgid "An error occurred when trying to retrieve your add-ons."
msgstr ""

#: src/udaddons/options.php:337
msgid "Claim not granted - your account login details were wrong"
msgstr ""

#: src/udaddons/options.php:335
msgid "Please wait whilst we make the claim..."
msgstr ""

#: src/udaddons/options.php:283
msgid "Errors occurred when trying to connect to UpdraftPlus.Com:"
msgstr ""

#: src/udaddons/options.php:278
msgid "You are presently <strong>not connected</strong> to an UpdraftPlus.Com account."
msgstr ""

#: src/udaddons/options.php:270
msgid "If you bought new add-ons, then follow this link to refresh your connection"
msgstr ""

#: src/udaddons/options.php:263
msgid "You are presently <strong class=\"success\">connected</strong> to an UpdraftPlus.Com account."
msgstr ""

#: src/admin.php:3330
msgid "Interested in knowing about your UpdraftPlus.Com password security? Read about it here."
msgstr ""

#: src/admin.php:3406
msgid "Forgotten your details?"
msgstr ""

#: src/admin.php:3319
msgid "Not yet got an account (it's free)? Go get one!"
msgstr ""

#: src/admin.php:3385
msgid "Connect with your UpdraftPlus.Com account"
msgstr ""

#: src/udaddons/options.php:134
msgid "You do seem to have the obsolete Updraft plugin installed - perhaps you got them confused?"
msgstr ""

#: src/udaddons/options.php:133
msgid "Go here to begin installing it."
msgstr ""

#: src/udaddons/options.php:133
msgid "UpdraftPlus is not yet installed."
msgstr ""

#: src/udaddons/options.php:130
msgid "Go here to activate it."
msgstr ""

#: src/udaddons/options.php:129
msgid "UpdraftPlus is not yet activated."
msgstr ""

#: src/udaddons/options.php:122, src/udaddons/options.php:120
msgid "Go here to connect."
msgstr ""

#: src/udaddons/options.php:120
msgid "You have not yet connected with your UpdraftPlus.Com account, to enable you to list your purchased add-ons."
msgstr ""

#: src/addons/moredatabase.php:337,
#: src/includes/class-updraftplus-encryption.php:148
msgid "Without it, encryption will be a lot slower."
msgstr ""

#: src/addons/moredatabase.php:337,
#: src/includes/class-updraftplus-encryption.php:148
msgid "Your web-server does not have the %s module installed."
msgstr ""

#: src/methods/googledrive.php:1508
msgid "<strong>(You appear to be already authenticated,</strong> though you can authenticate again to refresh your access if you've had a problem)."
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:78
msgid "Drop backup files here"
msgstr ""

#: src/admin.php:923
msgid "The web server returned an error code (try again, or check your web server logs)"
msgstr ""

#: src/admin.php:917, src/addons/wp-cli.php:117
msgid "If you exclude both the database and the files, then you have excluded everything!"
msgstr ""

#: src/restorer.php:3046
msgid "Site home:"
msgstr ""

#: src/addons/morestorage.php:231
msgid "Remote Storage Options"
msgstr ""

#: src/addons/autobackup.php:371, src/addons/autobackup.php:465
msgid "(logs can be found in the UpdraftPlus settings page as normal)..."
msgstr ""

#: src/addons/autobackup.php:330, src/addons/autobackup.php:1193
msgid "Remember this choice for next time (you will still have the chance to change it)"
msgstr ""

#: src/addons/azure.php:454
msgid "Upload failed"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:126
msgid "You can send a backup to more than one destination with Premium."
msgstr ""

#: src/addons/autobackup.php:331, src/addons/autobackup.php:1198,
#: src/addons/lockadmin.php:167
msgid "Read more about how this works..."
msgstr ""

#: src/addons/sftp.php:723
msgid "Failed: We were able to log in, but failed to successfully create a file in that location."
msgstr ""

#: src/addons/sftp.php:721
msgid "Failed: We were able to log in and move to the indicated directory, but failed to successfully create a file in that location."
msgstr ""

#: src/addons/sftp.php:622
msgid "Use SCP instead of SFTP"
msgstr ""

#: src/addons/sftp.php:123
msgid "SCP/SFTP user setting"
msgstr ""

#: src/addons/sftp.php:122
msgid "SCP/SFTP host setting"
msgstr ""

#: src/methods/email.php:80
msgid "The attempt to send the backup via email failed (probably the backup was too large for this method)"
msgstr ""

#: src/methods/email.php:57
msgid "Backup is of: %s."
msgstr ""

#: src/admin.php:1017
msgid "%s settings test result:"
msgstr ""

#: src/admin.php:4632, src/admin.php:4630
msgid "(Not finished)"
msgstr ""

#: src/admin.php:4632
msgid "If you are seeing more backups than you expect, then it is probably because the deletion of old backup sets does not happen until a fresh backup completes."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:345
msgid "<b>Do not</b> place it inside your uploads or plugins directory, as that will cause recursion (backups of backups of backups of...)."
msgstr ""

#: src/admin.php:3843
msgid "Job ID: %s"
msgstr ""

#: src/admin.php:3823
msgid "last activity: %ss ago"
msgstr ""

#: src/admin.php:3805, src/methods/updraftvault.php:557,
#: src/methods/updraftvault.php:604, src/methods/updraftvault.php:724
msgid "Unknown"
msgstr ""

#: src/admin.php:3756
msgid "Backup finished"
msgstr ""

#: src/admin.php:3751
msgid "Waiting until scheduled time to retry because of errors"
msgstr ""

#: src/admin.php:3747
msgid "Pruning old backup sets"
msgstr ""

#: src/admin.php:3734
msgid "Uploading files to remote storage"
msgstr ""

#: src/admin.php:3803
msgid "Encrypted database"
msgstr ""

#: src/admin.php:3795
msgid "Encrypting database"
msgstr ""

#: src/admin.php:3769
msgid "Created database backup"
msgstr ""

#: src/admin.php:3782
msgid "table: %s"
msgstr ""

#: src/admin.php:3780
msgid "Creating database backup"
msgstr ""

#: src/admin.php:3724
msgid "Created file backup zips"
msgstr ""

#: src/admin.php:3711
msgid "Creating file backup zips"
msgstr ""

#: src/admin.php:3706
msgid "Backup begun"
msgstr ""

#: src/restorer.php:1563
msgid "file"
msgstr ""

#: src/restorer.php:1555, src/addons/onedrive.php:1321
msgid "folder"
msgstr ""

#: src/restorer.php:1555, src/restorer.php:1563
msgid "UpdraftPlus needed to create a %s in your content directory, but failed - please check your file permissions and enable the access (%s)"
msgstr ""

#: src/class-updraftplus.php:3694
msgid "The backup has not finished; a resumption is scheduled"
msgstr ""

#: src/class-updraftplus.php:2388
msgid "Your website is visited infrequently and UpdraftPlus is not getting the resources it hoped for; please read this page:"
msgstr ""

#: src/admin.php:970, src/addons/autobackup.php:1212
msgid "Proceed with update"
msgstr ""

#: src/addons/autobackup.php:1205
msgid "Do not abort after pressing Proceed below - wait for the backup to complete."
msgstr ""

#: src/addons/autobackup.php:143, src/addons/autobackup.php:1154
msgid "UpdraftPlus Automatic Backups"
msgstr ""

#: src/addons/autobackup.php:558
msgid "Errors have occurred:"
msgstr ""

#: src/addons/autobackup.php:529
msgid "Creating backup with UpdraftPlus..."
msgstr ""

#: src/addons/autobackup.php:474, src/addons/autobackup.php:605,
#: src/addons/autobackup.php:656
msgid "Automatic Backup"
msgstr ""

#: src/addons/autobackup.php:465
msgid "Creating database backup with UpdraftPlus..."
msgstr ""

#: src/addons/autobackup.php:431
msgid "themes"
msgstr ""

#: src/addons/autobackup.php:424
msgid "plugins"
msgstr ""

#: src/addons/autobackup.php:375, src/addons/autobackup.php:472
msgid "Starting automatic backup..."
msgstr ""

#: src/addons/autobackup.php:371
msgid "Creating %s and database backup with UpdraftPlus..."
msgstr ""

#: src/addons/autobackup.php:328
msgid "Automatically backup (where relevant) plugins, themes and the WordPress database with UpdraftPlus before updating"
msgstr ""

#: src/addons/morefiles.php:271, src/addons/morefiles.php:272
msgid "If you are not sure then you should stop; otherwise you may destroy this WordPress installation."
msgstr ""

#: src/addons/morefiles.php:271, src/addons/morefiles.php:272
msgid "This does not look like a valid WordPress core backup - the file %s was missing."
msgstr ""

#: src/addons/morefiles.php:213
msgid "Unable to open zip file (%s) - could not pre-scan it to check its integrity."
msgstr ""

#: src/addons/morefiles.php:203
msgid "Unable to read zip file (%s) - could not pre-scan it to check its integrity."
msgstr ""

#: src/templates/wp-admin/settings/header.php:25
msgid "More plugins"
msgstr ""

#: src/includes/updraftplus-notices.php:37,
#: src/templates/wp-admin/settings/header.php:17,
#: src/templates/wp-admin/settings/tab-addons.php:28,
#: src/templates/wp-admin/settings/tab-addons.php:139
msgid "Support"
msgstr ""

#: src/class-updraftplus.php:5481
msgid "UpdraftPlus was unable to find the table prefix when scanning the database backup."
msgstr ""

#: src/class-updraftplus.php:5473
msgid "This database backup is missing core WordPress tables: %s"
msgstr ""

#: src/class-updraftplus.php:5068
msgid "The database is too small to be a valid WordPress database (size: %s Kb)."
msgstr ""

#: src/addons/autobackup.php:326, src/addons/autobackup.php:1180
msgid "Be safe with an automatic backup"
msgstr ""

#: src/admin.php:2990
msgid "If you can still read these words after the page finishes loading, then there is a JavaScript or jQuery problem in the site."
msgstr ""

#: src/admin.php:960
msgid "The file was uploaded."
msgstr ""

#: src/admin.php:959
msgid "Unknown server response status:"
msgstr ""

#: src/admin.php:958
msgid "Unknown server response:"
msgstr ""

#: src/admin.php:957
msgid "This decryption key will be attempted:"
msgstr ""

#: src/admin.php:956
msgid "Follow this link to attempt decryption and download the database file to your computer."
msgstr ""

#: src/admin.php:955
msgid "Upload error"
msgstr ""

#: src/admin.php:954
msgid "This file is not an UpdraftPlus encrypted database archive (such files are .gz.crypt files which have a name like: backup_(time)_(site name)_(code)_db.crypt.gz)."
msgstr ""

#: src/admin.php:953
msgid "Upload error:"
msgstr ""

#: src/admin.php:952
msgid "(make sure that you were trying to upload a zip file previously created by UpdraftPlus)"
msgstr ""

#: src/admin.php:943
msgid "Download to your computer"
msgstr ""

#: src/admin.php:942
msgid "Delete from your web server"
msgstr ""

#: src/methods/s3generic.php:191
msgid "Examples of S3-compatible storage providers:"
msgstr ""

#: src/admin.php:4608
msgid "You are missing one or more archives from this multi-archive set."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:325
msgid "Split archives every:"
msgstr ""

#: src/addons/moredatabase.php:297
msgid "Error: the server sent us a response (JSON) which we did not understand."
msgstr ""

#: src/admin.php:932
msgid "Warnings:"
msgstr ""

#: src/admin.php:931
msgid "Error: the server sent an empty response."
msgstr ""

#: src/includes/class-wpadmin-commands.php:205
msgid "This multi-archive backup set appears to have the following archives missing: %s"
msgstr ""

#: src/includes/class-wpadmin-commands.php:190
msgid "File (%s) was found, but has a different size (%s) from what was expected (%s) - it may be corrupt."
msgstr ""

#: src/includes/class-wpadmin-commands.php:185
msgid "File was found, but is zero-sized (you need to re-upload it): %s"
msgstr ""

#: src/includes/class-wpadmin-commands.php:183
msgid "File not found (you need to upload it): %s"
msgstr ""

#: src/addons/wp-cli.php:718, src/includes/class-wpadmin-commands.php:111
msgid "No such backup set exists"
msgstr ""

#: src/restorer.php:848
msgid "Moving unpacked backup into place..."
msgstr ""

#: src/backup.php:3998, src/backup.php:4329
msgid "Failed to open the zip file (%s) - %s"
msgstr ""

#: src/addons/morefiles.php:183
msgid "WordPress root directory server path: %s"
msgstr ""

#: src/methods/dreamobjects.php:193, src/methods/s3generic.php:201
msgid "%s end-point"
msgstr ""

#: src/methods/s3generic.php:191
msgid "... and many more!"
msgstr ""

#: src/methods/s3generic.php:80
msgid "S3 (Compatible)"
msgstr ""

#: src/includes/class-storage-methods-interface.php:301
msgid "File is not locally present - needs retrieving from remote storage"
msgstr ""

#: src/restorer.php:519
msgid "Looking for %s archive: file name: %s"
msgstr ""

#: src/restorer.php:635
msgid "Final checks"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:331
msgid "Check this to delete any superfluous backup files from your server after the backup run finishes (i.e. if you uncheck, then any files despatched remotely will also remain locally, and any files being kept locally will not be subject to the retention limits)."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:190
msgid "Drop encrypted database files (db.gz.crypt files) here to upload them for decryption"
msgstr ""

#: src/admin.php:4319
msgid "Your wp-content directory server path: %s"
msgstr ""

#: src/admin.php:949
msgid "Raw backup history"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:122
msgid "Show raw backup and file list"
msgstr ""

#: src/admin.php:930
msgid "Processing files - please wait..."
msgstr ""

#: src/class-updraftplus.php:5082
msgid "Failed to open database file."
msgstr ""

#: src/admin.php:5892
msgid "Known backups (raw)"
msgstr ""

#: src/restorer.php:1835
msgid "Files found:"
msgstr ""

#: src/admin.php:1101
msgid "Restoring table: %s"
msgstr ""

#: src/restorer.php:2528
msgid "Requested table engine (%s) is not present - changing to MyISAM."
msgstr ""

#: src/restorer.php:539
msgid "file is size:"
msgstr ""

#: src/admin.php:1380, src/admin.php:1398, src/admin.php:2995,
#: src/admin.php:5124, src/backup.php:4383, src/class-updraftplus.php:5334,
#: src/class-updraftplus.php:5334, src/updraftplus.php:226,
#: src/addons/googlecloud.php:1305, src/addons/migrator.php:304,
#: src/addons/migrator.php:307, src/addons/migrator.php:310,
#: src/templates/wp-admin/advanced/db-size.php:19
msgid "Go here for more information."
msgstr ""

#: src/admin.php:5123
msgid "Warning: If you can still read these words after the page finishes loading, then there is a JavaScript or jQuery problem in the site."
msgstr ""

#: src/admin.php:929
msgid "Some files are still downloading or being processed - please wait."
msgstr ""

#: src/addons/fixtime.php:570
msgid "The time zone used is that from your WordPress settings, in Settings -> General."
msgstr ""

#: src/addons/fixtime.php:570
msgid "Enter in format HH:MM (e.g. 14:22)."
msgstr ""

#: src/methods/ftp.php:208
msgid "%s login failure"
msgstr ""

#: src/addons/pcloud.php:339, src/addons/pcloud.php:440,
#: src/methods/dropbox.php:490
msgid "Failed to access %s when deleting (see log file for more)"
msgstr ""

#: src/methods/cloudfiles.php:424
msgid "Error - no such file exists."
msgstr ""

#: src/addons/azure.php:301, src/addons/webdav.php:788,
#: src/addons/webdav.php:795, src/addons/webdav.php:815,
#: src/methods/addon-base-v2.php:253, src/methods/openstack-base.php:460
msgid "%s Error"
msgstr ""

#: src/methods/openstack-base.php:86
msgid "%s error - failed to upload file"
msgstr ""

#: src/class-updraftplus.php:1527
msgid "%s error - failed to re-assemble chunks"
msgstr ""

#: src/methods/cloudfiles.php:250, src/methods/openstack-base.php:44,
#: src/methods/openstack-base.php:357, src/methods/openstack-base.php:422,
#: src/methods/openstack-base.php:495, src/methods/openstack-base.php:498,
#: src/methods/openstack-base.php:516, src/methods/openstack-base.php:521
msgid "%s authentication failed"
msgstr ""

#: src/admin.php:2722, src/admin.php:2751, src/admin.php:2743,
#: src/class-updraftplus.php:1230, src/class-updraftplus.php:1236,
#: src/class-updraftplus.php:5051, src/class-updraftplus.php:5049,
#: src/class-updraftplus.php:5309, src/class-updraftplus.php:5232,
#: src/addons/googlecloud.php:512, src/addons/migrator.php:390,
#: src/methods/googledrive.php:592, src/methods/s3.php:370
msgid "Error: %s"
msgstr ""

#: src/admin.php:4233
msgid "Backup directory specified exists, but is <b>not</b> writable."
msgstr ""

#: src/admin.php:4231
msgid "Backup directory specified does <b>not</b> exist."
msgstr ""

#: src/admin.php:3857, src/admin.php:4172
msgid "Warning: %s"
msgstr ""

#: src/backup.php:4032
msgid "A very large file was encountered: %s (size: %s Mb)"
msgstr ""

#: src/includes/class-wpadmin-commands.php:586
msgid "Failed to open directory: %s."
msgstr ""

#: src/backup.php:3217
msgid "%s: unreadable file - could not be backed up"
msgstr ""

#: src/backup.php:2113
msgid "Table %s has very many rows (%s) - we hope your web hosting company gives you enough resources to dump out that table in the backup."
msgstr ""

#: src/backup.php:2066
msgid "An error occurred whilst closing the final database file"
msgstr ""

#: src/class-updraftplus.php:3814
msgid "Warnings encountered:"
msgstr ""

#: src/class-updraftplus.php:3673
msgid "The backup succeeded (with warnings) and is now complete"
msgstr ""

#: src/class-updraftplus.php:1056
msgid "Your free disk space is very low - only %s Mb remain"
msgstr ""

#: src/addons/migrator.php:398
msgid "New site:"
msgstr ""

#: src/addons/migrator.php:375
msgid "Migrated site (from UpdraftPlus)"
msgstr ""

#: src/addons/migrator.php:324
msgid "Enter details for where this new site is to live within your multisite install:"
msgstr ""

#: src/addons/migrator.php:323
msgid "Information needed to continue:"
msgstr ""

#: src/addons/migrator.php:266
msgid "Network activating theme:"
msgstr ""

#: src/includes/migrator-lite.php:350
msgid "Processed plugin:"
msgstr ""

#: src/addons/sftp.php:149
msgid "Check your file permissions: Could not successfully create and enter directory:"
msgstr ""

#: src/methods/s3.php:1461
msgid "Please check your access credentials."
msgstr ""

#: src/addons/s3-enhanced.php:286, src/methods/s3.php:1438
msgid "The error reported by %s was:"
msgstr ""

#: src/restorer.php:2323
msgid "Please supply the requested information, and then continue."
msgstr ""

#: src/class-updraftplus.php:5243, src/restorer.php:3081
msgid "Site information:"
msgstr ""

#: src/admin.php:1109, src/admin.php:2990, src/class-updraftplus.php:5236,
#: src/restorer.php:3821
msgid "Warning:"
msgstr ""

#: src/class-updraftplus.php:5228, src/restorer.php:856
msgid "You are running on WordPress multisite - but your backup is not of a multisite site."
msgstr ""

#: src/admin.php:971, src/admin.php:1163, src/includes/updraftplus-tour.php:97
msgid "Close"
msgstr ""

#: src/admin.php:922, src/addons/autobackup.php:377,
#: src/addons/autobackup.php:469, src/methods/remotesend.php:75,
#: src/methods/remotesend.php:83, src/methods/remotesend.php:245,
#: src/methods/remotesend.php:261
msgid "Unexpected response:"
msgstr ""

#: src/admin.php:916, src/addons/reporting.php:545
msgid "To send to more than one address, separate each address with a comma."
msgstr ""

#: src/admin.php:947
msgid "PHP information"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:88
msgid "zip executable found:"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:58
msgid "show PHP information (phpinfo)"
msgstr ""

#: src/templates/wp-admin/settings/migrator-no-migrator.php:9
msgid "Do you want to migrate or clone/duplicate a site?"
msgstr ""

#: src/templates/wp-admin/settings/existing-backups-table.php:173
msgid "Please allow time for the communications with the remote storage to complete."
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:23
msgid "Also delete from remote storage"
msgstr ""

#: src/admin.php:3526
msgid "Latest UpdraftPlus.com news:"
msgstr ""

#: src/templates/wp-admin/settings/header.php:11,
#: src/templates/wp-admin/settings/tab-addons.php:73
msgid "Premium"
msgstr ""

#: src/templates/wp-admin/settings/header.php:15
msgid "News"
msgstr ""

#: src/admin.php:2000, src/includes/class-wpadmin-commands.php:614
msgid "Backup set not found"
msgstr ""

#: src/backup.php:290
msgid "%s - could not back this entity up; the corresponding directory does not exist (%s)"
msgstr ""

#: src/includes/updraftplus-notices.php:167,
#: src/includes/updraftplus-notices.php:168,
#: src/includes/updraftplus-notices.php:177,
#: src/includes/updraftplus-notices.php:178
msgid "RSS link"
msgstr ""

#: src/includes/updraftplus-notices.php:167,
#: src/includes/updraftplus-notices.php:168,
#: src/includes/updraftplus-notices.php:177,
#: src/includes/updraftplus-notices.php:178
msgid "Blog link"
msgstr ""

#: src/admin.php:1016
msgid "Testing %s Settings..."
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:69,
#: src/templates/wp-admin/settings/tab-backups.php:71
msgid "Or, you can place them manually into your UpdraftPlus directory (usually wp-content/updraft), e.g. via FTP, and then use the \"rescan\" link above."
msgstr ""

#: src/admin.php:1402, src/admin.php:1484
msgid "Notice"
msgstr ""

#: src/class-updraftplus.php:3793
msgid "Errors encountered:"
msgstr ""

#: src/admin.php:913
msgid "Rescanning (looking for backups that you have uploaded manually into the internal backup store)..."
msgstr ""

#: src/admin.php:928
msgid "Begun looking for this entity"
msgstr ""

#: src/addons/dropbox-folders.php:38, src/addons/pcloud.php:511
msgid "Store at"
msgstr ""

#: src/includes/class-search-replace.php:333
msgid "\"%s\" has no primary key, manual change needed on row %s."
msgstr ""

#: src/includes/class-search-replace.php:140
msgid "rows: %d"
msgstr ""

#: src/includes/migrator-lite.php:1037
msgid "Time taken (seconds):"
msgstr ""

#: src/admin.php:933, src/includes/migrator-lite.php:1036
msgid "Errors:"
msgstr ""

#: src/includes/migrator-lite.php:1035
msgid "SQL update commands run:"
msgstr ""

#: src/includes/migrator-lite.php:1034
msgid "Changes made:"
msgstr ""

#: src/includes/migrator-lite.php:1033
msgid "Rows examined:"
msgstr ""

#: src/includes/migrator-lite.php:1032
msgid "Tables examined:"
msgstr ""

#: src/includes/migrator-lite.php:924
msgid "Could not get list of tables"
msgstr ""

#: src/includes/migrator-lite.php:869
msgid "Warning: the database's site URL (%s) is different to what we expected (%s)"
msgstr ""

#: src/includes/migrator-lite.php:858
msgid "Nothing to do: the site URL is already: %s"
msgstr ""

#: src/includes/migrator-lite.php:821, src/includes/migrator-lite.php:825,
#: src/includes/migrator-lite.php:829, src/includes/migrator-lite.php:834,
#: src/includes/migrator-lite.php:838, src/includes/migrator-lite.php:843
msgid "Error: unexpected empty parameter (%s, %s)"
msgstr ""

#: src/includes/migrator-lite.php:779
msgid "Database: search and replace site URL"
msgstr ""

#: src/restorer.php:4382, src/includes/migrator-lite.php:629,
#: src/includes/migrator-lite.php:1018
msgid "Failed: we did not understand the result returned by the %s operation."
msgstr ""

#: src/restorer.php:4380, src/includes/migrator-lite.php:627,
#: src/includes/migrator-lite.php:1016
msgid "Failed: the %s operation was not able to start."
msgstr ""

#: src/includes/migrator-lite.php:372
msgid "Search and replace site location in the database (migrate)"
msgstr ""

#: src/includes/migrator-lite.php:372
msgid "All references to the site location in the database will be replaced with your current site URL, which is: %s"
msgstr ""

#: src/addons/multisite.php:779
msgid "Blog uploads"
msgstr ""

#: src/class-updraftplus.php:2093, src/addons/migrator.php:310
msgid "Must-use plugins"
msgstr ""

#: src/addons/multisite.php:229
msgid "Multisite Install"
msgstr ""

#: src/addons/fixtime.php:570
msgid "starting from next time it is"
msgstr ""

#: src/addons/sftp.php:713
msgid "However, we were able to log in and move to the indicated directory and successfully create a file in that location."
msgstr ""

#: src/addons/sftp.php:651
msgid "Failure: Port must be an integer."
msgstr ""

#: src/methods/ftp.php:442, src/methods/openstack2.php:120
msgid "password"
msgstr ""

#: src/addons/sftp.php:642, src/methods/openstack2.php:115
msgid "username"
msgstr ""

#: src/addons/sftp.php:638
msgid "host name"
msgstr ""

#: src/addons/sftp.php:621
msgid "Where to change directory to after logging in - often this is relative to your home directory."
msgstr ""

#: src/addons/sftp.php:615
msgid "Directory path"
msgstr ""

#: src/admin.php:1097, src/admin.php:3401, src/addons/lockadmin.php:178,
#: src/addons/moredatabase.php:265, src/addons/sftp.php:612,
#: src/addons/webdav.php:431, src/methods/openstack2.php:255,
#: src/methods/updraftvault.php:523,
#: src/templates/wp-admin/settings/updraftcentral-connect.php:50
msgid "Password"
msgstr ""

#: src/addons/sftp.php:610, src/addons/webdav.php:435
msgid "Port"
msgstr ""

#: src/addons/moredatabase.php:263, src/addons/sftp.php:609,
#: src/addons/webdav.php:433
msgid "Host"
msgstr ""

#: src/addons/sftp.php:375
msgid "Error: Failed to download"
msgstr ""

#: src/addons/sftp.php:683
msgid "Check your file permissions: Could not successfully create and enter:"
msgstr ""

#: src/addons/sftp.php:122, src/addons/sftp.php:123, src/addons/sftp.php:124
msgid "No %s found"
msgstr ""

#: src/templates/wp-admin/notices/thanks-for-using-main-dash.php:19
msgid "more"
msgstr ""

#: src/addons/morefiles.php:606
msgid "No backup of %s directories: there was nothing found to back up"
msgstr ""

#: src/admin.php:4333, src/addons/morefiles.php:371
msgid "If entering multiple files/directories, then separate them with commas."
msgstr ""

#: src/addons/morefiles.php:319
msgid "Be careful what you select - if you select / then it really will try to create a zip containing your entire webserver."
msgstr ""

#: src/addons/morefiles.php:317
msgid "If you are not sure what this option is for, then you will not want it, and should turn it off."
msgstr ""

#: src/addons/morefiles.php:294
msgid "More Files"
msgstr ""

#: src/addons/morefiles.php:182
msgid "WordPress core (including any additions to your WordPress root directory)"
msgstr ""

#: src/addons/morefiles.php:175
msgid "The above files comprise everything in a WordPress installation."
msgstr ""

#: src/addons/morefiles.php:156
msgid "Over-write wp-config.php"
msgstr ""

#: src/addons/morefiles.php:152, src/includes/class-wpadmin-commands.php:631
msgid "WordPress Core"
msgstr ""

#: src/addons/webdav.php:503, src/methods/addon-base-v2.php:361
msgid "Failed: We were not able to place a file in that directory - please check your credentials."
msgstr ""

#: src/admin.php:3928, src/admin.php:3946, src/admin.php:3987,
#: src/admin.php:3977, src/backup.php:2051, src/addons/googlecloud.php:967,
#: src/addons/googlecloud.php:1007, src/addons/googlecloud.php:1001,
#: src/addons/sftp.php:669, src/methods/addon-base-v2.php:344
msgid "Failed"
msgstr ""

#: src/addons/webdav.php:427
msgid "WebDAV URL"
msgstr ""

#: src/addons/webdav.php:815
msgid "Local write failed: Failed to download"
msgstr ""

#: src/addons/webdav.php:795
msgid "Error opening remote file: Failed to download"
msgstr ""

#: src/addons/googlecloud.php:375, src/addons/googlecloud.php:373,
#: src/addons/pcloud.php:293, src/addons/sftp.php:120,
#: src/addons/webdav.php:526, src/addons/webdav.php:689,
#: src/addons/webdav.php:695, src/addons/webdav.php:727,
#: src/addons/webdav.php:769, src/methods/addon-base-v2.php:81,
#: src/methods/addon-base-v2.php:129, src/methods/addon-base-v2.php:170,
#: src/methods/addon-base-v2.php:229, src/methods/addon-base-v2.php:318,
#: src/methods/ftp.php:42, src/methods/googledrive.php:324,
#: src/methods/googledrive.php:322
msgid "No %s settings were found"
msgstr ""

#: src/methods/ftp.php:464
msgid "Failure: we successfully logged in, but were not able to create a file in the given directory."
msgstr ""

#: src/methods/ftp.php:461
msgid "Success: we successfully logged in, and confirmed our ability to create a file in the given directory (login type:"
msgstr ""

#: src/methods/ftp.php:452
msgid "Failure: we did not successfully log in with those credentials."
msgstr ""

#: src/methods/ftp.php:434
msgid "Failure: No server details were given."
msgstr ""

#: src/methods/ftp.php:127
msgid "Needs to already exist"
msgstr ""

#: src/addons/onedrive.php:975, src/addons/pcloud.php:720,
#: src/methods/dropbox.php:934
msgid "Your %s account name: %s"
msgstr ""

#: src/addons/pcloud.php:710, src/methods/dropbox.php:910,
#: src/methods/dropbox.php:912
msgid "you have authenticated your %s account"
msgstr ""

#: src/methods/dropbox.php:603
msgid "Backups are saved in %s."
msgstr ""

#: src/methods/dropbox.php:602
msgid "Need to use sub-folders?"
msgstr ""

#: src/methods/dropbox.php:313
msgid "error: failed to upload file to %s (see log file for more)"
msgstr ""

#: src/addons/pcloud.php:147, src/addons/pcloud.php:707,
#: src/methods/dropbox.php:216
msgid "error: %s (see log file for more)"
msgstr ""

#: src/methods/s3.php:1456
msgid "The communication with %s was not encrypted."
msgstr ""

#: src/methods/s3.php:1454
msgid "The communication with %s was encrypted."
msgstr ""

#: src/addons/googlecloud.php:1030, src/methods/s3.php:1451
msgid "We accessed the bucket, and were able to create files within it."
msgstr ""

#: src/addons/googlecloud.php:1024, src/addons/googlecloud.php:1038,
#: src/methods/s3.php:1449, src/methods/s3.php:1461
msgid "We successfully accessed the bucket, but the attempt to create a file in it failed."
msgstr ""

#: src/addons/googlecloud.php:1024, src/addons/googlecloud.php:1038,
#: src/methods/s3.php:1449, src/methods/s3.php:1461
msgid "Failure"
msgstr ""

#: src/methods/openstack2.php:252, src/methods/s3.php:1430
msgid "Region"
msgstr ""

#: src/addons/googlecloud.php:140, src/addons/googlecloud.php:984,
#: src/methods/s3.php:1392
msgid "Failure: No bucket details were given."
msgstr ""

#: src/methods/s3.php:1370
msgid "API secret"
msgstr ""

#: src/methods/dreamobjects.php:191, src/methods/s3.php:1048,
#: src/methods/s3.php:1088, src/methods/s3generic.php:199
msgid "%s location"
msgstr ""

#: src/methods/dreamobjects.php:189, src/methods/s3.php:1044,
#: src/methods/s3.php:1086, src/methods/s3generic.php:197
msgid "%s secret key"
msgstr ""

#: src/methods/dreamobjects.php:188, src/methods/s3.php:1040,
#: src/methods/s3.php:1085, src/methods/s3generic.php:196
msgid "%s access key"
msgstr ""

#: src/methods/dreamobjects.php:185, src/methods/s3.php:938,
#: src/methods/s3.php:1083, src/methods/s3generic.php:195
msgid "If you see errors about SSL certificates, then please go here for help."
msgstr ""

#: src/methods/s3.php:520
msgid "%s re-assembly error (%s): (see log file for more)"
msgstr ""

#: src/methods/s3.php:516
msgid "upload (%s): re-assembly failed (see log for more details)"
msgstr ""

#: src/methods/s3.php:500
msgid "chunk %s: upload failed"
msgstr ""

#: src/methods/s3.php:472
msgid "error: file %s was shortened unexpectedly"
msgstr ""

#: src/methods/s3.php:450
msgid "%s upload: getting uploadID for multipart upload failed - see log file for more details"
msgstr ""

#: src/methods/email.php:106
msgid "Note:"
msgstr ""

#: src/methods/email.php:46
msgid "WordPress Backup"
msgstr ""

#: src/methods/cloudfiles.php:590, src/methods/openstack-base.php:535
msgid "We accessed the container, and were able to create files within it."
msgstr ""

#: src/methods/cloudfiles.php:586
msgid "Cloud Files error - we accessed the container, but failed to create a file within it"
msgstr ""

#: src/methods/cloudfiles.php:559, src/methods/openstack-base.php:477
msgid "Failure: No container details were given."
msgstr ""

#: src/admin.php:1096, src/addons/moredatabase.php:264,
#: src/addons/sftp.php:611, src/addons/webdav.php:430,
#: src/methods/cloudfiles-new.php:171, src/methods/cloudfiles.php:539,
#: src/methods/openstack2.php:254
msgid "Username"
msgstr ""

#: src/methods/cloudfiles-new.php:166, src/methods/cloudfiles.php:534,
#: src/methods/s3.php:1366
msgid "API key"
msgstr ""

#: src/admin.php:991, src/addons/moredatabase.php:113,
#: src/addons/moredatabase.php:115, src/addons/moredatabase.php:117,
#: src/addons/sftp.php:638, src/addons/sftp.php:642, src/addons/sftp.php:646,
#: src/addons/webdav.php:482, src/includes/class-remote-send.php:580,
#: src/includes/migrator-lite.php:242, src/methods/addon-base-v2.php:336,
#: src/methods/cloudfiles-new.php:166, src/methods/cloudfiles-new.php:171,
#: src/methods/cloudfiles.php:534, src/methods/cloudfiles.php:539,
#: src/methods/ftp.php:438, src/methods/ftp.php:442,
#: src/methods/openstack2.php:115, src/methods/openstack2.php:120,
#: src/methods/openstack2.php:125, src/methods/openstack2.php:130,
#: src/methods/s3.php:1366, src/methods/s3.php:1370
msgid "Failure: No %s was given."
msgstr ""

#: src/methods/cloudfiles.php:498
msgid "Cloud Files username"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:45, src/methods/cloudfiles-new.php:101,
#: src/methods/cloudfiles-new.php:291, src/methods/cloudfiles.php:482
msgid "UK"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:44, src/methods/cloudfiles-new.php:100,
#: src/methods/cloudfiles-new.php:290, src/methods/cloudfiles.php:481
msgid "US (default)"
msgstr ""

#: src/methods/cloudfiles.php:478
msgid "US or UK Cloud"
msgstr ""

#: src/methods/cloudfiles-new.php:285, src/methods/cloudfiles.php:461,
#: src/methods/openstack2.php:245
msgid "Also, you should read this important FAQ."
msgstr ""

#: src/methods/cloudfiles.php:461
msgid "Get your API key <a href=\"https://mycloud.rackspace.com/\" target=\"_blank\">from your Rackspace Cloud console</a> (<a href=\"http://www.rackspace.com/knowledge_center/article/rackspace-cloud-essentials-1-generating-your-api-key\" target=\"_blank\">read instructions here</a>), then pick a container name to use for storage."
msgstr ""

#: src/admin.php:1015, src/addons/azure.php:708, src/addons/backblaze.php:793,
#: src/addons/sftp.php:623, src/addons/webdav.php:440,
#: src/methods/backup-module.php:404, src/methods/cloudfiles-new.php:299,
#: src/methods/dreamobjects.php:194, src/methods/ftp.php:130,
#: src/methods/openstack2.php:258, src/methods/s3.php:1090,
#: src/methods/s3generic.php:214
msgid "Test %s Settings"
msgstr ""

#: src/class-updraftplus.php:1570, src/class-updraftplus.php:1614,
#: src/addons/webdav.php:788, src/methods/cloudfiles.php:398
msgid "Error opening local file: Failed to download"
msgstr ""

#: src/addons/sftp.php:250, src/methods/openstack-base.php:314,
#: src/methods/s3.php:408, src/methods/s3.php:420, src/methods/s3.php:421
msgid "%s Error: Failed to upload"
msgstr ""

#: src/class-updraftplus.php:1412
msgid "%s Error: Failed to open local file"
msgstr ""

#: src/addons/cloudfiles-enhanced.php:120,
#: src/addons/cloudfiles-enhanced.php:133,
#: src/addons/cloudfiles-enhanced.php:137, src/methods/cloudfiles.php:569,
#: src/methods/cloudfiles.php:572, src/methods/cloudfiles.php:575
msgid "Cloud Files authentication failed"
msgstr ""

#: src/addons/pcloud.php:552, src/methods/backup-module.php:634,
#: src/methods/dropbox.php:607
msgid "<strong>After</strong> you have saved your settings (by clicking 'Save Changes' below), then come back here and follow this link to complete authentication with %s."
msgstr ""

#: src/addons/googlecloud.php:1317, src/methods/googledrive.php:1505
msgid "Authenticate with Google"
msgstr ""

#: src/addons/googlecloud.php:1300, src/addons/onedrive.php:1319,
#: src/methods/googledrive.php:1491
msgid "Client Secret"
msgstr ""

#: src/addons/googlecloud.php:1299, src/methods/googledrive.php:1490
msgid "If Google later shows you the message \"invalid_client\", then you did not enter a valid client ID here."
msgstr ""

#: src/addons/googlecloud.php:1298, src/addons/onedrive.php:1317,
#: src/methods/googledrive.php:1489
msgid "Client ID"
msgstr ""

#: src/methods/googledrive.php:1498
msgid "You must add the following as the authorised redirect URI (under \"More Options\") when asked"
msgstr ""

#: src/addons/googlecloud.php:1289, src/methods/googledrive.php:1497
msgid "Select 'Web Application' as the application type."
msgstr ""

#: src/addons/googlecloud.php:590, src/addons/googlecloud.php:591,
#: src/addons/googlecloud.php:1062, src/addons/googlecloud.php:1060,
#: src/methods/googledrive.php:832, src/methods/googledrive.php:833,
#: src/methods/googledrive.php:821, src/methods/googledrive.php:822
msgid "Account is not authorized."
msgstr ""

#: src/addons/webdav.php:743, src/methods/googledrive.php:717,
#: src/methods/googledrive.php:779, src/methods/googledrive.php:797,
#: src/methods/googledrive.php:795
msgid "Failed to upload to %s"
msgstr ""

#: src/methods/googledrive.php:760
msgid "Account full: your %s account has only %d bytes left, but the file to be uploaded is %d bytes"
msgstr ""

#: src/methods/googledrive.php:868, src/methods/googledrive.php:904
msgid "Have not yet obtained an access token from Google - you need to authorise or re-authorise your connection to Google Drive."
msgstr ""

#: src/addons/googlecloud.php:904, src/addons/onedrive.php:1013,
#: src/methods/googledrive.php:674
msgid "you have authenticated your %s account."
msgstr ""

#: src/addons/googlecloud.php:904, src/addons/googlecloud.php:1030,
#: src/addons/onedrive.php:1013, src/addons/sftp.php:711,
#: src/addons/sftp.php:707, src/addons/wp-cli.php:500,
#: src/methods/addon-base-v2.php:358, src/methods/cloudfiles.php:590,
#: src/methods/googledrive.php:674, src/methods/openstack-base.php:535,
#: src/methods/s3.php:1451
msgid "Success"
msgstr ""

#: src/addons/onedrive.php:967, src/addons/pcloud.php:722,
#: src/methods/dropbox.php:964, src/methods/dropbox.php:955,
#: src/methods/googledrive.php:635
msgid "Your %s quota usage: %s %% used, %s available"
msgstr ""

#: src/methods/googledrive.php:598
msgid "%s authorization failed"
msgstr ""

#: src/methods/addon-not-yet-present.php:124
msgid "follow this link to get it"
msgstr ""

#: src/methods/addon-not-yet-present.php:125
msgid "%s support is available as an add-on"
msgstr ""

#: src/methods/addon-not-yet-present.php:34,
#: src/methods/addon-not-yet-present.php:76,
#: src/methods/addon-not-yet-present.php:83
msgid "You do not have the UpdraftPlus %s add-on installed - get it from %s"
msgstr ""

#: src/includes/Dropbox2/OAuth/Consumer/ConsumerAbstract.php:137
msgid "You need to re-authenticate with %s, as your existing credentials are not working."
msgstr ""

#: src/admin.php:3932, src/admin.php:3943, src/admin.php:3984,
#: src/admin.php:3980, src/restorer.php:537, src/restorer.php:4329,
#: src/restorer.php:4188, src/includes/class-remote-send.php:441,
#: src/includes/class-storage-methods-interface.php:329
msgid "OK"
msgstr ""

#: src/restorer.php:4293, src/restorer.php:4182
msgid "Table prefix has changed: changing %s table field(s) accordingly:"
msgstr ""

#: src/restorer.php:3990, src/includes/class-search-replace.php:519
msgid "the database query being run was:"
msgstr ""

#: src/restorer.php:2607
msgid "will restore as:"
msgstr ""

#: src/restorer.php:2436, src/restorer.php:3064, src/restorer.php:3244
msgid "Old table prefix:"
msgstr ""

#: src/class-updraftplus.php:3871, src/class-updraftplus.php:5140,
#: src/addons/reporting.php:97, src/addons/reporting.php:206
msgid "Backup of:"
msgstr ""

#: src/restorer.php:2806
msgid "Failed to open database file"
msgstr ""

#: src/restorer.php:2785
msgid "Failed to find database file"
msgstr ""

#: src/restorer.php:1174
msgid "Failed to write out the decrypted database to the filesystem"
msgstr ""

#: src/restorer.php:1158
msgid "Failed to create a temporary directory"
msgstr ""

#: src/restorer.php:855
msgid "Failed to delete working directory after restoring."
msgstr ""

#: src/restorer.php:850
msgid "Cleaning up rubbish..."
msgstr ""

#: src/restorer.php:849
msgid "Restoring the database (on a large site this can take a long time - if it times out (which can happen if your web hosting company has configured your hosting to limit resources) then you should use a different method, such as phpMyAdmin)..."
msgstr ""

#: src/restorer.php:846
msgid "Database successfully decrypted."
msgstr ""

#: src/restorer.php:845
msgid "Decrypting database (can take a while)..."
msgstr ""

#: src/restorer.php:844
msgid "Unpacking backup..."
msgstr ""

#: src/restorer.php:843
msgid "Copying this entity failed."
msgstr ""

#: src/restorer.php:842
msgid "Backup file not available."
msgstr ""

#: src/restorer.php:545, src/restorer.php:546
msgid "Could not read one of the files for restoration"
msgstr ""

#: src/restorer.php:725
msgid "Error message"
msgstr ""

#: src/restorer.php:542
msgid "The backup records do not contain information about the proper size of this file."
msgstr ""

#: src/restorer.php:534
msgid "Archive is expected to be size:"
msgstr ""

#: src/admin.php:5286
msgid "If making a request for support, please include this information:"
msgstr ""

#: src/admin.php:5286
msgid "ABORT: Could not find the information on which entities to restore."
msgstr ""

#: src/addons/wp-cli.php:747
msgid "UpdraftPlus Restoration: Progress"
msgstr ""

#: src/admin.php:5244
msgid "This backup does not exist in the backup history - restoration aborted."
msgstr ""

#: src/admin.php:4660
msgid "After pressing this button, you will be given the option to choose which components you wish to restore"
msgstr ""

#: src/admin.php:4745
msgid "Delete this backup set"
msgstr ""

#: src/admin.php:1162, src/templates/wp-admin/settings/form-contents.php:390
msgid "Save Changes"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:361
msgid "Disable SSL entirely where possible"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:357
msgid "Note that not all cloud backup methods are necessarily using SSL authentication."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:356
msgid "Do not verify SSL certificates"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:351
msgid "Use the server's SSL certificates"
msgstr ""

#: src/admin.php:4235
msgid "If that is unsuccessful check the permissions on your server or change it to another directory that is writable by your web server process."
msgstr ""

#: src/admin.php:4235
msgid "or, to reset this option"
msgstr ""

#: src/admin.php:4235
msgid "Follow this link to attempt to create the directory and set the permissions"
msgstr ""

#: src/admin.php:4227
msgid "Backup directory specified is writable, which is good."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:335
msgid "Backup directory"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:330
msgid "Delete local backup"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:310
msgid "open this to show some further options; don't bother with this unless you have a problem or are curious."
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:310
msgid "Show expert settings"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:309
msgid "Expert settings"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:320
msgid "Debug mode"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:305
msgid "Advanced / Debugging Settings"
msgstr ""

#: src/admin.php:946
msgid "Requesting start of backup..."
msgstr ""

#: src/admin.php:963, src/addons/morefiles.php:1034,
#: src/methods/backup-module.php:91,
#: src/templates/wp-admin/settings/delete-and-restore-modals.php:94
msgid "Cancel"
msgstr ""

#: src/admin.php:4510, src/addons/incremental.php:341,
#: src/addons/incremental.php:333, src/addons/reporting.php:270
msgid "None"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:104
msgid "Choose your remote storage"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:179
msgid "Manually decrypt a database backup file"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:158
msgid "Database encryption phrase"
msgstr ""

#: src/admin.php:3391, src/methods/updraftvault.php:521,
#: src/templates/wp-admin/settings/form-contents.php:256,
#: src/templates/wp-admin/settings/updraftcentral-connect.php:44
msgid "Email"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:148
msgid "The above directories are everything, except for WordPress core itself which you can download afresh from WordPress.org."
msgstr ""

#: src/addons/morefiles.php:368
msgid "Exclude these:"
msgstr ""

#: src/admin.php:4319
msgid "Any other directories found inside wp-content"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:145
msgid "Include in files backup"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:88
msgid "e.g. if your server is busy at day and you want to run overnight"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:88
msgid "To fix the time at which a backup should take place,"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:62
msgid "Database backup interval"
msgstr ""

#: src/addons/incremental.php:350, src/addons/incremental.php:337
msgid "Monthly"
msgstr ""

#: src/addons/incremental.php:349, src/addons/incremental.php:336
msgid "Fortnightly"
msgstr ""

#: src/addons/incremental.php:348, src/addons/incremental.php:335
msgid "Weekly"
msgstr ""

#: src/addons/incremental.php:347, src/addons/incremental.php:334
msgid "Daily"
msgstr ""

#: src/templates/wp-admin/settings/form-contents.php:25
msgid "Files backup interval"
msgstr ""

#: src/admin.php:973, src/admin.php:4186
msgid "Download log file"
msgstr ""

#: src/admin.php:4048
msgid "The folder exists, but your webserver does not have permission to write to it."
msgstr ""

#: src/admin.php:4029
msgid "The request to the filesystem to create the directory failed."
msgstr ""

#: src/admin.php:964, src/admin.php:3925, src/admin.php:3940,
#: src/admin.php:3972, src/admin.php:4745,
#: src/includes/class-remote-send.php:696,
#: src/templates/wp-admin/settings/existing-backups-table.php:167,
#: src/templates/wp-admin/settings/file-backup-exclude.php:11
msgid "Delete"
msgstr ""

#: src/admin.php:3877
msgid "show log"
msgstr ""

#: src/templates/wp-admin/advanced/wipe-settings.php:10
msgid "This will delete all your UpdraftPlus settings - are you sure you want to do this?"
msgstr ""

#: src/templates/wp-admin/advanced/total-size.php:19
msgid "count"
msgstr ""

#: src/templates/wp-admin/advanced/total-size.php:9
msgid "N.B. This count is based upon what was, or was not, excluded the last time you saved the options."
msgstr ""

#: src/templates/wp-admin/advanced/total-size.php:6
msgid "Total (uncompressed) on-disk data:"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:84,
#: src/templates/wp-admin/settings/tab-addons.php:105,
#: src/templates/wp-admin/settings/tab-addons.php:118,
#: src/templates/wp-admin/settings/tab-addons.php:131,
#: src/templates/wp-admin/settings/tab-addons.php:144,
#: src/templates/wp-admin/settings/tab-addons.php:157,
#: src/templates/wp-admin/settings/tab-addons.php:170,
#: src/templates/wp-admin/settings/tab-addons.php:183,
#: src/templates/wp-admin/settings/tab-addons.php:196,
#: src/templates/wp-admin/settings/tab-addons.php:209,
#: src/templates/wp-admin/settings/tab-addons.php:222,
#: src/templates/wp-admin/settings/tab-addons.php:235,
#: src/templates/wp-admin/settings/tab-addons.php:248,
#: src/templates/wp-admin/settings/tab-addons.php:261,
#: src/templates/wp-admin/settings/tab-addons.php:274,
#: src/templates/wp-admin/settings/tab-addons.php:287,
#: src/templates/wp-admin/settings/tab-addons.php:304
msgid "No"
msgstr ""

#: src/addons/webdav.php:439, src/templates/wp-admin/advanced/site-info.php:84,
#: src/templates/wp-admin/advanced/site-info.php:81,
#: src/templates/wp-admin/settings/tab-addons.php:79,
#: src/templates/wp-admin/settings/tab-addons.php:92,
#: src/templates/wp-admin/settings/tab-addons.php:95,
#: src/templates/wp-admin/settings/tab-addons.php:108,
#: src/templates/wp-admin/settings/tab-addons.php:121,
#: src/templates/wp-admin/settings/tab-addons.php:134,
#: src/templates/wp-admin/settings/tab-addons.php:147,
#: src/templates/wp-admin/settings/tab-addons.php:160,
#: src/templates/wp-admin/settings/tab-addons.php:173,
#: src/templates/wp-admin/settings/tab-addons.php:186,
#: src/templates/wp-admin/settings/tab-addons.php:199,
#: src/templates/wp-admin/settings/tab-addons.php:212,
#: src/templates/wp-admin/settings/tab-addons.php:225,
#: src/templates/wp-admin/settings/tab-addons.php:238,
#: src/templates/wp-admin/settings/tab-addons.php:251,
#: src/templates/wp-admin/settings/tab-addons.php:264,
#: src/templates/wp-admin/settings/tab-addons.php:277,
#: src/templates/wp-admin/settings/tab-addons.php:290,
#: src/templates/wp-admin/settings/tab-addons.php:307,
#: src/templates/wp-admin/settings/tab-addons.php:313
msgid "Yes"
msgstr ""

#: src/admin.php:6113, src/admin.php:6117,
#: src/templates/wp-admin/advanced/site-info.php:58,
#: src/templates/wp-admin/advanced/site-info.php:64,
#: src/templates/wp-admin/advanced/site-info.php:76,
#: src/templates/wp-admin/advanced/site-info.php:77
msgid "%s version:"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:56
msgid "Current memory usage"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:55
msgid "Peak memory usage"
msgstr ""

#: src/templates/wp-admin/advanced/site-info.php:35
msgid "Web server:"
msgstr ""

#: src/templates/wp-admin/settings/take-backup.php:99
msgid "Do you need WordPress Multisite support?"
msgstr ""

#: src/templates/wp-admin/settings/take-backup.php:95
msgid "Multisite"
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:45
msgid "Do read this helpful article of useful things to know before restoring."
msgstr ""

#: src/addons/morefiles.php:152,
#: src/templates/wp-admin/settings/delete-and-restore-modals.php:82
msgid "%s restoration options:"
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:75
msgid "You will need to restore it manually."
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:75
msgid "The following entity cannot be restored automatically: \"%s\"."
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:60
msgid "Your web server has PHP's so-called safe_mode active."
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:44
msgid "Choose the components to restore"
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:44
msgid "Restoring will replace this site's themes, plugins, uploads, database and/or other content directories (according to what is contained in the backup set, and your selection)."
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:32
msgid "Restore backup"
msgstr ""

#: src/templates/wp-admin/settings/delete-and-restore-modals.php:8
msgid "Delete backup set"
msgstr ""

#: src/admin.php:945
msgid "Download error: the server sent us a response which we did not understand."
msgstr ""

#: src/admin.php:99, src/admin.php:936, src/restorer.php:539,
#: src/restorer.php:567, src/restorer.php:2328, src/restorer.php:4369,
#: src/addons/backblaze.php:247, src/addons/backblaze.php:272,
#: src/addons/cloudfiles-enhanced.php:123, src/addons/s3-enhanced.php:236,
#: src/addons/s3-enhanced.php:241, src/addons/s3-enhanced.php:243,
#: src/addons/sftp.php:1040, src/addons/webdav.php:434,
#: src/includes/class-remote-send.php:359,
#: src/includes/class-remote-send.php:411,
#: src/includes/class-remote-send.php:405,
#: src/includes/class-remote-send.php:480,
#: src/includes/class-remote-send.php:538,
#: src/includes/class-remote-send.php:565,
#: src/includes/class-remote-send.php:620,
#: src/includes/class-remote-send.php:608,
#: src/includes/class-remote-send.php:603,
#: src/includes/class-remote-send.php:593,
#: src/includes/class-search-replace.php:333,
#: src/includes/class-search-replace.php:519,
#: src/includes/migrator-lite.php:614, src/includes/migrator-lite.php:924,
#: src/includes/migrator-lite.php:1002, src/methods/remotesend.php:80,
#: src/methods/remotesend.php:258, src/methods/updraftvault.php:722
msgid "Error:"
msgstr ""

#: src/admin.php:927
msgid "calculating..."
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:68,
#: src/templates/wp-admin/settings/tab-backups.php:70
msgid "UpdraftPlus - Upload backup files"
msgstr ""

#: src/includes/class-filesystem-functions.php:105,
#: src/templates/wp-admin/advanced/site-info.php:51
msgid "refresh"
msgstr ""

#: src/includes/class-filesystem-functions.php:126,
#: src/templates/wp-admin/advanced/site-info.php:51
msgid "Web-server disk space in use by UpdraftPlus"
msgstr ""

#: src/includes/class-filesystem-functions.php:126
msgid "This is a count of the contents of your Updraft directory"
msgstr ""

#: src/addons/google-enhanced.php:126, src/methods/googledrive.php:324,
#: src/methods/googledrive.php:322, src/methods/googledrive.php:674,
#: src/methods/googledrive.php:717, src/methods/googledrive.php:760,
#: src/methods/googledrive.php:767, src/methods/googledrive.php:779,
#: src/methods/googledrive.php:797, src/methods/googledrive.php:795,
#: src/methods/googledrive.php:1488, src/methods/googledrive.php:1489,
#: src/methods/googledrive.php:1491, src/methods/googledrive.php:1493,
#: src/methods/googledrive.php:1515, src/methods/googledrive.php:1515
msgid "Google Drive"
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:59,
#: src/templates/wp-admin/settings/tab-backups.php:63
msgid "If you are using this, then turn Turbo/Road mode off."
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:59,
#: src/templates/wp-admin/settings/tab-backups.php:63
msgid "Opera web browser"
msgstr ""

#: src/templates/wp-admin/settings/downloading-and-restoring.php:47,
#: src/templates/wp-admin/settings/tab-backups.php:51
msgid "More tasks:"
msgstr ""

#: src/admin.php:3553
msgid "Download most recently modified log file"
msgstr ""

#: src/admin.php:3515, src/admin.php:3509, src/addons/autobackup.php:372,
#: src/addons/autobackup.php:467,
#: src/templates/wp-admin/settings/take-backup.php:72
msgid "Last log message"
msgstr ""

#: src/admin.php:740, src/admin.php:972, src/admin.php:4660,
#: src/addons/migrator.php:248
msgid "Restore"
msgstr ""

#: src/admin.php:962, src/templates/wp-admin/settings/take-backup.php:52
msgid "Backup Now"
msgstr ""

#: src/templates/wp-admin/settings/take-backup.php:47
msgid "Time now"
msgstr ""

#: src/admin.php:441, src/admin.php:4485, src/admin.php:4538,
#: src/admin.php:5142, src/restorer.php:677, src/addons/moredatabase.php:266,
#: src/addons/reporting.php:285, src/addons/wp-cli.php:434,
#: src/includes/class-remote-send.php:445,
#: src/includes/class-wpadmin-commands.php:154,
#: src/includes/class-wpadmin-commands.php:629,
#: src/templates/wp-admin/settings/delete-and-restore-modals.php:81,
#: src/templates/wp-admin/settings/delete-and-restore-modals.php:82,
#: src/templates/wp-admin/settings/take-backup.php:34
msgid "Database"
msgstr ""

#: src/admin.php:431, src/admin.php:5899,
#: src/templates/wp-admin/settings/take-backup.php:24
msgid "Files"
msgstr ""

#: src/templates/wp-admin/settings/take-backup.php:19
msgid "Next scheduled backups"
msgstr ""

#: src/admin.php:410
msgid "At the same time as the files backup"
msgstr ""

#: src/admin.php:400, src/admin.php:421, src/admin.php:428, src/admin.php:471,
#: src/admin.php:502
msgid "Nothing currently scheduled"
msgstr ""

#: src/templates/wp-admin/settings/take-backup.php:6
msgid "JavaScript warning"
msgstr ""

#: src/admin.php:948
msgid "Delete Old Directories"
msgstr ""

#: src/admin.php:3050
msgid "Current limit is:"
msgstr ""

#: src/admin.php:3012
msgid "Your backup has been restored."
msgstr ""

#: src/templates/wp-admin/settings/header.php:25
msgid "Version"
msgstr ""

#: src/templates/wp-admin/settings/header.php:25
msgid "Lead developer's homepage"
msgstr ""

#: src/admin.php:5793
msgid "Your settings have been wiped."
msgstr ""

#: src/admin.php:2969
msgid "Backup directory successfully created."
msgstr ""

#: src/admin.php:2962
msgid "Backup directory could not be created"
msgstr ""

#: src/admin.php:3888
msgid "Remove old directories"
msgstr ""

#: src/includes/migrator-lite.php:245, src/includes/migrator-lite.php:261
msgid "Return to UpdraftPlus Configuration"
msgstr ""

#: src/admin.php:941, src/admin.php:2971, src/admin.php:3895,
#: src/admin.php:5091, src/admin.php:5080, src/admin.php:5068,
#: src/templates/wp-admin/settings/existing-backups-table.php:19,
#: src/templates/wp-admin/settings/existing-backups-table.php:142
msgid "Actions"
msgstr ""

#: src/admin.php:2854
msgid "Bad filename format - this does not look like an encrypted database file created by UpdraftPlus"
msgstr ""

#: src/admin.php:2743
msgid "Bad filename format - this does not look like a file created by UpdraftPlus"
msgstr ""

#: src/admin.php:2631
msgid "No local copy present."
msgstr ""

#: src/admin.php:2628
msgid "Download in progress"
msgstr ""

#: src/admin.php:939, src/admin.php:2617
msgid "File ready."
msgstr ""

#: src/admin.php:2598
msgid "Download failed"
msgstr ""

#: src/admin.php:937, src/class-updraftplus.php:1570,
#: src/class-updraftplus.php:1614, src/restorer.php:4326,
#: src/restorer.php:4184, src/restorer.php:4209, src/updraftplus.php:226,
#: src/addons/webdav.php:743, src/addons/wp-cli.php:503,
#: src/includes/class-filesystem-functions.php:439,
#: src/includes/class-storage-methods-interface.php:338,
#: src/methods/addon-base-v2.php:100, src/methods/addon-base-v2.php:105,
#: src/methods/addon-base-v2.php:239, src/methods/addon-base-v2.php:259,
#: src/methods/googledrive.php:1416, src/udaddons/options.php:250
msgid "Error"
msgstr ""

#: src/admin.php:2342
msgid "Could not find that job - perhaps it has already finished?"
msgstr ""

#: src/admin.php:2335
msgid "Job deleted"
msgstr ""

#: src/admin.php:2458, src/includes/class-commands.php:992
msgid "You should soon see activity in the \"Last log message\" field below."
msgstr ""

#: src/admin.php:1018
msgid "Nothing yet logged"
msgstr ""

#: src/admin.php:1394
msgid "Please consult this FAQ if you have problems backing up."
msgstr ""

#: src/admin.php:1394
msgid "Your website is hosted using the %s web server."
msgstr ""

#: src/admin.php:1375, src/admin.php:1380, src/admin.php:1386,
#: src/admin.php:1390, src/admin.php:1394, src/admin.php:1398,
#: src/admin.php:1416, src/admin.php:1429, src/admin.php:4426,
#: src/admin.php:4424, src/admin.php:4417, src/admin.php:6083,
#: src/admin.php:6363, src/addons/azure.php:689,
#: src/includes/migrator-lite.php:668, src/methods/cloudfiles-new.php:281,
#: src/methods/cloudfiles-new.php:282, src/methods/cloudfiles.php:455,
#: src/methods/dreamobjects.php:181, src/methods/dreamobjects.php:182,
#: src/methods/ftp.php:116, src/methods/openstack-base.php:577,
#: src/methods/openstack2.php:242, src/methods/s3.php:926,
#: src/methods/s3.php:930, src/methods/s3.php:1080, src/methods/s3.php:1081,
#: src/methods/s3generic.php:192, src/methods/s3generic.php:193,
#: src/methods/updraftvault.php:477, src/udaddons/updraftplus-addons.php:325,
#: src/templates/wp-admin/settings/downloading-and-restoring.php:27,
#: src/templates/wp-admin/settings/tab-backups.php:27
msgid "Warning"
msgstr ""

#: src/admin.php:756, src/admin.php:1297, src/admin.php:3212
msgid "Settings"
msgstr ""

#: src/backup.php:3093
msgid "Infinite recursion: consult your log for more information"
msgstr ""

#: src/class-updraftplus.php:4836, src/addons/azure.php:301,
#: src/methods/googledrive.php:1416, src/methods/s3.php:370
msgid "File not found"
msgstr ""

#: src/includes/class-updraftplus-encryption.php:354
msgid "The decryption key used:"
msgstr ""

#: src/backup.php:2934
msgid "Could not open the backup file for writing"
msgstr ""

#: src/class-updraftplus.php:4533
msgid "Could not read the directory"
msgstr ""

#: src/admin.php:2685, src/backup.php:1340
msgid "Backup directory (%s) is not writable, or does not exist."
msgstr ""

#: src/class-updraftplus.php:3872
msgid "WordPress backup is complete"
msgstr ""

#: src/class-updraftplus.php:3690
msgid "The backup attempt has finished, apparently unsuccessfully"
msgstr ""

#: src/class-updraftplus.php:2109
msgid "Others"
msgstr ""

#: src/class-updraftplus.php:2092, src/addons/multisite.php:526
msgid "Uploads"
msgstr ""

#: src/class-updraftplus.php:2091
msgid "Themes"
msgstr ""

#: src/class-updraftplus.php:2090
msgid "Plugins"
msgstr ""

#: src/class-updraftplus.php:740
msgid "No log files were found."
msgstr ""

#: src/admin.php:2551, src/admin.php:2547, src/class-updraftplus.php:735
msgid "The log file could not be read."
msgstr ""

#: src/admin.php:1456, src/admin.php:1477, src/admin.php:1515,
#: src/class-updraftplus.php:663, src/class-updraftplus.php:735,
#: src/class-updraftplus.php:740, src/class-updraftplus.php:745
msgid "UpdraftPlus notice:"
msgstr ""

#: src/options.php:92, src/addons/multisite.php:116,
#: src/addons/multisite.php:815
msgid "UpdraftPlus Backups"
msgstr ""