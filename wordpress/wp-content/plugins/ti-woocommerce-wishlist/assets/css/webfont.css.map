{"version": 3, "names": [], "mappings": "", "sources": ["webfont.css"], "file": "webfont.css", "sourcesContent": ["/*------------------------------------------------------------------\nWooCommerce Wishlist Plugin custom webfont\n-------------------------------------------------------------------*/\n@font-face {\n  font-family: \"tinvwl-webfont\";\n  font-display: block;\n  src: url(\"../fonts/tinvwl-webfont.eot?ver=xu2uyi\");\n  src: url(\"../fonts/tinvwl-webfont.eot?ver=xu2uyi#iefix\") format(\"embedded-opentype\"), url(\"../fonts/tinvwl-webfont.woff2?ver=xu2uyi\") format(\"woff2\"), url(\"../fonts/tinvwl-webfont.woff?ver=xu2uyi\") format(\"woff\"), url(\"../fonts/tinvwl-webfont.ttf?ver=xu2uyi\") format(\"truetype\"), url(\"../fonts/tinvwl-webfont.svg?ver=xu2uyi#tinvwl-webfont\") format(\"svg\");\n  font-weight: normal;\n  font-style: normal;\n}\n.ftinvwl {\n  /* use !important to prevent issues with browser extensions that change fonts */\n  font-family: \"tinvwl-webfont\" !important;\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  /* Better Font Rendering =========== */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.ftinvwl-twitter:before {\n  content: \"\\f099\";\n}\n\n.ftinvwl-facebook:before {\n  content: \"\\f09a\" !important;\n}\n\n.ftinvwl-facebook-f:before {\n  content: \"\\f09a\" !important;\n}\n\n.ftinvwl-google:before {\n  content: \"\\f0d5\" !important;\n}\n\n.ftinvwl-email:before {\n  content: \"\\f0e0\" !important;\n}\n\n.ftinvwl-pinterest:before {\n  content: \"\\f231\" !important;\n}\n\n.ftinvwl-whatsapp:before {\n  content: \"\\f232\" !important;\n}\n\n.ftinvwl-clipboard:before {\n  content: \"\\e911\" !important;\n}\n\n.ftinvwl-star:before {\n  content: \"\\e912\" !important;\n}\n\n.ftinvwl-shopping-cart:before {\n  content: \"\\e913\" !important;\n}\n\n.ftinvwl-magic:before {\n  content: \"\\e914\" !important;\n}\n\n.ftinvwl-info:before {\n  content: \"\\e915\" !important;\n}\n\n.ftinvwl-graduation-cap:before {\n  content: \"\\e918\" !important;\n}\n\n.ftinvwl-floppy-o:before {\n  content: \"\\e919\" !important;\n}\n\n.ftinvwl-eyedropper:before {\n  content: \"\\e91a\" !important;\n}\n\n.ftinvwl-exclamation-triangle:before {\n  content: \"\\e91b\" !important;\n}\n\n.ftinvwl-check:before {\n  content: \"\\e91e\" !important;\n}\n\n.ftinvwl-arrow-left:before {\n  content: \"\\e91f\" !important;\n}\n\n.ftinvwl-wrench:before {\n  content: \"\\e920\" !important;\n}\n\n.ftinvwl-chevron-down:before {\n  content: \"\\e900\" !important;\n}\n\n.ftinvwl-chevron-right:before {\n  content: \"\\e901\" !important;\n}\n\n.ftinvwl-chevron-left:before {\n  content: \"\\e902\" !important;\n}\n\n.ftinvwl-chevron-up:before {\n  content: \"\\e903\" !important;\n}\n\n.ftinvwl-cancel:before {\n  content: \"\\e904\" !important;\n}\n\n.ftinvwl-times:before {\n  content: \"\\e905\" !important;\n}\n\n.ftinvwl-heart-plus:before {\n  content: \"\\e906\" !important;\n}\n\n.ftinvwl-heart-mark-right:before {\n  content: \"\\e907\" !important;\n}\n\n.ftinvwl-heart2:before {\n  content: \"\\e908\" !important;\n}\n\n.ftinvwl-heart-o:before {\n  content: \"\\e909\" !important;\n}\n\n.ftinvwl-heart-mark-left:before {\n  content: \"\\e90a\" !important;\n}\n\n.ftinvwl-heart-mail:before {\n  content: \"\\e90b\" !important;\n}\n\n.ftinvwl-heart-tinv:before {\n  content: \"\\e90c\" !important;\n}\n\n.ftinvwl-key:before {\n  content: \"\\e90d\" !important;\n}\n\n.ftinvwl-lock:before {\n  content: \"\\e90e\" !important;\n}\n\n.ftinvwl-hearts:before {\n  content: \"\\e90f\" !important;\n}\n\n.ftinvwl-user:before {\n  content: \"\\e910\" !important;\n}\n\n/* PULSE */\n@-webkit-keyframes ftinvwl-pulse {\n  0% {\n    -webkit-transform: scale(1.1);\n    transform: scale(1.1);\n  }\n  50% {\n    -webkit-transform: scale(0.8);\n    transform: scale(0.8);\n  }\n  100% {\n    -webkit-transform: scale(1.1);\n    transform: scale(1.1);\n  }\n}\n@keyframes ftinvwl-pulse {\n  0% {\n    -webkit-transform: scale(1.1);\n    transform: scale(1.1);\n  }\n  50% {\n    -webkit-transform: scale(0.8);\n    transform: scale(0.8);\n  }\n  100% {\n    -webkit-transform: scale(1.1);\n    transform: scale(1.1);\n  }\n}\n.ftinvwl-pulse.ftinvwl-animated::before {\n  -webkit-animation: ftinvwl-pulse 2s linear infinite;\n  animation: ftinvwl-pulse 2s linear infinite;\n}"]}