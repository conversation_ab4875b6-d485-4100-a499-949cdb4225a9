.tinv-wishlist, .tinv-wishlist input, .tinv-wishlist select, .tinv-wishlist textarea, .tinv-wishlist button, .tinv-wishlist input[type=button], .tinv-wishlist input[type=reset], .tinv-wishlist input[type=submit] {
  font-family: Georgia, serif;
  font-size: 14px;
  font-weight: 400;
  text-transform: none;
  line-height: 1.75;
}

.tinv-wishlist .woocommerce form .form-row input.input-text {
  line-height: 1;
  padding-top: 9.5px;
  padding-bottom: 9.5px;
}
.tinv-wishlist label {
  font-weight: 400;
}
.tinv-wishlist :-moz-placeholder, .tinv-wishlist ::-webkit-input-placeholder {
  color: #1a1a1a;
  font-family: Georgia, serif;
}
.tinv-wishlist ::-moz-placeholder {
  color: #1a1a1a;
  font-family: Georgia, serif;
  opacity: 1;
}
.tinv-wishlist :-ms-input-placeholder {
  color: #1a1a1a;
  font-family: Georgia, serif;
}
.tinv-wishlist select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: 7px 10px;
  background-image: url(../img/select_caret_t.png);
  background-repeat: no-repeat;
  background-position: 96% center;
  background-position: calc(100% - 15px) center;
}
.tinv-wishlist button {
  text-align: center;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 1;
  text-transform: none;
  padding: 11.5px 19px;
}
.tinv-wishlist input[type=button], .tinv-wishlist input[type=reset], .tinv-wishlist input[type=submit] {
  text-align: center;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 1;
  text-transform: none;
  padding: 11.5px 19px;
}
.tinv-wishlist .button, .tinv-wishlist button {
  -webkit-box-shadow: none;
          box-shadow: none;
  text-transform: none;
  border: none;
  -webkit-transition: none;
  transition: none;
}
.tinv-wishlist input[type=button], .tinv-wishlist input[type=reset], .tinv-wishlist input[type=submit] {
  -webkit-box-shadow: none;
          box-shadow: none;
  text-transform: none;
  border: none;
  -webkit-transition: none;
  transition: none;
}
.tinv-wishlist .product-quantity input[type=text].qty {
  padding: 6px 15px;
}
.tinv-wishlist input[type=text], .tinv-wishlist input[type=email], .tinv-wishlist input[type=url], .tinv-wishlist input[type=password], .tinv-wishlist input[type=search], .tinv-wishlist input[type=tel], .tinv-wishlist input[type=number] {
  padding: 6px 15px;
}
.tinv-wishlist textarea {
  padding: 6px 15px;
}

.woocommerce.tinv-wishlist #respond input#submit, .woocommerce.tinv-wishlist a.button, .woocommerce.tinv-wishlist button.button, .woocommerce.tinv-wishlist input.button {
  text-align: center;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 1;
  text-transform: none;
  padding: 11.5px 17px;
  -webkit-transition: none;
  transition: none;
}
.woocommerce.tinv-wishlist #respond input#submit.alt, .woocommerce.tinv-wishlist a.button.alt, .woocommerce.tinv-wishlist button.button.alt, .woocommerce.tinv-wishlist input.button.alt {
  padding: 11.5px 20px;
}
.woocommerce.tinv-wishlist .tinvwl-continue-shop {
  padding-left: 17px;
  padding-right: 17px;
}

.tinv-wishlist input[type=text], .tinv-wishlist input[type=email], .tinv-wishlist input[type=url], .tinv-wishlist input[type=password], .tinv-wishlist input[type=search], .tinv-wishlist input[type=tel], .tinv-wishlist input[type=number], .tinv-wishlist textarea, .tinv-wishlist select, .tinv-wishlist .product-quantity input[type=text].qty {
  font-family: inherit;
  color: #686868;
  background-color: #f7f7f7;
  border-width: 1px;
  border-style: solid;
  border-color: #d1d1d1;
  border-radius: 2px;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-size: 14px;
}

.tinv-wishlist {
  color: #1a1a1a;
}
.tinv-wishlist a i.ftinvwl-chevron-left, .tinv-wishlist a i.ftinvwl-chevron-right {
  font-size: 10px;
}
.tinv-wishlist table th {
  padding: 22px 12px;
}
.tinv-wishlist table td {
  padding: 27px 12px;
}
.tinv-wishlist table .entry-date {
  background: 0 0;
  padding: 0;
  margin: 0;
  color: inherit;
}
.tinv-wishlist td.product-name {
  line-height: 20px;
}
.tinv-wishlist td.product-name a {
  line-height: 1.14;
}
.tinv-wishlist td.product-price {
  line-height: 24px;
}
.tinv-wishlist .product-stock i {
  font-size: 12px;
}
.tinv-wishlist .product-stock i.ftinvwl-times {
  font-size: 20px;
  margin-right: 8px;
}
.tinv-wishlist .tinvwl-table-manage-list td.product-quantity {
  padding-left: 9px;
  padding-right: 9px;
}
.tinv-wishlist.woocommerce table .quantity .qty {
  padding: 8px 12px;
}
.tinv-wishlist table.tinvwl-table-manage-list .product-action {
  padding-left: 11px;
  padding-right: 11px;
}
.tinv-wishlist .tinvwl-table-manage-list .product-action .tinvwl_move_product_button i, .tinv-wishlist .tinvwl-table-manage-list .product-action button[name=tinvwl-remove] i {
  font-size: 12px;
}
.tinv-wishlist .wishlist-cb {
  padding-left: 10px;
  padding-right: 10px;
}
.tinv-wishlist .wishlist-name {
  min-width: 340px;
}
.tinv-wishlist .wishlist-privacy {
  padding-left: 15px;
  padding-right: 15px;
  min-width: 130px;
}
.tinv-wishlist table:not(.tinvwl-public) .wishlist-date {
  min-width: 130px;
}
.tinv-wishlist .wishlist-action {
  padding-left: 10px;
  padding-right: 10px;
}
.tinv-wishlist .tinv-login.tinv-wrapped-block {
  padding: 30px;
}
.tinv-wishlist .tinv-login form input[name=login] {
  min-width: 219px;
}
.tinv-wishlist .tinv-login input[name=username] {
  padding-left: 40px;
}
.tinv-wishlist .tinv-login input[name=password] {
  padding-left: 36px;
}
.tinv-wishlist .tinv-login .form-row {
  position: relative;
}
.tinv-wishlist .tinv-login .tinvwl-icon {
  display: inline-block;
}
.tinv-wishlist .tinv-modal .button {
  font-weight: 400;
  padding: 11px 11px 14px 11px;
}
.tinv-wishlist .tinv-modal .button i {
  font-size: 14px;
}
.tinv-wishlist .tinv-modal button i {
  font-size: 14px;
}
.tinv-wishlist .tinvwl_added_to_wishlist .tinvwl_button_view, .tinv-wishlist .tinvwl_created_wishlist .tinvwl_button_view {
  padding: 11px 11px 14px 11px;
}
.tinv-wishlist .tinv-modal .tinvwl-buttons-group button.tinvwl_button_close, .tinv-wishlist .tinv-modal .tinvwl-buttons-group button.tinvwl_button_view {
  float: left !important;
  width: calc(50% - 5px);
  white-space: normal;
}
.tinv-wishlist .tinv-modal .tinvwl-buttons-group button + a button, .tinv-wishlist .tinv-modal .tinvwl-buttons-group button + button {
  margin: 0 0 0 10px;
}
.tinv-wishlist .tinv-header h2 {
  font-family: inherit;
  text-transform: none;
  text-shadow: none;
  letter-spacing: 0;
  font-size: 40px;
  font-weight: 700;
  margin-bottom: 40px;
  color: #000000;
}
.tinv-wishlist a:not(.button):not(.social) {
  font-family: inherit;
  font-weight: 400;
  color: #007acc;
  text-decoration: underline;
  border-bottom: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.tinv-wishlist a:not(.button):not(.social):active, .tinv-wishlist a:not(.button):not(.social):focus, .tinv-wishlist a:not(.button):not(.social):hover {
  color: #686868;
}
.tinv-wishlist select {
  font-size: 12px;
  background-size: 8px 6px;
}

@media only screen and (max-width: 1024px) and (min-width: 641px) {
  .tinv-wishlist table.tinvwl-table-manage-list td.product-thumbnail {
    padding-left: 10px;
    padding-right: 10px;
  }
  .tinv-wishlist .product-action .button {
    padding: 11.5px 12px !important;
  }
}
@media only screen and (max-width: 640px) {
  .tinv-wishlist table.tinvwl-table-manage-list th {
    padding-top: 26px;
    padding-bottom: 26px;
  }
  .tinv-wishlist table.tinvwl-table-manage-list tbody td {
    display: block;
    width: 100% !important;
    padding: 0 12px 12px;
    text-align: center;
  }
  .tinv-wishlist table.tinvwl-table-manage-lists tbody td, .tinv-wishlist table.tinvwl-table-manage-lists.tinvwl-public tbody td {
    display: block;
    width: 100% !important;
    padding: 0 12px 12px;
    text-align: center;
  }
  .tinv-wishlist table.tinvwl-table-manage-list tbody td:not(:last-child) {
    border-bottom: 0;
  }
  .tinv-wishlist table.tinvwl-table-manage-lists tbody td:not(:last-child), .tinv-wishlist table.tinvwl-table-manage-lists.tinvwl-public tbody td:not(:last-child) {
    border-bottom: 0;
  }
  .tinv-wishlist table.tinvwl-table-manage-list tbody td:first-child, .tinv-wishlist table.tinvwl-table-manage-lists tbody td:first-child {
    padding-top: 20px;
  }
  .tinv-wishlist table.tinvwl-table-manage-list td {
    padding: 12px;
  }
  .tinv-wishlist table.tinvwl-table-manage-lists td, .tinv-wishlist table.tinvwl-table-manage-lists.tinvwl-public td {
    padding: 12px;
  }
  .tinv-wishlist .tinvwl-table-manage-list td.product-cb, .tinv-wishlist .tinvwl-table-manage-lists td.wishlist-cb {
    padding-bottom: 20px;
  }
  .tinv-wishlist .tinvwl-table-manage-list td.product-thumbnail {
    padding-top: 20px;
    padding-bottom: 14px;
  }
  .tinv-wishlist .tinvwl-table-manage-list td.product-cb + td.product-remove + td.product-thumbnail {
    padding-top: 0;
  }
  .tinv-wishlist .tinvwl-table-manage-list td.product-name {
    padding-bottom: 6px;
  }
  .tinv-wishlist .tinvwl-table-manage-list td.product-price {
    padding-bottom: 23px;
  }
  .tinv-wishlist .tinvwl-table-manage-list td.product-date {
    padding-bottom: 16px;
  }
  .tinv-wishlist .tinvwl-table-manage-list td.product-stock {
    padding-bottom: 24px;
  }
  .tinv-wishlist .tinvwl-table-manage-list td.product-quantity {
    padding: 0 12px 10px;
  }
  .tinv-wishlist .wishlist-action button[value=manage_remove] {
    text-indent: -9999px;
    background-position: center;
  }
}
@media only screen and (max-width: 1200px) {
  .tinv-wishlist .wishlist-name {
    min-width: 200px;
  }
}
@media only screen and (max-width: 1024px) {
  .tinv-wishlist .wishlist-action button[value=manage_remove] {
    padding-left: 17px;
  }
}
@media only screen and (max-width: 1024px) and (min-width: 641px) {
  .tinv-wishlist tfoot .tinvwl-to-left select {
    width: 140px;
  }
  .tinv-wishlist .wishlist-action button[value=manage_remove] {
    background-image: none;
    padding: 11.5px 12px !important;
  }
}
.woocommerce.tinv-wishlist a.button.tinvwl_add_to_wishlist_button {
  font-weight: 700;
}
.woocommerce.tinv-wishlist a.button.tinvwl_add_to_wishlist_button:before {
  font-weight: 700;
  vertical-align: middle;
}
.woocommerce ul.products li.product .tinvwl_add_to_wishlist_button, .woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.icon-white:before, .woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.icon-black:before {
  color: #007acc;
  font-family: inherit;
  font-size: 16px;
}
.woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.tinvwl-button, .woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.tinvwl-button.icon-white:before, .woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.tinvwl-button.icon-black:before {
  color: #515151;
  background-color: #ebe9eb;
}
.woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.tinvwl-button:hover, .woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.tinvwl-button.icon-white:hover:before, .woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.tinvwl-button.icon-black:hover:before {
  color: #515151;
  background-color: #dad8da;
}
.woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.tinvwl-button {
  border-radius: 3px;
}
.woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.icon-white:before, .woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.icon-black:before {
  font-size: 16px;
}
.woocommerce ul.products li.product .tinvwl_add_to_wishlist_button:hover, .woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.icon-white:hover:before, .woocommerce ul.products li.product .tinvwl_add_to_wishlist_button.icon-black:hover:before {
  color: #686868;
}
.woocommerce div.product form.cart .tinvwl_add_to_wishlist_button {
  font-family: inherit;
  font-size: 16px;
}
.woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.tinvwl-button {
  background-color: #ebe9eb;
  border-radius: 3px;
}
.woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.icon-black:before, .woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.icon-white:before {
  font-size: 16px;
}
.woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.tinvwl-button, .woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.tinvwl-button.icon-white:before, .woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.tinvwl-button.icon-black:before {
  color: #515151;
}
.woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.tinvwl-button:hover, .woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.tinvwl-button.icon-white:hover:before, .woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.tinvwl-button.icon-black:hover:before {
  color: #686868;
  background-color: #dad8da;
}
.woocommerce div.product form.cart .tinvwl_add_to_wishlist_button, .woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.icon-white:before, .woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.icon-black:before {
  color: #007acc;
}
.woocommerce div.product form.cart .tinvwl_add_to_wishlist_button:hover, .woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.icon-white:hover:before, .woocommerce div.product form.cart .tinvwl_add_to_wishlist_button.icon-black:hover:before {
  color: #686868;
}

.woocommerce.tinv-wishlist #respond input#submit, .woocommerce.tinv-wishlist a.button, .woocommerce.tinv-wishlist button.button, .woocommerce.tinv-wishlist input.button {
  font-family: inherit;
  font-size: 14px;
  margin-right: 0;
  color: #515151;
  background-color: #ebe9eb;
  border-radius: 3px;
  -webkit-box-shadow: none;
          box-shadow: none;
  border: none;
}
.woocommerce.tinv-wishlist #respond input#submit:hover, .woocommerce.tinv-wishlist a.button:hover, .woocommerce.tinv-wishlist button.button:hover, .woocommerce.tinv-wishlist input.button:hover {
  color: #515151;
  background-color: #dad8da;
}
.woocommerce.tinv-wishlist #respond input#submit.alt, .woocommerce.tinv-wishlist a.button.alt, .woocommerce.tinv-wishlist button.button.alt, .woocommerce.tinv-wishlist input.button.alt {
  font-family: inherit;
  font-size: 14px;
  color: #ffffff;
  background-color: #a46497;
  border-radius: 3px;
}
.woocommerce.tinv-wishlist #respond input#submit.alt:hover, .woocommerce.tinv-wishlist a.button.alt:hover, .woocommerce.tinv-wishlist button.button.alt:hover, .woocommerce.tinv-wishlist input.button.alt:hover {
  color: #ffffff;
  background-color: #935386;
}

.tinv-wishlist .tinv-wrapped-block {
  padding: 30px 30px 39px;
  color: #1a1a1a;
  background-color: #f6f6f6;
}
.tinv-wishlist .tinv-search-list .tinv-wrapped-block {
  padding: 20px 24px;
}
.tinv-wishlist .tinv-wrapped-block input[type=text], .tinv-wishlist .tinv-wrapped-block input[type=password], .tinv-wishlist .tinv-wrapped-block input[type=search] {
  font-family: inherit;
  font-size: 14px;
  color: #1a1a1a;
  background-color: #ffffff;
  border-color: #d1d1d1;
  border-radius: 2px;
}
.tinv-wishlist .tinv-wrapped-block input::-webkit-input-placeholder {
  color: #1a1a1a;
}
.tinv-wishlist table, .tinv-wishlist table td {
  background-color: #ffffff;
}
.tinv-wishlist table, .tinv-wishlist table td, .tinv-wishlist table th {
  border-color: #d1d1d1;
}
.tinv-wishlist table {
  border-style: solid;
  border-width: 1px 0 0 1px;
  line-height: 16px;
}
.tinv-wishlist table th {
  border: 1px solid #d1d1d1;
}
.tinv-wishlist table td {
  border-style: solid;
  border-width: 0 1px 1px 0;
  font-size: 14px;
  font-family: inherit;
}
.tinv-wishlist table th {
  border-width: 0 1px 1px 0;
  font-size: 14px;
  font-family: inherit;
  font-weight: 700;
  text-transform: none;
  color: #1a1a1a;
  background-color: #ffffff;
}
.tinv-wishlist table td {
  color: #686868;
}
.tinv-wishlist td.product-price {
  font-family: inherit;
  font-size: 16px;
  color: #202020;
}
.tinv-wishlist td.product-price ins span.amount {
  font-weight: 400;
  color: #ffffff;
  background-color: #007acc;
}
.tinv-wishlist .product-stock .stock:not(.out-of-stock) {
  color: #6a8e19;
}
.tinv-wishlist .product-remove button {
  background-color: #f7f7f7;
}
.tinv-wishlist .product-remove button:active, .tinv-wishlist .product-remove button:focus, .tinv-wishlist .product-remove button:hover {
  background-color: #dad8da;
}
.tinv-wishlist .social-buttons li a {
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  background-color: #ebe9eb;
  text-decoration: none;
  font-size: 20px;
}
.tinv-wishlist .social-buttons li a:hover {
  background-color: #dad8da;
}
.tinv-wishlist .social-buttons li a.white, .tinv-wishlist .social-buttons li a.dark {
  color: #000000;
}
.tinv-wishlist .social-buttons li a.white:hover, .tinv-wishlist .social-buttons li a.dark:hover {
  color: #686868;
}
.tinv-wishlist .tinv-modal .tinv-modal-inner {
  line-height: 26px;
  color: #1a1a1a;
  background-color: #ffffff;
  font-family: inherit;
  font-size: 16px;
}
.tinv-wishlist .tinv-modal .tinv-modal-inner select {
  font-family: inherit;
  font-size: 16px;
  padding: 6.5px 10px;
  line-height: 22px;
  border-radius: 2px;
}
.tinv-wishlist input[type=text], .tinv-wishlist input[type=email], .tinv-wishlist input[type=url], .tinv-wishlist input[type=password], .tinv-wishlist input[type=search], .tinv-wishlist input[type=tel], .tinv-wishlist input[type=number] {
  min-height: 38px;
}
.tinv-wishlist select {
  min-height: 38px;
}
.tinv-wishlist .tinv-modal .tinv-close-modal, .tinv-wishlist .tinv-modal button.button {
  color: #515151;
  background-color: #ebe9eb;
}
.tinv-wishlist .tinv-modal .tinv-close-modal:hover, .tinv-wishlist .tinv-modal button.button:hover {
  color: #515151;
  background-color: #dad8da;
}

@media only screen and (-webkit-min-device-pixel-ratio: 1.5), not all, not all, not all {
  .tinv-wishlist select {
    background-image: url(../img/<EMAIL>);
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiIiwic291cmNlcyI6WyJ0aGVtZS5jc3MiXSwiZmlsZSI6InRoZW1lLmNzcyJ9 */

/*# sourceMappingURL=theme.css.map */
