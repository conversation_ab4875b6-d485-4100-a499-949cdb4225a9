/*
number input
*/
.tiwlform-number-container {
  display: inline-block;
  margin: 2px;
  position: relative;
  vertical-align: middle;
}

.tiwlform-number-container input, .tiwlform-number-container button {
  margin: 0;
  font-weight: 800;
  display: inline-block;
  font-size: 14px;
}

.tiwlform-number-container input[type=text] {
  width: 50px;
  height: 36px;
  text-align: right;
  border-radius: 5px;
  line-height: 26px;
  margin: 0;
}

.tiwlform-number-container button {
  width: 20px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  border-radius: 5px;
  margin: 0 2px;
  background-color: #f1eee8;
  border: 0;
  color: #000;
  padding: 0;
  cursor: pointer;
}

.tiwlform-number-container button:hover {
  background-color: #96b100;
  color: #FFF;
}

/*
on off button
*/
/*.tiwlform-onoff-container input {
    display: none;
}*/
.tiwlform-onoff-container {
  cursor: pointer;
  position: relative;
  display: inline-block;
  height: 36px;
  width: 132px;
  margin: 0;
  border-radius: 3px;
  -webkit-box-shadow: inset 1px 1px 6px 0 rgba(170, 157, 137, 0.16);
  box-shadow: inset 1px 1px 6px 0 rgba(170, 157, 137, 0.16);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  border: 1px solid #eae6df;
  background: #f1eee8;
}

.tiwlform-onoff-container.disabled.checked {
  /*-webkit-box-shadow: inset 0 0 0 36px rgba(0,0,0,0.4);
    box-shadow: inset 0 0 0 36px rgba(0,0,0,0.4);*/
  border-color: rgba(0, 0, 0, 0.4);
  background-color: rgba(0, 0, 0, 0.4);
}

.tiwlform-onoff-container.disabled .tiwlform-onoff-button {
  color: rgba(0, 0, 0, 0.4) !important;
}

.tiwlform-onoff-container.checked {
  /*-webkit-box-shadow: inset 0 0 0 36px #96b100;
    box-shadow: inset 0 0 0 36px #96b100;*/
  background: #96b100;
  border-color: #96b100;
}

.tiwlform-onoff-container.checked .tiwlform-onoff-button {
  left: 4px;
  color: #96b100;
}

.tiwlform-onoff-container .tiwlform-onoff-button {
  display: inline-block;
  /*font-size: 21px;
    line-height: 21px;
    font-weight: 600;
    font-family: dashicons;*/
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  top: 3px;
  left: 66px;
  width: 59px;
  height: 27px;
  line-height: 18px;
  padding: 4px;
  text-align: center;
  border-radius: 3px;
  -webkit-box-shadow: 1px 1px 4px 0 rgba(2, 2, 2, 0.21);
  box-shadow: 1px 1px 4px 0 rgba(2, 2, 2, 0.21);
  -webkit-transition: all 350ms cubic-bezier(0, 0.89, 0.44, 1);
  transition: all 350ms cubic-bezier(0, 0.89, 0.44, 1);
  color: #6b625a;
  background: #fff;
}

.tiwlform-onoff-container .tiwlform-onoff-button:before {
  content: "\e905";
  font-size: 20px;
  font-family: "tinvwl-webfont";
}

.tiwlform-onoff-container.checked .tiwlform-onoff-button:before {
  content: "\e91e";
  font-size: 11px;
  font-family: "tinvwl-webfont";
}

.tiwlform-onoff-container.disabled {
  background-color: rgba(0, 0, 0, 0.4);
}

/*
input range
*/
.tiwlform-range-container {
  padding-top: 25px;
  height: 50px;
}

.tiwlform-range-container .range {
  width: 100%;
  position: relative;
}

.tiwlform-range-container input[type=text] {
  display: none;
}

.tiwlform-range-container .line {
  margin: 0 5%;
  width: 90%;
  height: 4px;
  top: -14px;
  background-color: #ede8df;
  position: absolute;
}

.tiwlform-range-container .line .selector {
  position: absolute;
  z-index: 100;
  width: 15px;
  height: 15px;
  background: #fff;
  border: 5px solid #96b100;
  border-radius: 50%;
  top: -10px;
  right: -12.5px;
  cursor: pointer;
}

.tiwlform-range-container .line-selector {
  width: 0;
  background-color: #96b100;
  height: 6px;
  top: -15px;
}

.tiwlform-range-container .label {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  list-style: outside none none;
  padding: 0;
}

.tiwlform-range-container .label li {
  -moz-flex: 1 1;
  -ms-flex: 1 1;
  -webkit-box-flex: 1;
  flex: 1 1;
  position: relative;
  float: left;
  text-align: center;
  color: #000;
  padding: 10px 0;
  font-size: 14px;
  line-height: 14px;
  cursor: pointer;
  margin: 0;
}

.tiwlform-range-container .label li.active {
  font-weight: 600;
}

.tiwlform-range-container .label li.preactive::before {
  background: #96b100;
  width: 25px;
  height: 25px;
  top: -25px;
}

.tiwlform-range-container .label li::before {
  position: absolute;
  top: -20px;
  right: 0;
  left: 0;
  content: "";
  margin: 0 auto;
  width: 15px;
  height: 15px;
  background: #ede8df;
  border-radius: 50%;
}

/*
multi radio box
*/
.tiwlform-multirbox input {
  display: none;
}

.tiwlform-multirbox {
  border-radius: 3px;
  border: 1px solid #eae6df;
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) inset;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) inset;
  display: inline-block;
  margin: 2px;
  position: relative;
}

.tiwlform-multirbox label {
  display: inline-block;
  font-weight: normal;
  color: #948d84;
  margin: 0;
  line-height: 26px;
  vertical-align: middle;
  padding: 5px 10px;
  height: 26px;
}

.tiwlform-multirbox label.checked {
  -webkit-box-shadow: inset 0 0 0 36px #96b100;
  box-shadow: inset 0 0 0 36px #96b100;
  color: #FFF;
  border-radius: 3px;
}

.tiwlform-multirbox label .dashicons {
  line-height: 26px;
}

.tinvwl-multicheckbox {
  border: 1px solid #dbdbdb;
  border-radius: 3px;
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) inset;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) inset;
  margin: 2px;
}

.tinvwl-multicheckbox ul {
  height: 208px;
  overflow: auto;
  margin: 0;
  padding: 0 12px;
}

.tinvwl-multicheckbox li {
  height: 30px;
}

.tinvwl-multicheckbox label {
  width: 100%;
  display: inline-block;
  font-weight: normal;
}

.tinvwl-multicheckbox input {
  margin-right: 13px;
}

.tinvwl-multicheckbox > .tinvwl-before {
  background-color: #fbfaf9;
  border-bottom: 1px solid #dbdbdb;
  padding: 7px;
}

.tinvwl-multicheckbox > .tinvwl-after {
  background-color: #fbfaf9;
  border-top: 1px solid #dbdbdb;
  padding: 7px;
}

.tinvwl-targeting-box {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  padding-top: 28px;
}

.tinvwl-targeting-box-action {
  display: none;
}

.tinvwl-targeting-box .selector {
  -moz-flex: 1 1 50%;
  -ms-flex: 1 1 50%;
  -webkit-box-flex: 1;
  flex: 1 1 50%;
  padding-right: 20px;
}

.tinvwl-targeting-box .selector div + div {
  margin-top: 6px;
}

.tinvwl-targeting-box .zone {
  -moz-flex: 1 1 50%;
  -ms-flex: 1 1 50%;
  -webkit-box-flex: 1;
  flex: 1 1 50%;
  background-color: #fbfaf9;
  border-radius: 3px;
  position: relative;
  margin: 2px;
  padding: 0;
}

.tinvwl-targeting-box .inner {
  margin: 0;
  padding: 20px;
  overflow: auto;
  border-radius: 3px;
  min-height: 42px;
}

.tinvwl-targeting-box .filter-field, .tinvwl-targeting-box .tinvwl-multicheckbox {
  width: 100%;
}

.tinvwl-targeting-box .zone > .tinvwl-after {
  background-color: #f4f2ee;
  border-top: 1px solid #fbfaf9;
  padding: 7px;
  margin: 0;
  position: absolute;
  border-radius: 0 0 3px 3px;
  bottom: 0;
  left: 0;
  right: 0;
}

.tinvwl-targeting-box .action-bth {
  background-color: #FFF;
  color: #000;
  border: 1px solid #dbdbdb;
  font-weight: normal;
  text-align: center;
}

.tinvwl-targeting-box .action-bth:hover {
  background-color: #96b100;
  color: #FFF;
}

.tinvwl-numberrange {
  width: 100%;
}

.tinvwl-numberrange .range {
  position: relative;
  width: 100%;
  height: 5px;
}



/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiIiwic291cmNlcyI6WyJhZG1pbi1mb3JtLmNzcyJdLCJmaWxlIjoiYWRtaW4tZm9ybS5jc3MifQ== */

/*# sourceMappingURL=admin-form.css.map */
