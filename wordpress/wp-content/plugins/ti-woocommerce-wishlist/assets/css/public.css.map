{"version": 3, "names": [], "mappings": "", "sources": ["public.css"], "file": "public.css", "sourcesContent": ["/*------------------------------------*\n\t$WEBFONT\n*------------------------------------*/\n.tinv-wishlist form, .tinv-wishlist p:last-child, .tinv-wishlist table {\n  margin-bottom: 0;\n}\n\n.tinv-wishlist * {\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n}\n\n.tinvwl-wishlist :after, .tinvwl-wishlist :before {\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n}\n\n.tinv-wishlist select {\n  width: 140px;\n}\n.tinv-wishlist ul:not(.woocommerce-error) {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n.tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt:before, .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt:before {\n  margin-top: -10px;\n  margin-left: -10px;\n}\n\n.woocommerce.tinv-wishlist #respond input#submit.alt, .woocommerce.tinv-wishlist a.button.alt, .woocommerce.tinv-wishlist button.button.alt, .woocommerce.tinv-wishlist input.button.alt {\n  text-align: center;\n}\n\n.tinv-wishlist .button i, .tinv-wishlist .navigation-button a i {\n  margin-right: 6px;\n}\n.tinv-wishlist input[type=button] i, .tinv-wishlist input[type=reset] i, .tinv-wishlist input[type=submit] i {\n  margin-right: 6px;\n}\n.tinv-wishlist a.tinv-close-modal i {\n  margin-right: 0;\n}\n\na.wishlist_products_counter {\n  text-decoration: none;\n}\n\n.tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart:before, .woocommerce ul.products li.product a.tinvwl-button.tinvwl_add_to_wishlist_button.tinvwl-icon-heart:before, .woocommerce-page ul.products li.product a.tinvwl-button.tinvwl-icon-heart.tinvwl_add_to_wishlist_button:before, a.wishlist_products_counter.top_wishlist-heart:before, span.wishlist_products_counter.top_wishlist-heart:before, a.sidr-class-wishlist_products_counter.sidr-class-top_wishlist-heart:before {\n  content: \"\\e909\";\n  display: inline-block;\n  font-family: tinvwl-webfont !important;\n  speak: none;\n  font-style: normal;\n  font-weight: 400;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  font-size: 20px;\n  vertical-align: sub;\n  margin-right: 5px;\n}\n\n.tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus:before, .woocommerce ul.products li.product a.tinvwl-button.tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus:before, .woocommerce-page ul.products li.product a.tinvwl-button.tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus:before, a.wishlist_products_counter.top_wishlist-heart-plus:before, span.wishlist_products_counter.top_wishlist-heart-plus:before, a.sidr-class-wishlist_products_counter.sidr-class-top_wishlist-heart-plus:before {\n  content: \"\\e906\";\n  display: inline-block;\n  font-family: tinvwl-webfont !important;\n  speak: none;\n  font-style: normal;\n  font-weight: 400;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  font-size: 20px;\n  vertical-align: sub;\n  margin-right: 5px;\n}\n\n.tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt, .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt {\n  position: relative;\n  width: 18px;\n  height: 18px;\n  vertical-align: sub;\n}\n\na.wishlist_products_counter.top_wishlist-heart-plus.no-txt, span.wishlist_products_counter.top_wishlist-heart-plus.no-txt {\n  position: relative;\n  width: 18px;\n  height: 18px;\n  vertical-align: sub;\n}\n\n.tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt:before, .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt:before {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  margin-right: 0;\n}\n.tinv-wishlist .tinvwl-button.tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt, .tinv-wishlist .tinvwl-button.tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt {\n  padding-left: 1em;\n  vertical-align: bottom;\n}\n.tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.tinvwl-product-in-list:before {\n  content: \"\\e908\";\n}\n\n.woocommerce ul.products li.product a.tinvwl-button.tinvwl_add_to_wishlist_button.tinvwl-icon-heart.tinvwl-product-in-list:before, .woocommerce-page ul.products li.product a.tinvwl-button.tinvwl-icon-heart.tinvwl_add_to_wishlist_button.tinvwl-product-in-list:before, a.wishlist_products_counter.top_wishlist-heart.wishlist-counter-with-products:before, span.wishlist_products_counter.top_wishlist-heart.wishlist-counter-with-products:before {\n  content: \"\\e908\";\n}\n\n.tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.tinvwl-product-in-list:before, .woocommerce ul.products li.product a.tinvwl-button.tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.tinvwl-product-in-list:before, .woocommerce-page ul.products li.product a.tinvwl-button.tinvwl-icon-heart-plus.tinvwl_add_to_wishlist_button.tinvwl-product-in-list:before, a.wishlist_products_counter.top_wishlist-heart-plus.wishlist-counter-with-products:before, span.wishlist_products_counter.top_wishlist-heart-plus.wishlist-counter-with-products:before {\n  content: \"\\e907\";\n}\n\n.tinv-wishlist .tinvwl_add_to_wishlist_button.icon-white:before, a.wishlist_products_counter.top_wishlist-white:before, span.wishlist_products_counter.top_wishlist-white:before {\n  color: #FFF;\n}\n\n.tinv-wishlist .tinvwl_add_to_wishlist_button.icon-black:before, a.wishlist_products_counter.top_wishlist-black:before, span.wishlist_products_counter.top_wishlist-black:before {\n  color: #000;\n}\n\n.tinv-wishlist.tinvwl-before-add-to-cart .tinvwl_add_to_wishlist_button {\n  margin-bottom: 15px;\n}\n.tinv-wishlist.tinvwl-after-add-to-cart .tinvwl_add_to_wishlist_button {\n  margin-top: 15px;\n}\n.tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-custom.no-txt {\n  width: 18px;\n  height: 18px;\n  line-height: 1;\n}\n\n.wishlist-popup .tinv-wishlist .tinvwl_add_to_wishlist_button {\n  margin: 0;\n}\n.wishlist-popup .tinv-wishlist .tinvwl_add_to_wishlist_button::before {\n  display: none;\n}\n\na.wishlist_products_counter.top_wishlist-custom.no-txt, span.wishlist_products_counter.top_wishlist-custom.no-txt {\n  width: 18px;\n  height: 18px;\n  line-height: 1;\n}\n\n.tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-custom img, a.wishlist_products_counter.top_wishlist-custom img, span.wishlist_products_counter.top_wishlist-custom img {\n  display: inline-block !important;\n  vertical-align: baseline;\n  width: auto !important;\n  max-width: 16px;\n  max-height: 16px;\n  margin: 0 6px 0 0 !important;\n}\n\n.tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-custom.no-txt img, a.wishlist_products_counter.top_wishlist-custom.no-txt img, span.wishlist_products_counter.top_wishlist-custom.no-txt img {\n  margin-right: 0 !important;\n}\n\n.single-product div.product form.cart .tinvwl-button.tinvwl_add_to_wishlist_button, div.product form.cart .tinvwl_add_to_wishlist_button {\n  float: none;\n}\n\nul.products li.product .tinvwl_add_to_wishlist_button {\n  margin-top: 1em;\n}\n\n.tinvwl_add_to_wishlist_button {\n  display: inline-block;\n  cursor: pointer;\n  -webkit-transition: opacity 1s;\n  transition: opacity 1s;\n  opacity: 1;\n  visibility: visible;\n  position: relative;\n}\n\n.tinv-wishlist.woocommerce .stock.in-stock:before {\n  content: none;\n}\n\n.tinv-wraper.tinv-wishlist {\n  font-size: 100%;\n}\n.tinv-wraper.tinv-wishlist.tinvwl-above_thumb-add-to-cart {\n  position: absolute;\n  z-index: 10;\n  margin: 0;\n  top: 10px;\n  left: 10px;\n}\n.tinv-wraper.tinv-wishlist.tinvwl-above_thumb-add-to-cart a.tinvwl_add_to_wishlist_button {\n  margin-top: 0;\n}\n\n.tinv-create-list li input[type=radio] {\n  margin-right: 10px;\n}\n.tinv-create-list li + li {\n  margin-top: 15px;\n}\n.tinv-create-list .tinvwl-input-group + ul {\n  margin-top: 25px;\n}\n\n.tinv-search-list {\n  margin-bottom: 36px;\n}\n\n.tinv-wishlist .tinv-header {\n  margin-bottom: 30px;\n}\n.tinv-wishlist table {\n  position: relative;\n  table-layout: auto;\n  margin-bottom: 30px;\n}\n\n.tinv-overlay {\n  top: 0;\n  left: 0;\n  position: fixed;\n  -webkit-transition: opacity 0.3s ease, visibility 0.3s ease;\n  transition: opacity 0.3s ease, visibility 0.3s ease;\n}\n\n.tinv-wishlist .tinv-modal {\n  top: 0;\n  left: 0;\n  position: fixed;\n  -webkit-transition: opacity 0.3s ease, visibility 0.3s ease;\n  transition: opacity 0.3s ease, visibility 0.3s ease;\n}\n.tinv-wishlist table.tinvwl-table-manage-list {\n  margin-bottom: 27px;\n  width: 100%;\n}\n.tinv-wishlist table input[type=checkbox] {\n  margin-right: 0;\n}\n.tinv-wishlist table td, .tinv-wishlist table th {\n  padding: 1em;\n  vertical-align: middle;\n}\n.tinv-wishlist .tinvwl-table-manage-list .product-cb {\n  width: 35px;\n  text-align: center;\n}\n.tinv-wishlist .tinvwl-table-manage-list .product-remove {\n  width: 35px;\n  text-align: center;\n  padding: 1em 0.5em;\n}\n.tinv-wishlist .product-remove button {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  -ms-flex-line-pack: center;\n      align-content: center;\n  margin: 0 auto;\n  width: 27px;\n  height: 27px;\n  border-radius: 50%;\n  padding: 0;\n  -webkit-box-shadow: none;\n          box-shadow: none;\n  border: none;\n  background-color: #f7f7f7;\n  color: #000000;\n  font-size: 20px;\n  padding-left: 1px;\n  padding-top: 3px;\n  min-height: 0;\n}\n.tinv-wishlist .product-thumbnail {\n  min-width: 100px;\n  max-width: 100%;\n  width: 100px;\n}\n.tinv-wishlist .product-thumbnail .wp-post-image {\n  margin-bottom: 0;\n}\n.tinv-wishlist td.product-name a + .variation {\n  margin-top: 2px;\n}\n.tinv-wishlist .product-stock i {\n  margin-right: 15px;\n}\n.tinv-wishlist .product-stock p {\n  display: table;\n}\n.tinv-wishlist .product-stock p.stock::before {\n  display: none;\n}\n.tinv-wishlist .product-stock span {\n  display: table-cell;\n  vertical-align: middle;\n}\n.tinv-wishlist .product-action > .button > i, .tinv-wishlist .tinvwl-table-manage-list .product-action button[name=tinvwl-remove], .tinv-wishlist table thead th .tinvwl-mobile {\n  display: none;\n}\n.tinv-wishlist.woocommerce .product-quantity {\n  width: 80px;\n  text-align: center;\n}\n.tinv-wishlist.woocommerce table .quantity .qty {\n  max-width: 100%;\n  width: 62px;\n  text-align: left;\n}\n.tinv-wishlist .product-action {\n  width: 135px;\n  text-align: center;\n}\n.tinv-wishlist .product-action .button {\n  width: 100%;\n}\n.tinv-wishlist .product-action .tinvwl_move_product_button {\n  margin-top: 5px;\n}\n.tinv-wishlist .tinvwl-table-manage-list .product-action > button[name=tinvwl-remove] > i {\n  margin-right: 0;\n}\n.tinv-wishlist .wishlist-cb {\n  width: 33px;\n  text-align: center;\n}\n.tinv-wishlist .wishlist-name .tinvwl-rename-input input {\n  width: 100%;\n}\n.tinv-wishlist .wishlist-name .tinvwl-rename-button {\n  float: right;\n}\n.tinv-wishlist .wishlist-name .tinvwl-rename-button > i {\n  margin-right: 10px;\n}\n.tinv-wishlist .wishlist-privacy, .tinv-wishlist table:not(.tinvwl-public) .wishlist-date {\n  width: 18%;\n}\n.tinv-wishlist .wishlist-privacy select {\n  width: 100%;\n}\n.tinv-wishlist .wishlist-name {\n  width: 45%;\n}\n.tinv-wishlist .wishlist-action {\n  width: 120px;\n  text-align: center;\n}\n.tinv-wishlist .wishlist-action button[value=manage_remove] > i {\n  display: none;\n  margin-right: 0;\n}\n.tinv-wishlist tfoot .tinvwl-to-right .tinv-create-list {\n  display: inline-block;\n  vertical-align: middle;\n}\n.tinv-wishlist tfoot .tinvwl-to-right .tinv-create-list > a.button {\n  margin-right: 0;\n}\n.tinv-wishlist tfoot .tinvwl-to-left + .tinvwl-to-right {\n  margin-top: -10px;\n}\n.tinv-wishlist tfoot .tinvwl-to-left:not(:empty) {\n  float: left;\n  width: 35%;\n  margin-right: 2%;\n}\n.tinv-wishlist tfoot .tinvwl-to-right {\n  float: left;\n  width: 63%;\n  text-align: right;\n}\n.tinv-wishlist tfoot .tinvwl-to-right > * {\n  margin: 10px 0 0;\n  vertical-align: middle;\n}\n.tinv-wishlist tfoot .tinvwl-to-right > * + * {\n  margin-left: 10px;\n}\n.tinv-wishlist tfoot .tinvwl-to-left:empty + .tinvwl-to-right {\n  width: 100%;\n}\n.tinv-wishlist .social-buttons + .tinv-wishlist-clear + .navigation-button {\n  margin-top: 16px;\n}\n.tinv-wishlist .navigation-button {\n  margin-top: -10px;\n}\n.tinv-wishlist .navigation-button > li {\n  float: left;\n}\n.tinv-wishlist .navigation-button > li > .tinv-create-list > a, .tinv-wishlist .navigation-button > li > a {\n  margin-right: 30px;\n  margin-top: 10px;\n  display: inline-block;\n}\n.tinv-wishlist .navigation-button > li > .tinv-create-list > a.button, .tinv-wishlist .navigation-button > li > a.button {\n  margin-right: 10px;\n}\n.tinv-wishlist .navigation-button > li:last-child > .tinv-create-list > a, .tinv-wishlist .navigation-button > li:last-child > a {\n  margin-right: 0;\n}\n.tinv-wishlist .social-buttons {\n  text-align: right;\n}\n.tinv-wishlist .social-buttons > span, .tinv-wishlist .social-buttons > ul {\n  display: inline-block;\n  vertical-align: middle;\n}\n.tinv-wishlist .social-buttons > span {\n  margin-right: 27px;\n}\n.tinv-wishlist .social-buttons li {\n  float: left;\n  margin: 0 5px 0 0;\n  list-style: none;\n}\n.tinv-wishlist .social-buttons li:last-child {\n  margin-right: 0;\n}\n.tinv-wishlist .social-buttons li a.social {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-line-pack: center;\n      align-content: center;\n  -webkit-box-pack: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  width: 2em;\n  height: 2em;\n  border-radius: 50%;\n  text-align: center;\n  -webkit-box-shadow: none;\n          box-shadow: none;\n  border: 0;\n  font-size: 20px;\n  text-decoration: none;\n  text-transform: none !important;\n}\n.tinv-wishlist .social-buttons li a.social.white {\n  color: #ffffff;\n}\n.tinv-wishlist .social-buttons li a.social.dark {\n  color: #000000;\n}\n.tinv-wishlist .social-buttons li a.social i {\n  line-height: 2em;\n}\n.tinv-wishlist .navigation-button .tinv-create-list > a.tinvwl-no-icon > i, .tinv-wishlist .navigation-button li > a.tinvwl-no-icon > i {\n  display: none;\n}\n.tinv-wishlist .tinv-lists-nav {\n  margin-top: 35px;\n  margin-bottom: 35px;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: row;\n          flex-direction: row;\n  -ms-flex-wrap: wrap;\n      flex-wrap: wrap;\n  width: 100%;\n}\n.tinv-wishlist .tinv-lists-nav > * {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -ms-flex-preferred-size: 100%;\n      flex-basis: 100%;\n  -webkit-box-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n  text-align: center;\n  -webkit-box-pack: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n}\n.tinv-wishlist .tinv-lists-nav > span {\n  font-weight: 700;\n}\n.tinv-wishlist .tinv-next {\n  display: inline-block;\n  float: left;\n}\n.tinv-wishlist .tinv-prev {\n  display: inline-block;\n  float: left;\n  margin-right: 35px !important;\n}\n.tinv-wishlist .tinv-prev i {\n  margin-left: 0;\n  margin-right: 18px;\n}\n.tinv-wishlist .tinv-next i {\n  margin-left: 18px;\n  margin-right: 0;\n}\n\n@media only screen and (max-width: 1024px) {\n  .tinv-wishlist .tinvwl-table-manage-lists:not(.tinvwl-public) {\n    margin-top: 0;\n  }\n  .tinv-wishlist .tinvwl-table-manage-list .product-remove {\n    display: none;\n  }\n  .tinv-wishlist .tinvwl-table-manage-list .product-action button[name=tinvwl-remove] {\n    display: inline-block;\n    margin-top: 5px;\n  }\n  .tinv-wishlist .product-action {\n    width: 60px;\n  }\n}\n@media only screen and (max-width: 1024px) and (min-width: 769px) {\n  .tinv-wishlist .tinvwl-table-manage-list td.product-stock p {\n    display: block;\n    text-align: center;\n  }\n  .tinv-wishlist .product-stock span {\n    display: block;\n  }\n  .tinv-wishlist .tinvwl-table-manage-list td.product-stock i {\n    margin-right: 0;\n  }\n  .tinv-wishlist .tinvwl-table-manage-list td.product-stock .tinvwl-txt {\n    display: none;\n  }\n  .tinv-wishlist .product-thumbnail {\n    max-width: 76px;\n    width: 76px;\n  }\n  .tinv-wishlist .product-action .button > i {\n    display: inline-block;\n    margin-right: 0;\n  }\n  .tinv-wishlist .product-action .button .tinvwl-txt {\n    display: none;\n  }\n}\n@media only screen and (max-width: 1023px) {\n  .tinv-wishlist .tinv-lists-nav a.tinv-prev > i, .tinv-wishlist .tinv-lists-nav a.tinv-next > i {\n    margin: 0;\n  }\n  .tinv-wishlist .tinv-lists-nav a.tinv-prev > span, .tinv-wishlist .tinv-lists-nav a.tinv-next > span {\n    display: none;\n  }\n}\n@media only screen and (max-width: 768px) {\n  .tinv-wishlist table.tinvwl-table-manage-list tbody td.product-remove, .tinv-wishlist table.tinvwl-table-manage-list thead th:not(.product-name) {\n    display: none;\n  }\n  .tinv-wishlist table.tinvwl-table-manage-lists thead th:not(.wishlist-name) {\n    display: none;\n  }\n  .tinv-wishlist thead th .tinvwl-full {\n    display: none;\n  }\n  .tinv-wishlist table.tinvwl-table-manage-list thead th.product-name, .tinv-wishlist table.tinvwl-table-manage-lists thead th.wishlist-name {\n    display: block;\n    width: 100%;\n    text-align: center;\n  }\n  .tinv-wishlist table thead th .tinvwl-mobile {\n    display: block;\n  }\n  .tinv-wishlist table.tinvwl-table-manage-list tbody td {\n    display: block;\n    width: 100% !important;\n    text-align: center;\n  }\n  .tinv-wishlist table.tinvwl-table-manage-lists tbody td, .tinv-wishlist table.tinvwl-table-manage-lists.tinvwl-public tbody td {\n    display: block;\n    width: 100% !important;\n    text-align: center;\n  }\n  .tinv-wishlist table.tinvwl-table-manage-list tbody td:not(:last-child) {\n    border-bottom: 0;\n  }\n  .tinv-wishlist table.tinvwl-table-manage-lists tbody td:not(:last-child), .tinv-wishlist table.tinvwl-table-manage-lists.tinvwl-public tbody td:not(:last-child) {\n    border-bottom: 0;\n  }\n  .tinv-wishlist .product-stock p {\n    margin: 0 auto;\n  }\n  .tinv-wishlist .product-thumbnail img {\n    margin: 0 auto;\n    max-width: 80px;\n  }\n  .tinv-wishlist.woocommerce table .quantity .qty {\n    text-align: center;\n    width: 100%;\n  }\n  .tinv-wishlist .product-action .tinvwl_move_product_button {\n    margin-top: 10px;\n  }\n  .tinv-wishlist table.tinvwl-table-manage-list tfoot td {\n    display: block;\n    width: 100%;\n  }\n  .tinv-wishlist table.tinvwl-table-manage-lists .wishlist-action button[value=manage_remove] {\n    width: 100%;\n  }\n  .tinv-wishlist table.tinvwl-table-manage-lists .wishlist-name .tinvwl-rename-button {\n    float: none;\n  }\n}\n@media only screen and (max-width: 1024px) {\n  .tinv-wishlist .wishlist-name .tinvwl-rename-button > i, .tinv-wishlist tfoot .tinvwl-to-left:not(:empty) {\n    margin-right: 0;\n  }\n  .tinv-wishlist .wishlist-name .tinvwl-rename-button span {\n    display: none;\n  }\n  .tinv-wishlist .wishlist-action {\n    width: 60px;\n  }\n  .tinv-wishlist tfoot .tinvwl-to-left:not(:empty) {\n    float: none;\n    width: auto;\n  }\n  .tinv-wishlist tfoot .tinvwl-to-right {\n    float: none;\n    width: auto;\n    text-align: left;\n  }\n}\n@media only screen and (max-width: 1024px) and (min-width: 768px) {\n  .tinv-wishlist .wishlist-action button[value=manage_remove] span {\n    display: none;\n  }\n  .tinv-wishlist .wishlist-action button[value=manage_remove] > i {\n    display: inline-block;\n  }\n  .tinv-wishlist tfoot .tinvwl-to-left + .tinvwl-to-right {\n    margin-top: 0;\n  }\n}\n@media only screen and (max-width: 768px) {\n  .tinv-wishlist button[value=manage_apply] .tinvwl-mobile, .tinv-wishlist button[value=product_apply] .tinvwl-mobile {\n    display: none;\n  }\n  .tinv-wishlist tfoot .tinvwl-to-right .tinv-create-list {\n    display: block;\n  }\n  .tinv-wishlist .tinvwl-table-manage-list tfoot .tinvwl-to-right .button, .tinv-wishlist .tinvwl-table-manage-list tfoot .tinvwl-to-right button {\n    width: 100%;\n    margin: 10px 0 0;\n  }\n  .tinv-wishlist .tinvwl-table-manage-lists tfoot .tinvwl-to-right .button, .tinv-wishlist .tinvwl-table-manage-lists tfoot .tinvwl-to-right button {\n    width: 100%;\n    margin: 10px 0 0;\n  }\n  .tinv-wishlist tfoot .tinvwl-to-right > * {\n    margin: 10px 0 0;\n  }\n  .tinv-wishlist tfoot .tinvwl-to-right .button:first-child, .tinv-wishlist tfoot .tinvwl-to-right button:first-child {\n    margin-top: 0 !important;\n  }\n  .tinv-wishlist tfoot .tinvwl-to-left + .tinvwl-to-right {\n    margin-top: 20px;\n  }\n}\n@media only screen and (max-width: 1024px) {\n  .tinv-wishlist .social-buttons {\n    float: none;\n    width: auto;\n    text-align: left;\n    margin-top: 20px;\n  }\n  .tinv-wishlist .social-buttons + .tinv-wishlist-clear + .navigation-button {\n    margin-top: 30px;\n  }\n  .tinv-wishlist .tinv-login form input[name=login] {\n    min-width: auto;\n  }\n}\n@media only screen and (max-width: 768px) {\n  .tinv-wishlist .social-buttons {\n    text-align: center;\n  }\n  .tinv-wishlist .social-buttons > span {\n    display: block;\n    margin-top: 0;\n    margin-right: 0;\n    margin-bottom: 5px;\n  }\n}\n@media only screen and (max-width: 768px) {\n  .tinv-wishlist.woocommerce .tinv-login form .form-row-first, .tinv-wishlist.woocommerce .tinv-login form .form-row-last {\n    float: none;\n    width: 100%;\n  }\n  .tinv-wishlist.woocommerce .tinv-login form .form-row-first {\n    padding: 0;\n  }\n  .tinv-wishlist.woocommerce .tinv-login form .form-row-last {\n    padding: 0;\n    margin-top: 10px;\n  }\n  .tinv-wishlist.woocommerce .tinv-login form .tinvwl-input-group-btn {\n    display: block;\n    padding: 0;\n    width: auto;\n    margin-top: 10px;\n  }\n}\n.tinv-overlay {\n  width: 100%;\n  height: 100%;\n  visibility: hidden;\n  opacity: 0;\n  background: #191919;\n}\n\n.tinv-modal.tinv-modal-open .tinv-overlay {\n  visibility: visible;\n  opacity: 0.5;\n}\n\n.admin-bar .tinv-wishlist .tinv-modal {\n  padding-top: 32px !important;\n}\n\n.tinv-wishlist .tinv-modal {\n  overflow-y: auto;\n  overflow-x: hidden;\n  width: 0;\n  height: 0;\n  z-index: 9999;\n  outline: 0 !important;\n  -webkit-backface-visibility: hidden;\n  visibility: hidden;\n  opacity: 0;\n  text-align: left;\n}\n.tinv-wishlist .tinv-modal .tinv-modal-inner {\n  position: relative;\n  margin: 0 auto;\n  background-color: #fff;\n  max-width: 360px;\n  padding: 40px;\n}\n.tinv-wishlist .tinv-modal.tinv-modal-open {\n  visibility: visible;\n  opacity: 1;\n  width: 100%;\n  height: 100%;\n}\n.tinv-wishlist .tinv-modal .tinv-close-modal {\n  display: inline-block;\n  position: absolute;\n  top: 17px;\n  right: 14px;\n  width: 26px;\n  height: 26px;\n  line-height: 26px;\n  font-size: 12px;\n  text-align: center;\n  border-radius: 50%;\n  border-bottom: 0;\n  -webkit-box-shadow: none !important;\n          box-shadow: none !important;\n  background-color: #ebe9eb;\n}\n.tinv-wishlist .tinv-modal .icon_big_heart_check, .tinv-wishlist .tinv-modal .icon_big_times, .tinv-wishlist .tinv-modal img {\n  display: block;\n  margin: 0 auto;\n  margin-bottom: 25px;\n  opacity: 1 !important;\n}\n.tinv-wishlist .tinv-modal ul {\n  overflow: visible;\n  list-style: disc;\n  margin: 10px 0 0 20px;\n}\n.tinv-wishlist .tinv-modal li {\n  list-style: disc !important;\n}\n.tinv-wishlist .tinv-create-list .tinv-modal ul, .tinv-wishlist.tinv-create-list form ul {\n  list-style: none !important;\n  margin: 25px 0 0;\n}\n.tinv-wishlist .tinv-create-list .tinv-modal li, .tinv-wishlist.tinv-create-list form li {\n  list-style: none !important;\n}\n.tinv-wishlist .tinv-modal .already-in {\n  margin-bottom: 35px;\n}\n.tinv-wishlist .tinv-modal .delete-notification {\n  margin-bottom: 25px;\n}\n.tinv-wishlist .tinv-modal .already-in ul {\n  overflow: visible;\n  margin: 12px 0 27px 17px;\n}\n.tinv-wishlist .tinv-modal select {\n  width: 100%;\n}\n.tinv-wishlist .tinv-modal button + .button, .tinv-wishlist .tinv-modal button + button {\n  margin-top: 12px;\n  width: 100%;\n}\n.tinv-wishlist .tinv-modal input + button {\n  margin-top: 12px;\n  width: 100%;\n}\n.tinv-wishlist .tinv-modal label + button, .tinv-wishlist .tinv-modal label + input {\n  margin-top: 12px;\n  width: 100%;\n}\n.tinv-wishlist .tinv-modal select + button, .tinv-wishlist .tinv-modal select + input {\n  margin-top: 12px;\n  width: 100%;\n}\n\n@media screen and (max-width: 768px) {\n  .admin-bar .tinv-wishlist .tinv-modal {\n    padding-top: 46px !important;\n  }\n}\n@media screen and (max-width: 600px) {\n  .admin-bar .tinv-wishlist .tinv-modal {\n    padding-top: 0 !important;\n  }\n  .tinv-wishlist .tinv-modal .tinv-close-modal {\n    position: static;\n    display: block;\n    margin: 0 auto 20px;\n  }\n}\n.tinv-wishlist .tinv-modal .already-in + label {\n  display: block;\n  margin-top: 6px;\n}\n.tinv-wishlist .tinv-modal label select {\n  margin-top: 8px;\n}\n.tinv-wishlist .tinv-modal .delete-notification + button {\n  width: 100%;\n}\n.tinv-wishlist .tinvwl_added_to_wishlist, .tinv-wishlist .tinvwl_created_wishlist {\n  text-align: center;\n}\n.tinv-wishlist .tinvwl_added_to_wishlist .tinv-txt {\n  margin-bottom: 25px;\n}\n.tinv-wishlist .tinvwl_created_wishlist .tinv-txt {\n  margin-bottom: 25px;\n}\n.tinv-wishlist .tinvwl_created_wishlist button {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.woocommerce .tinv-wishlist .tinvwl_added_to_wishlist.tinv-modal button.button {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.tinv-wishlist .tinv-modal .tinvwl-buttons-group {\n  margin-top: 20px;\n}\n.tinv-wishlist .tinvwl-buttons-group button {\n  width: 100%;\n}\n.tinv-wishlist .tinvwl-buttons-group button + button {\n  margin-top: 7px;\n}\n.tinv-wishlist .tinvwl-buttons-group button i {\n  position: relative;\n}\n.tinv-wishlist .tinvwl-buttons-group button i.ftinvwl-heart-o, .tinv-wishlist .tinvwl-buttons-group button i.ftinvwl-key, .tinv-wishlist .tinvwl-buttons-group button i.ftinvwl-times {\n  font-size: 20px;\n  top: 0;\n  vertical-align: initial;\n}\n.tinv-wishlist .tinvwl-buttons-group button i.ftinvwl-heart-o::before, .tinv-wishlist .tinvwl-buttons-group button i.ftinvwl-key::before, .tinv-wishlist .tinvwl-buttons-group button i.ftinvwl-times::before {\n  position: relative;\n  top: 3px;\n}\n.tinv-wishlist .tinvwl-buttons-group + button {\n  width: 100%;\n  margin-top: 7px;\n}\n.tinv-wishlist .tinv-modal h2 {\n  text-align: center;\n  margin: 0 0 35px;\n}\n.tinv-wishlist .tinv-create-list .tinv-modal-inner {\n  max-width: 778px;\n  padding: 30px;\n}\n.tinv-wishlist .tinvwl-has-error:not(.tinvwl-input-group), .tinv-wishlist .tinvwl-input-group.tinvwl-has-error .form-control {\n  border: 2px solid #FF0000;\n}\n.tinv-wishlist .tinvwl-has-error:not(.tinvwl-input-group) {\n  border: 2px solid #FF0000;\n}\n.tinv-wishlist .tinvwl-has-error + .tinvwl-error {\n  padding: 5px 0 0 5px;\n  color: #FF0000;\n}\n\n@media only screen and (max-width: 768px) {\n  .navigation-button, .social-buttons, .tinv-lists-nav {\n    margin-left: 12px;\n    margin-right: 12px;\n  }\n}\n.tinvwl-tooltip {\n  display: none;\n}\n\n.tinvwl-input-group {\n  position: relative;\n  display: table;\n  border-collapse: separate;\n}\n.tinvwl-input-group .form-control {\n  position: relative;\n  z-index: 1;\n  float: left;\n  height: 38px;\n  width: 100%;\n  margin: 0;\n}\n.tinvwl-input-group .form-control + .tinvwl-input-group-btn {\n  padding-left: 15px;\n}\n\n.tinv-wishlist .tinvwl-to-left .tinvwl-input-group .form-control + .tinvwl-input-group-btn {\n  padding-left: 10px;\n}\n.tinv-wishlist .tinv-search-form .tinvwl-input-group .form-control + .tinvwl-input-group-btn {\n  padding-left: 9px;\n}\n\n.tinvwl-input-group .form-control, .tinvwl-input-group-addon, .tinvwl-input-group-btn {\n  display: table-cell;\n}\n\n.tinvwl-input-group-addon, .tinvwl-input-group-btn {\n  width: 1%;\n  white-space: nowrap;\n  vertical-align: top;\n}\n\n@media only screen and (max-width: 768px) {\n  .tinvwl-input-group {\n    width: 100%;\n  }\n}\n@media only screen and (max-width: 768px) {\n  .tinvwl-input-group:not(.tinvwl-no-full) {\n    display: block;\n  }\n  .tinvwl-input-group:not(.tinvwl-no-full) .form-control, .tinvwl-input-group:not(.tinvwl-no-full) .tinvwl-input-group-addon, .tinvwl-input-group:not(.tinvwl-no-full) .tinvwl-input-group-btn {\n    display: block;\n  }\n  .tinvwl-input-group:not(.tinvwl-no-full) .form-control {\n    float: none;\n  }\n  .tinv-wishlist .tinv-search-form .tinvwl-input-group:not(.tinvwl-no-full) .form-control + .tinvwl-input-group-btn {\n    padding-top: 10px;\n    padding-left: 0;\n  }\n  .tinvwl-input-group:not(.tinvwl-no-full) .form-control + .tinvwl-input-group-btn {\n    padding-top: 10px;\n    padding-left: 0;\n  }\n  .tinvwl-input-group:not(.tinvwl-no-full) .tinvwl-input-group-addon {\n    width: 100%;\n  }\n  .tinvwl-input-group:not(.tinvwl-no-full) .tinvwl-input-group-addon > button, .tinvwl-input-group:not(.tinvwl-no-full) .tinvwl-input-group-addon > input {\n    width: 100%;\n  }\n  .tinvwl-input-group:not(.tinvwl-no-full) .tinvwl-input-group-btn {\n    width: 100%;\n  }\n  .tinvwl-input-group:not(.tinvwl-no-full) .tinvwl-input-group-btn > button, .tinvwl-input-group:not(.tinvwl-no-full) .tinvwl-input-group-btn > input {\n    width: 100%;\n  }\n}\n.tinv-table {\n  display: table;\n  width: 100%;\n  height: 100%;\n}\n\n.tinv-cell {\n  display: table-cell;\n  vertical-align: middle;\n}\n\n.tinv-wishlist .tinv-wishlist-clear {\n  visibility: visible;\n  width: auto;\n  height: auto;\n}\n\n.tinv-wishlist-clear:before {\n  content: \"\";\n  display: table;\n}\n.tinv-wishlist-clear:after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.icon_big_heart_check {\n  display: inline-block;\n  width: 46px;\n  height: 46px;\n  font-family: tinvwl-webfont !important;\n  speak: none;\n  font-style: normal;\n  font-weight: 400;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  font-size: 60px;\n}\n\n.icon_big_times {\n  display: inline-block;\n  width: 46px;\n  height: 46px;\n  font-family: tinvwl-webfont !important;\n  speak: none;\n  font-style: normal;\n  font-weight: 400;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  font-size: 60px;\n}\n.icon_big_times::before {\n  content: \"\\e904\";\n  top: -7px;\n  left: -7px;\n  position: relative;\n}\n\n.icon_big_heart_check::before {\n  content: \"\\e90a\";\n  top: -7px;\n  left: -7px;\n  position: relative;\n}\n\n.tinvwl_add_to_wishlist_button.tinvwl-button.disabled-add-wishlist, .tinvwl_add_to_wishlist_button.disabled-add-wishlist {\n  opacity: 0.5 !important;\n  cursor: not-allowed;\n}\n\n.empty-name-wishlist {\n  border-color: #FF0000 !important;\n}\n\n.tinvwl_remove_from_wishlist-text, .tinvwl_already_on_wishlist-text, .tinvwl_add_to_wishlist_button.tinvwl-icon-custom img.icon-already-on-wishlist {\n  display: none !important;\n}\n\n.tinvwl-product-in-list.tinvwl-product-make-remove .tinvwl_remove_from_wishlist-text, .tinvwl-product-in-list.tinvwl-product-make-remove .tinvwl_already_on_wishlist-text, .tinvwl-product-in-list.tinvwl-product-make-remove img.icon-already-on-wishlist, .tinvwl_add_to_wishlist_button.tinvwl-product-in-list .tinvwl_remove_from_wishlist-text, .tinvwl_add_to_wishlist_button.tinvwl-product-in-list .tinvwl_already_on_wishlist-text, .tinvwl_add_to_wishlist_button.tinvwl-product-in-list img.icon-already-on-wishlist {\n  display: inline !important;\n}\n.tinvwl-product-in-list.tinvwl-product-make-remove .tinvwl_add_to_wishlist-text, .tinvwl-product-in-list.tinvwl-product-make-remove img.icon-add-on-wishlist, .tinvwl_add_to_wishlist_button.tinvwl-product-in-list .tinvwl_add_to_wishlist-text, .tinvwl_add_to_wishlist_button.tinvwl-product-in-list img.icon-add-on-wishlist {\n  display: none !important;\n}\n\n@media only screen and (max-width: 1024px) {\n  .tinv-wishlist .tinvwl-table-manage-list .product-remove {\n    display: table-cell;\n  }\n}\n@media only screen and (max-width: 768px) {\n  .tinv-wishlist table.tinvwl-table-manage-list tbody td.product-remove {\n    display: block;\n  }\n}\n.tooltipped {\n  position: relative;\n}\n\n.tooltipped::after {\n  position: absolute;\n  z-index: 1000000;\n  display: none;\n  padding: 0.5em 0.75em;\n  font: normal normal 11px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Helvetica, Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\";\n  -webkit-font-smoothing: subpixel-antialiased;\n  color: #fff;\n  text-align: center;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-wrap: break-word;\n  white-space: pre;\n  pointer-events: none;\n  content: attr(aria-label);\n  background: #1b1f23;\n  border-radius: 3px;\n  opacity: 0;\n}\n\n.tooltipped::before {\n  position: absolute;\n  z-index: 1000001;\n  display: none;\n  width: 0;\n  height: 0;\n  color: #1b1f23;\n  pointer-events: none;\n  content: \"\";\n  border: 6px solid transparent;\n  opacity: 0;\n}\n\n@-webkit-keyframes tooltip-appear {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes tooltip-appear {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n.tooltipped:hover::before, .tooltipped:hover::after,\n.tooltipped:active::before,\n.tooltipped:active::after,\n.tooltipped:focus::before,\n.tooltipped:focus::after {\n  display: inline-block;\n  text-decoration: none;\n  -webkit-animation-name: tooltip-appear;\n          animation-name: tooltip-appear;\n  -webkit-animation-duration: 0.1s;\n          animation-duration: 0.1s;\n  -webkit-animation-fill-mode: forwards;\n          animation-fill-mode: forwards;\n  -webkit-animation-timing-function: ease-in;\n          animation-timing-function: ease-in;\n  -webkit-animation-delay: 0.4s;\n          animation-delay: 0.4s;\n}\n\n.tooltipped-no-delay:hover::before, .tooltipped-no-delay:hover::after,\n.tooltipped-no-delay:active::before,\n.tooltipped-no-delay:active::after,\n.tooltipped-no-delay:focus::before,\n.tooltipped-no-delay:focus::after {\n  -webkit-animation-delay: 0s;\n          animation-delay: 0s;\n}\n\n.tooltipped-multiline:hover::after,\n.tooltipped-multiline:active::after,\n.tooltipped-multiline:focus::after {\n  display: table-cell;\n}\n\n.tooltipped-s::after,\n.tooltipped-se::after,\n.tooltipped-sw::after {\n  top: 100%;\n  right: 50%;\n  margin-top: 6px;\n}\n.tooltipped-s::before,\n.tooltipped-se::before,\n.tooltipped-sw::before {\n  top: auto;\n  right: 50%;\n  bottom: -7px;\n  margin-right: -6px;\n  border-bottom-color: #1b1f23;\n}\n\n.tooltipped-se::after {\n  right: auto;\n  left: 50%;\n  margin-left: -16px;\n}\n\n.tooltipped-sw::after {\n  margin-right: -16px;\n}\n\n.tooltipped-n::after,\n.tooltipped-ne::after,\n.tooltipped-nw::after {\n  right: 50%;\n  bottom: 100%;\n  margin-bottom: 6px;\n}\n.tooltipped-n::before,\n.tooltipped-ne::before,\n.tooltipped-nw::before {\n  top: -7px;\n  right: 50%;\n  bottom: auto;\n  margin-right: -6px;\n  border-top-color: #1b1f23;\n}\n\n.tooltipped-ne::after {\n  right: auto;\n  left: 50%;\n  margin-left: -16px;\n}\n\n.tooltipped-nw::after {\n  margin-right: -16px;\n}\n\n.tooltipped-s::after,\n.tooltipped-n::after {\n  -webkit-transform: translateX(50%);\n          transform: translateX(50%);\n}\n\n.tooltipped-w::after {\n  right: 100%;\n  bottom: 50%;\n  margin-right: 6px;\n  -webkit-transform: translateY(50%);\n          transform: translateY(50%);\n}\n.tooltipped-w::before {\n  top: 50%;\n  bottom: 50%;\n  left: -7px;\n  margin-top: -6px;\n  border-left-color: #1b1f23;\n}\n\n.tooltipped-e::after {\n  bottom: 50%;\n  left: 100%;\n  margin-left: 6px;\n  -webkit-transform: translateY(50%);\n          transform: translateY(50%);\n}\n.tooltipped-e::before {\n  top: 50%;\n  right: -7px;\n  bottom: 50%;\n  margin-top: -6px;\n  border-right-color: #1b1f23;\n}\n\n.tooltipped-align-right-1::after,\n.tooltipped-align-right-2::after {\n  right: 0;\n  margin-right: 0;\n}\n\n.tooltipped-align-right-1::before {\n  right: 10px;\n}\n\n.tooltipped-align-right-2::before {\n  right: 15px;\n}\n\n.tooltipped-align-left-1::after,\n.tooltipped-align-left-2::after {\n  left: 0;\n  margin-left: 0;\n}\n\n.tooltipped-align-left-1::before {\n  left: 5px;\n}\n\n.tooltipped-align-left-2::before {\n  left: 10px;\n}\n\n.tooltipped-multiline::after {\n  width: -webkit-max-content;\n  width: -moz-max-content;\n  width: max-content;\n  max-width: 250px;\n  word-wrap: break-word;\n  white-space: pre-line;\n  border-collapse: separate;\n}\n.tooltipped-multiline.tooltipped-s::after, .tooltipped-multiline.tooltipped-n::after {\n  right: auto;\n  left: 50%;\n  -webkit-transform: translateX(-50%);\n          transform: translateX(-50%);\n}\n.tooltipped-multiline.tooltipped-w::after, .tooltipped-multiline.tooltipped-e::after {\n  right: 100%;\n}\n\n@media screen and (min-width: 0 \\0 ) {\n  .tooltipped-multiline::after {\n    width: 250px;\n  }\n}\n.tooltipped-sticky::before, .tooltipped-sticky::after {\n  display: inline-block;\n}\n.tooltipped-sticky.tooltipped-multiline::after {\n  display: table-cell;\n}\n\n.tinvwl-table-manage-list .component_table_item_price:before {\n  font-family: FontAwesomeCP;\n  font-size: 1rem;\n  display: inline-block;\n  -webkit-transform: rotate(90deg);\n          transform: rotate(90deg);\n  content: \"\\e811\";\n  opacity: 0.25;\n  margin: 0px 12px 0px 2px;\n}\n\n.wishlist-icon {\n  position: relative;\n}\n\n.wishlist-popup {\n  opacity: 0;\n  max-height: 0;\n  position: absolute;\n  overflow: hidden;\n  padding: 5px;\n  margin-top: -10px;\n  border-radius: 5px;\n  line-height: 1.3;\n  text-align: center;\n  font-size: 0.9em;\n  top: 100%;\n  background-color: rgba(0, 0, 0, 0.8);\n  right: 0;\n  color: #FFF;\n  -webkit-transition: opacity 0.3s, max-height 0.3s;\n  transition: opacity 0.3s, max-height 0.3s;\n  -webkit-transition-delay: 0.3s;\n          transition-delay: 0.3s;\n}\n\n.wishlist-popup:after {\n  bottom: 100%;\n  right: 10px;\n  border: solid transparent;\n  content: \" \";\n  height: 0;\n  width: 0;\n  position: absolute;\n  pointer-events: none;\n  border-color: rgba(136, 183, 213, 0);\n  border-bottom-color: rgba(0, 0, 0, 0.8);\n  border-width: 10px;\n  margin-left: -10px;\n}\n\n.wishlist-icon:hover .wishlist-popup {\n  opacity: 1;\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n.wishlist-popup a {\n  color: #ccc;\n  display: block;\n}\n\n.wishlist-popup a:hover {\n  color: #FFF;\n}\n\n.wishlist-icon.added:after {\n  background-color: red;\n  -webkit-transform: translateY(-3px);\n          transform: translateY(-3px);\n}\n\n.wishlist-popup .ajax-loading,\n.wishlist-popup .feedback {\n  display: none !important;\n}\n\n.wishlist-title {\n  margin-bottom: 20px;\n}\n\nspan.tinvwl-product-stats {\n  background-color: #ebe9eb;\n  border-radius: 50%;\n  padding: 0.2em;\n  line-height: 1;\n  font-size: 0.7em;\n  min-width: 1.5em;\n  display: inline-block;\n  position: absolute;\n  top: -0.7em;\n  right: -1.5em;\n  text-align: center;\n}"]}