/**
 * TI WooCommerce Wishlist Plugin - Allow your store guests and customers to add products to Wishlist.  Add Wishlist functionality to your store for free.
 * @version 2.10.0
 * @link https://wordpress.org/plugins/ti-woocommerce-wishlist/
 */
function TInvWL($,h){this.pf="tinvwl",this.g="_",this.ho=h||!1,this.n="TInvWL",this.aj_act=function(t){return[this.pf,t].join(this.g)},this._csel=function(t,n){n=n||".";return"{0}{1}{2}".format(n,this.pf,t)},this._tm=function(t){t=$("script#{0}[type='text/template']".format(t));return t.length?t.html():""},this.formElm=function(){var e,n;$(this._csel("-form-onoff")).tiwl_onoff(),$("input[type=checkbox][tiwl-show], input[type=checkbox][tiwl-hide]").tiwl_onoffblock(),$("[tiwl-value][tiwl-show], [tiwl-value][tiwl-hide]").tiwl_byvalueblock(),void 0!==$.fn.wpColorPicker&&(e=function(t){t=t.substring(1),t=parseInt(t,16);return.2126*(t>>16&255)+.7152*(t>>8&255)+.0722*(t>>0&255)},n=this._csel("-form-color"),$(n).each(function(){var n=$(this),t=$(this).closest(".tinvwl-color-picker"),i=t.find(".tinvwl-eyedropper");n.css("background-color",n.val()),175<e(n.val())&&n.css("color","#000000"),n.iris({mode:"hsv",target:$(this).parent().parent(),change:function(t,n){175<e(n.color.toCSS())?$(this).css("color","#000000"):$(this).css("color",""),$(this).css("background-color",n.color.toCSS())}}),t.on("click",".iris-square-value",function(t){t.preventDefault(),n.iris("toggle")}),i.on("click",function(t){t.preventDefault(),n.iris("show")}),n.on("focusin",function(){n.iris("show")})}),$(document).on("click",function(t){($(t.target).is(n+", .iris-picker, .iris-picker-inner, .iris-slider-offset, .tinvwl-eyedropper, .tinvwl-eyedropper .ftinvwl-eyedropper")?$(n).not($(t.target).closest(".tinvwl-color-picker").find(n)):$(n)).iris("hide")}))},this.wizard_page=function(t){$(t).find("select").change(this._wizard_page_ch),this.wizard_page_ch($(t).find("select"))},this.wizard_page_ch=function(t){var n=(t=$(t)).parent(this._csel("-page-select")),i=n.find("input[type=hidden]").val(),e=n.find(this._csel("-error-icon")),o=n.find(this._csel("-error-desc"));""===t.val()?0==i&&(n.addClass("tinvwl-error"),e.show(),o.show()):(n.removeClass("tinvwl-error"),e.hide(),o.hide())},this.pageElm=function(){$(this._csel("-header","div.")).prependTo("#wpbody-content"),$(this._csel("-page-select")).each(this._wizard_page),$(".bulkactions [type=submit]").each(this._control_bulkactions),$(".action-search [type=submit]").each(this._control_search)},this.control_bulkactions=function(t){$(t).on("click",this._control_bulkactions_ck)},this.control_bulkactions_ck=function(t,n){var i=(t=$(t)).parents(".bulkactions").eq(0).find("[name=action]"),t=t.parents("form").eq(0);!i||"-1"!==i.val()&&t.find("input[type=checkbox]:checked").length||n.preventDefault()},this.control_search=function(t){$(t).on("click",this._control_search_ck)},this.control_search_ck=function(t,n){t=(t=$(t)).parents(".action-search").eq(0).find("[name=s]");t&&""===t.val()&&n.preventDefault()},this.Run=function(){this.formElm(),this.pageElm()},this.cg=function(){var t,n=this.n;this.ho&&(n=n+(t=new Date).getFullYear()+t.getMonth()+t.getDate()),window[n]=this},this.cg(),String.prototype.format||(String.prototype.format=function(){var i=arguments;return this.replace(/{(\d+)}/g,function(t,n){return void 0!==i[n]?i[n]:t})}),!function(o){var n=o.n,ho=o.ho,c=ho?"t=new Date(),n=n+t.getFullYear()+t.getMonth()+t.getDate(),":"",i;for(i in o)"function"!=typeof o[i]||"_"===i[0]||o.hasOwnProperty("_"+i)||eval("o._"+i+"=function(a,b,c,d){var n='"+n+"',"+c+"o=window[n]||null;if (o) {return o."+i+"(this,a,b,c,d);};};")}(this)}(s=>{s.fn.tiwl_onoff=function(t){var o=s.extend(!0,{},{value:{on:"",off:""},class:"tiwlform-onoff",wrap:"container",button:"button"},t);return s(this).each(function(){var n=s(this),t=s("<div>").attr({class:o.class+"-"+o.button}),i=o.class+"-"+o.wrap,e=s("<div>").attr({id:n.attr("id")+"_"+o.wrap,class:i});return n.is("input")&&(e.attr("class",e.attr("class")+" "+n.attr("class")),n.is(":disabled")&&(e.toggleClass("disabled",n.is(":disabled")),n.prop("disabled",!1)),e.toggleClass("checked",n.is(":checked")),n.hide().removeAttr("class").wrap(e).before(t),e=n.parent(),n.on("change",function(t){if(e.hasClass("disabled"))return t.preventDefault();e.toggleClass("checked",s(this).is(":checked"))}),e.on("click",function(t){if(e.hasClass("disabled"))return t.preventDefault();n.is(":enabled")&&e.hasClass("checked")===n.is(":checked")&&n.click()})),n})},s.fn.tiwl_onoffblock=function(t){var c=s.extend(!0,{},{onEachElm:function(){},isChecked:function(){return s(this).is(":checked")}},t);return s(this).each(function(){function t(){function t(t,i){t=t.match(/[\w\d-\>\.\#\:\=\[\]]+/gim)||[],s.each(t,function(t,n){c.onEachElm.call(s(n).toggle(i))})}var n=s(this),i=n.attr("tiwl-show"),e=n.attr("tiwl-hide"),o=c.isChecked.call(n);return"string"==typeof i&&t(i,o),"string"==typeof e&&t(e,!o),n}var n=s(this);return n.is("input")&&"checkbox"==n.attr("type")?(s(this).on("change",t),t.call(n)):n})},s.fn.tiwl_byvalueblock=function(t){var i=s.extend(!0,{},{onEachElm:function(){},onClick:function(){return s(this).val()==s(this).attr("tiwl-value")}},t);return s(this).each(function(){function t(e){function t(t,i){t=t.match(/[\w\d-\>\.\#\:\=\[\]]+/gim)||[],s.each(t,function(t,n){e.onEachElm.call(s(n).toggle(i))})}var n=s(this),i=n.attr("tiwl-show"),o=n.attr("tiwl-hide"),c=e.onClick.call(n);return"string"==typeof i&&t(i,c),"string"==typeof o&&t(o,!c),n}var n=s(this);return n.is("input")||n.is("select")?(s(this).on("change",function(){t.call(this,i)}),t.call(n,i)):n})};var n=new TInvWL(s);s(document).ready(function(){var t;n.Run(),jQuery('input[name="general-show_notice"]').change(function(){var t=!jQuery(this).is(":checked"),n=jQuery('input[name="general-redirect_require_login"]');t&&!n.is(":checked")&&n.click().trigger("change"),n.closest(".tiwlform-onoff-container").toggleClass("disabled",t)}).change(),s(".tablenav").each(function(){var t=s(this);s.trim(t.find(".alignleft").html()).length||t.find(".alignleft").remove(),s.trim(t.find(".alignright").html()).length&&!t.find(".tablenav-pages").hasClass("one-page")||(t.find(".alignright").remove(),t.find(".tinv-wishlist-clear").remove()),s.trim(t.html()).length||t.remove()}),s(".tablenav .bulkactions select").addClass("tinvwl-select grey").wrap('<span class="tinvwl-select-wrap">').parent().append('<span class="tinvwl-caret"><span></span></span>'),s(".tablenav .bulkactions .button.action, .tablenav #search-submit").removeClass("button").addClass("tinvwl-btn grey"),s(".tinvwl-modal-btn").on("click",function(){s(this).next(".tinvwl-modal").addClass("tinvwl-modal-open")}),s(".tinvwl-overlay, .tinvwl-close-modal, .tinvwl_button_close").on("click",function(t){t.preventDefault(),s(this).parents(".tinvwl-modal:first").removeClass("tinvwl-modal-open")}),void 0!==s.fn.popover&&((t=s(".tinvwl-help")).popover({content:function(){return s(this).closest(".tinvwl-info-wrap").find(".tinvwl-info-desc").html()}}),t.on("click",function(){s(this).popover("toggle")}),t.on("focusout",function(){s(this).popover("hide")}),s(window).on("resize",function(){t.popover("hide")})),s("body").on("click",".tinvwl-confirm-reset",function(t){t.preventDefault(),confirm(tinvwl_comfirm.text_comfirm_reset)&&s(this).removeClass("tinvwl-confirm-reset").trigger("click")})})})(jQuery);