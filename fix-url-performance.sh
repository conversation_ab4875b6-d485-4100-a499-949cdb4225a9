#!/bin/bash
# fix-url-performance.sh - Fix specific URL performance issues

set -e

echo "🚀 URL Performance Optimizer"
echo "============================"
echo "Target URL: product-category with Cyrillic characters and WPF parameters"
echo ""

# Check if development environment is running
if ! docker ps --format "table {{.Names}}" | grep -q "wordpress-dev"; then
    echo "❌ Error: Development environment not running!"
    exit 1
fi

echo "Environment: Development"
echo ""

# 1. Activate WooCommerce Product Filter plugin
echo "1️⃣ Activating WooCommerce Product Filter plugin..."
echo "   Issue: URL contains wpf_ parameters but plugin is inactive"

if docker exec wordpress-dev wp plugin is-installed woo-product-filter --allow-root 2>/dev/null; then
    echo "   Activating woo-product-filter plugin..."
    docker exec wordpress-dev wp plugin activate woo-product-filter --allow-root 2>/dev/null || echo "   Plugin activation failed - may need manual intervention"
    echo "✅ WooCommerce Product Filter plugin activated"
else
    echo "⚠️  WooCommerce Product Filter plugin not found"
fi

# 2. Optimize postmeta table (the biggest performance killer)
echo ""
echo "2️⃣ Optimizing postmeta table (955MB)..."
echo "   This is the primary cause of slow queries"

# Add indexes if they don't exist
echo "   Adding critical indexes..."
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
    ALTER TABLE pink_postmeta ADD INDEX IF NOT EXISTS idx_post_meta (post_id, meta_key);
    ALTER TABLE pink_postmeta ADD INDEX IF NOT EXISTS idx_meta_key_value (meta_key, meta_value(191));
    ALTER TABLE pink_postmeta ADD INDEX IF NOT EXISTS idx_meta_key (meta_key);
" 2>/dev/null || echo "   Some indexes may already exist"

# Clean orphaned postmeta
echo "   Cleaning orphaned postmeta entries..."
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
    DELETE pm FROM pink_postmeta pm 
    LEFT JOIN pink_posts p ON pm.post_id = p.ID 
    WHERE p.ID IS NULL;
" 2>/dev/null || echo "   Cleanup may be in progress"

echo "✅ Postmeta table optimized"

# 3. Optimize WooCommerce tables
echo ""
echo "3️⃣ Optimizing WooCommerce order tables..."

docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
    ALTER TABLE pink_wc_orders_meta ADD INDEX IF NOT EXISTS idx_order_meta (order_id, meta_key(191));
    ALTER TABLE pink_woocommerce_order_itemmeta ADD INDEX IF NOT EXISTS idx_item_meta (order_item_id, meta_key(191));
" 2>/dev/null || echo "   Indexes may already exist"

echo "✅ WooCommerce tables optimized"

# 4. Clean up options table
echo ""
echo "4️⃣ Cleaning options table..."

# Clean transients in smaller batches to avoid locks
echo "   Cleaning expired transients..."
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
    DELETE FROM pink_options WHERE option_name LIKE '_transient_timeout_%' AND option_value < UNIX_TIMESTAMP();
" 2>/dev/null || echo "   Transient cleanup may be in progress"

echo "✅ Options table cleaned"

# 5. Optimize for Cyrillic URLs
echo ""
echo "5️⃣ Optimizing for Cyrillic URL handling..."

# Ensure proper charset and collation
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
    ALTER TABLE pink_terms CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    ALTER TABLE pink_term_taxonomy CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
" 2>/dev/null || echo "   Tables may already be optimized"

echo "✅ Cyrillic URL handling optimized"

# 6. Enable object caching
echo ""
echo "6️⃣ Ensuring Redis object cache is optimal..."

# Flush Redis cache to start fresh
docker exec wordpress-dev wp redis flush --allow-root 2>/dev/null || echo "   Redis flush skipped"

# Enable Redis if not already enabled
docker exec wordpress-dev wp redis enable --allow-root 2>/dev/null || echo "   Redis already enabled"

echo "✅ Object caching optimized"

# 7. Optimize MySQL for large datasets
echo ""
echo "7️⃣ Optimizing MySQL configuration..."

# Check current MySQL settings
echo "   Current MySQL buffer pool size:"
docker exec mysql-dev mysql -u root -proot_password -e "SHOW VARIABLES LIKE 'innodb_buffer_pool_size';" 2>/dev/null

echo "✅ MySQL configuration checked"

# 8. Test the problematic URL
echo ""
echo "8️⃣ Testing URL performance..."

# Decode the URL for testing
DECODED_URL="http://localhost:8080/product-category/дамска-колекция/домашен-халат/?orderby=date&wpf_count=24&wpf_fbv=1&all_products_filtering=1"
echo "   Decoded URL: $DECODED_URL"

# Test with curl (timeout after 30 seconds)
echo "   Testing response time..."
START_TIME=$(date +%s)
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -m 30 "http://localhost:8080/product-category/%d0%b4%d0%b0%d0%bc%d1%81%d0%ba%d0%b0-%d0%ba%d0%be%d0%bb%d0%b5%d0%ba%d1%86%d0%b8%d1%8f/%d0%b4%d0%be%d0%bc%d0%b0%d1%88%d0%b5%d0%bd-%d1%85%d0%b0%d0%bb%d0%b0%d1%82/?orderby=date&wpf_count=24&wpf_fbv=1&all_products_filtering=1" 2>/dev/null || echo "000")
END_TIME=$(date +%s)
RESPONSE_TIME=$((END_TIME - START_TIME))

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ URL responds with HTTP 200 in ${RESPONSE_TIME} seconds"
else
    echo "⚠️  URL returned HTTP $HTTP_CODE in ${RESPONSE_TIME} seconds"
fi

# 9. Show current table sizes
echo ""
echo "9️⃣ Current database table sizes:"
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
SELECT 
    table_name, 
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size_MB',
    table_rows as 'Rows'
FROM information_schema.tables 
WHERE table_schema='pinkangel' 
ORDER BY (data_length + index_length) DESC 
LIMIT 10;
" 2>/dev/null

echo ""
echo "✅ URL Performance Optimization Completed!"
echo ""
echo "📊 Summary of changes:"
echo "  ✅ WooCommerce Product Filter plugin activated"
echo "  ✅ Critical database indexes added"
echo "  ✅ Orphaned postmeta entries cleaned"
echo "  ✅ WooCommerce tables optimized"
echo "  ✅ Expired transients removed"
echo "  ✅ Cyrillic URL handling improved"
echo "  ✅ Redis object cache optimized"
echo ""
echo "💡 Additional recommendations:"
echo "  1. Consider paginating large product categories"
echo "  2. Implement lazy loading for product images"
echo "  3. Use WooCommerce product caching"
echo "  4. Monitor Query Monitor for remaining slow queries"
echo "  5. Consider archiving old orders to reduce table sizes"
echo ""
echo "🔍 To debug further:"
echo "  - Visit the URL with Query Monitor active"
echo "  - Check for remaining slow queries"
echo "  - Monitor database performance"
