#!/bin/bash
# safe-database-cleanup.sh - Safe incremental database cleanup to avoid high CPU usage

set -e

echo "🧹 Safe Database Cleanup"
echo "======================="
echo "Performing incremental cleanup to avoid high CPU usage"
echo ""

# Check if development environment is running
if ! docker ps --format "table {{.Names}}" | grep -q "wordpress-dev"; then
    echo "❌ Error: Development environment not running!"
    exit 1
fi

echo "Environment: Development"
echo ""

# Function to check if any heavy queries are running
check_heavy_queries() {
    HEAVY_COUNT=$(docker exec mysql-dev mysql -u root -proot_password -e "
    SELECT COUNT(*) FROM information_schema.processlist 
    WHERE command = 'Query' AND time > 30 AND info LIKE '%DELETE%';
    " 2>/dev/null | tail -1)
    echo $HEAVY_COUNT
}

# Function to wait for heavy queries to finish
wait_for_queries() {
    echo "   Checking for running heavy queries..."
    while [ $(check_heavy_queries) -gt 0 ]; do
        echo "   Waiting for heavy queries to complete..."
        sleep 5
    done
    echo "   No heavy queries running"
}

# 1. Check current database load
echo "1️⃣ Checking current database load..."
wait_for_queries

# Show current process list
echo "   Current MySQL processes:"
docker exec mysql-dev mysql -u root -proot_password -e "
SELECT id, user, host, db, command, time, state, LEFT(info, 50) as query_start
FROM information_schema.processlist 
WHERE command != 'Sleep' AND id != CONNECTION_ID();
" 2>/dev/null || echo "   No active queries"

echo ""

# 2. Safe transient cleanup (small batches)
echo "2️⃣ Safe transient cleanup (small batches)..."
echo "   Cleaning expired transients in small batches to avoid locks"

# Clean in small batches of 100 records at a time
for i in {1..10}; do
    echo "   Batch $i: Cleaning expired transients..."
    
    DELETED=$(docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
    DELETE FROM pink_options 
    WHERE option_name LIKE '_transient_timeout_%' 
    AND option_value < UNIX_TIMESTAMP() 
    LIMIT 100;
    SELECT ROW_COUNT() as deleted;
    " 2>/dev/null | tail -1)
    
    if [ "$DELETED" = "0" ]; then
        echo "   No more expired transients to clean"
        break
    else
        echo "   Deleted $DELETED expired transients"
        sleep 2  # Small pause between batches
    fi
done

echo "✅ Transient cleanup completed safely"

# 3. Safe orphaned postmeta cleanup (very small batches)
echo ""
echo "3️⃣ Safe orphaned postmeta cleanup..."
echo "   ⚠️  This will be done in very small batches due to table size (955MB)"

# First, check how many orphaned records exist
echo "   Checking for orphaned postmeta records..."
ORPHANED_COUNT=$(docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
SELECT COUNT(*) FROM pink_postmeta pm 
LEFT JOIN pink_posts p ON pm.post_id = p.ID 
WHERE p.ID IS NULL 
LIMIT 1000;
" 2>/dev/null | tail -1)

echo "   Found approximately $ORPHANED_COUNT orphaned postmeta records (checked first 1000)"

if [ "$ORPHANED_COUNT" -gt 0 ]; then
    echo "   Cleaning orphaned postmeta in batches of 50..."
    
    for i in {1..5}; do
        echo "   Batch $i: Cleaning orphaned postmeta..."
        
        DELETED=$(docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
        DELETE pm FROM pink_postmeta pm 
        LEFT JOIN pink_posts p ON pm.post_id = p.ID 
        WHERE p.ID IS NULL 
        LIMIT 50;
        SELECT ROW_COUNT() as deleted;
        " 2>/dev/null | tail -1)
        
        if [ "$DELETED" = "0" ]; then
            echo "   No more orphaned postmeta in this batch"
            break
        else
            echo "   Deleted $DELETED orphaned postmeta records"
            sleep 5  # Longer pause for postmeta operations
        fi
        
        # Check if any heavy queries are running
        if [ $(check_heavy_queries) -gt 0 ]; then
            echo "   ⚠️  Heavy query detected, stopping cleanup"
            break
        fi
    done
else
    echo "   No orphaned postmeta records found"
fi

echo "✅ Orphaned postmeta cleanup completed safely"

# 4. Optimize tables (lightweight)
echo ""
echo "4️⃣ Lightweight table optimization..."

# Only optimize smaller tables to avoid locks
SMALL_TABLES=("pink_options" "pink_usermeta" "pink_terms" "pink_term_taxonomy")

for table in "${SMALL_TABLES[@]}"; do
    echo "   Optimizing $table..."
    docker exec mysql-dev mysql -u root -proot_password pinkangel -e "OPTIMIZE TABLE $table;" 2>/dev/null || echo "   Optimization skipped for $table"
    sleep 1
done

echo "✅ Table optimization completed"

# 5. Check final status
echo ""
echo "5️⃣ Final database status..."

# Show table sizes
echo "   Current table sizes (top 10):"
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
SELECT 
    table_name, 
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size_MB',
    table_rows as 'Rows'
FROM information_schema.tables 
WHERE table_schema='pinkangel' 
ORDER BY (data_length + index_length) DESC 
LIMIT 10;
" 2>/dev/null

# Check for any running processes
echo ""
echo "   Current MySQL processes:"
docker exec mysql-dev mysql -u root -proot_password -e "
SELECT COUNT(*) as active_queries
FROM information_schema.processlist 
WHERE command = 'Query' AND time > 5;
" 2>/dev/null

echo ""
echo "✅ Safe Database Cleanup Completed!"
echo ""
echo "📊 Summary:"
echo "  ✅ Cleaned expired transients safely"
echo "  ✅ Removed orphaned postmeta (limited batches)"
echo "  ✅ Optimized smaller tables"
echo "  ✅ No high CPU usage operations"
echo ""
echo "💡 For further cleanup:"
echo "  - Run this script multiple times for incremental cleanup"
echo "  - Monitor CPU usage during operations"
echo "  - Consider scheduling during low-traffic periods"
echo ""
echo "🔍 Monitor with:"
echo "  docker exec mysql-dev mysql -u root -proot_password -e 'SHOW PROCESSLIST;'"
echo "  docker stats mysql-dev"
