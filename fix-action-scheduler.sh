#!/bin/bash
# fix-action-scheduler.sh - Fix Action Scheduler duplicate entry error

set -e

echo "🔧 Action Scheduler Fix"
echo "======================"
echo "Fixing: Duplicate entry '0' for key 'pink_actionscheduler_actions.PRIMARY'"
echo ""

# Check if development environment is running
if ! docker ps --format "table {{.Names}}" | grep -q "wordpress-dev"; then
    echo "❌ Error: Development environment not running!"
    exit 1
fi

echo "Environment: Development"
echo ""

# 1. Analyze the current state
echo "1️⃣ Analyzing Action Scheduler table..."
echo "   Checking current state of pink_actionscheduler_actions table"

docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
SELECT 
    COUNT(*) as total_actions,
    MAX(action_id) as max_id,
    MIN(action_id) as min_id,
    COUNT(CASE WHEN action_id = 0 THEN 1 END) as zero_id_count
FROM pink_actionscheduler_actions;
" 2>/dev/null

echo ""

# 2. Check for records with action_id = 0
echo "2️⃣ Checking for problematic records..."
ZERO_COUNT=$(docker exec mysql-dev mysql -u root -proot_password pinkangel -e "SELECT COUNT(*) FROM pink_actionscheduler_actions WHERE action_id = 0;" 2>/dev/null | tail -1)

if [ "$ZERO_COUNT" -gt 0 ]; then
    echo "   Found $ZERO_COUNT records with action_id = 0"
    echo "   These are causing the duplicate entry error"
    
    # Show the problematic records
    echo "   Problematic records:"
    docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
    SELECT action_id, hook, status, scheduled_date_gmt 
    FROM pink_actionscheduler_actions 
    WHERE action_id = 0 
    LIMIT 5;
    " 2>/dev/null
else
    echo "   No records with action_id = 0 found"
fi

echo ""

# 3. Fix the AUTO_INCREMENT issue
echo "3️⃣ Fixing AUTO_INCREMENT attribute..."

# First, get the current maximum ID
MAX_ID=$(docker exec mysql-dev mysql -u root -proot_password pinkangel -e "SELECT COALESCE(MAX(action_id), 0) FROM pink_actionscheduler_actions;" 2>/dev/null | tail -1)
NEXT_ID=$((MAX_ID + 1))

echo "   Current max ID: $MAX_ID"
echo "   Setting AUTO_INCREMENT to: $NEXT_ID"

# Fix the AUTO_INCREMENT attribute
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
ALTER TABLE pink_actionscheduler_actions 
MODIFY COLUMN action_id bigint unsigned NOT NULL AUTO_INCREMENT;
" 2>/dev/null || echo "   AUTO_INCREMENT already set"

# Set the AUTO_INCREMENT value
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
ALTER TABLE pink_actionscheduler_actions AUTO_INCREMENT = $NEXT_ID;
" 2>/dev/null

echo "✅ AUTO_INCREMENT fixed"

# 4. Handle records with action_id = 0
echo ""
echo "4️⃣ Fixing records with action_id = 0..."

if [ "$ZERO_COUNT" -gt 0 ]; then
    echo "   Updating records with action_id = 0 to use proper IDs"
    
    # Update records with action_id = 0 to use new auto-increment IDs
    docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
    SET @new_id = $NEXT_ID;
    UPDATE pink_actionscheduler_actions 
    SET action_id = (@new_id := @new_id + 1) 
    WHERE action_id = 0;
    " 2>/dev/null
    
    echo "✅ Fixed $ZERO_COUNT records with invalid IDs"
else
    echo "   No records with action_id = 0 to fix"
fi

# 5. Clean up old/completed actions to prevent future issues
echo ""
echo "5️⃣ Cleaning up old Action Scheduler entries..."

# Delete completed actions older than 30 days
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
DELETE FROM pink_actionscheduler_actions 
WHERE status = 'complete' 
AND scheduled_date_gmt < DATE_SUB(NOW(), INTERVAL 30 DAY);
" 2>/dev/null

# Delete failed actions older than 7 days
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
DELETE FROM pink_actionscheduler_actions 
WHERE status = 'failed' 
AND scheduled_date_gmt < DATE_SUB(NOW(), INTERVAL 7 DAY);
" 2>/dev/null

echo "✅ Old actions cleaned up"

# 6. Reset Action Scheduler via WP-CLI
echo ""
echo "6️⃣ Resetting Action Scheduler via WordPress..."

# Clear any pending migration actions
docker exec wordpress-dev wp action-scheduler clean --allow-root 2>/dev/null || echo "   Action Scheduler clean command not available"

# Flush any cached data
docker exec wordpress-dev wp cache flush --allow-root 2>/dev/null || echo "   Cache flush skipped"

echo "✅ Action Scheduler reset"

# 7. Verify the fix
echo ""
echo "7️⃣ Verifying the fix..."

# Check table structure
echo "   Checking table structure:"
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
SHOW CREATE TABLE pink_actionscheduler_actions\G
" 2>/dev/null | grep -E "(action_id|AUTO_INCREMENT)"

# Check for any remaining problematic records
NEW_ZERO_COUNT=$(docker exec mysql-dev mysql -u root -proot_password pinkangel -e "SELECT COUNT(*) FROM pink_actionscheduler_actions WHERE action_id = 0;" 2>/dev/null | tail -1)

if [ "$NEW_ZERO_COUNT" -eq 0 ]; then
    echo "✅ No more records with action_id = 0"
else
    echo "⚠️  Still $NEW_ZERO_COUNT records with action_id = 0"
fi

# 8. Test the problematic URL
echo ""
echo "8️⃣ Testing the URL that caused the error..."

echo "   Testing URL response..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -m 30 "http://localhost:8080/product-category/%d0%b4%d0%b0%d0%bc%d1%81%d0%ba%d0%b0-%d0%ba%d0%be%d0%bb%d0%b5%d0%ba%d1%86%d0%b8%d1%8f/%d0%b4%d0%be%d0%bc%d0%b0%d1%88%d0%b5%d0%bd-%d1%85%d0%b0%d0%bb%d0%b0%d1%82/?orderby=date&wpf_count=24&wpf_fbv=1&all_products_filtering=1" 2>/dev/null || echo "000")

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ URL responds with HTTP 200"
else
    echo "⚠️  URL returned HTTP $HTTP_CODE"
fi

# 9. Show final statistics
echo ""
echo "9️⃣ Final Action Scheduler statistics:"
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
SELECT 
    COUNT(*) as total_actions,
    MAX(action_id) as max_id,
    MIN(action_id) as min_id,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
    COUNT(CASE WHEN status = 'complete' THEN 1 END) as complete,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
FROM pink_actionscheduler_actions;
" 2>/dev/null

echo ""
echo "✅ Action Scheduler Fix Completed!"
echo ""
echo "📊 Summary of changes:"
echo "  ✅ Fixed AUTO_INCREMENT attribute"
echo "  ✅ Resolved duplicate entry '0' error"
echo "  ✅ Cleaned up old action entries"
echo "  ✅ Reset Action Scheduler system"
echo ""
echo "💡 The error should no longer appear when accessing the URL"
echo "   Monitor the error logs to confirm the fix is working"
echo ""
echo "🔍 To monitor Action Scheduler:"
echo "  - Check WP Admin > Tools > Scheduled Actions"
echo "  - Monitor error logs: docker logs wordpress-dev"
echo "  - Watch for any new duplicate entry errors"
