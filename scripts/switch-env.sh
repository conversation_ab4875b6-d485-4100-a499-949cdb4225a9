#!/bin/bash
# switch-env.sh - Switch between development and production environments

echo "🔄 WordPress Environment Switcher"
echo "================================="
echo ""

# Check current status
DEV_RUNNING=$(docker ps --format "table {{.Names}}" | grep "mysql-dev" | wc -l)
PROD_RUNNING=$(docker ps --format "table {{.Names}}" | grep "mysql-prod" | wc -l)

echo "📊 Current Status:"
if [ "$DEV_RUNNING" -gt 0 ]; then
    echo "✅ Development: RUNNING (http://localhost:8080)"
else
    echo "⏹️  Development: STOPPED"
fi

if [ "$PROD_RUNNING" -gt 0 ]; then
    echo "✅ Production: RUNNING (https://localhost)"
else
    echo "⏹️  Production: STOPPED"
fi

echo ""

# Show options
echo "🎯 Available Actions:"
echo "1) Start Development (stop production)"
echo "2) Start Production (stop development)"
echo "3) Stop All"
echo "4) Status Only"
echo ""

read -p "Choose an option (1-4): " choice

case $choice in
    1)
        echo "🔧 Switching to Development..."
        if [ "$PROD_RUNNING" -gt 0 ]; then
            echo "Stopping production..."
            docker compose -f docker-compose.prod.yml down
        fi
        echo "Starting development..."
        docker compose -f docker-compose.dev.yml up -d
        echo "✅ Development environment started!"
        echo "🌐 Access: http://localhost:8080"
        echo "🗄️  phpMyAdmin: http://localhost:8081"
        ;;
    2)
        echo "🚀 Switching to Production..."
        if [ "$DEV_RUNNING" -gt 0 ]; then
            echo "Stopping development..."
            docker compose -f docker-compose.dev.yml down
        fi
        echo "Starting production..."
        docker compose -f docker-compose.prod.yml up -d
        echo "✅ Production environment started!"
        echo "🌐 Access: https://localhost"
        echo "🔒 HTTPS enabled with self-signed certificate"
        ;;
    3)
        echo "⏹️  Stopping all environments..."
        docker compose -f docker-compose.dev.yml down 2>/dev/null
        docker compose -f docker-compose.prod.yml down 2>/dev/null
        echo "✅ All environments stopped!"
        ;;
    4)
        echo "📊 Status check complete."
        ;;
    *)
        echo "❌ Invalid option. Exiting."
        exit 1
        ;;
esac

echo ""
echo "🔍 Final Status:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "wordpress|mysql|nginx|redis|phpmyadmin" || echo "No WordPress containers running"
