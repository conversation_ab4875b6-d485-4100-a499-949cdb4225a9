#!/bin/bash
# setup-swarm-prod.sh - Setup PinkAngel production environment in Docker Swarm mode

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo ""
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "${BLUE}🐳 $1${NC}"
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    echo ""
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        log_error ".env file not found"
        log_info "Run 'make setup-env' to create the .env file first"
        exit 1
    fi
    log_success ".env file found"
    
    # Check if Dock<PERSON> is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running"
        exit 1
    fi
    log_success "Docker is running"
    
    # Check required directories
    local required_dirs=(
        "wordpress"
        "nginx-config"
        "php-config"
        "mysql-config"
        "redis-config"
        "ssl"
        "nginx-cache"
        "nginx-proxy-config"
        "ssl-proxy"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_warning "Creating missing directory: $dir"
            mkdir -p "$dir"
        fi
    done
    log_success "All required directories exist"
}

# Start reverse proxy (outside of swarm)
start_reverse_proxy() {
    print_header "Starting Reverse Proxy (Non-Swarm)"
    
    log_info "The reverse-proxy container runs outside of Docker Swarm"
    log_info "Starting nginx-proxy with docker compose..."

    # Check if reverse-proxy is already running
    if docker ps --format "{{.Names}}" | grep -q "^reverse-proxy$"; then
        log_success "reverse-proxy container is already running"
        return 0
    fi

    # Start the reverse proxy
    docker compose -f docker-compose.proxy.yml up -d
    
    # Wait for it to be ready
    sleep 5
    
    if docker ps --format "{{.Names}}" | grep -q "^reverse-proxy$"; then
        log_success "reverse-proxy container started successfully"
    else
        log_error "Failed to start reverse-proxy container"
        return 1
    fi
}

# Setup Docker Swarm production environment
setup_swarm_production() {
    print_header "Setting Up Docker Swarm Production Environment"
    
    # Make the swarm management script executable
    chmod +x scripts/manage-swarm.sh
    
    log_info "Starting PinkAngel production stack in Docker Swarm mode..."

    # Start the stack (this will only build images if they don't exist)
    if ./scripts/manage-swarm.sh start; then
        log_success "Docker Swarm production stack started successfully"
    else
        log_error "Failed to start Docker Swarm production stack"
        return 1
    fi
}

# Connect reverse proxy to swarm network
connect_proxy_to_swarm() {
    print_header "Connecting Reverse Proxy to Swarm Network"

    # Wait for swarm network to be created
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if docker network ls --format "{{.Name}}" | grep -q "pinkangel-prod_wordpress-prod-network"; then
            log_success "Swarm network found"
            break
        fi

        log_info "Waiting for swarm network to be created... (attempt $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done

    if [ $attempt -gt $max_attempts ]; then
        log_warning "Swarm network not found after waiting"
        log_info "This is normal - the reverse-proxy will work through direct port access"
        return 0
    fi

    # Try to connect reverse-proxy to the swarm network
    local swarm_network="pinkangel-prod_wordpress-prod-network"

    # Check if already connected
    if docker network inspect "$swarm_network" --format '{{range .Containers}}{{.Name}} {{end}}' 2>/dev/null | grep -q "reverse-proxy"; then
        log_success "reverse-proxy is already connected to swarm network"
        return 0
    fi

    log_info "Attempting to connect reverse-proxy to swarm network: $swarm_network"

    # Note: This may not work with overlay networks, but we'll try
    if docker network connect "$swarm_network" reverse-proxy 2>/dev/null; then
        log_success "reverse-proxy connected to swarm network successfully"
    else
        log_info "Could not connect reverse-proxy to overlay network (this is expected)"
        log_info "The reverse-proxy will access services through published ports"
        log_info "Services are accessible on:"
        log_info "  - WordPress: http://localhost:8080, https://localhost:8443"
        log_info "  - phpMyAdmin: http://localhost:8081"
    fi
}

# Show final status
show_final_status() {
    print_header "Final Status Check"
    
    echo "🐳 Docker Swarm Status:"
    docker info --format 'Swarm: {{.Swarm.LocalNodeState}}'
    echo ""
    
    echo "📊 Stack Status:"
    ./scripts/manage-swarm.sh status
    echo ""
    
    echo "🔗 Network Connections:"
    echo "reverse-proxy container networks:"
    docker inspect reverse-proxy --format '{{range $net, $conf := .NetworkSettings.Networks}}{{$net}} {{end}}' 2>/dev/null || echo "reverse-proxy not found"
    echo ""
    
    echo "🌐 Access URLs:"
    echo "  • WordPress (direct): http://localhost:8080"
    echo "  • WordPress (HTTPS): https://localhost:8443"
    echo "  • phpMyAdmin: http://localhost:8081"
    echo "  • WordPress (via proxy): https://pinkangel.local (if configured)"
    echo ""
    
    echo "🔧 Management Commands:"
    echo "  • View stack status: make swarm-status"
    echo "  • View service logs: make swarm-logs SERVICE=<service_name>"
    echo "  • Scale service: make swarm-scale SERVICE=<service_name> REPLICAS=<number>"
    echo "  • Stop swarm: make swarm-stop"
    echo ""
}

# Main execution
main() {
    print_header "PinkAngel Docker Swarm Production Setup"
    
    log_info "Setting up PinkAngel production environment in Docker Swarm mode"
    log_warning "Note: The reverse-proxy container will run outside of swarm"
    echo ""
    
    # Run setup steps
    check_prerequisites
    start_reverse_proxy
    setup_swarm_production
    connect_proxy_to_swarm
    show_final_status
    
    print_header "Setup Complete!"
    log_success "PinkAngel production environment is now running in Docker Swarm mode"
    log_info "The reverse-proxy container is running outside of swarm as requested"
    echo ""
}

# Run main function
main "$@"
