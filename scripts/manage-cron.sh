#!/bin/bash
# manage-cron.sh - Manage WordPress cron container operations
# Provides commands to monitor, control, and debug the cron system

set -e

# Source environment detection
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/detect-environment.sh" >/dev/null 2>&1 || {
    echo "❌ Error: Could not source environment detection script"
    exit 1
}

# Configuration
SCRIPT_NAME="manage-cron"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }
print_header() { echo -e "${PURPLE}🔧 $1${NC}"; }

# Function to show usage
show_usage() {
    echo ""
    print_header "WordPress Cron Container Management"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  status          Show cron container status and health"
    echo "  logs [lines]    Show cron container logs (default: 50 lines)"
    echo "  health          Show detailed health information"
    echo "  restart         Restart the cron container"
    echo "  run-now [task]  Execute a specific cron task immediately"
    echo "  schedule        Show current cron schedule"
    echo "  monitor         Real-time monitoring of cron activities"
    echo "  debug           Enable debug mode and show detailed logs"
    echo ""
    echo "Task options for run-now:"
    echo "  wp-cron         Run WordPress core cron jobs"
    echo "  action-scheduler Run Action Scheduler processing"
    echo "  hourly          Run hourly maintenance tasks"
    echo "  daily           Run daily maintenance tasks"
    echo "  weekly          Run weekly maintenance tasks"
    echo ""
    echo "Examples:"
    echo "  $0 status                    # Show current status"
    echo "  $0 logs 100                  # Show last 100 log lines"
    echo "  $0 run-now wp-cron          # Run WordPress cron immediately"
    echo "  $0 monitor                   # Start real-time monitoring"
}

# Function to get cron container name based on environment
get_cron_container() {
    if [[ "$ENVIRONMENT" == "development" ]]; then
        echo "wordpress-cron-dev"
    else
        # Check if Docker Swarm is active
        if docker info | grep -q "Swarm: active" 2>/dev/null; then
            # In swarm mode, get the actual container ID/name
            docker ps -q --filter "name=pinkangel-prod_wordpress-cron-prod" | head -1
        else
            echo "wordpress-cron-prod"
        fi
    fi
}

# Function to get cron service name for swarm operations
get_cron_service() {
    if [[ "$ENVIRONMENT" == "development" ]]; then
        echo "wordpress-cron-dev"
    else
        echo "pinkangel-prod_wordpress-cron-prod"
    fi
}

# Function to check if cron container is running
is_cron_running() {
    if [[ "$ENVIRONMENT" == "development" ]]; then
        local container=$(get_cron_container)
        docker ps --format "{{.Names}}" | grep -q "^$container$"
    else
        # Check if Docker Swarm is active and service is running
        if docker info | grep -q "Swarm: active" 2>/dev/null; then
            local service=$(get_cron_service)
            docker service ls --format "{{.Name}}" | grep -q "^$service$" && \
            [ "$(docker service ls --filter "name=$service" --format "{{.Replicas}}" | cut -d'/' -f1)" != "0" ]
        else
            local container=$(get_cron_container)
            docker ps --format "{{.Names}}" | grep -q "^$container$"
        fi
    fi
}

# Function to show cron container status
show_status() {
    local container=$(get_cron_container)
    local service=$(get_cron_service)

    print_header "WordPress Cron Container Status ($ENVIRONMENT)"
    echo ""

    if is_cron_running; then
        print_success "Cron container is running"

        # Container/Service details
        echo ""
        print_info "Container Details:"
        if [[ "$ENVIRONMENT" == "development" ]]; then
            docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Image}}" | grep "$container" || echo "  Container info not available"
        else
            # Docker Swarm mode
            if docker info | grep -q "Swarm: active" 2>/dev/null; then
                echo "  Service: $service"
                docker service ls --filter "name=$service" --format "table {{.Name}}\t{{.Replicas}}\t{{.Image}}" 2>/dev/null || echo "  Service info not available"
                echo ""
                print_info "Service Tasks:"
                docker service ps "$service" --format "table {{.Name}}\t{{.Node}}\t{{.DesiredState}}\t{{.CurrentState}}" 2>/dev/null || echo "  Task info not available"

                # Get actual container ID for health checks
                container=$(docker ps -q --filter "name=pinkangel-prod_wordpress-cron-prod" | head -1)
            else
                docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Image}}" | grep "$container" || echo "  Container info not available"
            fi
        fi

        # Health status (if we have a container ID)
        if [[ -n "$container" ]]; then
            echo ""
            print_info "Health Status:"
            if docker exec "$container" test -f /tmp/cron-health.status 2>/dev/null; then
                docker exec "$container" cat /tmp/cron-health.status 2>/dev/null | python3 -m json.tool 2>/dev/null || {
                    docker exec "$container" cat /tmp/cron-health.status 2>/dev/null
                }
            else
                print_warning "Health status file not found"
            fi

            # Cron jobs status
            echo ""
            print_info "Active Cron Jobs:"
            docker exec "$container" crontab -l 2>/dev/null | grep -v '^#' | grep -v '^$' | while read line; do
                echo "  $line"
            done || print_warning "Could not retrieve cron jobs"
        else
            print_warning "Could not access container for detailed health information"
        fi

    else
        print_error "Cron container is not running"
        echo ""
        print_info "To start the cron container:"
        if [[ "$ENVIRONMENT" == "development" ]]; then
            echo "  make run-dev"
        else
            if docker info | grep -q "Swarm: active" 2>/dev/null; then
                echo "  make swarm-start"
            else
                echo "  make run-prod"
            fi
        fi
    fi
}

# Function to show logs
show_logs() {
    local lines=${1:-50}
    local container=$(get_cron_container)
    
    if ! is_cron_running; then
        print_error "Cron container is not running"
        return 1
    fi
    
    print_header "WordPress Cron Container Logs (last $lines lines)"
    echo ""
    
    docker logs "$container" --tail "$lines" --timestamps
}

# Function to show health information
show_health() {
    local container=$(get_cron_container)
    
    if ! is_cron_running; then
        print_error "Cron container is not running"
        return 1
    fi
    
    print_header "WordPress Cron Health Information"
    echo ""
    
    # Health status
    print_info "Current Health Status:"
    if docker exec "$container" test -f /tmp/cron-health.status 2>/dev/null; then
        docker exec "$container" cat /tmp/cron-health.status 2>/dev/null | python3 -m json.tool 2>/dev/null || {
            docker exec "$container" cat /tmp/cron-health.status 2>/dev/null
        }
    else
        print_warning "Health status file not found"
    fi
    
    echo ""
    
    # Log file sizes
    print_info "Log File Sizes:"
    docker exec "$container" find /var/log/wordpress-cron -name "*.log" -exec ls -lh {} \; 2>/dev/null | while read line; do
        echo "  $line"
    done || print_warning "Could not access log files"
    
    echo ""
    
    # Lock files
    print_info "Active Lock Files:"
    docker exec "$container" ls -la /tmp/*.lock 2>/dev/null | while read line; do
        echo "  $line"
    done || echo "  No active lock files"
    
    echo ""
    
    # WordPress connectivity
    print_info "WordPress Connectivity:"
    if docker exec "$container" wp core version --allow-root >/dev/null 2>&1; then
        print_success "WordPress accessible"
        docker exec "$container" wp core version --allow-root 2>/dev/null | while read version; do
            echo "  WordPress Version: $version"
        done
    else
        print_error "WordPress not accessible"
    fi
    
    # Database connectivity
    if docker exec "$container" php -r '
        $host = getenv("WORDPRESS_DB_HOST") ?: "mysql-dev";
        $user = getenv("WORDPRESS_DB_USER") ?: "root";
        $pass = getenv("WORDPRESS_DB_PASSWORD") ?: "root_password";
        $db = getenv("WORDPRESS_DB_NAME") ?: "pinkangel";
        $conn = new mysqli($host, $user, $pass, $db);
        if ($conn->connect_error) exit(1);
        $result = $conn->query("SELECT 1");
        exit($result ? 0 : 1);
    ' >/dev/null 2>&1; then
        print_success "Database accessible"
    else
        print_error "Database not accessible"
    fi
}

# Function to restart cron container
restart_cron() {
    local container=$(get_cron_container)
    
    print_header "Restarting WordPress Cron Container"
    echo ""
    
    if is_cron_running; then
        print_info "Stopping cron container..."
        docker stop "$container" >/dev/null 2>&1 || print_warning "Failed to stop container gracefully"
    fi
    
    print_info "Starting cron container..."
    if [[ "$ENVIRONMENT" == "development" ]]; then
        docker compose -f docker-compose.dev.yml up -d wordpress-cron-dev
    else
        docker compose -f docker-compose.prod.yml up -d wordpress-cron-prod
    fi
    
    # Wait for container to be ready
    print_info "Waiting for container to be ready..."
    sleep 5
    
    if is_cron_running; then
        print_success "Cron container restarted successfully"
    else
        print_error "Failed to restart cron container"
        return 1
    fi
}

# Function to run specific task immediately
run_task_now() {
    local task="$1"
    local container=$(get_cron_container)
    
    if ! is_cron_running; then
        print_error "Cron container is not running"
        return 1
    fi
    
    case "$task" in
        "wp-cron")
            print_header "Running WordPress Core Cron Jobs"
            docker exec "$container" /etc/cron-scripts/wp-cron.sh
            ;;
        "action-scheduler")
            print_header "Running Action Scheduler Processing"
            docker exec "$container" /etc/cron-scripts/action-scheduler.sh
            ;;
        "hourly")
            print_header "Running Hourly Maintenance Tasks"
            docker exec "$container" /etc/cron-scripts/hourly-tasks.sh
            ;;
        "daily")
            print_header "Running Daily Maintenance Tasks"
            docker exec "$container" /etc/cron-scripts/daily-tasks.sh
            ;;
        "weekly")
            print_header "Running Weekly Maintenance Tasks"
            docker exec "$container" /etc/cron-scripts/weekly-tasks.sh
            ;;
        *)
            print_error "Unknown task: $task"
            echo ""
            echo "Available tasks: wp-cron, action-scheduler, hourly, daily, weekly"
            return 1
            ;;
    esac
}

# Function to show current schedule
show_schedule() {
    local container=$(get_cron_container)
    
    if ! is_cron_running; then
        print_error "Cron container is not running"
        return 1
    fi
    
    print_header "Current Cron Schedule"
    echo ""
    
    print_info "System Crontab:"
    docker exec "$container" crontab -l 2>/dev/null || print_warning "Could not retrieve crontab"
    
    echo ""
    
    print_info "WordPress Scheduled Events:"
    docker exec "$container" wp cron event list --format=table --allow-root 2>/dev/null || print_warning "Could not retrieve WordPress events"
}

# Function to start real-time monitoring
start_monitoring() {
    local container=$(get_cron_container)
    
    if ! is_cron_running; then
        print_error "Cron container is not running"
        return 1
    fi
    
    print_header "Real-time Cron Monitoring (Press Ctrl+C to stop)"
    echo ""
    
    # Follow logs in real-time
    docker logs "$container" --follow --timestamps
}

# Function to enable debug mode
enable_debug() {
    local container=$(get_cron_container)
    
    if ! is_cron_running; then
        print_error "Cron container is not running"
        return 1
    fi
    
    print_header "Debug Mode - Detailed Logs"
    echo ""
    
    print_info "Recent error logs:"
    docker logs "$container" --tail 100 | grep -i error || echo "No recent errors found"
    
    echo ""
    
    print_info "Recent warning logs:"
    docker logs "$container" --tail 100 | grep -i warning || echo "No recent warnings found"
    
    echo ""
    
    print_info "Cron execution logs:"
    docker exec "$container" find /var/log/wordpress-cron -name "*.log" -exec tail -20 {} \; 2>/dev/null || print_warning "Could not access cron logs"
}

# Main script logic
case "${1:-}" in
    "status")
        show_status
        ;;
    "logs")
        show_logs "${2:-50}"
        ;;
    "health")
        show_health
        ;;
    "restart")
        restart_cron
        ;;
    "run-now")
        if [ -z "${2:-}" ]; then
            print_error "Task name required for run-now command"
            show_usage
            exit 1
        fi
        run_task_now "$2"
        ;;
    "schedule")
        show_schedule
        ;;
    "monitor")
        start_monitoring
        ;;
    "debug")
        enable_debug
        ;;
    "help"|"-h"|"--help"|"")
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
