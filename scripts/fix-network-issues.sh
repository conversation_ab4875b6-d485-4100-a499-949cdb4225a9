#!/bin/bash

# Script to diagnose and fix Docker network issues
# Specifically handles nginx-proxy network connection problems

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check Docker daemon status
check_docker_status() {
    log_info "Checking Docker daemon status..."
    
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker daemon is not running or accessible"
        exit 1
    fi
    
    log_success "Docker daemon is running"
}

# Diagnose network issues
diagnose_networks() {
    log_info "Diagnosing Docker networks..."
    
    # Check if proxy-network exists
    if docker network ls --format "table {{.Name}}" | grep -q "proxy-network"; then
        log_success "proxy-network exists"
    else
        log_warning "proxy-network does not exist"
    fi
    
    # Check if production network exists
    if docker network ls --format "table {{.Name}}" | grep -q "pinkangel-docker_wordpress-prod-network"; then
        log_success "pinkangel-docker_wordpress-prod-network exists"
    else
        log_warning "pinkangel-docker_wordpress-prod-network does not exist"
    fi
    
    # Check reverse-proxy container status
    if docker ps --format "table {{.Names}}" | grep -q "reverse-proxy"; then
        log_success "reverse-proxy container is running"
        
        # Check which networks it's connected to
        log_info "reverse-proxy is connected to these networks:"
        docker inspect reverse-proxy --format '{{range $net, $conf := .NetworkSettings.Networks}}  - {{$net}}{{end}}'
    else
        log_warning "reverse-proxy container is not running"
    fi
}

# Clean up orphaned containers and networks
cleanup_orphans() {
    log_info "Cleaning up orphaned containers and networks..."
    
    # Stop all containers first
    log_info "Stopping all containers..."
    docker compose -f docker-compose.dev.yml down --remove-orphans 2>/dev/null || true
    docker compose -f docker-compose.prod.yml down --remove-orphans 2>/dev/null || true
    docker compose -f docker-compose.proxy.yml down --remove-orphans 2>/dev/null || true
    
    # Remove any dangling networks
    log_info "Removing dangling networks..."
    docker network prune -f
    
    log_success "Cleanup completed"
}

# Fix network sandbox issues
fix_network_sandbox() {
    log_info "Fixing network sandbox issues..."
    
    # Restart Docker daemon (requires sudo)
    if command -v systemctl >/dev/null 2>&1; then
        log_info "Restarting Docker daemon (systemctl)..."
        sudo systemctl restart docker
    elif command -v service >/dev/null 2>&1; then
        log_info "Restarting Docker daemon (service)..."
        sudo service docker restart
    else
        log_warning "Cannot restart Docker daemon automatically"
        log_info "Please restart Docker Desktop manually"
        read -p "Press Enter after restarting Docker Desktop..."
    fi
    
    # Wait for Docker to be ready
    log_info "Waiting for Docker to be ready..."
    sleep 5
    
    while ! docker info >/dev/null 2>&1; do
        log_info "Waiting for Docker daemon..."
        sleep 2
    done
    
    log_success "Docker daemon is ready"
}

# Reconnect nginx-proxy to production network
reconnect_proxy() {
    log_info "Attempting to reconnect nginx-proxy to production network..."
    
    # Start proxy if not running
    if ! docker ps --format "table {{.Names}}" | grep -q "reverse-proxy"; then
        log_info "Starting nginx-proxy container..."
        docker compose -f docker-compose.proxy.yml up -d
        sleep 3
    fi

    # Start production containers if not running
    if ! docker ps --format "table {{.Names}}" | grep -q "wordpress-prod"; then
        log_info "Starting production containers..."
        docker compose -f docker-compose.prod.yml up -d --remove-orphans
        sleep 5
    fi
    
    # Try to connect
    if docker network ls --format "table {{.Name}}" | grep -q "pinkangel-docker_wordpress-prod-network"; then
        if ! docker network inspect pinkangel-docker_wordpress-prod-network --format '{{range .Containers}}{{.Name}} {{end}}' | grep -q "reverse-proxy" 2>/dev/null; then
            log_info "Connecting nginx-proxy to production network..."
            
            if docker network connect pinkangel-docker_wordpress-prod-network reverse-proxy 2>/dev/null; then
                log_success "nginx-proxy successfully connected to production network"
            else
                log_error "Failed to connect nginx-proxy to production network"
                return 1
            fi
        else
            log_success "nginx-proxy is already connected to production network"
        fi
    else
        log_error "Production network does not exist"
        return 1
    fi
}

# Main execution
main() {
    log_info "🔧 Starting Docker network issue diagnosis and repair..."
    echo ""
    
    check_docker_status
    echo ""
    
    diagnose_networks
    echo ""
    
    # Ask user what they want to do
    echo "What would you like to do?"
    echo "1) Clean up orphaned containers and networks"
    echo "2) Fix network sandbox issues (restart Docker)"
    echo "3) Reconnect nginx-proxy to production network"
    echo "4) Full cleanup and restart"
    echo "5) Exit"
    
    read -p "Enter your choice (1-5): " choice
    echo ""
    
    case $choice in
        1)
            cleanup_orphans
            ;;
        2)
            fix_network_sandbox
            ;;
        3)
            reconnect_proxy
            ;;
        4)
            cleanup_orphans
            echo ""
            fix_network_sandbox
            echo ""
            reconnect_proxy
            ;;
        5)
            log_info "Exiting..."
            exit 0
            ;;
        *)
            log_error "Invalid choice"
            exit 1
            ;;
    esac
    
    echo ""
    log_success "✅ Network issue repair completed!"
    log_info "You can now try running 'make run-prod' again"
}

# Run main function
main "$@"
