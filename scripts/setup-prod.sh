#!/bin/bash
# setup-prod.sh - Production environment setup
# Usage: ./setup-prod.sh [swarm]
# If 'swarm' argument is provided, will use Docker Swarm mode

SWARM_MODE=false
if [ "$1" = "swarm" ]; then
    SWARM_MODE=true
    echo "Setting up WordPress production environment in Docker Swarm mode..."
else
    echo "Setting up WordPress production environment in Docker Compose mode..."
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Error: .env file not found. Please create it from the template."
    exit 1
fi

# Load environment variables
source .env

# Verify nginx-proxy container is running and proxy-network exists
echo "🔍 Verifying nginx-proxy container and network..."
if ! docker ps --format "table {{.Names}}" | grep -q "reverse-proxy"; then
    echo "❌ Error: nginx-proxy container (reverse-proxy) is not running!"
    echo "💡 The production environment requires the nginx-proxy container to be running first."
    echo "🔧 This should have been started automatically, but something went wrong."
    exit 1
fi

# Check if proxy-network exists
if ! docker network ls --format "table {{.Name}}" | grep -q "proxy-network"; then
    echo "❌ Error: proxy-network does not exist!"
    echo "💡 The nginx-proxy container should create this network automatically."
    exit 1
fi

echo "✅ nginx-proxy container and network verified successfully"

# Create necessary directories
mkdir -p wordpress php-config mysql-config redis-config nginx-config ssl backups

# Create nginx cache directories with proper permissions
mkdir -p nginx-cache/proxy nginx-cache/fastcgi
chmod -R 755 nginx-cache

# Set proper permissions
chmod 755 wordpress
chmod 600 .env

# Generate SSL certificate (self-signed for testing)
if [ ! -f ssl/cert.pem ]; then
    echo "Generating SSL certificate..."
    mkdir -p ssl
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout ssl/key.pem \
        -out ssl/cert.pem \
        -subj "/C=US/ST=State/L=City/O=Organization/CN=${WORDPRESS_DOMAIN}"
fi

# Start production environment
echo "Starting production containers..."
docker compose -f docker-compose.prod.yml up -d

# Connect nginx-proxy to the production network after it's created
echo "🔗 Connecting nginx-proxy to production network..."
if docker network ls --format "table {{.Name}}" | grep -q "pinkangel-docker_wordpress-prod-network"; then
    # Check if nginx-proxy is already connected to avoid errors
    network_containers=$(docker network inspect pinkangel-docker_wordpress-prod-network --format '{{range .Containers}}{{.Name}} {{end}}' 2>/dev/null || echo "")
    if ! echo "$network_containers" | grep -q "reverse-proxy"; then
        # Try to connect with error handling and retry logic
        echo "Attempting to connect nginx-proxy to production network..."

        # First attempt
        if docker network connect pinkangel-docker_wordpress-prod-network reverse-proxy 2>/dev/null; then
            echo "✅ nginx-proxy connected to production network"
        else
            echo "⚠️  First connection attempt failed, checking container status..."

            # Check if reverse-proxy container is running
            if ! docker ps --format "table {{.Names}}" | grep -q "reverse-proxy"; then
                echo "❌ reverse-proxy container is not running, restarting..."
                docker compose -f docker-compose.proxy.yml up -d
                sleep 3
            fi

            # Wait a moment for container to stabilize
            echo "⏳ Waiting for container to stabilize..."
            sleep 2

            # Second attempt
            if docker network connect pinkangel-docker_wordpress-prod-network reverse-proxy 2>/dev/null; then
                echo "✅ nginx-proxy connected to production network (retry successful)"
            else
                echo "⚠️  Network connection failed, but continuing setup..."
                echo "💡 You may need to manually restart the proxy: make restart-proxy"
            fi
        fi

        # Restart nginx-proxy to refresh DNS resolution for production containers
        echo "🔄 Restarting nginx-proxy to refresh DNS resolution..."
        docker restart reverse-proxy
        sleep 3
        echo "✅ nginx-proxy restarted successfully"
    else
        echo "✅ nginx-proxy already connected to production network"
    fi
else
    echo "⚠️  Production network not found, skipping nginx-proxy connection"
fi

# Wait for MySQL to be ready
echo "Waiting for MySQL to be ready..."
until docker exec mysql-prod mysqladmin ping -h"localhost" --silent; do
    echo "MySQL is unavailable - sleeping for 2 seconds"
    sleep 2
done
echo "MySQL is ready!"

# Wait a bit more for WordPress to initialize
echo "Waiting for WordPress to initialize..."
sleep 10

# Install WordPress if not already configured
docker exec wordpress-prod wp core install \
    --url="${WORDPRESS_URL}" \
    --title="WordPress Production Site" \
    --admin_user="admin" \
    --admin_password="$(openssl rand -base64 12)" \
    --admin_email="admin@${WORDPRESS_DOMAIN}" \
    --allow-root

# Install and configure caching plugins
docker exec wordpress-prod wp plugin install redis-cache --activate --allow-root

# Enable Redis cache
docker exec wordpress-prod wp redis enable --allow-root

# Set up nginx cache directories inside container
echo "Setting up nginx cache directories..."
docker exec nginx-prod mkdir -p /var/cache/nginx-cache/proxy /var/cache/nginx-cache/fastcgi
docker exec nginx-prod chown -R nginx:nginx /var/cache/nginx-cache
docker exec nginx-prod chmod -R 755 /var/cache/nginx-cache

# Test nginx configuration and reload if valid
echo "Testing nginx configuration..."
if docker exec nginx-prod nginx -t; then
    echo "Reloading nginx with cache configuration..."
    docker exec nginx-prod nginx -s reload
    echo "✅ Nginx cache enabled successfully!"
else
    echo "⚠️  Nginx configuration test failed, but continuing..."
fi

# Configure WordPress for production
docker exec wordpress-prod wp config set WP_CACHE true --type=constant --allow-root
docker exec wordpress-prod wp config set DISABLE_WP_CRON true --type=constant --allow-root

echo "Production environment is ready!"
echo "Please update your DNS to point to this server."


