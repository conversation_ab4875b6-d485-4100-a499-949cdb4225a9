#!/bin/bash

# Complete Environment Setup Script
# Sets up the entire multi-website environment with reverse proxy

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root (for /etc/hosts modification)
check_sudo() {
    if [ "$EUID" -eq 0 ]; then
        return 0
    else
        return 1
    fi
}

# Update /etc/hosts
update_hosts() {
    local hosts_entries=(
        "127.0.0.1 pinkangel.local"
    )
    
    log_info "Updating /etc/hosts file..."
    
    for entry in "${hosts_entries[@]}"; do
        if ! grep -q "$entry" /etc/hosts; then
            if check_sudo; then
                echo "$entry" >> /etc/hosts
                log_success "Added: $entry"
            else
                log_warning "Please add this entry to /etc/hosts manually:"
                echo "  $entry"
            fi
        else
            log_info "Already exists: $entry"
        fi
    done
}

# Setup proxy
setup_proxy() {
    log_info "Setting up reverse proxy..."
    "$SCRIPT_DIR/manage-proxy.sh" setup
}

# Start environment
start_environment() {
    log_info "Starting complete environment..."
    
    cd "$PROJECT_ROOT"
    
    # Create proxy network first
    if ! docker network ls | grep -q "proxy-network"; then
        log_info "Creating proxy network..."
        docker network create proxy-network
    fi
    
    # Start development environment
    log_info "Starting development environment..."
    docker compose -f docker-compose.dev.yml up -d

    # Start production environment
    log_info "Starting production environment..."
    docker compose -f docker-compose.prod.yml up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to start..."
    sleep 10
    
    # Start reverse proxy
    log_info "Starting reverse proxy..."
    "$SCRIPT_DIR/manage-proxy.sh" start
    
    log_success "Environment started successfully!"
}

# Stop environment
stop_environment() {
    log_info "Stopping complete environment..."
    
    cd "$PROJECT_ROOT"
    
    # Stop reverse proxy
    "$SCRIPT_DIR/manage-proxy.sh" stop
    
    # Stop production environment
    docker compose -f docker-compose.prod.yml down

    # Stop development environment
    docker compose -f docker-compose.dev.yml down
    
    log_success "Environment stopped successfully!"
}

# Show status
show_status() {
    log_info "Environment Status:"
    echo ""
    
    # Check proxy
    echo "=== Reverse Proxy ==="
    "$SCRIPT_DIR/manage-proxy.sh" status
    echo ""
    
    # Check dev environment
    echo "=== Development Environment ==="
    if docker ps | grep -q "wordpress-dev"; then
        log_success "Development environment is running"
        docker ps --filter "name=wordpress-dev" --filter "name=mysql-dev" --filter "name=redis-dev" --format "table {{.Names}}\t{{.Status}}"
    else
        log_warning "Development environment is not running"
    fi
    echo ""
    
    # Check prod environment
    echo "=== Production Environment ==="
    if docker ps | grep -q "wordpress-prod"; then
        log_success "Production environment is running"
        docker ps --filter "name=wordpress-prod" --filter "name=nginx-prod" --filter "name=mysql-prod" --filter "name=redis-prod" --format "table {{.Names}}\t{{.Status}}"
    else
        log_warning "Production environment is not running"
    fi
    echo ""
    
    # Show access URLs
    echo "=== Access URLs ==="
    echo "Development: http://localhost:8080"
    echo "Production:  https://pinkangel.local"
    echo "PhpMyAdmin:  http://localhost:8081 (dev only)"
}

# Test connectivity
test_connectivity() {
    log_info "Testing connectivity..."
    
    # Test if proxy can reach backends
    if docker ps | grep -q "reverse-proxy"; then
        log_info "Testing proxy connectivity to backends..."
        
        # Test prod backend
        if docker exec reverse-proxy ping -c 1 nginx-prod >/dev/null 2>&1; then
            log_success "Can reach production backend"
        else
            log_error "Cannot reach production backend"
        fi
        
        # Test nginx configuration
        if docker exec reverse-proxy nginx -t >/dev/null 2>&1; then
            log_success "Nginx configuration is valid"
        else
            log_error "Nginx configuration has errors"
        fi
    else
        log_error "Reverse proxy is not running"
    fi
}

# Show help
show_help() {
    echo "Complete Environment Setup Script"
    echo ""
    echo "Usage: $0 <command>"
    echo ""
    echo "Commands:"
    echo "  setup       Setup the complete environment (run once)"
    echo "  start       Start all services (dev + prod + proxy)"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  status      Show status of all services"
    echo "  test        Test connectivity and configuration"
    echo "  hosts       Update /etc/hosts file"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  sudo $0 setup    # Initial setup (needs sudo for /etc/hosts)"
    echo "  $0 start         # Start everything"
    echo "  $0 status        # Check status"
    echo "  $0 test          # Test connectivity"
}

# Main script logic
case "${1:-}" in
    setup)
        setup_proxy
        update_hosts
        log_success "Setup completed!"
        log_info "Next step: Run '$0 start' to start all services"
        ;;
    start)
        start_environment
        ;;
    stop)
        stop_environment
        ;;
    restart)
        stop_environment
        sleep 3
        start_environment
        ;;
    status)
        show_status
        ;;
    test)
        test_connectivity
        ;;
    hosts)
        update_hosts
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: ${1:-}"
        echo ""
        show_help
        exit 1
        ;;
esac
