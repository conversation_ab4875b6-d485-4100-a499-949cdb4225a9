#!/bin/bash

# Nginx Reverse Proxy Management Script
# Helps manage the reverse proxy setup for multiple dockerized websites

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
PROXY_COMPOSE="$PROJECT_ROOT/docker-compose.proxy.yml"
NGINX_CONFIG_DIR="$PROJECT_ROOT/nginx-proxy-config"
SSL_DIR="$PROJECT_ROOT/ssl-proxy"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GRE<PERSON>}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if proxy is running
check_proxy_status() {
    if docker ps | grep -q "reverse-proxy"; then
        return 0
    else
        return 1
    fi
}

# Setup function
setup_proxy() {
    log_info "Setting up nginx reverse proxy..."
    
    # Create directories
    mkdir -p "$NGINX_CONFIG_DIR/sites" "$SSL_DIR" "$PROJECT_ROOT/nginx-proxy-cache"
    
    # Generate SSL certificates
    log_info "Generating SSL certificates..."
    "$SCRIPT_DIR/generate-proxy-ssl.sh"
    
    # Check if networks exist
    log_info "Checking Docker networks..."
    
    # Create external networks if they don't exist
    if ! docker network ls | grep -q "pinkangel-docker_wordpress-dev-network"; then
        log_warning "Dev network not found. Make sure to start dev environment first."
    fi
    
    if ! docker network ls | grep -q "pinkangel-docker_wordpress-prod-network"; then
        log_warning "Prod network not found. Make sure to start prod environment first."
    fi
    
    log_success "Proxy setup completed!"
    log_info "Next steps:"
    echo "1. Add domains to /etc/hosts:"
    echo "   127.0.0.1 pinkangel.local"
    echo "   127.0.0.1 pinkangel-dev.local"
    echo "2. Start your website containers first"
    echo "3. Run: $0 start"
}

# Start proxy
start_proxy() {
    log_info "Starting nginx reverse proxy..."
    
    if check_proxy_status; then
        log_warning "Proxy is already running"
        return 0
    fi
    
    cd "$PROJECT_ROOT"
    docker compose -f "$PROXY_COMPOSE" up -d
    
    # Wait a moment for startup
    sleep 3
    
    if check_proxy_status; then
        log_success "Proxy started successfully"
        log_info "Access your sites at:"
        echo "  - https://pinkangel-dev.local (development)"
        echo "  - https://pinkangel.local (production)"
    else
        log_error "Failed to start proxy"
        return 1
    fi
}

# Stop proxy
stop_proxy() {
    log_info "Stopping nginx reverse proxy..."
    
    cd "$PROJECT_ROOT"
    docker compose -f "$PROXY_COMPOSE" down
    
    log_success "Proxy stopped"
}

# Restart proxy
restart_proxy() {
    log_info "Restarting nginx reverse proxy..."
    stop_proxy
    sleep 2
    start_proxy
}

# Show proxy status
status_proxy() {
    if check_proxy_status; then
        log_success "Proxy is running"
        echo ""
        docker ps --filter "name=reverse-proxy" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    else
        log_warning "Proxy is not running"
    fi
}

# Show logs
logs_proxy() {
    if check_proxy_status; then
        docker logs -f reverse-proxy
    else
        log_error "Proxy is not running"
        return 1
    fi
}

# Test configuration
test_config() {
    if check_proxy_status; then
        log_info "Testing nginx configuration..."
        if docker exec reverse-proxy nginx -t; then
            log_success "Configuration is valid"
        else
            log_error "Configuration has errors"
            return 1
        fi
    else
        log_error "Proxy is not running"
        return 1
    fi
}

# Reload configuration
reload_config() {
    if check_proxy_status; then
        log_info "Reloading nginx configuration..."
        if docker exec reverse-proxy nginx -s reload; then
            log_success "Configuration reloaded"
        else
            log_error "Failed to reload configuration"
            return 1
        fi
    else
        log_error "Proxy is not running"
        return 1
    fi
}

# Add new site
add_site() {
    local site_name="$1"
    
    if [ -z "$site_name" ]; then
        log_error "Site name is required"
        echo "Usage: $0 add-site <site-name>"
        return 1
    fi
    
    local site_config="$NGINX_CONFIG_DIR/sites/${site_name}.conf"
    local template="$NGINX_CONFIG_DIR/sites/example-site.conf.template"
    
    if [ -f "$site_config" ]; then
        log_error "Site configuration already exists: $site_config"
        return 1
    fi
    
    if [ ! -f "$template" ]; then
        log_error "Template not found: $template"
        return 1
    fi
    
    log_info "Creating configuration for site: $site_name"
    
    # Copy template and replace placeholders
    sed "s/yoursite/$site_name/g" "$template" > "$site_config"
    
    log_success "Site configuration created: $site_config"
    log_info "Next steps:"
    echo "1. Edit $site_config to customize settings"
    echo "2. Generate SSL certificates for the site"
    echo "3. Add domain to /etc/hosts"
    echo "4. Reload proxy configuration: $0 reload"
}

# Show help
show_help() {
    echo "Nginx Reverse Proxy Management Script"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  setup           Setup the reverse proxy (run once)"
    echo "  start           Start the reverse proxy"
    echo "  stop            Stop the reverse proxy"
    echo "  restart         Restart the reverse proxy"
    echo "  status          Show proxy status"
    echo "  logs            Show proxy logs (follow mode)"
    echo "  test            Test nginx configuration"
    echo "  reload          Reload nginx configuration"
    echo "  add-site <name> Create configuration for a new site"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup"
    echo "  $0 start"
    echo "  $0 add-site mywebsite"
    echo "  $0 reload"
}

# Main script logic
case "${1:-}" in
    setup)
        setup_proxy
        ;;
    start)
        start_proxy
        ;;
    stop)
        stop_proxy
        ;;
    restart)
        restart_proxy
        ;;
    status)
        status_proxy
        ;;
    logs)
        logs_proxy
        ;;
    test)
        test_config
        ;;
    reload)
        reload_config
        ;;
    add-site)
        add_site "$2"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: ${1:-}"
        echo ""
        show_help
        exit 1
        ;;
esac
