#!/bin/bash
# db-migrate-simple.sh - Simple database migration with MySQL 8.0 compatibility (Linux/Debian compatible)

set -e

# Source common environment detection
DETECT_ENV_VERBOSE=true source scripts/detect-environment.sh "$@"

# Configuration
SOURCE_DB_FILE="wordpress_backup_26072025.sql"
TEMPORAL_DB="temporal"
TARGET_DB="pinkangel"
CONVERTED_DB_FILE="temporal.sql"

echo "🔄 Database Migration & Charset Conversion (Simple)"
echo "=================================================="
echo ""

# Check if source database file exists
if [ ! -f "$SOURCE_DB_FILE" ]; then
    echo "❌ Error: Source database file '$SOURCE_DB_FILE' not found!"
    exit 1
fi

echo "📋 Migration Details:"
echo "   Environment: $ENVIRONMENT"
echo "   MySQL container: $MYSQL_CONTAINER"
echo "   Source file: $SOURCE_DB_FILE ($(ls -lh "$SOURCE_DB_FILE" | awk '{print $5}'))"
echo "   Target database: $TARGET_DB"
echo ""

read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Migration cancelled."
    exit 1
fi

echo ""
echo "🔄 Starting database migration..."

# Step 1: Create temporal database with utf8mb3 charset
echo "1️⃣ Creating temporal database with utf8mb3 charset..."
docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "DROP DATABASE IF EXISTS $TEMPORAL_DB;" 2>/dev/null
docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "CREATE DATABASE $TEMPORAL_DB CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci;" 2>/dev/null

# Step 2: Fix key length issues and import
echo "2️⃣ Fixing key length issues and importing database..."

# Create a backup of the original file
BACKUP_FILE="${SOURCE_DB_FILE}.backup"
if [ ! -f "$BACKUP_FILE" ]; then
    echo "   Creating backup of original file..."
    cp "$SOURCE_DB_FILE" "$BACKUP_FILE"
fi

echo "   Applying key length fixes for MySQL 8.0..."

# Apply the specific fixes you identified
echo "   Fixing key_value_hash index..."
if sed -i 's/(`key_value_hash`)/(`key_value_hash`(40))/g' "$SOURCE_DB_FILE" 2>/dev/null; then
    echo "   ✅ key_value_hash fix applied"
else
    echo "   ⚠️  key_value_hash fix had issues (may be normal)"
fi

echo "   Fixing regex index..."
if sed -i 's/(`regex`) USING HASH/(`regex`(191)) USING HASH/g' "$SOURCE_DB_FILE" 2>/dev/null; then
    echo "   ✅ regex fix applied"
else
    echo "   ⚠️  regex fix had issues (may be normal)"
fi

echo "   Starting database import..."

# Import with MySQL compatibility settings and better error handling
echo "   Import progress will be logged to /tmp/import_log.txt"
if docker exec -i $MYSQL_CONTAINER mysql \
    --force \
    --init-command="SET SESSION sql_mode=''; SET SESSION foreign_key_checks=0; SET SESSION unique_checks=0; SET SESSION innodb_strict_mode=0;" \
    -u root -p$MYSQL_ROOT_PASSWORD $TEMPORAL_DB < "$SOURCE_DB_FILE" 2>&1 | \
    grep -v "Warning.*password.*insecure" | \
    grep -v "Unknown system variable" | \
    tee /tmp/import_log.txt; then

    echo "✅ Import command completed"

    # Show summary of any errors/warnings
    ERROR_COUNT=$(grep -i "error" /tmp/import_log.txt | wc -l)
    WARNING_COUNT=$(grep -i "warning" /tmp/import_log.txt | wc -l)
    echo "   Import summary: $ERROR_COUNT errors, $WARNING_COUNT warnings"
else
    echo "❌ Import command failed!"
    echo "   Check /tmp/import_log.txt for details"
    exit 1
fi

# Check if we have tables despite some errors
TABLE_COUNT=$(docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$TEMPORAL_DB';" 2>/dev/null | tail -1)

if [ "$TABLE_COUNT" -gt 0 ]; then
    echo "✅ Database imported successfully with $TABLE_COUNT tables!"

    # Check for critical errors (not just key length warnings)
    CRITICAL_ERRORS=$(grep -i "error" /tmp/import_log.txt | grep -v "key was too long" | grep -v "Specified key was too long" | wc -l)

    if [ "$CRITICAL_ERRORS" -gt 0 ]; then
        echo "⚠️  Warning: Some non-critical errors occurred during import"
        echo "   Tables imported: $TABLE_COUNT"
        echo "   You may want to review the log above"
    fi
else
    echo "❌ Import failed - no tables found!"
    exit 1
fi

# Step 3: Verify import
echo "3️⃣ Verifying import..."
TABLE_COUNT=$(docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$TEMPORAL_DB';" 2>/dev/null | tail -1)

if [ "$TABLE_COUNT" -gt 0 ]; then
    echo "✅ Import verified: $TABLE_COUNT tables found"

    # Additional verification: count expected tables from source file
    EXPECTED_TABLES=$(grep -i "CREATE TABLE" "$SOURCE_DB_FILE" | wc -l)
    echo "   Expected tables from source: $EXPECTED_TABLES"
    echo "   Actually imported: $TABLE_COUNT"

    if [ "$TABLE_COUNT" -lt "$EXPECTED_TABLES" ]; then
        echo "   ⚠️  Warning: Some tables may be missing ($((EXPECTED_TABLES - TABLE_COUNT)) tables)"
        echo "   This could be due to import errors or duplicate table definitions"
    fi
else
    echo "❌ Import verification failed: No tables found"
    exit 1
fi

# Step 4: Fix specific problematic tables
echo "4️⃣ Fixing specific problematic tables..."

# Fix pink_wpf_meta_keys (datetime and key length issues)
echo "   Fixing pink_wpf_meta_keys..."
if docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD $TEMPORAL_DB -e "
    SET SESSION sql_mode='';
    -- Update invalid datetime data
    UPDATE pink_wpf_meta_keys
    SET updated = NULL, locked = NULL, calculated = NULL
    WHERE updated = '0000-00-00 00:00:00'
       OR locked = '0000-00-00 00:00:00'
       OR calculated = '0000-00-00 00:00:00';

    -- Fix column defaults
    ALTER TABLE pink_wpf_meta_keys
    MODIFY COLUMN updated timestamp NULL DEFAULT NULL,
    MODIFY COLUMN locked timestamp NULL DEFAULT NULL,
    MODIFY COLUMN calculated timestamp NULL DEFAULT NULL;

    -- Fix key length and convert charset
    ALTER TABLE pink_wpf_meta_keys DROP INDEX meta_key;
    ALTER TABLE pink_wpf_meta_keys CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    ALTER TABLE pink_wpf_meta_keys ADD UNIQUE INDEX meta_key (meta_key(191));
" 2>/dev/null; then
    echo "   ✅ pink_wpf_meta_keys fixed and converted successfully"
else
    echo "   ⚠️  pink_wpf_meta_keys fix failed - will try in general conversion"
fi

# Fix pink_cli_cookie_scan_cookies (key length issues)
echo "   Fixing pink_cli_cookie_scan_cookies..."
if docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD $TEMPORAL_DB -e "
    -- Fix key length and convert charset
    ALTER TABLE pink_cli_cookie_scan_cookies DROP INDEX cookie;
    ALTER TABLE pink_cli_cookie_scan_cookies CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    ALTER TABLE pink_cli_cookie_scan_cookies ADD UNIQUE INDEX cookie (id_cli_cookie_scan, cookie_id(191));
" 2>/dev/null; then
    echo "   ✅ pink_cli_cookie_scan_cookies fixed and converted successfully"
else
    echo "   ⚠️  pink_cli_cookie_scan_cookies fix failed - will try in general conversion"
fi

echo "   ✅ Specific table fixes completed"

# Step 5: Convert charset from utf8mb3 to utf8mb4
echo "5️⃣ Converting tables from utf8mb3 to utf8mb4..."
echo "   Generating conversion commands..."

# Generate ALTER TABLE commands for remaining tables (excluding already converted ones)
CONVERSION_COMMANDS=$(docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD $TEMPORAL_DB -e "
SELECT CONCAT('ALTER TABLE \`', table_name, '\` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;')
FROM information_schema.tables
WHERE table_schema = '$TEMPORAL_DB'
AND table_collation LIKE '%utf8mb3%'
AND table_name NOT IN ('pink_wpf_meta_keys', 'pink_cli_cookie_scan_cookies');" 2>/dev/null | grep -v "CONCAT")

if [ -z "$CONVERSION_COMMANDS" ]; then
    echo "❌ No tables found to convert!"
    exit 1
fi

TOTAL_TABLES=$(echo "$CONVERSION_COMMANDS" | wc -l)
echo "   Converting $TOTAL_TABLES tables..."

# Debug: Show first few commands
echo "   Debug: First 3 commands:"
echo "$CONVERSION_COMMANDS" | head -3

# Save commands to a temporary file
TEMP_CONVERSION_FILE="/tmp/conversion_commands.sql"
echo "$CONVERSION_COMMANDS" > "$TEMP_CONVERSION_FILE"

echo "   Debug: File contains $(wc -l < "$TEMP_CONVERSION_FILE") lines"

# Execute conversion commands with better error handling
COUNTER=0
SUCCESS_COUNT=0
while IFS= read -r cmd; do
    if [ -n "$cmd" ] && [ "$cmd" != "CONCAT" ]; then
        COUNTER=$((COUNTER + 1))
        echo "   [$COUNTER/$TOTAL_TABLES] Converting table..."

        # Try conversion with force mode to skip problematic rows
        if docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD $TEMPORAL_DB \
           -e "SET SESSION sql_mode=''; $cmd" 2>/dev/null; then
            echo "   ✅ Table $COUNTER converted successfully"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        else
            echo "   ⚠️  Table $COUNTER had issues - skipping"
        fi
    fi
done < "$TEMP_CONVERSION_FILE"

echo "   Successfully converted: $SUCCESS_COUNT/$COUNTER tables"

# This line is now handled above

# Clean up temporary file
rm -f "$TEMP_CONVERSION_FILE"

echo "✅ Charset conversion completed!"

# Step 6: Export converted database
echo "6️⃣ Exporting converted database..."
if docker exec $MYSQL_CONTAINER mysqldump -u root -p$MYSQL_ROOT_PASSWORD $TEMPORAL_DB > "$CONVERTED_DB_FILE" 2>/dev/null; then
    echo "✅ Converted database exported to $CONVERTED_DB_FILE"
else
    echo "❌ Failed to export converted database!"
    exit 1
fi

# Step 7: Create target database with utf8mb4 charset
echo "7️⃣ Creating target database with utf8mb4 charset..."
docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "DROP DATABASE IF EXISTS $TARGET_DB;" 2>/dev/null
docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "CREATE DATABASE $TARGET_DB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null

# Step 8: Import converted database into target database
echo "8️⃣ Importing converted database into target database..."
if docker exec -i $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD $TARGET_DB < "$CONVERTED_DB_FILE" 2>/dev/null; then
    echo "✅ Converted database imported successfully!"
else
    echo "❌ Failed to import converted database!"
    exit 1
fi

# Step 9: Cleanup temporal database
echo "9️⃣ Cleaning up temporal database..."
docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "DROP DATABASE IF EXISTS $TEMPORAL_DB;" 2>/dev/null

# Step 10: Final verification
echo "🔟 Final verification..."
FINAL_TABLE_COUNT=$(docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$TARGET_DB';" 2>/dev/null | tail -1)
CHARSET_CHECK=$(docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "SELECT DEFAULT_CHARACTER_SET_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME = '$TARGET_DB';" 2>/dev/null | tail -1)

# Compare with expected tables from source
EXPECTED_TABLES=$(grep -i "CREATE TABLE" "$SOURCE_DB_FILE" | wc -l)

if [ "$FINAL_TABLE_COUNT" -gt 0 ] && [ "$CHARSET_CHECK" = "utf8mb4" ]; then
    echo "✅ Migration completed successfully!"
    echo ""
    echo "📊 Migration Summary:"
    echo "   Environment: $ENVIRONMENT"
    echo "   Source file: $SOURCE_DB_FILE"
    echo "   Target database: $TARGET_DB"
    echo "   Tables expected: $EXPECTED_TABLES"
    echo "   Tables migrated: $FINAL_TABLE_COUNT"
    echo "   Charset: utf8mb3 → utf8mb4"
    echo "   Converted file: $CONVERTED_DB_FILE"

    if [ "$FINAL_TABLE_COUNT" -lt "$EXPECTED_TABLES" ]; then
        echo ""
        echo "⚠️  Warning: Table count mismatch detected!"
        echo "   Expected: $EXPECTED_TABLES tables"
        echo "   Migrated: $FINAL_TABLE_COUNT tables"
        echo "   Missing: $((EXPECTED_TABLES - FINAL_TABLE_COUNT)) tables"
        echo ""
        echo "🔍 Troubleshooting:"
        echo "   1. Check /tmp/import_log.txt for import errors"
        echo "   2. Some CREATE TABLE statements might be duplicates or conditional"
        echo "   3. Tables might have failed due to charset/key length issues"
    fi

    echo ""
    echo "💡 Next steps:"
    echo "   1. Test your WordPress application"
    echo "   2. Run URL fixes if needed: bash scripts/fix-urls-after-import.sh"
    echo "   3. Check logs: make logs-$ENVIRONMENT"
    echo "   4. Clean up files: rm $CONVERTED_DB_FILE (optional)"
else
    echo "❌ Migration verification failed!"
    echo "   Tables: $FINAL_TABLE_COUNT"
    echo "   Charset: $CHARSET_CHECK"
    exit 1
fi

# Step 11: Cleanup files
echo "1️⃣1️⃣ Cleaning up files..."

# Restore original file from backup
if [ -f "${SOURCE_DB_FILE}.backup" ]; then
    echo "   Restoring original SQL file..."
    mv "${SOURCE_DB_FILE}.backup" "$SOURCE_DB_FILE"
    echo "   ✅ Original file restored"
fi

echo ""
echo "🧹 Cleanup:"
echo "   Temporary files cleaned up"
echo "   Original file restored"
