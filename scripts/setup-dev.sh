#!/bin/bash
# setup-dev.sh - Development environment setup

echo "Setting up WordPress development environment..."

# Create necessary directories
mkdir -p wordpress php-config mysql-config redis-config nginx-config ssl backups

# Create nginx cache directories with proper permissions
mkdir -p nginx-cache/proxy nginx-cache/fastcgi
chmod -R 755 nginx-cache

# Create cache directories
mkdir -p /tmp/nginx-cache

# Set permissions for scripts in scripts directory
chmod +x scripts/setup-dev.sh scripts/setup-prod.sh

# Start development environment
echo "Starting development containers..."
docker compose -f docker-compose.dev.yml up -d

# Wait for MySQL to be ready
echo "Waiting for MySQL to be ready..."
until docker exec mysql-dev mysqladmin ping -h"localhost" --silent; do
    echo "MySQL is unavailable - sleeping for 2 seconds"
    sleep 2
done
echo "MySQL is ready!"

# Install WordPress if not already installed
docker exec wordpress-dev wp core install \
    --url="http://localhost:8080" \
    --title="WordPress Development Site" \
    --admin_user="admin" \
    --admin_password="admin123" \
    --admin_email="<EMAIL>" \
    --allow-root

# Install Redis Object Cache plugin
docker exec wordpress-dev wp plugin install redis-cache --activate --allow-root

# Enable Redis cache
docker exec wordpress-dev wp redis enable --allow-root

echo "Development environment is ready!"
echo "WordPress: http://localhost:8080"
echo "Username: admin"
echo "Password: admin123"
echo "phpMyAdmin: http://localhost:8081"


