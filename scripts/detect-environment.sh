#!/bin/bash
# detect-environment.sh - Common environment detection for Docker containers

# Function to check if container is running
is_container_running() {
    docker ps --format "{{.Names}}" | grep -q "^$1$"
}

# Function to get container status and ports
get_container_info() {
    local container=$1
    if is_container_running "$container"; then
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "^$container"
    else
        echo "$container\tNot running\t-"
    fi
}

# Function to display environment info
display_environment_info() {
    local env=$1
    local wp_container=$2
    local mysql_container=$3
    local url=$4
    
    echo "📋 $env Environment:"
    echo "   WordPress: $(get_container_info $wp_container)"
    echo "   MySQL: $(get_container_info $mysql_container)"
    echo "   URL: $url"
    echo ""
}

# Check for environment parameter
FORCE_ENV=""
if [[ "$1" == "--env=dev" ]]; then
    FORCE_ENV="development"
elif [[ "$1" == "--env=prod" ]]; then
    FORCE_ENV="production"
elif [[ "$1" == "--env="* ]]; then
    echo "❌ Error: Invalid environment parameter '$1'"
    echo "   Valid options: --env=dev, --env=prod"
    exit 1
fi

# Detect running environments
DEV_RUNNING=false
PROD_RUNNING=false
SWARM_RUNNING=false

if is_container_running "wordpress-dev" && is_container_running "mysql-dev"; then
    DEV_RUNNING=true
fi

if is_container_running "wordpress-prod" && is_container_running "mysql-prod"; then
    PROD_RUNNING=true
fi

# Check for Docker Swarm production environment
if docker info | grep -q "Swarm: active" 2>/dev/null; then
    if docker service ls --format "{{.Name}}" | grep -q "^pinkangel-prod_wordpress-prod$" 2>/dev/null; then
        SWARM_RUNNING=true
        PROD_RUNNING=true  # Treat swarm as production
    fi
fi

# Handle environment detection
if [[ "$DEV_RUNNING" == true && "$PROD_RUNNING" == true ]]; then
    # Both environments running
    if [[ -z "$FORCE_ENV" ]]; then
        echo "⚠️  Both environments are currently running!"
        echo ""
        display_environment_info "Development" "wordpress-dev" "mysql-dev" "http://localhost:8080"
        display_environment_info "Production" "wordpress-prod" "mysql-prod" "http://localhost"
        echo "❌ Error: Multiple environments detected!"
        echo "   Please specify which environment to use:"
        echo "   - For development: $0 --env=dev"
        echo "   - For production:  $0 --env=prod"
        exit 1
    fi
    
    # Use forced environment
    if [[ "$FORCE_ENV" == "development" ]]; then
        ENVIRONMENT="development"
        WORDPRESS_CONTAINER="wordpress-dev"
        MYSQL_CONTAINER="mysql-dev"
        MYSQL_ROOT_PASSWORD="root_password"
        ENV_URL="http://localhost:8080"
    else
        ENVIRONMENT="production"
        if [[ "$SWARM_RUNNING" == true ]]; then
            WORDPRESS_CONTAINER=$(docker ps -q --filter "name=pinkangel-prod_wordpress-prod" | head -1)
            MYSQL_CONTAINER=$(docker ps -q --filter "name=pinkangel-prod_mysql-prod" | head -1)
        else
            WORDPRESS_CONTAINER="wordpress-prod"
            MYSQL_CONTAINER="mysql-prod"
        fi
        if [ -f ".env" ]; then
            MYSQL_ROOT_PASSWORD=$(grep "^MYSQL_ROOT_PASSWORD=" .env | cut -d'=' -f2)
        else
            MYSQL_ROOT_PASSWORD="your_secure_root_password_here"
        fi
        ENV_URL="http://localhost"
    fi
    
elif [[ "$DEV_RUNNING" == true ]]; then
    # Only development running
    if [[ "$FORCE_ENV" == "production" ]]; then
        echo "❌ Error: Production environment requested but not running!"
        echo "   Start production with: make prod"
        exit 1
    fi
    
    ENVIRONMENT="development"
    WORDPRESS_CONTAINER="wordpress-dev"
    MYSQL_CONTAINER="mysql-dev"
    MYSQL_ROOT_PASSWORD="root_password"
    ENV_URL="http://localhost:8080"
    
elif [[ "$PROD_RUNNING" == true ]]; then
    # Only production running
    if [[ "$FORCE_ENV" == "development" ]]; then
        echo "❌ Error: Development environment requested but not running!"
        echo "   Start development with: make run-dev"
        exit 1
    fi

    ENVIRONMENT="production"
    if [[ "$SWARM_RUNNING" == true ]]; then
        WORDPRESS_CONTAINER=$(docker ps -q --filter "name=pinkangel-prod_wordpress-prod" | head -1)
        MYSQL_CONTAINER=$(docker ps -q --filter "name=pinkangel-prod_mysql-prod" | head -1)
    else
        WORDPRESS_CONTAINER="wordpress-prod"
        MYSQL_CONTAINER="mysql-prod"
    fi
    if [ -f ".env" ]; then
        MYSQL_ROOT_PASSWORD=$(grep "^MYSQL_ROOT_PASSWORD=" .env | cut -d'=' -f2)
    else
        MYSQL_ROOT_PASSWORD="your_secure_root_password_here"
    fi
    ENV_URL="http://localhost"
    
else
    # No environments running
    echo "❌ Error: No Docker environments are currently running!"
    echo ""
    echo "   Please start an environment first:"
    echo "   - Development: make dev"
    echo "   - Production:  make prod"
    echo ""
    echo "   Then run this script again."
    exit 1
fi

# Export variables for use by other scripts
export ENVIRONMENT
export WORDPRESS_CONTAINER
export MYSQL_CONTAINER
export MYSQL_ROOT_PASSWORD
export ENV_URL

# Display detected environment info (only if not being sourced silently)
if [[ "${BASH_SOURCE[0]}" == "${0}" ]] || [[ "$DETECT_ENV_VERBOSE" == "true" ]]; then
    echo "🔍 Detected: $ENVIRONMENT environment"
    echo ""
    display_environment_info "$ENVIRONMENT" "$WORDPRESS_CONTAINER" "$MYSQL_CONTAINER" "$ENV_URL"
fi
