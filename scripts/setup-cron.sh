#!/bin/bash
# setup-cron.sh - Setup and deploy WordPress cron container
# Handles initial deployment and configuration of the cron system

set -e

# Source environment detection
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/detect-environment.sh" >/dev/null 2>&1 || {
    echo "❌ Error: Could not source environment detection script"
    exit 1
}

# Configuration
SCRIPT_NAME="setup-cron"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }
print_header() { echo -e "${PURPLE}🔧 $1${NC}"; }

# Function to show usage
show_usage() {
    echo ""
    print_header "WordPress Cron Container Setup"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  deploy          Deploy cron container for current environment"
    echo "  build           Build cron container image"
    echo "  verify          Verify cron container setup"
    echo "  migrate         Migrate from wp-cron to container-based cron"
    echo "  rollback        Rollback to wp-cron system"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy       # Deploy cron container"
    echo "  $0 build        # Build cron container image"
    echo "  $0 verify       # Verify setup is working"
}

# Function to build cron container image
build_cron_image() {
    print_header "Building WordPress Cron Container Image"
    echo ""
    
    print_info "Building Docker image..."
    docker build -f Dockerfile.cron -t wordpress-cron:latest . || {
        print_error "Failed to build cron container image"
        return 1
    }
    
    print_success "Cron container image built successfully"
}

# Function to deploy cron container
deploy_cron_container() {
    print_header "Deploying WordPress Cron Container ($ENVIRONMENT)"
    echo ""
    
    # Check if main WordPress environment is running
    if [[ "$ENVIRONMENT" == "development" ]]; then
        if ! docker ps --format "{{.Names}}" | grep -q "wordpress-dev"; then
            print_error "Development WordPress container is not running!"
            print_info "Start it first with: make dev"
            return 1
        fi
        COMPOSE_FILE="docker-compose.dev.yml"
        SERVICE_NAME="wordpress-cron-dev"
    else
        if ! docker ps --format "{{.Names}}" | grep -q "wordpress-prod"; then
            print_error "Production WordPress container is not running!"
            print_info "Start it first with: make prod"
            return 1
        fi
        COMPOSE_FILE="docker-compose.prod.yml"
        SERVICE_NAME="wordpress-cron-prod"
    fi
    
    # Build the image first
    print_info "Building cron container image..."
    build_cron_image || return 1
    
    # Deploy the container
    print_info "Deploying cron container..."
    docker compose -f "$COMPOSE_FILE" up -d "$SERVICE_NAME" || {
        print_error "Failed to deploy cron container"
        return 1
    }
    
    # Wait for container to be ready
    print_info "Waiting for container to be ready..."
    sleep 10
    
    # Verify deployment
    if docker ps --format "{{.Names}}" | grep -q "$SERVICE_NAME"; then
        print_success "Cron container deployed successfully"
        
        # Show initial status
        echo ""
        print_info "Initial container status:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "$SERVICE_NAME"
        
        # Wait a bit more and check health
        sleep 5
        if docker exec "$SERVICE_NAME" test -f /tmp/cron-health.status 2>/dev/null; then
            echo ""
            print_info "Health status:"
            docker exec "$SERVICE_NAME" cat /tmp/cron-health.status 2>/dev/null | python3 -m json.tool 2>/dev/null || {
                docker exec "$SERVICE_NAME" cat /tmp/cron-health.status 2>/dev/null
            }
        fi
    else
        print_error "Cron container failed to start"
        return 1
    fi
}

# Function to verify cron setup
verify_cron_setup() {
    print_header "Verifying WordPress Cron Container Setup"
    echo ""
    
    local container_name
    if [[ "$ENVIRONMENT" == "development" ]]; then
        container_name="wordpress-cron-dev"
    else
        container_name="wordpress-cron-prod"
    fi
    
    # Check if container is running
    if ! docker ps --format "{{.Names}}" | grep -q "$container_name"; then
        print_error "Cron container is not running"
        return 1
    fi
    
    print_success "Cron container is running"
    
    # Check WordPress connectivity
    print_info "Checking WordPress connectivity..."
    if docker exec "$container_name" wp core version --allow-root >/dev/null 2>&1; then
        print_success "WordPress is accessible from cron container"
        local wp_version=$(docker exec "$container_name" wp core version --allow-root 2>/dev/null)
        echo "  WordPress Version: $wp_version"
    else
        print_error "WordPress is not accessible from cron container"
        return 1
    fi
    
    # Check database connectivity
    print_info "Checking database connectivity..."
    if docker exec "$container_name" wp db check --allow-root >/dev/null 2>&1; then
        print_success "Database is accessible from cron container"
    else
        print_error "Database is not accessible from cron container"
        return 1
    fi
    
    # Check cron jobs
    print_info "Checking cron jobs..."
    local cron_count=$(docker exec "$container_name" crontab -l 2>/dev/null | grep -v '^#' | grep -v '^$' | wc -l || echo "0")
    if [[ "$cron_count" -gt 0 ]]; then
        print_success "Cron jobs are configured ($cron_count active jobs)"
    else
        print_warning "No cron jobs found"
    fi
    
    # Check health status
    print_info "Checking health status..."
    if docker exec "$container_name" test -f /tmp/cron-health.status 2>/dev/null; then
        local health_status=$(docker exec "$container_name" cat /tmp/cron-health.status 2>/dev/null | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('status', 'UNKNOWN'))" 2>/dev/null || echo "UNKNOWN")
        case "$health_status" in
            "OK")
                print_success "Health status: OK"
                ;;
            "WARNING")
                print_warning "Health status: WARNING"
                ;;
            "ERROR")
                print_error "Health status: ERROR"
                ;;
            *)
                print_warning "Health status: UNKNOWN"
                ;;
        esac
    else
        print_warning "Health status file not found"
    fi
    
    # Check if wp-cron is properly disabled
    print_info "Checking wp-cron configuration..."
    local wp_cron_disabled=$(docker exec "$container_name" wp config get DISABLE_WP_CRON --allow-root 2>/dev/null || echo "false")
    if [[ "$wp_cron_disabled" == "true" ]]; then
        print_success "WP-Cron is properly disabled"
    else
        print_warning "WP-Cron may not be disabled (DISABLE_WP_CRON = $wp_cron_disabled)"
    fi
    
    echo ""
    print_success "Cron container verification completed"
}

# Function to migrate from wp-cron to container-based cron
migrate_to_container_cron() {
    print_header "Migrating from WP-Cron to Container-Based Cron"
    echo ""
    
    print_warning "This will disable WordPress built-in cron system"
    read -p "Continue with migration? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Migration cancelled by user"
        return 0
    fi
    
    # Check if DISABLE_WP_CRON is already set
    local wp_cron_disabled=$(wp config get DISABLE_WP_CRON --allow-root 2>/dev/null || echo "false")
    if [[ "$wp_cron_disabled" == "true" ]]; then
        print_info "WP-Cron is already disabled"
    else
        print_info "Disabling WP-Cron in wp-config.php..."
        # This would need to be done manually or with a more sophisticated script
        print_warning "Please ensure DISABLE_WP_CRON is set to true in wp-config.php"
    fi
    
    # Deploy cron container
    deploy_cron_container || return 1
    
    # Verify setup
    verify_cron_setup || return 1
    
    print_success "Migration to container-based cron completed successfully"
    echo ""
    print_info "Next steps:"
    echo "  1. Monitor cron container with: make cron-monitor"
    echo "  2. Check logs with: make cron-logs"
    echo "  3. Run manual tasks with: make cron-run-now TASK=wp-cron"
}

# Function to rollback to wp-cron
rollback_to_wp_cron() {
    print_header "Rolling Back to WordPress Built-in Cron"
    echo ""
    
    print_warning "This will stop the cron container and re-enable wp-cron"
    read -p "Continue with rollback? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Rollback cancelled by user"
        return 0
    fi
    
    # Stop cron container
    local container_name
    if [[ "$ENVIRONMENT" == "development" ]]; then
        container_name="wordpress-cron-dev"
        docker compose -f docker-compose.dev.yml stop wordpress-cron-dev 2>/dev/null || true
    else
        container_name="wordpress-cron-prod"
        docker compose -f docker-compose.prod.yml stop wordpress-cron-prod 2>/dev/null || true
    fi
    
    print_info "Cron container stopped"
    
    print_warning "Please manually set DISABLE_WP_CRON to false in wp-config.php"
    print_info "Then restart your WordPress container to re-enable wp-cron"
    
    print_success "Rollback completed"
}

# Main script logic
case "${1:-}" in
    "deploy")
        deploy_cron_container
        ;;
    "build")
        build_cron_image
        ;;
    "verify")
        verify_cron_setup
        ;;
    "migrate")
        migrate_to_container_cron
        ;;
    "rollback")
        rollback_to_wp_cron
        ;;
    "help"|"-h"|"--help"|"")
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
