#!/bin/bash

# Test script to verify nginx-proxy integration with production environment
# This script tests that the make prod command properly starts the nginx-proxy container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test functions
test_proxy_not_running() {
    log_info "Test 1: Ensuring proxy is not running initially"
    
    # Stop proxy if running
    docker compose -f docker-compose.proxy.yml down 2>/dev/null || true
    
    # Verify it's not running
    if docker ps --format "table {{.Names}}" | grep -q "reverse-proxy"; then
        log_error "Proxy is still running after stop command"
        return 1
    fi
    
    log_success "Proxy is not running (as expected)"
    return 0
}

test_proxy_network_exists() {
    log_info "Test 2: Checking if proxy network gets created"
    
    # Start proxy using the manage script
    chmod +x scripts/manage-proxy.sh
    ./scripts/manage-proxy.sh start
    
    # Check if proxy is running
    if ! docker ps --format "table {{.Names}}" | grep -q "reverse-proxy"; then
        log_error "Proxy container is not running after start command"
        return 1
    fi
    
    # Check if network exists
    if ! docker network ls --format "table {{.Name}}" | grep -q "proxy-network"; then
        log_error "proxy-network does not exist after starting proxy"
        return 1
    fi
    
    log_success "Proxy container and network are running correctly"
    return 0
}

test_make_prod_integration() {
    log_info "Test 3: Testing make prod integration (synchronous behavior)"

    # First stop the proxy to test auto-start
    ./scripts/manage-proxy.sh stop

    # Verify proxy is stopped
    if docker ps --format "table {{.Names}}" | grep -q "reverse-proxy"; then
        log_error "Proxy is still running after stop"
        return 1
    fi

    log_info "Proxy stopped successfully"

    # Test the proxy start part of make prod (without actually running prod)
    log_info "Testing synchronous proxy auto-start functionality..."

    # This simulates what make prod does - synchronous start
    chmod +x scripts/manage-proxy.sh
    if ! ./scripts/manage-proxy.sh start; then
        log_error "Proxy start command failed"
        return 1
    fi

    # Verify proxy started
    if ! docker ps --format "table {{.Names}}" | grep -q "reverse-proxy"; then
        log_error "Proxy did not start automatically"
        return 1
    fi

    # Test that proxy-network exists
    if ! docker network ls --format "table {{.Name}}" | grep -q "proxy-network"; then
        log_error "proxy-network does not exist after starting proxy"
        return 1
    fi

    log_success "Synchronous proxy auto-start functionality works correctly"
    return 0
}

test_network_connection() {
    log_info "Test 4: Testing network connection functionality"

    # Ensure proxy is running
    if ! docker ps --format "table {{.Names}}" | grep -q "reverse-proxy"; then
        ./scripts/manage-proxy.sh start
    fi

    # Create a test network (simulating production network)
    docker network create test-prod-network 2>/dev/null || true

    # Test connecting proxy to the network
    if docker network connect test-prod-network reverse-proxy 2>/dev/null; then
        log_success "Successfully connected proxy to test network"

        # Verify connection
        if docker network inspect test-prod-network --format '{{range .Containers}}{{.Name}} {{end}}' | grep -q "reverse-proxy"; then
            log_success "Proxy is connected to test network"
        else
            log_error "Proxy connection verification failed"
            return 1
        fi

        # Disconnect and cleanup
        docker network disconnect test-prod-network reverse-proxy 2>/dev/null || true
    else
        log_warning "Network connection test skipped (proxy might already be connected)"
    fi

    # Cleanup test network
    docker network rm test-prod-network 2>/dev/null || true

    log_success "Network connection test completed"
    return 0
}

test_cleanup() {
    log_info "Test 5: Cleanup test"

    # Stop proxy
    ./scripts/manage-proxy.sh stop

    # Verify it's stopped
    if docker ps --format "table {{.Names}}" | grep -q "reverse-proxy"; then
        log_error "Proxy is still running after cleanup"
        return 1
    fi

    log_success "Cleanup completed successfully"
    return 0
}

# Main test execution
main() {
    log_info "Starting nginx-proxy integration tests..."
    echo ""
    
    # Check if we're in the right directory
    if [ ! -f "docker-compose.proxy.yml" ]; then
        log_error "docker-compose.proxy.yml not found. Please run this script from the project root."
        exit 1
    fi
    
    # Check if .env exists (required for production)
    if [ ! -f ".env" ]; then
        log_warning ".env file not found. Production tests will be limited."
    fi
    
    # Run tests
    local failed_tests=0
    
    if ! test_proxy_not_running; then
        ((failed_tests++))
    fi
    echo ""
    
    if ! test_proxy_network_exists; then
        ((failed_tests++))
    fi
    echo ""
    
    if ! test_make_prod_integration; then
        ((failed_tests++))
    fi
    echo ""

    if ! test_network_connection; then
        ((failed_tests++))
    fi
    echo ""

    if ! test_cleanup; then
        ((failed_tests++))
    fi
    echo ""
    
    # Summary
    if [ $failed_tests -eq 0 ]; then
        log_success "All tests passed! ✅"
        log_info "The nginx-proxy integration is working correctly."
        log_info "You can now run 'make prod' and it will automatically start the nginx-proxy container."
    else
        log_error "$failed_tests test(s) failed! ❌"
        log_info "Please check the errors above and fix any issues."
        exit 1
    fi
}

# Run main function
main "$@"
