#!/bin/bash
# restore.sh - Restore script for WordPress Docker environment

if [ -z "$1" ]; then
    echo "Usage: ./restore.sh <backup_file>"
    echo "Available backups:"
    if [ -d "backups" ]; then
        ls -la backups/wordpress_complete_backup_*.tar.gz 2>/dev/null || echo "No backups found"
    else
        echo "No backups directory found"
    fi
    exit 1
fi

BACKUP_FILE=$1
TEMP_DIR="/tmp/wp_restore_$$"

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo "Error: Backup file '$BACKUP_FILE' not found"
    exit 1
fi

echo "Restoring from backup: $BACKUP_FILE"

# Auto-detect environment based on running containers or ask user
if docker ps --format "table {{.Names}}" | grep -q "mysql-prod"; then
    ENV="prod"
    COMPOSE_FILE="docker-compose.prod.yml"
    MYSQL_CONTAINER="mysql-prod"
    REDIS_CONTAINER="redis-prod"
    MYSQL_ROOT_PASSWORD="${MYSQL_ROOT_PASSWORD:-root_password}"
    echo "Detected PRODUCTION environment"
elif docker ps --format "table {{.Names}}" | grep -q "mysql-dev"; then
    ENV="dev"
    COMPOSE_FILE="docker-compose.dev.yml"
    MYSQL_CONTAINER="mysql-dev"
    REDIS_CONTAINER="redis-dev"
    MYSQL_ROOT_PASSWORD="root_password"
    echo "Detected DEVELOPMENT environment"
else
    echo "No running environment detected. Which environment do you want to restore to?"
    echo "1) Development (dev)"
    echo "2) Production (prod)"
    read -p "Enter choice (1 or 2): " choice

    case $choice in
        1)
            ENV="dev"
            COMPOSE_FILE="docker-compose.dev.yml"
            MYSQL_CONTAINER="mysql-dev"
            REDIS_CONTAINER="redis-dev"
            MYSQL_ROOT_PASSWORD="root_password"
            ;;
        2)
            ENV="prod"
            COMPOSE_FILE="docker-compose.prod.yml"
            MYSQL_CONTAINER="mysql-prod"
            REDIS_CONTAINER="redis-prod"
            MYSQL_ROOT_PASSWORD="${MYSQL_ROOT_PASSWORD:-root_password}"
            ;;
        *)
            echo "Invalid choice. Exiting."
            exit 1
            ;;
    esac
fi

echo "Restoring to $ENV environment..."

# Create temporary directory
mkdir -p $TEMP_DIR
cd $TEMP_DIR

# Extract backup
echo "Extracting backup..."
tar -xzf "$(pwd)/../$BACKUP_FILE"

# Stop containers
echo "Stopping containers..."
docker compose -f ../$COMPOSE_FILE down

# Restore files
echo "Restoring WordPress files..."
if [ -f wordpress_files_*.tar.gz ]; then
    tar -xzf wordpress_files_*.tar.gz -C ../
    echo "✅ WordPress files restored"
else
    echo "⚠️  No WordPress files found in backup"
fi

# Start database container
echo "Starting database container..."
docker compose -f ../$COMPOSE_FILE up -d $MYSQL_CONTAINER
echo "Waiting for database to be ready..."
sleep 30

# Test database connection
echo "Testing database connection..."
for i in {1..10}; do
    if docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "SELECT 1;" >/dev/null 2>&1; then
        echo "✅ Database is ready"
        break
    fi
    echo "Waiting for database... ($i/10)"
    sleep 5
done

# Restore databases
echo "Restoring databases..."
for db_file in *_db_*.sql; do
    if [ -f "$db_file" ]; then
        # Extract database name from filename
        db_name=$(echo "$db_file" | sed 's/_db_.*\.sql$//')
        echo "Restoring database: $db_name"

        # Create database if it doesn't exist
        docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "CREATE DATABASE IF NOT EXISTS $db_name;"

        # Restore database
        docker exec -i $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD $db_name < "$db_file"
        echo "✅ Database $db_name restored"
    fi
done

# Start Redis container
echo "Starting Redis container..."
docker compose -f ../$COMPOSE_FILE up -d $REDIS_CONTAINER
sleep 10

# Restore Redis data
echo "Restoring Redis data..."
if [ -f redis_dump_*.rdb ]; then
    # Stop Redis to safely replace dump file
    docker exec $REDIS_CONTAINER redis-cli SHUTDOWN NOSAVE 2>/dev/null || true
    sleep 2

    # Copy dump file
    docker cp redis_dump_*.rdb $REDIS_CONTAINER:/data/dump.rdb

    # Restart Redis container
    docker restart $REDIS_CONTAINER
    sleep 5
    echo "✅ Redis data restored"
else
    echo "⚠️  No Redis dump found in backup"
fi

# Start all containers
echo "Starting all containers..."
docker compose -f ../$COMPOSE_FILE up -d

# Clean up
cd ..
rm -rf $TEMP_DIR

echo "✅ Restore completed successfully!"
echo ""
echo "Environment: $ENV"
echo "Access WordPress at: http://localhost:$([ "$ENV" = "dev" ] && echo "8080" || echo "80")"
echo "Access phpMyAdmin at: http://localhost:$([ "$ENV" = "dev" ] && echo "8081" || echo "8080")"


