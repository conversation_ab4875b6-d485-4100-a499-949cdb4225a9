#!/bin/bash

# Comprehensive verification script for docker-compose.prod.yml and docker-compose.proxy.yml
# This script checks configuration, dependencies, volumes, networks, and more

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if files exist
check_files_exist() {
    log_info "Checking if docker-compose files exist..."
    
    local files_missing=0
    
    if [ ! -f "docker-compose.prod.yml" ]; then
        log_error "docker-compose.prod.yml not found"
        ((files_missing++))
    else
        log_success "docker-compose.prod.yml found"
    fi
    
    if [ ! -f "docker-compose.proxy.yml" ]; then
        log_error "docker-compose.proxy.yml not found"
        ((files_missing++))
    else
        log_success "docker-compose.proxy.yml found"
    fi
    
    return $files_missing
}

# Validate docker-compose syntax
validate_syntax() {
    log_info "Validating docker-compose syntax..."
    
    local syntax_errors=0
    
    log_info "Checking docker-compose.prod.yml syntax..."
    if docker compose -f docker-compose.prod.yml config --quiet 2>/dev/null; then
        log_success "docker-compose.prod.yml syntax is valid"
    else
        log_error "docker-compose.prod.yml has syntax errors"
        ((syntax_errors++))
    fi

    log_info "Checking docker-compose.proxy.yml syntax..."
    if docker compose -f docker-compose.proxy.yml config --quiet 2>/dev/null; then
        log_success "docker-compose.proxy.yml syntax is valid"
    else
        log_error "docker-compose.proxy.yml has syntax errors"
        ((syntax_errors++))
    fi
    
    return $syntax_errors
}

# Check environment variables
check_environment_variables() {
    log_info "Checking environment variables in docker-compose.prod.yml..."
    
    # Extract all ${VAR} patterns
    local env_vars=$(grep -o '\${[^}]*}' docker-compose.prod.yml | sed 's/\${//' | sed 's/}//' | sort | uniq)
    local missing_vars=0
    
    for var in $env_vars; do
        if [ -f ".env" ] && grep -q "^$var=" .env; then
            log_success "  ✅ $var - Found in .env"
        else
            log_warning "  ⚠️  $var - Missing from .env"
            ((missing_vars++))
        fi
    done
    
    log_info "Found $missing_vars potentially missing environment variables"
    return 0
}

# Check volume mounts and directories
check_volumes_and_directories() {
    log_info "Checking volume mounts and required directories..."
    
    local missing_dirs=0
    
    # Directories that should exist for production
    local required_dirs=(
        "wordpress"
        "nginx-config"
        "php-config"
        "mysql-config"
        "redis-config"
        "ssl"
        "backups"
        "nginx-cache"
    )
    
    # Directories that should exist for proxy
    local proxy_dirs=(
        "nginx-proxy-config"
        "nginx-proxy-config/sites"
        "ssl-proxy"
        "nginx-proxy-cache"
    )
    
    log_info "Checking production directories..."
    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ]; then
            log_success "  ✅ $dir/ exists"
        else
            log_warning "  ⚠️  $dir/ missing (will be created automatically)"
            ((missing_dirs++))
        fi
    done
    
    log_info "Checking proxy directories..."
    for dir in "${proxy_dirs[@]}"; do
        if [ -d "$dir" ]; then
            log_success "  ✅ $dir/ exists"
        else
            log_warning "  ⚠️  $dir/ missing (will be created automatically)"
            ((missing_dirs++))
        fi
    done
    
    return 0
}

# Check required configuration files
check_config_files() {
    log_info "Checking required configuration files..."
    
    local missing_configs=0
    
    # Production config files
    local prod_configs=(
        "nginx-config/nginx.conf"
        "nginx-config/wordpress.conf"
        "php-config/php-prod.ini"
        "php-config/opcache.ini"
        "mysql-config/my-prod.cnf"
        "redis-config/redis-prod.conf"
    )
    
    # Proxy config files
    local proxy_configs=(
        "nginx-proxy-config/nginx.conf"
    )
    
    log_info "Checking production config files..."
    for config in "${prod_configs[@]}"; do
        if [ -f "$config" ]; then
            log_success "  ✅ $config exists"
        else
            log_error "  ❌ $config missing"
            ((missing_configs++))
        fi
    done
    
    log_info "Checking proxy config files..."
    for config in "${proxy_configs[@]}"; do
        if [ -f "$config" ]; then
            log_success "  ✅ $config exists"
        else
            log_error "  ❌ $config missing"
            ((missing_configs++))
        fi
    done
    
    return $missing_configs
}

# Check Dockerfiles
check_dockerfiles() {
    log_info "Checking required Dockerfiles..."
    
    local missing_dockerfiles=0
    
    local dockerfiles=(
        "Dockerfile.prod"
        "Dockerfile.cron"
    )
    
    for dockerfile in "${dockerfiles[@]}"; do
        if [ -f "$dockerfile" ]; then
            log_success "  ✅ $dockerfile exists"
        else
            log_error "  ❌ $dockerfile missing"
            ((missing_dockerfiles++))
        fi
    done
    
    return $missing_dockerfiles
}

# Check network configuration
check_networks() {
    log_info "Checking network configuration..."
    
    # Check if proxy-network is defined as external in prod
    if grep -q "proxy-network:" docker-compose.prod.yml && grep -A2 "proxy-network:" docker-compose.prod.yml | grep -q "external: true"; then
        log_success "  ✅ proxy-network correctly defined as external in production"
    else
        log_error "  ❌ proxy-network not properly configured as external in production"
    fi
    
    # Check if proxy-network is defined in proxy file
    if grep -q "proxy-network:" docker-compose.proxy.yml; then
        log_success "  ✅ proxy-network defined in proxy compose file"
    else
        log_error "  ❌ proxy-network missing from proxy compose file"
    fi
    
    return 0
}

# Check service dependencies
check_service_dependencies() {
    log_info "Checking service dependencies..."
    
    # Check if WordPress depends on MySQL and Redis
    if grep -A10 "wordpress-prod:" docker-compose.prod.yml | grep -q "depends_on:" && \
       grep -A10 "wordpress-prod:" docker-compose.prod.yml | grep -A5 "depends_on:" | grep -q "mysql-prod" && \
       grep -A10 "wordpress-prod:" docker-compose.prod.yml | grep -A5 "depends_on:" | grep -q "redis-prod"; then
        log_success "  ✅ WordPress service has correct dependencies"
    else
        log_warning "  ⚠️  WordPress service dependencies may be incomplete"
    fi
    
    # Check if nginx depends on WordPress
    if grep -A10 "nginx:" docker-compose.prod.yml | grep -q "depends_on:" && \
       grep -A10 "nginx:" docker-compose.prod.yml | grep -A5 "depends_on:" | grep -q "wordpress-prod"; then
        log_success "  ✅ Nginx service has correct dependencies"
    else
        log_warning "  ⚠️  Nginx service dependencies may be incomplete"
    fi
    
    return 0
}

# Main execution
main() {
    log_info "🔍 Starting comprehensive Docker Compose verification..."
    echo ""
    
    local total_errors=0
    
    if ! check_files_exist; then
        ((total_errors++))
    fi
    echo ""
    
    if ! validate_syntax; then
        ((total_errors++))
    fi
    echo ""
    
    check_environment_variables
    echo ""
    
    check_volumes_and_directories
    echo ""
    
    if ! check_config_files; then
        ((total_errors++))
    fi
    echo ""
    
    if ! check_dockerfiles; then
        ((total_errors++))
    fi
    echo ""
    
    check_networks
    echo ""
    
    check_service_dependencies
    echo ""
    
    if [ $total_errors -eq 0 ]; then
        log_success "✅ All critical checks passed! Docker Compose files are properly configured."
    else
        log_error "❌ Found $total_errors critical issues that need to be addressed."
        exit 1
    fi
}

# Run main function
main "$@"
