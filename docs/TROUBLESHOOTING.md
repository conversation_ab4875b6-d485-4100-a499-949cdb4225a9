# Troubleshooting Guide

Common issues and their solutions for the PinkAngel WordPress Docker environment.

## 🚨 Emergency Quick Fixes

### WordPress Shows Blank Page
```bash
# Check for PHP fatal errors
docker logs wordpress-dev --tail 20

# Install missing theme dependencies
make install-theme-deps-dev

# Check if all plugins are active
docker exec wordpress-dev wp plugin list --allow-root
```

### Database Connection Error
```bash
# Check if MySQL is running
docker ps | grep mysql

# Test database connection
docker exec mysql-dev mysql -u root -proot_password -e "SELECT 1"

# Restart environment
make stop && make dev
```

### SSL Certificate Issues
```bash
# Regenerate SSL certificates
make ssl

# Check certificate validity
openssl x509 -in ssl/cert.pem -text -noout

# Restart production with new certificates
make prod
```

## 🔧 Common Issues and Solutions

### 1. Port Conflicts

**Problem**: "Port already in use" errors

**Symptoms**:
```
Error: bind: address already in use
```

**Solutions**:
```bash
# Check what's using the ports
lsof -i :8080  # Development WordPress
lsof -i :8081  # phpMyAdmin  
lsof -i :80    # Production HTTP
lsof -i :443   # Production HTTPS

# Kill processes using the ports
sudo kill -9 $(lsof -t -i:8080)

# Or change ports in docker-compose files
# Edit docker-compose.dev.yml and change "8080:80" to "8090:80"
```

### 2. WordPress Theme Issues

**Problem**: PinkAngel theme shows fatal errors

**Symptoms**:
```
Fatal error: Call to undefined function woocommerce_mini_cart()
Fatal error: Call to undefined function aws_get_search_form()
```

**Solutions**:
```bash
# Install all required plugins
docker exec wordpress-dev wp plugin install woocommerce advanced-woo-search ti-woocommerce-wishlist polylang redis-cache --activate --allow-root

# Or use the make command
make install-theme-deps-dev

# Check plugin status
docker exec wordpress-dev wp plugin list --allow-root

# Switch to a default theme temporarily
docker exec wordpress-dev wp theme activate twentytwentyfive --allow-root
```

### 3. Database Issues

**Problem**: Database connection failures or corruption

**Symptoms**:
```
Error establishing a database connection
Database server is not responding
ERROR 1045 (28000): Access denied for user 'root'@'localhost'
```

**Solutions**:
```bash
# Check MySQL container status
docker ps | grep mysql

# Check MySQL logs
docker logs mysql-dev --tail 20

# Test database connection (development)
docker exec mysql-dev mysql -u root -proot_password -e "SHOW DATABASES;"

# Test database connection (production)
docker exec mysql-prod mysql -u root -p'your_secure_root_password_here' -e "SHOW DATABASES;"

# Restart MySQL container
docker restart mysql-dev  # or mysql-prod

# If data is corrupted, restore from backup
make restore BACKUP=your_backup_file.tar.gz
```

**Backup Issues**:
```bash
# If backup fails with "Access denied"
# Check that .env file exists and has correct passwords
cat .env | grep MYSQL_ROOT_PASSWORD

# Test backup manually
make backup-dev   # Should work with hardcoded password
make backup-prod  # Requires .env file with correct password
```

### 4. Redis Cache Issues

**Problem**: Redis not working or connection errors

**Symptoms**:
```
Redis connection failed
Object cache not working
```

**Solutions**:
```bash
# Test Redis connectivity
make test-redis

# Check Redis container
docker ps | grep redis

# Test Redis manually
docker exec redis-dev redis-cli ping

# Restart Redis
docker restart redis-dev

# Disable Redis cache temporarily
docker exec wordpress-dev wp redis disable --allow-root

# Re-enable Redis cache
docker exec wordpress-dev wp redis enable --allow-root
```

### 5. SSL/HTTPS Issues

**Problem**: SSL certificate errors or HTTPS not working

**Symptoms**:
```
SSL certificate problem
NET::ERR_CERT_AUTHORITY_INVALID
```

**Solutions**:
```bash
# Generate new SSL certificates
make ssl

# Check certificate files exist
ls -la ssl/

# Verify certificate validity
openssl x509 -in ssl/cert.pem -text -noout

# For production, use Let's Encrypt
# Install certbot and generate real certificates
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

### 6. File Permission Issues

**Problem**: WordPress can't write files or upload media

**Symptoms**:
```
Unable to create directory
Permission denied
```

**Solutions**:
```bash
# Fix WordPress file permissions
sudo chown -R www-data:www-data wordpress/
sudo find wordpress/ -type d -exec chmod 755 {} \;
sudo find wordpress/ -type f -exec chmod 644 {} \;

# Or use the make command
make permissions-fix

# For wp-config.php
sudo chmod 600 wordpress/wp-config.php
```

### 7. Memory and Performance Issues

**Problem**: WordPress running slowly or out of memory

**Symptoms**:
```
Fatal error: Allowed memory size exhausted
Site loading very slowly
```

**Solutions**:
```bash
# Check container resource usage
docker stats

# Increase PHP memory limit
# Edit php-config/php-dev.ini or php-config/php-prod.ini
memory_limit = 512M

# Rebuild containers
make dev-build && make dev

# Clear all caches
make cache-clear-dev

# Optimize database
docker exec wordpress-dev wp db optimize --allow-root
```

### 8. Docker Issues

**Problem**: Docker containers not starting or behaving unexpectedly

**Symptoms**:
```
Container exits immediately
Docker daemon not responding
```

**Solutions**:
```bash
# Check Docker daemon status
sudo systemctl status docker

# Restart Docker daemon
sudo systemctl restart docker

# Clean up Docker resources
make clean

# Remove all containers and start fresh
docker stop $(docker ps -q)
docker rm $(docker ps -aq)
make dev

# Check disk space
df -h
docker system df

# Prune unused resources
docker system prune -a
```

## 🔍 Diagnostic Commands

### System Health Check
```bash
# Overall system status
make monitor

# Check environment configuration
make check-env

# Test all services
make health-check

# View recent logs
make logs
```

### Detailed Diagnostics
```bash
# Container status
docker ps -a

# Resource usage
docker stats --no-stream

# Network connectivity
docker network ls
docker network inspect pinkangel-docker_wordpress-dev-network

# Volume information
docker volume ls
docker volume inspect pinkangel-docker_mysql_dev_data
```

### WordPress Diagnostics
```bash
# WordPress status
docker exec wordpress-dev wp core version --allow-root
docker exec wordpress-dev wp core check-update --allow-root

# Plugin status
docker exec wordpress-dev wp plugin list --allow-root

# Theme status
docker exec wordpress-dev wp theme list --allow-root

# Database status
docker exec wordpress-dev wp db check --allow-root

# Cache status
docker exec wordpress-dev wp redis status --allow-root
```

## 🛠️ Advanced Troubleshooting

### Debug Mode
Enable WordPress debug mode for detailed error information:

```bash
# Edit wp-config.php
docker exec wordpress-dev wp config set WP_DEBUG true --allow-root
docker exec wordpress-dev wp config set WP_DEBUG_LOG true --allow-root
docker exec wordpress-dev wp config set WP_DEBUG_DISPLAY false --allow-root

# View debug log
docker exec wordpress-dev tail -f /var/www/html/wp-content/debug.log
```

### Database Debugging
```bash
# Check database tables
docker exec mysql-dev mysql -u root -proot_password -e "USE pinkangel; SHOW TABLES;"

# Check WordPress options
docker exec mysql-dev mysql -u root -proot_password -e "USE pinkangel; SELECT option_name, option_value FROM wp_options WHERE option_name IN ('siteurl', 'home');"

# Check for corrupted tables
docker exec mysql-dev mysql -u root -proot_password -e "USE pinkangel; CHECK TABLE wp_posts, wp_options;"
```

### Network Debugging
```bash
# Test connectivity between containers
docker exec wordpress-dev ping mysql-dev
docker exec wordpress-dev ping redis-dev

# Check DNS resolution
docker exec wordpress-dev nslookup mysql-dev
```

## 📋 Prevention Tips

### Regular Maintenance
```bash
# Weekly tasks
make backup-prod
make update-dev
make cache-clear-dev

# Monthly tasks
make security-scan
docker system prune
```

### Monitoring Setup
```bash
# Set up log rotation
# Add to crontab:
0 2 * * * docker system prune -f

# Monitor disk space
df -h | grep -E '(8[0-9]|9[0-9])%'
```

### Best Practices
1. **Always backup before major changes**
2. **Test in development first**
3. **Keep plugins and themes updated**
4. **Monitor resource usage regularly**
5. **Use version control for custom code**

## 🆘 When All Else Fails

### Nuclear Option (Fresh Start)
```bash
# DANGER: This removes everything!
make stop
docker system prune -a --volumes
rm -rf wordpress/
git checkout -- .
make dev
```

### Get Help
1. **Check logs**: `make logs`
2. **Review documentation**: [docs/README.md](README.md)
3. **Search issues**: Check GitHub issues
4. **Ask for help**: Provide logs and error messages

---

## 🔗 URL Redirect Issues

### **Problem**: WordPress redirects to live site after database import

**Symptoms**:
- Visiting http://localhost:8080 redirects to https://pinkangel.bg
- Can't access local development site
- WordPress admin redirects to live site

**Solutions**:
```bash
# Quick fix (recommended)
make quick-url-fix

# Comprehensive fix with content replacement
make fix-urls

# Manual fix
docker exec wordpress-dev wp option update siteurl 'http://localhost:8080' --allow-root
docker exec wordpress-dev wp option update home 'http://localhost:8080' --allow-root
docker exec wordpress-dev wp cache flush --allow-root

# Clear browser cache
# Press Ctrl+Shift+R (or Cmd+Shift+R on Mac) to hard refresh
```

**Prevention**:
- The `import-original-db` script now automatically fixes URLs
- Always run URL fix after manual database imports

---

**Still having issues?** Check the [FAQ](FAQ.md) or review the [Commands Reference](COMMANDS.md).
