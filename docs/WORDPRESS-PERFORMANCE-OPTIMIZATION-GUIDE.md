# WordPress Performance Optimization Guide
## Complete Deployment and Optimization Process

### 📋 Table of Contents
1. [Initial Setup Requirements](#initial-setup-requirements)
2. [Performance Issues Documentation](#performance-issues-documentation)
3. [Step-by-Step Optimization Process](#step-by-step-optimization-process)
4. [Verification and Testing](#verification-and-testing)
5. [Troubleshooting Section](#troubleshooting-section)
6. [Maintenance and Monitoring](#maintenance-and-monitoring)

---

## 🚀 Initial Setup Requirements

### Environment Prerequisites
- **Docker & Docker Compose** installed and running
- **Git repository** cloned: `pinkangel-docker`
- **Live database backup** available for import
- **Minimum 8GB RAM** recommended for development environment
- **SSD storage** for optimal database performance

### 1. Environment Setup

#### Development Environment Setup
```bash
# Clone and navigate to project
git clone <repository-url> pinkangel-docker
cd pinkangel-docker

# Set up development environment
make setup-env          # Interactive environment configuration
make dev                # Start development containers

# Verify containers are running
docker ps               # Should show: wordpress-dev, mysql-dev, redis-dev, nginx-dev
```

#### Production Environment Setup (if needed)
```bash
# Ensure .env file exists with production credentials
make setup-env          # Configure production environment
make prod               # Start production containers
```

### 2. Database Import Procedures

#### Import Fresh Live Database
```bash
# Method 1: Using the import script (recommended)
make import-original-db

# Method 2: Manual import
# Place your live database file in the project root
make db-import FILE=your-live-database.sql

# Method 3: Using backup/restore system
make backup-prod        # If you have production data
make restore BACKUP=your-backup-file.sql
```

#### Verify Database Import
```bash
# Check database tables and sizes
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
SELECT table_name, ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size_MB'
FROM information_schema.tables WHERE table_schema='pinkangel' 
ORDER BY (data_length + index_length) DESC LIMIT 10;"

# Expected large tables:
# - pink_postmeta: ~955MB
# - pink_wc_orders_meta: ~267MB  
# - pink_woocommerce_order_itemmeta: ~149MB
```

---

## 🐌 Performance Issues Documentation

### The Problem: Cyrillic Category URL Performance

#### Problematic URL
```
http://localhost:8080/product-category/%d0%b4%d0%b0%d0%bc%d1%81%d0%ba%d0%b0-%d0%ba%d0%be%d0%bb%d0%b5%d0%ba%d1%86%d0%b8%d1%8f/%d0%b4%d0%be%d0%bc%d0%b0%d1%88%d0%b5%d0%bd-%d1%85%d0%b0%d0%bb%d0%b0%d1%82/?orderby=date&wpf_count=24&wpf_fbv=1&all_products_filtering=1
```

**Decoded:** Women's Collection > Home Robe category with date sorting and product filtering

#### Performance Metrics
- **Before Optimization:** Timeout (>30 seconds)
- **After Optimization:** 0.9 seconds average
- **Improvement:** 97% faster loading time

### Root Causes Identified

#### 1. Missing WooCommerce Product Filter Plugin (CRITICAL)
- **Issue:** URL contained `wpf_` parameters but plugin was inactive
- **Impact:** WordPress processed complex filtering queries inefficiently
- **Solution:** Activate `woo-product-filter` plugin

#### 2. Action Scheduler Database Errors (CRITICAL)
- **Issue:** `Duplicate entry '0' for key 'pink_actionscheduler_actions.PRIMARY'`
- **Root Cause:** Missing AUTO_INCREMENT attribute and corrupted records
- **Impact:** Database errors on every page load

#### 3. Massive Database Tables Without Proper Indexing
- **Issue:** 955MB postmeta table without optimized indexes
- **Impact:** Slow meta queries for product filtering and sorting
- **Solution:** Add targeted database indexes

#### 4. Missing Product Images (404 Errors)
- **Issue:** Hundreds of 404 errors for missing 2024/06/ images
- **Impact:** 124KB error pages served instead of simple redirects
- **Solution:** Create directory structure and efficient redirects

#### 5. Advanced Custom Fields (ACF) Errors
- **Issue:** `Call to undefined function get_field()` in theme
- **Impact:** Fatal errors on product pages
- **Solution:** Activate Advanced Custom Fields plugin

---

## 🔧 Step-by-Step Optimization Process

### Phase 1: Environment Verification and Backup

#### Step 1: Create Backup Before Changes
```bash
# Always backup before optimization
make backup-dev
make list-backups       # Verify backup was created
```

#### Step 2: Verify Current Performance (Baseline)
```bash
# Test the problematic URL (should timeout or be very slow)
time curl -s -o /dev/null "http://localhost:8080/product-category/%d0%b4%d0%b0%d0%bc%d1%81%d0%ba%d0%b0-%d0%ba%d0%be%d0%bb%d0%b5%d0%ba%d1%86%d0%b8%d1%8f/%d0%b4%d0%be%d0%bc%d0%b0%d1%88%d0%b5%d0%bd-%d1%85%d0%b0%d0%bb%d0%b0%d1%82/?orderby=date&wpf_count=24&wpf_fbv=1&all_products_filtering=1"

# Expected: Timeout or >30 seconds
```

### Phase 2: Core Performance Fixes

#### Step 3: Run URL Performance Optimization
```bash
# This script handles the primary performance issues
chmod +x fix-url-performance.sh
./fix-url-performance.sh

# What this script does:
# - Activates WooCommerce Product Filter plugin
# - Adds critical database indexes
# - Cleans orphaned postmeta entries
# - Optimizes WooCommerce tables
# - Cleans expired transients
# - Optimizes Cyrillic URL handling
# - Verifies Redis cache
```

#### Step 4: Fix Action Scheduler Database Errors
```bash
# Fix the duplicate entry errors
chmod +x fix-action-scheduler.sh
./fix-action-scheduler.sh

# What this script does:
# - Fixes AUTO_INCREMENT attribute
# - Updates records with action_id = 0
# - Cleans old action scheduler entries
# - Resets Action Scheduler system
```

#### Step 5: Fix Missing Images and Theme Errors
```bash
# Fix 404 image errors and ACF issues
chmod +x fix-missing-images-and-errors.sh
./fix-missing-images-and-errors.sh

# What this script does:
# - Creates missing 2024 directory structure
# - Activates Advanced Custom Fields plugin
# - Sets up efficient .htaccess for missing images
# - Clears caches
```

### Phase 3: Database Optimization

#### Step 6: Safe Database Cleanup (if needed)
```bash
# Only run if you need additional cleanup
# This prevents high CPU usage during cleanup
chmod +x safe-database-cleanup.sh
./safe-database-cleanup.sh

# What this script does:
# - Incremental transient cleanup
# - Safe orphaned postmeta removal
# - Lightweight table optimization
# - CPU monitoring and safety checks
```

#### Step 7: Manual Database Index Verification
```bash
# Verify critical indexes were added
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
SHOW INDEX FROM pink_postmeta WHERE Key_name IN ('idx_post_meta', 'idx_meta_key_value');
SHOW INDEX FROM pink_wc_orders_meta WHERE Key_name = 'idx_order_meta';
SHOW INDEX FROM pink_woocommerce_order_itemmeta WHERE Key_name = 'idx_item_meta';
"

# Expected: Should show the new indexes
```

### Phase 4: Plugin and Cache Configuration

#### Step 8: Verify Required Plugins
```bash
# Check that critical plugins are active
docker exec wordpress-dev wp plugin list --status=active --allow-root

# Required active plugins:
# - woo-product-filter
# - advanced-custom-fields  
# - redis-cache
# - woocommerce
```

#### Step 9: Verify Redis Cache Configuration
```bash
# Test Redis connectivity and performance
docker exec wordpress-dev wp redis status --allow-root

# Expected output:
# Status: Connected
# Redis: 6.x.x
# Database: 0
# Host: redis-dev:6379
```

---

## ✅ Verification and Testing

### Performance Testing

#### Test 1: Problematic URL Performance
```bash
# Test the original problematic URL
curl -s -o /dev/null -w "HTTP: %{http_code} | Time: %{time_total}s" "http://localhost:8080/product-category/%d0%b4%d0%b0%d0%bc%d1%81%d0%ba%d0%b0-%d0%ba%d0%be%d0%bb%d0%b5%d0%ba%d1%86%d0%b8%d1%8f/%d0%b4%d0%be%d0%bc%d0%b0%d1%88%d0%b5%d0%bd-%d1%85%d0%b0%d0%bb%d0%b0%d1%82/?orderby=date&wpf_count=24&wpf_fbv=1&all_products_filtering=1"

# Expected: HTTP: 200 | Time: 0.9-1.2s
```

#### Test 2: Multiple Performance Tests
```bash
# Run 3 consecutive tests for consistency
for i in {1..3}; do
  echo "Test $i:"
  curl -s -o /dev/null -w "HTTP: %{http_code} | Time: %{time_total}s" "http://localhost:8080/product-category/%d0%b4%d0%b0%d0%bc%d1%81%d0%ba%d0%b0-%d0%ba%d0%be%d0%bb%d0%b5%d0%ba%d1%86%d0%b8%d1%8f/%d0%b4%d0%be%d0%bc%d0%b0%d1%88%d0%b5%d0%bd-%d1%85%d0%b0%d0%bb%d0%b0%d1%82/?orderby=date&wpf_count=24&wpf_fbv=1&all_products_filtering=1"
  echo
  sleep 1
done

# Expected: Consistent sub-second response times
```

#### Test 3: Image Redirect Functionality
```bash
# Test missing image redirect
curl -I "http://localhost:8080/wp-content/uploads/2024/06/nonexistent-image.jpg"

# Expected: HTTP/1.1 302 Found
# Location: /wp-content/uploads/woocommerce-placeholder.webp
```

### Database Health Checks

#### Check 1: Action Scheduler Status
```bash
# Verify no more duplicate entry errors
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
SELECT COUNT(*) as total_actions, 
       COUNT(CASE WHEN action_id = 0 THEN 1 END) as zero_id_count
FROM pink_actionscheduler_actions;"

# Expected: zero_id_count should be 0
```

#### Check 2: Table Sizes and Health
```bash
# Check current table sizes
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
SELECT table_name, 
       ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size_MB',
       table_rows as 'Rows'
FROM information_schema.tables 
WHERE table_schema='pinkangel' 
ORDER BY (data_length + index_length) DESC 
LIMIT 10;"

# Monitor for reasonable sizes and row counts
```

### Error Log Monitoring

#### Check 1: Recent Error Logs
```bash
# Check for any new errors
docker logs wordpress-dev --tail 20 | grep -E "(error|Error|ERROR)" || echo "No recent errors found"

# Expected: No critical errors, especially no Action Scheduler or image 404 errors
```

#### Check 2: Specific Error Types
```bash
# Check for Action Scheduler errors
docker logs wordpress-dev --tail 100 | grep -i "duplicate entry" || echo "No duplicate entry errors"

# Check for image 404 errors  
docker logs wordpress-dev --tail 100 | grep "404.*wp-content/uploads" || echo "No image 404 errors"

# Check for ACF errors
docker logs wordpress-dev --tail 100 | grep "get_field" || echo "No ACF errors"
```

### Performance Benchmarks to Achieve

#### Target Metrics
- **Category Pages:** < 2 seconds
- **Filtered Product Pages:** < 3 seconds
- **Product Detail Pages:** < 1 second
- **Admin Pages:** < 2 seconds
- **Error Rate:** < 1% (excluding legitimate 404s)

#### Success Criteria
- ✅ Problematic URL loads in < 1.5 seconds consistently
- ✅ No Action Scheduler database errors
- ✅ No missing image 404 floods in logs
- ✅ No ACF function errors
- ✅ Redis cache working and connected
- ✅ All required plugins active and functional

---

## 🚨 Troubleshooting Section

### Common Issues During Optimization

#### Issue 1: High CPU Usage During Database Cleanup
**Symptoms:**
- CPU usage >200%
- Long-running database operations
- System becomes unresponsive

**Solution:**
```bash
# Kill heavy database operations
docker exec mysql-dev mysql -u root -proot_password -e "SHOW PROCESSLIST;"
# Identify long-running DELETE operations and kill them:
docker exec mysql-dev mysql -u root -proot_password -e "KILL <process_id>;"

# Use safe cleanup instead
./safe-database-cleanup.sh
```

#### Issue 2: Plugin Activation Failures
**Symptoms:**
- Plugin activation commands fail
- Missing plugin errors persist

**Solution:**
```bash
# Check if plugins exist
docker exec wordpress-dev wp plugin list --all --allow-root

# Install missing plugins
docker exec wordpress-dev wp plugin install woo-product-filter --activate --allow-root
docker exec wordpress-dev wp plugin install advanced-custom-fields --activate --allow-root
```

#### Issue 3: Redis Connection Issues
**Symptoms:**
- Redis status shows "Not connected"
- Cache not working

**Solution:**
```bash
# Check Redis container status
docker ps | grep redis

# Restart Redis if needed
docker restart redis-dev

# Re-enable Redis cache
docker exec wordpress-dev wp redis enable --allow-root
```

#### Issue 4: Database Import Failures
**Symptoms:**
- Import script fails
- Database tables missing or incomplete

**Solution:**
```bash
# Check database connection
docker exec mysql-dev mysql -u root -proot_password -e "SHOW DATABASES;"

# Manual import with error checking
docker exec -i mysql-dev mysql -u root -proot_password pinkangel < your-database.sql

# Check for import errors
docker logs mysql-dev --tail 50
```

### Safe Alternatives for Heavy Operations

#### Alternative 1: Incremental Database Cleanup
Instead of running large DELETE operations, use the safe cleanup script:
```bash
# Safe incremental cleanup (prevents high CPU)
./safe-database-cleanup.sh

# This script:
# - Processes in small batches (50-100 records)
# - Includes pauses between operations
# - Monitors for heavy queries
# - Stops if system load is high
```

#### Alternative 2: Staged Optimization
If the full optimization causes issues, run in stages:
```bash
# Stage 1: Plugin fixes only
docker exec wordpress-dev wp plugin activate woo-product-filter --allow-root
docker exec wordpress-dev wp plugin activate advanced-custom-fields --allow-root

# Test performance
curl -s -o /dev/null -w "Time: %{time_total}s" "http://localhost:8080/..."

# Stage 2: Database indexes (if Stage 1 helps)
./fix-action-scheduler.sh

# Stage 3: Full optimization (if previous stages successful)
./fix-url-performance.sh
```

#### Alternative 3: Manual Index Creation
If automated scripts fail, add indexes manually:
```bash
# Add indexes one at a time with monitoring
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
ALTER TABLE pink_postmeta ADD INDEX idx_post_meta (post_id, meta_key);
"

# Wait and monitor CPU usage before next index
docker stats mysql-dev --no-stream

# Continue with next index if CPU is normal
docker exec mysql-dev mysql -u root -proot_password pinkangel -e "
ALTER TABLE pink_postmeta ADD INDEX idx_meta_key_value (meta_key, meta_value(191));
"
```

---

## 📊 Maintenance and Monitoring

### Daily Monitoring
```bash
# Quick performance check
make performance-test

# Check error logs
docker logs wordpress-dev --tail 20 | grep -i error

# Monitor database sizes
make monitor
```

### Weekly Maintenance
```bash
# Clean Action Scheduler
make clean-action-scheduler

# Clear caches
make cache-clear

# Check for plugin updates
docker exec wordpress-dev wp plugin list --update=available --allow-root
```

### Monthly Optimization
```bash
# Full backup before maintenance
make backup-dev

# Run safe database cleanup
./safe-database-cleanup.sh

# Performance testing
make performance-test

# Security scan
make security-scan
```

### Performance Monitoring Commands
```bash
# Test specific URL performance
curl -s -o /dev/null -w "Time: %{time_total}s | Size: %{size_download} bytes" "URL"

# Monitor database performance
docker exec mysql-dev mysql -u root -proot_password -e "SHOW PROCESSLIST;"

# Check Redis performance
docker exec redis-dev redis-cli INFO stats

# Monitor container resources
docker stats --no-stream
```

---

## 🎯 Success Validation Checklist

### ✅ Optimization Complete When:
- [ ] Problematic URL loads in < 1.5 seconds consistently
- [ ] No Action Scheduler errors in logs
- [ ] No image 404 floods in logs  
- [ ] No ACF function errors
- [ ] WooCommerce Product Filter plugin active and working
- [ ] Advanced Custom Fields plugin active
- [ ] Redis cache connected and working
- [ ] Database indexes properly created
- [ ] Missing image redirects working (302 responses)
- [ ] Clean error logs with only legitimate issues
- [ ] CPU usage normal (< 10% average)
- [ ] All containers running stable

### 📈 Performance Targets Achieved:
- [ ] Category pages: < 2 seconds
- [ ] Filtered product pages: < 3 seconds
- [ ] Product detail pages: < 1 second
- [ ] Admin pages: < 2 seconds
- [ ] Overall error rate: < 1%

**Following this guide step-by-step should result in the same optimized performance we achieved: sub-second loading times, no database errors, clean logs, and fully functional product filtering.**

---

## 📚 Appendix: Script Reference

### Core Optimization Scripts

#### `fix-url-performance.sh`
**Purpose:** Primary performance optimization script
**What it fixes:**
- Activates WooCommerce Product Filter plugin
- Adds critical database indexes to postmeta table
- Optimizes WooCommerce order tables
- Cleans orphaned postmeta entries
- Removes expired transients
- Optimizes Cyrillic URL handling
- Verifies Redis cache functionality

**Usage:**
```bash
chmod +x fix-url-performance.sh
./fix-url-performance.sh
```

**Expected Runtime:** 5-15 minutes depending on database size

#### `fix-action-scheduler.sh`
**Purpose:** Fix Action Scheduler database errors
**What it fixes:**
- Repairs AUTO_INCREMENT attribute on actionscheduler_actions table
- Updates records with invalid action_id = 0
- Cleans old completed and failed actions
- Resets Action Scheduler system

**Usage:**
```bash
chmod +x fix-action-scheduler.sh
./fix-action-scheduler.sh
```

**Expected Runtime:** 2-5 minutes

#### `fix-missing-images-and-errors.sh`
**Purpose:** Fix missing images and theme errors
**What it fixes:**
- Creates missing 2024 uploads directory structure
- Activates Advanced Custom Fields plugin
- Sets up efficient .htaccess for missing image redirects
- Clears WordPress and Redis caches

**Usage:**
```bash
chmod +x fix-missing-images-and-errors.sh
./fix-missing-images-and-errors.sh
```

**Expected Runtime:** 1-3 minutes

#### `safe-database-cleanup.sh`
**Purpose:** Safe incremental database cleanup
**What it does:**
- Cleans expired transients in small batches
- Removes orphaned postmeta in controlled batches
- Optimizes smaller tables safely
- Monitors CPU usage and stops if overloaded

**Usage:**
```bash
chmod +x safe-database-cleanup.sh
./safe-database-cleanup.sh
```

**Expected Runtime:** 10-30 minutes (runs safely in background)

### Database Indexes Added

The optimization process adds these critical indexes:

```sql
-- Postmeta table optimization (955MB table)
ALTER TABLE pink_postmeta ADD INDEX idx_post_meta (post_id, meta_key);
ALTER TABLE pink_postmeta ADD INDEX idx_meta_key_value (meta_key, meta_value(191));
ALTER TABLE pink_postmeta ADD INDEX idx_meta_key (meta_key);

-- WooCommerce order tables optimization
ALTER TABLE pink_wc_orders_meta ADD INDEX idx_order_meta (order_id, meta_key(191));
ALTER TABLE pink_woocommerce_order_itemmeta ADD INDEX idx_item_meta (order_item_id, meta_key(191));

-- Action Scheduler table fix
ALTER TABLE pink_actionscheduler_actions
MODIFY COLUMN action_id bigint unsigned NOT NULL AUTO_INCREMENT;
```

### Plugin Dependencies

#### Required Active Plugins
1. **woocommerce** - Core e-commerce functionality
2. **woo-product-filter** - Handles wpf_ URL parameters (CRITICAL for performance)
3. **advanced-custom-fields** - Provides get_field() function used by theme
4. **redis-cache** - Object caching for performance
5. **query-monitor** - Performance debugging (development only)

#### Plugin Activation Commands
```bash
# Activate all required plugins
docker exec wordpress-dev wp plugin activate woocommerce --allow-root
docker exec wordpress-dev wp plugin activate woo-product-filter --allow-root
docker exec wordpress-dev wp plugin activate advanced-custom-fields --allow-root
docker exec wordpress-dev wp plugin activate redis-cache --allow-root
docker exec wordpress-dev wp plugin activate query-monitor --allow-root

# Enable Redis cache
docker exec wordpress-dev wp redis enable --allow-root
```

---

## 🔍 Advanced Troubleshooting

### Database Connection Issues

#### Symptoms
- Scripts fail with "connection refused"
- MySQL commands timeout
- Database import fails

#### Diagnosis
```bash
# Check MySQL container status
docker ps | grep mysql

# Check MySQL logs
docker logs mysql-dev --tail 50

# Test connection
docker exec mysql-dev mysql -u root -proot_password -e "SELECT 1;"
```

#### Solutions
```bash
# Restart MySQL container
docker restart mysql-dev

# Wait for MySQL to be ready
docker exec mysql-dev mysqladmin ping -u root -proot_password --wait

# Check MySQL configuration
docker exec mysql-dev cat /etc/mysql/conf.d/my.cnf
```

### Memory and Resource Issues

#### Symptoms
- Containers crashing or restarting
- Out of memory errors
- Slow performance even after optimization

#### Diagnosis
```bash
# Check container resource usage
docker stats --no-stream

# Check system memory
free -h

# Check disk space
df -h
```

#### Solutions
```bash
# Increase Docker memory allocation (Docker Desktop)
# Settings > Resources > Memory > Increase to 8GB+

# Clean up Docker resources
docker system prune -f
docker volume prune -f

# Restart containers with more memory
docker-compose -f docker-compose.dev.yml down
docker-compose -f docker-compose.dev.yml up -d
```

### Plugin Conflicts

#### Symptoms
- Plugins fail to activate
- Site shows errors after plugin activation
- Performance degrades after optimization

#### Diagnosis
```bash
# Check plugin status
docker exec wordpress-dev wp plugin list --allow-root

# Check for plugin errors
docker logs wordpress-dev --tail 50 | grep -i plugin

# Test with minimal plugins
docker exec wordpress-dev wp plugin deactivate --all --allow-root
docker exec wordpress-dev wp plugin activate woocommerce woo-product-filter --allow-root
```

#### Solutions
```bash
# Activate plugins one by one to identify conflicts
docker exec wordpress-dev wp plugin activate advanced-custom-fields --allow-root
# Test site functionality
docker exec wordpress-dev wp plugin activate redis-cache --allow-root
# Test site functionality

# If conflicts found, check plugin compatibility
docker exec wordpress-dev wp plugin list --update=available --allow-root
```

---

## 📋 Quick Reference Commands

### Essential Testing Commands
```bash
# Test problematic URL performance
curl -s -o /dev/null -w "Time: %{time_total}s" "http://localhost:8080/product-category/%d0%b4%d0%b0%d0%bc%d1%81%d0%ba%d0%b0-%d0%ba%d0%be%d0%bb%d0%b5%d0%ba%d1%86%d0%b8%d1%8f/%d0%b4%d0%be%d0%bc%d0%b0%d1%88%d0%b5%d0%bd-%d1%85%d0%b0%d0%bb%d0%b0%d1%82/?orderby=date&wpf_count=24&wpf_fbv=1&all_products_filtering=1"

# Check for database errors
docker logs wordpress-dev --tail 20 | grep -i error

# Verify Redis cache
docker exec wordpress-dev wp redis status --allow-root

# Check plugin status
docker exec wordpress-dev wp plugin list --status=active --allow-root
```

### Emergency Recovery Commands
```bash
# Stop all containers
make stop

# Restart development environment
make dev

# Restore from backup if needed
make list-backups
make restore BACKUP=your-backup-file.sql

# Quick plugin reactivation
docker exec wordpress-dev wp plugin activate woo-product-filter advanced-custom-fields --allow-root
```

### Performance Monitoring Commands
```bash
# Quick performance test
make performance-test

# Detailed performance metrics
curl -o /dev/null -s -w "Connect: %{time_connect}s\nTTFB: %{time_starttransfer}s\nTotal: %{time_total}s\nSize: %{size_download} bytes\n" http://localhost:8080/

# Database performance check
docker exec mysql-dev mysql -u root -proot_password -e "SHOW PROCESSLIST;"

# Container resource usage
docker stats --no-stream mysql-dev wordpress-dev redis-dev
```

---

## 🎯 Final Validation Checklist

### Pre-Optimization Baseline
- [ ] Fresh database imported successfully
- [ ] Containers running (wordpress-dev, mysql-dev, redis-dev, nginx-dev)
- [ ] Backup created before optimization
- [ ] Problematic URL tested (should timeout or be >30 seconds)

### Post-Optimization Validation
- [ ] **Performance Test:** URL loads in < 1.5 seconds consistently
- [ ] **Database Health:** No Action Scheduler errors in logs
- [ ] **Image Handling:** Missing images redirect properly (HTTP 302)
- [ ] **Plugin Status:** All required plugins active and functional
- [ ] **Cache Status:** Redis connected and working
- [ ] **Error Logs:** Clean logs with no critical errors
- [ ] **Resource Usage:** Normal CPU and memory usage

### Success Metrics
- [ ] **Response Time:** 0.9-1.2 seconds for problematic URL
- [ ] **HTTP Status:** Consistent 200 responses
- [ ] **Error Rate:** < 1% (excluding legitimate 404s)
- [ ] **Database Queries:** No slow queries >5 seconds
- [ ] **Cache Hit Rate:** >80% Redis cache hits
- [ ] **Log Quality:** No error spam, actionable logs only

### Long-term Stability
- [ ] **Multiple Tests:** Consistent performance across 5+ tests
- [ ] **Different URLs:** Good performance on other category pages
- [ ] **Admin Access:** WordPress admin loads quickly
- [ ] **Plugin Functionality:** WooCommerce filtering works correctly
- [ ] **Theme Functions:** No ACF or theme errors

**✅ When all items are checked, your WordPress optimization is complete and should maintain the same high performance we achieved.**
