# Deployment Checklist

## Pre-Deployment

- [ ] Ensure `.env` file is properly configured with production values
- [ ] Verify SSL certificates are in place (`ssl-proxy/` directory)
- [ ] Backup current production data if applicable
- [ ] Test the deployment in a staging environment

## Deployment Steps

### 1. Deploy the Stack
```bash
make run-prod
```

### 2. Wait for Services to Start
```bash
# Wait 30-60 seconds for all containers to be ready
docker ps  # Verify all containers are running
```

### 3. Import Database (if needed)
```bash
# If restoring from backup
make restore BACKUP=your-backup-file.sql

# Or if importing original database
make import-original-db
```

### 4. **CRITICAL: Fix WooCommerce Analytics**
```bash
make fix-woocommerce-analytics
```

### 5. Verify Deployment
- [ ] Website loads: `https://your-domain.com`
- [ ] WordPress admin accessible: `https://your-domain.com/wp-admin`
- [ ] WooCommerce analytics working: `WooCommerce → Analytics`
- [ ] phpMyAdmin accessible: `http://your-domain.com:8081`

## Post-Deployment Verification

### Website Functionality
- [ ] Homepage loads correctly
- [ ] Product pages display properly
- [ ] Shopping cart works
- [ ] Checkout process functions
- [ ] User registration/login works

### WooCommerce Analytics
- [ ] Analytics dashboard loads without errors
- [ ] Order reports show data
- [ ] Revenue reports display correctly
- [ ] Product analytics work
- [ ] Customer reports function

### Performance
- [ ] Page load times are acceptable
- [ ] Images load properly
- [ ] No 404 errors on critical pages
- [ ] SSL certificate is valid

### Admin Functions
- [ ] WordPress admin dashboard accessible
- [ ] WooCommerce settings can be modified
- [ ] Plugins are active and functioning
- [ ] Media uploads work

## Troubleshooting

### If WooCommerce Analytics Don't Work
1. **Run the fix script again:**
   ```bash
   make fix-woocommerce-analytics
   ```

2. **Check container logs:**
   ```bash
   make logs-prod
   ```

3. **Verify database connection:**
   ```bash
   docker exec -it wordpress-prod wp db check --allow-root
   ```

### If Website Doesn't Load
1. **Check container status:**
   ```bash
   docker ps
   ```

2. **Check nginx configuration:**
   ```bash
   docker exec -it nginx-prod nginx -t
   ```

3. **Check reverse proxy:**
   ```bash
   make restart-proxy
   ```

### If Database Issues
1. **Check MySQL container:**
   ```bash
   docker logs mysql-prod
   ```

2. **Test database connection:**
   ```bash
   docker exec -it mysql-prod mysql -u root -p
   ```

## Important Notes

### WooCommerce Analytics Fix
**This step is CRITICAL** - Always run `make fix-woocommerce-analytics` after deployment with a production database. This script:

- Creates missing WooCommerce analytics tables
- Enables analytics features
- Configures privacy settings
- Syncs existing order data
- Verifies everything is working

### Security Considerations
- External communications are disabled by default (privacy-focused)
- Marketplace suggestions are disabled
- Tracking is disabled
- Only essential WooCommerce features are enabled

### Backup Strategy
- Always backup before major deployments
- Test restore procedures regularly
- Keep multiple backup copies
- Document backup/restore procedures

## Emergency Rollback

If deployment fails:

1. **Stop the new deployment:**
   ```bash
   make stop-prod
   ```

2. **Restore from backup:**
   ```bash
   make restore BACKUP=last-known-good-backup.sql
   ```

3. **Start the previous version:**
   ```bash
   make run-prod
   ```

4. **Verify rollback:**
   - Test website functionality
   - Check admin access
   - Verify data integrity

## Success Criteria

Deployment is successful when:

- [ ] All containers are running and healthy
- [ ] Website loads without errors
- [ ] WordPress admin is accessible
- [ ] WooCommerce analytics display data correctly
- [ ] No critical functionality is broken
- [ ] Performance is acceptable
- [ ] SSL certificates are working
- [ ] All required services are operational

## Contact Information

Document who to contact for:
- **Technical Issues:** [Your technical contact]
- **Business Issues:** [Your business contact]
- **Emergency:** [Emergency contact]

---

**Remember:** Always run `make fix-woocommerce-analytics` after deploying with a production database!
