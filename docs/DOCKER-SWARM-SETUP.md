# Docker Swarm Setup for PinkAngel Production

This document explains how to use Docker Swarm for the PinkAngel production environment while keeping the development environment outside of swarm.

## Overview

The setup provides two production deployment options:

1. **Docker Compose Mode** (default): Traditional docker-compose deployment
2. **Docker Swarm Mode**: Production services run in Docker Swarm for better orchestration

### Key Features

- **Development environment**: Always runs outside of swarm using `docker-compose.dev.yml`
- **Production environment**: Can run in either Docker Compose or Docker Swarm mode
- **Reverse proxy**: Always runs outside of swarm using `docker-compose.proxy.yml`
- **Secrets management**: Docker Swarm uses Docker secrets for sensitive data
- **Service scaling**: Easy horizontal scaling of production services
- **Rolling updates**: Zero-downtime updates for production services

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Docker Host                              │
├─────────────────────────────────────────────────────────────┤
│  Development Environment (Docker Compose)                  │
│  ├── wordpress-dev                                         │
│  ├── mysql-dev                                             │
│  ├── redis-dev                                             │
│  └── phpmyadmin-dev                                        │
├─────────────────────────────────────────────────────────────┤
│  Reverse Proxy (Docker Compose)                            │
│  └── nginx-proxy (reverse-proxy container)                 │
├─────────────────────────────────────────────────────────────┤
│  Production Environment (Docker Swarm)                     │
│  ├── nginx (pinkangel-prod_nginx)                         │
│  ├── wordpress-prod (pinkangel-prod_wordpress-prod)       │
│  ├── mysql-prod (pinkangel-prod_mysql-prod)               │
│  ├── redis-prod (pinkangel-prod_redis-prod)               │
│  ├── phpmyadmin (pinkangel-prod_phpmyadmin)               │
│  ├── memcached (pinkangel-prod_memcached)                 │
│  └── wordpress-cron-prod (pinkangel-prod_wordpress-cron-prod) │
└─────────────────────────────────────────────────────────────┘
```

## Quick Start

### 1. Setup Environment

```bash
# Create .env file (if not already done)
make setup-env

# Start development environment (always outside swarm)
make run-dev
```

### 2. Start Production in Swarm Mode

```bash
# Start production environment in Docker Swarm mode
make swarm-start
```

This command will:
- Initialize Docker Swarm (if not already initialized)
- Create Docker secrets from your .env file
- Build required Docker images (only if they don't exist)
- Start the reverse-proxy container (outside swarm)
- Deploy the production stack to Docker Swarm
- Connect the reverse-proxy to the swarm network

**Performance Optimization**: The `swarm-start` command now checks if the required Docker images already exist. If they do, it skips the building step and goes directly to deployment, making subsequent starts much faster.

### 3. Verify Setup

```bash
# Check swarm stack status
make swarm-status

# View service logs
make swarm-logs SERVICE=wordpress-prod

# Test the setup
make test-wordpress
```

## Available Commands

### Swarm Management

```bash
# Start production in swarm mode (builds only if needed)
make swarm-start

# Force rebuild Docker images
make swarm-build

# Stop swarm production
make swarm-stop

# Check swarm status
make swarm-status

# View service logs
make swarm-logs SERVICE=<service_name>

# Scale a service
make swarm-scale SERVICE=<service_name> REPLICAS=<number>

# Update a service (rolling update)
make swarm-update SERVICE=<service_name>
```

### Individual Swarm Operations

```bash
# Initialize Docker Swarm
make swarm-init

# Create secrets from .env file
make swarm-secrets

# Build Docker images
make swarm-build

# Deploy stack
make swarm-deploy

# Remove stack
make swarm-remove
```

### Available Services

- `nginx` - Nginx web server
- `wordpress-prod` - WordPress application
- `mysql-prod` - MySQL database
- `redis-prod` - Redis cache
- `phpmyadmin` - phpMyAdmin web interface
- `memcached` - Memcached service
- `wordpress-cron-prod` - WordPress cron jobs

## Examples

### Scale WordPress to 2 replicas

```bash
make swarm-scale SERVICE=wordpress-prod REPLICAS=2
```

### View MySQL logs

```bash
make swarm-logs SERVICE=mysql-prod
```

### Update WordPress service (rolling update)

```bash
make swarm-update SERVICE=wordpress-prod
```

### Check overall status

```bash
make swarm-status
```

## Security Features

### Docker Secrets

All sensitive data is stored as Docker secrets:

- Database passwords
- WordPress security keys and salts
- SMTP credentials

Secrets are automatically created from your `.env` file when deploying.

### Network Isolation

- Production services run on an overlay network (`wordpress-prod-network`)
- Development services run on a bridge network (`wordpress-dev-network`)
- Reverse proxy connects to both networks as needed

## Differences from Docker Compose Mode

| Feature | Docker Compose | Docker Swarm |
|---------|----------------|--------------|
| Secrets | Environment variables | Docker secrets |
| Scaling | Manual container management | Built-in service scaling |
| Updates | Stop/start containers | Rolling updates |
| Networks | Bridge networks | Overlay networks |
| Orchestration | Basic | Advanced with health checks |
| Resource limits | Basic | Advanced with reservations |

## Troubleshooting

### Swarm not initialized

```bash
make swarm-init
```

### Services not starting

```bash
# Check service status
make swarm-status

# View service logs
make swarm-logs SERVICE=<service_name>

# Check Docker Swarm status
docker node ls
docker service ls
```

### Secrets issues

```bash
# Recreate secrets
make swarm-secrets

# List existing secrets
docker secret ls
```

### Network connectivity issues

```bash
# Check networks
docker network ls

# Inspect swarm network
docker network inspect pinkangel-prod_wordpress-prod-network

# Check reverse-proxy connections
docker inspect reverse-proxy
```

### Reset everything

```bash
# Stop all environments
make stop-all

# Remove swarm stack
make swarm-remove

# Leave swarm (if needed)
docker swarm leave --force

# Start fresh
make swarm-start
```

## Migration from Docker Compose to Swarm

To migrate from existing Docker Compose production to Swarm:

1. **Backup your data**:
   ```bash
   make backup-prod
   ```

2. **Stop Docker Compose production**:
   ```bash
   docker-compose -f docker-compose.prod.yml down
   ```

3. **Start Swarm production**:
   ```bash
   make swarm-start
   ```

The data volumes will be preserved during the migration.

## Performance Considerations

- **Resource limits**: Swarm mode includes memory limits and reservations
- **Placement constraints**: Services are constrained to manager nodes
- **Health checks**: Built-in health monitoring for services
- **Rolling updates**: Zero-downtime updates for production services

## Monitoring

Use the following commands to monitor your swarm:

```bash
# Overall status
make swarm-status

# Service logs
make swarm-logs SERVICE=<service_name>

# Docker native commands
docker service ls
docker service ps pinkangel-prod_<service_name>
docker node ls
```
