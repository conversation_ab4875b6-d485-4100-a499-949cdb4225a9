# WooCommerce Analytics Deployment Fix

## Overview

When deploying this WordPress/WooCommerce stack with a production database, the WooCommerce analytics may not work properly due to missing configuration and database tables. This document explains the issue and provides an automated fix.

## The Problem

When you deploy the stack with a production database that was created before the analytics features were properly configured, you may encounter:

1. **Missing Analytics Tables** - WooCommerce analytics tables may not exist in the database
2. **Disabled Analytics Features** - Analytics features may be disabled in WordPress options
3. **404 Errors on REST API** - WooCommerce admin REST API endpoints return 404 errors
4. **Empty Analytics Dashboard** - WooCommerce Analytics pages show no data or fail to load

## Root Causes

1. **Database State** - Production database may have been created before analytics were properly set up
2. **Feature Flags** - WooCommerce analytics features are disabled by default in some configurations
3. **Missing Tables** - Analytics tables are created during WooCommerce installation but may be missing
4. **Configuration** - Various WooCommerce admin settings need to be properly configured

## The Solution

We've created an automated fix script that handles all these issues:

### Script Location
```bash
scripts/fix-woocommerce-analytics.sh
```

### What the Script Does

1. **Detects Environment** - Automatically detects if you're running dev or prod
2. **Checks WooCommerce** - Verifies WooCommerce is installed and active
3. **Creates Missing Tables** - Creates all required WooCommerce analytics tables
4. **Enables Analytics Features** - Enables all necessary WooCommerce admin features
5. **Configures Privacy Settings** - Maintains your privacy preferences (disables external communications)
6. **Verifies Tables** - Checks that all required analytics tables exist
7. **Syncs Data** - Runs cron jobs to sync existing order data to analytics tables
8. **Clears Caches** - Clears WordPress and Redis caches
9. **Verifies Functionality** - Confirms analytics are working

## When to Run the Script

### Required Scenarios
Run this script in these situations:

1. **After Initial Deployment** - When deploying the stack for the first time with production database
2. **After Database Import** - When importing a production database that doesn't have analytics configured
3. **After Major WooCommerce Updates** - When WooCommerce updates may have changed analytics requirements
4. **When Analytics Stop Working** - If analytics suddenly stop working after any changes

### Optional Scenarios
You may also run it:

1. **After Plugin Updates** - If WooCommerce or related plugins are updated
2. **Periodic Maintenance** - As part of regular maintenance (safe to run multiple times)

## How to Run the Script

### Method 1: Using Make Command (Recommended)
```bash
make fix-woocommerce-analytics
```

### Method 2: Direct Script Execution
```bash
chmod +x scripts/fix-woocommerce-analytics.sh
./scripts/fix-woocommerce-analytics.sh
```

## Script Output

The script provides detailed output with color-coded status messages:

- 🔵 **[INFO]** - General information and progress updates
- 🟢 **[SUCCESS]** - Successful operations
- 🟡 **[WARNING]** - Non-critical issues that were handled
- 🔴 **[ERROR]** - Critical errors that need attention

### Example Output
```
=========================================
  WooCommerce Analytics Fix
=========================================

[INFO] Detected environment: production (container: wordpress-prod)
[SUCCESS] WooCommerce is active
[INFO] Creating WooCommerce analytics tables...
[SUCCESS] Analytics tables creation completed
[SUCCESS] Analytics feature enabled
[SUCCESS] WooCommerce admin features configured
[SUCCESS] Privacy settings configured (external communications disabled)
[SUCCESS] All analytics tables exist
[INFO] Found 77473 orders in analytics tables
[SUCCESS] Analytics data is available
[SUCCESS] Analytics data sync initiated
[SUCCESS] Caches cleared
[SUCCESS] WooCommerce analytics are enabled and should be working

=========================================
[SUCCESS] WooCommerce Analytics Fix Completed!
=========================================

Next steps:
1. Access your WordPress admin at https://your-domain/wp-admin
2. Navigate to WooCommerce → Analytics
3. Check that reports are loading properly

[INFO] Log file saved to: /tmp/woocommerce-analytics-fix.log
```

## Verification Steps

After running the script, verify that analytics are working:

1. **Access WordPress Admin**
   ```
   https://your-domain/wp-admin
   ```

2. **Navigate to WooCommerce Analytics**
   ```
   WooCommerce → Analytics → Overview
   ```

3. **Check Different Report Types**
   - Orders
   - Revenue
   - Products
   - Categories
   - Customers

4. **Verify REST API Endpoints**
   Test that these endpoints return proper responses (not 404):
   ```
   https://your-domain/wp-json/wc-admin/plugins/active
   https://your-domain/wp-json/wc-admin/options
   ```

## Troubleshooting

### Script Fails to Run
- Ensure Docker containers are running: `docker ps`
- Check container names match expected patterns
- Verify script has execute permissions: `chmod +x scripts/fix-woocommerce-analytics.sh`

### Analytics Still Not Working
1. **Check WordPress Admin Access** - Ensure you can log into WordPress admin
2. **Verify WooCommerce Version** - Ensure you're running a recent version of WooCommerce
3. **Check Error Logs** - Look at WordPress debug logs for specific errors
4. **Run Script Again** - The script is safe to run multiple times

### REST API Still Returns 404
1. **Check Nginx Configuration** - Ensure nginx is properly configured for REST API
2. **Verify Permalinks** - Go to Settings → Permalinks and click "Save Changes"
3. **Check .htaccess** - Ensure WordPress rewrite rules are properly configured

## Privacy and Security

The script maintains your privacy preferences:

- ✅ **Disables marketplace suggestions** (`woocommerce_show_marketplace_suggestions = no`)
- ✅ **Disables tracking** (`woocommerce_allow_tracking = no`)
- ✅ **Prevents external communications** - No data sent to WooCommerce.com
- ✅ **Enables only essential features** - Only enables features needed for analytics

## Integration with Deployment

### Recommended Deployment Workflow

1. **Deploy Stack**
   ```bash
   make run-prod
   ```

2. **Import Database** (if needed)
   ```bash
   make restore BACKUP=your-backup-file
   ```

3. **Fix Analytics**
   ```bash
   make fix-woocommerce-analytics
   ```

4. **Verify Everything Works**
   - Test website functionality
   - Check WooCommerce analytics
   - Verify admin access

### Automation

You can integrate this into your deployment scripts:

```bash
#!/bin/bash
# deployment.sh

# Deploy the stack
make run-prod

# Wait for containers to be ready
sleep 30

# Fix WooCommerce analytics
make fix-woocommerce-analytics

echo "Deployment completed with analytics fix applied"
```

## Log Files

The script creates detailed log files at `/tmp/woocommerce-analytics-fix.log` for debugging purposes. These logs include:

- Timestamp of execution
- All operations performed
- Any errors or warnings encountered
- Verification results

## Support

If you encounter issues with this script:

1. **Check the log file** for detailed error information
2. **Verify prerequisites** (Docker running, WooCommerce active)
3. **Run with verbose output** to see detailed progress
4. **Check container logs** for underlying WordPress/WooCommerce errors

The script is designed to be safe and idempotent - you can run it multiple times without causing issues.
