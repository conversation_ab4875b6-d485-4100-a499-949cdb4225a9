# Git Strategy for WordPress Docker Environment

## 📋 Overview

This document explains what files are tracked in Git and why, following WordPress and Docker best practices.

## 🔒 **IGNORED Folders/Files (Security & Best Practices)**

### `ssl/` - ❌ **NEVER TRACK**
- **Contains**: SSL certificates and private keys
- **Why ignored**: Security risk, environment-specific, regenerable
- **How to recreate**: `make ssl`

### `.env` - ❌ **NEVER TRACK** 
- **Contains**: Production secrets, database passwords, API keys
- **Why ignored**: Security risk, environment-specific
- **Alternative**: Use `.env.example` template

### WordPress Core Files - ❌ **DON'T TRACK**
- **Contains**: `wp-admin/`, `wp-includes/`, core PHP files
- **Why ignored**: Managed by WordPress updates, large files
- **How to recreate**: WordPress installation process

### User-Generated Content - ❌ **DON'T TRACK**
- **Contains**: `uploads/`, `cache/`, `backups/`, `languages/`
- **Why ignored**: Large files, environment-specific, auto-generated
- **How to manage**: Backup/restore scripts

### Third-Party Plugins/Themes - ❌ **DON'T TRACK**
- **Contains**: `plugins/redis-cache/`, `themes/twenty*/`
- **Why ignored**: Managed via WordPress admin or composer
- **How to manage**: Document in README.md

## ✅ **TRACKED Files (Configuration & Custom Code)**

### Infrastructure Configuration
```
✅ docker-compose.*.yml     # Container orchestration
✅ Dockerfile.*             # Container definitions  
✅ nginx-config/            # Web server configuration
✅ mysql-config/            # Database configuration
✅ *.sh                     # Automation scripts
✅ Makefile                 # Build automation
```

### WordPress Configuration
```
✅ wordpress/wp-config.php           # Environment-aware config
✅ wordpress/.htaccess               # URL rewriting rules
✅ wordpress/wp-content/object-cache.php  # Redis cache drop-in
✅ wordpress/wp-content/index.php    # Security protection
✅ wordpress/wp-content/*/index.php  # Directory protection
```

### Documentation & Setup
```
✅ README.md               # Project documentation
✅ .gitignore             # Git ignore rules
✅ GIT-STRATEGY.md        # This file
```

## 🎯 **Custom Development (When You Add Them)**

### Custom Themes
```
✅ wordpress/wp-content/themes/your-theme/     # Your custom theme
❌ wordpress/wp-content/themes/twenty*/       # Default themes
```

### Custom Plugins
```
✅ wordpress/wp-content/plugins/your-plugin/  # Your custom plugin
✅ wordpress/wp-content/mu-plugins/           # Must-use plugins
❌ wordpress/wp-content/plugins/redis-cache/  # Third-party plugins
```

## 🔄 **Workflow Recommendations**

### For New Environments
1. Clone repository
2. Run `make setup-env` to create `.env`
3. Run `make ssl` to generate certificates
4. Run `make prod` or `make dev` to start
5. Install WordPress via web interface

### For Plugin/Theme Management
1. **Third-party plugins**: Install via WordPress admin
2. **Custom plugins**: Develop in `wp-content/plugins/your-plugin/`
3. **Custom themes**: Develop in `wp-content/themes/your-theme/`
4. **Document dependencies** in README.md

### For Database Management
1. Use `make backup-prod` / `make backup-dev`
2. Use `make sync-prod-to-dev` for development
3. Store backups outside repository
4. Never commit database dumps

## 🚨 **Security Reminders**

- ❌ **NEVER commit** `.env` files
- ❌ **NEVER commit** SSL certificates/keys
- ❌ **NEVER commit** database dumps with real data
- ❌ **NEVER commit** `wp-config.php` with hardcoded secrets
- ✅ **ALWAYS use** environment variables for secrets
- ✅ **ALWAYS review** commits before pushing

## 📁 **Directory Structure**

```
pinkangel-docker/
├── 📁 ssl/                    # ❌ Ignored (certificates)
├── 📁 backups/               # ❌ Ignored (database dumps)
├── 📁 wordpress/             # 🔄 Partially tracked
│   ├── 📁 wp-admin/          # ❌ Ignored (core)
│   ├── 📁 wp-includes/       # ❌ Ignored (core)
│   ├── 📁 wp-content/
│   │   ├── 📁 uploads/       # ❌ Ignored (user files)
│   │   ├── 📁 cache/         # ❌ Ignored (cache)
│   │   ├── 📁 languages/     # ❌ Ignored (auto-downloaded)
│   │   ├── 📁 themes/
│   │   │   ├── 📁 twenty*/   # ❌ Ignored (default themes)
│   │   │   └── 📄 index.php  # ✅ Tracked (security)
│   │   ├── 📁 plugins/
│   │   │   ├── 📁 redis-cache/ # ❌ Ignored (third-party)
│   │   │   └── 📄 index.php  # ✅ Tracked (security)
│   │   ├── 📄 object-cache.php # ✅ Tracked (Redis drop-in)
│   │   └── 📄 index.php      # ✅ Tracked (security)
│   ├── 📄 wp-config.php      # ✅ Tracked (configuration)
│   └── 📄 .htaccess          # ✅ Tracked (URL rules)
├── 📄 .env                   # ❌ Ignored (secrets)
├── 📄 .gitignore             # ✅ Tracked
├── 📄 docker-compose.*.yml   # ✅ Tracked
└── 📄 *.sh                   # ✅ Tracked (scripts)
```

This strategy balances security, maintainability, and collaboration while following WordPress and Docker best practices.
