# Reverse Proxy Commands Summary

This document summarizes all available commands for managing the reverse proxy (nginx-proxy) container.

## Available Commands

### Primary Command

#### `make start-proxy`
**Description**: Start the reverse proxy (nginx-proxy) container
**Usage**: `make start-proxy`
**What it does**:
- Uses `scripts/manage-proxy.sh start`
- Includes setup verification and error handling
- Shows helpful status messages
- Recommended command for starting the reverse proxy

### Management Commands

#### `make stop-proxy`
**Description**: Stop the nginx-proxy container
**Usage**: `make stop-proxy`
**What it does**:
- Uses `scripts/manage-proxy.sh stop`
- Gracefully stops the reverse proxy container

#### `make restart-proxy`
**Description**: Restart the reverse proxy container
**Usage**: `make restart-proxy`
**What it does**:
- Uses `scripts/manage-proxy.sh restart`
- Useful for applying configuration changes
- Applies rate limit changes

#### `make status-proxy`
**Description**: Show nginx-proxy container status
**Usage**: `make status-proxy`
**What it does**:
- Shows if the container is running
- Displays port mappings
- Shows network connections

#### `make logs-proxy`
**Description**: Show nginx-proxy container logs
**Usage**: `make logs-proxy`
**What it does**:
- Shows real-time logs from the reverse proxy
- Useful for debugging connection issues

#### `make test-proxy`
**Description**: Test nginx-proxy integration with production environment
**Usage**: `make test-proxy`
**What it does**:
- Runs comprehensive integration tests
- Verifies network connectivity
- Tests proxy functionality

### Legacy Aliases

#### `make proxy`
**Description**: Alias for `start-proxy`
**Usage**: `make proxy`
**Note**: Provided for backward compatibility

#### `make proxy-status`
**Description**: Alias for `status-proxy`
**Usage**: `make proxy-status`

#### `make proxy-logs`
**Description**: Alias for `logs-proxy`
**Usage**: `make proxy-logs`

## Usage Examples

### Basic Usage

```bash
# Start the reverse proxy
make start-proxy

# Check if it's running
make status-proxy

# View logs
make logs-proxy

# Stop the proxy
make stop-proxy
```

### Integration with Environments

```bash
# Start reverse proxy first
make start-proxy

# Then start production (proxy will be auto-connected)
make run-prod

# Or start development (proxy available but not required)
make run-dev
```

### Troubleshooting

```bash
# Test proxy integration
make test-proxy

# Restart proxy to fix issues
make restart-proxy

# Check logs for errors
make logs-proxy

# Check status
make status-proxy
```

## When to Use Each Command

### Use `make start-proxy` when:
- Starting the reverse proxy manually
- You need error handling and status messages
- You want setup verification and helpful output
- General use (recommended command)

## Configuration Files

The reverse proxy uses these configuration files:

- **`docker-compose.proxy.yml`** - Main docker-compose configuration
- **`nginx-proxy-config/nginx.conf`** - Main nginx configuration
- **`nginx-proxy-config/sites/`** - Site-specific configurations
- **`ssl-proxy/`** - SSL certificates for HTTPS

## Networks

The reverse proxy creates and uses:

- **`proxy-network`** - Bridge network for proxy communication
- Connects to **`wordpress-prod-network`** when production is running
- Can connect to **`wordpress-dev-network`** if needed

## Ports

The reverse proxy listens on:

- **Port 80** - HTTP traffic (redirects to HTTPS)
- **Port 443** - HTTPS traffic

## Dependencies

The reverse proxy container:

- **No dependencies** - Can start independently
- **Connects to backends** - Automatically connects to running environments
- **Creates networks** - Creates proxy-network if it doesn't exist

## Integration with Other Commands

### Automatic Proxy Management

These commands automatically manage the proxy:

- `make run-prod` - Ensures proxy is running before starting production
- `make swarm-prod` - Ensures proxy is running before starting swarm
- `make build-prod` - Connects proxy to production network

### Manual Proxy Management

Use these when you want manual control:

- `make run-proxy` - Start proxy manually
- `make stop-proxy` - Stop proxy manually
- `make restart-proxy` - Restart proxy manually

## Best Practices

1. **Start proxy first** when setting up manually:
   ```bash
   make start-proxy
   make run-prod
   ```

2. **Use automatic startup** for convenience:
   ```bash
   make run-prod  # Automatically starts proxy
   ```

3. **Check status** when troubleshooting:
   ```bash
   make status-proxy
   make logs-proxy
   ```

4. **Restart proxy** after configuration changes:
   ```bash
   make restart-proxy
   ```

5. **Test integration** after setup:
   ```bash
   make test-proxy
   ```

## Summary

The reverse proxy commands provide simple and effective management of the nginx-proxy container:

- **`start-proxy`** - Primary command for starting the reverse proxy
- **Management commands** - For status, logs, restart operations
- **Automatic integration** - With production and swarm environments
- **Legacy aliases** - For backward compatibility

The simplified command structure makes it easy to manage the reverse proxy as part of your development and production workflows.
