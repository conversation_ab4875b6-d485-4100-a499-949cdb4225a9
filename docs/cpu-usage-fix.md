# High CPU Usage Fix - Database Operations

## Issue Summary
**Problem:** 200% CPU utilization caused by long-running database cleanup operations
**Root Cause:** Heavy database DELETE operations running for 12+ minutes without completion

## Problematic Operations Identified

### Running Processes That Caused High CPU:
```sql
-- Process 3162 (running for 750 seconds)
DELETE a, b FROM pink_options a, pink_options b
WHERE a.option_name LIKE '_transient_%'

-- Process 3523 (running for 372 seconds) 
DELETE pm FROM pink_postmeta pm
LEFT JOIN pink_posts p ON pm.post_id = p.ID
WHERE p.ID IS NULL
```

### System Impact:
- **CPU Usage:** 200% (should be <10%)
- **Memory Usage:** 1.076GiB / 7.653GiB (normal)
- **Duration:** Operations running for 12+ minutes without completion
- **Table Locks:** Blocking other database operations

## ✅ Resolution Applied

### 1. Immediate Action - Killed Heavy Processes
```bash
# Killed the performance optimization script
kill-process terminal_id:18

# Killed the heavy database operations
docker exec mysql-dev mysql -u root -proot_password -e "KILL 3162; <PERSON><PERSON><PERSON> 3523; KIL<PERSON> 3920;"
```

### 2. Verified System Recovery
- **CPU Usage:** Reduced from 200% to 2.22% ✅
- **URL Performance:** Still working at 1.05 seconds ✅
- **Database:** No blocking operations ✅

### 3. Created Safe Alternative - `safe-database-cleanup.sh`
**Features:**
- Incremental cleanup in small batches (50-100 records)
- CPU monitoring between operations
- Automatic pause if heavy queries detected
- Safe table optimization for smaller tables only
- Progress monitoring and reporting

**Safety Measures:**
```bash
# Small batch sizes to prevent locks
DELETE ... LIMIT 50;  # Instead of unlimited DELETE

# Pauses between operations
sleep 2-5  # Allow system recovery

# Heavy query detection
check_heavy_queries() # Monitor for long-running operations
```

## Current System Status

### ✅ Performance Metrics
- **CPU Usage:** 2.22% (normal)
- **URL Response:** 1.05 seconds (excellent)
- **Database:** No blocking operations
- **Memory:** 1.076GiB / 7.653GiB (normal)

### ✅ Database Health
```
Current MySQL processes:
- event_scheduler: Normal daemon process
- Sleeping connections: Normal idle connections
- No heavy DELETE operations running
```

### ✅ URL Functionality
- HTTP 200 responses
- Sub-second loading times
- No database errors
- Product filtering working

## Lessons Learned

### ❌ What Caused the Issue
1. **Massive Table Operations:** Trying to clean 955MB postmeta table in one operation
2. **No Batch Limits:** DELETE operations without LIMIT clauses
3. **No Progress Monitoring:** Operations running indefinitely
4. **Table Locks:** Large operations blocking other queries

### ✅ Best Practices Applied
1. **Batch Processing:** Small batches (50-100 records) with pauses
2. **Progress Monitoring:** Check for heavy queries between operations
3. **Resource Limits:** Stop operations if system load is high
4. **Incremental Approach:** Multiple small operations instead of one large one

## Safe Database Maintenance

### For Future Cleanup Operations:
```bash
# Use the safe cleanup script
./safe-database-cleanup.sh

# Monitor CPU usage during operations
docker stats mysql-dev --no-stream

# Check for heavy queries
docker exec mysql-dev mysql -u root -proot_password -e "SHOW PROCESSLIST;"
```

### Recommended Schedule:
- **Daily:** Run safe cleanup script during low traffic
- **Weekly:** Monitor table sizes and growth
- **Monthly:** Full database optimization during maintenance window

### Warning Signs to Watch:
- CPU usage >50% for extended periods
- Database queries running >60 seconds
- Table lock timeouts
- Application timeouts

## Files Created

### `safe-database-cleanup.sh`
- Incremental transient cleanup
- Safe orphaned postmeta removal
- Lightweight table optimization
- CPU monitoring and safety checks

### `cpu-usage-fix.md` (this file)
- Documentation of the issue and resolution
- Best practices for database maintenance
- Monitoring guidelines

## Conclusion

The high CPU usage issue was successfully resolved by:
1. **Stopping heavy database operations** that were running indefinitely
2. **Implementing safe alternatives** with batch processing and monitoring
3. **Maintaining system performance** while preserving URL functionality

**System is now stable with normal CPU usage and excellent URL performance.**
