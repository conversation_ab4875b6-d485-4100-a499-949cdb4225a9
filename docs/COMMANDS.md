# Command Reference

Complete reference for all available Make commands and Docker operations.

## 📋 Make Commands Overview

Run `make help` to see all available commands with descriptions.

## 🚀 Environment Management

### Basic Environment Control
```bash
make dev                    # Start development environment
make prod                   # Start production environment  
make stop                   # Stop all environments
make clean                  # Clean up Docker resources
make restart-dev            # Restart development environment
make restart-prod           # Restart production environment
```

### Environment Switching
```bash
make switch-env             # Interactive environment switcher
make check-env              # Check current environment configuration
```

### Build and Setup
```bash
make dev-build              # Build development images
make prod-build             # Build production images
make ssl                    # Generate SSL certificates
make setup-env              # Interactive .env file setup
```

## 🗄️ Database Operations

### Backup and Restore
```bash
make backup-dev             # Backup development database
make backup-prod            # Backup production database
make restore BACKUP=file    # Restore from backup file
```

### Database Synchronization
```bash
make sync-prod-to-dev       # Sync production → development
make sync-dev-to-prod       # Sync development → production (DANGEROUS!)
```

### Database Management
```bash
make db-export-dev          # Export development database
make db-export-prod         # Export production database
make db-import FILE=file    # Import database from file
```

## 🎨 Theme and Plugin Management

### PinkAngel Theme Dependencies
```bash
make install-theme-deps-dev # Install theme plugins (development)
make install-theme-deps-prod # Install theme plugins (production)
```

### WordPress Management
```bash
make wp-cli CMD="command"   # Execute WP-CLI commands
make update-dev             # Update WordPress, plugins, themes (dev)
make update-prod            # Update WordPress, plugins, themes (prod)
```

## 📊 Monitoring and Logs

### System Monitoring
```bash
make monitor                # Show system status and resource usage
make logs                   # Show recent logs from all containers
make logs-dev               # Follow development logs
make logs-prod              # Follow production logs
```

### Performance Testing
```bash
make test-redis             # Test Redis performance and connectivity
make perf-test              # Basic performance test
```

### Health Checks
```bash
make health-check           # Check all services health
make status                 # Show container status
```

## 🔧 Shell Access

### Container Shell Access
```bash
make shell-wp-dev           # WordPress development container shell
make shell-wp-prod          # WordPress production container shell
make shell-mysql-dev        # MySQL development container shell
make shell-mysql-prod       # MySQL production container shell
make shell-redis-dev        # Redis development container shell
make shell-redis-prod       # Redis production container shell
```

## 🛡️ Security and Maintenance

### Security Operations
```bash
make security-scan          # Basic security check
make permissions-fix        # Fix file permissions
```

### Cache Management
```bash
make cache-clear            # Clear all caches (auto-detects environment)
make cache-clear-prod       # Clear ALL possible caches in production (comprehensive)
make test-redis             # Test Redis performance and connectivity
```

#### Comprehensive Production Cache Clearing
The `cache-clear-prod` command clears ALL possible caches in production:
- WordPress core cache
- Redis object cache (with restart)
- Nginx FastCGI cache
- Nginx proxy cache
- PHP OPcache
- Memcached
- Plugin-specific caches (AIOSEO, Advanced Woo Search, WooCommerce)
- WordPress transients
- Reloads Nginx configuration

## 📁 File Operations

### WordPress File Management
```bash
make wp-download            # Download fresh WordPress files
make wp-permissions         # Fix WordPress file permissions
```

## 🔄 Advanced Operations

### Docker Management
```bash
make docker-prune           # Clean up unused Docker resources
make images-update          # Update all Docker images
make volumes-backup         # Backup Docker volumes
```

### Development Tools
```bash
make xdebug-enable          # Enable Xdebug (development)
make xdebug-disable         # Disable Xdebug (development)
make composer-install       # Install Composer dependencies
```

## 📋 Manual Docker Commands

### Development Environment
```bash
# Start development
docker-compose -f docker-compose.dev.yml up -d

# Stop development
docker-compose -f docker-compose.dev.yml down

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Rebuild containers
docker-compose -f docker-compose.dev.yml up -d --build
```

### Production Environment
```bash
# Start production
docker-compose -f docker-compose.prod.yml up -d

# Stop production
docker-compose -f docker-compose.prod.yml down

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Rebuild containers
docker-compose -f docker-compose.prod.yml up -d --build
```

### Individual Container Management
```bash
# WordPress containers
docker exec -it wordpress-dev bash
docker exec -it wordpress-prod bash

# MySQL containers
docker exec -it mysql-dev mysql -u root -p
docker exec -it mysql-prod mysql -u root -p

# Redis containers
docker exec -it redis-dev redis-cli
docker exec -it redis-prod redis-cli
```

## 🎯 WP-CLI Commands

### Plugin Management
```bash
# List plugins
docker exec wordpress-dev wp plugin list --allow-root

# Install plugin
docker exec wordpress-dev wp plugin install plugin-name --activate --allow-root

# Update plugins
docker exec wordpress-dev wp plugin update --all --allow-root
```

### Theme Management
```bash
# List themes
docker exec wordpress-dev wp theme list --allow-root

# Activate theme
docker exec wordpress-dev wp theme activate theme-name --allow-root

# Install theme
docker exec wordpress-dev wp theme install theme-name --activate --allow-root
```

### User Management
```bash
# List users
docker exec wordpress-dev wp user list --allow-root

# Create user
docker exec wordpress-dev wp user <NAME_EMAIL> --role=administrator --allow-root

# Update user password
docker exec wordpress-dev wp user update username --user_pass=newpassword --allow-root
```

### Database Operations
```bash
# Database info
docker exec wordpress-dev wp db check --allow-root

# Search and replace URLs
docker exec wordpress-dev wp search-replace 'old-url.com' 'new-url.com' --allow-root

# Export database
docker exec wordpress-dev wp db export backup.sql --allow-root

# Import database
docker exec wordpress-dev wp db import backup.sql --allow-root
```

### Cache Operations
```bash
# Flush object cache
docker exec wordpress-dev wp cache flush --allow-root

# Redis cache operations
docker exec wordpress-dev wp redis flush --allow-root
docker exec wordpress-dev wp redis status --allow-root
```

## 🔍 Debugging Commands

### Log Analysis
```bash
# WordPress debug logs
docker exec wordpress-dev tail -f /var/log/apache2/error.log
docker exec wordpress-prod tail -f /var/log/php8.3-fpm.log

# MySQL logs
docker logs mysql-dev
docker logs mysql-prod

# Redis logs
docker logs redis-dev
docker logs redis-prod
```

### Performance Analysis
```bash
# Container resource usage
docker stats

# Disk usage
docker system df

# Network inspection
docker network ls
docker network inspect pinkangel-docker_wordpress-dev-network
```

## 💡 Tips and Best Practices

### Command Aliases
Add these to your `.bashrc` or `.zshrc`:
```bash
alias wp-dev="docker exec wordpress-dev wp --allow-root"
alias wp-prod="docker exec wordpress-prod wp --allow-root"
alias mysql-dev="docker exec -it mysql-dev mysql -u root -p"
alias mysql-prod="docker exec -it mysql-prod mysql -u root -p"
```

### Useful Command Combinations
```bash
# Quick development restart
make stop && make dev

# Full production deployment
make prod-build && make prod && make install-theme-deps-prod

# Development with fresh database
make stop && make clean && make dev

# Production backup before changes
make backup-prod && make sync-prod-to-dev
```

### Emergency Commands
```bash
# Force stop all containers
docker stop $(docker ps -q)

# Remove all containers
docker rm $(docker ps -aq)

# Clean everything (DANGEROUS!)
docker system prune -a --volumes
```

---

**Need help with a specific command?** Check the [Troubleshooting Guide](TROUBLESHOOTING.md) or run `make help`.
