# SSL Certificate Setup Guide

## ✅ **Issue Resolved**

The SSL certificate errors have been fixed! Here's what was done and how to manage SSL certificates going forward.

## 🔧 **What Was Fixed**

### 1. Missing SSL Certificates
**Problem**: `/etc/nginx/ssl/default.crt` not found
**Solution**: Generated SSL certificates using the script

### 2. Deprecated Nginx Directive
**Problem**: `listen 443 ssl http2` is deprecated in newer nginx versions
**Solution**: Updated to modern syntax:
```nginx
# Old (deprecated)
listen 443 ssl http2;

# New (modern)
listen 443 ssl;
http2 on;
```

## 📁 **Generated Certificates**

The following certificates are now available in `ssl-proxy/`:
```
ssl-proxy/
├── default.crt/key      # Fallback certificate for unknown domains
└── pinkangel.crt/key    # Certificate for pinkangel.local
```

## 🚀 **Quick Setup Commands**

### Generate SSL Certificates (already done)
```bash
./scripts/generate-proxy-ssl.sh
```

### Add Domain to Hosts File
```bash
# Add this line to /etc/hosts
echo "127.0.0.1 pinkangel.local" | sudo tee -a /etc/hosts
```

### Start/Restart Proxy
```bash
./scripts/manage-proxy.sh restart
```

## 🌐 **Access Your Sites**

- **Development**: http://localhost:8080 (direct, no SSL needed)
- **Production**: https://pinkangel.local (via proxy with SSL)

## ⚠️ **Browser SSL Warnings**

Since these are self-signed certificates, browsers will show security warnings. This is normal for local development.

### To Proceed:
1. Click "Advanced" in the browser warning
2. Click "Proceed to pinkangel.local (unsafe)"

### To Trust Certificates System-Wide (Optional):
```bash
# macOS
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain ssl-proxy/pinkangel.crt

# Linux
sudo cp ssl-proxy/pinkangel.crt /usr/local/share/ca-certificates/
sudo update-ca-certificates
```

## 🔄 **Adding SSL for New Sites**

When adding a new website:

### 1. Generate Certificate
```bash
# Method 1: Add to the script
# Edit scripts/generate-proxy-ssl.sh and add your site

# Method 2: Generate manually
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout "ssl-proxy/mysite.key" \
    -out "ssl-proxy/mysite.crt" \
    -subj "/C=US/ST=State/L=City/O=MyOrg/CN=mysite.local"
```

### 2. Configure in Nginx
```nginx
server {
    listen 443 ssl;
    http2 on;
    server_name mysite.local;

    ssl_certificate /etc/nginx/ssl/mysite.crt;
    ssl_certificate_key /etc/nginx/ssl/mysite.key;
    # ... rest of configuration
}
```

### 3. Reload Proxy
```bash
./scripts/manage-proxy.sh reload
```

## 🔍 **Troubleshooting SSL Issues**

### Check Certificate Validity
```bash
# View certificate details
openssl x509 -in ssl-proxy/pinkangel.crt -text -noout

# Check expiration date
openssl x509 -in ssl-proxy/pinkangel.crt -noout -dates
```

### Test SSL Connection
```bash
# Test SSL handshake
openssl s_client -connect pinkangel.local:443 -servername pinkangel.local

# Test with curl
curl -k https://pinkangel.local
```

### Common SSL Errors
- **Certificate not found**: Run `./scripts/generate-proxy-ssl.sh`
- **Permission denied**: Check file permissions in `ssl-proxy/`
- **Wrong domain**: Ensure certificate CN matches the domain name
- **Expired certificate**: Regenerate certificates (valid for 365 days)

## 🔒 **Production SSL Certificates**

For production use with real domains:

### 1. Replace Self-Signed Certificates
```bash
# Replace with real certificates from your CA
cp /path/to/real/pinkangel.crt ssl-proxy/pinkangel.crt
cp /path/to/real/pinkangel.key ssl-proxy/pinkangel.key
```

### 2. Set Up Let's Encrypt (Optional)
```bash
# Install certbot
# Generate certificates for your real domain
# Copy certificates to ssl-proxy/ directory
# Set up auto-renewal
```

### 3. Reload Configuration
```bash
./scripts/manage-proxy.sh reload
```

## 📋 **Certificate Management Checklist**

- ✅ SSL certificates generated
- ✅ Nginx configuration updated to modern syntax
- ✅ Proxy container running successfully
- ✅ Configuration validated
- ⏳ Add domain to /etc/hosts: `sudo echo "127.0.0.1 pinkangel.local" >> /etc/hosts`
- ⏳ Test access: https://pinkangel.local

The reverse proxy is now ready to handle SSL termination for all your production websites!
