# Object Cache Pro

A **business class** Redis object cache backend for WordPress.

Truly reliable, highly optimized, fully customizable and with a _dedicated engineer_ when you most need it.

- Rewritten for raw performance
- WordPress object cache API compliant
- Easy debugging & logging
- Fully unit tested (100% code coverage)
- Secure connections with TLS
- Seamless WP CLI & Debug Bar integration
- Optimized for WooCommerce, Jetpack & Yoast SEO

## Installation

See the [installation instructions](https://objectcache.pro/docs/installation/).

## Documentation

You’ll find the documentation on [objectcache.pro/docs](https://objectcache.pro/docs/).

## Known Issues

- https://core.trac.wordpress.org/ticket/31245
- https://github.com/woocommerce/woocommerce/pull/24961
- https://github.com/woocommerce/woocommerce/pull/27696

### Query caching

- https://make.wordpress.org/core/2022/10/07/improvements-to-wp_query-performance-in-6-1/

### Query splitting

- https://core.trac.wordpress.org/changeset/56513
- https://github.com/wpmetabox/mb-relationships/commit/06aa11e0d99be5663622c06eb39b0de9b0817bd9
