# Domain Management Guide

This guide explains how to dynamically change the main domain for your PinkAngel WordPress site using the built-in domain management tools.

## 🌐 Overview

The domain management system allows you to easily change the main domain for your PinkAngel WordPress site from the default `pinkangel.local` to any custom domain. This is useful for:

- Setting up staging environments
- Using custom local domains
- Preparing for production deployment
- Testing with different domain configurations

## 🚀 Quick Start

### Change Domain

```bash
# Change to a new domain
make set-domain DOMAIN=newdomain.local

# Examples
make set-domain DOMAIN=staging.local
make set-domain DOMAIN=mycompany.local
make set-domain DOMAIN=test.example.com
```

### Rollback Changes

```bash
# List available backups
ls backups/domain-change-*

# Rollback to previous configuration
make rollback-domain BACKUP=backups/domain-change-20240123-143022
```

## 📋 What Gets Updated

When you change the domain, the following components are automatically updated:

### 1. Nginx Proxy Configuration
- **File**: `nginx-proxy-config/sites/pinkangel.conf`
- **Changes**: Updates `server_name` directives for both HTTP and HTTPS servers
- **Effect**: Routes traffic for the new domain to your WordPress site

### 2. SSL Certificates
- **Files**: `ssl-proxy/pinkangel.crt` and `ssl-proxy/pinkangel.key`
- **Changes**: Generates new self-signed certificates for the new domain
- **Effect**: Enables HTTPS access with the new domain

### 3. Environment Configuration
- **File**: `.env` (if it exists)
- **Changes**: Updates `WORDPRESS_DOMAIN` and `WORDPRESS_URL` variables
- **Effect**: Ensures WordPress knows about the new domain

### 4. WordPress Database URLs
- **Database Options**: `siteurl` and `home` options in WordPress database
- **Changes**: Updates WordPress site and home URLs to use the new domain
- **Effect**: Fixes asset loading, admin URLs, and all WordPress-generated links
- **Content**: Searches and replaces old domain URLs in post content, options, etc.

### 5. SSL Generation Script
- **File**: `scripts/generate-proxy-ssl.sh`
- **Changes**: Updates domain references for future SSL certificate generation
- **Effect**: Ensures future SSL regeneration uses the correct domain

### 6. Automatic Backup
- **Location**: `backups/domain-change-YYYYMMDD-HHMMSS/`
- **Contents**: Previous nginx config, SSL certificates, and .env file
- **Purpose**: Enables easy rollback if needed

## 🔧 Detailed Usage

### Set Domain Command

```bash
make set-domain DOMAIN=<new-domain>
```

**Features:**
- ✅ Domain format validation
- ✅ Current domain detection
- ✅ Automatic backup creation
- ✅ SSL certificate regeneration
- ✅ Interactive confirmation
- ✅ Comprehensive error handling

**Example Output:**
```
🌐 PinkAngel Domain Change Tool
================================

ℹ️  Detected current domain: pinkangel.local
ℹ️  Current domain: pinkangel.local
ℹ️  New domain: staging.local

Do you want to proceed with the domain change? (y/N): y

🔧 Creating backup of current configuration...
✅ Backup created in: backups/domain-change-20240123-143022

🔧 Updating nginx proxy configuration...
✅ Updated nginx configuration

🔧 Regenerating SSL certificates for staging.local...
✅ SSL certificates regenerated for staging.local

🔧 Updating .env file...
✅ Updated .env file

✅ Domain change completed successfully!
```

### Rollback Domain Command

```bash
make rollback-domain BACKUP=<backup-directory>
```

**Features:**
- ✅ Backup validation
- ✅ Previous domain detection
- ✅ Complete configuration restoration
- ✅ Interactive confirmation

## 📝 Post-Change Steps

After changing the domain, you need to complete these steps:

### 1. Update /etc/hosts File

Add the new domain to your local hosts file:

```bash
# Add new domain
echo "127.0.0.1 newdomain.local" | sudo tee -a /etc/hosts

# Remove old domain (optional)
sudo sed -i '/127.0.0.1 pinkangel.local/d' /etc/hosts
```

### 2. Restart Reverse Proxy

Apply the configuration changes:

```bash
make restart-proxy
```

### 3. Access Your Site

Visit your site with the new domain:

```
https://newdomain.local
```

## 🔍 Troubleshooting

### Common Issues

**1. Domain Validation Errors**
```
❌ Invalid domain format: invalid..domain
```
- **Solution**: Use a valid domain format (letters, numbers, dots, hyphens only)
- **Examples**: `staging.local`, `test.example.com`, `mysite.local`

**2. SSL Certificate Warnings**
```
Your connection is not private
```
- **Cause**: Browser doesn't trust self-signed certificates
- **Solution**: Click "Advanced" → "Proceed to domain (unsafe)" or add certificate to system trust store

**3. Site Not Loading**
```
This site can't be reached
```
- **Check**: Domain added to /etc/hosts file
- **Check**: Reverse proxy is running (`docker ps | grep reverse-proxy`)
- **Fix**: Run `make restart-proxy`

### Verification Commands

```bash
# Check nginx configuration
docker exec reverse-proxy nginx -t

# Check SSL certificate
openssl x509 -in ssl-proxy/pinkangel.crt -text -noout | grep Subject

# Check domain in hosts file
grep "newdomain.local" /etc/hosts

# Test domain resolution
curl -k https://newdomain.local
```

## 🔄 Environment Compatibility

The domain management system works with both development and production environments:

### Development Environment
- **Access**: Direct port access (`localhost:8080`) continues to work
- **Proxy**: New domain routes through reverse proxy
- **SSL**: Self-signed certificates for HTTPS access

### Production Environment
- **Access**: Only through the configured domain
- **SSL**: Replace self-signed certificates with real ones for production
- **Environment**: Updates `.env` file automatically

## 🛡️ Security Considerations

### Self-Signed Certificates
- **Development**: Acceptable for local testing
- **Production**: Replace with certificates from a trusted CA
- **Browser Warnings**: Expected with self-signed certificates

### Backup Security
- **Location**: Backups stored in `backups/` directory
- **Contents**: May contain sensitive configuration
- **Recommendation**: Exclude backups from version control

## 📚 Advanced Usage

### Custom SSL Certificates

To use custom SSL certificates instead of self-signed ones:

```bash
# Replace generated certificates
cp /path/to/your/domain.crt ssl-proxy/pinkangel.crt
cp /path/to/your/domain.key ssl-proxy/pinkangel.key

# Set proper permissions
chmod 644 ssl-proxy/pinkangel.crt
chmod 600 ssl-proxy/pinkangel.key

# Restart proxy
make restart-proxy
```

### Multiple Domains

To support multiple domains, you can:

1. Create additional nginx configuration files
2. Generate certificates for each domain
3. Update the proxy docker-compose configuration

### Automation

For automated deployments, you can use the scripts directly:

```bash
# Non-interactive domain change
echo "y" | ./scripts/set-domain.sh newdomain.local

# Check if change was successful
if grep -q "newdomain.local" nginx-proxy-config/sites/pinkangel.conf; then
    echo "Domain change successful"
else
    echo "Domain change failed"
    exit 1
fi
```

## 🎯 Best Practices

1. **Always backup** before making changes (automatic with the tool)
2. **Test the new domain** before removing the old one
3. **Update documentation** when changing domains permanently
4. **Use meaningful domain names** that reflect the environment
5. **Keep backups** for easy rollback capability
6. **Verify SSL certificates** after domain changes
7. **Update team members** about domain changes

## 📞 Support

If you encounter issues with domain management:

1. Check the troubleshooting section above
2. Verify all post-change steps were completed
3. Use the rollback feature to restore previous configuration
4. Check Docker logs: `docker logs reverse-proxy`
5. Validate nginx configuration: `docker exec reverse-proxy nginx -t`
