# Reverse Proxy Quick Start Guide

## Overview

This setup provides a nginx reverse proxy that handles multiple dockerized websites on ports 80/443, routing traffic based on domain names.

## Quick Setup (First Time)

```bash
# 1. Setup everything (needs sudo for /etc/hosts)
sudo ./scripts/setup-complete-environment.sh setup

# 2. Start all services
./scripts/setup-complete-environment.sh start

# 3. Check status
./scripts/setup-complete-environment.sh status
```

## Access Your Sites

- **Development**: http://localhost:8080 (direct access, bypasses proxy)
- **Production**: https://pinkangel.local (via reverse proxy)
- **PhpMyAdmin**: http://localhost:8081

## Daily Usage

```bash
# Start everything
./scripts/setup-complete-environment.sh start

# Stop everything
./scripts/setup-complete-environment.sh stop

# Check status
./scripts/setup-complete-environment.sh status

# Test connectivity
./scripts/setup-complete-environment.sh test
```

## Proxy Management

```bash
# Proxy-specific commands
./scripts/manage-proxy.sh start
./scripts/manage-proxy.sh stop
./scripts/manage-proxy.sh status
./scripts/manage-proxy.sh logs
./scripts/manage-proxy.sh reload
```

## Adding New Websites

```bash
# 1. Create site configuration
./scripts/manage-proxy.sh add-site mywebsite

# 2. Edit the generated config file
nano nginx-proxy-config/sites/mywebsite.conf

# 3. Generate SSL certificates
# Add to scripts/generate-proxy-ssl.sh or run manually:
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout "ssl-proxy/mywebsite.key" \
    -out "ssl-proxy/mywebsite.crt" \
    -subj "/C=US/ST=State/L=City/O=MyOrg/CN=mywebsite.local"

# 4. Add to /etc/hosts (only for production domains)
echo "127.0.0.1 mywebsite.local" | sudo tee -a /etc/hosts

# 5. Create docker-compose for your website
# Make sure it connects to proxy-network and doesn't expose 80/443

# 6. Reload proxy
./scripts/manage-proxy.sh reload
```

## File Structure

```
├── docker-compose.proxy.yml          # Reverse proxy setup
├── nginx-proxy-config/
│   ├── nginx.conf                    # Main nginx config
│   └── sites/
│       ├── pinkangel.conf           # PinkAngel routing
│       └── example-site.conf.template # Template for new sites
├── ssl-proxy/                       # SSL certificates
├── nginx-proxy-cache/              # Proxy cache
└── scripts/
    ├── setup-complete-environment.sh # Main setup script
    ├── manage-proxy.sh              # Proxy management
    └── generate-proxy-ssl.sh        # SSL certificate generation
```

## Troubleshooting

### Common Issues

1. **Certificate warnings**: Browser will show warnings for self-signed certificates
2. **Connection refused**: Check if all containers are running and on correct networks
3. **502 Bad Gateway**: Backend containers might not be ready or reachable

### Debug Commands

```bash
# Check proxy logs
./scripts/manage-proxy.sh logs

# Test nginx config
./scripts/manage-proxy.sh test

# Check container connectivity
docker exec reverse-proxy ping nginx-prod

# Check networks
docker network ls
docker network inspect proxy-network
```

### Reset Everything

```bash
# Stop all services
./scripts/setup-complete-environment.sh stop

# Remove proxy network
docker network rm proxy-network

# Start fresh
./scripts/setup-complete-environment.sh start
```

## What Changed

### Modified Files

- `docker-compose.dev.yml`: Kept original port 8080 for direct access (no proxy)
- `docker-compose.prod.yml`: Removed external ports 80/443, added proxy network

### New Files

- `docker-compose.proxy.yml`: Reverse proxy configuration
- `nginx-proxy-config/`: Nginx configuration for proxy
- `ssl-proxy/`: SSL certificates directory
- Various management scripts

### Networks

- `proxy-network`: Connects reverse proxy to all websites
- Existing networks (`wordpress-dev-network`, `wordpress-prod-network`) remain unchanged

## Benefits

- **Single entry point**: All websites accessible through standard ports 80/443
- **SSL termination**: Centralized SSL certificate management
- **Load balancing**: Can easily add multiple backend instances
- **Caching**: Proxy-level caching for better performance
- **Security**: Centralized security headers and rate limiting
- **Easy scaling**: Add new websites without port conflicts
