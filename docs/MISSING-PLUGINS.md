# Missing Plugins Analysis

Complete analysis of plugins needed to match the live PinkAngel.bg database.

## 📊 Current Status

**Your Environment (Auto-detected):**
- ✅ **4 plugins installed** (matching live database)
- ❌ **34 plugins missing** (needed for full compatibility)
- 🔍 **Works with both dev and prod** environments

**Live Database Requirements:**
- **38 total plugins** active in live environment
- **194 database tables** (vs your current 53)

## ✅ Already Installed Plugins (4/38)

| Plugin | Version | Status |
|--------|---------|--------|
| **WooCommerce** | 10.0.2 | ✅ Active |
| **Advanced Woo Search** | 3.38 | ✅ Active |
| **TI WooCommerce Wishlist** | 2.10.0 | ✅ Active |
| **Polylang** | 3.7.3 | ✅ Active |

## ❌ Missing Plugins (34/38)

### 🆓 Free Plugins (28 plugins)

#### **Essential Plugins (Install First)**
```bash
# Auto-detects your environment (dev/prod)
docker exec wordpress-dev wp plugin install \
  advanced-custom-fields \
  all-in-one-seo-pack \
  contact-form-7 \
  ultimate-member \
  wordfence \
  --activate --allow-root
```

| Plugin | Purpose | Priority |
|--------|---------|----------|
| **Advanced Custom Fields** | Custom fields management | 🔴 High |
| **All in One SEO Pack** | SEO optimization | 🔴 High |
| **Contact Form 7** | Contact forms | 🔴 High |
| **Ultimate Member** | User management | 🔴 High |
| **Wordfence** | Security | 🔴 High |

#### **WooCommerce Extensions**
```bash
docker exec wordpress-prod wp plugin install \
  woo-accordions \
  woo-discount-rules \
  woo-product-filter \
  woocommerce-delivery-notes \
  --activate --allow-root
```

| Plugin | Purpose | Priority |
|--------|---------|----------|
| **Woo Accordions** | Product accordions | 🟡 Medium |
| **Woo Discount Rules** | Discount management | 🟡 Medium |
| **Woo Product Filter** | Product filtering | 🟡 Medium |
| **WooCommerce Delivery Notes** | Order delivery notes | 🟡 Medium |

#### **Development & Utility**
```bash
docker exec wordpress-prod wp plugin install \
  query-monitor \
  classic-editor \
  code-snippets \
  loco-translate \
  wp-crontrol \
  --activate --allow-root
```

| Plugin | Purpose | Priority |
|--------|---------|----------|
| **Query Monitor** | Development debugging | 🟢 Low |
| **Classic Editor** | WordPress classic editor | 🟡 Medium |
| **Code Snippets** | Custom code management | 🟢 Low |
| **Loco Translate** | Translation management | 🟡 Medium |
| **WP Crontrol** | Cron job management | 🟢 Low |

#### **Performance & SEO**
```bash
docker exec wordpress-prod wp plugin install \
  cloudflare \
  google-site-kit \
  updraftplus \
  --activate --allow-root
```

| Plugin | Purpose | Priority |
|--------|---------|----------|
| **Cloudflare** | CDN integration | 🟡 Medium |
| **Google Site Kit** | Google services integration | 🟡 Medium |
| **UpdraftPlus** | Backup management | 🔴 High |
| **W3 Total Cache** | Caching (conflicts with Redis) | ⚠️ Caution |

#### **Remaining Plugins**
```bash
docker exec wordpress-prod wp plugin install \
  cookie-law-info \
  flexible-checkout-fields \
  head-footer-code \
  menu-image \
  official-facebook-pixel \
  theme-translation-for-polylang \
  um-recaptcha \
  um-woocommerce \
  wp-mail-logging \
  wpc-variation-swatches \
  --activate --allow-root
```

| Plugin | Purpose | Priority |
|--------|---------|----------|
| **Cookie Law Info** | GDPR compliance | 🟡 Medium |
| **Flexible Checkout Fields** | Custom checkout fields | 🟡 Medium |
| **Head Footer Code** | Custom code injection | 🟢 Low |
| **Menu Image** | Images in menus | 🟢 Low |
| **Facebook Pixel** | Facebook tracking | 🟡 Medium |
| **Theme Translation for Polylang** | Theme translation | 🟡 Medium |
| **UM reCAPTCHA** | Spam protection | 🟡 Medium |
| **UM WooCommerce** | User/WooCommerce integration | 🟡 Medium |
| **WP Mail Logging** | Email logging | 🟢 Low |
| **WPC Variation Swatches** | Product variation display | 🟡 Medium |

### 💰 Premium/Pro Plugins (3 plugins)

These require **paid licenses** and cannot be installed via WP-CLI:

| Plugin | Purpose | Alternative |
|--------|---------|-------------|
| **Woo Discount Rules Pro** | Advanced discount rules | Use free version |
| **WooFilter Pro** | Advanced product filtering | Use free alternatives |
| **WP-Optimize Premium** | Database optimization | Use free version |

### 🔧 Custom Plugins (3 plugins)

These are **proprietary/custom** plugins from the live site:

| Plugin | Purpose | Solution |
|--------|---------|----------|
| **middleware/function.php** | Custom functionality | Get from live site |
| **releva-1.0.34/relevaai-page-token.php** | Releva AI integration | Get from live site |
| **sh_wp_version_hider.php** | WordPress version hiding | Recreate or find alternative |

## 🚀 Installation Strategy

### **Phase 1: Essential Setup**
```bash
# Install critical plugins first
make check-missing-plugins
docker exec wordpress-prod wp plugin install advanced-custom-fields all-in-one-seo-pack contact-form-7 ultimate-member wordfence --activate --allow-root
```

### **Phase 2: WooCommerce Enhancement**
```bash
# Add WooCommerce functionality
docker exec wordpress-prod wp plugin install woo-discount-rules woocommerce-delivery-notes wpc-variation-swatches --activate --allow-root
```

### **Phase 3: Performance & SEO**
```bash
# Add performance tools (be careful with caching conflicts)
docker exec wordpress-prod wp plugin install google-site-kit updraftplus --activate --allow-root
```

### **Phase 4: Additional Features**
```bash
# Add remaining functionality as needed
docker exec wordpress-prod wp plugin install cookie-law-info official-facebook-pixel um-woocommerce --activate --allow-root
```

## ⚠️ Important Considerations

### **Caching Conflicts**
- **W3 Total Cache** vs **Redis Cache**: Don't use both simultaneously
- **Recommendation**: Keep your current Redis setup, skip W3 Total Cache

### **Performance Impact**
- **38 plugins** will significantly increase:
  - Memory usage
  - Page load times
  - Database queries
  - Admin dashboard complexity

### **License Requirements**
- **3 premium plugins** need paid licenses
- **Consider alternatives** or skip if not essential

### **Custom Plugins**
- **3 custom plugins** need manual installation
- **May require** specific configuration or API keys

## 📋 Quick Commands

```bash
# Check what's missing
make check-missing-plugins

# Install essential plugins only
docker exec wordpress-prod wp plugin install advanced-custom-fields all-in-one-seo-pack contact-form-7 ultimate-member wordfence --activate --allow-root

# Install all free plugins (heavy setup)
# Use the individual commands from sections above

# Check plugin status
docker exec wordpress-prod wp plugin list --allow-root

# Monitor performance
make monitor
```

## 🎯 Recommendations

1. **Start Small**: Install essential plugins first, test performance
2. **Monitor Resources**: Watch memory/CPU usage as you add plugins
3. **Skip Premium**: Use free alternatives unless you have licenses
4. **Test Thoroughly**: Verify functionality after each installation phase
5. **Keep Current Redis**: Don't install W3 Total Cache, keep your Redis setup

**Remember**: You don't need all 38 plugins to have a functional site. Start with essentials and add others based on actual needs.
