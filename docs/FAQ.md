# Frequently Asked Questions (FAQ)

Common questions and answers about the PinkAngel WordPress Docker environment.

## 🚀 Getting Started

### Q: What is this project?
**A:** This is a complete Docker-based WordPress development and production environment specifically configured for the PinkAngel theme with WooCommerce integration, Redis caching, and full DevOps automation.

### Q: Do I need Docker experience to use this?
**A:** No! The project includes simple Make commands that handle all Docker complexity. Just run `make dev` to get started.

### Q: What's the difference between development and production environments?
**A:** 
- **Development**: Uses Apache, includes Xdebug, phpMyAdmin, and development-friendly settings
- **Production**: Uses Nginx with SSL, optimized for performance and security

### Q: Can I use this for other WordPress themes?
**A:** Yes, but you'll need to modify the theme dependencies. The PinkAngel theme requires specific plugins (WooCommerce, Advanced Woo Search, etc.).

## 🎨 PinkAngel Theme

### Q: Why does my site show a blank page after activating PinkAngel theme?
**A:** The PinkAngel theme requires several plugins. Run `make install-theme-deps-dev` to install all dependencies automatically.

### Q: What plugins does PinkAngel theme need?
**A:** 
- WooCommerce (e-commerce)
- Advanced Woo Search (product search)
- TI WooCommerce Wishlist (wishlist functionality)
- Polylang (multilingual support)
- Redis Object Cache (performance)

### Q: Can I use PinkAngel theme without WooCommerce?
**A:** No, the theme is specifically designed for e-commerce and has WooCommerce functions built into its templates.

### Q: How do I switch from the default theme to PinkAngel?
**A:** 
1. Install dependencies: `make install-theme-deps-dev`
2. Activate theme: `docker exec wordpress-dev wp theme activate pinkangel --allow-root`

## 🔧 Technical Questions

### Q: What ports does this use?
**A:** 
- **Development**: 8080 (WordPress), 8081 (phpMyAdmin), 3306 (MySQL), 6379 (Redis)
- **Production**: 80 (HTTP), 443 (HTTPS), 3306 (MySQL), 6379 (Redis)

### Q: How do I change the ports?
**A:** Edit the port mappings in `docker-compose.dev.yml` or `docker-compose.prod.yml`. For example, change `"8080:80"` to `"9080:80"`.

### Q: Can I run both development and production at the same time?
**A:** Yes! They use different ports and container names, so they can run simultaneously.

### Q: How much disk space does this need?
**A:** Approximately 2-3GB for all Docker images, plus space for your WordPress content and databases.

### Q: How much RAM is recommended?
**A:** 8GB+ is recommended, especially when running both environments simultaneously.

## 🗄️ Database Questions

### Q: How do I access the database?
**A:** 
- **Development**: phpMyAdmin at http://localhost:8081 (root/root_password)
- **Production**: Use `make shell-mysql-prod` for command line access

### Q: How do I sync data between environments?
**A:** 
- Production → Development: `make sync-prod-to-dev`
- Development → Production: `make sync-dev-to-prod` (be careful!)

### Q: Where are database backups stored?
**A:** In the `backups/` folder. Use `make backup-prod` or `make backup-dev` to create backups.

### Q: What database does WordPress use?
**A:** WordPress uses the `pinkangel` database in both development and production environments. The system automatically creates this database on first startup.

### Q: Can I import an existing WordPress database?
**A:** Yes! First fix MySQL compatibility issues with `./quick-fix-db.sh yourfile.sql`, then use `make import-original-db` to import the fixed file.

## 🔒 Security Questions

### Q: Is this secure for production use?
**A:** Yes, the production environment includes security hardening, SSL support, and follows WordPress security best practices.

### Q: How do I use real SSL certificates?
**A:** Replace the self-signed certificates in the `ssl/` folder with real certificates from Let's Encrypt or your certificate authority.

### Q: Should I change the default passwords?
**A:** Absolutely! Edit the `.env` file and change all default passwords before production use.

### Q: What about WordPress security keys?
**A:** The system uses environment variables for security keys. Generate new ones at https://api.wordpress.org/secret-key/1.1/salt/

## 🚀 Performance Questions

### Q: How fast is this setup?
**A:** Very fast! It includes Redis object caching, Nginx optimization, and PHP-FPM for production. Run `make test-redis` to check performance.

### Q: Can I add more caching?
**A:** Yes, the production environment includes Memcached and you can add additional caching plugins.

### Q: How do I optimize for my server?
**A:** Adjust the configuration files in `php-config/`, `mysql-config/`, and `redis-config/` based on your server resources.

## 🛠️ Development Questions

### Q: How do I debug PHP code?
**A:** Xdebug is pre-configured for development. Set up your IDE to listen on port 9003 with the key "VSCODE".

### Q: Can I modify the WordPress files?
**A:** Yes, the `wordpress/` folder is mounted as a volume. Changes are immediately reflected.

### Q: How do I install additional plugins?
**A:** Use WP-CLI: `docker exec wordpress-dev wp plugin install plugin-name --activate --allow-root`

### Q: Can I use Composer for PHP dependencies?
**A:** Yes, you can add a `composer.json` file to your theme or plugin directories.

## 🔄 Workflow Questions

### Q: What's the recommended development workflow?
**A:** 
1. Develop in the development environment
2. Test thoroughly
3. Sync to production: `make sync-dev-to-prod`
4. Deploy to live server

### Q: How do I keep WordPress updated?
**A:** Run `make update-dev` or `make update-prod` to update WordPress core, plugins, and themes.

### Q: Should I commit the WordPress files to Git?
**A:** Only commit configuration files and custom themes/plugins. See [Git Strategy](GIT-STRATEGY.md) for details.

## 🆘 Troubleshooting

### Q: WordPress shows "Error establishing a database connection"
**A:** 
1. Check if MySQL is running: `docker ps | grep mysql`
2. Restart the environment: `make stop && make dev`
3. Check the logs: `make logs`

### Q: I get "port already in use" errors
**A:** 
1. Check what's using the port: `lsof -i :8080`
2. Stop the conflicting service
3. Or change the port in docker-compose files

### Q: Redis cache isn't working
**A:** 
1. Test Redis: `make test-redis`
2. Enable Redis cache: `docker exec wordpress-dev wp redis enable --allow-root`
3. Check Redis status: `docker exec wordpress-dev wp redis status --allow-root`

### Q: The site is very slow
**A:** 
1. Check container resources: `docker stats`
2. Clear caches: `make cache-clear-dev`
3. Optimize database: `docker exec wordpress-dev wp db optimize --allow-root`

## 📚 Documentation Questions

### Q: Where can I find more detailed documentation?
**A:** Check the `docs/` folder for comprehensive guides on all aspects of the system.

### Q: How do I contribute to the documentation?
**A:** Edit the relevant `.md` files in the `docs/` folder and submit a pull request.

### Q: Is there a command reference?
**A:** Yes! See [Commands Reference](COMMANDS.md) for all available commands.

## 🔮 Advanced Questions

### Q: Can I customize the Docker images?
**A:** Yes, modify `Dockerfile.dev` and `Dockerfile.prod` to customize the images, then rebuild with `make dev-build` or `make prod-build`.

### Q: How do I add custom Nginx configuration?
**A:** Edit files in the `nginx-config/` folder and restart the production environment.

### Q: Can I use this with Docker Swarm or Kubernetes?
**A:** The Docker Compose files can be adapted for orchestration platforms, but you'll need to modify them for your specific setup.

### Q: How do I monitor the production environment?
**A:** Use `make monitor` for basic monitoring, or integrate with external monitoring tools like Prometheus/Grafana.

---

**Still have questions?** Check the [Troubleshooting Guide](TROUBLESHOOTING.md) or open an issue with your question!
