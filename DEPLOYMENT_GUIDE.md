# 🚀 PinkAngel WordPress Production Deployment Guide

**Complete Docker Swarm deployment guide for pinkangel.bg on a fresh server**

---

## 📋 **Table of Contents**

1. [Server Prerequisites & Setup](#1-server-prerequisites--setup)
2. [Docker Installation & Swarm Initialization](#2-docker-installation--swarm-initialization)
3. [Project Setup](#3-project-setup)
4. [SSL Certificate Setup](#4-ssl-certificate-setup)
5. [Database Setup](#5-database-setup)
6. [WordPress Files](#6-wordpress-files)
7. [Docker Swarm Deployment](#7-docker-swarm-deployment)
8. [Post-Deployment Configuration](#8-post-deployment-configuration)
9. [Verification & Testing](#9-verification--testing)
10. [Maintenance Commands](#10-maintenance-commands)

---

## **1. Server Prerequisites & Setup**

### **1.1 Server Specifications**

**Minimum Requirements:**
- **CPU**: 2 cores (4 cores recommended)
- **RAM**: 4GB (8GB recommended)
- **Disk**: 50GB SSD (100GB recommended)
- **Network**: 1Gbps connection

**Recommended Production Specifications:**
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Disk**: 100GB+ SSD
- **Network**: High-speed connection with low latency

### **1.2 Operating System Requirements**

**Supported OS:**
- Ubuntu 20.04 LTS or newer
- Debian 11 or newer
- CentOS 8 or newer
- RHEL 8 or newer

**This guide assumes Ubuntu 22.04 LTS**

### **1.3 Initial Server Configuration**

```bash
# 1. Update system packages
sudo apt update && sudo apt upgrade -y

# 2. Install essential packages
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# 3. Configure timezone (adjust as needed)
sudo timedatectl set-timezone Europe/Sofia

# 4. Configure hostname
sudo hostnamectl set-hostname pinkangel-prod

# 5. Create deployment user (optional but recommended)
sudo useradd -m -s /bin/bash deploy
sudo usermod -aG sudo deploy
sudo usermod -aG docker deploy  # Will be created later
```

### **1.4 Firewall Configuration**

```bash
# 1. Install and enable UFW
sudo apt install -y ufw
sudo ufw --force enable

# 2. Configure basic rules
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 3. Allow SSH (adjust port if needed)
sudo ufw allow 22/tcp

# 4. Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 5. Allow Docker Swarm ports
sudo ufw allow 2377/tcp   # Cluster management
sudo ufw allow 7946/tcp   # Node communication
sudo ufw allow 7946/udp   # Node communication
sudo ufw allow 4789/udp   # Overlay network traffic

# 6. Verify firewall status
sudo ufw status verbose
```

### **1.5 Domain DNS Configuration**

**Required DNS Records:**
```
# A Record
pinkangel.bg        A    YOUR_SERVER_IP
www.pinkangel.bg    A    YOUR_SERVER_IP

# Optional CNAME (if using www redirect)
www.pinkangel.bg    CNAME    pinkangel.bg
```

**Verification:**
```bash
# Test DNS resolution
nslookup pinkangel.bg
nslookup www.pinkangel.bg

# Test from external location
dig pinkangel.bg +short
```

---

## **2. Docker Installation & Swarm Initialization**

### **2.1 Docker Installation (Ubuntu)**

```bash
# 1. Remove old Docker versions
sudo apt remove -y docker docker-engine docker.io containerd runc

# 2. Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 3. Add Docker repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 4. Update package index
sudo apt update

# 5. Install Docker Engine
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# 6. Start and enable Docker
sudo systemctl start docker
sudo systemctl enable docker

# 7. Add user to docker group
sudo usermod -aG docker $USER
sudo usermod -aG docker deploy  # If using deploy user

# 8. Verify installation
docker --version
docker compose version
```

**⚠️ Important:** Log out and log back in for group changes to take effect.

### **2.2 Docker Swarm Initialization**

```bash
# 1. Initialize Docker Swarm
docker swarm init --advertise-addr YOUR_SERVER_IP

# 2. Verify swarm status
docker info | grep -A 10 "Swarm:"

# 3. Save join tokens (for future nodes)
docker swarm join-token worker > /home/<USER>/swarm-worker-token.txt
docker swarm join-token manager > /home/<USER>/swarm-manager-token.txt

# 4. Create overlay networks (if needed)
docker network create --driver overlay --attachable pinkangel-network
```

### **2.3 Docker Configuration Optimization**

```bash
# 1. Create Docker daemon configuration
sudo mkdir -p /etc/docker

# 2. Configure Docker daemon
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "live-restore": true
}
EOF

# 3. Restart Docker
sudo systemctl restart docker

# 4. Verify configuration
docker info | grep -E "(Storage Driver|Logging Driver)"
```

---

## **3. Project Setup**

### **3.1 Repository Setup**

```bash
# 1. Create project directory
sudo mkdir -p /opt/pinkangel
sudo chown $USER:$USER /opt/pinkangel
cd /opt/pinkangel

# 2. Clone repository (adjust URL as needed)
git clone https://github.com/your-username/pinkangel-docker.git .

# Or transfer files from existing server:
# rsync -avz --progress user@old-server:/path/to/pinkangel-docker/ /opt/pinkangel/

# 3. Set proper permissions
sudo chown -R $USER:docker /opt/pinkangel
chmod +x scripts/*.sh
```

### **3.2 Environment Configuration**

```bash
# 1. Create .env file from template
cp .env.example .env

# 2. Edit .env file with production values
nano .env
```

**Required .env Configuration:**
```bash
# Database Configuration
WORDPRESS_DB_HOST=mysql-prod
WORDPRESS_DB_NAME=pinkangel
WORDPRESS_DB_USER=wordpress
WORDPRESS_DB_PASSWORD=your_secure_database_password_here
MYSQL_ROOT_PASSWORD=your_secure_root_password_here

# WordPress Security Keys (generate at https://api.wordpress.org/secret-key/1.1/salt/)
WORDPRESS_AUTH_KEY='your-unique-auth-key-here'
WORDPRESS_SECURE_AUTH_KEY='your-unique-secure-auth-key-here'
WORDPRESS_LOGGED_IN_KEY='your-unique-logged-in-key-here'
WORDPRESS_NONCE_KEY='your-unique-nonce-key-here'
WORDPRESS_AUTH_SALT='your-unique-auth-salt-here'
WORDPRESS_SECURE_AUTH_SALT='your-unique-secure-auth-salt-here'
WORDPRESS_LOGGED_IN_SALT='your-unique-logged-in-salt-here'
WORDPRESS_NONCE_SALT='your-unique-nonce-salt-here'

# SMTP Configuration (optional)
WORDPRESS_SMTP_HOST=smtp.gmail.com
WORDPRESS_SMTP_PORT=587
WORDPRESS_SMTP_USER=<EMAIL>
WORDPRESS_SMTP_PASSWORD=your-email-app-password

# Domain Configuration
DOMAIN=pinkangel.bg
```

**Generate WordPress Security Keys:**
```bash
# Generate new keys automatically
curl -s https://api.wordpress.org/secret-key/1.1/salt/ >> temp_keys.txt
# Copy the generated keys to your .env file
```

### **3.3 Directory Structure Setup**

```bash
# 1. Create required directories
mkdir -p wordpress/wp-content/{uploads,themes,plugins}
mkdir -p mysql-data
mkdir -p nginx-cache
mkdir -p ssl-certificates
mkdir -p backups
mkdir -p logs

# 2. Set proper permissions
sudo chown -R www-data:www-data wordpress/wp-content/uploads
sudo chmod -R 755 wordpress/wp-content
sudo chown -R 999:999 mysql-data  # MySQL user in container
sudo chmod 755 nginx-cache
sudo chmod 700 ssl-certificates
sudo chmod 755 backups logs

# 3. Verify directory structure
tree -L 3 /opt/pinkangel
```
