# Use resolver for dynamic upstream resolution in Docker Swarm
resolver 127.0.0.11 valid=30s;

# Use variable for dynamic upstream resolution (avoids startup DNS resolution)
# This allows nginx to start even if wordpress-prod is not yet available

# Cache zones (now enabled)
proxy_cache_path /var/cache/nginx-cache/proxy levels=1:2 keys_zone=proxy_cache:100m inactive=60m;
proxy_cache_key "$scheme$request_method$host$request_uri";

fastcgi_cache_path /var/cache/nginx-cache/fastcgi levels=1:2 keys_zone=wordpress:100m inactive=60m;
fastcgi_cache_key "$scheme$request_method$host$request_uri";

# HTTP server (for reverse proxy)
server {
    listen 80;
    server_name _;
    root /var/www/html;
    index index.php index.html index.htm;

    # Security
    server_tokens off;

    # Deny access to sensitive files
    location ~* /(?:uploads|files)/.*\.php$ {
        deny all;
    }

    location ~* /(?:wp-config\.php|wp-config-sample\.php|readme\.html|license\.txt) {
        deny all;
    }

    # WordPress permalinks
    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    # Cache static files
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Handle PHP files (general rate limiting for public pages)
    location ~ \.php$ {
        # limit_req zone=wp_public burst=15 nodelay;  # Allow 15 quick requests for public pages
        include fastcgi_params;
        fastcgi_intercept_errors on;
        set $upstream wordpress-prod:9000;
        fastcgi_pass $upstream;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param HTTP_PROXY "";

        # Caching for PHP (now enabled)
        fastcgi_cache wordpress;
        fastcgi_cache_valid 200 301 302 60m;
        fastcgi_cache_valid 404 1m;
        fastcgi_cache_bypass $skip_cache;
        fastcgi_no_cache $skip_cache;
        add_header X-Cache-Status $upstream_cache_status;
    }

    # Rate limiting for wp-login and wp-admin (admin-friendly production security)
    location ~* /wp-login\.php {
        # limit_req zone=wp_login burst=10 nodelay;  # Allow 10 quick login attempts
        include fastcgi_params;
        set $upstream wordpress-prod:9000;
        fastcgi_pass $upstream;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }

    location ~* /wp-admin/ {
        # limit_req zone=wp_admin burst=30 nodelay;  # Allow 30 quick admin requests
        location ~ \.php$ {
            include fastcgi_params;
            set $upstream wordpress-prod:9000;
            fastcgi_pass $upstream;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        }
    }

    # API endpoints (REST API) - handle all wp-json requests
    location ~* ^/wp-json/ {
        # limit_req zone=wp_api burst=20 nodelay;   # Allow 20 quick API calls
        try_files $uri $uri/ /index.php?$args;
        include fastcgi_params;
        fastcgi_intercept_errors on;
        set $upstream wordpress-prod:9000;
        fastcgi_pass $upstream;
        fastcgi_param SCRIPT_FILENAME $document_root/index.php;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param HTTP_PROXY "";
    }

    # AJAX endpoints
    location ~* /wp-admin/admin-ajax\.php {
        # limit_req zone=wp_api burst=20 nodelay;   # Allow 20 quick AJAX calls
        include fastcgi_params;
        set $upstream wordpress-prod:9000;
        fastcgi_pass $upstream;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }

    # Deny access to any files with a .php extension in the uploads directory
    location ~* /wp-content/uploads/.*\.php$ {
        deny all;
    }

    # Cache bypass conditions
    set $skip_cache 0;

    # POST requests and urls with a query string should always go to PHP
    if ($request_method = POST) {
        set $skip_cache 1;
    }

    if ($query_string != "") {
        set $skip_cache 1;
    }

    # Don't cache uris containing the following segments
    if ($request_uri ~* "/wp-admin/|/xmlrpc.php|wp-.*.php|/feed/|index.php|sitemap(_index)?.xml") {
        set $skip_cache 1;
    }

    # Don't use the cache for logged in users or recent commenters
    if ($http_cookie ~* "comment_author|wordpress_[a-f0-9]+|wp-postpass|wordpress_no_cache|wordpress_logged_in") {
        set $skip_cache 1;
    }
}

# HTTPS server (disabled when behind reverse proxy)
# server {
#     listen 443 ssl;
#     http2 on;
#     server_name _;
#     root /var/www/html;
#     index index.php index.html index.htm;
#
#     # SSL Configuration
#     ssl_certificate /etc/nginx/ssl/cert.pem;
#     ssl_certificate_key /etc/nginx/ssl/key.pem;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#
#     # Security
#     server_tokens off;
#
#     # Deny access to sensitive files
#     location ~* /(?:uploads|files)/.*\.php$ {
#         deny all;
#     }
#
#     location ~* /(?:wp-config\.php|wp-config-sample\.php|readme\.html|license\.txt) {
#         deny all;
#     }
#
#     # WordPress permalinks
#     location / {
#         try_files $uri $uri/ /index.php?$args;
#     }
#
#     # Cache static files
#     location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
#         expires 1y;
#         add_header Cache-Control "public, immutable";
#         access_log off;
#     }
#
#     # Handle PHP files
#     location ~ \.php$ {
#         include fastcgi_params;
#         fastcgi_intercept_errors on;
#         fastcgi_pass php-fpm;
#         fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#         fastcgi_param PATH_INFO $fastcgi_path_info;
#         fastcgi_param HTTP_PROXY "";
#
#         # Caching for PHP (now enabled)
#         fastcgi_cache wordpress;
#         fastcgi_cache_valid 200 301 302 60m;
#         fastcgi_cache_valid 404 1m;
#         fastcgi_cache_bypass $skip_cache;
#         fastcgi_no_cache $skip_cache;
#         add_header X-Cache-Status $upstream_cache_status;
#     }
#
#     # Rate limiting for wp-login and wp-admin
#     location ~* /wp-login\.php {
#         limit_req zone=login_limit burst=2 nodelay;
#         include fastcgi_params;
#         fastcgi_pass php-fpm;
#         fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#     }
#
#     location ~* /wp-admin/ {
#         limit_req zone=wp_limit burst=5 nodelay;
#         location ~ \.php$ {
#             include fastcgi_params;
#             fastcgi_pass php-fpm;
#             fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#         }
#     }
#
#     # Deny access to any files with a .php extension in the uploads directory
#     location ~* /wp-content/uploads/.*\.php$ {
#         deny all;
#     }
#
#     # Cache bypass conditions
#     set $skip_cache 0;
#
#     # POST requests and urls with a query string should always go to PHP
#     if ($request_method = POST) {
#         set $skip_cache 1;
#     }
#
#     if ($query_string != "") {
#         set $skip_cache 1;
#     }
#
#     # Don't cache uris containing the following segments
#     if ($request_uri ~* "/wp-admin/|/xmlrpc.php|wp-.*.php|/feed/|index.php|sitemap(_index)?.xml") {
#         set $skip_cache 1;
#     }
#
#     # Don't use the cache for logged in users or recent commenters
#     if ($http_cookie ~* "comment_author|wordpress_[a-f0-9]+|wp-postpass|wordpress_no_cache|wordpress_logged_in") {
#         set $skip_cache 1;
#     }
# }
