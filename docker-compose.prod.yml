# Docker Compose Production Configuration (Non-Swarm Mode)
# For Docker Swarm mode, use: docker stack deploy -c docker-stack.prod.yml pinkangel-prod
# This file is used when running: make run-prod (non-swarm)
# For swarm mode, use: make swarm-prod

services:
  nginx:
    image: nginx:alpine
    container_name: nginx-prod
    restart: unless-stopped
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./wordpress:/var/www/html:ro
      - ./nginx-config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx-config/wordpress.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx-cache:/var/cache/nginx-cache
      - nginx_logs:/var/log/nginx
    depends_on:
      - wordpress-prod
    networks:
      - wordpress-prod-network
      - proxy-network

  wordpress-prod:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: wordpress-prod
    restart: unless-stopped
    environment:
      WORDPRESS_DB_HOST: mysql-prod
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: ${WORDPRESS_DB_PASSWORD}
      WORDPRESS_DB_NAME: pinkangel
      WORDPRESS_TABLE_PREFIX: pink_
      # WordPress security keys
      WORDPRESS_AUTH_KEY: ${WORDPRESS_AUTH_KEY}
      WORDPRESS_SECURE_AUTH_KEY: ${WORDPRESS_SECURE_AUTH_KEY}
      WORDPRESS_LOGGED_IN_KEY: ${WORDPRESS_LOGGED_IN_KEY}
      WORDPRESS_NONCE_KEY: ${WORDPRESS_NONCE_KEY}
      WORDPRESS_AUTH_SALT: ${WORDPRESS_AUTH_SALT}
      WORDPRESS_SECURE_AUTH_SALT: ${WORDPRESS_SECURE_AUTH_SALT}
      WORDPRESS_LOGGED_IN_SALT: ${WORDPRESS_LOGGED_IN_SALT}
      WORDPRESS_NONCE_SALT: ${WORDPRESS_NONCE_SALT}
      # Redis configuration
      WORDPRESS_REDIS_HOST: redis-prod
      WORDPRESS_REDIS_PORT: 6379
      WORDPRESS_REDIS_DATABASE: 0
      # Environment identification
      WORDPRESS_ENV: production
      # SMTP configuration
      WORDPRESS_SMTP_HOST: ${WORDPRESS_SMTP_HOST}
      WORDPRESS_SMTP_PORT: ${WORDPRESS_SMTP_PORT}
      WORDPRESS_SMTP_USER: ${WORDPRESS_SMTP_USER}
      WORDPRESS_SMTP_PASSWORD: ${WORDPRESS_SMTP_PASSWORD}
    volumes:
      - ./wordpress:/var/www/html
      - ./php-config/php-prod.ini:/usr/local/etc/php/conf.d/php-prod.ini:ro
      - ./php-config/opcache.ini:/usr/local/etc/php/conf.d/opcache.ini:ro
    depends_on:
      - mysql-prod
      - redis-prod
    networks:
      - wordpress-prod-network

  mysql-prod:
    image: mysql:8.0
    container_name: mysql-prod
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: pinkangel
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: ${WORDPRESS_DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
    volumes:
      - mysql_prod_data:/var/lib/mysql
      - ./mysql-config/my-prod.cnf:/etc/mysql/conf.d/my.cnf:ro
      - ./backups:/backups
    networks:
      - wordpress-prod-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: phpmyadmin-prod
    restart: unless-stopped
    environment:
      PMA_HOST: mysql-prod
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      PMA_ARBITRARY: 1
      UPLOAD_LIMIT: 100M
    ports:
      - "8081:80"
    depends_on:
      - mysql-prod
    networks:
      - wordpress-prod-network

  redis-prod:
    image: redis:7-alpine
    container_name: redis-prod
    restart: unless-stopped
    volumes:
      - redis_prod_data:/data
      - ./redis-config/redis-prod.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - wordpress-prod-network

  memcached:
    image: memcached:alpine
    container_name: memcached-prod
    restart: unless-stopped
    command: memcached -m 256
    networks:
      - wordpress-prod-network

  wordpress-cron-prod:
    build:
      context: .
      dockerfile: Dockerfile.cron
    container_name: wordpress-cron-prod
    restart: unless-stopped
    init: true
    environment:
      WORDPRESS_DB_HOST: mysql-prod
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: ${WORDPRESS_DB_PASSWORD}
      WORDPRESS_DB_NAME: pinkangel
      WORDPRESS_TABLE_PREFIX: pink_
      # WordPress security keys
      WORDPRESS_AUTH_KEY: ${WORDPRESS_AUTH_KEY}
      WORDPRESS_SECURE_AUTH_KEY: ${WORDPRESS_SECURE_AUTH_KEY}
      WORDPRESS_LOGGED_IN_KEY: ${WORDPRESS_LOGGED_IN_KEY}
      WORDPRESS_NONCE_KEY: ${WORDPRESS_NONCE_KEY}
      WORDPRESS_AUTH_SALT: ${WORDPRESS_AUTH_SALT}
      WORDPRESS_SECURE_AUTH_SALT: ${WORDPRESS_SECURE_AUTH_SALT}
      WORDPRESS_LOGGED_IN_SALT: ${WORDPRESS_LOGGED_IN_SALT}
      WORDPRESS_NONCE_SALT: ${WORDPRESS_NONCE_SALT}
      # Redis configuration
      WORDPRESS_REDIS_HOST: redis-prod
      WORDPRESS_REDIS_PORT: 6379
      WORDPRESS_REDIS_DATABASE: 0
      # Cron-specific environment
      CRON_ENVIRONMENT: production
      CRON_LOG_LEVEL: info
    volumes:
      - ./wordpress:/var/www/html:ro
      - ./php-config/php-prod.ini:/usr/local/etc/php/conf.d/php-prod.ini:ro
      - ./php-config/opcache.ini:/usr/local/etc/php/conf.d/opcache.ini:ro
      - cron_prod_logs:/var/log/wordpress-cron
    depends_on:
      - mysql-prod
      - redis-prod
      - wordpress-prod
    networks:
      - wordpress-prod-network

volumes:
  mysql_prod_data:
  redis_prod_data:
  nginx_logs:
  cron_prod_logs:

networks:
  wordpress-prod-network:
    driver: bridge
  proxy-network:
    external: true
    name: proxy-network
