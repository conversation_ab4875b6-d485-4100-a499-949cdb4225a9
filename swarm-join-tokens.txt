# Docker Swarm Join Tokens
# Generated on: Mon Jul 28 01:05:17 EEST 2025
# Swarm ID: utibh0to3r4u7pypqdtp2rvqf

## IMPORTANT: Keep these tokens secure! ##
# Anyone with these tokens can join your Docker Swarm cluster

# ============================================================================
# WORKER JOIN TOKEN
# ============================================================================
# Workers can run containers but cannot manage the swarm

To add a worker to this swarm, run the following command:

    docker swarm join --token SWMTKN-1-41g7f0891s5kqf9k6rvd73e1y2p1krx2rcouxqbepnrlptln01-bs45kgkdtxy2wxzvklqxgin1c ************:2377

# ============================================================================
# MANAGER JOIN TOKEN  
# ============================================================================
# Managers can run containers AND manage the swarm (add/remove nodes, etc.)

To add a manager to this swarm, run the following command:

    docker swarm join --token SWMTKN-1-41g7f0891s5kqf9k6rvd73e1y2p1krx2rcouxqbepnrlptln01-15ie5m8zojqfv0fv0vqiim82d ************:2377

# ============================================================================
# CURRENT SWARM STATUS
# ============================================================================

ID                            HOSTNAME         STATUS    AVAILABILITY   MANAGER STATUS   ENGINE VERSION
9k6zfrglndwm9pt76sq35ciwu *   docker-desktop   Ready     Active         Leader           28.3.2

# ============================================================================
# SWARM INFORMATION
# ============================================================================

 Swarm: active
  NodeID: 9k6zfrglndwm9pt76sq35ciwu
  Is Manager: true
  ClusterID: utibh0to3r4u7pypqdtp2rvqf
  Managers: 1
  Nodes: 1
  Default Address Pool: 10.0.0.0/8  
  SubnetSize: 24
  Data Path Port: 4789
  Orchestration:
   Task History Retention Limit: 5
  Raft:
   Snapshot Interval: 10000
   Number of Old Snapshots to Retain: 0
   Heartbeat Tick: 1
   Election Tick: 10
  Dispatcher:
   Heartbeat Period: 5 seconds
  CA Configuration:
   Expiry Duration: 3 months
   Force Rotate: 0

# ============================================================================
# TOKEN MANAGEMENT COMMANDS
# ============================================================================

# Rotate worker token (invalidates old worker token):
# docker swarm join-token --rotate worker

# Rotate manager token (invalidates old manager token):
# docker swarm join-token --rotate manager

# View current tokens:
# make swarm-join-tokens

# View nodes:
# make swarm-nodes

# ============================================================================
# SECURITY NOTES
# ============================================================================

# 1. Store this file securely - it contains sensitive join tokens
# 2. Rotate tokens regularly for security
# 3. Remove nodes that are no longer needed
# 4. Monitor swarm activity regularly

# Generated by: scripts/save-swarm-tokens.sh
