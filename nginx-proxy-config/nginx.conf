user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    log_format proxy '$remote_addr - $remote_user [$time_local] "$request" '
                     '$status $body_bytes_sent "$http_referer" '
                     '"$http_user_agent" "$http_x_forwarded_for" '
                     'upstream: $upstream_addr response_time: $upstream_response_time';

    access_log /var/log/nginx/access.log proxy;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Buffer sizes
    client_body_buffer_size 128k;
    client_max_body_size 100m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Proxy settings
    proxy_buffering on;
    proxy_buffer_size 128k;
    proxy_buffers 4 256k;
    proxy_busy_buffers_size 256k;
    proxy_temp_file_write_size 256k;
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;

    # Proxy headers
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;

    # Rate limiting zones (admin-friendly production security) - DISABLED
    # limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;     # 30 req/sec for general traffic
    # limit_req_zone $binary_remote_addr zone=admin_pages:10m rate=5r/s;  # 5 req/sec for admin PHP pages
    # limit_req_zone $binary_remote_addr zone=admin_assets:10m rate=50r/s; # 50 req/sec for admin JS/CSS
    # limit_req_zone $binary_remote_addr zone=static_assets:10m rate=100r/s; # 100 req/sec for frontend static assets
    # limit_req_zone $binary_remote_addr zone=login:10m rate=2r/s;        # 2 req/sec for login
    # limit_req_zone $binary_remote_addr zone=api:10m rate=20r/s;         # 20 req/sec for API calls
    # limit_req_zone $binary_remote_addr zone=checkout:10m rate=100r/s;

    # Security headers
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Robots-Tag "noindex, nofollow, nosnippet, noarchive" always;

    # Cache settings for proxy
    proxy_cache_path /var/cache/nginx-cache/proxy levels=1:2 keys_zone=proxy_cache:100m inactive=60m max_size=1g;
    proxy_cache_key "$scheme$request_method$host$request_uri";
    proxy_cache_valid 200 301 302 10m;
    proxy_cache_valid 404 1m;

    # Default server (catch-all for unknown domains)
    server {
        listen 80 default_server;
        listen 443 ssl default_server;
        server_name "";

        # Default SSL certificate (self-signed fallback)
        ssl_certificate /etc/nginx/ssl/default.crt;
        ssl_certificate_key /etc/nginx/ssl/default.key;

        return 444; # Close connection without response
    }

    # Include site-specific configurations
    include /etc/nginx/sites-available/*.conf;
}
