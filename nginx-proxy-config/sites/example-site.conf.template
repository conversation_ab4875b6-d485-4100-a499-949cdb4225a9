# Template configuration for additional websites (PRODUCTION ONLY)
# Copy this file and rename it to your-site.conf, then customize the values
# Note: Development environments should use direct port access, not the proxy

# Upstream definitions for your site (production only)
upstream yoursite_prod {
    server yoursite-nginx-prod:443;  # Replace with your prod container name
    keepalive 32;
}

# HTTP server (redirect to HTTPS)
server {
    listen 80;
    server_name yoursite.local;  # Replace with your production domain

    # Rate limiting
    limit_req zone=general burst=20 nodelay;

    # Redirect to HTTPS
    return 301 https://$server_name$request_uri;
}



# HTTPS server for production
server {
    listen 443 ssl;
    http2 on;
    server_name yoursite.local;  # Replace with your production domain

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/yoursite.crt;  # Replace with your cert
    ssl_certificate_key /etc/nginx/ssl/yoursite.key;  # Replace with your key
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Rate limiting
    limit_req zone=general burst=20 nodelay;

    # Proxy settings
    location / {
        proxy_pass https://yoursite_prod;
        proxy_ssl_verify off;  # Adjust based on your SSL setup
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Caching
        proxy_cache proxy_cache;
        proxy_cache_bypass $http_pragma $http_authorization;
        proxy_no_cache $http_pragma $http_authorization;
        add_header X-Cache-Status $upstream_cache_status;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Special handling for admin areas
    location ~* /(admin|login|dashboard) {
        limit_req zone=login burst=5 nodelay;
        proxy_pass https://yoursite_prod;
        proxy_ssl_verify off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # No caching for admin areas
        proxy_no_cache 1;
        proxy_cache_bypass 1;
    }

    # Static files caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf|zip)$ {
        proxy_pass https://yoursite_prod;
        proxy_ssl_verify off;
        proxy_cache proxy_cache;
        proxy_cache_valid 200 1h;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Cache-Status $upstream_cache_status;
    }
}
