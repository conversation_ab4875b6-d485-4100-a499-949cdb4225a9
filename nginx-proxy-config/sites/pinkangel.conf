# Upstream definitions for pinkangel (connecting to Docker Swarm published ports)
upstream pinkangel_prod {
    server **********:8080;
    keepalive 32;
}

# HTTP server for pinkangel (redirect to HTTPS)
server {
    listen 80;
    server_name pinkangel.bg localhost;

    # Rate limiting (reduced for better admin experience)
    # limit_req zone=general burst=50 nodelay;

    # Redirect to HTTPS
    return 301 https://$server_name$request_uri;
}



# HTTPS server for pinkangel production
server {
    listen 443 ssl;
    http2 on;
    server_name pinkangel.bg localhost;

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/pinkangel.crt;
    ssl_certificate_key /etc/nginx/ssl/pinkangel.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Rate limiting (admin-friendly for general traffic)
    # limit_req zone=general burst=100 nodelay;

    client_max_body_size 50M;
    client_body_buffer_size 128k;
    client_header_buffer_size 4k;
    large_client_header_buffers 4 16k;

location ~* /\?key=wc_order_ {
    proxy_pass http://pinkangel_prod;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    
    # Longer timeouts for order processing
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # No caching for order confirmations
    proxy_no_cache 1;
    proxy_cache_bypass 1;
}

location = /wp-admin/admin-ajax.php {
    proxy_pass http://pinkangel_prod;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;

    # Increase limits for AJAX
    client_max_body_size 10M;
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # No caching for AJAX
    proxy_no_cache 1;
    proxy_cache_bypass 1;
}


    # Proxy settings
    location / {
        proxy_pass http://pinkangel_prod;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Caching
        proxy_cache proxy_cache;
        proxy_cache_bypass $http_pragma $http_authorization;
        proxy_no_cache $http_pragma $http_authorization;
        add_header X-Cache-Status $upstream_cache_status;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # WordPress login page
    location ~* /wp-login\.php {
        # limit_req zone=login burst=20 nodelay;
        proxy_pass http://pinkangel_prod;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # No caching for login
        proxy_cache proxy_cache;
        proxy_no_cache 1;
        proxy_cache_bypass 1;
        add_header X-Cache-Status $upstream_cache_status;

        # Timeouts for login operations
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # WordPress admin static assets (JS, CSS) - very generous limits
    location ~* /wp-admin/.*\.(js|css)$ {
        # limit_req zone=admin_assets burst=100 nodelay;
        proxy_pass http://pinkangel_prod;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Cache static admin assets
        proxy_cache proxy_cache;
        proxy_cache_valid 200 1h;
        expires 1h;
        add_header Cache-Control "public";
        add_header X-Cache-Status $upstream_cache_status;

        # Timeouts for static assets
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # WordPress load-scripts.php and load-styles.php - very generous limits
    location ~* /wp-admin/load-(scripts|styles)\.php {
        # limit_req zone=admin_assets burst=50 nodelay;
        proxy_pass http://pinkangel_prod;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Cache combined assets
        proxy_cache proxy_cache;
        proxy_cache_valid 200 30m;
        expires 30m;
        add_header Cache-Control "public";
        add_header X-Cache-Status $upstream_cache_status;

        # Timeouts for combined assets
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # WordPress admin PHP pages - moderate limits
    location ~* /wp-admin/ {
        # limit_req zone=admin_pages burst=20 nodelay;
        proxy_pass http://pinkangel_prod;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # No caching for admin pages
        proxy_cache proxy_cache;
        proxy_no_cache 1;
        proxy_cache_bypass 1;
        add_header X-Cache-Status $upstream_cache_status;

        # Timeouts for admin operations
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location ~* ^/checkout/order-received/ {
        # limit_req zone=checkout burst=5 nodelay;
        try_files $uri $uri/ /index.php?$args;
    }

    # WordPress API endpoints (AJAX, REST API)
    location ~* /wp-json/ {
        # limit_req zone=api burst=30 nodelay;
        proxy_pass http://pinkangel_prod;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # No caching for API
        proxy_cache proxy_cache;
        proxy_no_cache 1;
        proxy_cache_bypass 1;
        add_header X-Cache-Status $upstream_cache_status;

        # Timeouts for API operations
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

#    # WordPress AJAX endpoints
#    location ~* /wp-admin/admin-ajax\.php {
#        # limit_req zone=api burst=30 nodelay;
#        proxy_pass http://pinkangel_prod;
#        proxy_set_header Host $host;
#        proxy_set_header X-Real-IP $remote_addr;
#        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#        proxy_set_header X-Forwarded-Proto $scheme;
#
#        # No caching for AJAX
#        proxy_cache proxy_cache;
#        proxy_no_cache 1;
#        proxy_cache_bypass 1;
#        add_header X-Cache-Status $upstream_cache_status;
#
#        # Timeouts for AJAX operations
#        proxy_connect_timeout 30s;
#        proxy_send_timeout 30s;
#        proxy_read_timeout 30s;
#    }

    # WordPress core assets (wp-includes) - very generous limits
    location ~* /wp-includes/.*\.(js|css)$ {
        # limit_req zone=admin_assets burst=100 nodelay;
        proxy_pass http://pinkangel_prod;
        proxy_cache proxy_cache;
        proxy_cache_valid 200 1h;
        expires 1h;
        add_header Cache-Control "public";
        add_header X-Cache-Status $upstream_cache_status;
    }

    # Font files with CORS headers (must come before general theme assets)
    location ~* /wp-content/(themes|plugins)/.*\.(woff|woff2|ttf|eot|otf)$ {
        # limit_req zone=static_assets burst=200 nodelay;
        proxy_pass http://pinkangel_prod;
        proxy_cache proxy_cache;
        proxy_cache_valid 200 2h;
        expires 1y;

        # CORS headers for font files
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept" always;

        # Cache headers
        add_header Cache-Control "public, immutable";
        add_header X-Cache-Status $upstream_cache_status;

        # Handle preflight OPTIONS requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
            add_header Access-Control-Max-Age 86400;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }

        # Timeouts for font assets
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Theme and plugin assets (non-font files) - very generous limits for frontend
    location ~* /wp-content/(themes|plugins)/.*\.(css|js|png|jpg|jpeg|gif|ico|svg|webp)$ {
        # limit_req zone=static_assets burst=200 nodelay;
        proxy_pass http://pinkangel_prod;
        proxy_cache proxy_cache;
        proxy_cache_valid 200 2h;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Cache-Status $upstream_cache_status;

        # Timeouts for static assets
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Uploads directory assets - very generous limits
    location ~* /wp-content/uploads/.*\.(png|jpg|jpeg|gif|ico|svg|webp|pdf|doc|docx|zip)$ {
        # limit_req zone=static_assets burst=200 nodelay;
        proxy_pass http://pinkangel_prod;
        proxy_cache proxy_cache;
        proxy_cache_valid 200 4h;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Cache-Status $upstream_cache_status;

        # Timeouts for uploads
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Static files caching (general fallback) - generous limits
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
        # limit_req zone=static_assets burst=150 nodelay;
        proxy_pass http://pinkangel_prod;
        proxy_cache proxy_cache;
        proxy_cache_valid 200 1h;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Cache-Status $upstream_cache_status;

        # Timeouts for static assets
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
