# WordPress Docker Environment Makefile
.PHONY: help run-dev build-dev stop-dev stop-all clean-all build-all logs shell backup restore monitor update test-rate-limits sync-from-live swarm-start swarm-stop swarm-status swarm-logs swarm-scale swarm-update swarm-update-service swarm-init swarm-secrets swarm-build swarm-deploy swarm-remove swarm-test swarm-join-tokens swarm-nodes swarm-save-tokens swarm-backup swarm-cache-clear swarm-reload-nginx swarm-shell-wp swarm-shell-mysql swarm-shell-redis swarm-cron-status swarm-cron-logs swarm-update-wordpress proxy-start proxy-stop proxy-restart proxy-status proxy-logs proxy-test

# Default target
help: ## Show this help message with organized command groups
	@echo ""
	@echo "\033[1;34m🚀 WordPress Docker Environment - Command Reference\033[0m"
	@echo "\033[90m════════════════════════════════════════════════════════════════\033[0m"
	@echo ""
	@echo "\033[1;32m📦 ENVIRONMENT SETUP & CONTROL\033[0m"
	@echo "\033[36m  setup-env\033[0m          Interactive setup of .env file for production"
	@echo "\033[36m  run-dev\033[0m            Start development environment"
	@echo "\033[36m  swarm-start\033[0m        Start production in Docker Swarm (builds only if needed)"
	@echo "\033[36m  build-dev\033[0m          Build and start development environment from scratch"
	@echo "\033[36m  stop-dev\033[0m           Stop development environment only"
	@echo "\033[36m  stop-all\033[0m           Stop all environments (dev + proxy + swarm)"
	@echo "\033[36m  switch-env\033[0m         Interactive environment switcher (dev/prod)"
	@echo "\033[36m  check-env\033[0m          Check current environment configuration"
	@echo "\033[36m  verify-env\033[0m         Verify that .env file values are being used correctly"
	@echo "\033[36m  verify-docker\033[0m      Verify docker-compose files configuration"
	@echo "\033[36m  fix-network\033[0m        Diagnose and fix Docker network issues"
	@echo "\033[36m  test-prod\033[0m          Test production environment setup and functionality"
	@echo "\033[36m  test-wordpress\033[0m     Quick test of WordPress and phpMyAdmin accessibility"
	@echo "\033[36m  show-db-info\033[0m       Show database connection information for phpMyAdmin"
	@echo ""
	@echo "\033[1;36m🔗 REVERSE PROXY MANAGEMENT\033[0m"
	@echo "\033[36m  proxy-start\033[0m        Start nginx-proxy container (reverse proxy)"
	@echo "\033[36m  proxy-stop\033[0m         Stop nginx-proxy container"
	@echo "\033[36m  proxy-restart\033[0m      Restart nginx-proxy container"
	@echo "\033[36m  proxy-status\033[0m       Show nginx-proxy container status"
	@echo "\033[36m  proxy-logs\033[0m         Show nginx-proxy container logs"
	@echo "\033[36m  proxy-test\033[0m         Test nginx-proxy integration"
	@echo ""
	@echo "\033[1;35m🐳 DOCKER SWARM MANAGEMENT\033[0m"
	@echo "\033[36m  swarm-init\033[0m         Initialize Docker Swarm"
	@echo "\033[36m  swarm-secrets\033[0m      Create Docker Swarm secrets from .env file"
	@echo "\033[36m  swarm-build\033[0m        Build Docker images for Swarm"
	@echo "\033[36m  swarm-deploy\033[0m       Deploy production stack to Docker Swarm"
	@echo "\033[36m  swarm-remove\033[0m       Remove production stack from Docker Swarm"
	@echo "\033[36m  swarm-status\033[0m       Show Docker Swarm stack status"
	@echo "\033[36m  swarm-logs\033[0m         Show Docker Swarm service logs (usage: make swarm-logs SERVICE=service_name)"
	@echo "\033[36m  swarm-scale\033[0m        Scale Docker Swarm service (usage: make swarm-scale SERVICE=service_name REPLICAS=number)"
	@echo "\033[36m  swarm-update\033[0m       Update Docker Swarm service (usage: make swarm-update SERVICE=service_name [TYPE=force|image|config])"
	@echo "\033[36m  swarm-update-service\033[0m Advanced service update with options (usage: make swarm-update-service SERVICE=service_name TYPE=force|image|config)"
	@echo "\033[36m  swarm-join-tokens\033[0m  Show Docker Swarm join tokens for adding new nodes"
	@echo "\033[36m  swarm-nodes\033[0m        Show Docker Swarm nodes information"
	@echo "\033[36m  swarm-save-tokens\033[0m  Save join tokens to file for secure storage"
	@echo ""
	@echo "\033[1;33m💾 DATABASE OPERATIONS\033[0m"
	@echo "\033[36m  backup\033[0m             Create backup of current environment (auto-detects)"
	@echo "\033[36m  backup-dev\033[0m         Create development environment backup"
	@echo "\033[36m  swarm-backup\033[0m       Create Docker Swarm production backup"
	@echo "\033[36m  list-backups\033[0m       List all available backups by environment"
	@echo "\033[36m  restore\033[0m            Restore from backup (usage: make restore BACKUP=filename)"
	@echo "\033[36m  sync-prod-to-dev\033[0m   Sync production database to development"
	@echo "\033[36m  sync-dev-to-prod\033[0m   Sync development to production (DANGEROUS!)"
	@echo "\033[36m  sync-from-live\033[0m     Sync files from live server (uploads, theme, plugins)"
	@echo "\033[36m  import-original-db\033[0m Import original live database with pink_ prefix"
	@echo ""
	@echo "\033[1;31m🔧 MAINTENANCE & OPTIMIZATION\033[0m"
	@echo "\033[36m  optimize-dev-performance\033[0m  Comprehensive development performance optimization"
	@echo "\033[36m  fix-slow-queries\033[0m          Fix slow and duplicate queries (Query Monitor issues)"
	@echo "\033[36m  clean-action-scheduler\033[0m    Clean WooCommerce Action Scheduler"
	@echo "\033[36m  clean-spam-comments\033[0m       Clean spam comments (safe mode)"
	@echo "\033[36m  cache-clear\033[0m               Clear all caches (WordPress + Redis)"
	@echo "\033[36m  swarm-cache-clear\033[0m         Clear ALL possible caches in Docker Swarm production"
	@echo "\033[36m  swarm-reload-nginx\033[0m        Reload nginx configuration in Docker Swarm"
	@echo "\033[36m  test-rate-limits\033[0m          Test admin rate limiting (simulates 6 page switches)"
	@echo "\033[36m  complete-fix\033[0m              Complete fix after database import"
	@echo "\033[36m  fix-urls\033[0m                  Fix WordPress URLs after importing database"
	@echo "\033[36m  fix-permalinks\033[0m            Fix WordPress permalinks and rewrite rules"
	@echo "\033[36m  fix-woocommerce\033[0m           Fix WooCommerce permalinks and pages"
	@echo "\033[36m  fix-woocommerce-analytics\033[0m Fix WooCommerce analytics after deployment"
	@echo ""
	@echo "\033[1;35m📊 MONITORING & DEBUGGING\033[0m"
	@echo "\033[36m  monitor\033[0m            Show system status and monitoring info"
	@echo "\033[36m  logs\033[0m               Show logs for all containers"
	@echo "\033[36m  logs-dev\033[0m           Show development environment logs"
	@echo "\033[36m  swarm-logs SERVICE=name\033[0m   Show Docker Swarm service logs"
	@echo "\033[36m  performance-test\033[0m   Test current performance (response times)"
	@echo "\033[36m  perf-test\033[0m          Run basic performance test"
	@echo "\033[36m  test-redis\033[0m         Test Redis performance and connectivity"
	@echo "\033[36m  check-missing-plugins\033[0m  Check which plugins are missing"
	@echo ""
	@echo "\033[1;33m⏰ CRON MANAGEMENT\033[0m"
	@echo "\033[36m  cron-status\033[0m        Show WordPress cron container status"
	@echo "\033[36m  cron-logs\033[0m          Show cron container logs"
	@echo "\033[36m  cron-health\033[0m        Show detailed cron health information"
	@echo "\033[36m  cron-restart\033[0m       Restart the cron container"
	@echo "\033[36m  cron-monitor\033[0m       Real-time cron monitoring dashboard"
	@echo "\033[36m  cron-run-now\033[0m       Run specific cron task immediately"
	@echo ""
	@echo "\033[1;36m🔨 DEVELOPMENT TOOLS\033[0m"
	@echo "\033[36m  dev-mode\033[0m           Enable development-friendly settings"
	@echo "\033[36m  disable-captcha\033[0m    Disable captcha plugins (works with dev and prod)"
	@echo "\033[36m  shell-wp-dev\033[0m       Access WordPress development container shell"
	@echo "\033[36m  shell-mysql-dev\033[0m    Access MySQL development container shell"
	@echo "\033[36m  shell-redis-dev\033[0m    Access Redis development container shell"
	@echo "\033[36m  wp-cli\033[0m             Access WP-CLI (usage: make wp-cli CMD=\"command\")"
	@echo ""
	@echo "\033[1;37m⚙️  ADVANCED OPERATIONS\033[0m"
	@echo "\033[36m  build-all\033[0m          Build all images (dev + swarm)"
	@echo "\033[36m  clean-all\033[0m          Clean up all containers, images, and volumes"
	@echo "\033[36m  ssl\033[0m                Generate self-signed SSL certificate"
	@echo "\033[36m  update\033[0m             Update WordPress core, plugins, and themes"
	@echo "\033[36m  security-scan\033[0m      Run basic security checks"
	@echo ""
	@echo "\033[90m════════════════════════════════════════════════════════════════\033[0m"
	@echo "\033[1;33m💡 Quick Start:\033[0m"
	@echo "   \033[32mmake setup-env\033[0m     → Set up environment configuration"
	@echo "   \033[32mmake run-dev\033[0m       → Start development environment"
	@echo "   \033[32mmake proxy-start\033[0m   → Start reverse proxy container"
	@echo "   \033[32mmake swarm-start\033[0m   → Start production environment (Docker Swarm)"
	@echo "   \033[32mmake backup\033[0m        → Create backup before changes"
	@echo "   \033[32mmake monitor\033[0m       → Check system status"
	@echo ""
	@echo "\033[1;31m⚠️  Performance Issues?\033[0m"
	@echo "   \033[32mmake optimize-dev-performance\033[0m  → Comprehensive optimization"
	@echo "   \033[32mmake fix-slow-queries\033[0m          → Fix database performance"
	@echo "   \033[32mmake performance-test\033[0m          → Test current performance"
	@echo ""
	@echo "\033[1;33m⏰ Cron Management:\033[0m"
	@echo "   \033[32mmake cron-status\033[0m               → Check cron container status"
	@echo "   \033[32mmake cron-monitor\033[0m              → Real-time cron monitoring"
	@echo "   \033[32mmake cron-run-now TASK=wp-cron\033[0m → Run specific cron task"
	@echo ""
	@echo "\033[1;36m🔗 Reverse Proxy:\033[0m"
	@echo "   \033[32mmake proxy-start\033[0m               → Start nginx reverse proxy"
	@echo "   \033[32mmake proxy-restart\033[0m             → Restart proxy (apply config changes)"
	@echo "   \033[32mmake proxy-status\033[0m              → Check proxy status"
	@echo ""
	@echo "\033[1;35m🐳 Docker Swarm (Production):\033[0m"
	@echo "   \033[32mmake swarm-start\033[0m               → Start production (builds images only if needed)"
	@echo "   \033[32mmake swarm-stop\033[0m                → Stop Docker Swarm production environment"
	@echo "   \033[32mmake swarm-status\033[0m              → Check swarm stack status"
	@echo "   \033[32mmake swarm-update SERVICE=name\033[0m → Update service (force restart)"
	@echo "   \033[32mmake swarm-update-service\033[0m      → Advanced service update with options"
	@echo "   \033[32mmake swarm-join-tokens\033[0m         → Show join tokens for adding nodes"
	@echo "   \033[32mmake swarm-nodes\033[0m               → Show swarm nodes information"
	@echo "   \033[32mmake swarm-save-tokens\033[0m         → Save tokens to file for secure storage"
	@echo "   \033[32mmake swarm-logs SERVICE=name\033[0m   → View service logs"
	@echo "   \033[32mmake swarm-scale SERVICE=name REPLICAS=2\033[0m → Scale service"
	@echo "   📖 See docs/DOCKER-SWARM-SETUP.md for detailed documentation"
	@echo ""

help-all: ## Show detailed help with all commands
	@echo "WordPress Docker Environment - All Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-25s\033[0m %s\n", $$1, $$2}'

# ═══════════════════════════════════════════════════════════════
# 📦 ENVIRONMENT SETUP & CONTROL
# ═══════════════════════════════════════════════════════════════

setup-env: ## Interactive setup of .env file for production
	@chmod +x scripts/setup-env.sh
	@./scripts/setup-env.sh

check-env: ## Check current environment configuration status
	@chmod +x scripts/check-env.sh
	@./scripts/check-env.sh

verify-env: ## Verify that .env file values are being used correctly
	@chmod +x scripts/verify-env-usage.sh
	@./scripts/verify-env-usage.sh

verify-docker: ## Verify docker-compose.prod.yml and docker-compose.proxy.yml configuration
	@chmod +x scripts/verify-docker-compose.sh
	@./scripts/verify-docker-compose.sh

fix-network: ## Diagnose and fix Docker network issues
	@chmod +x scripts/fix-network-issues.sh
	@./scripts/fix-network-issues.sh

test-prod: ## Test production environment setup and functionality
	@chmod +x scripts/test-production-setup.sh
	@./scripts/test-production-setup.sh

test-wordpress: ## Quick test of WordPress and phpMyAdmin accessibility
	@echo "🧪 Testing WordPress and database access..."
	@echo "📍 Direct HTTP access (port 8080):"
	@curl -s -o /dev/null -w "   Status: %{http_code} | Time: %{time_total}s\n" http://localhost:8080 || echo "   ❌ Failed"
	@echo "📍 phpMyAdmin access (port 8081):"
	@curl -s -o /dev/null -w "   Status: %{http_code} | Time: %{time_total}s\n" http://localhost:8081 || echo "   ❌ Failed"
	@echo "📍 Direct HTTPS access (port 8443):"
	@curl -k -s -o /dev/null -w "   Status: %{http_code} | Time: %{time_total}s\n" https://localhost:8443 || echo "   ❌ Failed"
	@echo "📍 Following redirects (HTTP):"
	@curl -L -s -o /dev/null -w "   Final Status: %{http_code} | Redirects: %{num_redirects} | Time: %{time_total}s\n" http://localhost:8080 || echo "   ❌ Failed"
	@echo ""
	@echo "🌐 Available URLs:"
	@echo "   - WordPress: http://localhost:8080"
	@echo "   - phpMyAdmin: http://localhost:8081"
	@echo "   - HTTPS: https://localhost:8443"

show-db-info: ## Show database connection information for phpMyAdmin
	@echo "🗄️  Database Connection Information:"
	@echo ""
	@echo "📍 phpMyAdmin URL: http://localhost:8081"
	@echo ""
	@echo "🔐 Login Credentials:"
	@echo "   Username: root"
	@if [ -f .env ]; then \
		echo "   Password: $$(grep MYSQL_ROOT_PASSWORD .env | cut -d'=' -f2)"; \
	else \
		echo "   Password: (check .env file for MYSQL_ROOT_PASSWORD)"; \
	fi
	@echo ""
	@echo "🗃️  Database Details:"
	@echo "   Database Name: pinkangel"
	@echo "   WordPress User: wordpress"
	@echo "   Host: mysql-prod (internal)"
	@echo "   Port: 3306"

switch-env: ## Interactive environment switcher (dev/prod)
	@chmod +x scripts/switch-env.sh
	@./scripts/switch-env.sh

# Development environment
run-dev: ## Start development environment
	@echo "🚀 Starting development environment..."
	@chmod +x scripts/setup-dev.sh
	@./scripts/setup-dev.sh

build-dev: ## Build and start development environment from scratch
	@echo "🔨 Building and starting development environment..."
	@docker compose -f docker-compose.dev.yml build --no-cache
	@docker compose -f docker-compose.dev.yml up -d

stop-dev: ## Stop development environment only
	@echo "⏹️  Stopping development environment..."
	@docker compose -f docker-compose.dev.yml down

# Legacy aliases for backward compatibility
dev: run-dev ## Alias for run-dev (backward compatibility)
dev-build: build-dev ## Alias for build-dev (backward compatibility)
dev-stop: stop-dev ## Alias for stop-dev (backward compatibility)

# Legacy production commands removed - use Docker Swarm commands instead:
# - run-prod → swarm-start
# - build-prod → swarm-build
# - logs-prod → swarm-logs SERVICE=service_name

# ═══════════════════════════════════════════════════════════════
# 🐳 DOCKER SWARM PRODUCTION ENVIRONMENT
# ═══════════════════════════════════════════════════════════════

swarm-start: ## Start production environment in Docker Swarm mode
	@echo "🐳 Starting production environment in Docker Swarm mode..."
	@if [ ! -f .env ]; then echo "❌ Error: .env file not found. Run 'make setup-env' first."; exit 1; fi
	@chmod +x scripts/setup-swarm-prod.sh
	@./scripts/setup-swarm-prod.sh

swarm-init: ## Initialize Docker Swarm
	@echo "🐳 Initializing Docker Swarm..."
	@chmod +x scripts/manage-swarm.sh
	@./scripts/manage-swarm.sh init

swarm-secrets: ## Create Docker Swarm secrets from .env file
	@echo "🔐 Creating Docker Swarm secrets..."
	@if [ ! -f .env ]; then echo "❌ Error: .env file not found. Run 'make setup-env' first."; exit 1; fi
	@chmod +x scripts/manage-swarm.sh
	@./scripts/manage-swarm.sh secrets

swarm-build: ## Build Docker images for Swarm
	@echo "🔨 Building Docker images for Swarm..."
	@chmod +x scripts/manage-swarm.sh
	@./scripts/manage-swarm.sh build

swarm-deploy: ## Deploy production stack to Docker Swarm
	@echo "🚀 Deploying production stack to Docker Swarm..."
	@if [ ! -f .env ]; then echo "❌ Error: .env file not found. Run 'make setup-env' first."; exit 1; fi
	@chmod +x scripts/manage-swarm.sh
	@./scripts/manage-swarm.sh deploy

swarm-remove: ## Remove production stack from Docker Swarm
	@echo "🗑️ Removing production stack from Docker Swarm..."
	@chmod +x scripts/manage-swarm.sh
	@./scripts/manage-swarm.sh remove

swarm-stop: ## Stop Docker Swarm production environment
	@echo "⏹️ Stopping Docker Swarm production environment..."
	@chmod +x scripts/manage-swarm.sh
	@./scripts/manage-swarm.sh remove

swarm-status: ## Show Docker Swarm stack status
	@echo "📊 Checking Docker Swarm stack status..."
	@chmod +x scripts/manage-swarm.sh
	@./scripts/manage-swarm.sh status

swarm-logs: ## Show Docker Swarm service logs (usage: make swarm-logs SERVICE=service_name)
	@if [ -z "$(SERVICE)" ]; then echo "❌ Usage: make swarm-logs SERVICE=service_name"; echo "💡 Available services: nginx, wordpress-prod, mysql-prod, redis-prod, phpmyadmin, memcached, wordpress-cron-prod"; exit 1; fi
	@echo "📋 Showing logs for service: $(SERVICE)"
	@chmod +x scripts/manage-swarm.sh
	@./scripts/manage-swarm.sh logs $(SERVICE)

swarm-scale: ## Scale Docker Swarm service (usage: make swarm-scale SERVICE=service_name REPLICAS=number)
	@if [ -z "$(SERVICE)" ] || [ -z "$(REPLICAS)" ]; then echo "❌ Usage: make swarm-scale SERVICE=service_name REPLICAS=number"; echo "💡 Available services: nginx, wordpress-prod, mysql-prod, redis-prod, phpmyadmin, memcached, wordpress-cron-prod"; exit 1; fi
	@echo "⚖️ Scaling service $(SERVICE) to $(REPLICAS) replicas..."
	@chmod +x scripts/manage-swarm.sh
	@./scripts/manage-swarm.sh scale $(SERVICE) $(REPLICAS)

swarm-update: ## Update Docker Swarm service (usage: make swarm-update SERVICE=service_name [TYPE=force|image|config])
	@if [ -z "$(SERVICE)" ]; then echo "❌ Usage: make swarm-update SERVICE=service_name [TYPE=force|image|config]"; echo "💡 Available services: nginx, wordpress-prod, mysql-prod, redis-prod, phpmyadmin, memcached, wordpress-cron-prod"; echo "💡 Update types: force (default), image, config"; exit 1; fi
	@echo "🔄 Updating service: $(SERVICE) (type: $(or $(TYPE),force))"
	@chmod +x scripts/manage-swarm.sh
	@./scripts/manage-swarm.sh update $(SERVICE) $(or $(TYPE),force)

swarm-update-service: ## Advanced service update with options (usage: make swarm-update-service SERVICE=service_name TYPE=force|image|config)
	@if [ -z "$(SERVICE)" ]; then echo "❌ Usage: make swarm-update-service SERVICE=service_name TYPE=force|image|config"; echo "💡 Available services: nginx, wordpress-prod, mysql-prod, redis-prod, phpmyadmin, memcached, wordpress-cron-prod"; echo "💡 Update types:"; echo "   • force  - Rolling restart (default)"; echo "   • image  - Update to latest image"; echo "   • config - Update configuration from stack file"; exit 1; fi
	@echo "🔄 Advanced service update: $(SERVICE) (type: $(or $(TYPE),force))"
	@chmod +x scripts/manage-swarm.sh
	@./scripts/manage-swarm.sh update-service $(SERVICE) $(or $(TYPE),force)

swarm-test: ## Test Docker Swarm setup and functionality
	@echo "🧪 Testing Docker Swarm setup..."
	@chmod +x scripts/test-swarm-setup.sh
	@./scripts/test-swarm-setup.sh

swarm-join-tokens: ## Show Docker Swarm join tokens for adding new nodes
	@echo "🔑 Docker Swarm Join Tokens"
	@echo ""
	@echo "\033[1;34m📋 Worker Join Token:\033[0m"
	@docker swarm join-token worker
	@echo ""
	@echo "\033[1;34m👑 Manager Join Token:\033[0m"
	@docker swarm join-token manager
	@echo ""
	@echo "\033[1;33m💡 Usage:\033[0m"
	@echo "• Copy the appropriate command above"
	@echo "• Run it on the new node you want to add to the swarm"
	@echo "• Workers can only run containers, managers can also manage the swarm"
	@echo ""
	@echo "\033[1;33m🔄 Token Management:\033[0m"
	@echo "• Rotate worker token:  docker swarm join-token --rotate worker"
	@echo "• Rotate manager token: docker swarm join-token --rotate manager"

swarm-nodes: ## Show Docker Swarm nodes information
	@echo "🖥️  Docker Swarm Nodes Information"
	@echo ""
	@echo "\033[1;34m📊 Current Nodes:\033[0m"
	@docker node ls
	@echo ""
	@echo "\033[1;34m🔍 Swarm Status:\033[0m"
	@docker info | grep -A 15 "Swarm:" | head -16
	@echo ""
	@echo "\033[1;33m💡 Node Management:\033[0m"
	@echo "• View node details: docker node inspect <node-id>"
	@echo "• Remove node:       docker node rm <node-id>"
	@echo "• Promote to manager: docker node promote <node-id>"
	@echo "• Demote to worker:   docker node demote <node-id>"

swarm-save-tokens: ## Save join tokens to file for secure storage
	@echo "💾 Saving Docker Swarm join tokens to file..."
	@chmod +x scripts/save-swarm-tokens.sh
	@./scripts/save-swarm-tokens.sh

swarm-backup: ## Create backup of Docker Swarm production environment
	@echo "💾 Creating Docker Swarm production backup..."
	@chmod +x scripts/backup-swarm.sh
	@./scripts/backup-swarm.sh

swarm-cache-clear: ## Clear ALL possible caches in Docker Swarm production
	@echo "🧹 Clearing ALL Docker Swarm production caches..."
	@chmod +x scripts/swarm-cache-clear.sh
	@./scripts/swarm-cache-clear.sh

swarm-reload-nginx: ## Reload nginx configuration in Docker Swarm
	@echo "🔄 Reloading nginx configuration in Docker Swarm..."
	@docker service update --force pinkangel-prod_nginx

swarm-shell-wp: ## Access WordPress container shell in Docker Swarm
	@echo "🐚 Accessing WordPress container in Docker Swarm..."
	@docker exec -it $$(docker ps -q --filter "name=pinkangel-prod_wordpress-prod") bash

swarm-shell-mysql: ## Access MySQL container shell in Docker Swarm
	@echo "🗄️ Accessing MySQL container in Docker Swarm..."
	@docker exec -it $$(docker ps -q --filter "name=pinkangel-prod_mysql-prod") mysql -u root -p

swarm-shell-redis: ## Access Redis container shell in Docker Swarm
	@echo "🔴 Accessing Redis container in Docker Swarm..."
	@docker exec -it $$(docker ps -q --filter "name=pinkangel-prod_redis-prod") redis-cli

swarm-cron-status: ## Show WordPress cron service status in Docker Swarm
	@echo "⏰ Checking WordPress cron service status in Docker Swarm..."
	@docker service ls --filter "name=pinkangel-prod_wordpress-cron-prod" --format "table {{.Name}}\t{{.Replicas}}\t{{.Image}}"
	@echo ""
	@echo "📋 Service Tasks:"
	@docker service ps pinkangel-prod_wordpress-cron-prod --format "table {{.Name}}\t{{.Node}}\t{{.DesiredState}}\t{{.CurrentState}}"

swarm-cron-logs: ## Show WordPress cron service logs in Docker Swarm
	@echo "📋 Showing WordPress cron service logs in Docker Swarm..."
	@docker service logs pinkangel-prod_wordpress-cron-prod --tail 50

swarm-update-wordpress: ## Update WordPress in Docker Swarm production environment
	@echo "🔄 Updating WordPress in Docker Swarm production..."
	@chmod +x scripts/update-wordpress.sh
	@./scripts/update-wordpress.sh --env=prod

# ═══════════════════════════════════════════════════════════════
# 🔗 REVERSE PROXY MANAGEMENT
# ═══════════════════════════════════════════════════════════════

proxy-start: ## Start nginx-proxy container (reverse proxy)
	@echo "🔗 Starting nginx-proxy container (reverse proxy)..."
	@chmod +x scripts/manage-proxy.sh
	@./scripts/manage-proxy.sh start

proxy-stop: ## Stop nginx-proxy container
	@echo "⏹️ Stopping nginx-proxy container..."
	@chmod +x scripts/manage-proxy.sh
	@./scripts/manage-proxy.sh stop

proxy-restart: ## Restart nginx-proxy container (applies configuration changes)
	@echo "🔄 Restarting nginx-proxy container..."
	@chmod +x scripts/manage-proxy.sh
	@./scripts/manage-proxy.sh restart

proxy-status: ## Show nginx-proxy container status
	@echo "📊 Checking nginx-proxy container status..."
	@chmod +x scripts/manage-proxy.sh
	@./scripts/manage-proxy.sh status

proxy-logs: ## Show nginx-proxy container logs
	@echo "📋 Showing nginx-proxy container logs..."
	@chmod +x scripts/manage-proxy.sh
	@./scripts/manage-proxy.sh logs

proxy-test: ## Test nginx-proxy integration and functionality
	@echo "🧪 Testing nginx-proxy integration..."
	@chmod +x scripts/test-proxy-integration.sh
	@./scripts/test-proxy-integration.sh

# Legacy aliases for backward compatibility
start-proxy: proxy-start ## Legacy alias (use proxy-start instead)
stop-proxy: proxy-stop ## Legacy alias (use proxy-stop instead)
restart-proxy: proxy-restart ## Legacy alias (use proxy-restart instead)
status-proxy: proxy-status ## Legacy alias (use proxy-status instead)
logs-proxy: proxy-logs ## Legacy alias (use proxy-logs instead)
test-proxy: proxy-test ## Legacy alias (use proxy-test instead)
proxy: proxy-start ## Legacy alias (use proxy-start instead)
test-proxy-integration: proxy-test ## Legacy alias (use proxy-test instead)

# General environment control
stop-all: ## Stop all environments (dev + prod + proxy + swarm)
	@echo "⏹️  Stopping all environments..."
	@docker compose -f docker-compose.dev.yml down 2>/dev/null || true
	@# Legacy docker-compose prod removed - swarm handles production
	@docker compose -f docker-compose.proxy.yml down 2>/dev/null || true
	@chmod +x scripts/manage-swarm.sh 2>/dev/null || true
	@./scripts/manage-swarm.sh remove 2>/dev/null || true
	@echo "✅ All environments stopped"

# Legacy alias for backward compatibility
stop: stop-all ## Alias for stop-all (backward compatibility)

clean-all: ## Clean up all Docker resources (containers, images, volumes)
	@echo "🧹 Cleaning up Docker resources..."
	@docker compose -f docker-compose.dev.yml down -v --remove-orphans 2>/dev/null || true
	@# Legacy docker-compose prod removed - swarm handles production
	@docker compose -f docker-compose.proxy.yml down -v --remove-orphans 2>/dev/null || true
	@docker system prune -f
	@docker volume prune -f
	@echo "✅ Docker cleanup completed"

build-all: ## Build all Docker images (dev + swarm)
	@echo "🔨 Building all Docker images..."
	@docker compose -f docker-compose.dev.yml build --no-cache
	@make swarm-build
	@echo "✅ All images built successfully"

# Legacy aliases for backward compatibility
clean: clean-all ## Alias for clean-all (backward compatibility)
build: build-all ## Alias for build-all (backward compatibility)

# ═══════════════════════════════════════════════════════════════
# 📊 MONITORING & DEBUGGING
# ═══════════════════════════════════════════════════════════════

# System monitoring
monitor: ## 📊 Show comprehensive system status and monitoring info
	@echo "📊 System monitoring..."
	@chmod +x scripts/monitor.sh
	@./scripts/monitor.sh

# Log management
logs: ## 📋 Show recent logs for all containers (last 50 lines)
	@echo "📋 Recent logs from all containers:"
	@docker compose -f docker-compose.dev.yml logs --tail=50 2>/dev/null || docker compose -f docker-compose.prod.yml logs --tail=50

logs-dev: ## 📋 Show development environment logs (live tail)
	@echo "📋 Development environment logs (live):"
	@docker compose -f docker-compose.dev.yml logs -f

# logs-prod removed - use: make swarm-logs SERVICE=service_name

logs-errors: ## ❌ Show recent error logs from WordPress containers
	@echo "❌ Recent error logs:"
	@docker logs wordpress-dev --tail 50 2>/dev/null | grep -i error || echo "No dev errors found"
	@docker logs wordpress-prod --tail 50 2>/dev/null | grep -i error || echo "No prod errors found"

# Performance testing
performance-test: ## ⚡ Test current performance (response times for key pages)
	@echo "⚡ Testing current performance..."
	@echo "Frontend test:"
	@time curl -s -w "Response Time: %{time_total}s | HTTP Code: %{http_code}\n" "http://localhost:8080/" -o /dev/null
	@echo "Admin test:"
	@time curl -s -w "Response Time: %{time_total}s | HTTP Code: %{http_code}\n" "http://localhost:8080/wp-admin/" -o /dev/null

perf-test: ## ⚡ Run basic performance test with detailed metrics
	@echo "⚡ Running detailed performance test..."
	@curl -o /dev/null -s -w "Time: %{time_total}s\nSize: %{size_download} bytes\nSpeed: %{speed_download} bytes/s\n" http://localhost:8080/ || curl -o /dev/null -s -w "Time: %{time_total}s\nSize: %{size_download} bytes\nSpeed: %{speed_download} bytes/s\n" http://localhost/

test-redis: ## 🔴 Test Redis performance and connectivity
	@echo "🔴 Testing Redis performance..."
	@chmod +x scripts/test-redis.sh
	@./scripts/test-redis.sh

# Plugin and dependency checking
check-missing-plugins: ## 🔍 Check which plugins are missing compared to live database
	@echo "🔍 Checking for missing plugins..."
	@chmod +x scripts/check-missing-plugins.sh
	@./scripts/check-missing-plugins.sh

security-scan: ## 🔒 Run basic security checks
	@echo "🔒 Running security checks..."
	@docker exec wordpress-prod wp plugin list --status=inactive --allow-root 2>/dev/null || docker exec wordpress-dev wp plugin list --status=inactive --allow-root
	@echo "💡 Check for unused plugins above and remove them for security."

# ═══════════════════════════════════════════════════════════════
# 🔨 DEVELOPMENT TOOLS
# ═══════════════════════════════════════════════════════════════

# Development mode settings
dev-mode: ## 🛠️ Enable development-friendly settings (disable captcha, enable debug)
	@echo "🛠️ Enabling development mode..."
	@chmod +x scripts/dev-mode-toggle.sh
	@./scripts/dev-mode-toggle.sh

disable-captcha: ## 🚫 Disable captcha plugins (works with dev and prod)
	@chmod +x scripts/disable-captcha.sh
	@./scripts/disable-captcha.sh

# Container shell access
shell-wp-dev: ## 🐚 Access WordPress development container shell
	@echo "🐚 Accessing WordPress development container..."
	@docker exec -it wordpress-dev bash

shell-mysql-dev: ## 🗄️ Access MySQL development container shell
	@echo "🗄️ Accessing MySQL development container..."
	@docker exec -it mysql-dev mysql -u root -p

shell-redis-dev: ## 🔴 Access Redis development container shell
	@echo "🔴 Accessing Redis development container..."
	@docker exec -it redis-dev redis-cli

# Production shell access moved to swarm commands:
# - shell-wp-prod → swarm-shell-wp
# - shell-mysql-prod → swarm-shell-mysql
# - shell-redis-prod → swarm-shell-redis

# WP-CLI access
wp-cli: ## 🎯 Access WP-CLI (usage: make wp-cli CMD="command")
	@if [ -z "$(CMD)" ]; then echo "❌ Usage: make wp-cli CMD=\"wp command\""; echo "💡 Example: make wp-cli CMD=\"plugin list\""; exit 1; fi
	@echo "🎯 Running WP-CLI command: $(CMD)"
	@docker exec wordpress-prod wp $(CMD) --allow-root 2>/dev/null || docker exec wordpress-dev wp $(CMD) --allow-root

# ═══════════════════════════════════════════════════════════════
# 💾 DATABASE OPERATIONS
# ═══════════════════════════════════════════════════════════════

# Backup operations
backup: ## Create backup of current environment (auto-detects dev/prod)
	@echo "💾 Creating backup..."
	@chmod +x scripts/backup.sh
	@./scripts/backup.sh

backup-dev: ## Create backup of development environment specifically
	@echo "💾 Creating development backup..."
	@chmod +x scripts/backup-dev.sh
	@./scripts/backup-dev.sh

# backup-prod removed - use: make swarm-backup

list-backups: ## List all available backups by environment
	@chmod +x scripts/list-backups.sh
	@./scripts/list-backups.sh

restore: ## Restore from backup (usage: make restore BACKUP=filename)
	@if [ -z "$(BACKUP)" ]; then echo "❌ Usage: make restore BACKUP=filename"; echo "💡 Run 'make list-backups' to see available backups"; exit 1; fi
	@echo "🔄 Restoring from backup: $(BACKUP)"
	@chmod +x scripts/restore.sh
	@./scripts/restore.sh $(BACKUP)

# Database synchronization
sync-prod-to-dev: ## Sync production database to development (safe)
	@echo "🔄 Syncing production → development..."
	@chmod +x scripts/sync-prod-to-dev.sh
	@./scripts/sync-prod-to-dev.sh

sync-dev-to-prod: ## Sync development database to production (⚠️ DANGEROUS!)
	@echo "⚠️  WARNING: This will overwrite production data!"
	@read -p "Are you sure? Type 'yes' to continue: " confirm && [ "$$confirm" = "yes" ] || exit 1
	@chmod +x scripts/sync-dev-to-prod.sh
	@./scripts/sync-dev-to-prod.sh

sync-from-live: ## Sync files from live server to local environment (usage: make sync-from-live [OPTIONS])
	@echo "📥 Syncing files from live server..."
	@echo "💡 Available options: DRY_RUN=1, UPLOADS_ONLY=1, THEME_ONLY=1, PLUGINS_ONLY=1"
	@chmod +x scripts/sync-from-live.sh
	@if [ "$(DRY_RUN)" = "1" ]; then \
		./scripts/sync-from-live.sh --dry-run; \
	elif [ "$(UPLOADS_ONLY)" = "1" ]; then \
		./scripts/sync-from-live.sh --uploads-only; \
	elif [ "$(THEME_ONLY)" = "1" ]; then \
		./scripts/sync-from-live.sh --theme-only; \
	elif [ "$(PLUGINS_ONLY)" = "1" ]; then \
		./scripts/sync-from-live.sh --plugins-only; \
	else \
		./scripts/sync-from-live.sh; \
	fi

# Database import and export
import-db: ## Import database from pinkangel_db_original.sql
	@echo "📥 Importing database..."
	@chmod +x scripts/import-database.sh
	@./scripts/import-database.sh

db-export: ## Export current database to backups folder
	@echo "📤 Exporting database..."
	@mkdir -p backups
	@docker exec mysql-prod mysqldump -u wordpress -p wordpress > backups/db-export-$(shell date +%Y%m%d-%H%M%S).sql 2>/dev/null || docker exec mysql-dev mysqldump -u wordpress -p wordpress > backups/db-export-$(shell date +%Y%m%d-%H%M%S).sql
	@echo "✅ Database exported to backups/"

db-import: ## Import database from file (usage: make db-import FILE=filename)
	@if [ -z "$(FILE)" ]; then echo "❌ Usage: make db-import FILE=filename"; exit 1; fi
	@echo "📥 Importing database from $(FILE)..."
	@docker exec -i mysql-prod mysql -u wordpress -p wordpress < $(FILE) 2>/dev/null || docker exec -i mysql-dev mysql -u wordpress -p wordpress < $(FILE)
	@echo "✅ Database imported successfully"

# Database fixing tools
fix-db-keys: ## Fix MySQL key length issues in database files
	@chmod +x scripts/quick-fix-db.sh
	@echo "🔧 Usage: make fix-db-keys FILE=database.sql [OUTPUT=fixed.sql] [LENGTH=191]"
	@if [ -z "$(FILE)" ]; then echo "❌ Error: FILE parameter required"; exit 1; fi
	@./scripts/quick-fix-db.sh "$(FILE)" "$(OUTPUT)" "$(LENGTH)"

# ═══════════════════════════════════════════════════════════════
# ⚙️ ADVANCED OPERATIONS
# ═══════════════════════════════════════════════════════════════

# WordPress updates
update: ## 🔄 Update WordPress core, plugins, and themes (environment-aware)
	@echo "🔄 Updating WordPress..."
	@chmod +x scripts/update-wordpress.sh
	@./scripts/update-wordpress.sh

# SSL certificate generation
ssl: ## 🔒 Generate self-signed SSL certificate for production
	@echo "🔒 Generating self-signed SSL certificate..."
	@mkdir -p ssl
	@openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
		-keyout ssl/key.pem \
		-out ssl/cert.pem \
		-subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
	@echo "✅ Self-signed SSL certificate generated in ssl/ directory"
	@echo "⚠️  For production, replace with a real SSL certificate from a CA"

fix-missing-images: ## 🖼️ Fix missing images and theme errors (recent fix)
	@echo "🖼️ Fixing missing images and theme errors..."
	@chmod +x scripts/fix-missing-images-and-errors.sh
	@./scripts/fix-missing-images-and-errors.sh

fix-action-scheduler: ## 🔧 Fix Action Scheduler duplicate entry errors
	@echo "🔧 Fixing Action Scheduler database errors..."
	@chmod +x scripts/fix-action-scheduler.sh
	@./scripts/fix-action-scheduler.sh

# Database cleanup operations
clean-action-scheduler: ## 🧹 Clean WooCommerce Action Scheduler (auto-detects environment)
	@echo "🧹 Cleaning Action Scheduler..."
	@chmod +x scripts/clean-action-scheduler.sh
	@./scripts/clean-action-scheduler.sh

clean-action-scheduler-dev: ## 🧹 Clean Action Scheduler for development (keep 7 days)
	@chmod +x scripts/clean-action-scheduler.sh
	@./scripts/clean-action-scheduler.sh dev 7

clean-action-scheduler-prod: ## 🧹 Clean Action Scheduler for production (keep 30 days)
	@chmod +x scripts/clean-action-scheduler.sh
	@./scripts/clean-action-scheduler.sh prod 30

clean-spam-comments: ## 🗑️ Clean spam comments (auto-detects environment, safe mode)
	@echo "🗑️ Cleaning spam comments..."
	@chmod +x scripts/clean-spam-comments.sh
	@./scripts/clean-spam-comments.sh

clean-spam-comments-aggressive: ## 🗑️ Clean spam comments aggressively (auto-detects environment)
	@echo "🗑️ Aggressively cleaning spam comments..."
	@chmod +x scripts/clean-spam-comments.sh
	@./scripts/clean-spam-comments.sh auto aggressive

safe-database-cleanup: ## 🧹 Safe incremental database cleanup (prevents high CPU usage)
	@echo "🧹 Running safe database cleanup..."
	@chmod +x scripts/safe-database-cleanup.sh
	@./scripts/safe-database-cleanup.sh

# Cache operations
cache-clear: ## 🗑️ Clear all caches (WordPress + Redis)
	@echo "🗑️ Clearing all caches..."
	@docker exec wordpress-prod wp cache flush --allow-root 2>/dev/null || docker exec wordpress-dev wp cache flush --allow-root
	@docker exec redis-prod redis-cli FLUSHALL 2>/dev/null || docker exec redis-dev redis-cli FLUSHALL
	@echo "✅ All caches cleared"

# cache-clear-prod removed - use: make swarm-cache-clear
# reload-nginx-prod removed - use: make swarm-reload-nginx
# restart-proxy moved to proxy section - use: make proxy-restart

test-rate-limits: ## 🧪 Test admin rate limiting (simulates 6 page switches)
	@echo "🧪 Testing admin rate limiting improvements..."
	@chmod +x scripts/test-admin-rate-limits.sh
	@./scripts/test-admin-rate-limits.sh



# WordPress URL and permalink fixes
complete-fix: ## 🔧 Complete fix after database import (URLs + permalinks + WooCommerce)
	@echo "🔧 Running complete post-import fix..."
	@chmod +x scripts/complete-post-import-fix.sh
	@./scripts/complete-post-import-fix.sh

fix-urls: ## 🔗 Fix WordPress URLs after importing live database
	@echo "🔗 Fixing WordPress URLs..."
	@chmod +x scripts/fix-urls-after-import.sh
	@./scripts/fix-urls-after-import.sh

quick-url-fix: ## ⚡ Quick fix for WordPress URL redirects
	@echo "⚡ Running quick URL fix..."
	@chmod +x scripts/quick-url-fix.sh
	@./scripts/quick-url-fix.sh

fix-permalinks: ## 🔗 Fix WordPress permalinks and rewrite rules
	@echo "🔗 Fixing WordPress permalinks..."
	@chmod +x scripts/fix-permalinks.sh
	@./scripts/fix-permalinks.sh

fix-woocommerce: ## 🛒 Fix WooCommerce permalinks and pages
	@echo "🛒 Fixing WooCommerce permalinks..."
	@chmod +x scripts/fix-woocommerce-permalinks.sh
	@./scripts/fix-woocommerce-permalinks.sh

# Theme dependency management
install-theme-deps-dev: ## 🎨 Install PinkAngel theme dependencies (development)
	@echo "🎨 Installing PinkAngel theme dependencies for development..."
	@docker exec wordpress-dev wp plugin install woocommerce advanced-woo-search ti-woocommerce-wishlist polylang redis-cache --activate --allow-root
	@docker exec wordpress-dev wp redis enable --allow-root
	@echo "✅ Theme dependencies installed successfully!"

install-theme-deps-prod: ## 🎨 Install PinkAngel theme dependencies (production)
	@echo "🎨 Installing PinkAngel theme dependencies for production..."
	@docker exec wordpress-prod wp plugin install woocommerce advanced-woo-search ti-woocommerce-wishlist polylang redis-cache --activate --allow-root
	@docker exec wordpress-prod wp redis enable --allow-root
	@echo "✅ Theme dependencies installed successfully!"

# ═══════════════════════════════════════════════════════════════
# ⏰ CRON MANAGEMENT
# ═══════════════════════════════════════════════════════════════

# Cron container management
cron-status: ## ⏰ Show WordPress cron container status and health
	@echo "⏰ Checking WordPress cron container status..."
	@chmod +x scripts/manage-cron.sh
	@./scripts/manage-cron.sh status

cron-logs: ## 📋 Show WordPress cron container logs
	@echo "📋 Showing WordPress cron container logs..."
	@chmod +x scripts/manage-cron.sh
	@./scripts/manage-cron.sh logs $(LINES)

cron-health: ## 🏥 Show detailed WordPress cron health information
	@echo "🏥 Checking WordPress cron health..."
	@chmod +x scripts/manage-cron.sh
	@./scripts/manage-cron.sh health

cron-restart: ## 🔄 Restart the WordPress cron container
	@echo "🔄 Restarting WordPress cron container..."
	@chmod +x scripts/manage-cron.sh
	@./scripts/manage-cron.sh restart

cron-monitor: ## 📊 Real-time WordPress cron monitoring dashboard
	@echo "📊 Starting WordPress cron monitoring..."
	@chmod +x scripts/cron-monitor.sh
	@./scripts/cron-monitor.sh dashboard

cron-run-now: ## ⚡ Run specific cron task immediately (usage: make cron-run-now TASK=wp-cron)
	@if [ -z "$(TASK)" ]; then echo "❌ Usage: make cron-run-now TASK=task_name"; echo "💡 Available tasks: wp-cron, action-scheduler, hourly, daily, weekly"; exit 1; fi
	@echo "⚡ Running cron task: $(TASK)"
	@chmod +x scripts/manage-cron.sh
	@./scripts/manage-cron.sh run-now $(TASK)

cron-schedule: ## 📅 Show current WordPress cron schedule
	@echo "📅 Showing WordPress cron schedule..."
	@chmod +x scripts/manage-cron.sh
	@./scripts/manage-cron.sh schedule

cron-report: ## 📊 Generate detailed cron monitoring report
	@echo "📊 Generating cron monitoring report..."
	@chmod +x scripts/cron-monitor.sh
	@./scripts/cron-monitor.sh report

cron-alerts: ## 🚨 Check for cron system alerts
	@echo "🚨 Checking for cron system alerts..."
	@chmod +x scripts/cron-monitor.sh
	@./scripts/cron-monitor.sh alerts

cron-setup: ## 🔧 Setup and deploy WordPress cron container
	@echo "🔧 Setting up WordPress cron container..."
	@chmod +x scripts/setup-cron.sh
	@./scripts/setup-cron.sh deploy

cron-build: ## 🔨 Build WordPress cron container image
	@echo "🔨 Building WordPress cron container image..."
	@chmod +x scripts/setup-cron.sh
	@./scripts/setup-cron.sh build

cron-verify: ## ✅ Verify WordPress cron container setup
	@echo "✅ Verifying WordPress cron container setup..."
	@chmod +x scripts/setup-cron.sh
	@./scripts/setup-cron.sh verify

# ═══════════════════════════════════════════════════════════════
# ⚙️ ADVANCED OPERATIONS
# ═══════════════════════════════════════════════════════════════

fix-woocommerce-analytics: ## 🛠️ Fix WooCommerce analytics after deployment
	@echo "🛠️ Fixing WooCommerce analytics configuration..."
	@chmod +x scripts/fix-woocommerce-analytics.sh
	@./scripts/fix-woocommerce-analytics.sh



